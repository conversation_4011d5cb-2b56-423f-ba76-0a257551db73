import {MxCADUiPrPoint, McDbLine, MxCpp, McDbBlockReference, McDbAttributeDefinition, McDbAttribute} from "mxcad"
// 绘制线路
export async function drawLines(formData, fileName, scale) {
    const blkFilePath = import.meta.url.substring(0, import.meta.url.lastIndexOf('/')) + fileName;
    const mxcad = MxCpp.App.getCurrentMxCAD();
    const blkrecId = await mxcad.insertBlock(blkFilePath, formData.spanClass);
    if (!blkrecId.isValid()) return;
    const blkRef = createBlockReference(blkrecId, scale, mxcad, formData);
    if (!blkRef) return;
    const firstPoint = await getPoint("请点击确定起始点", (v, worldDraw) => {
        blkRef.position = v;
        worldDraw.drawMcDbEntity(blkRef);
    });
    if (!firstPoint) return;
    blkRef.position = firstPoint;
    let currentPoint = firstPoint

    const initialBlkRefId = mxcad.drawEntity(blkRef);
    if (!initialBlkRefId.isValid) {
        console.log("初始块插入失败");
        return;
    }
    setBlockAttributes(blkRef, initialBlkRefId, blkrecId);
    while (true) {
        const nextPoint = await getPoint("请点击确定下一点（ESC结束）", (pt, worldDraw) => {
            drawPreviewLineAndBlock(currentPoint, pt, blkRef, worldDraw);
        });
        if (!nextPoint) break;
        // drawLineSegment(currentPoint, nextPoint, blkRef, blkrecId, mxcad);
        const pointNew = getIntersectionPointFromCenter(currentPoint.x, currentPoint.y, formData.spanWidth / 2,nextPoint.x, nextPoint.y)
        const nextPointNew = getIntersectionPointFromCenter(nextPoint.x, nextPoint.y, formData.spanWidth / 2, pointNew.x, pointNew.y)
        drawLineSegment(pointNew, nextPointNew,nextPoint, blkRef, blkrecId, mxcad);
        currentPoint = nextPoint;
    }
}
function getIntersectionPointFromCenter(cx, cy, radius, x2, y2) {
    const dx = x2 - cx;
    const dy = y2 - cy;
    const length = Math.sqrt(dx * dx + dy * dy);
    const unitDx = dx / length;
    const unitDy = dy / length;
    const intersectionX = cx + unitDx * radius
    const intersectionY = cy + unitDy * radius
    return { x: intersectionX, y: intersectionY,z: 0 };
}

function createBlockReference(blkrecId, scale, mxcad, formData) {
    const blkRef = new McDbBlockReference();
    blkRef.blockTableRecordId = blkrecId;
    const boundingBox = blkRef.getBoundingBox();
    if (boundingBox.ret && formData.spanWidth) {
        console.log(formData.spanWidth)
        let dLen = Math.abs(boundingBox.maxPt.x - boundingBox.minPt.x);
        blkRef.setScale(parseFloat(formData.spanWidth) / dLen)
    }
    return blkRef;
}
async function getPoint(message, userDrawCallback) {
    const pointPicker = new MxCADUiPrPoint();
    pointPicker.setMessage(message);
    pointPicker.setUserDraw(userDrawCallback);
    return await pointPicker.go();
}
function drawPreviewLineAndBlock(currentPoint, nextPoint, blkRef, worldDraw) {
    const line = new McDbLine(currentPoint.x, currentPoint.y, currentPoint.z, nextPoint.x, nextPoint.y, nextPoint.z);
    worldDraw.drawMcDbEntity(line);
    blkRef.position = nextPoint;
    worldDraw.drawMcDbEntity(blkRef);
}
function drawLineSegment(startPoint, endPointNew, endPoint, blkRef, blkrecId, mxcad) {
    const line = new McDbLine(startPoint.x, startPoint.y, startPoint.z, endPointNew.x, endPointNew.y, endPointNew.z);
    // const MyLineType = mxcad.addLinetype("MyLineType", "6,-8");
    // line.linetypeId = MyLineType;
    // let lineTypeLen = 14;
    // let lscale = mxcad.getSysVarDouble("LTSCALE");
    // let dashline = mxcad.mxdraw.viewCoordLong2Cad(20);
    // let lDispalyLen = lineTypeLen * lscale;
    // let dScale = dashline / lDispalyLen;
    // line.linetypeScale = dScale
    mxcad.drawEntity(line);
    blkRef.position = endPoint;
    const blkRefId = mxcad.drawEntity(blkRef);
    if (!blkRefId.isValid) {
        console.log("图块插入失败");
        return;
    }
    setBlockAttributes(blkRef, blkRefId, blkrecId);
}
function setBlockAttributes(blkRef, blkRefId, blkrecId) {
    const blkEntity = blkRefId.getMcDbEntity() as McDbBlockReference;
    blkEntity.disableDisplay(true);
    const blkRecord = blkrecId.getMcDbBlockTableRecord();
    const entityIds = blkRecord.getAllEntityId();
    if (entityIds.length === 1 && entityIds[0].isKindOf("McDbBlockReference")) {
        const nestedBlock = entityIds[0].getMcDbEntity() as McDbBlockReference;
        const nestedRecordId = nestedBlock.blockTableRecordId;
        blkEntity.blockTableRecordId = nestedRecordId;
        setAttributesForBlock(blkRefId, nestedRecordId);
    } else {
        setAttributesForBlock(blkRefId, blkrecId);
    }
    blkEntity.disableDisplay(false);
}
function setAttributesForBlock(blkRefId, blkrecId) {
    const blkEntity = blkRefId.getMcDbEntity() as McDbBlockReference;
    const blkRecord = blkrecId.getMcDbBlockTableRecord();
    const entityIds = blkRecord.getAllEntityId();
    entityIds.forEach((id, index) => {
        if (!id.isKindOf("McDbAttributeDefinition")) return;
        const attribDef = id.getMcDbEntity() as McDbAttributeDefinition;
        const attrib = new McDbAttribute();
        Object.assign(attrib, {
            position: attribDef.position,
            alignmentPoint: attribDef.alignmentPoint,
            height: attribDef.height,
            trueColor: attribDef.trueColor,
            widthFactor: attribDef.widthFactor,
            textString: attribDef.textString,
            tag: attribDef.tag,
            isInvisible: false,
        });
        attrib.transformBy(blkEntity.blockTransform);
        const appendedAttrib = blkEntity.appendAttribute(attrib).getMcDbEntity() as McDbAttribute;
        Object.assign(appendedAttrib, {
            textStyle: attribDef.textStyle,
            layer: attribDef.layer,
        });
    });
}
