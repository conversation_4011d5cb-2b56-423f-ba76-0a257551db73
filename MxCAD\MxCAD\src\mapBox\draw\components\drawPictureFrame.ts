import { McDbBlockTableRecord,MxCADUiPrPoint, McGePoint3d, McDbPolyline, MxCpp,MxTools, McDbText, McDb, McCmColor, McDbBlockReference, McDbAttributeDefinition,ColorIndexType,McDbMText,McDbPoint } from "mxcad";
import {MxFun} from "mxdraw";
import {MxDrawTable} from "./drawTable";

export async function pictureFrame(numRectangles) {
//    去重
    const uniquenumRectangles=numRectangles.reduce((acc,current)=>{
        const x=acc.find(item=>item.legendname===current.legendname);
        if(!x){
            acc.push(current)
        }
        return acc
    },[])

    numRectangles=uniquenumRectangles
    console.log(uniquenumRectangles,'标注管理111111111',numRectangles)
    const width = 500;  // 长方形宽度
    const height = 60;  // 长方形高度

    // 设置第一个长方形的中心点
    const getCenterPt = new MxCADUiPrPoint();
    getCenterPt.setMessage("请点击确定中心点");
    const centerPt = await getCenterPt.go();
    if (!centerPt) return;
    const mxcad = MxCpp.App.getCurrentMxCAD();
    let blkTable =  mxcad.getDatabase().getBlockTable();
    let blkRecId = blkTable.add(new McDbBlockTableRecord());
    // 根据ObjectId再次得到刚刚添加的图块记录
    let blkTableRecord:McDbBlockTableRecord = blkRecId.getMcDbBlockTableRecord()

    // 绘制第一个长方形并放文字
    let pt1 = new McGePoint3d(centerPt.x + width / 2, centerPt.y + height / 2, centerPt.z);
    let pt2 = new McGePoint3d(centerPt.x - width / 2, centerPt.y + height / 2, centerPt.z);
    let pt3 = new McGePoint3d(centerPt.x - width / 2, centerPt.y - height / 2, centerPt.z);
    let pt4 = new McGePoint3d(centerPt.x + width / 2, centerPt.y - height / 2, centerPt.z);

    // 创建并绘制第一个长方形
    let pl = new McDbPolyline();
    pl.addVertexAt(pt1);
    pl.addVertexAt(pt2);
    pl.addVertexAt(pt3);
    pl.addVertexAt(pt4);
    pl.isClosed = true;
    // mxcad.drawEntity(pl);
    blkTableRecord.appendAcDbEntity(pl)

    // 绘制长方形左侧的竖线（形成正方形的边界）
    let linePt1 = new McGePoint3d(centerPt.x - width / 2, centerPt.y + height / 2, centerPt.z); // 左上角
    let linePt2 = new McGePoint3d(centerPt.x - width / 2, centerPt.y - height / 2, centerPt.z); // 左下角
    let line = new McDbPolyline();
    line.addVertexAt(linePt1);
    line.addVertexAt(linePt2);
    // mxcad.drawEntity(line);
    blkTableRecord.appendAcDbEntity(line)

    // 创建文本实体
    const text = new McDbText();
    text.textString = "图例"; // 使用传入的文本参数
    text.position = new McGePoint3d(centerPt.x, centerPt.y - 15, centerPt.z);
    text.alignmentPoint = new McGePoint3d(centerPt.x, centerPt.y - 15, centerPt.z);
    text.height = 30; // 文本高度
    text.widthFactor = 1; // 文本宽度缩放
    text.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    text.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    text.trueColor = new McCmColor(0, 0, 0); // 文本颜色（黑色）
    // 绘制文本
    // mxcad.drawEntity(text);
    blkTableRecord.appendAcDbEntity(text)
    // 绘制后续的长方形并放文字
    let previousCenterY = centerPt.y;  // 记录前一个长方形的Y坐标
    for (let i = 0; i < numRectangles.length; i++) {
        const newCenterPt = new McGePoint3d(centerPt.x, previousCenterY - height, centerPt.z);
        // 获取新长方形的四个顶点
        pt1 = new McGePoint3d(newCenterPt.x + width / 2, newCenterPt.y + height / 2, newCenterPt.z);
        pt2 = new McGePoint3d(newCenterPt.x - width / 2, newCenterPt.y + height / 2, newCenterPt.z);
        pt3 = new McGePoint3d(newCenterPt.x - width / 2, newCenterPt.y - height / 2, newCenterPt.z);
        pt4 = new McGePoint3d(newCenterPt.x + width / 2, newCenterPt.y - height / 2, newCenterPt.z);
        // 创建并绘制当前长方形
        pl = new McDbPolyline();
        pl.addVertexAt(pt1);
        pl.addVertexAt(pt2);
        pl.addVertexAt(pt3);
        pl.addVertexAt(pt4);
        pl.isClosed = true;
        // mxcad.drawEntity(pl);
        blkTableRecord.appendAcDbEntity(pl)

        // 绘制左侧竖线（形成正方形的边界）
        linePt1 = new McGePoint3d((newCenterPt.x - width / 2) + height, newCenterPt.y + height / 2, newCenterPt.z); // 左上角
        linePt2 = new McGePoint3d((newCenterPt.x - width / 2) + height, newCenterPt.y - height / 2, newCenterPt.z); // 左下角
        line = new McDbPolyline();
        line.addVertexAt(linePt1);
        line.addVertexAt(linePt2);
        // mxcad.drawEntity(line);
        blkTableRecord.appendAcDbEntity(line)

         let pcNum=0
        if(numRectangles[i].text=='10kV电缆段新建'){
            pcNum=85
            
        }
       
        let sfArr=['柱上断路器','跌落式熔断器','柱上避雷器','柱上负荷开关']
        let zhushangArr=['柱上变压器新建','柱上变压器原有','柱上变压器拆除']
        let sbY=0;//y方向偏移量
        let sfLv=0
        if(sfArr.indexOf(numRectangles[i].text.slice(0,-2)) !== -1){
            sbY=-25
            sfLv=4
        }else if(numRectangles[i].text=='普通单拉线新建'){
            sbY=-25
            sfLv=0
        }  else if(numRectangles[i].text=='接地新建'){
            sbY=25
            sfLv=0
        }else if(zhushangArr.indexOf(numRectangles[i].text)!==-1){
            pcNum=-15
        }
        if(numRectangles[i].text=='电缆上下杆新建'){
            console.log(numRectangles[i].fileName,'电缆上下干')
            const mxcad=MxCpp.getCurrentMxCAD()
            const point =new McDbPoint()
            const color = new McCmColor()
            color.setRGB(255,255,255)
            point.trueColor=color
            point.setPosition((newCenterPt.x - width / 2)+pcNum + height / 2, newCenterPt.y+sbY, newCenterPt.z)
            mxcad.drawEntity(point)
            mxcad.drawPoint((newCenterPt.x - width / 2)+pcNum + height / 2, newCenterPt.y+sbY)
        }
        // 设置图块路径
        let blkFilePath = import.meta.url.substring(0, import.meta.url.lastIndexOf('/')) + numRectangles[i].fileName;
        // 插入图块文件
        let blkrecId = await mxcad.insertBlock(blkFilePath, numRectangles[i].spanClass);
        if (!blkrecId.isValid()) {
            // 插入图块失败
            // return
        }
        let blkRef = new McDbBlockReference();
        blkRef.blockTableRecordId = blkrecId;
        let box = blkRef.getBoundingBox();
        if (box.ret && numRectangles[i].spanWidth) {
            let dLen = Math.abs(box.maxPt.x - box.minPt.x);
            blkRef.setScale(parseFloat(numRectangles[i].spanWidth) / dLen-sfLv);
        }
        
        // mxcad.drawEntity(blkRef);
        // console.log(numRectangles[i], '----',blkRef.position)
       
        blkRef.position = new McGePoint3d((newCenterPt.x - width / 2)+pcNum + height / 2, newCenterPt.y+sbY, newCenterPt.z); // 正方形中心位置
        blkTableRecord.appendAcDbEntity(blkRef)
        console.log(numRectangles[i].text,'文本文字')
        // 创建文本实体
        const text = new McDbText();
        text.textString = numRectangles[i].text; // 使用传入的文本参数
        text.position = new McGePoint3d(newCenterPt.x, newCenterPt.y - 15, newCenterPt.z);
        text.alignmentPoint = new McGePoint3d(newCenterPt.x, newCenterPt.y - 15, newCenterPt.z);
        text.height = 30; // 文本高度
        text.widthFactor = 1; // 文本宽度缩放
        text.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
        text.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
        text.trueColor = new McCmColor(0, 0, 0); // 文本颜色（黑色）
        // 绘制文本
        // mxcad.drawEntity(text);
        blkTableRecord.appendAcDbEntity(text)
        // 更新前一个长方形的Y坐标
        previousCenterY = newCenterPt.y;
    }
    // 设置图块的基点 一般是包围盒内的点， 可以任意指定
    blkTableRecord.origin = centerPt;
    // 实例化块参照 这里需要设置我们刚刚添加图块记录得到的ObjectId
    let blkRef = new McDbBlockReference();
    blkRef.blockTableRecordId = blkRecId;
    // 最后设置位置 渲染图块
    blkRef.position = centerPt;
    mxcad.drawEntity(blkRef);
    mxcad.updateDisplay()
}

// 主要设备材料清单
export async function blockExcelFun (value, startIndex) {
    let leftHeight = 0 // 左边框宽
    let rightHeight = 0 // 右边框宽
    let bottomWidth = 0 // 下边框宽
    let borderWidth = 0 // 外框宽
    let TqBorderWidth = 0 // 图签宽
    // 处理偏移量
    const offsetX = value.offsetX || 0; // 默认值为 0

    let blkFilePath = import.meta.url.substring(0, import.meta.url.lastIndexOf('/')) + `/assets/TuK/A4-210x297 标准改1.dwg.mxweb`;
    // 插入图块文件
    let mxcad = MxCpp.App.getCurrentMxCAD();
    let blkrecId = await mxcad.insertBlock(blkFilePath, 'A4-210x297 标准改1.dwg');
    if (!blkrecId.isValid()) {
        return
    }

    let blkRef = new McDbBlockReference();
    blkRef.blockTableRecordId = blkrecId;
    blkRef.position = new McGePoint3d(offsetX,0,0);
    mxcad.drawEntity(blkRef);
    let blkRecord: any = blkrecId.getMcDbBlockTableRecord();
    let ids = blkRecord.getAllEntityId();
    ids.forEach((id: any, index: any) => {
        if (!id.isKindOf("McDbAttributeDefinition")) return;
        let attribDef = id.getMcDbEntity() as McDbAttributeDefinition;
        if (attribDef.tag === '左边框宽') {
            leftHeight = Number(attribDef.textString)
        } else if (attribDef.tag === '右边框宽') {
            rightHeight = Number(attribDef.textString)
        } else if (attribDef.tag === '外框宽') {
            borderWidth = Number(attribDef.textString)
        } else if (attribDef.tag === '下边框高') {
            bottomWidth = Number(attribDef.textString)
        }
    })
    let blkFilePathT = import.meta.url.substring(0, import.meta.url.lastIndexOf('/')) + `/assets/TuK/标准图签改.dwg.mxweb`;
    let blkrecIdT = await mxcad.insertBlock(blkFilePathT, '标准图签改.dwg');
    if (!blkrecIdT.isValid()) {
        return
    }
    let blkRefT = new McDbBlockReference();
    blkRefT.blockTableRecordId = blkrecIdT;
    let blkRecordT: any = blkrecIdT.getMcDbBlockTableRecord();
    let idsT = blkRecordT.getAllEntityId();
    idsT.forEach((idT: any, index: any) => {
        if (!idT.isKindOf("McDbAttributeDefinition")) return;
        let attribDef = idT.getMcDbEntity() as McDbAttributeDefinition;
        if (attribDef.tag === '图签宽') {
            TqBorderWidth = Number(attribDef.textString)
        }
    })
    blkRefT.position = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + offsetX,bottomWidth,0);
    mxcad.drawEntity(blkRefT);

    // 创建文本实体
    const text = new McDbText();
    text.textString = '主要材料清单'; // 使用传入的文本参数
    text.position = new McGePoint3d(115 + offsetX,275,0);
    text.alignmentPoint = new McGePoint3d(115 + offsetX,275,0);
    text.height = 7; // 文本高度
    text.widthFactor = 1; // 文本宽度缩放
    text.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    text.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    text.trueColor = new McCmColor(0, 0, 0); // 文本颜色（紫色）
    // 绘制文本
    mxcad.drawEntity(text);

    // 供电公司
    const textG = new McDbText();
    const positionG = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 10 + offsetX,bottomWidth + 16,0)
    textG.textString = value.countyOrganisationName ? value.countyOrganisationName : ''; // 使用传入的文本参数
    textG.position = positionG;
    textG.alignmentPoint = positionG;
    textG.height = 2; // 文本高度
    textG.widthFactor = 1; // 文本宽度缩放
    textG.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textG.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textG.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    mxcad.drawEntity(textG);

    // 项目名称
    const textPrject = new McDbText();
    const positionPrject = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 55 + offsetX,bottomWidth + 16,0)
    textPrject.textString = value.projectName ? value.projectName : ''; // 使用传入的文本参数
    textPrject.position = positionPrject;
    textPrject.alignmentPoint = positionPrject;
    textPrject.height = 2; // 文本高度
    textPrject.widthFactor = 1; // 文本宽度缩放
    textPrject.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textPrject.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textPrject.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    mxcad.drawEntity(textPrject);

    // 阶段
    const textJd = new McDbText();
    const positionJd = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 80 + offsetX,bottomWidth + 16,0)
    const textString = value.stage && value.stage === '1' ? '需求' : value.stage && value.stage === '2' ? '可研' : value.stage && value.stage === '3' ? '初设' : value.stage && value.stage === '4' ? '施工' : value.stage && value.stage === '5' ? '施工变更' : value.stage && value.stage === '5' ? '竣工' : ''
    textJd.textString = textString; // 使用传入的文本参数
    textJd.position = positionJd;
    textJd.alignmentPoint = positionJd;
    textJd.height = 2; // 文本高度
    textJd.widthFactor = 1; // 文本宽度缩放
    textJd.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textJd.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textJd.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    mxcad.drawEntity(textJd);

    // 时间
    const textTime = new McDbText();
    const positionTime = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 40 + offsetX,bottomWidth + 1.5,0)
    const textStringTime = min_getTime()
    textTime.textString = textStringTime; // 使用传入的文本参数
    textTime.position = positionTime;
    textTime.alignmentPoint = positionTime;
    textTime.height = 1; // 文本高度
    textTime.widthFactor = 1; // 文本宽度缩放
    textTime.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textTime.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textTime.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    mxcad.drawEntity(textTime);

    // 图号：项目编号+n
    const textCode = new McDbText();
    const positionCode = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 60 + offsetX,bottomWidth + 1.5,0)
    const textStringCode = value.proCode ? value.proCode + '-' + value.num : ''; // 使用传入的文本参数
    textCode.textString = textStringCode; // 使用传入的文本参数
    textCode.position = positionCode;
    textCode.alignmentPoint = positionCode;
    textCode.height = 1; // 文本高度
    textCode.widthFactor = 1; // 文本宽度缩放
    textCode.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textCode.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textCode.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    mxcad.drawEntity(textCode);

    // 图框名称：主要设备清单+当前页1,2,3
    const textK = new McDbText();
    const positionK = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 55 + offsetX,bottomWidth + 12,0)
    const textStringK = '主要设备清单-' + value.num; // 使用传入的文本参数
    textK.textString = textStringK; // 使用传入的文本参数
    textK.position = positionK;
    textK.alignmentPoint = positionK;
    textK.height = 2; // 文本高度
    textK.widthFactor = 1; // 文本宽度缩放
    textK.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textK.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textK.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    mxcad.drawEntity(textK);

    let lScale = 1 //MxFun.screenCoordLong2Doc(100) / 200;
    let draw = new MxDrawTable();
    draw.data.setRowTextHeight(1.5)
    draw.data.addColumn(0, "序号", 4, 1.5);
    draw.data.addColumn(0,"ERP编码", 14, 1.5);
    draw.data.addColumn(0,"物料名称", 15, 1.5);
    draw.data.addColumn(0,"规格型号", 45, 1.5);
    draw.data.addColumn(0,"物料描述", 45, 1.5);
    draw.data.addColumn(0,"技术规范", 25, 1.5);
    draw.data.addColumn(0,"单位", 5, 1.5);
    draw.data.addColumn(0,"数量", 5, 1.5);
    draw.data.addColumn(0,"材料供给", 8, 1.5);
    draw.data.addColumn(0,"是否备品备件", 8, 1.5);
    draw.data.addColumn(0,"是否计入设备材料损耗", 8, 1.5);
    console.log(value)
    const arr = value.tableData ? JSON.parse(value.tableData) : []
    for (let i = 0; i < arr.length; i++) {
        let sT = "" +(i + 1 + startIndex);
        let rowHeight = 3; // 设置行高
        if(arr[i].spec){
            console.log(arr[i].spec.length,arr[i].spec)
            rowHeight = Math.ceil(arr[i].spec.length / 28) * 3
        }
        draw.data.addRow([
            sT, 
            arr[i].materialcodeerp, 
            arr[i].materialname, 
            arr[i].spec, 
            arr[i].materialdescription, 
            arr[i].technicalprotocol, 
            arr[i].erpunit, 
            arr[i].quantity, 
            arr[i].isdonor === "0" ? '甲供' : arr[i].isdonor === "1" ? '乙供' : '', 
            arr[i].isspare === '1' ? '是' : '否', 
            arr[i].isloss === '1' ? '是' : '否'],
            rowHeight
        )
    }
    // let getPoint = new MxCADUiPrPoint();
    // getPoint.setMessage("\n指定表格插入点:");
    // let pt = await getPoint.go();
    // if (!pt) return;
    draw.draw(new McGePoint3d(30 + offsetX,270,0), lScale);
    // 刷新显示
    mxcad.regen();
}

// 钢管杆明细表生成dwg
export async function steelExcelFun (value, startIndex) {
    console.log("🚀 ~ steelExcelFun ~ value:", value)
    let mxcad = MxCpp.App.getCurrentMxCAD();
    let blkTable = mxcad.getDatabase().getBlockTable();
    
    let leftHeight = 0 // 左边框宽
    let rightHeight = 0 // 右边框宽
    let bottomWidth = 0 // 下边框宽
    let borderWidth = 0 // 外框宽
    let TqBorderWidth = 0 // 图签宽
    // 处理偏移量
    const offsetX = value.offsetX || 0; // 默认值为 0

    let blkFilePath = import.meta.url.substring(0, import.meta.url.lastIndexOf('/')) + `/assets/TuK/A4-210x297 标准改1.dwg.mxweb`;
    // 插入图块文件
    let blkrecId = await mxcad.insertBlock(blkFilePath, 'A4-210x297 标准改1.dwg');
    if (!blkrecId.isValid()) {
        return
    }

    let blkRef = new McDbBlockReference();
    blkRef.blockTableRecordId = blkrecId;
    blkRef.position = new McGePoint3d(offsetX,0,0);
    mxcad.drawEntity(blkRef);
    let blkRecord: any = blkrecId.getMcDbBlockTableRecord();
    let ids = blkRecord.getAllEntityId();
    ids.forEach((id: any, index: any) => {
        if (!id.isKindOf("McDbAttributeDefinition")) return;
        let attribDef = id.getMcDbEntity() as McDbAttributeDefinition;
        if (attribDef.tag === '左边框宽') {
            leftHeight = Number(attribDef.textString)
        } else if (attribDef.tag === '右边框宽') {
            rightHeight = Number(attribDef.textString)
        } else if (attribDef.tag === '外框宽') {
            borderWidth = Number(attribDef.textString)
        } else if (attribDef.tag === '下边框高') {
            bottomWidth = Number(attribDef.textString)
        }
    })
    let blkFilePathT = import.meta.url.substring(0, import.meta.url.lastIndexOf('/')) + `/assets/TuK/标准图签改.dwg.mxweb`;
    let blkrecIdT = await mxcad.insertBlock(blkFilePathT, '标准图签改.dwg');
    if (!blkrecIdT.isValid()) {
        return
    }
    let blkRefT = new McDbBlockReference();
    blkRefT.blockTableRecordId = blkrecIdT;
    let blkRecordT: any = blkrecIdT.getMcDbBlockTableRecord();
    let idsT = blkRecordT.getAllEntityId();
    idsT.forEach((idT: any, index: any) => {
        if (!idT.isKindOf("McDbAttributeDefinition")) return;
        let attribDef = idT.getMcDbEntity() as McDbAttributeDefinition;
        if (attribDef.tag === '图签宽') {
            TqBorderWidth = Number(attribDef.textString)
        }
    })
    blkRefT.position = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + offsetX,bottomWidth,0);
    mxcad.drawEntity(blkRefT);

    // 创建文本实体
    const text = new McDbText();
    text.textString = '钢管杆明细表'; // 使用传入的文本参数
    text.position = new McGePoint3d(115 + offsetX,275,0);
    text.alignmentPoint = new McGePoint3d(115 + offsetX,275,0);
    text.height = 7; // 文本高度
    text.widthFactor = 1; // 文本宽度缩放
    text.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    text.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    text.trueColor = new McCmColor(0, 0, 0); // 文本颜色（紫色）
    // 绘制文本
    mxcad.drawEntity(text);

    // 供电公司
    const textG = new McDbText();
    const positionG = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 10 + offsetX,bottomWidth + 16,0)
    textG.textString = value.countyOrganisationName ? value.countyOrganisationName : ''; // 使用传入的文本参数
    textG.position = positionG;
    textG.alignmentPoint = positionG;
    textG.height = 2; // 文本高度
    textG.widthFactor = 1; // 文本宽度缩放
    textG.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textG.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textG.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    mxcad.drawEntity(textG);

    // 项目名称
    const textPrject = new McDbText();
    const positionPrject = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 55 + offsetX,bottomWidth + 16,0)
    textPrject.textString = value.projectName ? value.projectName : ''; // 使用传入的文本参数
    textPrject.position = positionPrject;
    textPrject.alignmentPoint = positionPrject;
    textPrject.height = 2; // 文本高度
    textPrject.widthFactor = 1; // 文本宽度缩放
    textPrject.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textPrject.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textPrject.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    mxcad.drawEntity(textPrject);

    // 阶段
    const textJd = new McDbText();
    const positionJd = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 80 + offsetX,bottomWidth + 16,0)
    const textString = value.stage && value.stage === '1' ? '需求' : value.stage && value.stage === '2' ? '可研' : value.stage && value.stage === '3' ? '初设' : value.stage && value.stage === '4' ? '施工' : value.stage && value.stage === '5' ? '施工变更' : value.stage && value.stage === '5' ? '竣工' : ''
    textJd.textString = textString; // 使用传入的文本参数
    textJd.position = positionJd;
    textJd.alignmentPoint = positionJd;
    textJd.height = 2; // 文本高度
    textJd.widthFactor = 1; // 文本宽度缩放
    textJd.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textJd.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textJd.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    mxcad.drawEntity(textJd);

    // 时间
    const textTime = new McDbText();
    const positionTime = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 40 + offsetX,bottomWidth + 1.5,0)
    const textStringTime = min_getTime()
    textTime.textString = textStringTime; // 使用传入的文本参数
    textTime.position = positionTime;
    textTime.alignmentPoint = positionTime;
    textTime.height = 1; // 文本高度
    textTime.widthFactor = 1; // 文本宽度缩放
    textTime.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textTime.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textTime.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    mxcad.drawEntity(textTime);

    // 图号：项目编号+n
    const textCode = new McDbText();
    const positionCode = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 60 + offsetX,bottomWidth + 1.5,0)
    const textStringCode = value.proCode ? value.proCode + '-' + value.num : ''; // 使用传入的文本参数
    textCode.textString = textStringCode; // 使用传入的文本参数
    textCode.position = positionCode;
    textCode.alignmentPoint = positionCode;
    textCode.height = 1; // 文本高度
    textCode.widthFactor = 1; // 文本宽度缩放
    textCode.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textCode.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textCode.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    mxcad.drawEntity(textCode);

    // 图框名称：主要设备清单+当前页1,2,3
    const textK = new McDbText();
    const positionK = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 55 + offsetX,bottomWidth + 12,0)
    const textStringK = '钢管杆明细表-' + value.num; // 使用传入的文本参数
    textK.textString = textStringK; // 使用传入的文本参数
    textK.position = positionK;
    textK.alignmentPoint = positionK;
    textK.height = 2; // 文本高度
    textK.widthFactor = 1; // 文本宽度缩放
    textK.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textK.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textK.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    mxcad.drawEntity(textK);

    // 创建主表头表格
    let mainHeaderTable = new MxDrawTable();
    mainHeaderTable.data.setRowTextHeight(1)
    mainHeaderTable.data.addColumn(0, "序号", 5, 1.5);
    mainHeaderTable.data.addColumn(0, "杆型", 25, 1.5);
    mainHeaderTable.data.addColumn(0, "单基总重(kg)", 15, 1.5);
    mainHeaderTable.data.addColumn(0, "杆塔Ⅰ段", 10, 1.5); // 合并 "杆塔Ⅰ段(kg)", "螺栓脚钉垫圈重(kg)", "杆塔Ⅱ段(kg)" 的宽度
    mainHeaderTable.data.addColumn(0, "杆塔Ⅰ段", 20, 1.5); // 合并 "杆塔Ⅰ段(kg)", "螺栓脚钉垫圈重(kg)", "杆塔Ⅱ段(kg)" 的宽度
    mainHeaderTable.data.addColumn(0, "杆塔Ⅱ段", 10, 1.5);
    mainHeaderTable.data.addColumn(0, "爬梯重(kg)", 12, 1.5);
    mainHeaderTable.data.addColumn(0, "横担总重(kg)", 10, 1.5);
    mainHeaderTable.data.addColumn(0, "基数", 5, 1.5);
    mainHeaderTable.data.addColumn(0, "总重(kg)", 12, 1.5);

    mainHeaderTable.data.addColumn(1, "", 5, 1.5);
    mainHeaderTable.data.addColumn(1, "", 25, 1.5);
    mainHeaderTable.data.addColumn(1, "", 15, 1.5);
    mainHeaderTable.data.addColumn(1, "杆塔Ⅰ段(kg)", 10, 1.5); // 合并 "杆塔Ⅰ段(kg)", "螺栓脚钉垫圈重(kg)", "杆塔Ⅱ段(kg)" 的宽度
    mainHeaderTable.data.addColumn(1, "螺栓脚钉垫圈重(kg)", 20, 1.5); // 合并 "杆塔Ⅰ段(kg)", "螺栓脚钉垫圈重(kg)", "杆塔Ⅱ段(kg)" 的宽度
    mainHeaderTable.data.addColumn(1, "总重(kg)", 10, 1.5);
    mainHeaderTable.data.addColumn(1, "", 12, 1.5);
    mainHeaderTable.data.addColumn(1, "", 10, 1.5);
    mainHeaderTable.data.addColumn(1, "", 5, 1.5);
    mainHeaderTable.data.addColumn(1, "", 12, 1.5);
    // 添加数据
    const arr = value.tableData ? JSON.parse(value.tableData) : [];
    for (let i = 0; i < arr.length; i++) {
        let sT = "" + (i + 1 + startIndex);
        mainHeaderTable.data.addRow([
            sT, // 序号
            arr[i].RodType, // 杆型
            arr[i].GrossWeightOfSingleBase, // 单基总重(kg)
            arr[i].GD1, // 杆塔Ⅰ段(kg)
            arr[i].DJLS, // 螺栓脚钉垫圈重(kg)
            arr[i].GD2, // 杆塔Ⅱ段(kg)
            arr[i].PTZL, // 爬梯重(kg)
            arr[i].CrossArm, // 横担总重(kg)
            arr[i].Base, // 基数
            arr[i].FinalGrossWeight // 总重(kg)
        ]);
    }

    // 设置表格位置
    let lScale = 1//MxFun.screenCoordLong2Doc(100) / 200;
    // 绘制子表头和数据表格
    mainHeaderTable.draw(new McGePoint3d(30 + offsetX, 270 , 0), lScale);
    // 刷新显示
    mxcad.regen();
}

export async function steelExcelFun1(value) {
    console.log("🚀 ~ steelExcelFun1 ~ value:", value)
    // 处理偏移量
    const offsetX = value.offsetX || 0; // 默认值为 0
    let blkFilePath = import.meta.url.substring(0, import.meta.url.lastIndexOf('/')) + `/assets/TuK/A3-420x297  标准样图.dwg.mxweb`;
    // 插入图块文件
    let mxcad = MxCpp.App.getCurrentMxCAD();
    mxcad.newFile();
    let blkrecId = await mxcad.insertBlock(blkFilePath, 'A4-210x297 标准改1.dwg');
    // if (!blkrecId.isValid()) {
    //     return;
    // }

    let blkRef = new McDbBlockReference();
    blkRef.blockTableRecordId = blkrecId;
    blkRef.position = new McGePoint3d(offsetX, 0, 0);
    mxcad.drawEntity(blkRef);

    const height = 2;  // 长方形高度
    const textHeight = 2;
    const height1 = 58;  // 长方形高度
    const arr = value.tableData ? JSON.parse(value.tableData) : [];
    const allItems = arr.flatMap(item => item.matched || []);
    const uniqueParams = Array.from(
    new Map(allItems.map(item => [item.intervalparametername, item]))
    .values()
    ).map(item => item.intervalparametername);
    let matchedList=[];
    arr.forEach(item => {
    const rowData = new Array(uniqueParams.length).fill(''); // 初始化空行
    // 填充当前item的matched值
    (item.matched || []).forEach(matchedItem => {
        const paramIndex = uniqueParams.indexOf(matchedItem.intervalparametername);
        if (paramIndex !== -1) {
        rowData[paramIndex] = matchedItem.intervalparametervalue;
        }
    });
    matchedList.push(rowData);
    });
    const options = {
        startPt: new McGePoint3d(30, (271-height1)-1, 0), // 起始点
        titleWidth: 24,    // 标题列宽度
        dataWidth: 24,      // 数据列宽度
        rowHeight: 8,       // 行高
        textHeight: 2     // 文字高度
    };
    // 设置表格位置
    let lScale = 1; // 缩放比例
    // 调整长方形的位置，使其位于表格上方并与之拼接
    const centerPt = new McGePoint3d(30 + offsetX, 271, 0);
    const centerPt1 = new McGePoint3d(30 + offsetX, 271-height1/2-1, 0);
    let blkTable = mxcad.getDatabase().getBlockTable();
    let blkRecId = blkTable.add(new McDbBlockTableRecord());
    let blkTableRecord = blkRecId.getMcDbBlockTableRecord();
    await drawVerticalTableWithFixedTitle(mxcad,uniqueParams, matchedList, options, blkTableRecord);
    const arr1 = value.tableData ? JSON.parse(value.tableData) : [];
    // 如果 arr1 为空，直接返回
    if (arr1.length === 0) return;
    // 为 arr1 中的每个元素添加 width 属性
    // widthList[index]
    arr1.forEach((element, index) => {
        element.width = '24';
    });
    arr1.unshift({ name: "10kV母线",name2:"一次接线图", width: 24 })
    // 绘制长方形
   await drawRectangles(arr1, centerPt, height,textHeight, blkTableRecord,false);
   await drawRectangles(arr1, centerPt1, height1,textHeight, blkTableRecord,true);

    // 设置图块的基点
    blkTableRecord.origin = centerPt;

    // 实例化块参照
    let blkRef1 = new McDbBlockReference();
    blkRef1.blockTableRecordId = blkRecId;
    blkRef1.position = centerPt;
    mxcad.drawEntity(blkRef1);
    mxcad.updateDisplay();

    // 刷新显示
    mxcad.regen();

    // 返回一个 Promise
    return new Promise((resolve, reject) => {
        mxcad.saveFile(void 0, (data) => {
            let blob: Blob;
            const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
            if (isSafari) {
                blob = new Blob([data.buffer], { type: "application/octet-stream" });
            } else {
                blob = new Blob([data.buffer], { type: "application/octet-binary" });
            }
            const file = new File([blob], 'test' + value.num + '.mxweb', { type: isSafari ? "application/octet-stream" : "application/octet-binary" });
            resolve(file); // 返回文件对象
        }, false, true);
    });
}

/** 绘制竖向排列的表格（第一列为固定标题，后续为数据列）
 * @param {Array} titleColumn - 固定标题列 ['点操机构', '是否', '地方', ...]
 * @param {Array} dataRows - 数据行 [[行1数据], [行2数据], ...] 
 * @param {Object} options - 配置项
 * @param {McGePoint3d} options.startPt - 起始点坐标
 * @param {number} options.titleWidth - 标题列宽度
 * @param {number} options.dataWidth - 数据列宽度
 * @param {number} options.rowHeight - 行高
 * @param {number} options.textHeight - 文字高度
 * @param {MxDbBlockTableRecord} blkTableRecord - 块表记录
 */
async function drawVerticalTableWithFixedTitle(mxcad, titleColumn, dataRows, options, blkTableRecord) {
    const {
        startPt,
        titleWidth = 30,
        dataWidth = 40,
        rowHeight = 10,
        textHeight = 3
    } = options;

    let currentY = startPt.y;

    // 修正列数计算：1列标题 + dataRows.length列数据
    const colCount = 1 + dataRows.length;  // dataRows.length就是数据列数
    let currentX = startPt.x;

    // 绘制表格线（先画垂直线）
    for (let col = 0; col <= colCount; col++) {
        const lineStart = new McGePoint3d(currentX, currentY, startPt.z);
        const lineEnd = new McGePoint3d(
            currentX,
            currentY - titleColumn.length * rowHeight,
            startPt.z
        );
        await drawLine(mxcad, lineStart, lineEnd, 0.05);
        
        // 更新下一列位置
        currentX += (col === 0 ? titleWidth : dataWidth);
    }

    // 重置X坐标，绘制水平线和内容
    currentX = startPt.x;
    
    // 绘制标题列（第一列）
    for (let row = 0; row <= titleColumn.length; row++) {
        // 水平线
        const lineStart = new McGePoint3d(currentX, currentY, startPt.z);
        const lineEnd = new McGePoint3d(
            currentX + titleWidth + (dataWidth * (colCount - 1)),
            currentY,
            startPt.z
        );
        await drawLine(mxcad, lineStart, lineEnd, 0.05);

        // 标题列文字（最后一行不画）
        if (row < titleColumn.length) {
            const textCenter = new McGePoint3d(
                currentX + titleWidth / 2,
                currentY - rowHeight / 2,
                startPt.z
            );
            await drawText(blkTableRecord, textCenter, titleColumn[row], textHeight);
        }

        // 数据列内容
        for (let col = 1; col < colCount; col++) {
            if (row < titleColumn.length && dataRows[col-1] && dataRows[col-1][row]) {
                const dataCenter = new McGePoint3d(
                    currentX + titleWidth + (dataWidth * (col-1)) + dataWidth / 2,
                    currentY - rowHeight / 2,
                    startPt.z
                );
                await drawText(
                    blkTableRecord, 
                    dataCenter, 
                    dataRows[col-1][row], 
                    textHeight
                );
            }
        }

        currentY -= rowHeight;
    }
}

async function drawLine(mxcad,pt1, pt2, dLineWidth) {
    mxcad.drawLineWidth = dLineWidth;
    mxcad.drawColorIndex = ColorIndexType.kMagenta;
    mxcad.drawColor = new McCmColor(0, 0, 0);
    mxcad.drawLine(pt1.x, pt1.y, pt2.x, pt2.y);
}
async function drawRectangles(arr1, centerPt, height, textHeight, blkTableRecord, flag) {
    let mxcad = MxCpp.App.getCurrentMxCAD();
    let previousCenterX = centerPt.x;
    for (let i = 0; i < arr1.length; i++) {
        const newCenterPt = new McGePoint3d(previousCenterX + arr1[i].width / 2, centerPt.y, centerPt.z);
        // 绘制长方形
        await drawRectangle(blkTableRecord, newCenterPt, arr1[i].width, height);
        // **只在第一个长方形绘制文本**
        if (i === 0) {
            let textString = flag ? arr1[0].name2 : arr1[0].name;
            await drawText(blkTableRecord, newCenterPt, textString, textHeight);
        }
        // 插入图块 arr1[i].file（仅在 flag=true 且不是第一个长方形时插入）
        if (flag && i > 0) {
            await insertBlock1(mxcad, blkTableRecord, arr1[i].file, newCenterPt);
        }
        previousCenterX = newCenterPt.x + arr1[i].width / 2;
    }
}
async function drawRectangle(blkTableRecord, newCenterPt, width, height) {
    let pl = new McDbPolyline();
    pl.addVertexAt(new McGePoint3d(newCenterPt.x + width / 2, newCenterPt.y + height / 2, newCenterPt.z));
    pl.addVertexAt(new McGePoint3d(newCenterPt.x - width / 2, newCenterPt.y + height / 2, newCenterPt.z));
    pl.addVertexAt(new McGePoint3d(newCenterPt.x - width / 2, newCenterPt.y - height / 2, newCenterPt.z));
    pl.addVertexAt(new McGePoint3d(newCenterPt.x + width / 2, newCenterPt.y - height / 2, newCenterPt.z));
    pl.isClosed = true;
    blkTableRecord.appendAcDbEntity(pl);
}

async function drawText(blkTableRecord, newCenterPt, textString, textHeight) {
    const text = new McDbText();
    text.textString = textString;
    text.position = new McGePoint3d(newCenterPt.x, newCenterPt.y, newCenterPt.z);
    text.alignmentPoint = new McGePoint3d(newCenterPt.x, newCenterPt.y, newCenterPt.z);
    text.height = textHeight;
    text.widthFactor = 1;
    text.horizontalMode = McDb.TextHorzMode.kTextCenter;
    text.verticalMode = McDb.TextVertMode.kTextVertMid;
    text.trueColor = new McCmColor(0, 0, 0);
    blkTableRecord.appendAcDbEntity(text);
}

async function insertBlock1(mxcad, blkTableRecord, file, columnCenterPt) {
    // 将 Base64 数据转换为 Blob
    const byteCharacters = atob(file.split(',')[1]);
    const byteNumbers = new Array(byteCharacters.length);
    for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    const byteArray = new Uint8Array(byteNumbers);
    const blob = new Blob([byteArray], { type: 'text/plain' });

    // 创建 Blob URL
    const blkFilePathNr = URL.createObjectURL(blob);
    // 插入图块
    let blkrecIdNr = await mxcad.insertBlock(blkFilePathNr, '图纸名称');

    // 获取图块的边界框
    let dwgBTR = blkrecIdNr.getMcDbBlockTableRecord();
    const boundboxPoints = dwgBTR.getBoundingBox();

    // 计算图块的中心点
    const blockWidth = boundboxPoints.maxPt.x - boundboxPoints.minPt.x;
    const blockHeight = boundboxPoints.maxPt.y - boundboxPoints.minPt.y;
    const blockCenterX = boundboxPoints.minPt.x + blockWidth / 2;
    const blockCenterY = boundboxPoints.minPt.y + blockHeight / 2;

    // 调整图块的位置，使其中心与列中心对齐
    let blkRefNr = new McDbBlockReference();
    blkRefNr.blockTableRecordId = blkrecIdNr;
    blkRefNr.position = new McGePoint3d(
        columnCenterPt.x - blockCenterX, // 水平居中
        columnCenterPt.y - blockCenterY, // 垂直居中
        0
    );
    blkTableRecord.appendAcDbEntity(blkRefNr);
    mxcad.updateDisplay();
}
//拆旧物资清册
export async function demolitionMaterials (value) {
    console.log("🚀 ~ demolitionMaterials ~ value:", value)
    
    let leftHeight = 0 // 左边框宽
    let rightHeight = 0 // 右边框宽
    let bottomWidth = 0 // 下边框宽
    let borderWidth = 0 // 外框宽
    let TqBorderWidth = 0 // 图签宽
    // 处理偏移量
    const offsetX = value.offsetX || 0; // 默认值为 0

    let blkFilePath = import.meta.url.substring(0, import.meta.url.lastIndexOf('/')) + `/assets/TuK/A4-210x297 标准改1.dwg.mxweb`;
    // 插入图块文件
    let mxcad = MxCpp.App.getCurrentMxCAD();
    mxcad.newFile();
    let blkrecId = await mxcad.insertBlock(blkFilePath, 'A4-210x297 标准改1.dwg');
    if (!blkrecId.isValid()) {
        return
    }

    let blkRef = new McDbBlockReference();
    blkRef.blockTableRecordId = blkrecId;
    blkRef.position = new McGePoint3d(offsetX,0,0);
    mxcad.drawEntity(blkRef);
    let blkRecord: any = blkrecId.getMcDbBlockTableRecord();
    let ids = blkRecord.getAllEntityId();
    ids.forEach((id: any, index: any) => {
        if (!id.isKindOf("McDbAttributeDefinition")) return;
        let attribDef = id.getMcDbEntity() as McDbAttributeDefinition;
        if (attribDef.tag === '左边框宽') {
            leftHeight = Number(attribDef.textString)
        } else if (attribDef.tag === '右边框宽') {
            rightHeight = Number(attribDef.textString)
        } else if (attribDef.tag === '外框宽') {
            borderWidth = Number(attribDef.textString)
        } else if (attribDef.tag === '下边框高') {
            bottomWidth = Number(attribDef.textString)
        }
    })
    let blkFilePathT = import.meta.url.substring(0, import.meta.url.lastIndexOf('/')) + `/assets/TuK/标准图签改.dwg.mxweb`;
    let blkrecIdT = await mxcad.insertBlock(blkFilePathT, '标准图签改.dwg');
    if (!blkrecIdT.isValid()) {
        return
    }
    let blkRefT = new McDbBlockReference();
    blkRefT.blockTableRecordId = blkrecIdT;
    let blkRecordT: any = blkrecIdT.getMcDbBlockTableRecord();
    let idsT = blkRecordT.getAllEntityId();
    idsT.forEach((idT: any, index: any) => {
        if (!idT.isKindOf("McDbAttributeDefinition")) return;
        let attribDef = idT.getMcDbEntity() as McDbAttributeDefinition;
        if (attribDef.tag === '图签宽') {
            TqBorderWidth = Number(attribDef.textString)
        }
    })
    blkRefT.position = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + offsetX,bottomWidth,0);
    mxcad.drawEntity(blkRefT);

    // 创建文本实体
    const text = new McDbText();
    text.textString = '拆旧物资清册'; // 使用传入的文本参数
    text.position = new McGePoint3d(115 + offsetX,275,0);
    text.alignmentPoint = new McGePoint3d(115 + offsetX,275,0);
    text.height = 7; // 文本高度
    text.widthFactor = 1; // 文本宽度缩放
    text.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    text.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    text.trueColor = new McCmColor(0, 0, 0); // 文本颜色（紫色）
    // 绘制文本
    mxcad.drawEntity(text);
    function textSF(text){
        console.log(text.length,'文本长度')
        let  result = 1;//文本宽度缩放等级
        let num =0; //文本位置
        if( text.length>=20&&text.length<=28){
            result = 0.6 
            num=1
        }else if(text.length>28){
            result = 0.4
            num = 0.7
        }
        console.log(result,num)
        return {result,num};
    }
    // 供电公司
    const textG = new McDbText();
    let num=textSF(value.countyOrganisationName ? value.countyOrganisationName : '').num
    const positionG = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 17 + offsetX,bottomWidth + 16+num,0)
    textG.textString = value.countyOrganisationName ? value.countyOrganisationName : ''; // 使用传入的文本参数
    textG.position = positionG;
    textG.alignmentPoint = positionG;
    textG.height = 2; // 文本高度
    textG.widthFactor =  textSF(value.countyOrganisationName ? value.countyOrganisationName : '').result; // 文本宽度缩放
    textG.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textG.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textG.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    mxcad.drawEntity(textG);

    // 项目名称
    const textPrject = new McDbText();
    const positionPrject = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 59 + offsetX,bottomWidth + 16+textSF(value.projectName ? value.projectName : '').num,0)
    textPrject.textString = value.projectName ? value.projectName : ''; // 使用传入的文本参数
    textPrject.position = positionPrject;
    textPrject.alignmentPoint = positionPrject;
    textPrject.height = 2; // 文本高度
    textPrject.widthFactor = textSF(value.projectName ? value.projectName : '').result; // 文本宽度缩放
    textPrject.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textPrject.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textPrject.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    mxcad.drawEntity(textPrject);

    // 阶段
    const textJd = new McDbText();
    const positionJd = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 80 + offsetX,bottomWidth + 16,0)
    const textString = value.stage && value.stage === '1' ? '需求' : value.stage && value.stage === '2' ? '可研' : value.stage && value.stage === '3' ? '初设' : value.stage && value.stage === '4' ? '施工' : value.stage && value.stage === '5' ? '施工变更' : value.stage && value.stage === '5' ? '竣工' : ''
    textJd.textString = textString; // 使用传入的文本参数
    textJd.position = positionJd;
    textJd.alignmentPoint = positionJd;
    textJd.height = 2; // 文本高度
    textJd.widthFactor = 1; // 文本宽度缩放
    textJd.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textJd.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textJd.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    mxcad.drawEntity(textJd);

    // 时间
    const textTime = new McDbText();
    const positionTime = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 40 + offsetX,bottomWidth + 1.5,0)
    const textStringTime = min_getTime()
    textTime.textString = textStringTime; // 使用传入的文本参数
    textTime.position = positionTime;
    textTime.alignmentPoint = positionTime;
    textTime.height = 1; // 文本高度
    textTime.widthFactor = 1; // 文本宽度缩放
    textTime.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textTime.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textTime.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    mxcad.drawEntity(textTime);

    // 图号：项目编号+n
    const textCode = new McDbText();
    const positionCode = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 65 + offsetX,bottomWidth + 1.5,0)
    const textStringCode = value.proCode ? value.proCode + '-' + value.num : ''; // 使用传入的文本参数
    textCode.textString = textStringCode; // 使用传入的文本参数
    textCode.position = positionCode;
    textCode.alignmentPoint = positionCode;
    textCode.height = 1; // 文本高度
    textCode.widthFactor = 1; // 文本宽度缩放
    textCode.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textCode.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textCode.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    mxcad.drawEntity(textCode);

    // 图框名称：主要设备清单+当前页1,2,3
    const textK = new McDbText();
    const positionK = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 55 + offsetX,bottomWidth + 12,0)
    const textStringK = '拆旧物资清册-' + value.num; // 使用传入的文本参数
    textK.textString = textStringK; // 使用传入的文本参数
    textK.position = positionK;
    textK.alignmentPoint = positionK;
    textK.height = 2; // 文本高度
    textK.widthFactor = 1; // 文本宽度缩放
    textK.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textK.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textK.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    mxcad.drawEntity(textK);

// 创建主表头表格
let mainHeaderTable = new MxDrawTable();
mainHeaderTable.data.setRowTextHeight(1)
mainHeaderTable.data.addColumn(0, "序号", 5, 1.5);
mainHeaderTable.data.addColumn(0, "物料类别", 25, 1.5);
mainHeaderTable.data.addColumn(0, "物料名称", 30, 1.5);
mainHeaderTable.data.addColumn(0, "物料规格", 30, 1.5); // 合并 "杆塔Ⅰ段(kg)", "螺栓脚钉垫圈重(kg)", "杆塔Ⅱ段(kg)" 的宽度
mainHeaderTable.data.addColumn(0, "单重(kg)", 20, 1.5); // 合并 "杆塔Ⅰ段(kg)", "螺栓脚钉垫圈重(kg)", "杆塔Ⅱ段(kg)" 的宽度
mainHeaderTable.data.addColumn(0, "总重(kg)", 12, 1.5);
mainHeaderTable.data.addColumn(0, "总数量", 12, 1.5);
mainHeaderTable.data.addColumn(0, "单位", 12, 1.5);
mainHeaderTable.data.addColumn(0, "状态", 12, 1.5);
// 添加数据
const arr = value.tableData ? JSON.parse(value.tableData) : [];
if(arr.length===0){
arr.push({
    materialname:'',
    materialdescription:'',
    DJLS:'',
    GD2:'',
    num:'',
    designunit:'',
    state:''
})
}
let states=[{ StatusValue: 'RemoveUsing', StatusName: '拆除利旧' },
    { StatusValue: 'RemoveScrap', StatusName: '拆除报废' }, 
    { StatusValue: 'RemoveAbandon', StatusName: '拆除回收'}]
for (let i = 0; i < arr.length; i++) {
    console.log(arr[i],'llllllllllll');
    
    let sT = "" + (i + 1);
    let statusName = states.find(state => state.StatusValue === arr[i].state)?.StatusName ||'';
    mainHeaderTable.data.addRow([
        sT, // 序号
        arr[i].materialname, // 物料类别
        arr[i].materialname, // 物料名称
        arr[i].materialdescription, // 物料规格
        arr[i].DJLS, // 单重(kg)
        arr[i].GD2, // 总重(kg)
        arr[i].num, // 总数量
        arr[i].designunit, // 单位
        statusName, // 状态
    ]);
}

// 设置表格位置
let lScale = 1//MxFun.screenCoordLong2Doc(100) / 200;

// 绘制子表头和数据表格
mainHeaderTable.draw(new McGePoint3d(30 + offsetX, 270 , 0), lScale);

// 刷新显示
mxcad.regen();

    // 返回一个 Promise
    return new Promise((resolve, reject) => {
        mxcad.saveFile(void 0, (data) => {
            let blob: Blob;
            const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
            if (isSafari) {
                blob = new Blob([data.buffer], { type: "application/octet-stream" });
            } else {
                blob = new Blob([data.buffer], { type: "application/octet-binary" });
            }
            const file = new File([blob], 'test' + value.num + '.mxweb', { type: isSafari ? "application/octet-stream" : "application/octet-binary" });
            console.log(file,'filllllllllllll');
            
            resolve(file); // 返回文件对象
        }, false, true);
    });
}
//利旧清册
export async function ljqcExcelFun (value) {
    console.log("🚀 ~ ljqcExcelFun ~ value:", value)
    
    let leftHeight = 0 // 左边框宽
    let rightHeight = 0 // 右边框宽
    let bottomWidth = 0 // 下边框宽
    let borderWidth = 0 // 外框宽
    let TqBorderWidth = 0 // 图签宽
    // 处理偏移量
    const offsetX = value.offsetX || 0; // 默认值为 0

    let blkFilePath = import.meta.url.substring(0, import.meta.url.lastIndexOf('/')) + `/assets/TuK/A4-210x297 标准改1.dwg.mxweb`;
    // 插入图块文件
    let mxcad = MxCpp.App.getCurrentMxCAD();
    mxcad.newFile();
    let blkrecId = await mxcad.insertBlock(blkFilePath, 'A4-210x297 标准改1.dwg');
    if (!blkrecId.isValid()) {
        return
    }

    let blkRef = new McDbBlockReference();
    blkRef.blockTableRecordId = blkrecId;
    blkRef.position = new McGePoint3d(offsetX,0,0);
    mxcad.drawEntity(blkRef);
    let blkRecord: any = blkrecId.getMcDbBlockTableRecord();
    let ids = blkRecord.getAllEntityId();
    ids.forEach((id: any, index: any) => {
        if (!id.isKindOf("McDbAttributeDefinition")) return;
        let attribDef = id.getMcDbEntity() as McDbAttributeDefinition;
        if (attribDef.tag === '左边框宽') {
            leftHeight = Number(attribDef.textString)
        } else if (attribDef.tag === '右边框宽') {
            rightHeight = Number(attribDef.textString)
        } else if (attribDef.tag === '外框宽') {
            borderWidth = Number(attribDef.textString)
        } else if (attribDef.tag === '下边框高') {
            bottomWidth = Number(attribDef.textString)
        }
    })
    let blkFilePathT = import.meta.url.substring(0, import.meta.url.lastIndexOf('/')) + `/assets/TuK/标准图签改.dwg.mxweb`;
    let blkrecIdT = await mxcad.insertBlock(blkFilePathT, '标准图签改.dwg');
    if (!blkrecIdT.isValid()) {
        return
    }
    let blkRefT = new McDbBlockReference();
    blkRefT.blockTableRecordId = blkrecIdT;
    let blkRecordT: any = blkrecIdT.getMcDbBlockTableRecord();
    let idsT = blkRecordT.getAllEntityId();
    idsT.forEach((idT: any, index: any) => {
        if (!idT.isKindOf("McDbAttributeDefinition")) return;
        let attribDef = idT.getMcDbEntity() as McDbAttributeDefinition;
        if (attribDef.tag === '图签宽') {
            TqBorderWidth = Number(attribDef.textString)
        }
    })
    blkRefT.position = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + offsetX,bottomWidth,0);
    mxcad.drawEntity(blkRefT);

    // 创建文本实体
    const text = new McDbText();
    text.textString = '利旧物资清册'; // 使用传入的文本参数
    text.position = new McGePoint3d(115 + offsetX,275,0);
    text.alignmentPoint = new McGePoint3d(115 + offsetX,275,0);
    text.height = 7; // 文本高度
    text.widthFactor = 1; // 文本宽度缩放
    text.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    text.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    text.trueColor = new McCmColor(0, 0, 0); // 文本颜色（紫色）
    // 绘制文本
    mxcad.drawEntity(text);

    // 供电公司
    const textG = new McDbText();
    const positionG = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 10 + offsetX,bottomWidth + 16,0)
    textG.textString = value.countyOrganisationName ? value.countyOrganisationName : ''; // 使用传入的文本参数
    textG.position = positionG;
    textG.alignmentPoint = positionG;
    textG.height = 2; // 文本高度
    textG.widthFactor = 1; // 文本宽度缩放
    textG.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textG.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textG.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    mxcad.drawEntity(textG);

    // 项目名称
    const textPrject = new McDbText();
    const positionPrject = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 55 + offsetX,bottomWidth + 16,0)
    textPrject.textString = value.projectName ? value.projectName : ''; // 使用传入的文本参数
    textPrject.position = positionPrject;
    textPrject.alignmentPoint = positionPrject;
    textPrject.height = 2; // 文本高度
    textPrject.widthFactor = 1; // 文本宽度缩放
    textPrject.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textPrject.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textPrject.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    mxcad.drawEntity(textPrject);

    // 阶段
    const textJd = new McDbText();
    const positionJd = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 80 + offsetX,bottomWidth + 16,0)
    const textString = value.stage && value.stage === '1' ? '需求' : value.stage && value.stage === '2' ? '可研' : value.stage && value.stage === '3' ? '初设' : value.stage && value.stage === '4' ? '施工' : value.stage && value.stage === '5' ? '施工变更' : value.stage && value.stage === '5' ? '竣工' : ''
    textJd.textString = textString; // 使用传入的文本参数
    textJd.position = positionJd;
    textJd.alignmentPoint = positionJd;
    textJd.height = 2; // 文本高度
    textJd.widthFactor = 1; // 文本宽度缩放
    textJd.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textJd.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textJd.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    mxcad.drawEntity(textJd);

    // 时间
    const textTime = new McDbText();
    const positionTime = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 40 + offsetX,bottomWidth + 1.5,0)
    const textStringTime = min_getTime()
    textTime.textString = textStringTime; // 使用传入的文本参数
    textTime.position = positionTime;
    textTime.alignmentPoint = positionTime;
    textTime.height = 1; // 文本高度
    textTime.widthFactor = 1; // 文本宽度缩放
    textTime.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textTime.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textTime.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    mxcad.drawEntity(textTime);

    // 图号：项目编号+n
    const textCode = new McDbText();
    const positionCode = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 60 + offsetX,bottomWidth + 1.5,0)
    const textStringCode = value.proCode ? value.proCode + '-' + value.num : ''; // 使用传入的文本参数
    textCode.textString = textStringCode; // 使用传入的文本参数
    textCode.position = positionCode;
    textCode.alignmentPoint = positionCode;
    textCode.height = 1; // 文本高度
    textCode.widthFactor = 1; // 文本宽度缩放
    textCode.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textCode.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textCode.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    mxcad.drawEntity(textCode);

    // 图框名称：主要设备清单+当前页1,2,3
    const textK = new McDbText();
    const positionK = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 55 + offsetX,bottomWidth + 12,0)
    const textStringK = '利旧物资清册-' + value.num; // 使用传入的文本参数
    textK.textString = textStringK; // 使用传入的文本参数
    textK.position = positionK;
    textK.alignmentPoint = positionK;
    textK.height = 2; // 文本高度
    textK.widthFactor = 1; // 文本宽度缩放
    textK.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textK.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textK.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    mxcad.drawEntity(textK);

    // 创建主表头表格
    let mainHeaderTable = new MxDrawTable();
    mainHeaderTable.data.setRowTextHeight(1)
    mainHeaderTable.data.addColumn(0, "序号", 5, 1.5);
    mainHeaderTable.data.addColumn(0, "物料类别", 15, 1.5);
    mainHeaderTable.data.addColumn(0, "物料名称", 35, 1.5);
    mainHeaderTable.data.addColumn(0, "物料规格", 35, 1.5); 
    mainHeaderTable.data.addColumn(0, "利旧数量", 10, 1.5); 
    mainHeaderTable.data.addColumn(0, "拆除利旧数量", 10, 1.5);
    mainHeaderTable.data.addColumn(0, "单位", 10, 1.5);
    mainHeaderTable.data.addColumn(0, "年限", 10, 1.5);
    mainHeaderTable.data.addColumn(0, "备注", 12, 1.5);
    // 添加数据
    const arr = value.tableData ? JSON.parse(value.tableData) : [];
    for (let i = 0; i < arr.length; i++) {
        let sT = "" + (i + 1);
        mainHeaderTable.data.addRow([
            arr[i].number, 
            arr[i].materialCategory, 
            arr[i].materialName, 
            arr[i].materialSpecification, 
            arr[i].newReusedQuantity+'', 
            arr[i].removedReusedQuantity, 
            arr[i].unit, 
            "", 
            arr[i].remarks, 
        ]);
    }

    // 设置表格位置
    let lScale = 1//MxFun.screenCoordLong2Doc(100) / 200;

    // 绘制子表头和数据表格
    mainHeaderTable.draw(new McGePoint3d(30 + offsetX, 270 , 0), lScale);

    // 刷新显示
    mxcad.regen();

    // 返回一个 Promise
    return new Promise((resolve, reject) => {
        mxcad.saveFile(void 0, (data) => {
            let blob: Blob;
            const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
            if (isSafari) {
                blob = new Blob([data.buffer], { type: "application/octet-stream" });
            } else {
                blob = new Blob([data.buffer], { type: "application/octet-binary" });
            }
            const file = new File([blob], 'test' + value.num + '.mxweb', { type: isSafari ? "application/octet-stream" : "application/octet-binary" });
            resolve(file); // 返回文件对象
        }, false, true);
    });
}

//杆型一览图
export async function gxylExcelFun (value, startIndex) {
    console.log("🚀 ~ gxylExcelFun ~ value:", value)
    
    let mxcad = MxCpp.App.getCurrentMxCAD();
    let blkTable = mxcad.getDatabase().getBlockTable();

    let leftHeight = 0 // 左边框宽
    let rightHeight = 0 // 右边框宽
    let bottomWidth = 0 // 下边框宽
    let borderWidth = 0 // 外框宽
    let TqBorderWidth = 0 // 图签宽
    // 处理偏移量
    const offsetX = value.offsetX || 0; // 默认值为 0

    let blkFilePath = import.meta.url.substring(0, import.meta.url.lastIndexOf('/')) + `/assets/TuK/A3-420x297  标准样图.dwg.mxweb`;
    // 插入图块文件
    let blkrecId = await mxcad.insertBlock(blkFilePath, 'A3-420x297  标准样图.dwg');
    if (!blkrecId.isValid()) {
        return
    }

    let blkRef = new McDbBlockReference();
    blkRef.blockTableRecordId = blkrecId;
    blkRef.position = new McGePoint3d(offsetX,0,0);
    mxcad.drawEntity(blkRef);
    let blkRecord: any = blkrecId.getMcDbBlockTableRecord();
    let ids = blkRecord.getAllEntityId();
    ids.forEach((id: any, index: any) => {
        if (!id.isKindOf("McDbAttributeDefinition")) return;
        let attribDef = id.getMcDbEntity() as McDbAttributeDefinition;
        if (attribDef.tag === '左边框宽') {
            leftHeight = Number(attribDef.textString)
        } else if (attribDef.tag === '右边框宽') {
            rightHeight = Number(attribDef.textString)
        } else if (attribDef.tag === '外框宽') {
            borderWidth = Number(attribDef.textString)
        } else if (attribDef.tag === '下边框高') {
            bottomWidth = Number(attribDef.textString)
        }
    })
    let blkFilePathT = import.meta.url.substring(0, import.meta.url.lastIndexOf('/')) + `/assets/TuK/标准图签改.dwg.mxweb`;
    let blkrecIdT = await mxcad.insertBlock(blkFilePathT, '标准图签改.dwg');
    if (!blkrecIdT.isValid()) {
        return
    }
    let blkRefT = new McDbBlockReference();
    blkRefT.blockTableRecordId = blkrecIdT;
    let blkRecordT: any = blkrecIdT.getMcDbBlockTableRecord();
    let idsT = blkRecordT.getAllEntityId();
    idsT.forEach((idT: any, index: any) => {
        if (!idT.isKindOf("McDbAttributeDefinition")) return;
        let attribDef = idT.getMcDbEntity() as McDbAttributeDefinition;
        if (attribDef.tag === '图签宽') {
            TqBorderWidth = Number(attribDef.textString)
        }
    })
    blkRefT.position = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + offsetX,bottomWidth,0);
    mxcad.drawEntity(blkRefT);

    //#region 非内容
    // 创建文本实体
    const text = new McDbText();
    text.textString = '配电线路杆型一览图'; // 使用传入的文本参数
    text.position = new McGePoint3d(210 + offsetX,275,0);
    text.alignmentPoint = new McGePoint3d(210 + offsetX,275,0);
    text.height = 7; // 文本高度
    text.widthFactor = 1; // 文本宽度缩放
    text.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    text.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    text.trueColor = new McCmColor(0, 0, 0); // 文本颜色（紫色）
    // 绘制文本
    mxcad.drawEntity(text);

    // 供电公司
    const textG = new McDbText();
    const positionG = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 10 + offsetX,bottomWidth + 16,0)
    textG.textString = value.countyOrganisationName ? value.countyOrganisationName : ''; // 使用传入的文本参数
    textG.position = positionG;
    textG.alignmentPoint = positionG;
    textG.height = 2; // 文本高度
    textG.widthFactor = 1; // 文本宽度缩放
    textG.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textG.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textG.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    mxcad.drawEntity(textG);

    // 项目名称
    const textPrject = new McDbText();
    const positionPrject = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 55 + offsetX,bottomWidth + 16,0)
    textPrject.textString = value.projectName ? value.projectName : ''; // 使用传入的文本参数
    textPrject.position = positionPrject;
    textPrject.alignmentPoint = positionPrject;
    textPrject.height = 2; // 文本高度
    textPrject.widthFactor = 1; // 文本宽度缩放
    textPrject.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textPrject.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textPrject.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    mxcad.drawEntity(textPrject);

    // 阶段
    const textJd = new McDbText();
    const positionJd = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 80 + offsetX,bottomWidth + 16,0)
    const textString = value.stage && value.stage === '1' ? '需求' : value.stage && value.stage === '2' ? '可研' : value.stage && value.stage === '3' ? '初设' : value.stage && value.stage === '4' ? '施工' : value.stage && value.stage === '5' ? '施工变更' : value.stage && value.stage === '5' ? '竣工' : ''
    textJd.textString = textString; // 使用传入的文本参数
    textJd.position = positionJd;
    textJd.alignmentPoint = positionJd;
    textJd.height = 2; // 文本高度
    textJd.widthFactor = 1; // 文本宽度缩放
    textJd.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textJd.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textJd.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    mxcad.drawEntity(textJd);

    // 时间
    const textTime = new McDbText();
    const positionTime = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 40 + offsetX,bottomWidth + 1.5,0)
    const textStringTime = min_getTime()
    textTime.textString = textStringTime; // 使用传入的文本参数
    textTime.position = positionTime;
    textTime.alignmentPoint = positionTime;
    textTime.height = 1; // 文本高度
    textTime.widthFactor = 1; // 文本宽度缩放
    textTime.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textTime.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textTime.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    mxcad.drawEntity(textTime);

    // 图号：项目编号+n
    const textCode = new McDbText();
    const positionCode = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 60 + offsetX,bottomWidth + 1.5,0)
    const textStringCode = value.proCode ? value.proCode + '-' + value.num : ''; // 使用传入的文本参数
    textCode.textString = textStringCode; // 使用传入的文本参数
    textCode.position = positionCode;
    textCode.alignmentPoint = positionCode;
    textCode.height = 1; // 文本高度
    textCode.widthFactor = 1; // 文本宽度缩放
    textCode.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textCode.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textCode.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    mxcad.drawEntity(textCode);

    // 图框名称：XXX+当前页1,2,3
    const textK = new McDbText();
    const positionK = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 55 + offsetX,bottomWidth + 12,0)
    const textStringK = '杆塔型式一览图-' + value.num; // 使用传入的文本参数
    textK.textString = textStringK; // 使用传入的文本参数
    textK.position = positionK;
    textK.alignmentPoint = positionK;
    textK.height = 2; // 文本高度
    textK.widthFactor = 1; // 文本宽度缩放
    textK.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textK.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textK.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    mxcad.drawEntity(textK);
    //#endregion
    
    const arr = value.tableData ? JSON.parse(value.tableData) : [];
    //七列八行
    // 创建主表头表格
    let mainHeaderTable = new MxDrawTable();
    mainHeaderTable.data.setRowTitleHeight(4)
    mainHeaderTable.data.setRowTextHeight(2.5)
    mainHeaderTable.data.addColumn(0, "序号", 35, 2.5);
    mainHeaderTable.data.addColumn(0, startIndex + 1, 57, 2.5);
    mainHeaderTable.data.addColumn(0, startIndex + 2, 57, 2.5);
    mainHeaderTable.data.addColumn(0, startIndex + 3, 57, 2.5); 
    mainHeaderTable.data.addColumn(0, startIndex + 4, 57, 2.5); 
    mainHeaderTable.data.addColumn(0, startIndex + 5, 57, 2.5);
    mainHeaderTable.data.addColumn(0, startIndex + 6, 57, 2.5);
    const rodTypes = ["杆型"]
    const poleHeights = ["杆身重量"]
    const anchorBoltHeights = ["地脚螺栓/钢管杆重量"]
    const baseTypes = ["基础类型"]
    const userNumberLists = ["适用杆号"]
    const remakess = ["备注"]
    const dwgs = ["杆型简图"]
    for (let i = 0; i < 6; i++) {
        dwgs.push("")
        if(i >= arr.length){
            rodTypes.push("")
            poleHeights.push("")
            anchorBoltHeights.push("")
            baseTypes.push("")
            userNumberLists.push("")
            remakess.push("")
        } else{
            rodTypes.push(arr[i].rodType)
            poleHeights.push(arr[i].poleHeight)
            anchorBoltHeights.push(arr[i].anchorBoltHeight)
            baseTypes.push(arr[i].baseType)
            userNumberLists.push(`${arr[i].userNumberList.join(',')} \\P (共${arr[i].count}基)`)
            remakess.push(arr[i].remakes.join(','))
            console.error("插入图纸", value.fileBlobArr[i])
            try{
                let blkFilePathNr =  value.fileBlobArr[i] ? URL.createObjectURL(value.fileBlobArr[i].fileBlob) : ''
                let blkRecId = blkTable.add(new McDbBlockTableRecord());
                // 根据ObjectId再次得到刚刚添加的图块记录
                let blkTableRecord:McDbBlockTableRecord = blkRecId.getMcDbBlockTableRecord()
                let blkrecIdNr = await mxcad.insertBlock(blkFilePathNr, value.fileBlobArr[i].drawPath.split('/').pop());
                if (!blkrecIdNr.isValid()) {
                    continue
                }
                let dwgBTR:McDbBlockTableRecord = blkrecIdNr.getMcDbBlockTableRecord() as any;
                const boundboxPoints = dwgBTR.getBoundingBox()
                dwgBTR.origin = boundboxPoints.minPt;
                const maxPoiont = boundboxPoints.maxPt
                // 格子高 170  宽 57 计算图块缩放比例
                const scale = Math.min(57 / Math.abs(maxPoiont.x - dwgBTR.origin.x), 170 / Math.abs(maxPoiont.y - dwgBTR.origin.y))
                let blkRefNr = new McDbBlockReference();
                blkRefNr.blockTableRecordId = blkrecIdNr;
                blkRefNr.position = new McGePoint3d(30+offsetX+35+57*i, 26, 0)
                blkRefNr.setScale(scale)
                blkTableRecord.appendAcDbEntity(blkRefNr)
    
                // 实例化块参照 这里需要设置我们刚刚添加图块记录得到的ObjectId
                let blkRefObj = new McDbBlockReference();
                blkRefObj.blockTableRecordId = blkRecId;
                // 最后设置位置 渲染图块
                mxcad.drawEntity(blkRefObj);
            } catch (error) {
                console.error("插入图纸失败", error, value.fileBlobArr[i])
            }
            mxcad.updateDisplay()
            mxcad.regen();
        }
    }
    mainHeaderTable.data.addRow(rodTypes, 15)//杆型
    mainHeaderTable.data.addRow(poleHeights, 10)//杆身重量
    mainHeaderTable.data.addRow(anchorBoltHeights, 10)//地脚螺栓/钢管杆重量
    mainHeaderTable.data.addRow(baseTypes, 10)//基础类型
    mainHeaderTable.data.addRow(userNumberLists, 15)//适用杆号+数量
    mainHeaderTable.data.addRow(remakess, 10)//备注
    mainHeaderTable.data.addRow(dwgs, 170)

    let lScale = 1//MxFun.screenCoordLong2Doc(100) / 200;
    // 绘制子表头和数据表格
    mainHeaderTable.draw(new McGePoint3d(30 + offsetX, 270 , 0), lScale);
    // 刷新显示
    mxcad.regen();
}
//基础型式一览图
export async function jcylExcelFun (value, startIndex) {
    console.log("🚀 ~ jcylExcelFun ~ value:", value)
    let mxcad = MxCpp.App.getCurrentMxCAD();
    let blkTable = mxcad.getDatabase().getBlockTable();

    let leftHeight = 0 // 左边框宽
    let rightHeight = 0 // 右边框宽
    let bottomWidth = 0 // 下边框宽
    let borderWidth = 0 // 外框宽
    let TqBorderWidth = 0 // 图签宽
    // 处理偏移量
    const offsetX = value.offsetX || 0; // 默认值为 0

    let blkFilePath = import.meta.url.substring(0, import.meta.url.lastIndexOf('/')) + `/assets/TuK/A3-420x297  标准样图.dwg.mxweb`;
    // 插入图块文件
    let blkrecId = await mxcad.insertBlock(blkFilePath, 'A3-420x297  标准样图.dwg');
    if (!blkrecId.isValid()) {
        return
    }

    let blkRef = new McDbBlockReference();
    blkRef.blockTableRecordId = blkrecId;
    blkRef.position = new McGePoint3d(offsetX,0,0);
    mxcad.drawEntity(blkRef);
    let blkRecord: any = blkrecId.getMcDbBlockTableRecord();
    let ids = blkRecord.getAllEntityId();
    ids.forEach((id: any, index: any) => {
        if (!id.isKindOf("McDbAttributeDefinition")) return;
        let attribDef = id.getMcDbEntity() as McDbAttributeDefinition;
        if (attribDef.tag === '左边框宽') {
            leftHeight = Number(attribDef.textString)
        } else if (attribDef.tag === '右边框宽') {
            rightHeight = Number(attribDef.textString)
        } else if (attribDef.tag === '外框宽') {
            borderWidth = Number(attribDef.textString)
        } else if (attribDef.tag === '下边框高') {
            bottomWidth = Number(attribDef.textString)
        }
    })
    let blkFilePathT = import.meta.url.substring(0, import.meta.url.lastIndexOf('/')) + `/assets/TuK/标准图签改.dwg.mxweb`;
    let blkrecIdT = await mxcad.insertBlock(blkFilePathT, '标准图签改.dwg');
    if (!blkrecIdT.isValid()) {
        return
    }
    let blkRefT = new McDbBlockReference();
    blkRefT.blockTableRecordId = blkrecIdT;
    let blkRecordT: any = blkrecIdT.getMcDbBlockTableRecord();
    let idsT = blkRecordT.getAllEntityId();
    idsT.forEach((idT: any, index: any) => {
        if (!idT.isKindOf("McDbAttributeDefinition")) return;
        let attribDef = idT.getMcDbEntity() as McDbAttributeDefinition;
        if (attribDef.tag === '图签宽') {
            TqBorderWidth = Number(attribDef.textString)
        }
    })
    blkRefT.position = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + offsetX,bottomWidth,0);
    mxcad.drawEntity(blkRefT);

    //#region 非内容 标题和图签文本
    // 创建文本实体
    const text = new McDbText();
    text.textString = '配电线路基础型式一览图'; // 使用传入的文本参数
    text.position = new McGePoint3d(210 + offsetX,275,0);
    text.alignmentPoint = new McGePoint3d(210 + offsetX,275,0);
    text.height = 7; // 文本高度
    text.widthFactor = 1; // 文本宽度缩放
    text.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    text.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    text.trueColor = new McCmColor(0, 0, 0); // 文本颜色（紫色）
    // 绘制文本
    mxcad.drawEntity(text);

    // 供电公司
    const textG = new McDbText();
    const positionG = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 10 + offsetX,bottomWidth + 16,0)
    textG.textString = value.countyOrganisationName ? value.countyOrganisationName : ''; // 使用传入的文本参数
    textG.position = positionG;
    textG.alignmentPoint = positionG;
    textG.height = 2; // 文本高度
    textG.widthFactor = 1; // 文本宽度缩放
    textG.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textG.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textG.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    mxcad.drawEntity(textG);

    // 项目名称
    const textPrject = new McDbText();
    const positionPrject = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 55 + offsetX,bottomWidth + 16,0)
    textPrject.textString = value.projectName ? value.projectName : ''; // 使用传入的文本参数
    textPrject.position = positionPrject;
    textPrject.alignmentPoint = positionPrject;
    textPrject.height = 2; // 文本高度
    textPrject.widthFactor = 1; // 文本宽度缩放
    textPrject.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textPrject.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textPrject.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    mxcad.drawEntity(textPrject);

    // 阶段
    const textJd = new McDbText();
    const positionJd = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 80 + offsetX,bottomWidth + 16,0)
    const textString = value.stage && value.stage === '1' ? '需求' : value.stage && value.stage === '2' ? '可研' : value.stage && value.stage === '3' ? '初设' : value.stage && value.stage === '4' ? '施工' : value.stage && value.stage === '5' ? '施工变更' : value.stage && value.stage === '5' ? '竣工' : ''
    textJd.textString = textString; // 使用传入的文本参数
    textJd.position = positionJd;
    textJd.alignmentPoint = positionJd;
    textJd.height = 2; // 文本高度
    textJd.widthFactor = 1; // 文本宽度缩放
    textJd.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textJd.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textJd.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    mxcad.drawEntity(textJd);

    // 时间
    const textTime = new McDbText();
    const positionTime = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 40 + offsetX,bottomWidth + 1.5,0)
    const textStringTime = min_getTime()
    textTime.textString = textStringTime; // 使用传入的文本参数
    textTime.position = positionTime;
    textTime.alignmentPoint = positionTime;
    textTime.height = 1; // 文本高度
    textTime.widthFactor = 1; // 文本宽度缩放
    textTime.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textTime.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textTime.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    mxcad.drawEntity(textTime);

    // 图号：项目编号+n
    const textCode = new McDbText();
    const positionCode = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 60 + offsetX,bottomWidth + 1.5,0)
    const textStringCode = value.proCode ? value.proCode + '-' + value.num : ''; // 使用传入的文本参数
    textCode.textString = textStringCode; // 使用传入的文本参数
    textCode.position = positionCode;
    textCode.alignmentPoint = positionCode;
    textCode.height = 1; // 文本高度
    textCode.widthFactor = 1; // 文本宽度缩放
    textCode.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textCode.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textCode.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    mxcad.drawEntity(textCode);

    // 图框名称：XXX+当前页1,2,3
    const textK = new McDbText();
    const positionK = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 55 + offsetX,bottomWidth + 12,0)
    const textStringK = '基础型式一览图-' + value.num; // 使用传入的文本参数
    textK.textString = textStringK; // 使用传入的文本参数
    textK.position = positionK;
    textK.alignmentPoint = positionK;
    textK.height = 2; // 文本高度
    textK.widthFactor = 1; // 文本宽度缩放
    textK.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textK.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textK.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    mxcad.drawEntity(textK);
    //#endregion
    
    const arr = value.tableData ? JSON.parse(value.tableData) : [];
    //#region 报表内容1 五列五行
    // 创建主表头表格
    let mainHeaderTable1 = new MxDrawTable();
    mainHeaderTable1.data.setRowTitleHeight(12.5)
    mainHeaderTable1.data.setRowTextHeight(2.5)
    mainHeaderTable1.data.addColumn(0, "序号", 22, 2.5);
    mainHeaderTable1.data.addColumn(0, startIndex + 1, 82.6, 2.5);
    mainHeaderTable1.data.addColumn(0, startIndex + 2, 82.6, 2.5);
    mainHeaderTable1.data.addColumn(0, startIndex + 3, 82.6, 2.5); 
    mainHeaderTable1.data.addColumn(0, startIndex + 4, 82.6, 2.5); 
    const baseTypes = ["基础类型"]
    const applys = ["适用于"]
    const counts = ["数量"]
    const dwgs = ["基础简图"]
    for (let i = 0; i < 6; i++) {
        dwgs.push("")
        if(i >= arr.length){
            counts.push("")
            baseTypes.push("")
            applys.push("")
        } else{
            baseTypes.push(arr[i].moduleName)
            applys.push(arr[i].basicType.join('\\P'))
            counts.push(arr[i].count)
            console.error("插入图纸", value.fileBlobArr[i])
            try{
                let blkFilePathNr =  value.fileBlobArr[i] ? URL.createObjectURL(value.fileBlobArr[i].fileBlob) : ''
                let blkRecId = blkTable.add(new McDbBlockTableRecord());
                // 根据ObjectId再次得到刚刚添加的图块记录
                let blkTableRecord:McDbBlockTableRecord = blkRecId.getMcDbBlockTableRecord()
                let blkrecIdNr = await mxcad.insertBlock(blkFilePathNr, value.fileBlobArr[i].drawPath.split('/').pop());
                if (!blkrecIdNr.isValid()) {
                    continue
                }
                let dwgBTR:McDbBlockTableRecord = blkrecIdNr.getMcDbBlockTableRecord() as any;
                const boundboxPoints = dwgBTR.getBoundingBox()
                dwgBTR.origin = boundboxPoints.minPt;
                const maxPoiont = boundboxPoints.maxPt
                // 格子高 117  宽 82.6 计算图块缩放比例
                const scale = Math.min(82.6 / Math.abs(maxPoiont.x - dwgBTR.origin.x), 117 / Math.abs(maxPoiont.y - dwgBTR.origin.y))
                let blkRefNr = new McDbBlockReference();
                blkRefNr.blockTableRecordId = blkrecIdNr;
                blkRefNr.position = new McGePoint3d(67+offsetX+82.6*i, 102.6, 0)
                blkRefNr.setScale(scale)
    
                blkTableRecord.appendAcDbEntity(blkRefNr)
                // 实例化块参照 这里需要设置我们刚刚添加图块记录得到的ObjectId
                let blkRefObj = new McDbBlockReference();
                blkRefObj.blockTableRecordId = blkRecId;
                // 最后设置位置 渲染图块
                mxcad.drawEntity(blkRefObj);
            }catch(e){
                console.error('插入图纸报错', value.fileBlobArr[i]);
            }
            mxcad.updateDisplay()
            mxcad.regen();
        }
    }
    mainHeaderTable1.data.addRow(baseTypes, 12.5)
    mainHeaderTable1.data.addRow(applys, 12.5)
    mainHeaderTable1.data.addRow(counts, 12.5)
    mainHeaderTable1.data.addRow(dwgs, 117)

    let lScale1 = 1//MxFun.screenCoordLong2Doc(100) / 200;
    // 绘制子表头和数据表格
    mainHeaderTable1.draw(new McGePoint3d(45 + offsetX, 270 , 0), lScale1);
    //#endregion

    //#region 报表内容2 
    // {
    //     两行 {"基础组件名称",  { colIndex = 0} }, moduleName   
    //     两行 {"基础类型",  { colIndex = 1} }, basicType []
    //     两行 {"适用杆径",  { colIndex = 2} }, sygj
    //     两行 {"典设代码",  { colIndex = 3} }, dsdm
    //     地栓护帽 {"等级1",  { colIndex = 4} }, dshmdj
    //     地栓护帽 {"体积(m^3)1",  { colIndex = 5} },  dshmtj
    //     垫层 {"等级2",  { colIndex =6} }, dcdj
    //     垫层 {"体积(m^3)2",  { colIndex =7} }, dctj
    //     混凝土（基础） {"等级3",  { colIndex = 8} }, hntjcdj
    //     混凝土（基础） {"体积(m^3)3",  { colIndex = 9} }, hntjctj
    //     混凝土（桩基） {"等级4",  { colIndex = 10} }, hntzjdj
    //     混凝土（桩基） {"体积(m^3)4",  { colIndex = 11} }, hntzjtj 
    //     钢筋重量 {"Q235B（钢管桩）",  { colIndex = 12} }, q235bggz
    //     钢筋重量 {"Q235B（压桩帽）",  { colIndex = 13} }, q235byzm
    //     钢筋重量 {"HPB235",  { colIndex = 14} }, hpb235
    //     钢筋重量 {"HRB335",  { colIndex = 15} }, hrb335
    //     钢筋重量 {"HPB300",  { colIndex = 16} }, hpb300
    //     钢筋重量 {"HRB400",  { colIndex = 17} }, hpb400
    //     钢筋重量 {"I级",  { colIndex = 18} }, gjzl1
    //     钢筋重量 {"II级",  { colIndex = 19} }, gjzl2
    //     钢筋重量 {"III级",  { colIndex = 20} }, gjzl3
    //     钢筋重量 {"合计",  { colIndex = 21} }, sum
    //     两行 {"适用土质",  { colIndex = 22} }, sytz
    //     两行 {"适用弯矩（KN·m）",  { colIndex = 23} } sywj
    // };
    const checkValues = ["moduleName", "dcdj", "dctj", "dsdm", "dshmdj", "dshmtj", "gjzl1", "gjzl2", "gjzl3", "hntjcdj", "hntjctj", "hntzjdj", "hntzjtj", "q235bggz", "q235byzm", 
        "hpb235", "hrb335", "hpb300", "hpb400", "sum", "sygj", "sytz", "sywj"]
    const checkDataInfo = {moduleName: false, basicType: false, dcdj: false, dctj: false, dsdm: false, dshmdj: false, dshmtj: false, gjzl1: false, gjzl2: false, gjzl3: false, hntjcdj: false, hntjctj: false, 
        hntzjdj: false, hntzjtj: false, q235bggz: false, q235byzm: false, hpb235: false, hrb335: false, hpb300: false, hpb400: false, sum: false, sygj: false, sytz: false, sywj: false}
    //统计数据哪些列是有数据的
    arr.forEach((item) => {
        if(item.basicType && item.basicType.length > 0 && !checkDataInfo.basicType){
            checkDataInfo.basicType = true
        }
        checkValues.forEach((checkValue) => {
            if(item[checkValue] && !checkDataInfo[checkValue]){
                checkDataInfo[checkValue] = true
            }
        })
    })

    // 创建主表头表格
    let mainHeaderTable2 = new MxDrawTable();
    mainHeaderTable2.data.setRowTitleHeight(6)
    mainHeaderTable2.data.setRowTextHeight(2.5)
    let rowDatas = new Array(arr.length).fill().map(() => [])
    if(checkDataInfo.moduleName){
        mainHeaderTable2.data.addColumn(0, "基础组件名称", 30, 2.5);
        mainHeaderTable2.data.addColumn(1, "", 30, 2.5);
        arr.forEach((item, index) => {
            rowDatas[index].push(item.moduleName || "")
        })
    }
    if(checkDataInfo.basicType){
        mainHeaderTable2.data.addColumn(0, "基础类型", 30, 2.5);
        mainHeaderTable2.data.addColumn(1, "", 30, 2.5);
        arr.forEach((item, index) => {
            rowDatas[index].push((item.basicType || []).join('\\P'))
        })
    }
    if(checkDataInfo.sygj){
        mainHeaderTable2.data.addColumn(0, "适用杆径", 15, 2.5);
        mainHeaderTable2.data.addColumn(1, "", 15, 2.5);
        arr.forEach((item, index) => {
            rowDatas[index].push(item.sygj || "")
        })
    }
    if(checkDataInfo.dsdm){
        mainHeaderTable2.data.addColumn(0, "典设代码", 15, 2.5);
        mainHeaderTable2.data.addColumn(1, "", 15, 2.5);
        arr.forEach((item, index) => {
            rowDatas[index].push(item.dsdm || "")
        })
    }
    if(checkDataInfo.dshmdj || checkDataInfo.dshmtj){
        mainHeaderTable2.data.addColumn(0, "地栓护帽", 15, 2.5);
        mainHeaderTable2.data.addColumn(1, "等级", 15, 2.5);
        mainHeaderTable2.data.addColumn(0, "地栓护帽", 15, 2.5);
        mainHeaderTable2.data.addColumn(1, "体积(m^3)", 15, 2.5);
        arr.forEach((item, index) => {
            rowDatas[index].push(item.dshmdj || "")
            rowDatas[index].push(item.dshmtj || "")
        })
    }
    if(checkDataInfo.dcdj || checkDataInfo.dctj){
        mainHeaderTable2.data.addColumn(0, "垫层", 15, 2.5);
        mainHeaderTable2.data.addColumn(1, "等级", 15, 2.5);
        mainHeaderTable2.data.addColumn(0, "垫层", 15, 2.5);
        mainHeaderTable2.data.addColumn(1, "体积(m^3)", 15, 2.5);
        arr.forEach((item, index) => {
            rowDatas[index].push(item.dcdj || "")
            rowDatas[index].push(item.dctj || "")
        })
    }
    if(checkDataInfo.hntjcdj || checkDataInfo.hntjctj){
        mainHeaderTable2.data.addColumn(0, "混凝土（基础）", 15, 2.5);
        mainHeaderTable2.data.addColumn(1, "等级", 15, 2.5);
        mainHeaderTable2.data.addColumn(0, "混凝土（基础）", 15, 2.5);
        mainHeaderTable2.data.addColumn(1, "体积(m^3)", 15, 2.5);
        arr.forEach((item, index) => {
            rowDatas[index].push(item.hntjcdj || "")
            rowDatas[index].push(item.hntjctj || "")
        })
    }
    if(checkDataInfo.hntzjdj || checkDataInfo.hntzjtj){
        mainHeaderTable2.data.addColumn(0, "混凝土（桩基）", 22, 2.5);
        mainHeaderTable2.data.addColumn(1, "等级", 22, 2.5);
        mainHeaderTable2.data.addColumn(0, "混凝土（桩基）", 22, 2.5);
        mainHeaderTable2.data.addColumn(1, "体积(m^3)", 22, 2.5);
        arr.forEach((item, index) => {
            rowDatas[index].push(item.hntzjdj || "")
            rowDatas[index].push(item.hntzjtj || "")
        })
    }
    if(checkDataInfo.q235bggz || checkDataInfo.q235byzm || checkDataInfo.hpb235 || checkDataInfo.hrb335 || checkDataInfo.hpb300 || checkDataInfo.hpb400 ||
        checkDataInfo.gjzl1 || checkDataInfo.gjzl2 || checkDataInfo.gjzl3 || checkDataInfo.sum){
        mainHeaderTable2.data.addColumn(0, "钢筋重量", 15, 2.5);
        mainHeaderTable2.data.addColumn(1, "Q235B（钢管桩）", 15, 2.5);
        mainHeaderTable2.data.addColumn(0, "钢筋重量", 15, 2.5);
        mainHeaderTable2.data.addColumn(1, "Q235B（压桩帽）", 15, 2.5);
        mainHeaderTable2.data.addColumn(0, "钢筋重量", 15, 2.5);
        mainHeaderTable2.data.addColumn(1, "HPB235", 15, 2.5);
        mainHeaderTable2.data.addColumn(0, "钢筋重量", 15, 2.5);
        mainHeaderTable2.data.addColumn(1, "HRB335", 15, 2.5);
        mainHeaderTable2.data.addColumn(0, "钢筋重量", 15, 2.5);
        mainHeaderTable2.data.addColumn(1, "HPB300", 15, 2.5);
        mainHeaderTable2.data.addColumn(0, "钢筋重量", 15, 2.5);
        mainHeaderTable2.data.addColumn(1, "HRB400", 15, 2.5);
        mainHeaderTable2.data.addColumn(0, "钢筋重量", 15, 2.5);
        mainHeaderTable2.data.addColumn(1, "I级", 15, 2.5);
        mainHeaderTable2.data.addColumn(0, "钢筋重量", 15, 2.5);
        mainHeaderTable2.data.addColumn(1, "II级", 15, 2.5);
        mainHeaderTable2.data.addColumn(0, "钢筋重量", 15, 2.5);
        mainHeaderTable2.data.addColumn(1, "III级", 15, 2.5);
        mainHeaderTable2.data.addColumn(0, "钢筋重量", 15, 2.5);
        mainHeaderTable2.data.addColumn(1, "合计", 15, 2.5);
        arr.forEach((item, index) => {
            rowDatas[index].push(item.q235bggz || "")
            rowDatas[index].push(item.q235byzm || "")
            rowDatas[index].push(item.hpb235 || "")
            rowDatas[index].push(item.hrb335 || "")
            rowDatas[index].push(item.hpb300 || "")
            rowDatas[index].push(item.hpb400 || "")
            rowDatas[index].push(item.gjzl1 || "")
            rowDatas[index].push(item.gjzl2 || "")
            rowDatas[index].push(item.gjzl3 || "")
            rowDatas[index].push(item.sum || "")
        })
    }
    if(checkDataInfo.sytz){
        mainHeaderTable2.data.addColumn(0, "适用土质", 15, 2.5);
        mainHeaderTable2.data.addColumn(1, "", 15, 2.5);
        arr.forEach((item, index) => {
            rowDatas[index].push(item.sytz || "")
        })
    }
    if(checkDataInfo.sywj){
        mainHeaderTable2.data.addColumn(0, "适用弯矩（KN·m）", 15, 2.5);
        mainHeaderTable2.data.addColumn(1, "", 15, 2.5);
        arr.forEach((item, index) => {
            rowDatas[index].push(item.sywj || "")
        })
    }
    rowDatas.forEach((item)=>{
        mainHeaderTable2.data.addRow(item, 13)
    })
    let lScale2 = 1//MxFun.screenCoordLong2Doc(100) / 200;
    // 绘制子表头和数据表格
    mainHeaderTable2.draw(new McGePoint3d(45 + offsetX, 92.6 , 0), lScale2);
    //#endregion

    // 刷新显示
    mxcad.regen();
}
//电缆通道土建表
export async function cabcleChannel (value) {
    console.log("🚀 ~ cabcleChannel ~ value:", value)
    
    let leftHeight = 0 // 左边框宽
    let rightHeight = 0 // 右边框宽
    let bottomWidth = 0 // 下边框宽
    let borderWidth = 0 // 外框宽
    let TqBorderWidth = 0 // 图签宽
    // 处理偏移量
    const offsetX = value.offsetX || 0; // 默认值为 0

    let blkFilePath = import.meta.url.substring(0, import.meta.url.lastIndexOf('/')) + `/assets/TuK/A4-210x297 标准改1.dwg.mxweb`;
    // 插入图块文件
    let mxcad = MxCpp.App.getCurrentMxCAD();
    mxcad.newFile();
    let blkrecId = await mxcad.insertBlock(blkFilePath, 'A4-210x297 标准改1.dwg');
    if (!blkrecId.isValid()) {
        return
    }

    let blkRef = new McDbBlockReference();
    blkRef.blockTableRecordId = blkrecId;
    blkRef.position = new McGePoint3d(offsetX,0,0);
    mxcad.drawEntity(blkRef);
    let blkRecord: any = blkrecId.getMcDbBlockTableRecord();
    let ids = blkRecord.getAllEntityId();
    ids.forEach((id: any, index: any) => {
        if (!id.isKindOf("McDbAttributeDefinition")) return;
        let attribDef = id.getMcDbEntity() as McDbAttributeDefinition;
        if (attribDef.tag === '左边框宽') {
            leftHeight = Number(attribDef.textString)
        } else if (attribDef.tag === '右边框宽') {
            rightHeight = Number(attribDef.textString)
        } else if (attribDef.tag === '外框宽') {
            borderWidth = Number(attribDef.textString)
        } else if (attribDef.tag === '下边框高') {
            bottomWidth = Number(attribDef.textString)
        }
    })
    let blkFilePathT = import.meta.url.substring(0, import.meta.url.lastIndexOf('/')) + `/assets/TuK/标准图签改.dwg.mxweb`;
    let blkrecIdT = await mxcad.insertBlock(blkFilePathT, '标准图签改.dwg');
    if (!blkrecIdT.isValid()) {
        return
    }
    let blkRefT = new McDbBlockReference();
    blkRefT.blockTableRecordId = blkrecIdT;
    let blkRecordT: any = blkrecIdT.getMcDbBlockTableRecord();
    let idsT = blkRecordT.getAllEntityId();
    idsT.forEach((idT: any, index: any) => {
        if (!idT.isKindOf("McDbAttributeDefinition")) return;
        let attribDef = idT.getMcDbEntity() as McDbAttributeDefinition;
        if (attribDef.tag === '图签宽') {
            TqBorderWidth = Number(attribDef.textString)
        }
    })
    blkRefT.position = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + offsetX,bottomWidth,0);
    mxcad.drawEntity(blkRefT);

    // 创建文本实体
    const text = new McDbText();
    text.textString = '电缆通道土建表'; // 使用传入的文本参数
    text.position = new McGePoint3d(115 + offsetX,275,0);
    text.alignmentPoint = new McGePoint3d(115 + offsetX,275,0);
    text.height = 7; // 文本高度
    text.widthFactor = 1; // 文本宽度缩放
    text.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    text.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    text.trueColor = new McCmColor(0, 0, 0); // 文本颜色（紫色）
    // 绘制文本
    mxcad.drawEntity(text);

    // 供电公司
    const textG = new McDbText();
    const positionG = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 10 + offsetX,bottomWidth + 16,0)
    textG.textString = value.countyOrganisationName ? value.countyOrganisationName : ''; // 使用传入的文本参数
    textG.position = positionG;
    textG.alignmentPoint = positionG;
    textG.height = 2; // 文本高度
    textG.widthFactor = 1; // 文本宽度缩放
    textG.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textG.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textG.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    mxcad.drawEntity(textG);

    // 项目名称
    const textPrject = new McDbText();
    const positionPrject = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 55 + offsetX,bottomWidth + 16,0)
    textPrject.textString = value.projectName ? value.projectName : ''; // 使用传入的文本参数
    textPrject.position = positionPrject;
    textPrject.alignmentPoint = positionPrject;
    textPrject.height = 2; // 文本高度
    textPrject.widthFactor = 1; // 文本宽度缩放
    textPrject.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textPrject.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textPrject.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    mxcad.drawEntity(textPrject);

    // 阶段
    const textJd = new McDbText();
    const positionJd = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 80 + offsetX,bottomWidth + 16,0)
    const textString = value.stage && value.stage === '1' ? '需求' : value.stage && value.stage === '2' ? '可研' : value.stage && value.stage === '3' ? '初设' : value.stage && value.stage === '4' ? '施工' : value.stage && value.stage === '5' ? '施工变更' : value.stage && value.stage === '5' ? '竣工' : ''
    textJd.textString = textString; // 使用传入的文本参数
    textJd.position = positionJd;
    textJd.alignmentPoint = positionJd;
    textJd.height = 2; // 文本高度
    textJd.widthFactor = 1; // 文本宽度缩放
    textJd.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textJd.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textJd.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    mxcad.drawEntity(textJd);

    // 时间
    const textTime = new McDbText();
    const positionTime = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 40 + offsetX,bottomWidth + 1.5,0)
    const textStringTime = min_getTime()
    textTime.textString = textStringTime; // 使用传入的文本参数
    textTime.position = positionTime;
    textTime.alignmentPoint = positionTime;
    textTime.height = 1; // 文本高度
    textTime.widthFactor = 1; // 文本宽度缩放
    textTime.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textTime.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textTime.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    mxcad.drawEntity(textTime);

    // 图号：项目编号+n
    const textCode = new McDbText();
    const positionCode = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 60 + offsetX,bottomWidth + 1.5,0)
    const textStringCode = value.proCode ? value.proCode + '-' + value.num : ''; // 使用传入的文本参数
    textCode.textString = textStringCode; // 使用传入的文本参数
    textCode.position = positionCode;
    textCode.alignmentPoint = positionCode;
    textCode.height = 1; // 文本高度
    textCode.widthFactor = 1; // 文本宽度缩放
    textCode.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textCode.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textCode.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    mxcad.drawEntity(textCode);

    // 图框名称：主要设备清单+当前页1,2,3
    const textK = new McDbText();
    const positionK = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 55 + offsetX,bottomWidth + 12,0)
    const textStringK = '电缆通道土建表-' + value.num; // 使用传入的文本参数
    textK.textString = textStringK; // 使用传入的文本参数
    textK.position = positionK;
    textK.alignmentPoint = positionK;
    textK.height = 2; // 文本高度
    textK.widthFactor = 1; // 文本宽度缩放
    textK.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textK.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textK.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    mxcad.drawEntity(textK);

// 创建主表头表格
let mainHeaderTable = new MxDrawTable();
mainHeaderTable.data.setRowTextHeight(1)
mainHeaderTable.data.addColumn(0, "序号", 5, 1.5);
mainHeaderTable.data.addColumn(0, "类型", 50, 1.5);
mainHeaderTable.data.addColumn(0, "规格", 15, 1.5);
mainHeaderTable.data.addColumn(0, "模块", 20, 1.5);
mainHeaderTable.data.addColumn(0, "单位", 12, 1.5);
mainHeaderTable.data.addColumn(0, "数量", 12, 1.5);
mainHeaderTable.data.addColumn(0, "状态", 12, 1.5);
// 添加数据
const arr = value.tableData ? JSON.parse(value.tableData) : [];
for (let i = 0; i < arr.length; i++) {
    let sT = "" + (i + 1);
    mainHeaderTable.data.addRow([
        sT, // 序号
        arr[i].moduleName, // 类型
        arr[i].moduleType, // 规格
        arr[i].moduleCode, // 模块
        arr[i].unit, // 单位
        arr[i].quantity, // 数量
        arr[i].state, // 状态
    ]);
}

// 设置表格位置
let lScale = 1//MxFun.screenCoordLong2Doc(100) / 200;

// 绘制子表头和数据表格
mainHeaderTable.draw(new McGePoint3d(30 + offsetX, 270 , 0), lScale);

// 刷新显示
mxcad.regen();

    // 返回一个 Promise
    return new Promise((resolve, reject) => {
        mxcad.saveFile(void 0, (data) => {
            let blob: Blob;
            const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
            if (isSafari) {
                blob = new Blob([data.buffer], { type: "application/octet-stream" });
            } else {
                blob = new Blob([data.buffer], { type: "application/octet-binary" });
            }
            const file = new File([blob], 'test' + value.num + '.mxweb', { type: isSafari ? "application/octet-stream" : "application/octet-binary" });
            resolve(file); // 返回文件对象
        }, false, true);
    });
}
// 电缆明细表生成dwg
export async function cableExcelFun (value, startIndex) {
    console.log("🚀 ~ cableExcelFun ~ value:", value)
    let mxcad = MxCpp.App.getCurrentMxCAD();
    let blkTable = mxcad.getDatabase().getBlockTable();
    
    let leftHeight = 0 // 左边框宽
    let rightHeight = 0 // 右边框宽
    let bottomWidth = 0 // 下边框宽
    let borderWidth = 0 // 外框宽
    let TqBorderWidth = 0 // 图签宽
    // 处理偏移量
    const offsetX = value.offsetX || 0; // 默认值为 0

    let blkFilePath = import.meta.url.substring(0, import.meta.url.lastIndexOf('/')) + `/assets/TuK/A3-420x297  标准样图.dwg.mxweb`;
    // 插入图块文件
    let blkrecId = await mxcad.insertBlock(blkFilePath, 'A3-420x297 标准改1.dwg');
    if (!blkrecId.isValid()) {
        return
    }

    let blkRef = new McDbBlockReference();
    blkRef.blockTableRecordId = blkrecId;
    blkRef.position = new McGePoint3d(offsetX,0,0);
    mxcad.drawEntity(blkRef);
    let blkRecord: any = blkrecId.getMcDbBlockTableRecord();
    let ids = blkRecord.getAllEntityId();
    ids.forEach((id: any, index: any) => {
        if (!id.isKindOf("McDbAttributeDefinition")) return;
        let attribDef = id.getMcDbEntity() as McDbAttributeDefinition;
        if (attribDef.tag === '左边框宽') {
            leftHeight = Number(attribDef.textString)
        } else if (attribDef.tag === '右边框宽') {
            rightHeight = Number(attribDef.textString)
        } else if (attribDef.tag === '外框宽') {
            borderWidth = Number(attribDef.textString)
        } else if (attribDef.tag === '下边框高') {
            bottomWidth = Number(attribDef.textString)
        }
    })
    let blkFilePathT = import.meta.url.substring(0, import.meta.url.lastIndexOf('/')) + `/assets/TuK/标准图签改.dwg.mxweb`;
    let blkrecIdT = await mxcad.insertBlock(blkFilePathT, '标准图签改.dwg');
    if (!blkrecIdT.isValid()) {
        return
    }
    let blkRefT = new McDbBlockReference();
    blkRefT.blockTableRecordId = blkrecIdT;
    let blkRecordT: any = blkrecIdT.getMcDbBlockTableRecord();
    let idsT = blkRecordT.getAllEntityId();
    idsT.forEach((idT: any, index: any) => {
        if (!idT.isKindOf("McDbAttributeDefinition")) return;
        let attribDef = idT.getMcDbEntity() as McDbAttributeDefinition;
        if (attribDef.tag === '图签宽') {
            TqBorderWidth = Number(attribDef.textString)
        }
    })
    blkRefT.position = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + offsetX,bottomWidth,0);
    mxcad.drawEntity(blkRefT);

    // 创建文本实体
    const text = new McDbText();
    text.textString = '电缆明细表'; // 使用传入的文本参数
    text.position = new McGePoint3d(145 + offsetX,275,0);
    text.alignmentPoint = new McGePoint3d(145 + offsetX,275,0);
    text.height = 7; // 文本高度
    text.widthFactor = 1; // 文本宽度缩放
    text.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    text.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    text.trueColor = new McCmColor(0, 0, 0); // 文本颜色（紫色）
    // 绘制文本
    mxcad.drawEntity(text);

    // 供电公司
    const textG = new McDbText();
    const positionG = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 10 + offsetX,bottomWidth + 16,0)
    textG.textString = value.countyOrganisationName ? value.countyOrganisationName : ''; // 使用传入的文本参数
    textG.position = positionG;
    textG.alignmentPoint = positionG;
    textG.height = 2; // 文本高度
    textG.widthFactor = 1; // 文本宽度缩放
    textG.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textG.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textG.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    mxcad.drawEntity(textG);

    // 项目名称
    const textPrject = new McDbText();
    const positionPrject = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 55 + offsetX,bottomWidth + 16,0)
    textPrject.textString = value.projectName ? value.projectName : ''; // 使用传入的文本参数
    textPrject.position = positionPrject;
    textPrject.alignmentPoint = positionPrject;
    textPrject.height = 2; // 文本高度
    textPrject.widthFactor = 1; // 文本宽度缩放
    textPrject.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textPrject.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textPrject.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    mxcad.drawEntity(textPrject);

    // 阶段
    const textJd = new McDbText();
    const positionJd = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 80 + offsetX,bottomWidth + 16,0)
    const textString = value.stage && value.stage === '1' ? '需求' : value.stage && value.stage === '2' ? '可研' : value.stage && value.stage === '3' ? '初设' : value.stage && value.stage === '4' ? '施工' : value.stage && value.stage === '5' ? '施工变更' : value.stage && value.stage === '5' ? '竣工' : ''
    textJd.textString = textString; // 使用传入的文本参数
    textJd.position = positionJd;
    textJd.alignmentPoint = positionJd;
    textJd.height = 2; // 文本高度
    textJd.widthFactor = 1; // 文本宽度缩放
    textJd.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textJd.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textJd.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    mxcad.drawEntity(textJd);

    // 时间
    const textTime = new McDbText();
    const positionTime = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 40 + offsetX,bottomWidth + 1.5,0)
    const textStringTime = min_getTime()
    textTime.textString = textStringTime; // 使用传入的文本参数
    textTime.position = positionTime;
    textTime.alignmentPoint = positionTime;
    textTime.height = 1; // 文本高度
    textTime.widthFactor = 1; // 文本宽度缩放
    textTime.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textTime.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textTime.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    mxcad.drawEntity(textTime);

    // 图号：项目编号+n
    const textCode = new McDbText();
    const positionCode = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 60 + offsetX,bottomWidth + 1.5,0)
    const textStringCode = value.proCode ? value.proCode + '-' + value.num : ''; // 使用传入的文本参数
    textCode.textString = textStringCode; // 使用传入的文本参数
    textCode.position = positionCode;
    textCode.alignmentPoint = positionCode;
    textCode.height = 1; // 文本高度
    textCode.widthFactor = 1; // 文本宽度缩放
    textCode.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textCode.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textCode.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    mxcad.drawEntity(textCode);

    // 图框名称：主要设备清单+当前页1,2,3
    const textK = new McDbText();
    const positionK = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 55 + offsetX,bottomWidth + 12,0)
    const textStringK = '电缆明细表-' + value.num; // 使用传入的文本参数
    textK.textString = textStringK; // 使用传入的文本参数
    textK.position = positionK;
    textK.alignmentPoint = positionK;
    textK.height = 2; // 文本高度
    textK.widthFactor = 1; // 文本宽度缩放
    textK.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textK.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textK.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    mxcad.drawEntity(textK);

    // 创建主表头表格
    let mainHeaderTable = new MxDrawTable();
    mainHeaderTable.data.setRowTextHeight(1)
    mainHeaderTable.data.addColumn(0, "序号", 5, 1.5);
    mainHeaderTable.data.addColumn(0, "线路名称", 25, 1.5);
    mainHeaderTable.data.addColumn(0, "电缆型号", 25, 1.5);
    mainHeaderTable.data.addColumn(0, "起止设备", 10, 1.5);
    mainHeaderTable.data.addColumn(0, "起止设备", 20, 1.5);
    mainHeaderTable.data.addColumn(0, "电缆长度（米）", 15, 1.5);
    mainHeaderTable.data.addColumn(0, "电缆长度（米）", 15, 1.5);
    mainHeaderTable.data.addColumn(0, "电缆长度（米）", 15, 1.5);
    mainHeaderTable.data.addColumn(0, "电缆终端（套）", 15, 1.5);
    mainHeaderTable.data.addColumn(0, "电缆终端（套）", 15, 1.5);
    mainHeaderTable.data.addColumn(0, "电缆终端（套）", 15, 1.5);
    mainHeaderTable.data.addColumn(0, "电缆终端（套）", 15, 1.5);
    mainHeaderTable.data.addColumn(0, "电缆设备（台）", 15, 1.5);
    mainHeaderTable.data.addColumn(0, "电缆设备（台）", 15, 1.5);

    mainHeaderTable.data.addColumn(1, "", 5, 1.5);
    mainHeaderTable.data.addColumn(1, "", 25, 1.5);
    mainHeaderTable.data.addColumn(1, "", 25, 1.5);
    mainHeaderTable.data.addColumn(1, "起始设备名称", 10, 1.5); 
    mainHeaderTable.data.addColumn(1, "终止设备名称", 20, 1.5); 
    mainHeaderTable.data.addColumn(1, "新建长度", 15, 1.5);
    mainHeaderTable.data.addColumn(1, "利旧长度", 15, 1.5);
    mainHeaderTable.data.addColumn(1, "拆旧长度", 15, 1.5);
    mainHeaderTable.data.addColumn(1, "户外终端", 15, 1.5);
    mainHeaderTable.data.addColumn(1, "户内终端", 15, 1.5);
    mainHeaderTable.data.addColumn(1, "设备终端", 15, 1.5);
    mainHeaderTable.data.addColumn(1, "中间接头", 15, 1.5);
    mainHeaderTable.data.addColumn(1, "电缆对接箱", 15, 1.5);
    mainHeaderTable.data.addColumn(1, "电缆分支箱", 15, 1.5);
    // 添加数据
    const arr = value.tableData ? JSON.parse(value.tableData) : [];
    for (let i = 0; i < arr.length; i++) {
        mainHeaderTable.data.addRow([
            arr[i].number, 
            arr[i].lineName, 
            arr[i].cableType,
            arr[i].startUserNumber,
            arr[i].endUserNumber,
            arr[i].newLength, 
            arr[i].oldLength, 
            arr[i].removeLength, 
            arr[i].outdoorTerminalNum, 
            arr[i].indoorTerminalNum, 
            arr[i].equipmentTerminalNum,
            arr[i].midJointNum,
            arr[i].cableDockingNum,
            arr[i].cableBranchNum
        ]);
    }

    // 设置表格位置
    let lScale = 1//MxFun.screenCoordLong2Doc(100) / 200;
    // 绘制子表头和数据表格
    mainHeaderTable.draw(new McGePoint3d(30 + offsetX, 270 , 0), lScale);
    // 刷新显示
    mxcad.regen();
}
// 杆塔明细表生成dwg
export async function gtmxExcelFun (value, startIndex) {
    let leftHeight = 0 // 左边框宽
    let rightHeight = 0 // 右边框宽
    let bottomWidth = 0 // 下边框宽
    let borderWidth = 0 // 外框宽
    let TqBorderWidth = 0 // 图签宽
    // 处理偏移量
    const offsetX = value.offsetX || 0; // 默认值为 0

    let blkFilePath = import.meta.url.substring(0, import.meta.url.lastIndexOf('/')) + `/assets/TuK/A2-594x420  标准.dwg.mxweb`;
    // 插入图块文件
    let mxcad = MxCpp.App.getCurrentMxCAD();
    let blkrecId = await mxcad.insertBlock(blkFilePath, 'A2-594x420  标准.dwg');
    if (!blkrecId.isValid()) {
        return
    }

    let blkRef = new McDbBlockReference();
    blkRef.blockTableRecordId = blkrecId;
    blkRef.position = new McGePoint3d(offsetX,0,0);
    mxcad.drawEntity(blkRef);
    let blkRecord: any = blkrecId.getMcDbBlockTableRecord();
    let ids = blkRecord.getAllEntityId();
    ids.forEach((id: any, index: any) => {
        if (!id.isKindOf("McDbAttributeDefinition")) return;
        let attribDef = id.getMcDbEntity() as McDbAttributeDefinition;
        if (attribDef.tag === '左边框宽') {
            leftHeight = Number(attribDef.textString)
        } else if (attribDef.tag === '右边框宽') {
            rightHeight = Number(attribDef.textString)
        } else if (attribDef.tag === '外框宽') {
            borderWidth = Number(attribDef.textString)
        } else if (attribDef.tag === '下边框高') {
            bottomWidth = Number(attribDef.textString)
        }
    })
    let blkFilePathT = import.meta.url.substring(0, import.meta.url.lastIndexOf('/')) + `/assets/TuK/标准图签改.dwg.mxweb`;
    let blkrecIdT = await mxcad.insertBlock(blkFilePathT, '标准图签改.dwg');
    if (!blkrecIdT.isValid()) {
        return
    }
    let blkRefT = new McDbBlockReference();
    blkRefT.blockTableRecordId = blkrecIdT;
    let blkRecordT: any = blkrecIdT.getMcDbBlockTableRecord();
    let idsT = blkRecordT.getAllEntityId();
    idsT.forEach((idT: any, index: any) => {
        if (!idT.isKindOf("McDbAttributeDefinition")) return;
        let attribDef = idT.getMcDbEntity() as McDbAttributeDefinition;
        if (attribDef.tag === '图签宽') {
            TqBorderWidth = Number(attribDef.textString)
        }
    })
    const tqX = borderWidth - TqBorderWidth - rightHeight
    blkRefT.position = new McGePoint3d(tqX + offsetX,bottomWidth,0);
    mxcad.drawEntity(blkRefT);
    setSignContent(mxcad, value, offsetX, tqX, bottomWidth, "杆塔明细表", 
        (borderWidth-leftHeight-rightHeight)/2 + leftHeight,
        blkRef.getBoundingBox().maxPt.y - bottomWidth)

    let lScale = 1 //MxFun.screenCoordLong2Doc(100) / 200;
    let draw = new MxDrawTable();
    setGTMXExcelTitle(draw)
    console.log(value)
    const arr = value.tableData ? JSON.parse(value.tableData) : []
    for (let i = 0; i < arr.length; i++) {
        draw.data.addRow([
            arr[i].towerNo, //杆塔编号
            arr[i].towerType, //杆塔型号(杆塔描述)
            arr[i].towerHead, //杆头型号(杆塔描述)
            arr[i].towerSegment, //杆段情况(杆塔描述)
            arr[i].towerMaterial, //杆塔材质(杆塔描述)
            arr[i].towerStatus, //杆塔状态(杆塔描述)
            arr[i].span, //档距(档距描述(m))
            arr[i].strainSection, //耐张段(档距描述(m))
            arr[i].rulingSpan, //代表档距(档距描述(m))
            arr[i].wireModel, //导线型号(导线描述)
            arr[i].wireStatus, //导线状态(导线描述)
            arr[i].direction, //方向(水平转角)
            arr[i].angle, //度数(水平转角)
            arr[i].chassisModel, //型号(杆塔基础/底盘)
            arr[i].chassisCount, //块数(杆塔基础/底盘)
            arr[i].chuckModel, //型号(杆塔基础/卡盘)
            arr[i].chuckCount, //块数(杆塔基础/卡盘)
            arr[i].pullPlateModel, //型号(杆塔基础/拉盘)
            arr[i].pullPlateCount, //块数(杆塔基础/拉盘)
            arr[i].terrain, //地形(杆塔基础)
            arr[i].terrainStatus, //地质情况(杆塔基础)
            arr[i].towerPoleModel, //型号(杆塔配电设备/附件)
            arr[i].towerPoleStatus, //状态(杆塔配电设备/附件)
            arr[i].towerPoleCount, //套数(杆塔配电设备/附件)
            arr[i].groundingDeviceModel, //型号(接地装置)
            arr[i].groundingDeviceCount, //套数(接地装置)
            arr[i].tensionInsulatorModel, //型号(耐张绝缘子串)
            arr[i].tensionInsulatorCount, //串数(耐张绝缘子串)
            arr[i].straightInsulatorModel, //型号(直线绝缘子)
            arr[i].straightInsulatorCount, //个数(直线绝缘子)
            arr[i].continuousHardwareModel, //型号(接续金具)
            arr[i].continuousHardwareCount, //个数(接续金具)
            arr[i].shockproofHammerModel, //型号(防振锤)
            arr[i].ShockproofHammerCount, //个数(防振锤)
            arr[i].crossArmModel, //型号(横担)
            arr[i].crossArmCount, //套数(横担)
            arr[i].pullWireModel, //型号(拉线装置)
            arr[i].pullWireCount, //组数(拉线装置)
            arr[i].highWayCount, //公路个数(交叉跨越)
            arr[i].communicationLineCount, //通信线个数(交叉跨越)
            arr[i].powerLineCount, //电力线个数(交叉跨越)
            arr[i].homeCount, //房屋个数(交叉跨越)
            arr[i].economicGoodsCount, //经济物个数(交叉跨越)
            arr[i].riverCount, //河流个数(交叉跨越)
            arr[i].cultivatedLandCount, //耕地个数(交叉跨越)
            arr[i].pondCount, //水塘个数(交叉跨越)
            arr[i].railwayCount, //铁路个数(交叉跨越)
            arr[i].notes //备注
        ], 3)
    }
    draw.draw(new McGePoint3d(30 + offsetX, blkRef.getBoundingBox().maxPt.y - bottomWidth - 35,0), lScale);
    // 刷新显示
    mxcad.regen();
}

//批量画圆
export async function cabcleCircle(value) { 
    // 图纸尺寸
    const PAPER_WIDTH = 420;
    const PAPER_HEIGHT = 297;
    let leftHeight = 0 // 左边框宽
    let rightHeight = 0 // 右边框宽
    let bottomWidth = 0 // 下边框宽
    let borderWidth = 0 // 外框宽
    let TqBorderWidth = 0 // 图签宽
    // 处理偏移量
    // 图纸尺寸
    const offsetX = 0; // 默认值为 0 图纸是420x297
    let blkFilePath = import.meta.url.substring(0, import.meta.url.lastIndexOf('/')) + `/assets/TuK/A3-420x297  标准样图.dwg.mxweb`;
    
    // 插入图块文件
    let mxcad = MxCpp.App.getCurrentMxCAD();
    mxcad.newFile();
    let blkrecId = await mxcad.insertBlock(blkFilePath, 'A4-210x297 标准改1.dwg');
    if (!blkrecId.isValid()) {
        return;
    }

    let blkRef = new McDbBlockReference();
    blkRef.blockTableRecordId = blkrecId;
    blkRef.position = new McGePoint3d(offsetX, 0, 0);
    // blkRef.position = new McGePoint3d(PAPER_WIDTH/2, PAPER_HEIGHT/2, 0);
    mxcad.drawEntity(blkRef);
   let blkRecord: any = blkrecId.getMcDbBlockTableRecord();
          let ids = blkRecord.getAllEntityId();
          ids.forEach((id: any, index: any) => {
              if (!id.isKindOf("McDbAttributeDefinition")) return;
              let attribDef = id.getMcDbEntity() as McDbAttributeDefinition;
              if (attribDef.tag === '左边框宽') {
                  leftHeight = Number(attribDef.textString)
              } else if (attribDef.tag === '右边框宽') {
                  rightHeight = Number(attribDef.textString)
              } else if (attribDef.tag === '外框宽') {
                  borderWidth = Number(attribDef.textString)
              } else if (attribDef.tag === '下边框高') {
                  bottomWidth = Number(attribDef.textString)
              }
          })
          let blkFilePathT = import.meta.url.substring(0, import.meta.url.lastIndexOf('/')) + `/assets/TuK/标准图签改.dwg.mxweb`;
          let blkrecIdT = await mxcad.insertBlock(blkFilePathT, '标准图签改.dwg');
          if (!blkrecIdT.isValid()) {
              return
          }
          let blkRefT = new McDbBlockReference();
          blkRefT.blockTableRecordId = blkrecIdT;
          let blkRecordT: any = blkrecIdT.getMcDbBlockTableRecord();
          let idsT = blkRecordT.getAllEntityId();
          idsT.forEach((idT: any, index: any) => {
              if (!idT.isKindOf("McDbAttributeDefinition")) return;
              let attribDef = idT.getMcDbEntity() as McDbAttributeDefinition;
              if (attribDef.tag === '图签宽') {
                  TqBorderWidth = Number(attribDef.textString)
              }
          })
          blkRefT.position = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + offsetX,bottomWidth,0);
          mxcad.drawEntity(blkRefT);
      
          // 创建文本实体
          const text = new McDbText();
          text.textString = '通道内敷设位置图'; // 使用传入的文本参数
          text.position = new McGePoint3d(210 + offsetX,275,0);
          text.alignmentPoint = new McGePoint3d(210 + offsetX,275,0);
          text.height = 7; // 文本高度
          text.widthFactor = 1; // 文本宽度缩放
          text.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
          text.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
          text.trueColor = new McCmColor(0, 0, 0); // 文本颜色（紫色）
          // 绘制文本
          mxcad.drawEntity(text);
      
          // 供电公司
          const textG = new McDbText();
          const positionG = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 10 + offsetX,bottomWidth + 16,0)
          textG.textString = value.countyOrganisationName ? value.countyOrganisationName : ''; // 使用传入的文本参数
          textG.position = positionG;
          textG.alignmentPoint = positionG;
          textG.height = 2; // 文本高度
          textG.widthFactor = 1; // 文本宽度缩放
          textG.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
          textG.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
          textG.trueColor = new McCmColor(0, 0, 0)
          // 绘制文本
          mxcad.drawEntity(textG);
      
          // 项目名称
          const textPrject = new McDbText();
          const positionPrject = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 55 + offsetX,bottomWidth + 16,0)
          textPrject.textString = value.projectName ? value.projectName : ''; // 使用传入的文本参数
          textPrject.position = positionPrject;
          textPrject.alignmentPoint = positionPrject;
          textPrject.height = 2; // 文本高度
          textPrject.widthFactor = 1; // 文本宽度缩放
          textPrject.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
          textPrject.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
          textPrject.trueColor = new McCmColor(0, 0, 0)
          // 绘制文本
          mxcad.drawEntity(textPrject);
      
          // 阶段
          const textJd = new McDbText();
          const positionJd = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 80 + offsetX,bottomWidth + 16,0)
          const textString = value.stage && value.stage === '1' ? '需求' : value.stage && value.stage === '2' ? '可研' : value.stage && value.stage === '3' ? '初设' : value.stage && value.stage === '4' ? '施工' : value.stage && value.stage === '5' ? '施工变更' : value.stage && value.stage === '5' ? '竣工' : ''
          textJd.textString = textString; // 使用传入的文本参数
          textJd.position = positionJd;
          textJd.alignmentPoint = positionJd;
          textJd.height = 2; // 文本高度
          textJd.widthFactor = 1; // 文本宽度缩放
          textJd.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
          textJd.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
          textJd.trueColor = new McCmColor(0, 0, 0)
          // 绘制文本
          mxcad.drawEntity(textJd);
      
          // 时间
          const textTime = new McDbText();
          const positionTime = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 40 + offsetX,bottomWidth + 1.5,0)
          const textStringTime = min_getTime()
          textTime.textString = textStringTime; // 使用传入的文本参数
          textTime.position = positionTime;
          textTime.alignmentPoint = positionTime;
          textTime.height = 1; // 文本高度
          textTime.widthFactor = 1; // 文本宽度缩放
          textTime.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
          textTime.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
          textTime.trueColor = new McCmColor(0, 0, 0)
          // 绘制文本
          mxcad.drawEntity(textTime);
      
          // 图号：项目编号+n
          const textCode = new McDbText();
          const positionCode = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 60 + offsetX,bottomWidth + 1.5,0)
          const textStringCode = value.proCode ? value.proCode + '-' + value.num : ''; // 使用传入的文本参数
          textCode.textString = textStringCode; // 使用传入的文本参数
          textCode.position = positionCode;
          textCode.alignmentPoint = positionCode;
          textCode.height = 1; // 文本高度
          textCode.widthFactor = 1; // 文本宽度缩放
          textCode.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
          textCode.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
          textCode.trueColor = new McCmColor(0, 0, 0)
          // 绘制文本
          mxcad.drawEntity(textCode);
      
          // 图框名称：主要设备清单+当前页1,2,3
          const textK = new McDbText();
          const positionK = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 55 + offsetX,bottomWidth + 12,0)
          const textStringK = '通道内敷设位置图-' + value.num; // 使用传入的文本参数
          textK.textString = textStringK; // 使用传入的文本参数
          textK.position = positionK;
          textK.alignmentPoint = positionK;
          textK.height = 2; // 文本高度
          textK.widthFactor = 1; // 文本宽度缩放
          textK.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
          textK.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
          textK.trueColor = new McCmColor(0, 0, 0)
          // 绘制文本
          mxcad.drawEntity(textK);
    // 定义圆的半径和间距
    const radius = 20; // 圆的半径
    const spacing = 5; // 圆与圆之间的间隔
    const rows = 4;    // 行数
    const cols = 4;    // 列数
 // 计算阵列总尺寸
 const totalWidth = cols * (2 * radius) + (cols - 1) * spacing;
 const totalHeight = rows * (2 * radius) + (rows - 1) * spacing;

 // 计算起始位置（以图纸中心为基准）
 const centerX = PAPER_WIDTH / 2;
 const centerY = PAPER_HEIGHT / 2;
 const startX = centerX - totalWidth / 2 + radius;
 const startY = centerY + totalHeight / 2 - radius;
    // 计算起始位置（使整个阵列居中）
    // const startX = -(cols * (radius * 2 + spacing) - spacing) / 2;
    // const startY = (rows * (radius * 2 + spacing) - spacing) / 2;
  // 计数器，用于标注数字 1 到 16
  let counter = 1;
//   const arr = value.tableData ? JSON.parse(value.tableData) : []
  // 绘制 4x4 的圆阵列，并在中心标注数字
  for (let i = 0; i < rows; i++) {
      for (let j = 0; j < cols; j++) {
          const x = startX + j * (radius * 2 + spacing);
          const y = startY - i * (radius * 2 + spacing);
          // 绘制圆
          mxcad.drawCircle(x, y, radius);
        const newCenterPt = new McGePoint3d(x, y, 0);
         await drawTextCopy(mxcad, newCenterPt, counter.toString(), 10);
          counter++; // 数字递增
      }
  }
     //绘制文字描述---几号孔已被占用
     const textCircle = new McDbMText();
     const positionCircle = new McGePoint3d(totalWidth/2,totalHeight+100,0)
     const textStringCircle = '电缆一已被1、2、3、4、5、6、7、8号孔已被占用;电缆二已被9、10、11、12、13、14、15、16号孔已被占用'; // 使用传入的文本参数
     textCircle.attachment = McDb.AttachmentPoint.kTopLeft
     textCircle.contents = addNewlineAfterEvery20Chars(textStringCircle)
     textCircle.location = positionCircle
     textCircle.trueColor = new McCmColor(0, 0, 0)
     textCircle.textHeight = 2
     mxcad.drawEntity(textCircle);
    mxcad.zoomAll();
    mxcad.regen();
    mxcad.updateDisplay();

    // 返回一个 Promise
    return new Promise((resolve, reject) => {
        mxcad.saveFile(void 0, (data) => {
            let blob: Blob;
            const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
            if (isSafari) {
                blob = new Blob([data.buffer], { type: "application/octet-stream" });
            } else {
                blob = new Blob([data.buffer], { type: "application/octet-binary" });
            }
            const file = new File([blob], 'test' + 123 + '.mxweb', { type: isSafari ? "application/octet-stream" : "application/octet-binary" });
            resolve(file); // 返回文件对象
        }, false, true);
    });
}
//封装文字显示
async function drawTextCopy(mxcad, newCenterPt, textString, textHeight) {
    const text = new McDbText();
    text.textString = textString;
    text.position = new McGePoint3d(newCenterPt.x, newCenterPt.y, newCenterPt.z);
    text.alignmentPoint = new McGePoint3d(newCenterPt.x, newCenterPt.y, newCenterPt.z);
    text.height = textHeight;
    text.widthFactor = 1;
    text.horizontalMode = McDb.TextHorzMode.kTextCenter;
    text.verticalMode = McDb.TextVertMode.kTextVertMid;
    text.trueColor = new McCmColor(0, 0, 0);
    mxcad.drawEntity(text);
}
//文字换行
function addNewlineAfterEvery20Chars(text) {
    let result = "";
    for (let i = 0; i < text.length; i += 20) {
        result += text.slice(i, i + 20) + "\\P";
    }
    return result;
}
function textLineSf(text) {
    console.log(text.length,'文本长度')
    let  result = 1;//文本宽度缩放等级
    let num =0; //文本位置
    if( text.length>=20&&text.length<=28){
        result = 0.6 
        num=1
    }else if(text.length>28){
        result = 0.6
        num = 0.7
    }
    return {result,num};
}
// 只有图框和图签进行预览
export async function blockInsertFrameFun (value, type) {
    let leftHeight = 0 // 左边框宽
    let rightHeight = 0 // 右边框宽
    let bottomWidth = 0 // 下边框宽
    let borderWidth = 0 // 外框宽
    let TqBorderWidth = 0 // 图签宽
    let mxcad = MxCpp.App.getCurrentMxCAD();
    if (type === '预览') {
        mxcad.newFile();
    }
    let blkTable =  mxcad.getDatabase().getBlockTable();
    let blkRecId = blkTable.add(new McDbBlockTableRecord());
    // 根据ObjectId再次得到刚刚添加的图块记录
    let blkTableRecord:McDbBlockTableRecord = blkRecId.getMcDbBlockTableRecord()
    // 处理偏移量
    const offsetX = 0; // 默认值为 0
    let blkFilePath = value.openUrlTk ? URL.createObjectURL(value.openUrlTk) : ''
    // 插入图块文件
    let blkrecId = await mxcad.insertBlock(blkFilePath, value.tfdx);
    if (!blkrecId.isValid()) {
        return
    }

    let blkRef = new McDbBlockReference();
    blkRef.blockTableRecordId = blkrecId;
    blkRef.position = new McGePoint3d(offsetX, 0, 0);
    // mxcad.drawEntity(blkRef);
    blkTableRecord.appendAcDbEntity(blkRef)
    let blkRecord: any = blkrecId.getMcDbBlockTableRecord();
    let ids = blkRecord.getAllEntityId();
    ids.forEach((id: any, index: any) => {
        if (!id.isKindOf("McDbAttributeDefinition")) return;
        let attribDef = id.getMcDbEntity() as McDbAttributeDefinition;
        console.log(attribDef, 'logattribDeflogattribDeflogattribDef')
        if (attribDef.tag === '左边框宽') {
            leftHeight = Number(attribDef.textString)
        } else if (attribDef.tag === '右边框宽') {
            rightHeight = Number(attribDef.textString)
        } else if (attribDef.tag === '外框宽') {
            borderWidth = Number(attribDef.textString)
        } else if (attribDef.tag === '下边框高') {
            bottomWidth = Number(attribDef.textString)
        }
    })
    let blkFilePathT =  value.openUrlTq ? URL.createObjectURL(value.openUrlTq) : ''
    let blkrecIdT = await mxcad.insertBlock(blkFilePathT, '图签.dwg');
    if (!blkrecIdT.isValid()) {
        return
    }
    // let blkRefT = new McDbBlockReference();
    // blkRefT.blockTableRecordId = blkrecIdT;
    // let blkRecordT: any = blkrecIdT.getMcDbBlockTableRecord();
    // let idsT = blkRecordT.getAllEntityId();

    let blkRefT = new McDbBlockReference();
    blkRefT.blockTableRecordId = blkrecIdT;
    let blkRecordT: any = blkrecIdT.getMcDbBlockTableRecord();
    const boundboxPoints1 = blkRecordT.getBoundingBox()
    TqBorderWidth=boundboxPoints1.maxPt.x-boundboxPoints1.minPt.x
    // idsT.forEach((idT: any, index: any) => {
    //     console.log(idT,'图签位置111图签位置111')
    //     if (!idT.isKindOf("McDbAttributeDefinition")) return;
    //     let attribDef = idT.getMcDbEntity() as McDbAttributeDefinition;
    //     if (attribDef.tag === '图签宽') {
    //         TqBorderWidth = Number(attribDef.textString)
    //     }
    // })
    console.log(TqBorderWidth,'图签位置111')
    blkRefT.position = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + offsetX,bottomWidth,0)
    // mxcad.drawEntity(blkRefT);
    blkTableRecord.appendAcDbEntity(blkRefT)
    // 供电公司
    const textG = new McDbText();
    const positionG = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 17 + offsetX,bottomWidth + 16,0)
    textG.textString = value.sjdw ? value.sjdw : ''; // 使用传入的文本参数
    textG.position = positionG;
    textG.alignmentPoint = positionG;
    textG.height = 2; // 文本高度
    textG.widthFactor = textLineSf(value.sjdw ? value.sjdw : '').result; // 文本宽度缩放
    textG.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textG.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textG.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    // mxcad.drawEntity(textG);
    blkTableRecord.appendAcDbEntity(textG)

    // 项目名称
    const textPrject = new McDbText();
    let num=0
    if(value.gcmc.length>=20){
        num=1
    }
    console.log(textLineSf(value.gcmc ? value.gcmc : '').result,'缩放等级')
    const positionPrject = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 59 + offsetX,bottomWidth + 16+textLineSf(value.gcmc ? value.gcmc : '').num,0)
    textPrject.textString = value.gcmc ? value.gcmc : ''; // 使用传入的文本参数
    textPrject.position = positionPrject;
    textPrject.alignmentPoint = positionPrject;
    textPrject.widthFactor = textLineSf(value.gcmc ? value.gcmc : '').result; // 文本宽度缩放
    textPrject.height = textPrject.widthFactor*2; // 文本高度
    textPrject.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textPrject.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textPrject.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    // mxcad.drawEntity(textPrject);
    blkTableRecord.appendAcDbEntity(textPrject)

    // 阶段
    const textJd = new McDbText();
    const positionJd = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 80 + offsetX,bottomWidth + 16,0)
    const textString = value.sjjd ? value.sjjd : ''; // 使用传入的文本参数
    textJd.textString = textString; // 使用传入的文本参数
    textJd.position = positionJd;
    textJd.alignmentPoint = positionJd;
    textJd.height = 2; // 文本高度
    textJd.widthFactor = 1; // 文本宽度缩放
    textJd.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textJd.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textJd.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    // mxcad.drawEntity(textJd);
    blkTableRecord.appendAcDbEntity(textJd)

    // 时间
    const textTime = new McDbText();
    const positionTime = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 40 + offsetX,bottomWidth + 1.5,0)
    const textStringTime = value.time ? value.time : '';
    textTime.textString = textStringTime; // 使用传入的文本参数
    textTime.position = positionTime;
    textTime.alignmentPoint = positionTime;
    textTime.height = 1; // 文本高度
    textTime.widthFactor = 1; // 文本宽度缩放
    textTime.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textTime.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textTime.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    // mxcad.drawEntity(textTime);
    blkTableRecord.appendAcDbEntity(textTime)

    // 比例
    const textBl = new McDbText();
    const positionBl = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 40 + offsetX,bottomWidth + 5.5,0)
    const textStringBl = value.tzbl ? value.tzbl : '';
    textBl.textString = textStringBl; // 使用传入的文本参数
    textBl.position = positionBl;
    textBl.alignmentPoint = positionBl;
    textBl.height = 1; // 文本高度
    textBl.widthFactor = 1; // 文本宽度缩放
    textBl.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textBl.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textBl.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    // mxcad.drawEntity(textBl);
    blkTableRecord.appendAcDbEntity(textBl)

    // 图号：项目编号+n
    const textCode = new McDbText();
    const positionCode = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 70 + offsetX,bottomWidth + 1.5,0)
    const textStringCode = value.th ? value.th : ''; // 使用传入的文本参数
    textCode.textString = textStringCode; // 使用传入的文本参数
    textCode.position = positionCode;
    textCode.alignmentPoint = positionCode;
    textCode.height = 1; // 文本高度
    textCode.widthFactor = 1; // 文本宽度缩放
    textCode.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textCode.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textCode.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    // mxcad.drawEntity(textCode);
    blkTableRecord.appendAcDbEntity(textCode)

    // 图框名称：主要设备清单+当前页1,2,3
    const textK = new McDbText();
    const positionK = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 55 + offsetX,bottomWidth + 12,0)
    const textStringK = value.tzlx ? value.tzlx : ''; // 使用传入的文本参数
    textK.textString = textStringK; // 使用传入的文本参数
    textK.position = positionK;
    textK.alignmentPoint = positionK;
    textK.height = 2; // 文本高度
    textK.widthFactor = 1; // 文本宽度缩放
    textK.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textK.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textK.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    // mxcad.drawEntity(textK);
    blkTableRecord.appendAcDbEntity(textK)

    // 实例化块参照 这里需要设置我们刚刚添加图块记录得到的ObjectId
    let blkRefObj = new McDbBlockReference();
    blkRefObj.blockTableRecordId = blkRecId;
    // 最后设置位置 渲染图块
    if (type === '插入') {
        let getPoint = new MxCADUiPrPoint();
        getPoint.setMessage("\指定插入基点");
        getPoint.setUserDraw((v, worldDraw) => {
            blkRef.position = v;
            worldDraw.drawMcDbEntity(blkRef);
        });
        let pt = await getPoint.go();
        if (!pt) return;
        blkRefObj.position = pt;
    }
    let newBlkRefId = mxcad.drawEntity(blkRefObj);
    mxcad.updateDisplay()
    mxcad.regen();
    // 获取该对象的实体
    const ent = newBlkRefId.getMcDbEntity();
    if(!ent) return { id: null, handle: null };
    // 获取对象ID
    const entId = ent.getObjectID();
    // 获取对象句柄
    const sHandle = ent.getHandle();
    console.log(sHandle, 'sHandlesHandle')
    return { handle: sHandle, show: true };
}

// 获取当前时间
function min_getTime() {
    const date = new Date()
    const y = date.getFullYear()
    let m = date.getMonth() + 1
    m = m < 10 ? '0' + m : m
    let d = date.getDate()
    d = d < 10 ? '0' + d : d
    return y + '-' + m + '-' + d
}

/** 设置杆塔明细表标题
 * @param draw 
 */
function setGTMXExcelTitle(draw: MxDrawTable) {
    draw.data.setRowTextHeight(1.5)
    draw.data.addColumn(0, "", 18, 1.5);
    draw.data.addColumn(1, "杆号", 18, 1.5);
    draw.data.addColumn(2, "", 18, 1.5);
    draw.data.addColumn(0,"", 10, 1.5);
    draw.data.addColumn(0,"", 10, 1.5);
    draw.data.addColumn(0,"", 23, 1.5);
    draw.data.addColumn(0,"", 9, 1.5);
    draw.data.addColumn(0,"", 9, 1.5);
    draw.data.addColumn(1,"杆塔描述", 10, 1.5);
    draw.data.addColumn(1,"杆塔描述", 10, 1.5);
    draw.data.addColumn(1,"杆塔描述", 23, 1.5);
    draw.data.addColumn(1,"杆塔描述", 9, 1.5);
    draw.data.addColumn(1,"杆塔描述", 9, 1.5);
    draw.data.addColumn(2,"杆型代号", 10, 1.5);
    draw.data.addColumn(2,"杆头代号", 10, 1.5);
    draw.data.addColumn(2,"杆段情况", 23, 1.5);
    draw.data.addColumn(2,"材质", 9, 1.5);
    draw.data.addColumn(2,"状态", 9, 1.5);

    draw.data.addColumn(0,"档距描述(m)", 7, 1.5);
    draw.data.addColumn(0,"档距描述(m)", 10, 1.5);
    draw.data.addColumn(0,"档距描述(m)", 15, 1.5);
    draw.data.addColumn(1,"", 7, 1.5);
    draw.data.addColumn(1,"", 10, 1.5);
    draw.data.addColumn(1,"", 15, 1.5);
    draw.data.addColumn(2,"档距(m)", 7, 1.5);
    draw.data.addColumn(2,"耐张档距(m)", 10, 1.5);
    draw.data.addColumn(2,"代表档距(m)", 15, 1.5);

    draw.data.addColumn(0,"", 20, 1.5);
    draw.data.addColumn(0,"", 9, 1.5);
    draw.data.addColumn(1,"导线描述", 20, 1.5);
    draw.data.addColumn(1,"导线描述", 9, 1.5);
    draw.data.addColumn(2,"型号", 20, 1.5);
    draw.data.addColumn(2,"状态", 9, 1.5);

    draw.data.addColumn(0,"", 15, 1.5);
    draw.data.addColumn(0,"", 15, 1.5);
    draw.data.addColumn(1,"水平转角", 15, 1.5);
    draw.data.addColumn(1,"水平转角", 15, 1.5);
    draw.data.addColumn(2,"方向", 15, 1.5);
    draw.data.addColumn(2,"度数", 15, 1.5);

    draw.data.addColumn(0,"杆塔基础", 15, 1.5);
    draw.data.addColumn(0,"杆塔基础", 9, 1.5);
    draw.data.addColumn(0,"杆塔基础", 15, 1.5);
    draw.data.addColumn(0,"杆塔基础", 9, 1.5);
    draw.data.addColumn(0,"杆塔基础", 15, 1.5);
    draw.data.addColumn(0,"杆塔基础", 9, 1.5);
    draw.data.addColumn(0,"杆塔基础", 10, 1.5);
    draw.data.addColumn(0,"杆塔基础", 10, 1.5);
    draw.data.addColumn(1,"底盘/塔基", 15, 1.5);
    draw.data.addColumn(1,"底盘/塔基", 9, 1.5);
    draw.data.addColumn(1,"卡盘", 15, 1.5);
    draw.data.addColumn(1,"卡盘", 9, 1.5);
    draw.data.addColumn(1,"拉盘", 15, 1.5);
    draw.data.addColumn(1,"拉盘", 9, 1.5);
    draw.data.addColumn(1,"地形", 10, 1.5);
    draw.data.addColumn(1,"地质情况", 10, 1.5);
    draw.data.addColumn(2,"型号", 15, 1.5);
    draw.data.addColumn(2,"数量", 9, 1.5);
    draw.data.addColumn(2,"型号", 15, 1.5);
    draw.data.addColumn(2,"数量", 9, 1.5);
    draw.data.addColumn(2,"型号", 15, 1.5);
    draw.data.addColumn(2,"数量", 9, 1.5);
    draw.data.addColumn(2,"", 10, 1.5);
    draw.data.addColumn(2,"", 10, 1.5);

    draw.data.addColumn(0,"杆塔配电设备/附件", 25, 1.5);
    draw.data.addColumn(0,"杆塔配电设备/附件", 9, 1.5);
    draw.data.addColumn(0,"杆塔配电设备/附件", 9, 1.5);
    draw.data.addColumn(1,"", 25, 1.5);
    draw.data.addColumn(1,"", 9, 1.5);
    draw.data.addColumn(1,"", 9, 1.5);
    draw.data.addColumn(2,"型号", 25, 1.5);
    draw.data.addColumn(2,"状态", 9, 1.5);
    draw.data.addColumn(2,"数量", 9, 1.5);

    draw.data.addColumn(0,"", 25, 1.5);
    draw.data.addColumn(0,"", 9, 1.5);
    draw.data.addColumn(1,"接地装置", 25, 1.5);
    draw.data.addColumn(1,"接地装置", 9, 1.5);
    draw.data.addColumn(2,"型号", 25, 1.5);
    draw.data.addColumn(2,"数量", 9, 1.5);

    draw.data.addColumn(0,"绝缘子与金具", 15, 1.5);
    draw.data.addColumn(0,"绝缘子与金具", 9, 1.5);
    draw.data.addColumn(0,"绝缘子与金具", 15, 1.5);
    draw.data.addColumn(0,"绝缘子与金具", 9, 1.5);
    draw.data.addColumn(0,"绝缘子与金具", 15, 1.5);
    draw.data.addColumn(0,"绝缘子与金具", 9, 1.5);
    draw.data.addColumn(0,"绝缘子与金具", 15, 1.5);
    draw.data.addColumn(0,"绝缘子与金具", 9, 1.5);
    draw.data.addColumn(1,"耐张绝缘子串", 15, 1.5);
    draw.data.addColumn(1,"耐张绝缘子串", 9, 1.5);
    draw.data.addColumn(1,"柱式绝缘子串", 15, 1.5);
    draw.data.addColumn(1,"柱式绝缘子串", 9, 1.5);
    draw.data.addColumn(1,"接续线夹", 15, 1.5);
    draw.data.addColumn(1,"接续线夹", 9, 1.5);
    draw.data.addColumn(1,"防振锤型号", 15, 1.5);
    draw.data.addColumn(1,"防振锤型号", 9, 1.5);
    draw.data.addColumn(2,"型号", 15, 1.5);
    draw.data.addColumn(2,"数量", 9, 1.5);
    draw.data.addColumn(2,"型号", 15, 1.5);
    draw.data.addColumn(2,"数量", 9, 1.5);
    draw.data.addColumn(2,"型号", 15, 1.5);
    draw.data.addColumn(2,"数量", 9, 1.5);
    draw.data.addColumn(2,"型号", 15, 1.5);
    draw.data.addColumn(2,"数量", 9, 1.5);

    draw.data.addColumn(0,"", 20, 1.5);
    draw.data.addColumn(0,"", 9, 1.5);
    draw.data.addColumn(1,"横担", 20, 1.5);
    draw.data.addColumn(1,"横担", 9, 1.5);
    draw.data.addColumn(2,"型号", 20, 1.5);
    draw.data.addColumn(2,"数量", 9, 1.5);

    draw.data.addColumn(0,"", 15, 1.5);
    draw.data.addColumn(0,"", 9, 1.5);
    draw.data.addColumn(1,"拉线装置", 15, 1.5);
    draw.data.addColumn(1,"拉线装置", 9, 1.5);
    draw.data.addColumn(2,"型号", 15, 1.5);
    draw.data.addColumn(2,"数量", 9, 1.5);

    draw.data.addColumn(0,"交叉跨越(处)", 5, 1.5);
    draw.data.addColumn(0,"交叉跨越(处)", 5, 1.5);
    draw.data.addColumn(0,"交叉跨越(处)", 5, 1.5);
    draw.data.addColumn(0,"交叉跨越(处)", 5, 1.5);
    draw.data.addColumn(0,"交叉跨越(处)", 5, 1.5);
    draw.data.addColumn(0,"交叉跨越(处)", 5, 1.5);
    draw.data.addColumn(1,"", 5, 1.5);
    draw.data.addColumn(1,"", 5, 1.5);
    draw.data.addColumn(1,"", 5, 1.5);
    draw.data.addColumn(1,"", 5, 1.5);
    draw.data.addColumn(1,"", 5, 1.5);
    draw.data.addColumn(1,"", 5, 1.5);
    draw.data.addColumn(2,"公路", 5, 1.5);
    draw.data.addColumn(2,"通讯线", 5, 1.5);
    draw.data.addColumn(2,"电力线", 5, 1.5);
    draw.data.addColumn(2,"房屋", 5, 1.5);
    draw.data.addColumn(2,"经济物", 5, 1.5);
    draw.data.addColumn(2,"河流", 5, 1.5);

    draw.data.addColumn(0,"", 30, 1.5);
    draw.data.addColumn(1,"", 30, 1.5);
    draw.data.addColumn(2,"备注", 30, 1.5);
}
/** 设置图签内容+内容标题
 * @param mxcad 
 * @param value 图签内容
 * @param offsetX 
 * @param baseX 图签基点X坐标
 * @param baseY 图签基点Y坐标
 * @param typeName 图签类型
 * @param midX 内容标题中点X坐标
 * @param midY 内容标题中点Y坐标
 */
function setSignContent(mxcad: any, value: any, offsetX: number, baseX: number, baseY: number, typeName: string, midX: number, midY: number) {
    // 创建文本实体
    const text = new McDbText();
    text.textString = typeName; // 使用传入的文本参数
    text.position = new McGePoint3d(midX + offsetX, midY-25, 0);
    text.alignmentPoint = new McGePoint3d(midX + offsetX, midY-25, 0);
    text.height = 7; // 文本高度
    text.widthFactor = 1; // 文本宽度缩放
    text.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    text.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    text.trueColor = new McCmColor(0, 0, 0); // 文本颜色（紫色）
    // 绘制文本
    mxcad.drawEntity(text);

    // 供电公司
    const textG = new McDbText();
    const positionG = new McGePoint3d(baseX + 10 + offsetX,baseY + 16,0)
    textG.textString = value.countyOrganisationName ? value.countyOrganisationName : ''; // 使用传入的文本参数
    textG.position = positionG;
    textG.alignmentPoint = positionG;
    textG.height = 2; // 文本高度
    textG.widthFactor = 1; // 文本宽度缩放
    textG.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textG.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textG.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    mxcad.drawEntity(textG);

    // 项目名称
    const textPrject = new McDbText();
    const positionPrject = new McGePoint3d(baseX + 55 + offsetX,baseY + 16,0)
    textPrject.textString = value.projectName ? value.projectName : ''; // 使用传入的文本参数
    textPrject.position = positionPrject;
    textPrject.alignmentPoint = positionPrject;
    textPrject.height = 2; // 文本高度
    textPrject.widthFactor = 1; // 文本宽度缩放
    textPrject.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textPrject.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textPrject.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    mxcad.drawEntity(textPrject);

    // 阶段
    const textJd = new McDbText();
    const positionJd = new McGePoint3d(baseX + 80 + offsetX,baseY + 16,0)
    const textString = value.stage && value.stage === '1' ? '需求' : value.stage && value.stage === '2' ? '可研' : value.stage && value.stage === '3' ? '初设' : value.stage && value.stage === '4' ? '施工' : value.stage && value.stage === '5' ? '施工变更' : value.stage && value.stage === '5' ? '竣工' : ''
    textJd.textString = textString; // 使用传入的文本参数
    textJd.position = positionJd;
    textJd.alignmentPoint = positionJd;
    textJd.height = 2; // 文本高度
    textJd.widthFactor = 1; // 文本宽度缩放
    textJd.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textJd.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textJd.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    mxcad.drawEntity(textJd);

    // 时间
    const textTime = new McDbText();
    const positionTime = new McGePoint3d(baseX + 40 + offsetX,baseY + 1.5,0)
    const textStringTime = min_getTime()
    textTime.textString = textStringTime; // 使用传入的文本参数
    textTime.position = positionTime;
    textTime.alignmentPoint = positionTime;
    textTime.height = 1; // 文本高度
    textTime.widthFactor = 1; // 文本宽度缩放
    textTime.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textTime.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textTime.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    mxcad.drawEntity(textTime);

    // 图号：项目编号+n
    const textCode = new McDbText();
    const positionCode = new McGePoint3d(baseX + 60 + offsetX,baseY + 1.5,0)
    const textStringCode = value.proCode ? value.proCode + '-' + value.num : ''; // 使用传入的文本参数
    textCode.textString = textStringCode; // 使用传入的文本参数
    textCode.position = positionCode;
    textCode.alignmentPoint = positionCode;
    textCode.height = 1; // 文本高度
    textCode.widthFactor = 1; // 文本宽度缩放
    textCode.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textCode.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textCode.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    mxcad.drawEntity(textCode);

    // 图框名称：主要设备清单+当前页1,2,3
    const textK = new McDbText();
    const positionK = new McGePoint3d(baseX + 55 + offsetX, baseY + 12,0)
    const textStringK = typeName + '-' + value.num; // 使用传入的文本参数
    textK.textString = textStringK; // 使用传入的文本参数
    textK.position = positionK;
    textK.alignmentPoint = positionK;
    textK.height = 2; // 文本高度
    textK.widthFactor = 1; // 文本宽度缩放
    textK.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textK.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textK.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    mxcad.drawEntity(textK);
}