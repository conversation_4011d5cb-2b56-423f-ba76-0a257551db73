import{d as T,h as R,z as P,a0 as b,_ as A,$ as C,a1 as n,m as V,Q as U,V as h,H as I,W as N,u as g,B as F,a3 as B,a4 as j,ab as z,F as O}from"./vue-DfH9C9Rx.js";import{M as W}from"./index-8X61wlK0.js";import{J as $,I as H,a0 as J,C as Q,s as f,u as q,a1 as S,n as G,a2 as K,_ as X}from"./index-D95UjFey.js";import{M as Y}from"./mxcad-CfPpL1Bn.js";import{S as Z}from"./vuetify-B_xYg4qv.js";import"./mxdraw-C_n_7lEs.js";import"./mapbox-gl-PN3pDJHq.js";import"./xlsx-Ck5MUZvf.js";const ee=()=>{const M=T(0),L=$(),{createLineType:w}=L,{currentLineType:D,lineTypeList:o}=H(L),x=T([]),v=T("");return{lineTypeList:x,createLineType:w,index:M,filePath:v,parseTextLineTypes:t=>{const r=t.split(`
`),c=[];let l=null;for(let p=0;p<r.length;p++){const s=r[p].trim();if(s.startsWith("*")){l&&c.push(l);const u=s.split(","),_=u[0].slice(1).trim(),e=u.slice(1).join(",").trim(),i=r[p+1].trim().replace(/^A,/,"");l=w({name:_,appearance:e,explain:e,value:i}),p+=1}}return l&&c.push(l),c},addLineType:async t=>{let r=!1;const c=Y.getCurrentMxCAD(),l=c.database.getLinetypeTable().get(t.name);if(l.isErase())r=!0;else{if(!await new Promise(_=>{Q().open({title:"MxCAD",text:f("254")+" "+t.name+f("695")+","+f("696"),cancelTitle:f("212"),defineTitle:f("211"),cancel:()=>{_(!1)},define:()=>{_(!0)}})}))return;l.erase()}if(!c.addLinetypeEx(t.name,t.value||"","").isValid()){q().error(f("254")+":"+t.name+f("697")+"!");return}r&&o.value.push(t);const s=o.value.find(({name:u})=>t.name===u);s&&(s.value=t.value,s.explain=s.appearance=t.appearance),D.value=s||t},...J}},te={class:"d-flex algin-center mt-3"},se={class:"mt-2"},ae={class:"w-100"},ne={class:"w-100"},le={class:"text-left"},ie={class:"text-left"},oe={class:"w-100"},re=["onClick"],ce={class:"text-left"},pe={class:"text-left"},ue=R({__name:"index",emits:["change"],setup(M,{expose:L,emit:w}){const{createLineType:D,lineTypeList:o,showDialog:x,isShow:v,index:y,filePath:m,parseTextLineTypes:t,addLineType:r,onReveal:c}=ee(),l=()=>{r(o.value[y.value]),x(!1)},p=[{name:"确定",fun:l,primary:!0},{name:"取消",fun:()=>x(!1)}],s=T(),u=()=>{s.value&&s.value.click()},_=async e=>{const a=e.target.files;if(!a)return;const d=a[0],k=await K(d),E=await S(k);o.value=t(E),m.value=d.name};return P(async()=>{const e=new URL(""+new URL("mx-CeEsFOMF.lin",import.meta.url).href,import.meta.url).href,a=await(await fetch(e)).blob(),d=await S(a);m.value=e,o.value=t(d)}),c(e=>{y.value=o.value.findIndex(({name:i})=>e.name===i)}),L({showDialog:x}),(e,i)=>(b(),A(W,{title:e.t("线型加载或重载"),"max-width":"600",modelValue:g(v),"onUpdate:modelValue":i[1]||(i[1]=a=>F(v)?v.value=a:null),footerBtnList:p},{default:C(()=>[n("div",te,[V(G,{onClick:u,class:"ml-1"},{default:C(()=>[U(h(e.t("文件"))+"(F)...",1)]),_:1}),I(n("input",{class:"form__inset w-100",disabled:!0,"onUpdate:modelValue":i[0]||(i[0]=a=>F(m)?m.value=a:null)},null,512),[[N,g(m)]])]),n("input",{type:"file",ref_key:"fillSelectEl",ref:s,onChange:_,style:{display:"none"},accept:".lin"},null,544),n("div",se,[n("p",null,h(e.t("可用线型")),1),V(Z,{class:"w-100",cellpadding:"20",height:"300"},{default:C(()=>[n("thead",ae,[n("tr",ne,[n("th",le,h(e.t("线型")),1),n("th",ie,h(e.t("说明")),1)])]),n("tbody",oe,[(b(!0),B(O,null,j(g(o),(a,d)=>(b(),B("tr",{key:a.id,class:z(g(y)===d?"active":""),onClick:k=>y.value=d,onDblclick:l},[n("td",ce,h(a.name),1),n("td",pe,h(a.appearance),1)],42,re))),128))])]),_:1})])]),_:1},8,["title","modelValue"]))}}),Le=X(ue,[["__scopeId","data-v-e0f8d3df"]]);export{Le as default};
