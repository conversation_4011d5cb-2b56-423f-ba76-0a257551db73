import{M as c}from"./index-8X61wlK0.js";import{g as _,i as w,d as V,_ as h}from"./index-D95UjFey.js";import{M as g}from"./mxcad-CfPpL1Bn.js";import{E as v,C as x,i as C}from"./vuetify-B_xYg4qv.js";import{h as B,d as k,a0 as r,_ as n,$ as s,a1 as t,m as d,V as e,u as l,Q as D,a5 as M,B as N}from"./vue-DfH9C9Rx.js";import"./mxdraw-C_n_7lEs.js";import"./mapbox-gl-PN3pDJHq.js";import"./xlsx-Ck5MUZvf.js";const y={class:"px-3"},S={class:"title"},E={class:"info mt-3"},I={class:"version-info mt-2"},L={class:"url"},R=B({__name:"index",setup(T){const{showDialog:m,isShow:i}=V,p=[{name:"确定",fun:()=>{m(!1)},primary:!0}],u=k("");return(a,o)=>(r(),n(c,{title:a.t("703"),modelValue:l(i),"onUpdate:modelValue":o[0]||(o[0]=f=>N(i)?i.value=f:null),"max-width":"320",footerBtnList:p},{default:s(()=>[t("div",y,[d(x,{class:"mt-4"},{default:s(()=>[d(v,null,{default:s(()=>[t("p",S,e(a.t("704"))+" "+e(l(_)()),1),t("p",E,e(a.t("705")),1),t("p",I,[t("p",null,e(a.t("708"))+":"+e(l(g).App.GetVersionDateString()),1)])]),_:1})]),_:1}),t("div",L,[t("p",null,[D(e(a.t("710"))+": ",1),o[1]||(o[1]=t("a",{href:"https://www.mxdraw3d.com/",target:"_blank"},"https://www.mxdraw3d.com",-1))]),l(w)()?(r(),n(C,{key:0,"model-value":u.value},{prepend:s(()=>[t("div",null,e(a.t("712"))+":",1)]),_:1},8,["model-value"])):M("",!0)])])]),_:1},8,["title","modelValue"]))}}),q=h(R,[["__scopeId","data-v-75d0a9da"]]);export{q as default};
