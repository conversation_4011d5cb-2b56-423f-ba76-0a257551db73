import request from '@/utils/request'

// 查询物料类别列表
export function listBaseMaterialsType(query) {
  return request({
    url: '/base_materials_type/list',
    method: 'get',
    params: query
  })
}

// 查询物料类别详细
export function getBaseMaterialsType(objId) {
  return request({
    url: '/base_materials_type/' + objId,
    method: 'get'
  })
}

// 新增物料类别
export function addBaseMaterialsType(data) {
  return request({
    url: '/base_materials_type',
    method: 'post',
    data: data
  })
}

// 修改物料类别
export function updateBaseMaterialsType(data) {
  return request({
    url: '/base_materials_type/update',
    method: 'post',
    data: data
  })
}

// 删除物料类别
export function delBaseMaterialsType(objId) {
  return request({
    url: '/base_materials_type' + objId,
    method: 'post'
  })
}

// 获取物料类别树形图
export function getBaseMaterialsTypeTreeList(query) {
  return request({
    url: '/base_materials_type/treeList',
    method: 'get',
    params: query
  })
}
