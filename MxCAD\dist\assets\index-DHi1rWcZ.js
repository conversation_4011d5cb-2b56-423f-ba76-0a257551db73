import{Q as X,V as nt,Z as T,U as F,_ as ot}from"./index-CzBriCFR.js";import{M as Y}from"./index-itnQ6avM.js";import{M as O,y as r,D as at,a6 as it,L as st,a as M,z as k,a7 as U,K as rt,$ as lt}from"./mxcad-DrgW2waE.js";import{M as Z}from"./mxdraw-BQ5HhRCY.js";import{d as E,h as z,w as tt,_ as H,$ as j,u as g,a9 as dt,a1 as ut,c as ct,z as mt,m as i,a4 as d,B as W,F as K,a0 as C,V as b,Q as P,a5 as wt,a3 as pt}from"./vue-Cj9QYd7Z.js";import{z as q,B as _,h as yt,j as ht,a as gt,V as J,b as Q,L as xt}from"./vuetify-BqCp6y38.js";import"./handsontable-Ch5RdAT_.js";import"./mapbox-gl-DQAr7S0z.js";const ft=()=>O.getCurrentMxCAD().getDatabase().getDimStyleTable().getAllRecordId()?.map(u=>u.getMcDbDimStyleTableRecord())||[],Pt=w=>O.getCurrentMxCAD().getDatabase().getDimStyleTable().get(w)?.getMcDbDimStyleTableRecord(),Mt=()=>O.getCurrentMxCAD().getDatabase().getCurrentlyDimStyleId().getMcDbDimStyleTableRecord(),B={width:300,height:140,paddingRatio:.1},Dt=(w,e)=>{const u=O.getCurrentMxCAD(),l=Array.isArray(w)?w:[w],c=[];let o=null,n=null;for(const f of l){const{maxPt:v,minPt:D}=f.getBoundingBox();!o||!n?(o=new r(v.x,v.y,v.z),n=new r(D.x,D.y,D.z)):(o.x=Math.max(o.x,v.x),o.y=Math.max(o.y,v.y),n.x=Math.min(n.x,D.x),n.y=Math.min(n.y,D.y))}if(!o||!n)return;const{maxPt:x,minPt:a}=u.getDatabase().currentSpace.getBoundingBox(),y=new r(Math.max(o.x,x.x),Math.max(o.y,x.y),0),p=new r(Math.min(n.x,a.x),Math.min(n.y,a.y),0),V=Z.screenCoordLong2Doc(Math.abs(y.x-p.x));p.x-=V,p.y-=V,y.x+=V,y.y+=V;let s=new at;s.append(p),s.append(new r(p.x,y.y,0)),s.append(new r(y.x,y.y,0)),s.append(new r(y.x,p.y,0));let t=new it;t.setVertices(s);const m=u.drawEntity(t),L=m.getMcDbEntity();if(!L)return;for(const f of l){const v=u.drawEntity(f),D=v.getMcDbEntity();D&&(D.drawOrder=L.drawOrder+1,c.push(v))}let h=Math.abs(o.x-n.x),I=Math.abs(o.y-n.y);if(h<1||I<1)return;const G=typeof e?.paddingRatio=="number"?e.paddingRatio:B.paddingRatio;let S;if(typeof e?.padding=="number"){const f=typeof e?.width=="number"?e.width:B.width;S=e.padding/f*h}else S=Math.min(h,I)*G;o.x+=S,o.y+=S,n.x-=S,n.y-=S,h=Math.abs(o.x-n.x),I=Math.abs(o.y-n.y);let A,R;if(e?.width&&e?.height){const f=e.width/e.height,v=h/I,D=(o.x+n.x)/2,$=(o.y+n.y)/2;if(v<f){const N=I*f/2;o.x=D+N,n.x=D-N}else if(v>f){const N=h/f/2;o.y=$+N,n.y=$-N}A=e.width,R=e.height}else if(e?.width)A=e.width,R=Math.round(e.width/(h/I));else if(e?.height)R=e.height,A=Math.round(e.height*(h/I));else if(R=B.height,A=Math.round(R*(h/I)),A>B.width){const f=B.width/A;A=B.width,R=Math.round(R*f)}return new Promise((f,v)=>{Z.getCurrentDraw().createCanvasImageData(D=>{m.erase(),c.forEach($=>$.erase()),f(D),u.mxdraw.updateCanvasSize()},{width:A,height:R,range_pt1:o.toVector3(),range_pt2:n.toVector3()})})},Ct=()=>{const w=new st(699.953,39.054,0,670);w.trueColor=new M(255,255,255);const e=new k;e.startPoint=new r(-214.683,1029.846,0),e.endPoint=new r(632.317,1029.846,0),e.trueColor=new M(255,255,255);const u=new U;u.xLine1Point=e.startPoint,u.xLine2Point=e.endPoint,u.dimLinePoint=new r((e.startPoint.x+e.endPoint.x)/2,e.startPoint.y+80,0),u.rotation=0,u.trueColor=new M(255,255,255);const l=new k;l.startPoint=new r(632.317,1029.846,0),l.endPoint=new r(632.317,909.846,0),l.trueColor=new M(255,255,255);const c=new k;c.startPoint=new r(632.317,909.846,0),c.endPoint=new r(2139.221,909.846,0),c.trueColor=new M(255,255,255);const o=new U;o.xLine1Point=c.startPoint,o.xLine2Point=c.endPoint,o.dimLinePoint=new r((c.startPoint.x+c.endPoint.x)/2,c.startPoint.y+80,0),o.rotation=0,o.trueColor=new M(255,255,255);const n=new k;n.startPoint=new r(-214.683,33.846,0),n.endPoint=new r(-214.683,1029.846,0),n.trueColor=new M(255,255,255);const x=new U;x.xLine1Point=n.startPoint,x.xLine2Point=n.endPoint,x.dimLinePoint=new r(n.startPoint.x-80,(n.startPoint.y+n.endPoint.y)/2,0),x.rotation=Math.PI/2,x.trueColor=new M(255,255,255);const a=new rt;a.center=new r(692.281,45.25,0),a.radius=907.036,a.startAngle=3.1541653489654133,a.endAngle=4.707204922330254,a.trueColor=new M(255,255,255);const y=new U,p=new r(a.center.x+a.radius*Math.cos((a.startAngle+a.endAngle)/2),a.center.y+a.radius*Math.sin((a.startAngle+a.endAngle)/2),0);y.xLine1Point=a.center,y.xLine2Point=p,y.dimLinePoint=new r((a.center.x+p.x)/2+50,(a.center.y+p.y)/2+50,0);const V=Math.atan2(p.y-a.center.y,p.x-a.center.x);y.rotation=V,y.trueColor=new M(255,255,255);const s=new k;s.startPoint=new r(663.71,-861.336,0),s.endPoint=new r(3162.946,-863.298,0),s.trueColor=new M(255,255,255);const t=new k;t.startPoint=new r(3162.946,-863.298,0),t.endPoint=new r(2139.221,909.846,0),t.trueColor=new M(255,255,255);const m=new U;m.xLine1Point=t.startPoint,m.xLine2Point=t.endPoint,m.dimLinePoint=new r(t.startPoint.x+100,(t.startPoint.y+t.endPoint.y)/2,0);const L=Math.atan2(t.endPoint.y-t.startPoint.y,t.endPoint.x-t.startPoint.x);m.rotation=L,m.trueColor=new M(255,255,255),new lt;const h=new U,I=(s.startPoint.x+t.startPoint.x)/2,G=(s.startPoint.y+t.startPoint.y)/2;return h.xLine1Point=t.startPoint,h.xLine2Point=new r(I,G,0),h.dimLinePoint=new r(t.startPoint.x+200*Math.cos((L+Math.PI)/2),t.startPoint.y+200*Math.sin((L+Math.PI)/2),0),h.trueColor=new M(255,255,255),[w,e,l,c,n,a,s,t,u,o,x,y,m,h]},bt=()=>{const w=E();return{updateImgUrl:async u=>{const{height:l,width:c,paddingRatio:o}=u,n=Ct();w.value=await Dt(n,{height:l,width:c,paddingRatio:o})},imgUrl:w}},vt=["src","width","height"],Lt=z({__name:"PreviewImage",props:{styleName:{}},setup(w){const e=w,u=E(null),l=E({width:272,height:220}),{imgUrl:c,updateImgUrl:o}=bt();return tt(()=>e.styleName,n=>{n&&(u.value=Pt(n),o({height:l.value.height,width:l.value.width}))},{immediate:!0}),(n,x)=>(H(),j("div",{class:"w-100 bg-black preview",style:ut({width:l.value.width+"px",height:l.value.height+"px"})},[g(c)?(H(),j("img",{key:0,src:g(c),width:l.value.width,height:l.value.height},null,8,vt)):dt("",!0)],4))}}),It=w=>{const e=E([{name:"Standard"},{name:"样式0"}]),u=E(0);tt(w,o=>{if(o){const n=ft().filter(a=>!!a);e.value=n.map(a=>({name:a.name}));const x=Mt();x&&l(e.value.findIndex(a=>a.name===x.name))}});const l=o=>{u.value=o},c=ct(()=>e.value[u.value].name);return{items:e,index:u,setIndex:l,currentItemText:c}},Vt=()=>{const w=["所有样式","正在使用样式"],e=E(w[0]);return{selectItems:w,selectCurrentItem:e}},At={class:"px-3 pt-2"},Rt={class:"mt-3"},St={class:"d-flex flex-column justify-center algin-center w-100 h-100"},Tt=z({__name:"index",setup(w){const{isShow:e,showDialog:u}=X(!1,"Mx_Dimstyle"),{isShow:l,showDialog:c}=X(!1),{items:o,index:n,setIndex:x,currentItemText:a}=It(e),{selectItems:y,selectCurrentItem:p}=Vt(),V=[{name:"关闭",fun:()=>u(!1)}];return mt(()=>{}),(s,t)=>(H(),j(K,null,[i(Y,{title:s.t("509"),modelValue:g(e),"onUpdate:modelValue":t[2]||(t[2]=m=>W(e)?e.value=m:null),"max-width":"600",footerBtnList:V},{default:d(()=>[C("div",At,[C("p",null,b(s.t("510")+":"+g(a)),1),i(q,{class:"mt-1"},{default:d(()=>[i(_,{cols:4},{default:d(()=>[C("p",null,[P(b(s.t("362"))+"(",1),t[6]||(t[6]=C("span",{class:"text-decoration-underline"},"S",-1)),t[7]||(t[7]=P("):"))]),i(yt,{density:"compact",class:"list-border overflow-y py-0",height:"220"},{default:d(()=>[(H(!0),j(K,null,wt(g(o),(m,L)=>(H(),pt(gt,{key:L+m.name,onClick:h=>g(x)(L),active:g(n)===L,value:m,class:"pa-0 list-item","min-height":"24",height:"24"},{default:d(()=>[i(ht,{textContent:b(m.name)},null,8,["textContent"])]),_:2},1032,["onClick","active","value"]))),128))]),_:1}),C("p",Rt,[P(b(s.t("511"))+"(",1),t[8]||(t[8]=C("span",{class:"text-decoration-underline"},"L",-1)),t[9]||(t[9]=P("):"))]),i(J,{"bg-color":"grey-lighten-2",class:"",modelValue:g(p),"onUpdate:modelValue":t[0]||(t[0]=m=>W(p)?p.value=m:null),items:g(y)},{"append-item":d(()=>t[10]||(t[10]=[])),_:1},8,["modelValue","items"])]),_:1}),i(_,{cols:6},{default:d(()=>[C("p",null,b(s.t("478")+":"+g(a)),1),i(Lt,{"style-name":g(a)},null,8,["style-name"]),i(nt,{title:s.t("222")},{default:d(()=>t[11]||(t[11]=[P(" ISO-25 ")])),_:1},8,["title"])]),_:1}),i(_,{cols:2,"align-self":"start"},{default:d(()=>[i(T,{class:"mt-5 w-100"},{default:d(()=>[i(F,{"key-name":"U"},{default:d(()=>[P(b(s.t("512")),1)]),_:1})]),_:1}),i(T,{class:"mt-2 w-100",onClick:t[1]||(t[1]=()=>g(c)(!0))},{default:d(()=>[i(F,{"key-name":"N"},{default:d(()=>[P(b(s.t("270")),1)]),_:1}),i(Q,{icon:"class:iconfont more"})]),_:1}),i(T,{class:"mt-2 w-100"},{default:d(()=>[i(F,{"key-name":"M"},{default:d(()=>[P(b(s.t("287")),1)]),_:1}),i(Q,{icon:"class:iconfont more"})]),_:1}),i(T,{class:"mt-2 w-100"},{default:d(()=>[i(F,{"key-name":"D"},{default:d(()=>[P(b(s.t("219")),1)]),_:1}),i(Q,{icon:"class:iconfont more"})]),_:1})]),_:1})]),_:1})])]),_:1},8,["title","modelValue"]),i(Y,{title:s.t("513"),"max-width":"300",modelValue:g(l),"onUpdate:modelValue":t[5]||(t[5]=m=>W(l)?l.value=m:null)},{actions:d(()=>t[16]||(t[16]=[C("div",{class:"mt-3"},null,-1)])),default:d(()=>[i(q,{class:"px-3"},{default:d(()=>[i(_,{cols:"9"},{default:d(()=>[C("p",null,[P(b(s.t("514"))+"(",1),t[12]||(t[12]=C("span",{class:"text-decoration-underline"},"N",-1)),t[13]||(t[13]=P(")"))]),i(xt,{class:"mt-2"}),C("p",null,[P(b(s.t("515"))+"(",1),t[14]||(t[14]=C("span",{class:"text-decoration-underline"},"N",-1)),t[15]||(t[15]=P(")"))]),i(J,{"bg-color":"grey-lighten-2",class:"",modelValue:g(p),"onUpdate:modelValue":t[3]||(t[3]=m=>W(p)?p.value=m:null),items:g(y)},null,8,["modelValue","items"])]),_:1}),i(_,{cols:"3","justify-center":"","align-self":"center"},{default:d(()=>[C("div",St,[i(T,{primary:"",class:"mt-6 w-100"},{default:d(()=>[P(b(s.t("516")),1)]),_:1}),i(T,{class:"mt-6 w-100",onClick:t[4]||(t[4]=m=>g(c)(!1))},{default:d(()=>[P(b(s.t("517")),1)]),_:1})])]),_:1})]),_:1})]),_:1},8,["title","modelValue"])],64))}}),Ft=ot(Tt,[["__scopeId","data-v-58594093"]]);export{Ft as default};
