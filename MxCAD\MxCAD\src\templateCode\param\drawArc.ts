import { MxCpp, McDbArc, McGePoint3d, McCmColor} from "mxcad";

//画圆弧
function drawArc() {
  const mxcad = MxCpp.getCurrentMxCAD();
  // 创建新画布
  mxcad.newFile();

  const arc = new McDbArc()
  arc.center = new McGePoint3d(-100, -100),
    arc.radius = 20
  arc.startAngle = Math.PI / 2
  arc.endAngle = Math.PI * 3 / 2
  arc.trueColor = new McCmColor(255, 233, 0)
  mxcad.drawEntity(arc)

  const arc_1 = new McDbArc()
  const pt1 = new McGePoint3d(-60, -80)
  const pt2 = new McGePoint3d(-80, -100)
  const pt3 = new McGePoint3d(-60, -120)
  arc_1.trueColor = new McCmColor(0, 255, 0)
  arc_1.computeArc(pt1.x, pt1.y, pt2.x, pt2.y, pt3.x, pt3.y)
  mxcad.drawEntity(arc_1)

  mxcad.drawArc(-120, -100, 20, 100, 250);

  mxcad.drawColor = new McCmColor(255, 0, 0)
  mxcad.drawArc2(-80, -80, -100, -100, -80, -120)

  mxcad.drawColor = new McCmColor(0, 0, 255)
  mxcad.drawArc3(-40, -80, -40, -120, 1)

  mxcad.zoomAll()
  mxcad.zoomScale(0.8)
};

// 调用绘圆方法
drawArc()