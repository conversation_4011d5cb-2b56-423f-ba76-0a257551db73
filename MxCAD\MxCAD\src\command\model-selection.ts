import {
    MxCpp,
    McObject,
    MxCADUiPrEntity,
    MxCADResbuf,
    addCurrentSelect,
    MxCADUtility,
    McObjectId,
    MxCADUiPrPoint,
    MxCADSelectionSet, McDbAttribute, McDbBlockReference, McDbAttributeDefinition
} from "mxcad";
import { MxFun} from "mxdraw";


async function Mx_ModelSelection(data: any) {
    let selEntity = new MxCADUiPrEntity();
    selEntity.setMessage("选择对象");
    let id = await selEntity.go();
    if (!id.isValid()) return;

    let ent = id.getMcDbEntity();
    if (ent === null) return;
    const equId = ent.getxDataString('equipmentId')?.val
    console.log('选择对象', id)
    const params = {
        id: equId || id.id
    }
    MxFun.postMessageToParentFrame({messageId: data?.id, params });

    clearSelectionChange()
    // const filter = new MxCADResbuf()
/*    const getPoint = new MxCADUiPrPoint()
    const point = await getPoint.go()
    if(!point) return
    let objId = MxCADUtility.findEntAtPoint(point.x, point.y,point.z,-1)
// 选中对象设置高亮
    MxCADUtility.highlightEntity(objId.id, true)
    console.log('选择对象:',objId)*/
   /* while(true){
        const getPoint = new MxCADUiPrPoint()
        const point = await getPoint.go()
        if(!point) break;
        let objId = MxCADUtility.findEntAtPoint(point.x, point.y,point.z,-1)
        console.log('选择对象', objId)
        mxcad.addCurrentSelect(objId)
    }*/
}

function Mx_EquipmentReplace(data: any) {
    const mxcad = MxCpp.App.getCurrentMxCAD();
    console.log('Mx_EquipmentReplace', data)
    const value = data.params.data
    const ss = new MxCADSelectionSet();
    console.log('ss', ss)
    let filter = new MxCADResbuf();
    filter.AddMcDbEntityTypes("INSERT");
    //选择所有图形元素
    ss.allSelect(filter);
    if (ss.count() == 0) return;
    ss.forEach((idF)=> {
        console.log('idF', idF)
        const item = value.find(item => item.id == idF.id);
        if(item) {
        const ent = idF.getMcDbEntity() as McDbBlockReference;
            console.log('ent', ent)
            // 删除
            idF.erase();
            const entId = ent.getObjectID();
            // 获取对象句柄
            const sHandle = ent.getHandle();
            const str = item.legendtypekeyName
            const attribDefList = item.attributeList
            BlockFun(
                {spanWidth: 100,
                    spanClass: str,
                    IsShowArr: []},
                `/assets/ModuleLegens/${str}.mxweb`,
                1,
                JSON.parse(attribDefList),
                ent.position
            ).then(res => {
                console.log('res',res)
                MxFun.postMessageToParentFrame({messageId: data?.id, params: {id: res.id.id} });
            })
        }
    })

    setTimeout(() => {
           mxcad.updateDisplay()
   mxcad.regen();
    }, 500)
}


const clearSelectionChange = () => {
    MxCpp.getCurrentMxCAD().mxdraw.clearMxCurrentSelect()
}

// 绘制线路
async function BlockFun(formData, fileName, scale, attribDefList, position) {
    // 设置图块路径
    let blkFilePath = import.meta.url.substring(0, import.meta.url.lastIndexOf('/')) + fileName;
    console.log(blkFilePath)
    // 插入图块文件
    let mxcad = MxCpp.App.getCurrentMxCAD();
    let blkrecId = await mxcad.insertBlock(blkFilePath, formData.spanClass);
    if (!blkrecId.isValid()) {
        // 插入图块
        return { id: null, handle: null };
    }

    let blkRef = new McDbBlockReference();
    blkRef.blockTableRecordId = blkrecId;
    let box = blkRef.getBoundingBox();
    if (box.ret && formData.spanWidth) {
        let dLen = Math.abs(box.maxPt.x - box.minPt.x);
        blkRef.setScale(parseFloat(formData.spanWidth) / dLen)
    }
    blkRef.position = position;
    let newBlkRefId = mxcad.drawEntity(blkRef);
    if (!newBlkRefId.isValid) {
        console.log("insert error");
        return { id: null, handle: null };
    }
    blkRef = newBlkRefId.getMcDbEntity() as McDbBlockReference;

    // 如果块有属性定义，下面为块引创建属性定义。
    blkRef.disableDisplay(true);
    let blkRecord: any = blkrecId.getMcDbBlockTableRecord();
    let ids = blkRecord.getAllEntityId();
    if(ids.length === 1 && ids[0].isKindOf("McDbBlockReference")){
        const blockId = ids[0];
        const block = blockId.getMcDbEntity() as McDbBlockReference;
        const recordId = block.blockTableRecordId;
        blkRef.blockTableRecordId = recordId;
        blkRef.disableDisplay(true);
        setAttribute(newBlkRefId, recordId, attribDefList, formData);
    }else{
        setAttribute(newBlkRefId, blkrecId, attribDefList, formData);
    }
    // 获取该对象的实体
    const ent = newBlkRefId.getMcDbEntity();
    if(!ent) return { id: null, handle: null };
    // 获取对象ID
    const entId = ent.getObjectID();
    // 获取对象句柄
    const sHandle = ent.getHandle();
    console.log("对象id", entId);
    console.log("对象句柄", sHandle);
    console.log("对象坐标", blkRef.position);
    console.log('blkRef', blkRef)
    return { id: entId, handle: sHandle, pointsXYZ: blkRef.position };
}

function setAttribute(newBlkRefId, blkrecId, attribDefList, formData){
    const blkRef = newBlkRefId.getMcDbEntity() as McDbBlockReference;
    // 如果块有属性定义，下面为块引创建属性定义。
    blkRef.disableDisplay(true);
    let blkRecord: any = blkrecId.getMcDbBlockTableRecord();
    let ids = blkRecord.getAllEntityId();
    ids.forEach((id: any, index: any) => {
        if (!id.isKindOf("McDbAttributeDefinition")) return;
        let attribDef = id.getMcDbEntity() as McDbAttributeDefinition;
        console.log(attribDef.tag)
        let tag = attribDef.tag;
        let attrib = new McDbAttribute();
        attrib.position = attribDef.position;
        attrib.alignmentPoint = attribDef.alignmentPoint
        attrib.height = attribDef.height
        attrib.trueColor = attribDef.trueColor
        attrib.widthFactor = attribDef.widthFactor
        if (attribDefList.length > 0) {
            attribDefList.forEach(item => {
                if (item.name === tag) {
                    attrib.textString = item.value
                }
            })
        } else {
            attrib.textString = attribDef.textString
        }
        attrib.tag = tag;
        if (formData.IsShowArr.length > 0) {
            formData.IsShowArr.forEach(item => {
                if (item.TagName === tag) {
                    attrib.isInvisible = item.IsShow === '1' ? false : true
                }
            })
        } else {
            attrib.isInvisible = false
        }
        attrib.transformBy(blkRef.blockTransform);
        attrib = blkRef.appendAttribute(attrib).getMcDbEntity() as McDbAttribute;
        attrib.textStyle = attribDef.textStyle
        attrib.layer = attribDef.layer
    })
    blkRef.disableDisplay(false);
}


export function init() {
    MxFun.addCommand("Mx_ModelSelection", Mx_ModelSelection);
    MxFun.addCommand("Mx_EquipmentReplace", Mx_EquipmentReplace);
}
