import {ref} from 'vue';
import {getEdit5Value} from "@/api/onlineDesign/index.js";
import {ElMessage} from "element-plus";
export const useFormData = () => {
    const dataList = ref([]); // 响应式数据

    const initData = async (list) => {
        console.log(list,'初始化list')
        try {
            // 使用 Promise.all 批量处理异步操作
            const processedList = await Promise.all(
                list.map(async (item) => {
                    const legendAttributes = item.LegendAttributes.filter(sb => sb.IsLook);
                    const formGroupItems = await handleFormGroupItems(item).catch(() => []); // 捕获错误并返回空数组
                    return {
                        ...item,
                        formData: handFormData(legendAttributes),
                        formGroupItems
                    };
                })
            );

            // 赋值给 dataList
            dataList.value = processedList;
            console.log('数据初始化完成:', dataList.value);
        } catch (error) {
            console.error('数据初始化失败:', error);
            // ElMessage.warning('当前暂无数据！')
            dataList.value = []; // 失败时清空数据
        }
    };


// 工具函数：解析分组类型
    const parseGroupType = (groupType) => {
        const [prefix = '', suffix = '0'] = (groupType || '').split('_');
        return {prefix, suffix: parseInt(suffix, 10) || 0};
    };

// 工具函数：处理不同编辑类型的属性项
    const processAttributeItem = async (item, legendTypeKey) => {
        const editType = Number(item.editType) || 0;
        const processedItem = {...item};

        // 策略模式处理不同编辑类型
        const editTypeHandlers = {
            1: () => {
                processedItem.options = handFormItemOption(item.Category);
                return processedItem;
            },
            5: async () => {
                try {
                    processedItem.options = await getAsyncOption(legendTypeKey, item.AttributeKey);
                } catch (e) {
                    processedItem.options = [];
                }
                return processedItem;
            }
        };

        return editTypeHandlers[editType]?.() || processedItem;
    };

// 主处理函数
    const handleFormGroupItems = async (e) => {
        // 1. 数据预处理
        const validAttributes = e.LegendAttributes
            .filter(sb => sb.IsLook)
            .filter(item => item.groupType); // 提前过滤无效项
console.log(validAttributes,'数据预处理')
        // 2. 异步处理所有属性项
        const processedItems = await Promise.all(
            validAttributes.map(async item => ({
                item: await processAttributeItem(item, e.LegendTypeKey),
                groupInfo: parseGroupType(item.groupType)
            }))
        );

        // 3. 分组处理
        const groupMap = processedItems.reduce((acc, {item, groupInfo}) => {
            const groupKey = groupInfo.prefix;

            if (!acc[groupKey]) {
                acc[groupKey] = {
                    title: groupInfo.prefix,
                    groupNumber: groupInfo.suffix,
                    children: []
                };
            }

            acc[groupKey].children.push(item);
            return acc;
        }, {});

        // 4. 排序处理
        return Object.values(groupMap)
            .map(group => ({
                ...group,
                children: group.children.sort((a, b) => a.Sort - b.Sort)
            }))
            .sort((a, b) => a.groupNumber - b.groupNumber);
    };

    const handFormData = (arr) => {
        return arr.reduce((acc, item) => {
            acc[item.AttributeKey] = item.AttributeValue;
            return acc;
        }, {});
    }

    const handFormItemOption = (str) => {
        return str?.split(';').map(item => {
            const [value, label] = item.split(',');
            return {value, label};
        })
    }

    const getAsyncOption = (legendTypeKey, attributeKey) => {
        const params = {
            legendTypeKey,
            attributeKey
        }
        return getEdit5Value(params).then(res => {
            if (res.code === 200) {
                return res.data.map((item) => {
                    return {
                        label: item.text,
                        value: item.value,
                    }
                })
            } else {
                return []
            }
        }).catch(() => {
            return []
        })
    }

    return {
        dataList, // 响应式数据
        initData  // 初始化方法
    };
};
