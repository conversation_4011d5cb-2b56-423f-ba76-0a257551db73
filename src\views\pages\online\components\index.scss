::v-deep .el-select {
  width: 257px;
  height: 27px;
}

::v-deep .el-form-item.el-form-item--default {
  margin-bottom: 0;
  line-height: 0;
}

::v-deep .el-form-item--default .el-form-item__content {
  line-height: normal;
}
::v-deep .el-form-item__error {
  right: 22px !important;
  top: 12px !important;
  left: auto;
}
::v-deep .el-select__placeholder {
  color: #282b33;
  font-weight: 400;
}
::v-deep .el-input__inner {
  color: #282b33;
  font-weight: 400;
}
h4 {
  font-family: "Noto Sans SC", sans-serif;
}
.title-text {
  color: #008486;
  font-weight: bold;
  font-family: "Noto Sans SC", sans-serif;
}
.check_radio {
  ::v-deep .el-checkbox__label {
    color: #282b33;
    font-weight: 400;
  }
}
.line {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 1px;
  text-align: center;
  font-family: "Noto Sans SC", sans-serif;
  font-weight: bold;
  :last-child {
    margin-bottom: 0;
  }
  ::v-deep .el-table .el-table__header-wrapper th {
    background: #50a9aa !important;
    font-size: 14px;
    color: #ffffff;
    font-weight: 600;
  }
  .tw-ipt {
    width: 84px;
    height: 40px;
    background: #b5dddd;
    //   font-weight: 550;
    font-size: 14px;
    color: #282b33;
    display: flex;
    justify-content: center;
    align-items: center;
    // span {
    //   width: 60px;
    // }
  }
  .radio-group {
    color: #282b33;
    font-weight: 400;
    ::v-deep .el-radio__label {
      color: #282b33;
      font-weight: 400;
    }
  }
  .checkbox-group {
    ::v-deep .el-checkbox__label {
      color: #282b33;
      font-weight: 400;
    }
  }

  .input-table {
    margin-top: -2px;
    margin-bottom: 1px;
  }
  .tw-sct {
    width: 105px;
    height: 40px;
    background: #e4f2f2;
    display: flex;
    justify-content: center;
    align-items: center;
    .in-item {
      width: 82px;
      height: 27px;
    }
  }
  .line-item {
    width: 84px;
    height: 40px;
    background: #b5dddd;
    //   font-weight: 550;
    font-size: 14px;
    color: #282b33;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 10px;
    // span {
    //   width: 60px;
    // }
  }
  .line-input {
    // flex: 1;
    width: 294px;
    height: 40px;
    background: #e4f2f2;
    display: flex;
    justify-content: center;
    align-items: center;
    .in-item {
      width: 257px;
      height: 27px;
    }
  }
  .line-no-display {
    width: 294px;
    height: 40px;
    background: #e4f2f2;
    display: flex;
    align-items: center;
  }
  .radio-input {
    width: 294px;
    height: 60px;
    display: flex;
    background: #e4f2f2;
    flex-direction: column;
    .in-item {
      height: 20px;
      ::v-deep .el-input__wrapper {
        width: 150px;
      }
    }
  }
  //按钮
  .line-bnt {
    width: 84px;
    height: 30px;
    background: #0e8b8d;
    border-radius: 5px;
    color: white;
    line-height: 30px;
    cursor: pointer;
    font-size: 12px;
  }
}
.lineTwo {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 1px;
  text-align: center;
  font-family: "Noto Sans SC", sans-serif;
  font-weight: bold;
  :last-child {
    margin-bottom: 0;
  }
  ::v-deep .el-table .el-table__header-wrapper th {
    background: #50a9aa !important;
    font-size: 14px;
    color: #ffffff;
    font-weight: 600;
  }
  .tw-ipt {
    width: 84px;
    height: 40px;
    background: #b5dddd;
    //   font-weight: 550;
    font-size: 14px;
    color: #282b33;
    display: flex;
    justify-content: center;
    align-items: center;
    // span {
    //   width: 60px;
    // }
  }
  .radio-group {
    color: #282b33;
    font-weight: 400;
    ::v-deep .el-radio__label {
      color: #282b33;
      font-weight: 400;
    }
  }
  .checkbox-group {
    ::v-deep .el-checkbox__label {
      color: #282b33;
      font-weight: 400;
    }
  }

  .input-table {
    margin-top: -2px;
    margin-bottom: 1px;
  }
  .tw-sct {
    width: 105px;
    height: 40px;
    background: #e4f2f2;
    display: flex;
    justify-content: center;
    align-items: center;
    .in-item {
      width: 82px;
      height: 27px;
    }
  }
  .line-item {
    width: 84px;
    height: 40px;
    background: #b5dddd;
    //   font-weight: 550;
    font-size: 14px;
    color: #282b33;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 10px;
    // span {
    //   width: 60px;
    // }
  }
  .line-input {
    // flex: 1;
    width: 180px;
    height: 40px;
    background: #e4f2f2;
    display: flex;
    justify-content: center;
    align-items: center;
    .in-item {
      width: 170px;
      height: 27px;
      ::v-deep .el-date-editor.el-input, .el-date-editor.el-input__wrapper {
        width: 170px;
        height: 27px;
      }
      ::v-deep .el-select__wrapper {
        min-height: 27px;
      }
    }
  }
  .line-no-display {
    width: 180px;
    height: 40px;
    background: #e4f2f2;
    display: flex;
    align-items: center;
  }
  .radio-input {
    width: 180px;
    height: 60px;
    display: flex;
    background: #e4f2f2;
    flex-direction: column;
    .in-item {
      height: 20px;
      ::v-deep .el-input__wrapper {
        width: 150px;
      }
    }
  }
  //按钮
  .line-bnt {
    width: 84px;
    height: 30px;
    background: #0e8b8d;
    border-radius: 5px;
    color: white;
    line-height: 30px;
    cursor: pointer;
    font-size: 12px;
  }
}
// 标注样式
.bz-line {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 1px;
  font-family: "Noto Sans SC", sans-serif;
  font-weight: bold;
  height: 150px;
  .bz-left {
    width: 100px;
    margin-left: 10px;
    .bz-border {
      padding: 0 10px;
      border: #69b5b6 2px solid;
      height: 120px;
    }
  }
  .bz-right {
    width: 500px;
    margin-left: 10px;
    .input-table {
      margin-top: -2px;
      margin-bottom: 1px;
    }
    ::v-deep .el-table .el-table__header-wrapper th {
      background: #50a9aa !important;
      font-size: 14px;
      color: #ffffff;
      font-weight: 600;
    }
  }
}
.material {
  ::v-deep .el-table .el-table__header-wrapper th {
    background: #50a9aa !important;
    font-size: 14px;
    color: #ffffff;
    font-weight: 600;
  }
  .active {
    background: #0e8b8d !important;
    color: white !important;
  }
}
.small-material {
  ::v-deep .el-table .el-table__header-wrapper th {
    background: #50a9aa !important;
    font-size: 11px !important;
    color: #ffffff;
    font-weight: 600;
  }
  ::v-deep .el-table td .el-table__cell div {
    font-size: 10px !important;
    font-weight: 600;
  }
}
.meter-draw {
  ::v-deep .el-table .el-table__header-wrapper th {
    background: #50a9aa !important;
    font-size: 14px;
    color: #ffffff;
    font-weight: 600;
  }
  .draw {
    font-size: 12px;
    color: #282b33;
    font-weight: bold;

    span {
      margin-left: 10px;
    }
  }
}

.draw-footer {
  margin: 0 10px 10px 10px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  border: 1px solid #badddd;
}
.photo-box {
  padding: 10px;
  display: flex;
  height: 450px;
  justify-content: flex-start;
  background: #e4f2f2;
  .photo-left {
    width: 200px;
    height: 430px;
    .photo-border {
      height: 30px;
      width: 200px;
      border-right: 2px solid #badddd;
      border-left: 2px solid #badddd;
      border-top: 2px solid #badddd;
    }
    .left-tree {
      border: 2px solid #badddd;
      padding: 10px;
      height: 400px;
      overflow: auto;
      .el-tree {
        background: transparent;
      }
      ::v-deep .el-tree-node:focus > .el-tree-node__content {
        background: rgba(80, 169, 170, 0.4);
      }
      ::v-deep .el-tree-node :hover {
        background: rgba(80, 169, 170, 0.1) !important;
      }
    }
  }
  .photo-right {
    margin-left: 10px;
    width: 600px;
    height: 430px;
    .photo-border {
      height: 30px;
      width: 600px;
      border: 2px solid #badddd;
    }
    .photo-table {
      width: 350px;
      ::v-deep .el-table .el-table__header-wrapper th {
        background: #50a9aa !important;
        font-size: 14px;
        color: #ffffff;
        font-weight: 600;
      }
    }
    .photo-img {
      margin-top: -2px;
      width: 250px;
      background: white;
    }
  }
  .photo-rightCmd {
    margin-left: 10px;
    width: 800px;
    height: 430px;
    .photo-border {
      height: 30px;
      width: 800px;
      border: 2px solid #badddd;
    }
    .photo-table {
      width: 350px;
      ::v-deep .el-table .el-table__header-wrapper th {
        background: #50a9aa !important;
        font-size: 14px;
        color: #ffffff;
        font-weight: 600;
      }
    }
    .photo-img {
      margin-top: -2px;
      width: 250px;
      background: white;
    }
    .photo-imgCmd {
      margin-top: -2px;
      width: 450px;
      background: white;
    }
  }
}

.photo-boxGhwl {
  padding: 10px;
  display: flex;
  height: 450px;
  justify-content: flex-start;
  //background: #e4f2f2;
  .photo-left {
    width: 200px;
    height: 430px;
    .photo-border {
      height: 30px;
      width: 200px;
      border-right: 2px solid #badddd;
      border-left: 2px solid #badddd;
      border-top: 2px solid #badddd;
    }
    .left-tree {
      border: 2px solid #badddd;
      padding: 10px;
      height: 400px;
      overflow: auto;
      .el-tree {
        background: transparent;
      }
      ::v-deep .el-tree-node:focus > .el-tree-node__content {
        background: rgba(80, 169, 170, 0.4);
      }
      ::v-deep .el-tree-node :hover {
        background: rgba(80, 169, 170, 0.1) !important;
      }
    }
  }
  .photo-right {
    margin-left: 10px;
    width: 100%;
    height: 430px;
    .photo-border {
      height: 30px;
      width: 100%;
      border: 2px solid #badddd;
    }
    .photo-table {
      width: 100%;
      ::v-deep .el-table .el-table__header-wrapper th {
        background: #50a9aa !important;
        font-size: 14px;
        color: #ffffff;
        font-weight: 600;
      }
    }
    .photo-img {
      margin-top: -2px;
      width: 250px;
      background: white;
    }
  }
}
::v-deep .el-checkbox__input.is-checked .el-checkbox__inner {
  background: #fff;
}
/* 修改Element UI中el-checkbox选中时的对号颜色 */
::v-deep .el-checkbox__inner:after {
  border-color: #07888a; /* 将对号颜色改为红色 */
}


.formBox {
  display: flex;
  //justify-content: center;
  align-items: center;
  margin-bottom: 1px;
  text-align: center;
  font-family: "Noto Sans SC", sans-serif;
  font-weight: bold;
  :last-child {
    margin-bottom: 0;
  }
  ::v-deep .el-table .el-table__header-wrapper th {
    background: #50a9aa !important;
    font-size: 14px;
    color: #ffffff;
    font-weight: 600;
  }
  .formBox-item {
    width: 104px;
    height: 40px;
    background: #b5dddd;
    font-size: 14px;
    color: #282b33;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 10px;
  }
  .formBox-input {
    width: 180px;
    height: 40px;
    background: #e4f2f2;
    display: flex;
    justify-content: center;
    align-items: center;
    .in-item {
      width: 170px;
      height: 27px;
      ::v-deep .el-date-editor.el-input, .el-date-editor.el-input__wrapper {
        width: 170px;
        height: 27px;
      }
      ::v-deep .el-select__wrapper {
        min-height: 27px;
      }
    }
  }
  .formBox-btn {
    margin-left: 10px;
  }
}