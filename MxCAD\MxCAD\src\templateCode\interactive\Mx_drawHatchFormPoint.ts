import { MxCADUiPrPoint, MxCADUtility, MxCpp } from "mxcad";

// 选点填充
async function MxTest_DrawHatchFormPoint() {
    // 指定填充区域内部一点
    const getPoint = new MxCADUiPrPoint();
    getPoint.setMessage("\n指定填充区域内部一点:");
    getPoint.disableAllTrace(true);
    getPoint.setDisableOsnap(true);
    let pt = await getPoint.go();
    if (!pt) return;
    
    // 绘制填充
    let hatch = MxCADUtility.builderHatchFromPoint(pt);
    if (!hatch) {
        Mx.MxFun.acutPrintf("没有找到闭合区域\n")
        return;
    }

    let mxcad = MxCpp.getCurrentMxCAD();
    mxcad.drawEntity(hatch);
};

// 调用选点填充的方法
MxTest_DrawHatchFormPoint();