<script setup>
import DataDialog from "@/components/DataDialog/index.vue";
import useAppStore from "@/store/modules/app.js";
import {tansParams} from "@/utils/ruoyi.js";
import {getToken} from "@/utils/auth.js";
import {useRoute} from "vue-router";
import {getTuFu, getTuZhiLeiXing, hebingPdf, selectTzList} from "@/api/desginManage/ToolManagement.js";
import {downloadFileDWG} from "@/api/task/index.js";
const appStore = useAppStore();
const urlRoute = new useRoute()
const route = useRoute();
let { proxy } = getCurrentInstance();
import { ElLoading } from 'element-plus'
const closeDialog = () => {
  appStore.mapIndex = "";
  const myiframe = document.getElementById("myiframeYl");
  const existingIframe = document.getElementById("MXCADYl");
  if (existingIframe) {
    myiframe.removeChild(existingIframe);
  }
};
const storeForm=ref({
  tfxz:'',
  tzlx:'',
})
const iframe = ref(null)
const multipleSelection = ref([])
const tableData = ref([
  {
    name: "线路1",
    key: "New",
    sk: " JG/TH",
    ksd: "删除",
  },
])
const options = ref({
  tfxzOptions: [],
  tzlxOptions: [],
})
const cadYlFun = () => {
  const myiframe = document.getElementById("myiframeYl");
  const existingIframe = document.getElementById("MXCADYl");
  if (existingIframe) {
    myiframe.removeChild(existingIframe);
  }
  let params = tansParams({
    ...urlRoute.query,
    token: getToken() || urlRoute.query?.token,
    mod:'preview'
  })
  // const src = params.length !== 0
  //     ? `${import.meta.env.VITE_APP_MX_APP_SRC}?${params.endsWith('&') ? params.substring(0, params.length - 1) : params}`
  //     : `${import.meta.env.VITE_APP_MX_APP_SRC}`
  const src =`${import.meta.env.VITE_APP_MX_APP_SRC}?${params.endsWith('&') ? params.substring(0, params.length - 1) : params}`
  const iframeElement = document.createElement("iframe");
  iframe.value = iframeElement;
  nextTick(async () => {
    const myiframe = document.getElementById("myiframeYl");
    iframeElement.src = src
    iframeElement.id = "MXCADYl";
    iframeElement.style.width = "100%";
    iframeElement.style.height = "100%";
    myiframe.append(iframeElement);
    setTimeout(() => {
      oniframeMessage({
        type: "cadPreview",
        content: "Mx_cadPreview",
        formData: {
          openUrl: null,
        }
      })
    },1600)
  });
}
const tufuList = () => {
  getTuFu(route.query.id).then(res => {
    getTuZhiLeiXing(route.query.id).then(resD => {
      options.value.tfxzOptions = res.data
      storeForm.value.tfxz = res.data.length > 0 ? res.data[0] : ''
      options.value.tzlxOptions = resD.data
      storeForm.value.tzlx = resD.data.length > 0 ? resD.data[0] : ''
      getTzList()
    })
  })
}
const getTzList = () => {
  if (!storeForm.value.tfxz) return proxy.$message.warning('请选择图幅！')
  if (!storeForm.value.tzlx) return proxy.$message.warning('请选择图纸类型！')
  selectTzList({ tf: storeForm.value.tfxz, tz: storeForm.value.tzlx,id:route.query.id }).then(res => {
    tableData.value = res.data
  })
}
// 处理点击事件
const oniframeMessage = (param) => {
  const myiframe = document.querySelector("#myiframeYl").querySelector("iframe");
  if (myiframe && myiframe.contentWindow) {
    myiframe.contentWindow.postMessage(param, "*");
    // 先移除之前的事件监听器，防止重复绑定
    window.removeEventListener('message', handleMessage);
    // 添加新的事件监听器
    window.addEventListener('message', handleMessage);
  }
}
const handleMessage = (event) => {}
// 处理点击事件
const oniframeMessageF = (param) => {
  const myiframe = document.querySelector("#myiframe").querySelector("iframe");
  if (myiframe && myiframe.contentWindow) {
    myiframe.contentWindow.postMessage(param, "*");
    // 先移除之前的事件监听器，防止重复绑定
    window.removeEventListener('message', handleMessageF);
    // 添加新的事件监听器
    window.addEventListener('message', handleMessageF);
  }
}
const handleMessageF = (event) => {
  if (event.data.type === 'parentCad') {
    if (event.data.params.content === '批量打印Pdf') {
      const arr = multipleSelection.value.filter(item => item.assessmenttype)
      console.log(arr,'ddddd')
      if(arr.length === 0)return proxy.$message.warning('请选择要打印的图纸') 
      const loading = ElLoading.service({
        lock: true,
        text: '加载中...',
        background: 'rgba(0, 0, 0, 0.7)',
      })
      const files = event.data.params.formData.files
      const fileFormData = new FormData()
      fileFormData.append('taskId', route.query.id)
      files.forEach(item => {
        fileFormData.append('files', item)
      })
      fileFormData.append("ids", JSON.stringify(arr));
      hebingPdf(fileFormData).then(res => {
        const link = document.createElement("a");
        const url = window.URL.createObjectURL(res);
        link.href = url;
        link.download = "批量打印.pdf";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
        loading.close()
      })
    }
  }
}
const Preview = (row) => {
  downloadFileDWG(row.id).then(res => {
    oniframeMessage({
      type: "cadPreview",
      content: "Mx_cadPreview",
      formData: {
        openUrl: res,
      }
    })
  })
}
const dwBtn = (row) => {
  oniframeMessageF({
    type: "MsgCad",
    content: "Mx_tzdw",
    formData: {
      handler: row.handler,
      name: row.name
    }
  })
}
const handleSelectionChange = (val) => {
  multipleSelection.value = val
}
const dyPdfBtn = () => {
  const arr = multipleSelection.value.filter(item => !item.assessmenttype)
  oniframeMessageF({
    type: "MsgCad",
    content: "Mx_pdfDy",
    formData: {
      arr: JSON.stringify(arr),
    }
  })
}
watch(
    () => appStore.mapIndex,
    (newInfo, oldInfo) => {
      console.log(newInfo, oldInfo);
      if (newInfo == "批量打印") {
        cadYlFun()
        tufuList()
      }
    }
);
</script>

<template>
  <!-- 批量打印 -->
  <data-dialog v-if="appStore.mapIndex == '批量打印'" dataWidth="830px" @close="closeDialog">
    <template #header>
      <h4 style="margin-left: 10px; margin-top: 0px; line-height: 39px">
        批量打印
      </h4>
    </template>
    <template #body>
      <div style="display: flex">
        <div class="line">
          <div class="line-item">
            <span> 图幅选择 </span>
          </div>
          <div class="line-input">
            <el-select
                v-model="storeForm.tfxz"
                class="in-item"
                placeholder="请选择"
                @change="getTzList"
            >
              <el-option
                  v-for="item in options.tfxzOptions"
                  :key="item"
                  :label="item"
                  :value="item"
              >
              </el-option>
            </el-select>
          </div>
        </div>
        <div class="line">
          <div class="line-item">
            <span> 图纸类型 </span>
          </div>
          <div class="line-input">
            <el-select
                v-model="storeForm.tzlx"
                class="in-item"
                placeholder="请选择"
                @change="getTzList"
            >
              <el-option
                  v-for="item in options.tzlxOptions"
                  :key="item"
                  :label="item"
                  :value="item"
              >
              </el-option>
            </el-select>
          </div>
        </div>
      </div>
      <div class="material" style="display: flex">
        <el-table
            :data="tableData"
            border
            size="small"
            style="
              margin-top: -2px;
              margin-left: -2px;
              width: 378px;
              height: 300px;
            "
            @selection-change="handleSelectionChange"
        >
          <el-table-column align="center" label="序号" type="index" width="50"></el-table-column>
          <el-table-column align="center" type="selection" width="50" />
          <el-table-column align="center" label="图纸列表" prop="name"/>
          <el-table-column align="center" label="操作" prop="name" width="110">
            <template #default="scope">
              <el-button v-if="!scope.row.assessmenttype" link type="primary" @click="dwBtn(scope.row)">定位</el-button>
              <el-button v-else link type="primary" @click="Preview(scope.row)">预览</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div style="width: 378px;height: 300px;">
          <div id="myiframeYl" style="height: 300px;padding: 5px"></div>
        </div>
      </div>
      <div class="line" style="background: #e4f2f2; height: 50px">
        <div class="line-bnt" @click="dyPdfBtn">打印</div>
        <div style="
              margin-left: 30px;
              background: #e4f2f2;
              border: 2px solid #199092;
              color: #199092;
              width: 84px;
              height: 30px;
              line-height: 27px;
              cursor: pointer;
              border-radius: 5px;
              font-size: 12px;
            " @click="closeDialog">取消</div>
      </div>
    </template>
  </data-dialog>
</template>

<style lang="scss" scoped>
@use '../../index' as *;
::v-deep(.mytable-style.vxe-table .vxe-header--column.col-green) {
  background: #50a9aa;
  font-size: 14px;
  color: #ffffff;
  font-weight: 600;
}
</style>
