import {MxF<PERSON>} from "mxdraw";
import {
  MxCpp, MxCADUiPrPoint, MxCADUiPrInt, McDbEntity,
  MxCADResbuf, McGePoint3d, MxCADUtility, McDbArc, McDbCircle,
  McObjectId, McGeVector3d, McDbPolyline, McCmColor, McDbLine,
  McDb, McDbCurve, MxCADUiPrEntity, McDbObject, MxCADUiPrDist, MxCADUiPrKeyWord,
  McDbText, MxCADUiPrString, McDbBlockTableRecord, McDbBlockReference, McGeMatrix3d,
  McDbEllipse,
  Mx3dGePoint
} from "mxcad";
// import proj4 from "proj4"
import svgson from 'svgson'
import {svgstr} from './svg'

export function init(){
  MxFun.addCommand("Mx_NewLonLat", Mx_DrawSVG);
}

const Mx_DrawSVG = () => {
  let mxcad = MxCpp.App.getCurrentMxCAD();
  svgson(svgstr).then(json =>{
    console.log(json)
    const styles = []
    const defs = []
    const gs = []
    json.children.forEach(c=>{
      if(c.name === 'style'){
        c.value.split('\n\t\t').filter(s=>s!='').forEach(element => {
          styles.push(element)
        });
      }else if(c.name === 'defs'){
        c.children.forEach(element => {
          defs.push({
            id: element.attributes.id, 
            viewBox: element.attributes.viewBox, 
            children: element.children
          })
        });
      }else if(c.name === 'g' && c.children.length > 0){
        gs.push(readChild(c))
      }
    })
    console.log(defs)
    console.log(gs)
    for(const element of gs){
      if (element.id === 'BackGround_Layer') continue
      for(const container of element.children){
        if(!container.children) continue
        container.children.forEach(entity=>{
          if(entity.name === 'polyline'){
            const point1 = entity.points[0].split(' ')
            const point2 = entity.points[0].split(' ')
            const pl = new McDbPolyline();
            pl.addVertexAt(new McGePoint3d(point1[0], point1[1], 0));
            pl.addVertexAt(new McGePoint3d(point2[0], point2[1], 0));
            pl.trueColor = new McCmColor(
              parseInt(entity.stroke.substring(1,3), 16),
              parseInt(entity.stroke.substring(3,5), 16),
              parseInt(entity.stroke.substring(5,7), 16)
            )
            mxcad.drawEntity(pl)
          } else if(entity.name === 'use'){
            const def = defs.find(o=>o.id === entity['xlink:href'].substring(1))
            console.log('use', entity)
            console.log('def', def)
          } else if(entity.name === 'line'){
            const l = new McDbLine(entity.x1, entity.y1, 0, entity.x2, entity.y2, 0);
            // l.trueColor = 
            mxcad.drawEntity(l)
          } else if(entity.name === 'ellipse'){
            const ellipse = new McDbEllipse()
            ellipse.center = new McGePoint3d(entity.cx, entity.cy, 0)
            // ellipse.majorAxis = majorAxis
            // ellipse.minorAxis = minorAxis
            // ellipse.radiusRatio = radiusRatio
            // ellipse.startAngle = startAngle
            // ellipse.endAngle = endAngle
          }
        })
      }
    }
  })
  // const [cadX, cadY] = proj4("EPSG:4326", "EPSG:3857", [120.461875220714, 30.6413025534919]);
  // const [cadX1, cadY1] = proj4("EPSG:4326", "EPSG:3857", [120.461960343222, 30.6413833669921]);
  // const line1 = new McDbLine(cadX, cadY, 0, cadX1, cadY1, 0)
  // mxcad.drawEntity(line1)
  // const line2 = new McDbLine(cadX, cadY, 0, cadX + 1000, cadY + 1000, 0)
  // mxcad.drawEntity(line2)
  // console.log(`长度：${line1.getLength()}`, cadX, cadY, cadX1, cadY1);
}

const readChild = (data)=>{
  const result = {
    id: data.attributes.id, 
    viewBox: data.attributes.viewBox, 
    children: []
  }
  if(data.name === 'g' && data.children.length > 0){
    data.children.forEach(element => {
      const r = readChild(element)
      if(r != null) result.children.push(r)
    })
    return result
  }
  else if(data.name !== 'g' && data.name !== 'metadata'){
    return drawTypeMethod(data)
  }
}

const drawTypeMethod = (data)=>{
  const attr = {
    name: data.name,
    ...data.attributes
  }
  console.log(data.name)
  switch (data.name) {
    case 'polyline':
      attr.points = data.attributes.points.split(',')
      break;
    case 'rect':
      attr.points = [data.attributes.x + ' ' + data.attributes.y]
      break;
    case 'line':
      attr.points = [attr.x1 + ' ' + attr.y1, attr.x2 + ' ' + attr.y2]
    default:
      break;
  }
  return attr
}