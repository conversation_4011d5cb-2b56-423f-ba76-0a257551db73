var Tt=Object.defineProperty;var Rt=(t,e,o)=>e in t?Tt(t,e,{enumerable:!0,configurable:!0,writable:!0,value:o}):t[e]=o;var G=(t,e,o)=>Rt(t,typeof e!="symbol"?e+"":e,o);import{i as Vt,ag as me,aV as he,af as xe,s as y,aW as V,G as ut,H as it,ae as _t,av as Nt,u as Ce,b as tt,aX as gt,aY as ft,A as Wt,k as Le,ah as ue,I as ve,aZ as yt,a_ as ht,am as wt,an as ot,J as $t,a$ as zt,b0 as Kt,b1 as Ot,b2 as Ft,b3 as Bt,ao as Ht,b4 as Ut}from"./index-D95UjFey.js";import{a4 as jt,M as $,F as mt,K as Q,c as Te,h as j,N as W,Y as Xt,y as z,L as U,I as ce,O as ee,A as Z,B as q,a as et,a5 as Ge,k as ae,e as F,Z as Ze,J as ke,m as Re,a6 as Yt,i as qe,a7 as Ke,a8 as rt,$ as ye,a9 as Gt,g as Zt,a3 as qt,d as nt,l as Jt,aa as Je}from"./mxcad-CfPpL1Bn.js";import"./mapbox-gl-PN3pDJHq.js";import{M as Qt}from"./index-BH_Qf1-v.js";import{s as en}from"./hooks-CmmFp-On.js";import{g as N,M as I,h as S,o as Ve,D as xt,v as ge,w as Pt,t as pt,x as tn,a as nn,f as sn}from"./mxdraw-C_n_7lEs.js";import{p as on}from"./print-B4h2MorP.js";import{d as rn}from"./hooks-B307GJzH.js";import{u as At}from"./hooks-DBtpDKrj.js";import{u as an}from"./useMultilineTextDialog-whPVs4Gi.js";import"./vue-DfH9C9Rx.js";import"./vuetify-B_xYg4qv.js";import"./xlsx-Ck5MUZvf.js";const cn=(t=!1,e=!1,o=!1)=>new Promise(async(i,r)=>{let d;d=$.App.getCurrentMxCAD().getCurrentOriginaFileName(),d.length==0?d="temp_empty"+he(!0):d.indexOf(".")==-1?d+=he(!0):d.substring(d.length-6)!=he(!0)&&(d+=he(!0));let a=d;!$.App.getCurrentMxCAD().saveFile(d,async s=>{let n;/^((?!chrome|android).)*safari/i.test(navigator.userAgent)?n=new Blob([s.buffer],{type:"application/octet-stream"}):n=new Blob([s.buffer],{type:"application/octet-binary"});try{i({blob:n,data:s,filename:a})}catch(D){r(D),console.error(D)}},e,o,t?void 0:{compression:0})&&r(y("391"))}),bt=async()=>{try{if(!Vt()){const t=me(),{filename:e,blob:o,data:i}=await cn(!0,!1,!1);await jt({blob:o,filename:e,types:[{description:"webcad File",accept:{"application/octet-stream":[he(!0)]}}]}),t&&xe("MxFullScreen")}}catch(t){console.error(t)}},ln=async()=>{await bt()};async function dn(t,e){let o=t+"_preloading.json",i=await mt.getJsonFromUrl(o),r={ok:!0,tz:!1};if(!i)return new Promise((l,s)=>{l(r)});i.tz&&(r.tz=i.tz);let d=[];return i.images.forEach(l=>{l.substring(0,5)!="http:"&&l.substring(0,6)!="https:"&&d.push(l)}),i.images=d,i.images.length===0&&i.externalReference.length===0?new Promise((l,s)=>{l(r)}):(i.hash=e,(await en(i))?.data?r.ok=!0:r.ok=!1,new Promise((l,s)=>{l(r)}))}const un=async()=>{const t=new Q;t.setMessage(y("375"));let e;t.setUserDraw(l=>{e=l});const o=await t.go();if(!o||!o.isValid())return;let i=o.getMcDbCurve();if(!i||!(i instanceof Te))return;const r=new j;r.setUserDraw((l,s)=>{if(!i||!e)return;const n=i.clone();n&&(n.move(e,l),s.drawMcDbEntity(n))});const d=await r.go();if(!d)return;let a=i.clone();if(a)if(a.move(e,d),i instanceof W){const l=$.getCurrentMxCAD().drawEntity(a);let s=new Xt;s.copyFormAryId([l]),$.App.MxCADAssist.MxExplode(s.imp)}else{const l=a.getSamplePoints(.1);if(l.GetCount()===0)return;let s;const n=$.getCurrentMxCAD();l.forEach((f,D)=>{if(D===1010){const P=new z(f.x,f.y,f.z);if(s){const g=new U;g.startPoint=s,g.endPoint=P,g.colorIndex=a.colorIndex,g.trueColor=a.trueColor,g.drawOrder=a.drawOrder,g.layer=a.layer,g.layerId=a.layerId,g.linetype=a.linetype,g.linetypeScale=a.linetypeScale,g.lineweight=g.lineweight,g.textStyle=g.textStyle,n.drawEntity(g)}s=P}}),n.updateDisplay()}};V("_SampleCurve",un);function _e(t,e,o){const i=e.clone(),r=o.clone(),d=t.clone(),a=d.sub(i),l=r.sub(i),s=d.sub(r);let n,f=a.crossProduct(l).length()/l.length(),D=a.dotProduct(l);const P=l.clone().mult(D/l.length()**2);return i.clone().addvec(P),D<0?n=a.length():D>Math.pow(l.length(),2)?n=s.length():n=f,Math.floor(n)}const gn=async()=>{const t=$.getCurrentMxCAD(),e=new j;e.setMessage(y("342")),e.setKeyWords("");let o=await e.go(),i,r;if(e.getStatus()===N.kNone){const n=new Q,f=new ce;f.AddMcDbEntityTypes("CIRCLE,ARC,LINE,LWPOLYLINE");let D;const P=(g,m)=>{D=g;const x=F.findEntAtPoint(g.x,g.y,g.z,-1,f);if(!(x&&x.isValid()))return;const u=x.getMcDbEntity();u&&(r&&r.highlight(!1),u.highlight(!0),r=u)};for(;;){n.setMessage(y("343")),n.setUserDraw(P),n.setFilter(f);const m=(await n.go()).getMcDbEntity();if(r&&r.highlight(!1),n.getStatus()===N.kCancel)return;if(m instanceof ee){const x=m.getStartPoint().val,u=m.radius,c=Z.kXAxis.clone().rotateBy(m.endAngle).mult(u),h=m.center.clone().addvec(c);if(!x||!h)return;o=x,i=h;break}else if(m instanceof q){const x=m.center,u=m.getStartPoint().val;if(!u)return;o=u,i=u.clone().addvec(x.sub(u).mult(2));break}else if(m instanceof U){o=m.startPoint,i=m.endPoint;break}else if(m instanceof W){for(let x=0;x<m.numVerts();x++){const u=m.getPointAt(x).val,c=m.getPointAt(x+1).val,w=m.getClosestPointTo(D,!1).val;c&&_e(w,u,c)===0&&(o=u,i=c)}break}else{I.acutPrintf(`
`+y("344"));continue}}}if(!o||!i&&(e.setMessage(y("345")),e.setUserDraw((n,f)=>{o&&f.drawMcDbLine(o.x,o.y,o.z,n.x,n.y,n.z)}),i=await e.go(),!i))return;let d,a,l,s;for(;;){e.setMessage(y("346")),e.setKeyWords(`[${y("347")}(T)/${y("281")}(A)${typeof s>"u"?y("352")+"/(H)/"+y("353")+"(V)":""}/${y("354")}(R)]`),e.clearLastInputPoint();let n;const f=o.clone(),D=i.clone();let P,g;e.setUserDraw(u=>{if(!o||!i)return;n&&n.erase(),r instanceof q&&(P||(P=r.center.clone().addvec(Z.kXAxis.clone().rotateBy(Math.PI/2).mult(r.radius))),g||(g=r.center.clone().addvec(Z.kXAxis.clone().rotateBy(-Math.PI/2).mult(r.radius))),u.y<P.y&&u.y>g.y&&(u.x>o.x||u.x<i.x)&&(o=P,i=g),u.x<f.x&&u.x>D.x&&(u.y>P.y||u.y<g.y)&&(o=f,i=D)),s==="H"&&(u.x<o.x?u.x=o.x:u.x>i.x&&(u.x=i.x)),s==="V"&&(u.y<o.y&&(u.y=o.y),u.y>i.y&&(u.y=i.y)),n=t.drawDimRotated(o.x,o.y,i.x,i.y,u.x,u.y,l||0);const c=n.getMcDbDimension();c&&(c.textPosition=u,c.useSetTextPosition(),d&&(c.dimensionText=d),a&&(c.textRotation=a),c.trueColor=new et(0,255,0))});const m=await e.go();if(e.isKeyWordPicked("T")){const u=new Ge;u.clearLastInputPoint(),u.setMessage(`${y("348")}<${d||n.getMcDbDimension()?.dimensionText||""}>`),u.setKeyWords("");const c=await u.go();if(typeof c!="string")return;d=c,n&&n.erase();continue}if(n&&n.erase(),e.isKeyWordPicked("A")){const u=new ae;u.clearLastInputPoint(),u.setMessage(y("338"));const c=await u.go();if(!c||u.getStatus()===N.kCancel)return;u.getDetailedResult()===S.kCoordIn?a=c*(Math.PI/180):a=c;continue}if(e.isKeyWordPicked("H")){s="H";continue}if(e.isKeyWordPicked("V")){s="V";continue}if(e.isKeyWordPicked("R")){const u=new ae;u.clearLastInputPoint(),u.setMessage(y("355"));const c=await u.go();if(!c||u.getStatus()===N.kCancel)return;u.getDetailedResult()===S.kCoordIn?l=c*(Math.PI/180):l=c;continue}if(!m)return;s==="H"&&(m.x<o.x?m.x=o.x:m.x>i.x&&(m.x=i.x)),s==="V"&&(m.y<o.y&&(m.y=o.y),m.y>i.y&&(m.y=i.y)),n=t.drawDimRotated(o.x,o.y,i.x,i.y,m.x,m.y,l||0);const x=n.getMcDbDimension();if(!x)return;x.textPosition=m,x.useSetTextPosition(),d&&(x.dimensionText=d),a&&(x.textRotation=a),x.trueColor=new et(0,255,0),t.updateDisplay();break}};V("_DrawRotatedDimension",gn);const fn=async()=>{const t=$.getCurrentMxCAD();console.log(t.drawDimStyle);const e=new j;e.setMessage(`${y("342")}<${y("205")}>`),e.setKeyWords("");let o=await e.go(),i;if(e.getStatus()===N.kNone){const a=new Q,l=new ce;l.AddMcDbEntityTypes("CIRCLE,ARC,LINE,LWPOLYLINE");let s,n;const f=(D,P)=>{s=D;const g=F.findEntAtPoint(D.x,D.y,D.z,-1,l);if(!(g&&g.isValid()))return;const m=g.getMcDbEntity();m&&(n&&n.highlight(!1),m.highlight(!0),n=m)};for(;;){a.setMessage(y("343")),a.setUserDraw(f),a.setFilter(l);const P=(await a.go()).getMcDbEntity();if(n&&n.highlight(!1),a.getStatus()===N.kCancel)return;if(P instanceof ee){const g=P.getStartPoint().val,m=P.radius,x=Z.kXAxis.clone().rotateBy(P.endAngle).mult(m),c=P.center.clone().addvec(x);if(!g||!c)return;o=g,i=c;break}else if(P instanceof q){const g=P.center,m=P.getClosestPointTo(s,!1).val;if(!m)return;o=m,i=m.clone().addvec(g.sub(m).mult(2));break}else if(P instanceof U){o=P.startPoint,i=P.endPoint;break}else if(P instanceof W){for(let g=0;g<P.numVerts();g++){const m=P.getPointAt(g).val,x=P.getPointAt(g+1).val,u=P.getClosestPointTo(s,!1).val;x&&_e(u,m,x)===0&&(o=m,i=x)}break}else{I.acutPrintf(`
`+y("344"));continue}}}if(!o||!i&&(e.setMessage(y("345")),e.setUserDraw((a,l)=>{o&&l.drawMcDbLine(o.x,o.y,o.z,a.x,a.y,a.z)}),i=await e.go(),!i))return;let r,d;for(;;){e.setMessage(y("346")),e.setKeyWords(`[${y("347")}(T)/${y("281")}(A)]`),e.clearLastInputPoint();let a;e.setUserDraw(n=>{if(!o||!i)return;a&&a.erase(),a=t.drawDimAligned(o.x,o.y,i.x,i.y,n.x,n.y);const f=a.getMcDbDimension();f&&(r&&(f.dimensionText=r),d&&(f.textRotation=d))});const l=await e.go();if(e.isKeyWordPicked("T")){const n=new Ge;n.clearLastInputPoint(),n.setMessage(`${y("348")}<${r||a.getMcDbDimension()?.dimensionText||""}>`),n.setKeyWords("");const f=await n.go();if(typeof f!="string")return;r=f,a&&a.erase();continue}if(a&&a.erase(),e.isKeyWordPicked("A")){const n=new ae;n.clearLastInputPoint(),n.setMessage(y("338"));const f=await n.go();if(!f||n.getStatus()===N.kCancel)return;n.getDetailedResult()===S.kCoordIn?d=f*(Math.PI/180):d=f;continue}if(!l)return;a=t.drawDimAligned(o.x,o.y,i.x,i.y,l.x,l.y);const s=a.getMcDbDimension();if(!s)return;s.textPosition=l,r&&(s.dimensionText=r),d&&(s.textRotation=d),t.updateDisplay();break}};V("_DrawAlignedDimension",fn);async function yn(){let t=I.getCurrentDraw().getViewAngle();t-=Math.PI*.5,$.getCurrentMxCAD().zoomAngle(t)}async function hn(){const t=new Ze;t.setMessage(y("310")),t.setKeyWords(`[${y("311")} UCS(C)/${y("312")}(W)/${y("281")}(A)/${y("313")}(X)]`);const e=await t.go();if(e?.toLocaleLowerCase(),e?.toLocaleLowerCase(),e?.toLocaleLowerCase()==="a"){const o=new ae;o.clearLastInputPoint(),o.setMessage(y("314"));let i=await o.go();if(!i)return;o.getDetailedResult()===S.kCoordIn&&(i=i*(Math.PI/180)),$.getCurrentMxCAD().zoomAngle(i)}if(e?.toLocaleLowerCase()==="x"){const o=new ae;o.clearLastInputPoint(),o.setMessage(y("315"));let i=await o.go();if(!i)return;o.getDetailedResult()===S.kCoordIn&&(i=i*(Math.PI/180));let r=I.getCurrentDraw().getViewAngle();r+=i,$.getCurrentMxCAD().zoomAngle(r)}}V("Mx_Plan90CCW",yn);V("Mx_Plan",hn);function wn(){const t=$.getCurrentMxCAD();let e=t.mxdraw.getViewColor();const{createColor:o}=ut();xe("Mx_Color",{color:o({color:it(e).toString()}),call:i=>{const r=it(i),d=r.red(),a=r.green(),l=r.blue();t.setViewBackgroundColor(d,a,l),I.callEvent("updateBackgroundColor",new et(d,a,l))}})}V("_ViewColor",wn);function Dt(t,e){const o=document.timeline?document.timeline.currentTime:performance.now();let i=!1;function r(d){if(i)return;const a=d-o,l=Math.round(a/t);e(l);const s=(l+1)*t+o,n=document.timeline?document.timeline.currentTime:performance.now();return setTimeout(()=>{requestAnimationFrame(r)},s-n)}return r(o),()=>{i=!0}}function Mt(t,e,o){let i=e,r=o;for(;r-i>1e-4;){let a=(i+r)/2;Math.floor(t/a)*a>t||a<e?r=a:i=a}let d=Math.floor(t/i);return t/d}function Se(t,e,o,i){const r=t.distanceTo(e),d=[],a=Mt(r,o,i);if(isNaN(a))return d;const l=r/a,s=e.sub(t).normalize();for(let n=0;n<l;n++)d.push(t.clone().addvec(s.clone().mult(a*n)));return d}function mn(t=new z,e=new z,o=3,i=Math.PI*2){const r=[];o=Math.max(3,o),r.push(e);const d=i/o;for(let a=1;a<o;a++){const l=Math.cos(d*a),s=Math.sin(d*a),n=t.clone(),f=e.clone(),D=f.x-n.x,P=f.y-n.y,g=D*l-P*s+n.x,m=D*s+P*l+n.y,x=new z(g,m);r.push(x)}return r}const xn=(t,e)=>{const o=new z(t.x,e.y,t.z),i=new z(e.x,t.y,e.z);return[t,o,e,i]};async function Pn(){const t=I.viewCoordLong2Cad(2);let e=Number(localStorage.getItem("mx_revcloud_minArcLength")),o=Number(localStorage.getItem("mx_revcloud_maxArcLength"));if(isNaN(e)||e<1e-4){const n=I.viewCoordLong2Cad(12);localStorage.setItem("mx_revcloud_minArcLength",n.toString())}if(isNaN(o)||o<1e-4){const n=I.viewCoordLong2Cad(12);localStorage.setItem("mx_revcloud_maxArcLength",n.toString())}const i=new j;let r=-.45,d=!1,a=!1,l=!1;function s(n,f){n.colorIndex=f.colorIndex,n.trueColor=f.trueColor,n.drawOrder=f.drawOrder,n.layer=f.layer,n.layerId=f.layerId,n.linetype=f.linetype,n.linetypeScale=f.linetypeScale,n.lineweight=f.lineweight,n.textStyle=f.textStyle}for(;;){let n=y("356");d&&(n=y("357")),a&&(n=y("358")),i.setMessage(n),i.setKeyWords(`[${y("359")}(A)/${y("208")}(O)/${y("260")}(R)/${y("360")}(P)/${y("361")}(F)/${y("362")}(S)]`);const f=new W;i.clearLastInputPoint();const D=async g=>{if(i.clearLastInputPoint(),i.setMessage(y("363")),i.setKeyWords(`[${y("211")}(Y)/${y("212")}(N)]`),await i.go(),i.isKeyWordPicked("Y")){const m=g.numVerts();for(let x=0;x<m;x++){const u=g.getBulgeAt(x);g.setBulgeAt(x,-u)}}return!0};let P=await i.go();if(i.isKeyWordPicked("A")){const g=new ke;g.setMessage(y("364"));let m=await g.go();if(typeof m!="number")return;if(m<1e-5){I.acutPrintf(`
`+y("365"));return}e=m,localStorage.setItem("mx_revcloud_minArcLength",m.toString());const x=async()=>{g.setMessage(y("366"));let u=await g.go();if(typeof u!="number")return!1;if(u<1e-5){I.acutPrintf(`
`+y("367"));return}if(u<e)return I.acutPrintf(`
`+y("368")),await x();o=u,localStorage.setItem("mx_revcloud_maxArcLength",u.toString())};if(await x()===!1)return;continue}if(i.isKeyWordPicked("O")){const g=new ce;g.AddMcDbEntityTypes("CIRCLE,ARC,LINE,LWPOLYLINE,ELLIPSE");const x=(await F.userSelect(y("205"),g))[0];if(!x)return;const u=x.getMcDbEntity();if(!u)return;if(u instanceof W){f.isClosed=u.isClosed,f.constantWidth=u.constantWidth,s(f,u);const c=u.numVerts();let w;for(let h=0;h<c;h++){const p=u.getPointAt(h).val;w&&p&&Se(w,p,e,o).forEach(A=>{f.addVertexAt(A,r,void 0,l?t:void 0)}),w=p}return f.isClosed?Se(w,u.getPointAt(0).val,e,o).forEach(h=>{f.addVertexAt(h,r,void 0,l?t:void 0)}):f.addVertexAt(w,r),await D(f),u.erase(),$.getCurrentMxCAD().drawEntity(f)}if(u instanceof U)return s(f,u),Se(u.startPoint,u.endPoint,e,o).forEach(c=>{f.addVertexAt(c,r,void 0,l?t:void 0)}),f.addVertexAt(u.endPoint,r,void 0,l?t:void 0),await D(f),u.erase(),$.getCurrentMxCAD().drawEntity(f);if(u instanceof q){const c=u.getLength().val,w=c/Mt(c,e,o),h=u.getStartPoint().val;return mn(u.center,h,w).forEach(p=>{f.addVertexAt(p,r,void 0,l?t:void 0)}),s(f,u),u instanceof q&&(f.isClosed=!0),await D(f),u.erase(),$.getCurrentMxCAD().drawEntity(f)}}if(i.isKeyWordPicked("R")){d=!0,a=!1;continue}if(i.isKeyWordPicked("P")){a=!0,d=!1;continue}if(i.isKeyWordPicked("F")){a=!1,d=!1;continue}if(i.isKeyWordPicked("S")){if(i.setMessage(y("369")),i.setKeyWords(`[${y("370")}(N)/${y("371")}(C)]`),await i.go(),i.getDetailedResult()===S.kEcsIn||i.getDetailedResult()===S.kNewCommadIn||i.getStatus()===N.kNone||i.getStatus()===N.kCancel)return;i.isKeyWordPicked("N")&&(l=!1),i.isKeyWordPicked("C")&&(l=!0);continue}if(!P)return;if(d){i.setMessage(y("350")),i.setMessage("");const g=(x,u)=>{if(!P)return;const[c,w,h,p]=xn(P,u),A=P.x<u.x&&P.y<u.y||P.x>u.x&&P.y>u.y;[[c,w],[w,h],[h,p],[p,c]].forEach(([b,M])=>{Se(b,M,e,o).forEach(C=>{x.addVertexAt(C,A?r:-r,void 0,l?t:void 0)})}),x.isClosed=!0};i.setUserDraw((x,u)=>{const c=new W;g(c,x),u.drawMcDbEntity(c)});const m=await i.go();return m?(g(f,m),$.getCurrentMxCAD().drawEntity(f)):void 0}else if(a){let g=P;const m=[];m.push(g);const x=(u,c)=>{let w;c.forEach(h=>{w&&Se(w,h,e,o).forEach(p=>{u.addVertexAt(p,r,void 0,l?t:void 0)}),w=h}),c.length>2?(u.isClosed=!0,Se(w,c[0],e,o).forEach(h=>{u.addVertexAt(h,r,void 0,l?t:void 0)})):u.addVertexAt(w,r,void 0,l?t:void 0)};for(i.setUserDraw((u,c)=>{const w=new W;x(w,[...m,u]),c.drawMcDbEntity(w)});;){i.setMessage(y("372")),i.setKeyWords(m.length<2?"":`[${y("373")}(U)]`);const u=await i.go();if(i.isKeyWordPicked("U")){m.pop(),i.clearLastInputPoint();continue}if(i.getDetailedResult()===S.kNullEnterIn||i.getDetailedResult()===S.kMouseRightIn)return x(f,m),await D(f),$.getCurrentMxCAD().drawEntity(f);if(!u)return;m.push(u)}}else{f.addVertexAt(P,r,void 0,l?t:void 0);const g=P.clone();let m=0,x=P;const u=async()=>{w(),await D(f),$.getCurrentMxCAD().drawEntity(f)};i.setMessage(y("374")+"..."),i.setKeyWords(""),i.setUserDraw((p,A)=>{P&&(x=p,m=P.distanceTo(p),A.drawMcDbEntity(f.clone()),A.drawLine(P.toVector3(),p.toVector3()))}),i.clearLastInputPoint();let c=!1;const w=Dt(20,async()=>{if(!(m<e)){if(x.distanceTo(g)<e){f.isClosed=!0,I.stopRunCommand(),c=!0;return}f.addVertexAt(x,r,void 0,l?t:void 0),P=x,m=0}}),h=await i.go();if((i.getDetailedResult()===S.kMouseRightIn||i.getDetailedResult()===S.kNullEnterIn)&&await u(),!h&&!c)return w();h&&f.addVertexAt(h,r,void 0,l?t:void 0),await u();break}}}V("_Revcloud",Pn);async function pn(){const t=me(),e=()=>{t&&xe("MxFullScreen")},o=(l,s,n=210,f=297,D=0)=>{let{baseUrl:P="",mxfilepath:g="",printPdfUrl:m=""}=_t()||{},x={width:""+n,height:""+f,roate_angle:D,bd_pt1_x:""+l.x,bd_pt1_y:""+l.y,bd_pt2_x:""+s.x,bd_pt2_y:""+s.y};$.getCurrentMxCAD().saveFileToUrl(m,(u,c)=>{try{let w=JSON.parse(c);if(w.ret=="ok"){let h=P+g+w.file;on(h),e()}else console.log(c)}catch{console.log("Mx: sserverResult error")}},void 0,JSON.stringify(x))};I.acutPrintf(`
`+y("316"));let i=$.getCurrentMxCAD(),r=0,d=!1;for(;;){let l=new j;l.setMessage(`
`+y("317")+":"),l.setKeyWords(`[${y("318")}(S)/[${y("319")}(D)]`),l.disableAllTrace();let s=await l.go();if(l.isKeyWordPicked("D"))d=!0;else if(l.isKeyWordPicked("S")){let n=new Ze;n.setMessage(y("320")),n.setKeyWords(`[A1(A)/A2(B)/A3(C)/A4(D)/${y("321")}16.55x23.90(E)]`),n.setDisableDynInput(!0);const f=await n.go();if(!f)return;let D={A:{w:594,h:841},B:{w:420,h:594},C:{w:297,h:420},D:{w:210,h:297},E:{w:165.5,h:239,nw:130,nh:190}};if(!D[f])return;let P=new Ge;P.setMessage(`${y("322")}(${y("323")})`);let g=await P.go();if(!g)return;var a=parseFloat(g);if(isNaN(a)){I.acutPrintf(y("324"));return}let m=D[f].w*a,x=D[f].h*a,u=0,c=0;if(D[f].nw&&(u=D[f].nw*a),D[f].nh&&(c=D[f].nh*a),d){let A=m;m=x,x=A,A=u,u=c,c=A}l=new j,l.disableAllTrace(),l.setMessage(`
`+y("325")),l.setKeyWords(""),l.setUserDraw((A,b)=>{b.setColor(16711680);let M=new W,C=new z(A.x-m*.5,A.y-x*.5),L=new z(A.x-m*.5,A.y+x*.5),v=new z(A.x+m*.5,A.y+x*.5),E=new z(A.x+m*.5,A.y-x*.5);M.addVertexAt(C),M.addVertexAt(L),M.addVertexAt(v),M.addVertexAt(E),M.constantWidth=I.screenCoordLong2Doc(2),M.isClosed=!0,b.drawMcDbEntity(M);let k=[];if(k.push(C.toVector3()),k.push(L.toVector3()),k.push(v.toVector3()),k.push(E.toVector3()),b.setColor(12868),b.drawSolid(k,.5),u>0&&c>0){let T=new z(A.x-u*.5,A.y-c*.5),R=new z(A.x-u*.5,A.y+c*.5),B=new z(A.x+u*.5,A.y+c*.5),O=new z(A.x+u*.5,A.y-c*.5),_=[];_.push(T.toVector3()),_.push(R.toVector3()),_.push(B.toVector3()),_.push(O.toVector3()),_.push(T.toVector3());let H=i.mxdraw.viewCoordLong2Cad(3),X=Ve.createDashedLines(_,16777215,H*2,H);b.drawEntity(X)}});let w=await l.go();if(!w)return e();let h=new z(w.x-m*.5,w.y-x*.5),p=new z(w.x+m*.5,w.y+x*.5);o(h,p,d?D[f].h:D[f].w,d?D[f].w:D[f].h,r);return}else{if(!s)return e();l.setMessage(`
`+y("326")+":"),l.setUserDraw((f,D)=>{if(!s)return e();D.setColor(16711680);let P=new W;P.addVertexAt(s),P.addVertexAt(new z(s.x,f.y)),P.addVertexAt(f),P.addVertexAt(new z(f.x,s.y)),P.constantWidth=I.screenCoordLong2Doc(2),P.isClosed=!0,D.drawMcDbEntity(P);let g=[];g.push(s.toVector3()),g.push(new THREE.Vector3(s.x,f.y)),g.push(f.toVector3()),g.push(new THREE.Vector3(f.x,s.y)),D.setColor(12868),D.drawSolid(g,.5)}),l.setDisableOsnap(!0),l.setDisableOrthoTrace(!0),l.setDynamicInputType(xt.kXYCoordInput);let n=await l.go();if(!n)return e();o(s,n,d?297:210,d?210:297,r);return}}}V("Plot",pn);async function An(){const t=I.viewCoordLong2Cad(20),e=new j;e.setDisableDynInput(!0),e.setDynamicInputType(xt.kNoInput);let o=await e.go();if(!o)return;const i=new W;let r=0,d=o;const a=Dt(20,()=>{r<t||(i.addVertexAt(d),o=d,r=0)});e.setUserDraw((n,f)=>{o&&(d=n,r=o.distanceTo(n),f.drawMcDbEntity(i.clone()),f.drawLine(o.toVector3(),n.toVector3()))});const l=await e.go();if(!l)return a();a(),i.addVertexAt(l),$.getCurrentMxCAD().drawEntity(i)}V("MxET_Pencil",An);function bn(t){const e=t.requestFullscreen||t.mozRequestFullScreen||t.webkitRequestFullScreen||t.msRequestFullscreen;e?e.call(t):(Ce().error(y("309")+"!"),console.error(y("309")+"!"))}function Dn(){const t=document.exitFullscreen||document.mozCancelFullScreen||document.webkitExitFullscreen||document.msExitFullscreen;t?t.call(document):(Ce().error(y("309")+"!"),console.error(y("309")+"!"))}window.addEventListener("keydown",t=>{t.keyCode===122&&(t.returnValue=!1,xe("MxFullScreen"))},!0);Nt.register({key:{keyCode:"Escape"},when(t){return t.isRunCommand?!1:me()},action(){xe("MxFullScreen")}});V("MxFullScreen",()=>{me()?(Dn(),"keyboard"in navigator&&navigator.keyboard.unlock()):(bn(document.body),"keyboard"in navigator&&navigator.keyboard.lock())});async function Mn(){const t=new ke;t.setMessage(y("指定圆环的内径"));const e=await t.go();if(!e)return;t.setMessage(y("指定圆环的外径"));const o=await t.go();if(!o)return;const i=new j;i.clearLastInputPoint(),i.setMessage(y("指定圆环的中心点")),i.setUserDraw((D,P)=>{const g=new q,m=new q;g.radius=e/2,m.radius=o/2,g.center=D,m.center=D,P.drawMcDbEntity(g),P.drawMcDbEntity(m)});const r=await i.go();if(!r)return;const d=Math.abs(e-o)/4,a=Math.min(e,o),l=r.clone().addvec(Z.kXAxis.clone().mult(a/2+d)),s=r.clone().addvec(Z.kXAxis.clone().negate().mult(a/2+d)),n=new W,f=1;n.addVertexAt(l,f),n.addVertexAt(s,f),n.addVertexAt(l,f),n.isClosed=!0,n.constantWidth=d*2,$.getCurrentMxCAD().drawEntity(n)}V("_donut",Mn);function Ne(t,e,o=0){const i=t.numVerts();for(let r=0;r<t.numVerts();r++){const d=t.getPointAt(r).val;let a=t.getPointAt(r+1).val;if(t.getBulgeAt(r)===0){if(r+1===i&&t.isClosed&&(a=t.getPointAt(0).val),a&&_e(e,d,a)<o)return{start:d,end:a,startIndex:r,endIndex:r+1}}else if(a){const s=t.getParamAtPoint(d).val,n=t.getParamAtPoint(a).val,f=t.getParamAtPoint(e).val;if(n>f&&f>s)return{start:d,end:a,startIndex:r,endIndex:r+1}}}if(t.isClosed){const r=t.getPointAt(0).val,d=t.getPointAt(t.numVerts()-1).val;if(_e(e,d,r)<o)return{start:d,end:r,startIndex:t.numVerts()-1,endIndex:0,isClosed:!0}}}async function Cn(){const t=$.getCurrentMxCAD(),e=new Q;let o,i,r,d,a,l;const s=new ce;s.AddMcDbEntityTypes("CIRCLE,ARC,LINE,LWPOLYLINE");let n,f;const D=(u,c)=>{n=u;const w=F.findEntAtPoint(u.x,u.y,u.z,-1,s);if(!(w&&w.isValid()))return;const h=w.getMcDbEntity();h&&(f&&f.highlight(!1),h.highlight(!0),f=h)};for(;;){e.setMessage(`${y("238")}${y("284")}、${y("268")}、${y("283")}${y("327")} <${y("328")}>`),e.setUserDraw(D),e.setFilter(s);const c=(await e.go()).getMcDbEntity();if(f&&f.highlight(!1),e.getStatus()===N.kCancel)return;if(e.getStatus()===N.kNone){const w=new j;w.clearLastInputPoint(),w.setMessage(y("329"));const h=await w.go();if(!h)return;w.setBasePt(h),w.setMessage(y("330"));const p=await w.go();if(!p)return;w.setMessage(y("331"));const A=await w.go();if(!A)return;o=h.x,i=h.y,r=p.x,d=p.y,a=A.x,l=A.y;break}else if(c instanceof ee){const w=c.getStartPoint().val,h=c.radius,p=Z.kXAxis.clone().rotateBy(c.endAngle).mult(h),A=c.center,b=A.clone().addvec(p);if(!w||!b)return;r=w.x,d=w.y,a=b.x,l=b.y,o=A.x,i=A.y;break}else if(c instanceof q){const w=c.center;o=w.x,i=w.y,r=n.x,d=n.y;const h=new j;h.setMessage(y("331"));const p=await h.go();if(!p)return;const A=c.getClosestPointTo(p,!1).val;a=A.x,l=A.y;break}else if(c instanceof U||c instanceof W){const w=new Q,h=new ce;h.AddMcDbEntityTypes("LINE,LWPOLYLINE"),w.setFilter(h);let p;f=null,w.setUserDraw((M,C)=>{D(M),p=M});const A=c.getClosestPointTo(n,!1).val;let b;if(r=A.x,d=A.y,c instanceof U)b=c,b.highlight(!0);else{const{start:M,end:C}=Ne(c,A,2)||{};M&&C&&(b=new U(M,C)),c.highlight(!1)}if(!b)return;for(;;){w.setMessage(y("332"));const M=await w.go();if(w.getStatus()===N.kCancel)return b.highlight(!1);if(!M||!M.isValid())continue;let C=M.getMcDbEntity(),L;if(C instanceof W){const{start:T,end:R}=Ne(C,w.getDocPickPoint(),2)||{};L=new U(T,R),C.highlight(!1)}else if(C instanceof U)L=C;else{I.acutPrintf(`
`+y("333"));continue}if(!L)return b.highlight(!1);b.highlight(!1),L.highlight(!1);const v=b.clone()?.IntersectWith(L.clone(),Re.Intersect.kExtendBoth);if(v.isEmpty())continue;const E=v.at(0),k=L.getClosestPointTo(p,!1).val;a=k.x,l=k.y,o=E.x,i=E.y;break}break}}const P=new j;P.clearLastInputPoint();let g,m,x;if(![o,i,r,d,a,l].some(u=>typeof u!="number"))for(;;){P.setMessage(y("334")),P.setKeyWords(`[${y("285")}(T)/${y("281")}(A)/${y("335")}(Q)]`),P.setUserDraw((w,h)=>{let p=new Yt;p.compute(o,i,r,d,a,l,w.x,w.y),m&&(p.dimensionText=m),x&&(p.textRotation=x),h.drawMcDbEntity(p,!0)});const u=await P.go();if(P.isKeyWordPicked("T")){const w=new Ge;w.clearLastInputPoint(),w.setMessage(`${y("336")}${y("337")}<${m||g.getMcDbDimension()?.dimensionText||""}>`),w.setKeyWords("");const h=await w.go();if(typeof h!="string")return;m=h;continue}if(P.isKeyWordPicked("A")){const w=new ae;w.clearLastInputPoint(),w.setMessage(y("338"));const h=await w.go();if(!h||w.getStatus()===N.kCancel)return;w.getDetailedResult()===S.kCoordIn?x=h*(Math.PI/180):x=h;continue}if(P.isKeyWordPicked("Q"),!u)return;t.drawUseDefaultProperties=!0,g=t.drawDimAngular(o,i,r,d,a,l,u.x,u.y);const c=g.getMcDbDimension();if(!c)return;m&&(c.dimensionText=m),x&&(c.textRotation=x);return}}V("_dimangular",Cn);function kn(t,e,o){const{x:i,y:r}=e,{x:d,y:a}=o,{x:l,y:s}=t,n=Math.min(i,d),f=Math.min(r,a),D=Math.max(i,d),P=Math.max(r,a);return l>=n&&l<=D&&s>=f&&s<=P}function Qe(t,e,o,i,r){const d=e.sub(t),a=o.distanceTo(i)===0,l=o.x>i.x;r.forEach(s=>{if(s)if(l&&!a&&!(s instanceof q)&&!(s instanceof Ke&&(s.startAngle===0&&s.endAngle===Math.PI*2||s.endAngle===0&&s.startAngle===Math.PI*2))){const n=s.getGripPoints();if(n.isEmpty())return;n.forEach((f,D)=>{if(kn(f,o,i)&&!(s instanceof ee&&D===2)&&!(s instanceof U&&D===2)){if(s instanceof W&&n.length()!==s.numVerts()){const P=s.numVerts();let g=0;for(let m=0;m<P;m++){if(s.getPointAt(m).val,s.getBulgeAt(m)!==0&&(g++,g===D))return;g++}}s.moveGripPointsAt(D,d.x,d.y,d.z)}})}else s.move(t,e)})}const Be=tt(0,"_stretch_offsetX"),He=tt(0,"_stretch_offsetY"),Ue=tt(0,"_stretch_offsetZ");async function En(){const t=new qe;let e=F.getCurrentSelect(),o,i;if(e.length<=0){if(!await t.userSelect(y("376")))return;e=t.getIds();const n=t.getSelectPoint();o=n.pt1,i=n.pt2}else{const{point1:n,point2:f}=F.getCurrentSelectPoints();o=n,i=f}const r=new j;r.setMessage(`
`+y("377")),r.setKeyWords(`[${y("378")}(D)]`);const d=await r.go(),a=async n=>{if(!(typeof n>"u"&&(r.setMessage(`
${y("379")}<${Be.value}, ${He.value}, ${Ue.value}>`),r.setKeyWords(""),r.clearLastInputPoint(),n=await r.go(),!n))){if(n){const{x:f,y:D,z:P}=n;Be.value=f,He.value=D,Ue.value=P}else{if(Be.value===0&&He.value===0&&Ue.value===0)return;n=new z(Be.value,He.value,Ue.value)}Qe(new z,n,o,i,e.map(f=>f.getMcDbEntity()))}};if(r.isKeyWordPicked("D")||r.getStatus()===N.kNone)return await a();if(!d)return;r.setMessage(`
${y("380")}${y("327")}<${y("381")}>`),r.setBasePt(d),r.setKeyWords(""),r.setUserDraw((n,f)=>{const D=e.map(P=>P.clone());Qe(d,n,o,i,D),D.forEach((P,g)=>{const m=e[g]?.getMcDbEntity();if(m){const x=m.trueColor.getColorValue(m.layerId);f.setColor(Number(x))}f.drawMcDbEntity(P)})});let l=await r.go();if(r.getStatus()===N.kNone)return await a(d);if(!l)return;const s=e.map(n=>n.getMcDbEntity()).filter(n=>!!n);Qe(d,l,o,i,s)}V("_stretch",En);class vn{constructor(e){G(this,"resizeObserver",null);G(this,"mutationObserver",null);G(this,"element",null);this.callback=e}addListener(e){this.element=e,this.setupResizeObserver(),this.setupMutationObserver()}removeListener(){this.resizeObserver&&(this.resizeObserver.unobserve(this.element),this.resizeObserver.disconnect(),this.resizeObserver=null),this.mutationObserver&&(this.mutationObserver.disconnect(),this.mutationObserver=null),this.element=null}setupResizeObserver(){typeof ResizeObserver<"u"&&(this.resizeObserver=new ResizeObserver(this.callback),this.resizeObserver.observe(this.element))}setupMutationObserver(){typeof MutationObserver<"u"&&(this.mutationObserver=new MutationObserver(this.callback),this.mutationObserver.observe(this.element,{attributes:!0}))}}function In(t,e){const o=new vn(e);return o.addListener(t),o}function Ln(t){t.removeListener()}function Sn(t,e){let o;return function(...i){clearTimeout(o),o=setTimeout(()=>t(...i),e)}}function Tn(t,e,o){typeof e=="string"&&(e=parseFloat(e));const i=document.createElement("canvas"),r=i.getContext("2d");if(!r)return;r.font=e+"px "+o;let a=r.measureText(t).width,s=e*1.2;return i.remove(),a=Math.ceil(a),s=Math.ceil(s),{width:a,height:s}}function Rn(t){const e=document.createElement("template");e.innerHTML=t.trim();const o=document.createElement("div");o.style.position="absolute",o.style.visibility="hidden",o.style.pointerEvents="none",o.appendChild(e.content.cloneNode(!0)),document.body.appendChild(o);let{width:i,height:r}=o.getBoundingClientRect();return i=Math.ceil(i)+5,r=Math.ceil(r),document.body.removeChild(o),{width:i,height:r}}function Vn(t){let e={watermark_txt:y("382"),watermark_html:"",watermark_x:20,watermark_y:20,watermark_rows:200,watermark_cols:200,watermark_x_space:80,watermark_y_space:80,watermark_color:"#aaa",watermark_alpha:.4,watermark_fontsize:"15px",watermark_font:"微软雅黑",watermark_width:"auto",watermark_height:"auto",watermark_angle:15,watermark_className:"mx_mask_div"};e={...e,...t};const o=$.getCurrentMxCAD().mxdraw.getCanvas();let i=null,r=null,d;const a=()=>{document.querySelectorAll("."+e.watermark_className).forEach(f=>f.remove())},l=()=>{a(),d&&Ln(d)},s=()=>{const n=document.createDocumentFragment(),f=o.parentElement,D=f.clientWidth,P=f.clientHeight;if(D===i&&P===r)return;i=D,r=P,a();const g=Math.max(f.scrollWidth,f.clientWidth),m=Math.max(f.scrollHeight,f.clientHeight);if(e.watermark_width==="auto"||e.watermark_height==="auto")if(e.watermark_html!==""){const{width:w,height:h}=Rn(e.watermark_html);e.watermark_width==="auto"&&(e.watermark_width=w),e.watermark_height==="auto"&&(e.watermark_height=h)}else{const w=e.watermark_txt.length,h=parseFloat(e.watermark_fontsize)||16,{width:p=w*h,height:A=h+e.watermark_x_space+e.watermark_y_space}=Tn(e.watermark_txt,e.watermark_fontsize,e.watermark_font)||{};e.watermark_width==="auto"&&(e.watermark_width=p),e.watermark_height==="auto"&&(e.watermark_height=A)}e.watermark_cols=Math.ceil(g/(e.watermark_x_space+e.watermark_width)),e.watermark_rows=Math.ceil(m/(e.watermark_y_space+e.watermark_height));let x,u;for(let w=0;w<e.watermark_rows;w++){u=e.watermark_y+(e.watermark_y_space+e.watermark_height)*w;for(let h=0;h<e.watermark_cols;h++){x=e.watermark_x+(e.watermark_width+e.watermark_x_space)*h;var c=document.createElement("div");c.id=(e.watermark_className||"")+w+h,c.className=e.watermark_className||"",e.watermark_html!==""?c.innerHTML=e.watermark_html:c.appendChild(document.createTextNode(e.watermark_txt)),c.style.webkitTransform="rotate(-"+e.watermark_angle+"deg)",c.style.MozTransform="rotate(-"+e.watermark_angle+"deg)",c.style.msTransform="rotate(-"+e.watermark_angle+"deg)",c.style.OTransform="rotate(-"+e.watermark_angle+"deg)",c.style.transform="rotate(-"+e.watermark_angle+"deg)",c.style.visibility="",c.style.position="absolute",c.style.left=x+"px",c.style.top=u+"px",c.style.overflow="hidden",c.style.zIndex="9999",c.style.pointerEvents="none",c.style.opacity=e.watermark_alpha.toString(),c.style.fontSize=e.watermark_fontsize,c.style.fontFamily=e.watermark_font,c.style.color=e.watermark_color,c.style.textAlign="center",c.style.width=e.watermark_width+"px",c.style.height=e.watermark_height+"px",c.style.display="block",n.appendChild(c)}}f.appendChild(n)};return s(),d=In(o,Sn(s,200)),l}V("_watermark",Vn);async function _n(){const t=$.getCurrentMxCAD();for(;;){const e=new Q;e.setMessage(y("349"));const o=await e.go();if(e.getStatus()===N.kCancel||e.getStatus()===N.kNone)return;const i=o.getMcDbEntity();if(i instanceof ee||i instanceof q){const r=new j;r.setMessage(y("350"));const d=await r.go();if(r.getStatus()===N.kCancel||r.getStatus()===N.kNone)return;if(d){const a=i.center.clone(),l=d.sub(a).normalize().mult(i.radius),s=a.clone().addvec(l),n=a.clone().addvec(l.clone().negate()),f=Math.min(n.distanceTo(d),s.distanceTo(d));t.drawDimDiametric(s.x,s.y,n.x,n.y,f)}break}else{I.acutPrintf(y("351"));continue}}}V("_DrawDiametricDimension",_n);async function Nn(){const t=$.getCurrentMxCAD();for(;;){const e=new Q;e.setMessage(y("选择圆弧或者圆"));const o=await e.go();if(e.getStatus()===N.kCancel||e.getStatus()===N.kNone)return;const i=o.getMcDbEntity();if(i instanceof ee||i instanceof q){const r=i.center.clone(),d=Z.kXAxis.clone().mult(i.radius),a=r.clone().addvec(d);t.drawDimDiametric(r.x,r.y,a.x,a.y,i.radius);break}else{I.acutPrintf(y("所选对象不是圆弧或圆"));continue}}}V("_DrawRadialDimension",Nn);function Ct(t){return new Promise(e=>{const{hideLoading:o,showLoading:i}=ft();try{let r=Ce().info(y("440")+"...");const d=$.App.getCurrentMxCAD().openWebFile(t,a=>{a===0?(Ce().success(y("441")),e(!0)):(Ce().error(y("307")),e(!1)),o(),r()});gt(t).then(a=>{a/(1024*1024)>1&&d&&i()}),e(d)}catch{o(),e(!1)}})}function Wn(t){Ct(t)}V("_openMxweb",Wn);let at=!1;Wt(()=>{if(at)return;let t;const e=[],o=$.getCurrentMxCAD().mxdraw.getOrbitControls();o.addEventListener("change",()=>{clearTimeout(t),t=setTimeout(function(){const r=o.object.position.clone(),d=o.object.zoom,a=o.target.clone(),l=$.getCurrentMxCAD().mxdraw.getCamera();e.push({zoom:d,position:r,target:a,camera:l.clone(!1)})},500)});const i=r=>{if(!r)return!1;const d=$.getCurrentMxCAD().mxdraw,a=d.getOrbitControls(),l=a.object.position.clone(),s=a.object.zoom,n=a.target.clone(),f=$.getCurrentMxCAD().mxdraw.getCamera();return f.copy(r.camera,!1),f.updateProjectionMatrix(),s===r.zoom&&l.equals(r.position)&&n.equals(r.target)?i(e.pop()):(a.object.position.copy(r.position),a.object.zoom=r.zoom,a.target.copy(r.target),a.update(),d._mxdrawObj.mcObject.updateDisplayMatrixData(),!0)};V("Mx_WindowZoom",async()=>{let r=new j;r.setMessage(`
`+y("392")),r.setKeyWords(`[${y("265")}(A)/${y("236")}(E)/${y("275")}(P)/${y("208")}(O)]`);let d=$.getCurrentMxCAD(),a=await r.go();if(r.isKeyWordPicked("A"))return d.mxdraw.zoomInitialStates();if(r.isKeyWordPicked("E"))return d.zoomAll(!0);if(r.isKeyWordPicked("P")){i(e.pop())||I.acutPrintf(`
`+y("393")+`
`+y("394"));return}if(r.isKeyWordPicked("O")){const s=await F.userSelect(`ZOOM ${y("205")}`);if(s.length<=0)return;const n=F.getMcDbEntitysBoundingBox(s);if(!n)return;const{minPt:f,maxPt:D}=n;return d.zoomW(f,D)}if(a==null)return;r.setUserDraw((s,n)=>{n.drawRect(rt.cad2doc1(a),rt.cad2doc1(s))}),r.setKeyWords("");let l=await r.go();l!=null&&d.zoomW(a,l)}),at=!0});class Ee extends ge{constructor(){super(...arguments);G(this,"dDashArray",.03);G(this,"dDashRatio",.1);G(this,"isAligned",!1);G(this,"fixedSize",!0);G(this,"referenceAxis",new THREE.Vector3(0,1,1));G(this,"offsetHeight");G(this,"isDrawsMallRound",!0)}getTypeName(){return"MxAuxiliaryLine"}create(){return new Ee}onViewChange(){return this.fixedSize?(this.setNeedUpdateDisplay(!1),!0):!1}worldDraw(o){const{dDashArray:i,dDashRatio:r}=this.getDash();let d=o.getMxObject();o.setLineWidth(.7),o.setDash(i,r);let a=this.toSmallcoord(d,this.pt1),l=this.toSmallcoord(d,this.pt2),s=a,n=l;typeof this.offsetHeight>"u"&&(this.offsetHeight=d.screenCoordLong2World(40));const f=this.offsetHeight;if(this.isAligned){a.x>l.x&&(a=this.pt2,l=this.pt1);const g=l.clone().sub(a).cross(this.referenceAxis.clone().normalize()).normalize();s=a.clone().add(g.clone().multiplyScalar(f)),n=l.clone().add(g.clone().multiplyScalar(f))}else{const g=Math.abs(this.referenceAxis.y?a.y-l.y:a.x-l.x)+f;let m=f,x=g;(this.referenceAxis.x!==0&&a.x>l.x||this.referenceAxis.y!==0&&a.y>l.y)&&(a=this.pt2,l=this.pt1),this.referenceAxis.x>0&&(m=g,x=f),this.referenceAxis.y>0&&(m=g,x=f),s=a.clone().add(this.referenceAxis.clone().multiplyScalar(m)),n=l.clone().add(this.referenceAxis.clone().multiplyScalar(x))}s.setZ(a.z),n.setZ(l.z);const D=Ve.createDashedLines([a,s,n,l],8421504,.1,.1);if(o.drawEntity(D),this.isDrawsMallRound){const g=d.screenCoordLong2World(1);o.drawCircle(a,g),o.drawCircle(l,g),o.drawCircle(s,g),o.drawCircle(n,g)}const P=s.distanceTo(n).toFixed(3);if(o.getType()===Pt.kWorldDraw){const g=new pt;g.opacity=1,g.text=P,g.backgroundColor=16777185,g.color=16,g.position=new THREE.Vector3((s.x+n.x)/2,(s.y+n.y)/2),g.setFontSize(36),g.height=d.screenCoordLong2World(16),g.setLineWidthByPixels(!0),g.setUseSmallcoordDisplay(this.use_smallcoord_display),g.worldDraw(o)}else{const g=Ve.creatTextSprite(P,new THREE.Vector3((s.x+n.x)/2,(s.y+n.y)/2),d.screenCoordLong2World(16),0,16777215);g&&o.drawEntity(g)}}getGripPoints(){return[this.pt1,this.pt2]}moveGripPointsAt(o,i){return o===0&&this.pt1.add(i),o===1&&this.pt2.add(i),!0}dwgIn(o){return super.dwgIn(o),this.dwgInHelp(o,["referenceAxis","isAligned","offsetHeight","fixedSize","isDrawsMallRound"]),!0}dwgOut(o){return o=super.dwgOut(o),this.dwgOutHelp(o,["referenceAxis","isAligned","offsetHeight","fixedSize","isDrawsMallRound"]),o}}class we extends tn{constructor(){super(...arguments);G(this,"dDashArray",.03);G(this,"dDashRatio",.1);G(this,"pt1");G(this,"pt2");G(this,"center");G(this,"fixedSize",!0);G(this,"offsetDist");G(this,"isClockwise","auto");G(this,"isDrawsMallRound",!0);G(this,"isMaxRadius",!1);G(this,"radius")}getTypeName(){return"MxAuxiliaryArc"}create(){return new we}onViewChange(){return this.fixedSize?(this.setNeedUpdateDisplay(!1),!0):!1}calculateLineAngle(o,i,r){const d=Math.PI*2/360;let a=Math.atan2(i.y-o.y,i.x-o.x)*180/Math.PI*d,l=Math.atan2(r.y-o.y,r.x-o.x)*180/Math.PI*d;return{startAngle:a,endAngle:l}}getClockwise(){const o=this.getStartPoint(),i=this.getEndPoint();let r=this.isClockwise;if(r==="auto"){const d=o.clone().sub(this.center),a=i.clone().sub(this.center);r=d.x*a.y-d.y*a.x<0}return r}getAngle(){const o=this.center,i=this.getStartPoint(),r=this.getEndPoint();let d=this.getClockwise();const{startAngle:a,endAngle:l}=this.calculateLineAngle(o,i,r);let s=l-a;return!d&&s<0?s+=2*Math.PI:d&&s>0&&(s-=2*Math.PI),Math.abs(s)}getRadius(o=!1){return this.radius?this.radius+(o&&this.offsetDist||0):this.isMaxRadius?Math.max(this.center.distanceTo(this.pt1),this.center.distanceTo(this.pt2)):this.center.distanceTo(this.pt2)+(o&&this.offsetDist||0)}getStartPoint(o=I.getCurrentDraw()){let i=this.pt1;this.pt2;let r=this.center,d=i;typeof this.offsetDist>"u"&&(this.offsetDist=o.screenCoordLong2World(40));const a=this.offsetDist,l=i.clone().sub(r).normalize(),s=this.getRadius()+a;return d=r.clone().add(l.multiplyScalar(s)),d.setZ(i.z),d}getEndPoint(o=I.getCurrentDraw()){let i=this.pt2,r=this.center,d=i;typeof this.offsetDist>"u"&&(this.offsetDist=o.screenCoordLong2World(40));const a=this.offsetDist,l=i.clone().sub(r).normalize(),s=this.getRadius()+a;return d=r.clone().add(l.multiplyScalar(s)),d.setZ(i.z),d}worldDraw(o){const{dDashArray:i,dDashRatio:r}=this.getDash();let d=o.getMxObject();o.setDash(i,r),o.setLineWidth(.7),this.toSmallcoord(d,this.pt2);let a=this.center;typeof this.offsetDist>"u"&&(this.offsetDist=d.screenCoordLong2World(40));const l=this.offsetDist,s=this.getStartPoint(),n=this.getEndPoint(),f=this.getRadius()+l,{startAngle:D,endAngle:P}=this.calculateLineAngle(a,s,n);let g=this.getClockwise();const m=new THREE.ArcCurve(a.x,a.y,f,D,P,g),x=new THREE.Geometry().setFromPoints(m.getPoints(50)),u=Ve.createDashedLines([s,a,n],8421504,.1,.1);if(o.setColor(8421504),o.drawEntity(u),o.drawGeometryLines(x),this.isDrawsMallRound){const b=d.screenCoordLong2World(2);o.drawCircle(s,b),o.drawCircle(n,b)}const c=this.getAngle(),w=THREE.MathUtils.radToDeg(c).toFixed(3)+"°",{x:h,y:p}=m.getPointAt(.5),A=new THREE.Vector3(h,p);if(o.getType()===Pt.kWorldDraw){const b=new pt;b.opacity=1,b.text=w,b.backgroundColor=16777185,b.color=16,b.position=A,b.setFontSize(36),b.height=d.screenCoordLong2World(16),b.setLineWidthByPixels(!0),b.setUseSmallcoordDisplay(this.use_smallcoord_display),b.worldDraw(o)}else{const b=Ve.creatTextSprite(w,A,d.screenCoordLong2World(16),0,16777215);b&&o.drawEntity(b)}}getGripPoints(){return[this.pt1,this.pt2,this.center]}moveGripPointsAt(o,i){return o===0&&this.pt1.add(i),o===1&&this.pt2.add(i),o===2&&this.center.add(i),!0}dwgIn(o){return this.dwgInHelp(o,["fixedSize","isDrawsMallRound","offsetDist","pt1","pt2","center","isClockwise","isMaxRadius","radius"]),!0}dwgOut(o){return this.dwgOutHelp(o,["fixedSize","isDrawsMallRound","offsetDist","pt1","pt2","center","isClockwise","isMaxRadius","radius"]),o}}function $e(t,e,o){if(t.isEqualTo(e))return 0;let i=t.c().addvec(e.c().sub(t).mult(.5)),r=e.c().sub(t);r.rotateBy(Math.PI/2,Z.kZAxis);let d=new U(i,i.c().addvec(r)),a=o.c();a.rotateBy(Math.PI/2,Z.kZAxis);let l=new U(t,t.c().addvec(a)),s=d.IntersectWith(l,Re.Intersect.kExtendBoth);if(s.isEmpty())return 0;let n=s.at(0),f=n.distanceTo(t);r.normalize(),r.mult(f);let D=n.c().addvec(r),P=n.c().subvec(r),g=D.c().sub(t),m=P.c().sub(t),x=D;return g.angleTo1(o)>m.angleTo1(o)&&(x=P),F.calcBulge(t,x,e).val}const kt=async(t,e)=>{let o=!0;const i=new j;let r=!1;const d=g=>{g.key==="Control"&&(r=!0)},a=$.getCurrentMxCAD(),l=()=>r=!1;window.addEventListener("keydown",d),window.addEventListener("keyup",l);const s=()=>{window.removeEventListener("keydown",d),window.removeEventListener("keyup",l)};if(!t){i.clearLastInputPoint(),i.setMessage(y("395"));const g=await i.go();if(i.getDetailedResult()===S.kNewCommadIn)return s(),!1;if(!g)return s();t=g}const n=[{point:t}];for(;;)if(o){i.setMessage(y("396")),i.setKeyWords(`[${y("284")}(A)/${n.length>2?y("397")+"(C)/":""}${y("373")}(U)/${y("398")}(T)]`),i.setUserDraw((m,x)=>{const u=new W;n.forEach(({point:c,bulge:w})=>{u.addVertexAt(c,w)}),u.addVertexAt(m),x.drawMcDbEntity(u)});const g=await i.go();if(i.getDetailedResult()===S.kNullEnterIn||i.getDetailedResult()===S.kNullSpaceIn){const m=new W;n.forEach(({point:c,bulge:w})=>{m.addVertexAt(c,w)});const x=m.getLength().val,u=a.drawEntity(m);return e&&e(n),{dist:x,pl:m,plId:u}}if(i.getDetailedResult()===S.kNewCommadIn)return s(),!1;if(i.getStatus()===N.kCancel||i.getStatus()===N.kNone)return s();if(i.isKeyWordPicked("A")){o=!1;continue}else if(i.isKeyWordPicked("U")){n.length===0?I.acutPrintf(y("399")):(n.pop(),n[n.length-1]?.point&&i.setLastInputPoint(n[n.length-1].point),e&&e(n));continue}else if(i.isKeyWordPicked("T")){const m=new W;n.forEach(({point:c,bulge:w})=>{m.addVertexAt(c,w)});const x=m.getLength().val,u=a.drawEntity(m);return e&&e(n),{dist:x,pl:m,plId:u}}else if(i.isKeyWordPicked("C")){const m=new W;m.isClosed=!0,n.forEach(({point:c,bulge:w})=>{m.addVertexAt(c,w)});const x=m.getLength().val;e&&e(n);const u=a.drawEntity(m);return{dist:x,pl:m,plId:u}}else if(g){n.push({point:g}),e&&e(n);continue}}else{const g=i;g.setMessage(ye("NO1_ID_ARX_PL5",`${y("400")}(${y("401")})`)),g.setKeyWords(`[${y("281")}(A)/${y("402")}(CE)/${n.length>2?y("397")+"(C)/":""}${y("247")}(D)/${y("283")}(L)/${y("280")}(R)/${y("403")}(S)/${y("373")}(U)]`);let m=new Z;if(n.length<2)m.copy(Z.kXAxis);else{let c=n.length,w=n[c-2].point,h=n[c-2].bulge,p=n[c-1].point;if(!h||Math.abs(h)<1e-7)m=new z(p.x,p.y,0).sub(new z(w.x,w.y,0));else{let A=new W;A.addVertexAt(w,h),A.addVertexAt(p);let b=A.getFirstDeriv(new z(p.x,p.y,0));b.ret?m=b.val:m.copy(Z.kXAxis)}}let x=n[n.length-1];g.setUserDraw((c,w)=>{let h=$e(x.point,c,r?m.clone().negate():m),p=new W;n.forEach(({point:A,bulge:b})=>{p.addVertexAt(A,b)}),p.addVertexAt(x.point,h),p.addVertexAt(c),w.drawMcDbEntity(p)});let u=await g.go();if(g.getDetailedResult()===S.kNullEnterIn||g.getDetailedResult()===S.kNullSpaceIn){const c=new W;n.forEach(({point:p,bulge:A})=>{c.addVertexAt(p,A)});const w=c.getLength().val,h=a.drawEntity(c);return e&&e(n),{dist:w,pl:c,plId:h}}if(u!==null){let c={point:u,bulge:0};n[n.length-1].bulge=$e(x.point,c.point,r?m.clone().negate():m),n.push(c),e&&e(n)}else if(g.getStatus()==N.kKeyWord){if(g.isKeyWordPicked("A")){const c=new ae;c.setBasePt(x.point),c.setMessage(ye("ID_ARX_PLGET_I_ANGLE",y("404")));const w=await c.go();if(c.getDetailedResult()===S.kNewCommadIn)return s(),!1;if(w===null)break;g.setMessage(`${y("400")}(${y("401")})`),g.setKeyWords(`[${y("402")}(CE)/${y("280")}(R)]`),g.setLastInputPoint(x.point);const h=n[n.length-1].point,p=M=>{const C=Math.PI/2-w/2,L=new z((M.x+h.x)/2,(M.y+h.y)/2),v=L.distanceTo(h),E=v/Math.sin(C),k=v/Math.tan(C),T=L.sub(h).rotateBy(Math.PI/2).normalize().mult(r?-E-k:E-k),R=L.addvec(T);return F.calcBulge(h,R,M).val};g.setUserDraw((M,C)=>{const L=p(M);let v=new W;n.forEach(({point:E,bulge:k})=>{v.addVertexAt(E,k)}),v.addVertexAt(x.point,L),v.addVertexAt(M),C.drawMcDbEntity(v)});const A=await g.go();if(g.isKeyWordPicked("CE")){g.setMessage(y("405")),g.setKeyWords(""),g.setBasePt(h),g.setUserDraw((E,k)=>{const T=E.distanceTo(h),R=E.addvec(E.sub(h).rotateBy(w).normalize().mult(T)),B=p(R);let O=new W;n.forEach(({point:_,bulge:H})=>{O.addVertexAt(_,H)}),O.addVertexAt(x.point,B),O.addVertexAt(R),k.drawMcDbEntity(O)});const M=await g.go();if(g.getDetailedResult()===S.kNewCommadIn)return s(),!1;if(!M)break;const C=M.distanceTo(h),L=M.addvec(M.sub(h).rotateBy(w).normalize().mult(C));let v={};v.bulge=0,v.point=L,n[n.length-1].bulge=p(L),n.push(v),g.setLastInputPoint(L),e&&e(n);continue}if(g.isKeyWordPicked("R")){const M=new ke;M.setMessage(y("406")),M.setKeyWords("");const C=await M.go();if(M.getDetailedResult()===S.kNewCommadIn)return s(),!1;if(typeof C!="number")break;const L=C*Math.sin(w/2);g.setBasePt(h),g.setMessage(`${y("407")}(${y("408")})`),g.setKeyWords(""),g.setUserDraw((T,R)=>{const B=h.clone().addvec(T.sub(h).normalize().mult(L*2)),O=p(B);let _=new W;n.forEach(({point:H,bulge:X})=>{_.addVertexAt(H,X)}),_.addVertexAt(x.point,O),_.addVertexAt(B),R.drawMcDbEntity(_)});const v=await g.go();if(g.getDetailedResult()===S.kNewCommadIn)return s(),!1;if(!v)break;const E=h.clone().addvec(v.sub(h).normalize().mult(L*2));let k={};k.bulge=0,k.point=E,n[n.length-1].bulge=p(E),n.push(k),e&&e(n);continue}if(!A)break;let b={};b.bulge=0,b.point=A,n[n.length-1].bulge=p(A),n.push(b),e&&e(n)}if(g.isKeyWordPicked("CE")){g.setMessage(y("405")),g.setKeyWords(""),g.setUserDraw(()=>{});const c=await g.go();if(g.getDetailedResult()===S.kNewCommadIn)return s(),!1;if(!c)return s();const w=x.point,h=c.distanceTo(w);g.setLastInputPoint(w),g.setMessage(`${y("400")}(${y("401")})`),g.setKeyWords(`[${y("281")}(A)/${y("409")}(L)]`),g.clearLastInputPoint(),g.setUserDraw((k,T)=>{T.drawLine(k.toVector3(),c.toVector3());const R=c.clone().addvec(k.sub(c).normalize().mult(h)),B=c.sub(w).angleTo2(c.sub(R),Z.kZAxis),O=new z((w.x+R.x)/2,(w.y+R.y)/2),_=c.sub(O).normalize().mult(-h),H=c.clone().addvec((r?B<Math.PI:B>Math.PI)?_.negate():_),X=F.calcBulge(w,H,R).val;let Y=new W;n.forEach(({point:te,bulge:le})=>{Y.addVertexAt(te,le)}),Y.addVertexAt(x.point,X),Y.addVertexAt(R),T.drawMcDbEntity(Y)});const p=await g.go();if(g.isKeyWordPicked("A")){const k=new ae;k.setBasePt(c),k.setMessage(ye("ID_ARX_PLGET_I_ANGLE",`${y("404")}(${y("401")})`)),k.setKeyWords(""),k.setUserDraw((te,le)=>{const se=Z.kXAxis.clone().angleTo2(te.sub(c),Z.kZAxis),ie=r?Math.PI*2-se/2:se/2,fe=w.sub(c).rotateBy(ie).normalize().mult(h),re=w.sub(c).rotateBy(se).normalize().mult(h),Pe=c.clone().addvec(fe),J=c.clone().addvec(re),pe=F.calcBulge(w,Pe,J).val;let de=new W;n.forEach(({point:Ae,bulge:be})=>{de.addVertexAt(Ae,be)}),de.addVertexAt(x.point,pe),de.addVertexAt(J),le.drawMcDbEntity(de)});const T=await k.go();if(k.getDetailedResult()===S.kNewCommadIn)return s(),!1;if(T===null)break;const R=r?Math.PI*2-T/2:T/2,B=w.sub(c).rotateBy(R).normalize().mult(h),O=w.sub(c).rotateBy(T).normalize().mult(h),_=c.clone().addvec(B),H=c.clone().addvec(O),X=F.calcBulge(w,_,H).val;let Y={};Y.bulge=0,Y.point=H,n[n.length-1].bulge=X,n.push(Y),new W,e&&e(n);continue}if(g.isKeyWordPicked("L")){const k=new ke;k.setMessage(`${y("410")}(${y("401")})`),k.setKeyWords(""),k.setBasePt(w),k.setUserDraw((Y,te)=>{const le=Y.distanceTo(w);if(le>h*2)return;const se=Math.asin(le/2/h)*2,ie=w.sub(c).normalize().mult(h),fe=c.clone().addvec(ie.clone().rotateBy(r?Math.PI-se/2:se/2)),re=c.clone().addvec(ie.clone().rotateBy(se)),Pe=F.calcBulge(w,fe,re).val;let J=new W;n.forEach(({point:pe,bulge:de})=>{J.addVertexAt(pe,de)}),J.addVertexAt(x.point,Pe),J.addVertexAt(re),te.drawMcDbEntity(J)});const T=await k.go();if(k.getDetailedResult()===S.kNewCommadIn)return s(),!1;if(typeof T!="number")break;if(T>h*2){I.acutPrintf(`*${y("411")} ${y("412")}*`),g.setLastInputPoint(w);continue}const R=Math.asin(T/2/h)*2,B=w.sub(c).normalize().mult(h),O=c.clone().addvec(B.clone().rotateBy(r?Math.PI-R/2:R/2)),_=c.clone().addvec(B.clone().rotateBy(R)),H=F.calcBulge(w,O,_).val;let X={};X.bulge=0,X.point=_,n[n.length-1].bulge=H,n.push(X),g.setLastInputPoint(_),e&&e(n);continue}if(g.getDetailedResult()===S.kNewCommadIn)return s(),!1;if(!p)return s();const A=c.clone().addvec(p.sub(c).normalize().mult(h)),b=c.sub(w).angleTo2(c.sub(A),Z.kZAxis),M=new z((w.x+A.x)/2,(w.y+A.y)/2),C=c.sub(M).normalize().mult(-h),L=c.clone().addvec((r?b<Math.PI:b>Math.PI)?C.negate():C),v=F.calcBulge(w,L,A).val;let E={};E.bulge=0,E.point=A,n[n.length-1].bulge=v,n.push(E),g.setLastInputPoint(A),e&&e(n);continue}if(g.isKeyWordPicked("R")){let c=new ke;c.setMessage(ye("ID_ARX_PLGETSTARTRADIUS",y("413"))),c.setKeyWords("");let w=await c.go();if(c.getDetailedResult()===S.kNewCommadIn)return s(),!1;if(w===null)break;const h=x.point;g.setLastInputPoint(h),g.setMessage(`${y("400")}(${y("401")})`),g.setKeyWords(`[${y("281")}(A)]`),g.setUserDraw((E,k)=>{if(!w)return;const T=E.distanceTo(h);if(T>w*2)return;const R=Math.acos(T/2/w),B=h.clone().addvec(E.sub(h).rotateBy(R).normalize().mult(w)),O=B.clone().addvec(new z((h.x+E.x)/2,(h.y+E.y)/2).sub(B).normalize().mult(r?-w:w)),_=F.calcBulge(h,O,E).val;let H=new W;n.forEach(({point:X,bulge:Y})=>{H.addVertexAt(X,Y)}),H.addVertexAt(x.point,_),H.addVertexAt(E),k.drawMcDbEntity(H)});const p=await g.go();if(g.isKeyWordPicked("A")){const E=new ae;E.setMessage(ye("ID_ARX_PLGET_I_ANGLE",y("404"))),E.setKeyWords(""),E.setBasePt(h);const k=await E.go();if(E.getDetailedResult()===S.kNewCommadIn)return s(),!1;if(typeof k!="number")break;const T=Math.sin(k/2)*w*2;g.setMessage(`${y("407")}(${y("401")})`),g.setKeyWords(""),g.setBasePt(h),g.setUserDraw((te,le)=>{if(!w)return;const se=te.sub(h).normalize(),ie=h.clone().addvec(se.clone().mult(T)),fe=ie.clone().addvec(se.clone().negate().rotateBy(-(Math.PI/2-k/2)).mult(w)),re=fe.clone().addvec(fe.sub(new z((h.x+ie.x)/2,(h.y+ie.y)/2)).normalize().mult(r?w:-w)),Pe=F.calcBulge(h,re,ie).val;let J=new W;n.forEach(({point:pe,bulge:de})=>{J.addVertexAt(pe,de)}),J.addVertexAt(x.point,Pe),J.addVertexAt(ie),le.drawMcDbEntity(J)});const R=await g.go();if(g.getDetailedResult()===S.kNewCommadIn)return s(),!1;if(!R)break;const B=R.sub(h).normalize(),O=h.clone().addvec(B.clone().mult(T)),_=O.clone().addvec(B.clone().negate().rotateBy(-(Math.PI/2-k/2)).mult(w)),H=_.clone().addvec(_.sub(new z((h.x+O.x)/2,(h.y+O.y)/2)).normalize().mult(r?w:-w)),X=F.calcBulge(h,H,O).val;let Y={};Y.bulge=0,Y.point=O,n[n.length-1].bulge=X,n.push(Y),e&&e(n);continue}if(g.getDetailedResult()===S.kNewCommadIn)return s(),!1;if(!p)break;const A=p.distanceTo(h);if(A>w*2){I.acutPrintf(`${y("400")} *${y("412")}*`);continue}const b=Math.acos(A/2/w),M=h.clone().addvec(p.sub(h).rotateBy(b).normalize().mult(w)),C=M.clone().addvec(new z((h.x+p.x)/2,(h.y+p.y)/2).sub(M).normalize().mult(r?-w:w)),L=F.calcBulge(h,C,p).val;let v={};v.bulge=0,v.point=p,n[n.length-1].bulge=L,n.push(v),e&&e(n);continue}if(g.isKeyWordPicked("D")){g.setMessage(y("414")),g.setKeyWords(""),g.setUserDraw((A,b)=>{b.drawLine(A.toVector3(),x.point.toVector3())});const c=await g.go();if(g.getDetailedResult()===S.kNewCommadIn)return s(),!1;if(!c)break;const w=c.sub(x.point);g.setLastInputPoint(x.point),g.setMessage(`${y("400")}(${y("401")})`),g.setKeyWords(""),g.setUserDraw((A,b)=>{let M=$e(x.point,A,r?w.clone().negate():w),C=new W;n.forEach(({point:L,bulge:v})=>{C.addVertexAt(L,v)}),C.addVertexAt(x.point,M),C.addVertexAt(A),b.drawMcDbEntity(C)});const h=await g.go();if(g.getDetailedResult()===S.kNewCommadIn)return s(),!1;let p={};p.bulge=0,p.point=h,n[n.length-1].bulge=$e(x.point,p.point,r?w.clone().negate():w),n.push(p),e&&e(n);continue}if(g.isKeyWordPicked("L"))o=!0;else if(g.isKeyWordPicked("S")){let c=new z(n[n.length-1].point.x,n[n.length-1].point.y,0),w=new j;w.setMessage(ye("NO1_ID_SPECIFY_ARC2",y("84"))),w.setBasePt(c);let h=await w.go();if(w.getDetailedResult()===S.kNewCommadIn)return s(),!1;if(h===null)break;let p=new j;p.setMessage(ye("ID_CIRCULAR_ENDPOINT",`${y("400")}(${y("401")})`)),p.setUserDraw((b,M)=>{const C=new ee;C.computeArc(c.x,c.y,h.x,h.y,b.x,b.y);let L=h;r&&(L=C.center.clone().addvec(C.center.clone().sub(h)));let v=F.calcBulge(c,L,b).val;const E=new W;n.forEach(({point:k,bulge:T})=>{E.addVertexAt(k,T)}),E.addVertexAt(x.point,v),E.addVertexAt(b),M.drawMcDbEntity(E)});let A=await p.go();if(A!==null){let b=h;if(r){const C=new ee;C.computeArc(c.x,c.y,h.x,h.y,A.x,A.y),b=C.center.clone().addvec(C.center.clone().sub(h))}let M=F.calcBulge(c,b,A);if(M.ret){let C={};C.point=A,C.dBluge=0,n[n.length-1].bulge=M.val,n.push(C),e&&e(n)}else I.acutPrintf(ye("ID_ENDPOINT_INVALID1",`
 ${y("415")} *${y("412")}*`))}else{if(p.getDetailedResult()===S.kNewCommadIn)return s(),!1;break}}else if(g.isKeyWordPicked("C")){n[n.length-1].bulge=$e(x.point,t,r?m.clone().negate():m);const c=new W;c.isClosed=!0,n.forEach(({point:p,bulge:A})=>{c.addVertexAt(p,A)});const w=c.getLength().val;e&&e(n);const h=a.drawEntity(c);return{dist:w,pl:c,plId:h}}else if(g.isKeyWordPicked("U")&&n.length>1){n.pop();const c=new W;n.forEach(({point:w,bulge:h})=>{c.addVertexAt(w,h)}),e&&e(n),n.length>0&&g.setLastInputPoint(n[n.length-1].point)}}else{if(g.getDetailedResult()===S.kNewCommadIn)return s(),!1;break}}const f=new W;n.forEach(({point:g,bulge:m})=>{f.addVertexAt(g,m)});const D=f.getLength().val;e&&e(n);const P=a.drawEntity(f);return{dist:D,pl:f,plId:P}};function ct(t,e){const o=e.x,i=e.y;return t.x>o&&t.y>i||t.x<o&&t.y<i}function $n(t,e){const o=(t.x+e.x)/2,i=(t.y+e.y)/2;return new z(o,i)}function zn(t,e,o){const i=ct(t,o),r=ct(e,o);return i&&r}async function Et(t=!1){const e=new j;let o=[];e.clearLastInputPoint(),e.setMessage(y("395"));const i=await e.go();if(e.getDetailedResult()===S.kNewCommadIn)return!1;if(!i)return;e.setUserDraw((d,a)=>{a.drawLine(i.toVector3(),d.toVector3())}),e.setMessage(y("433")),e.setKeyWords(`[${y("434")}(M)]`);const r=await e.go();if(e.getDetailedResult()===S.kNewCommadIn)return!1;if(e.getStatus()!==N.kCancel){if(e.isKeyWordPicked("M")){const d=await kt(i,s=>{const n=new W;s.forEach(({point:f,bulge:D})=>{n.addVertexAt(f,D)}),I.acutPrintf(y("279")+" = "+n.getLength().val)});if(d===!1)return d;const{plId:a}=d||{};if(!a)return;const l=a.getMcDbEntity();return l&&o.push(l),{markedLines:o}}else if(r){if(t){const P=zn(i,r,$n(i,r)),g=new Ee;if(g.referenceAxis=new THREE.Vector3(0,0,P?-1:1),g.isAligned=!0,g.pt1=i.toVector3(),g.pt2=r.toVector3(),I.addToCurrentSpace(g),o.push(g),Math.abs(i.x-r.x)>.001&&Math.abs(i.y-r.y)>.001){const x=new Ee;x.isAligned=!1,x.referenceAxis=new THREE.Vector3(0,i.y>r.y?1:-1,-1),x.pt1=i.toVector3(),x.pt2=r.toVector3(),I.addToCurrentSpace(x),o.push(x);const u=new Ee;u.isAligned=!1,u.referenceAxis=new THREE.Vector3(i.x>r.x?1:-1,0,-1),u.pt1=i.toVector3(),u.pt2=r.toVector3(),I.addToCurrentSpace(u),o.push(u);const c=new we;c.offsetDist=0,c.center=i.toVector3(),c.pt1=i.toVector3().add(new THREE.Vector3(1,0,0)),c.pt2=r.toVector3(),I.addToCurrentSpace(c),o.push(c)}const m=new ge;m.pt1=i.toVector3(),m.pt2=r.toVector3(),I.addToCurrentSpace(m),o.push(m)}const d=i.distanceTo(r);let a=r.x-i.x,l=r.y-i.y,s=r.z-i.z,n=Math.atan2(l,a)*(180/Math.PI),f=Math.sqrt(a*a+l*l+s*s),D=f===0?0:Math.asin(s/f)*(180/Math.PI);return I.acutPrintf(`${y("279")} = ${Le(d,4)} , XY ${y("435")} = ${Le(n,4)} , ${y("436")} XY ${y("437")} = ${Le(D,4)}
 X 增量 = ${Le(a,4)}，  Y 增量 = ${Le(l,4)}，   Z 增量 = ${Le(s,4)}`),{dist:d,angleInDegrees:n,angleWithZAxis:D,deltaX:a,deltaY:l,deltaZ:s,markedLines:o}}}}V("_MxMeasurementDistance",Et);async function Kn(t=!1){for(;;){const e=new Q,o=new ce;o.AddMcDbEntityTypes("CIRCLE,ARC"),e.setFilter(o),e.setMessage(y("428"));const i=await e.go();if(e.getDetailedResult()===S.kCodeAbort||e.getDetailedResult()===S.kEcsIn||e.getDetailedResult()===S.kMouseRightIn)return;if(e.getDetailedResult()===S.kNewCommadIn)return!1;if(e.getDetailedResult()===S.kNullSpaceIn||e.getDetailedResult()===S.kNullEnterIn||!i)return;const r=i.getMcDbEntity();if(r instanceof ee||r instanceof q){const d=[];if(t){const{val:a,ret:l}=r.getClosestPointTo(e.getDocPickPoint(),!1);if(!l||!a)return;const s=r.center,n=new Ee;n.pt1=a.toVector3(),n.pt2=s.toVector3(),n.isAligned=!0,n.offsetHeight=0,I.addToCurrentSpace(n),d.push(n)}return I.acutPrintf(y("280")+" = "+r.radius+`
`+y("429")+" = "+r.radius*2+`
`),{radius:r.radius,markedLines:d}}}}function Ye(t,e,o,i){const r=_e(i,t,o),d=_e(i,e,o);return isNaN(d)?!0:isNaN(r)?!1:d===r?o.distanceTo(t)>o.distanceTo(e):r<d}async function On(){const t=new Q,e=new ce;for(e.AddMcDbEntityTypes("CIRCLE,ARC,LINE,LWPOLYLINE");;){t.setFilter(e),t.setMessage(`${y("238")}${y("284")}、${y("268")}、${y("283")}<${y("328")}>`);const o=await t.go(),i=t.getDetailedResult(),r=[];if(t.getDetailedResult()===S.kNewCommadIn||t.getDetailedResult()===S.kCodeAbort)return!1;if(t.getDetailedResult()===S.kEcsIn)return;if(i===S.kNullEnterIn||i===S.kNullSpaceIn||i===S.kMouseRightIn){const a=new nn,l=new we;a.setMessage(`
${y("417")}`);const s=await a.go();if(!s)return;l.center=s,a.setMessage("\n${t('330')}"),a.setUserDraw((x,u)=>{u.drawLine(x,s)});const n=await a.go();if(!n)return;l.pt1=n,a.setMessage(`
${y("331")}:`),a.setUserDraw((x,u)=>{u.drawLine(x,s),u.drawLine(n,s),l.offsetDist=0,l.pt2=x,u.drawCustomEntity(l)});const f=await a.go();if(!f)return;l.pt2=f;let D=s.distanceTo(n)-s.distanceTo(f);l.offsetDist=D<0?0:D/2;const P=new ge;P.pt1=s,P.pt2=n;const g=new ge;g.pt1=s,g.pt2=f,I.addToCurrentSpace(P),I.addToCurrentSpace(g),I.addToCurrentSpace(l);const m=l.getAngle();return I.acutPrintf(y("281")+" = "+m+"°"),r.push(l,P,g),{angle:m,markedArcs:r}}if(!o)continue;const d=o.getMcDbEntity();if(d){if(d instanceof U||d instanceof W){let a,l,s,n;const f=t.getDocPickPoint();let D;if(d instanceof W){const{start:b,end:M}=Ne(d,f,2)||{};a=b,l=M}else a=d.startPoint,l=d.endPoint;if(!a||!l)continue;for(;;){const b=new ce;b.AddMcDbEntityTypes("LINE,LWPOLYLINE"),t.setFilter(b),t.setMessage(y("332"));const M=await t.go();if(D=t.getDocPickPoint(),t.getDetailedResult()===S.kNewCommadIn||t.getDetailedResult()===S.kCodeAbort)return!1;if(t.getDetailedResult()===S.kEcsIn)return;if(!M||!M.isValid())continue;let C=M.getMcDbEntity();if(C instanceof W){const{start:L,end:v}=Ne(C,D,2)||{};s=L,n=v}else C instanceof U&&(s=C.startPoint,n=C.endPoint);if(s&&n)break}const P=new U(a,l).IntersectWith(new U(s,n),Re.Intersect.kExtendBoth);if(P.isEmpty()){I.acutPrintf(y("418"));return}if(!a||!l||!s||!n||!D)continue;const g=P.at(0),x=Ye(a,l,g,f)?a:l,c=Ye(s,n,g,f)?s:n,w=new we;w.center=g.toVector3(),w.pt1=x.toVector3(),w.pt2=c.toVector3(),w.isMaxRadius=!0,w.offsetDist=0,I.addToCurrentSpace(w);const h=w.getAngle();I.acutPrintf(y("281")+" = "+h+"°"),r.push(w);const p=new ge;p.pt1=w.pt1,p.pt2=w.center;const A=new ge;return A.pt1=w.center,A.pt2=w.pt2,I.addToCurrentSpace(p),I.addToCurrentSpace(A),r.push(p,A),{angle:h,markedArcs:r}}if(d instanceof ee){const a=new we;a.offsetDist=d.radius,a.center=d.center.toVector3(),a.pt1=new THREE.Vector3(d.radius),a.pt1.applyAxisAngle(new THREE.Vector3(0,0,1),d.startAngle).add(d.center.toVector3()),a.pt2=new THREE.Vector3(d.radius),a.pt2.applyAxisAngle(new THREE.Vector3(0,0,1),d.endAngle).add(d.center.toVector3());let l=d.endAngle-d.startAngle;l>Math.PI?l-=Math.PI*2:l<-Math.PI&&(l+=Math.PI*2),a.isClockwise=Math.abs(l)>Math.PI&&l>0,I.addToCurrentSpace(a);const s=a.getAngle();I.acutPrintf(y("281")+" = "+s+"°"),r.push(a);const n=new ge;n.pt1=a.pt1,n.pt2=a.center;const f=new ge;return f.pt1=a.center,f.pt2=a.pt2,I.addToCurrentSpace(n),I.addToCurrentSpace(f),r.push(n,f),{angle:s,markedArcs:r}}if(d instanceof q){const a=new we;a.center=d.center.toVector3(),a.pt1=t.getDocPickPoint().toVector3();const l=new j;l.setUserDraw((f,D)=>{a.pt2=f.toVector3(),a.offsetDist=0,D.drawCustomEntity(a)}),l.setMessage(y("419"));const s=await l.go();if(l.getDetailedResult()===S.kNewCommadIn)return!1;if(!s)return;a.pt2=s.toVector3(),a.offsetDist=d.radius,I.addToCurrentSpace(a);const n=a.getAngle();return I.acutPrintf(y("281")+" = "+n+"°"),r.push(a),{angle:n,markedArcs:r}}}}}const vt=t=>{const e=t.numVerts(),o=[];if(e<3)return o;const i=$.getCurrentMxCAD(),r=t.getPointAt(0).val,d=t.getPointAt(e-1).val;if(!t.isClosed&&!r.isEqualTo(d))return o;i.pathMoveTo(r.x,r.y);for(let s=0;s<e;s++){const n=t.getPointAt(s).val,f=t.getBulgeAt(s),{val1:D,val2:P}=t.getWidthsAt(s);i.pathLineToEx(n.x,n.y,D,P,f)}i.pathLineTo(d.x,d.y);const l=i.drawPathToHatch().getMcDbEntity();return l&&o.push(l),o},Fn=async t=>{const e=$.getCurrentMxCAD();if(t instanceof W)return vt(t);if(t instanceof q){e.pathMoveTo(t.center.x,t.center.y),e.pathCircle(t.center.x,t.center.y,t.radius);const i=e.drawPathToHatch().getMcDbEntity();if(i)return[i]}else{const o=t.clone(),{minPt:i,maxPt:r}=e.getDatabase().currentSpace.getBoundingBox(),d=i.clone(),a=r.clone();o.move(d,a);const l=o.getBoundingBox(),s=new z((l.minPt.x+l.maxPt.x)/2,(l.minPt.y+l.maxPt.y)/2),n=e.drawEntity(o);await(async()=>new Promise(D=>setTimeout(D,1)))();const f=F.builderHatchFromPoint(s);if(n.erase(),o.erase(),f){f.move(a,d);const P=e.drawEntity(f).getMcDbEntity();if(P)return[P]}}},Bn=async()=>{const t=new j;let e=[],o=!1,i=!1,r=0;for(;;){t.setMessage((o?`(${y("420")})`:"")+(i?`(${y("421")})`:"")+`${y("357")}<${y("208")}>`),t.setKeyWords(`[${y("208")}(O)/${o?"":y("422")+"(A)/"}${i?"":y("423")+"(S)/"}${y("424")}(X)]`),await(async()=>new Promise(a=>setTimeout(a,1)))();const d=await t.go();if(t.getDetailedResult()===S.kNewCommadIn)return e.length>0?{markedLines:e}:!1;if(t.getDetailedResult()===S.kCodeAbort)return e.length>0?{markedLines:e}:!1;if(t.getDetailedResult()===S.kEcsIn||t.getDetailedResult()===S.kMouseRightIn)return e.length>0?{markedLines:e}:void 0;if(t.isKeyWordPicked("O")||t.getDetailedResult()===S.kNullEnterIn||t.getDetailedResult()===S.kNullSpaceIn)for(;;){const a=new Q;a.setMessage(y("205")),await(async()=>new Promise(P=>setTimeout(P,1)))();const l=await a.go();if(a.getDetailedResult()===S.kNewCommadIn)return e.length>0?{markedLines:e}:!1;if(a.getDetailedResult()===S.kCodeAbort)return e.length>0?{markedLines:e}:!1;if(a.getDetailedResult()===S.kEcsIn)return e.length>0?{markedLines:e}:!1;if(a.getDetailedResult()===S.kNullEnterIn||a.getDetailedResult()===S.kNullSpaceIn||a.getDetailedResult()===S.kMouseRightIn)break;if(!l)continue;const s=l.getMcDbEntity();if(!s)continue;let n=s.getArea().val,f=0;s instanceof Te&&(f=s.getLength().val);const D=await Fn(s);if(D&&e.push(...D),n===0&&D)if(s instanceof q)n=Math.PI*Math.pow(s.radius,2);else if(s instanceof Ke&&s.startAngle===0&&s.endAngle===Math.PI*2){const P=Math.sqrt(s.majorAxis.x*s.majorAxis.x+s.majorAxis.y*s.majorAxis.y)/2,g=P*s.radiusRatio;n=Math.PI*P*g;const m=Math.pow((P-g)/(P+g),2);f=Math.PI*(P+g)*(1+3*m/(10+Math.sqrt(4-3*m)))}else n=D.reduce((P,g)=>P+g.getArea().val,0);if(n===0?I.acutPrintf(y("425")+`
`):I.acutPrintf(`${y("234")} = ${n}, ${y("426")} = ${f}
`),o||i){o?r+=n:r-=n,I.acutPrintf(`${y("427")} = ${r}
`);continue}return{markedLines:e}}if(t.isKeyWordPicked("A")){o=!0,i=!1;continue}if(t.isKeyWordPicked("S")){i=!0,o=!1;continue}if(t.isKeyWordPicked("X"))return I.acutPrintf(`${y("427")} = ${r}
`),e.length>0?{markedLines:e}:void 0;if(d){const a=await kt(d);if(a===!1)return e.length>0?{markedLines:e}:a;const{plId:l}=a||{};if(!l)return e.length>0?{markedLines:e}:void 0;const s=l.getMcDbEntity();if(s instanceof W){s.isClosed=!0,e.push(s);const n=vt(s);e.push(...n);const f=s.getArea().val;if(I.acutPrintf(`${y("234")} = ${s.getArea().val}, ${y("426")} = ${s.getLength().val}
`),o||i){o?r+=f:r-=f,I.acutPrintf(`${y("427")} = ${r}
`);continue}return{markedLines:e}}}}};class Hn extends ge{getTypeName(){return"MxSelectLine"}worldDraw(e){e.setLineWidthByPixels(!0),e.setLineWidth(10),e.setColor(16737894),e.drawSelectLine(this.pt1,this.pt2)}}function It(t,e,o){const r=t.distanceTo(e)/2,d=r*o,a=(r*r+d*d)/(2*d),l=(t.x+e.x)/2,s=(t.y+e.y)/2;return new z(l,s).addvec(e.clone().sub(t).perpVector().normalize().mult(a-d))}function Un(t,e,o,i){const r=e.clone().sub(t),d=o.clone().sub(t),a=r.length(),l=new THREE.Vector3().crossVectors(r,d);typeof i>"u"&&(i=l.dot(new THREE.Vector3(0,0,1))<0);let s=r.clone().add(d);return i&&(s=s.negate()),s=s.normalize(),t.clone().add(s.multiplyScalar(a))}V("_MEASUREGEOM",async()=>{let t,e,o=y("移动光标"),i=[];const r=()=>{i.forEach(d=>{d.erase()})};try{for(;;){const d=new j;d.setCursorType(sn.kCross),d.disableAllTrace(),d.setDisableDynInput(!0),d.clearLastInputPoint(),d.setMessage(o);const a=`[${y("距离")}(D)/${y("半径")}(R)/${y("角度")}(A)/${y("面积")}(AR)/${y("快速")}(Q)/${y("模式")}(M)/${y("退出")}(X)]`;d.setKeyWords(a);const l=$.getCurrentMxCAD(),s=new qe,n=new ce([Gt.kEntityType,"LINE,ARC,CIRCLE,LWPOLYLINE,INSERT"]),f=(u,c,w)=>{let h,p,A;for(let b=0;b<u.length;b++){let C=u[b].getMcDbEntity();if(!C)continue;if(C instanceof Zt){const k=C.blockTableRecordId.getMcDbBlockTableRecord();if(!k)continue;return f(k.getAllEntityId(),c.clone(),w.clone())}if(!(C instanceof Te))continue;const L=C.IntersectWith(new U(c,w),Re.Intersect.kOnBothOperands);if(C instanceof W&&C.isClosed){const k=C.numVerts()-1,T=C.getPointAt(0).val,R=C.getPointAt(k).val,B=new U(T,R).IntersectWith(new U(c,w),Re.Intersect.kOnBothOperands);if(!B.isEmpty()){const O=B.at(0),_=O.distanceTo(c);(typeof h>"u"||_<h)&&(p=C,h=_,A=O)}}if(L.isEmpty())continue;let v,E;L.forEach(k=>{const T=k.distanceTo(c);(typeof E>"u"||T<E)&&(E=T,v=k)}),(typeof h>"u"||E<h)&&(p=C,h=E,A=v)}if(p)return{minDistEnt:p,minDist:h,intersectPt:A}},D=(u,c)=>(s.crossingSelect(u.x,u.y,c.x,c.y,n),s.isNull()?void 0:f(s.getIds(),u,c)),P=(u,c,w,h)=>{const p=c.clone().addvec(w.clone().mult(I.viewCoordLong2Cad(16))),A=Ve.createDashedLines([c.toVector3(),p.toVector3()],8421504,.1,.1),b=c.clone().addvec(w.clone().mult(I.viewCoordLong2Cad(28)));u.drawEntity(A),u.drawText(h,I.viewCoordLong2Cad(16),0,b.toVector3())},g=(u,c,w,h,p)=>{let A;if(c instanceof W||c instanceof U){let b,M;if(c instanceof U&&(b=c.startPoint,M=c.endPoint),c instanceof W){const C=Ne(c,p,10);if(!C)return;const L=c.getBulgeAt(C.startIndex);if(L===0)b=C.start,M=C.end;else{const v=c.getParamAtPoint(C.start).val,E=c.getParamAtPoint(C.end).val,k=c.getDistAtParam(v).val,T=c.getDistAtParam(E).val,R=It(C.start,C.end,L);u.drawCircle(C.end.toVector3(),5);const{x:B,y:O}=Un(R.toVector3(),C.start.toVector3(),C.end.toVector3()),_=new z(B,O);let H=_.clone().sub(R).normalize();const X=R.distanceTo(_);Math.abs(L)>1&&(_.addvec(R.clone().sub(_).normalize().mult(X*2)),H.negate()),P(u,_,H,Math.abs(T-k).toFixed(3))}}if(b&&M){const C=new Hn;C.pt1=b.toVector3(),C.pt2=M.toVector3(),u.drawCustomEntity(C);const L=new Ee;L.pt1=b.toVector3(),L.pt2=M.toVector3(),L.isAligned=!0,A=(E=h.x>w.x||h.y<w.y)=>{L.referenceAxis=new THREE.Vector3(0,0,E?1:-1),u.drawCustomEntity(L)};const v=new U(b,M);return{draw:A,line:v}}}if(c instanceof q){const b=new Z(-1,0,0),M=c.center.clone().addvec(b.clone().mult(c.radius));P(u,M,b,c.getLength().val.toFixed(3))}if(c instanceof ee){const M=c.getGripPoints().at(2);P(u,M,M.sub(c.center).normalize(),c.getLength().val.toFixed(3))}},m=(u,c,w,h,p)=>{if(!h||!p||h.startPoint.isEqualTo(p.startPoint)&&h.endPoint.isEqualTo(p.endPoint))return;const A=h.IntersectWith(p,Re.Intersect.kOnBothOperands);if(A.isEmpty())return;const b=new we;b.offsetDist=0;const M=A.at(0),C=Ye(h.startPoint,h.endPoint,M,c),L=Ye(p.startPoint,p.endPoint,M,w);C?b.pt1=h.startPoint.toVector3():b.pt1=h.endPoint.toVector3(),L?b.pt2=p.startPoint.toVector3():b.pt2=p.endPoint.toVector3(),b.center=M.toVector3();const v=b.getAngle();if(v===Math.PI/2){u.setColor(16760576),b.radius=Math.min(I.viewCoordLong2Cad(10),Math.min(h.getLength().val,p.getLength().val)/10);const E=b.getRadius(!0),k=b.getStartPoint().clone().add(b.center.clone().sub(b.pt1).normalize().negate().multiplyScalar(E)),T=b.getEndPoint().clone().add(b.center.clone().sub(b.pt2).normalize().negate().multiplyScalar(E)),R=new THREE.Vector3(k.x+T.x-b.center.x,k.y+T.y-b.center.y);u.drawLine(k,R),u.drawLine(T,R)}else b.radius=I.viewCoordLong2Cad(26),u.drawCustomEntity(b);return v},x=(u,c,w,h,p,A)=>{const b=new U(c,w),M=new U(c,h),C=new U(c,p),L=new U(c,A),v=D(c,w),E=D(c,h),k=D(c,p),T=D(c,A);v&&(b.endPoint=v.intersectPt),E&&(M.endPoint=E.intersectPt),k&&(C.endPoint=k.intersectPt),T&&(L.endPoint=T.intersectPt),u.setColor(16777215);const{line:R,draw:B}=v&&g(u,v.minDistEnt,c,w,v.intersectPt)||{},{line:O,draw:_}=E&&g(u,E.minDistEnt,c,h,E.intersectPt)||{},{line:H,draw:X}=k&&g(u,k.minDistEnt,c,p,k.intersectPt)||{},{line:Y,draw:te}=T&&g(u,T.minDistEnt,c,A,T.intersectPt)||{},le=v&&k&&m(u,v.intersectPt,k.intersectPt,R,H),se=v&&T&&m(u,v.intersectPt,T.intersectPt,R,Y),ie=E&&k&&m(u,E.intersectPt,k.intersectPt,O,H),fe=E&&T&&m(u,E.intersectPt,T.intersectPt,O,Y);v&&E&&m(u,v.intersectPt,E.intersectPt,R,O),k&&T&&m(u,k.intersectPt,T.intersectPt,H,Y);const re=Math.PI/2,Pe=le===re&&se===re&&ie===re&&fe===re;function J(K){return K.startPoint.x===K.endPoint.x?1/0:(K.endPoint.y-K.startPoint.y)/(K.endPoint.x-K.startPoint.x)}const pe=(K,ne)=>{const Ie=J(K),De=J(ne);return Ie===De},de=(K,ne)=>{const Ie=K.getLength().val,De=ne.getLength().val;return Ie===De},Ae=(K,ne)=>pe(K,ne)&&de(K,ne),be=[];R&&be.push({line:R,draw:B,info:v}),O&&be.push({line:O,draw:_,info:E}),H&&be.push({line:H,draw:X,info:k}),Y&&be.push({line:Y,draw:te,info:T}),be.reduce((K,ne)=>{let Ie=!1;for(let De=0;De<K.length;De++){const st=K[De];if(Ae(st[0].line,ne.line)){st.push(ne),Ie=!0;break}}return Ie||K.push([ne]),K},[]).forEach(K=>{if(K.length===1){if(Ae(K[0].line,new U(b.endPoint,M.endPoint))||Ae(K[0].line,new U(C.endPoint,L.endPoint)))return;K[0].draw&&K[0].draw()}else if(!Pe)if(K.length===2){if(Ae(K[1].line,new U(b.endPoint,M.endPoint))||Ae(K[1].line,new U(C.endPoint,L.endPoint)))return;v&&E||k&&T?(v&&E&&(v.intersectPt.distanceTo(c)<E.intersectPt.distanceTo(c)?B&&B(!0):_&&_()),k&&T&&(k.intersectPt.distanceTo(c)<T.intersectPt.distanceTo(c)?X&&X(!1):te&&te())):(K[0].draw&&K[0].draw(),K[1].draw&&K[1].draw())}else K.forEach(({draw:ne})=>{ne&&ne()})}),u.setColor(16760576),u.drawMcDbEntity(b),u.drawMcDbEntity(M),u.drawMcDbEntity(C),u.drawMcDbEntity(L);const Fe=I.viewCoordLong2Cad(16);if(v&&E){const K=b.endPoint.distanceTo(M.endPoint);b.endPoint.distanceTo(c)>M.endPoint.distanceTo(c)?u.drawText(K.toFixed(3),Fe,0,new THREE.Vector3((c.x+b.endPoint.x)/2,(c.y+b.endPoint.y)/2)):u.drawText(K.toFixed(3),Fe,0,new THREE.Vector3((c.x+M.endPoint.x)/2,(c.y+M.endPoint.y)/2))}if(k&&T){const K=C.endPoint.distanceTo(L.endPoint);C.endPoint.distanceTo(c)>L.endPoint.distanceTo(c)?u.drawText(K.toFixed(3),Fe,0,new THREE.Vector3((c.x+C.endPoint.x)/2,(c.y+C.endPoint.y)/2)):u.drawText(K.toFixed(3),Fe,0,new THREE.Vector3((c.x+L.endPoint.x)/2,(c.y+L.endPoint.y)/2))}};if(d.setUserDraw((u,c)=>{const w=I.viewCoordLong2Cad(l.mxdraw.getViewWidth()),h=I.viewCoordLong2Cad(l.mxdraw.getViewHeight()),p=new z(-w,u.y),A=new z(u.x+w,u.y),b=new z(u.x,u.y+h),M=new z(u.x,-h);x(c,u,p,A,b,M)}),await d.go(),t=d.keyWordPicked(),d.getStatus()===N.kCancel||d.getDetailedResult()===S.kCodeAbort||d.getDetailedResult()===S.kEcsIn)break;if((d.getDetailedResult()===S.kMouseRightIn||d.getDetailedResult()===S.kNullSpaceIn||d.getDetailedResult()===S.kNullEnterIn)&&(t=e),t?.toLocaleUpperCase()==="D"){r();const u=await Et(!0);if(u===!1)break;const{markedLines:c}=u||{};c&&i.push(...c),await(async()=>new Promise(w=>setTimeout(w,1)))()}if(t?.toLocaleUpperCase()==="R"){r();const u=await Kn(!0);if(u===!1)break;const{markedLines:c}=u||{};c&&i.push(...c),await(async()=>new Promise(w=>setTimeout(w,1)))()}if(t?.toLocaleUpperCase()==="A"){r();const u=await On();if(u===!1)break;const{markedArcs:c}=u||{};c&&i.push(...c),await(async()=>new Promise(w=>setTimeout(w,1)))()}if(t?.toLocaleUpperCase()==="AR"){r();const u=await Bn();if(u===!1)break;const{markedLines:c}=u||{};c&&i.push(...c),await(async()=>new Promise(w=>setTimeout(w,1)))()}if(t?.toLocaleUpperCase()==="Q"&&r(),t?.toLocaleUpperCase()==="X")break;a.includes(t)&&t!==""&&(e=t)}}catch(d){console.warn(d)}r()});async function jn(){const t=new j;t.setMessage(y("416"));const e=await t.go();e&&I.acutPrintf(`X = ${e.x}     Y = ${e.y}     Z = ${e.z} 
`)}V("ID",jn);function oe(t,e,o,i){const r=t-o,d=e-i;let a=0;return r==0?a=Math.PI/2:a=Math.atan(Math.abs(d/r)),r<0&&d>=0?a=Math.PI-a:r<0&&d<0?a=Math.PI+a:r>=0&&d<0&&(a=Math.PI*2-a),a-Math.PI}function Xn(t,e,o,i=!1){let r={x:e.x-t.x,y:e.y-t.y},d={x:o.x-t.x,y:o.y-t.y},a=r.x*r.x+r.y*r.y;if(a===0||i){let l=a===0?0:(d.x*r.x+d.y*r.y)/a;return new z(t.x+r.x*l,t.y+r.y*l)}else{let l=(d.x*r.x+d.y*r.y)/a;return l<0?t:l>1?e:new z(t.x+r.x*l,t.y+r.y*l)}}function Yn(t,e,o){let i={x:e.x-t.x,y:e.y-t.y},r={x:o.x-t.x,y:o.y-t.y},d=i.x*i.x+i.y*i.y;if(d===0)return o.x===t.x&&o.y===t.y;let a=(r.x*i.x+r.y*i.y)/d;return a>=0&&a<=1?!0:a<0?{isStart:!0}:{isStart:!1}}let je=0,Xe=0,Gn=0,lt=0,dt=0;const Zn={DE:y("166"),P:y("814"),T:y("398"),DY:y("815")};let Me="DE";const ze=(t,e,o,i)=>{const r=t.getLength().val,d=t.getDistAtPoint(o).val;let a=t.getStartPoint().val,l=t.getEndPoint().val;const s=d<r/2;let n;if(t instanceof U){const f=t.startPoint,D=t.endPoint;return s?(t.startPoint=a.clone().addvec(a.sub(l).normalize().mult(e)),n=()=>{t.startPoint=f}):(t.endPoint=t.endPoint.clone().addvec(l.sub(a).normalize().mult(e)),n=()=>{t.endPoint=D}),{ent:t,fallback:n}}if(t instanceof ee){const f=t.startAngle,D=t.endAngle,P=t.clone();s?(P.startAngle=t.startAngle,P.endAngle=t.startAngle-1e-7):(P.startAngle=t.endAngle,P.endAngle=t.endAngle-1e-7);const g=P.getLength().val,m=t.center,x=t.getGripPoints().at(2);let c=F.calcBulge(a,x,l).val>0,w;if(s)if(w=P.getPointAtDist(c?g-e:e).val,c)t.startAngle=oe(m.x,m.y,w.x,w.y),n=()=>{t.startAngle=f};else{const h=t.endAngle;t.startAngle=h,t.endAngle=oe(m.x,m.y,w.x,w.y),n=()=>{t.startAngle=f,t.endAngle=D}}else if(w=P.getPointAtDist(c?e:g-e).val,c)t.endAngle=oe(m.x,m.y,w.x,w.y),n=()=>{t.endAngle=D};else{const h=t.startAngle;t.startAngle=oe(m.x,m.y,w.x,w.y),t.endAngle=h,n=()=>{t.startAngle=f,t.endAngle=D}}return{fallback:n,ent:t}}if(t instanceof Ke){const f=t.clone();f.startAngle=0,f.endAngle=Math.PI*2;const P=f.getLength().val-r;if(e>P)return;const g=f.getPointAtDist(0).val,m=f.getPointAtDist(s?f.getDistAtPoint(a).val-e:f.getDistAtPoint(l).val+e).val,x=g.sub(f.center),u=m.sub(f.center),c=Math.atan2(x.y,x.x);let h=Math.atan2(u.y,u.x)-c;h<0&&(h+=2*Math.PI);const p=t.startAngle,A=t.endAngle;return s?t.startAngle=h:t.endAngle=h,{fallback:()=>{s?t.startAngle=p:t.endAngle=A},ent:t}}if(t instanceof W){if(t.isClosed)return;const f=h=>{const p=[];for(let A=0;A<h.numVerts();A++){const{val1:b,val2:M}=h.getWidthsAt(A);p.push({point:h.getPointAt(A).val,bulge:h.getBulgeAt(A),startWidth:b,endWidth:M})}return p},D=(h,p)=>{for(;h.removeVertexAt(0););for(let A=0;A<p.length;A++){const b=p[A];h.addVertexAt(b.point,b.bulge,b.startWidth,b.endWidth,A)}},P=(h,p,A)=>{const b=It(h,p,A),M=new ee;return M.center=b,M.radius=b.sub(h).length(),M.startAngle=oe(b.x,b.y,h.x,h.y),M.endAngle=oe(b.x,b.y,p.x,p.y),M},g=(h,p,A,b)=>{if(A)if(b)h.startAngle=oe(h.center.x,h.center.y,p.x,p.y);else{const M=h.endAngle;h.startAngle=M,h.endAngle=oe(h.center.x,h.center.y,p.x,p.y)}else if(b)h.endAngle=oe(h.center.x,h.center.y,p.x,p.y);else{const M=h.startAngle;h.startAngle=oe(h.center.x,h.center.y,p.x,p.y),h.endAngle=M}return h},m=(h,p,A,{startIndex:b,endIndex:M,numVerts:C})=>{const L=f(h);if(h.setPointAt(A?b:M,p),A)for(let v=0;v<b;v++)h.removeVertexAt(0);else for(let v=M+1;v<C;v++)h.removeVertexAt(M+1);return()=>D(h,L)},x=(h,p,A,{start:b,end:M,startIndex:C,endIndex:L,numVerts:v,bulge:E})=>{const k=f(h),T=P(b,M,E),R=E>0;g(T,p,A,R);const B=T.getGripPoints().at(2),O=F.calcBulge(A?p:b,B,A?M:p).val;if(h.setPointAt(A?C:L,p),h.setBulgeAt(C,O),A)for(let _=0;_<C;_++)h.removeVertexAt(0);else for(let _=L+1;_<v;_++)h.removeVertexAt(L+1);return()=>D(h,k)},u=(h,p,A,{startPoint:b,endPoint:M,numVerts:C})=>{const L=A?b.clone().addvec(b.sub(M).normalize().mult(p)):M.clone().addvec(M.sub(b).normalize().mult(p)),v=A?0:C-1,E=A?b:M;return h.setPointAt(v,L),()=>h.setPointAt(v,E)},c=(h,p,A,{startPoint:b,endPoint:M,numVerts:C,bulge:L})=>{const v=P(b,M,L),E=v.clone();E.startAngle=A?v.startAngle:v.endAngle,E.endAngle=E.startAngle-1e-7;const k=E.getLength().val,T=L>0,R=p%k,B=E.getPointAtDist(A?T?k-R:R:T?R:k-R).val;g(v,B,A,T);const O=v.getGripPoints().at(2),_=F.calcBulge(A?B:b,O,A?M:B).val,H=A?0:C-1,X=A?0:C-2,Y=A?b:M,te=L;return h.setPointAt(H,B),h.setBulgeAt(X,_),()=>{h.setPointAt(H,Y),h.setBulgeAt(X,te)}},w=t.numVerts();if(e<0){const h=s?t.getPointAtDist(Math.abs(e)).val:t.getPointAtDist(r-Math.abs(e)).val,p=Ne(t,h,.1);if(!p)return;const{start:A,end:b,startIndex:M,endIndex:C}=p,L=t.getBulgeAt(M),v=L===0?m(t,h,s,{startIndex:M,endIndex:C,numVerts:w}):x(t,h,s,{start:A,end:b,startIndex:M,endIndex:C,numVerts:w,bulge:L});return{ent:t,fallback:v}}else{l=s?t.getPointAt(1).val:l,a=s?a:t.getPointAt(w-2).val;const h=s?t.getBulgeAt(0):t.getBulgeAt(w-2),p=h===0?u(t,e,s,{startPoint:a,endPoint:l,numVerts:w}):c(t,e,s,{startPoint:a,endPoint:l,numVerts:w,bulge:h});return{ent:t,fallback:p}}}};V("Mx_lengthen",async()=>{let t=[];const e=new Q,o=new ce;o.AddMcDbEntityTypes("LWPOLYLINE,LINE,ARC,ELLIPSE"),e.setFilter(o);const i=async d=>{for(;;){e.setMessage(y("817")),e.setKeyWords(`[${y("373")}(U)]`);let a;e.setUserDraw((f,D)=>{const P=F.findEntAtPoint(f.x,f.y,f.z,void 0,o);a&&a.highlight(!1),a=P.getMcDbCurve();const g=P.clone();g&&g instanceof Te&&ze(g,typeof d=="number"?d:d(g,f),f)&&(a&&a.highlight(!0),D.setColor(Number(g.trueColor.getColorValue(g.layerId))),D.drawMcDbEntity(g))});const l=await e.go();if(a&&a.highlight(!1),e.getStatus()===N.kCancel||e.getStatus()===N.kNone)return!0;if(e.isKeyWordPicked("U")){const f=t.pop();f?f?.fallback():I.acutPrintf(`
`+y("819"));continue}if(l===null)return!0;const s=l.getMcDbCurve();if(s===null)return!0;const n=ze(s,typeof d=="number"?d:d(s,e.pickPoint()),e.pickPoint());n&&t.push(n)}},r=async d=>{const a=l=>{if(l instanceof ee){const s=l.clone();return s.startAngle=0,s.endAngle=d,s.getLength().val}if(l instanceof Ke){const s=l.clone(),n=s.getLength().val;return s.endAngle+=d,s.getLength().val-n}return 0};for(;;){e.setMessage(y("817")),e.setKeyWords(`[${y("373")}(U)]`);let l;const s=new ce;s.AddMcDbEntityTypes("LWPOLYLINE,ARC,ELLIPSE"),e.setUserDraw((g,m)=>{const x=F.findEntAtPoint(g.x,g.y,g.z,void 0,s);l&&l.highlight(!1),l=x.getMcDbCurve();const u=x.clone();if(!u||!(u instanceof Te))return;let c=a(u);ze(u,c,g)&&(l&&l.highlight(!0),m.setColor(Number(u.trueColor.getColorValue(u.layerId))),m.drawMcDbEntity(u))});const n=await e.go();if(l&&l.highlight(!1),e.getStatus()===N.kCancel||e.getStatus()===N.kNone)return!0;if(e.isKeyWordPicked("U")){const g=t.pop();g?g?.fallback():I.acutPrintf(`
`+y("819"));continue}if(n===null)return!0;const f=n.getMcDbCurve();if(f===null)return!0;const D=a(f),P=ze(f,D,e.pickPoint());P&&t.push(P)}};e:for(;;){e.setMessage(`${y("813")}<${Zn[Me]}(DE)>`),e.setKeyWords(`[${y("166")}(DE)/${y("814")}(P)/${y("398")}(T)/${y("815")}(DY)]`);const d=await e.go();if(e.getStatus()===N.kCancel)break;if(e.getStatus()===N.kNone){setTimeout(()=>{Me&&I.setCommandLineInputData(Me,13)});continue}if(e.isKeyWordPicked("DE")){Me="DE";const l=new ke;l.setMessage(`${y("816")}<${je.toFixed(4)}>`),l.setKeyWords(`[${y("281")}(A)]`);let s=await l.go();if(l.getStatus()===N.kCancel)break;if(l.getStatus()===N.kNone&&typeof je=="number"&&(s=je),l.isKeyWordPicked("A")){const n=new ae;n.setMessage(y("820"));let f=await n.go();if(n.getStatus()===N.kNone&&(f=Gn),l.getStatus()===N.kCancel||typeof f!="number"||await r(f))break}if(typeof s=="number"){if(je=s,await i(s))break}else break}if(e.isKeyWordPicked("P"))for(Me="P";;){const l=new qt;l.setMessage(`${y("821")}<${Xe.toFixed(4)}>`);let s=await l.go();if(l.getStatus()===N.kCancel)break e;if(l.getStatus()===N.kNone&&typeof s=="number"&&(Xe=s),typeof s=="number"){if(s<=0){I.acutPrintf(`
`+y("822"));continue}if(Xe=s,await i(n=>{const f=n.getLength().val;return f/100*Xe-f}))break e}else break}if(e.isKeyWordPicked("T")){Me="T";const l=new ke;l.setMessage(y("823")+"<"+lt.toFixed(4)+">"),l.setKeyWords(`[${y("281")}(A)]`);let s=await l.go();if(l.getStatus()===N.kNone&&(s=lt),l.getStatus()===N.kCancel)break;if(l.isKeyWordPicked("A")){const n=new ae;n.setMessage(y("824")+"<"+dt.toFixed(4)+">");let f=await n.go();if(l.getStatus()===N.kCancel||(l.getStatus()===N.kNone&&(f=dt),typeof f!="number")||await i(D=>0))break}if(typeof s!="number"||await i(n=>s-n.getLength().val))break}if(e.isKeyWordPicked("DY")){Me="DY";const l=(s,n,f,D,P,g)=>{if(!(s instanceof W)){if(s instanceof U){let m=0;const x=Xn(s.startPoint,s.endPoint,n,!0),u=Yn(s.startPoint,s.endPoint,x);if(typeof u=="object"&&u.isStart!==f){const c=s.startPoint,w=s.endPoint;return u.isStart?(s.endPoint=c,s.startPoint=x):(s.startPoint=w,s.endPoint=x),{ent:s,fallback:()=>{s.startPoint=c,s.endPoint=w}}}else return f?m=x.distanceTo(s.endPoint)-D:m=x.distanceTo(s.startPoint)-D,ze(s,m,P)}if(s instanceof ee){const m=s.startAngle,x=s.endAngle,u=s.getClosestPointTo(n,!0).val,c=s.getGripPoints().at(2);let h=F.calcBulge(s.getGripPoints().at(0),c,s.getGripPoints().at(1)).val>0;const p=s.center;let A;if(h)s.endAngle=oe(p.x,p.y,u.x,u.y),A=()=>{s.endAngle=x};else{const b=s.startAngle;s.startAngle=oe(p.x,p.y,u.x,u.y),s.endAngle=b,A=()=>{s.startAngle=m,s.endAngle=x}}return{ent:s,fallback:A}}if(s instanceof Ke){const m=s.endAngle,x=s.center,u=Math.atan2(n.y-x.y,n.x-x.x),c=Math.atan2(s.majorAxis.y,s.majorAxis.x);let w=(u-c+Math.PI*2)%(Math.PI*2);return w<s.startAngle&&(w+=Math.PI*2),s.endAngle=w,{ent:s,fallback:()=>{s.endAngle=m}}}}};for(;;){e.setMessage(y("817")),e.setKeyWords(`[${y("373")}(U)]`);let s;const n=await e.go();if(e.isKeyWordPicked("U")){const u=t.pop();u?u?.fallback():I.acutPrintf(`
`+y("819"));continue}if(e.getStatus()===N.kCancel||e.getStatus()===N.kNone)return!0;if(!n||(s=n.getMcDbCurve(),!s))continue;const f=e.pickPoint(),D=s.getLength().val,g=s.getDistAtPoint(f).val<D/2,m=new j;m.setMessage(y("825")),e.setKeyWords(`[${y("373")}(U)]`),m.setUserDraw((u,c)=>{if(!s)return;s.highlight(!1);const w=n.clone();w instanceof Te&&l(w,u,g,D,f)&&(s.highlight(!0),c.setColor(Number(s.trueColor.getColorValue(s.layerId))),c.drawMcDbEntity(w))});const x=await m.go();if(s&&s.highlight(!1),m.getStatus()===N.kCancel||e.getStatus()===N.kNone)return!0;if(m.isKeyWordPicked("U")){const u=t.pop();u?u?.fallback():I.acutPrintf(`
`+y("819"));continue}if(x===null)return!0;if(s){const u=l(s,x,g,D,f);u&&t.push(u)}}}if(!d)continue;const a=d.getMcDbCurve();a&&I.acutPrintf(`
`+y("818")+": "+a.getLength().val.toFixed(4))}});V("deselect",()=>{const t=$.getCurrentMxCAD();t.mxdraw.clearMxCurrentSelect(),t.updateDisplay()});const Oe=async t=>{const{dialog:e}=At();e.showDialog(!0,t);try{return await new Promise((o,i)=>{e.onConfirm(o),e.onCancel(i)})}catch{return[""]}},Lt=async t=>{const e=await F.userSelect(`选择需要${t?"锁定":"解锁"}图层的对象`),o=new Set,i=ue(),{setValue:r}=i,{list:d}=ve(i);e.forEach(a=>{if(a.type===nt.kMxCAD){const l=a.getMcDbEntity();if(!l)return;const s=d.value.findIndex(({name:n})=>n===l.layer);s>=0&&o.add(s)}}),r("lock",t,Array.from(o),!0)};V("_OpenAllLayer",()=>{const t=ue(),{setLayerList:e,stringifyJSON:o}=t;rn(!0),e(o())});V("_SelOffLayer",async()=>{const t=await F.userSelect("选择需要关闭图层的实体"),e=new Set,o=ue(),{setValue:i,setLayerList:r,stringifyJSON:d}=o,{list:a}=ve(o);t.forEach(l=>{if(l.type===nt.kMxCAD){const s=l.getMcDbEntity();if(!s)return;const n=a.value.findIndex(({name:f})=>f===s.layer);n>=0&&e.add(n)}}),i("visible",!1,Array.from(e)),r(d())});V("_layer_recovery",()=>{const t=ue(),{recoveryLayerStateHistory:e}=t;e()});V("_layer_putCurrent",async()=>{const t=new Q;t.setMessage("选择将使其图层将成为当前图层的对象");const e=await t.go();if(!e||!e.isValid())return;const o=e.getMcDbEntity();if(!o)return;const i=ue(),{putCurrent:r}=i,{list:d}=ve(i),a=d.value.findIndex(({name:l})=>o.layer===l);a<0||r(a)});V("_layer_matching",async()=>{const t=await F.userSelect("选择需要修改图层的对象"),e=new Q;e.setMessage("选择匹配图层的对象"),e.setKeyWords("[名称(N)]");const o=await e.go();let i="";if(e.isKeyWordPicked("N")){const{dialog:r}=At();r.showDialog(!0);try{i=(await Oe())[0]}catch{return}}else{if(!o||!o.isValid())return;const r=o.getMcDbEntity();if(!r)return;i=r.layer}i!==""&&(t.forEach(r=>{const d=r.getMcDbEntity();d&&(d.layer=i)}),$.getCurrentMxCAD().updateDisplay())});V("_layer_setEntToCurrentLayer",async()=>{const t=await F.userSelect("选择需要修改图层的对象"),e=yt().getCurrentlyLayerName();t.forEach(o=>{const i=o.getMcDbEntity();i&&(i.layer=e)}),$.getCurrentMxCAD().updateDisplay()});V("_layer_CopyObjectsToNewLayer",async()=>{const t=await F.userSelect("选择要复制的对象"),e=new Q;e.setMessage("选择目标图层上的对象"),e.setKeyWords("[名称(N)]");const o=await e.go();let i="";if(e.isKeyWordPicked("N"))i=(await Oe())[0];else{if(!o||!o.isValid())return;const l=o.getMcDbEntity();if(!l)return;i=l.layer}const r=new j;r.setMessage("指定基点");let d=await r.go();if(!d)return;r.setMessage("指定位移的第二个点"),r.setUserDraw((l,s)=>{t.forEach(n=>{if(!d)return;const f=n.clone();f&&(f.layer=i,f.move(d,l),s.drawMcDbEntity(f))})});let a=await r.go();t.forEach(l=>{if(!d||!a)return;const s=l.clone();if(!s)return;s.move(d,a);const f=$.getCurrentMxCAD().drawEntity(s).getMcDbEntity();f&&(f.layer=i)})});V("_layer_freeze",async()=>{const t=await F.userSelect("选择需要冻结图层的对象"),e=new Set,o=ue(),{setValue:i}=o,{list:r}=ve(o);t.forEach(d=>{if(d.type===nt.kMxCAD){const a=d.getMcDbEntity();if(!a)return;const l=r.value.findIndex(({name:s})=>s===a.layer);l>=0&&e.add(l)}}),i("freeze",!0,Array.from(e),!0)});V("_layer_thawedAll",()=>{const t=ue(),{setValue:e}=t,{list:o}=ve(t);e("freeze",!1,o.value.map((i,r)=>r),!0)});V("_layer_lock",()=>Lt(!0));V("_layer_unlock",()=>Lt(!1));V("_layer_combined",async()=>{let t,e=await F.userSelect("选择要合并的图层上的对象",null,async(s,n)=>{t=n,n.setKeyWords("[命名(N)]")}),o=[];if(t&&t.isKeyWordPicked("N"))o=await Oe({isMultiple:!0});else if(e){const s=new Set;e.forEach(n=>{const f=n.getMcDbEntity();f&&s.add(f.layer)}),o=Array.from(s)}if(o.length>0&&o[0]==="")return;I.acutPrintf(`
选定的图层:`+o.join(","));const i=new Q;i.setMessage("选择目标图层上的对象"),i.setKeyWords("名称(N)");const r=await i.go();let d="";if(i.isKeyWordPicked("N"))d=(await Oe())[0];else{if(!r||!r.isValid())return;const s=r.getMcDbEntity();if(!s)return;d=s.layer}I.acutPrintf(`
将要把`+o.length+'个图层合并到图层"'+d+'"中。');const a=new Ze;if(a.setMessage("是否继续?"),a.setKeyWords("[是(Y)/否(N)]"),(await a.go())?.toLocaleUpperCase()==="Y"){const s=new qe;s.allSelect(),s.forEach(x=>{const u=x.getMcDbEntity();u&&o.includes(u.layer)&&(u.layer=d)});const n=ue(),{remove:f,setLayerList:D,stringifyJSON:P}=n,{list:g}=ve(n),m=o.map(x=>g.value.findIndex(u=>u.name===x));f(m),D(P())}});V("_layer_remove",async()=>{let t,e=await F.userSelect("选择要合并的图层上的对象",null,async(m,x)=>{t=x,x.setKeyWords("[命名(N)]")}),o=[];if(t&&t.isKeyWordPicked("N"))o=await Oe({isMultiple:!0});else if(e){const m=new Set;e.forEach(x=>{const u=x.getMcDbEntity();u&&m.add(u.layer)}),o=Array.from(m)}if(o.length>0&&o[0]==="")return;const i=new Ze;i.setMessage("删除图层上的对象"),i.setKeyWords("[删除(D)/不删除对象改为当前图层(C)]");const r=await i.go();if(i.getStatus()===N.kCancel)return;const d=r?.toLocaleUpperCase()==="D",a=yt().getCurrentlyLayerName(),l=ue(),{remove:s,setLayerList:n,stringifyJSON:f}=l,{list:D}=ve(l),P=o.map(m=>D.value.findIndex(x=>x.name===m)),g=s(P);if(n(f()),!g)I.acutPrintf(`删除图层失败
命令`);else{const m=new qe;m.allSelect();const x=g.map(({name:u})=>u);m.forEach(u=>{const c=u.getMcDbEntity();c&&x.includes(c.layer)&&(d?c.erase():c.layer=a)}),I.acutPrintf(`
已删除的图层:`+x.join(",")+`"
命令`)}});async function St(t,e){return new Promise(async o=>{let i=t.hash,r=t.type;const d=t.file,{hideLoading:a,showLoading:l}=ft();if(r===he()){d.source.source.size/(1024*1024)>1&&l();const n=URL.createObjectURL(d.source.source);setTimeout(()=>{Ct(n),setTimeout(()=>{URL.revokeObjectURL(n)},5e3)})}else{let{mxfilepath:n=""}=wt()||{},f=zt(),D=f+n+i+"."+r+he(!0),P=f+n+i+"/___mx___tz___.dwg.mxweb",g=!1;if(!t.isUseServerExistingFile){let w=await dn(D,i);if(!w.ok)return a(),o(!1);w.tz&&(g=!0)}Kt(y("305")+"..."),Ot(!0);var s=new Date().getTime();let m=0;e||(m=Je.EMSCRIPTEN_FETCH_LOAD_TO_MEMORY|Je.EMSCRIPTEN_FETCH_PERSIST_FILE|Je.EMSCRIPTEN_FETCH_REPLACE);let x=$.App.getCurrentMxCAD(),u=!1;Ft.emit("startOpenFile");const c=x.openWebFile(D,w=>{if(a(),u=!0,Bt(),w===0){o(!0);var h=new Date().getTime();if(h-s>5e3&&Ce().success(y("306")+"..."),g){let p=f+"/mxcad/files/tz";Ht.post(p,{fileHash:i}).then(A=>{A&&A.data&&A.data.code==0&&x.getImp().loadTz(P)})}}else o(!1),Ce().error(y("307"))},void 0,void 0,m,!g);gt(D).then(w=>{w/(1024*1024)>1&&c&&!u&&l()}),o(!!c),o(!1)}Ut(d.name)})}let We=me();V("OpenDwg",async()=>{We=me(),await ht(!1,"OpenDwgImp"),We&&xe("MxFullScreen")});V("OpenDwg_DoNotUseCache",async()=>{We=me(),await ht(!0,"OpenDwgImp_DoNotUseCache"),We&&xe("MxFullScreen")});V("OpenDwgImp_DoNotUseCache",async t=>{await St(t,!1)});V("OpenDwgImp",async t=>{await St(t,!0)});V("Mx_SaveAs",bt);V("Mx_QSave",ln);V("Mx_Export_DWG",async()=>{{We=me();const t=()=>{We&&xe("MxFullScreen")};let{baseUrl:e="",saveDwgUrl:o="",mxfilepath:i=""}=wt()||{};e.substring(0,16)=="http://localhost"&&(e=ot()+e.substring(16)),o.substring(0,16)=="http://localhost"&&(o=ot()+o.substring(16));let r=$.getCurrentMxCAD();r.saveFileToUrl(o,(d,a)=>{try{let l=JSON.parse(a);if(l.ret=="ok"){let s=e+i+l.file;fetch(s).then(async n=>{const f=await n.blob();let D=r.getCurrentOriginaFileName();function P(g){const m=g.toLowerCase(),x=m.lastIndexOf(".");if(x!==-1){const u=m.substring(x);if(u===he(!0))return g.substring(0,x)+".dwg";if(u===".dwg"||u===".dxf")return g}return g+".dwg"}D=P(D),await mt.saveAsFileDialog({blob:f,filename:D,types:[{description:"dwg"+y("231"),accept:{"application/octet-stream":[".dwg"]}}]}),t()})}else console.log(a)}catch{console.log("Mx: sserverResult error")}})}});V("Mx_debug",()=>{});V("Mx_clear_buf",()=>{});V("Mx_Array",Qt);V("Mx_NewFile",()=>{{$.getCurrentMxCAD().newFile();const{initLayerList:e}=ue(),{initColorIndexList:o}=ut(),{initLineTypeList:i}=$t();e(),o(),i()}});V("Mx_NewFile_Template",()=>{$.getCurrentMxCAD().openWebFile("empty_template.mxweb")});V("MxPE_DrawMText_old",async()=>{const t=new j;t.setMessage(y("308"));const e=await t.go();if(!e)return;const{open:o}=an(),i=await o();if(typeof i=="boolean")return;const{text:r,size:d}=i,a=new Jt;a.contents=r||"",d&&(a.textHeight=d),a.location=e;const l=$.getCurrentMxCAD(),s=l.drawEntity(a);return l.updateDisplay(),s});
