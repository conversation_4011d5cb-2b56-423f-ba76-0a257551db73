import request from '@/utils/request'

// 查询任务管理详细
export function getManage(objId) {
  return request({
    url: '/system/manage/' + objId,
    method: 'get'
  })
}

// 新增任务管理
export function addManage(data) {
  return request({
    url: '/task_manage/add',
    method: 'post',
    data: data
  })
}

// 修改任务管理
export function updateManage(data) {
  return request({
    url: '/system/manage',
    method: 'post',
    data: data
  })
}

// 删除任务管理
export function delManage(objId) {
  return request({
    url: '/system/manage/' + objId,
    method: 'post'
  })
}
