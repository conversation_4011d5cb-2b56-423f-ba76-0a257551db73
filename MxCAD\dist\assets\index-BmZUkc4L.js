import{r as Le,h as Se,d as J,w as Ne,c as Ae,$ as h,m as C,a4 as L,u,B as Ee,F as oe,A as Ve,K as pe,_ as m,a0 as r,a5 as ve,a3 as ce,Q as K,V as v,H as M,ac as Te,U as he,a9 as Ie,W as Ke,a1 as Pe}from"./vue-Cj9QYd7Z.js";import{q as je,$ as Ue,aj as Ce,ak as We,al as _e,u as $e,r as Re,b as g,am as we,an as Oe,c as Be,a3 as ze,Q as Fe,Z as G,ao as He,a4 as qe,_ as Qe}from"./index-CzBriCFR.js";import{M as Ye}from"./index-itnQ6avM.js";import Ze from"./index-KTGbKNgu.js";import{e as S,d as ue,I as X,M as de,h as Je,Z as be,i as xe}from"./mxcad-DrgW2waE.js";import{M as ee,g as Ge}from"./mxdraw-BQ5HhRCY.js";import"./mapbox-gl-DQAr7S0z.js";import{u as ke}from"./hooks-bxDgWiT9.js";import{L as Xe,b as W,R as P,S as et,k as tt}from"./vuetify-BqCp6y38.js";import"./handsontable-Ch5RdAT_.js";const De=je(),{add:nt,remove:te,putCurrent:ye,toggle:st,setValue:w,setIndex:V,getIndex:Me,stringifyJSON:at,create:qt,initLayerList:lt,setLayerList:it,recoveryLayerStateHistory:rt}=De,{index:c,list:f,currentLayer:ot,rootId:fe}=Ue(De),ct=({onAddLayer:ne})=>{const N=Ce("Shift"),j=Ce("Control"),b=()=>Array.isArray(c.value)&&c.value.length>1,se=(e,s,...n)=>{const[t,a,l]=n;if(N.value||j.value)return e.preventDefault();if(!s.isSelect)return w(t,a,l);if(b())return e.stopPropagation(),w(t,a),!1;w(t,a,l)};let D;const $=(e,s)=>{if(s&&s.target&&O(s.target),N.value){typeof D>"u"&&(D=c.value);const n=Math.max(D,e);V(Oe(n===e?D:e,n));return}if(j.value){let n=[];if(Array.isArray(c.value)){const t=c.value.indexOf(e);t>=0&&c.value.length>1?(n=[...c.value],n.splice(t,1)):n=[...c.value,e]}else n=[c.value,e];V(n);return}V(e),D=e,U()},ae=(e,s)=>{(!b()||!f.value[e].isSelect)&&$(e,s)};let A;const U=()=>{Array.isArray(c.value)?A=c.value[c.value.length-1]:A=c.value},R=()=>{ne&&ne(nt()),N&&(D=f.value.length-1)};let E;const O=e=>{if(e)if(e.tagName==="INPUT")E=e;else{let n=e?.getElementsByTagName("input")[0];n?E=n:O(e.parentNode)}},B=e=>{const s=Me();if(Array.isArray(s)&&s.length>1)return;const n=Array.isArray(s)?s[0]:s;ot.value!==f.value[n]&&f.value[n].id!==fe.value&&e&&setTimeout(()=>{e.focus(),e.select()})},le=(e,s)=>{E=e.target,c.value===s&&B(E)},ie=()=>{A&&V(A),A=void 0},me=()=>{V(f.value.map((e,s)=>s))},ge=()=>{V([]),U()},z=()=>{const e=Array.isArray(c.value)?c.value:[c.value];let s=[];U(),st("isSelect",f.value.map((n,t)=>(e.includes(t)||s.push(t),t))),c.value=s},T=We({hasIcon:!0,iconType:"svg-icon",menuList:[{label:"置为当前",fn:()=>ye(void 0,!0),disabled:b},{label:"新建图层",fn:()=>R()},{label:"删除",fn:()=>te(),disabled:()=>{if(b())return!1;let e=Me(c.value);return e=typeof e=="number"?e:e[0],f.value[e]?.id===fe.value},icon:_e("youjianshanchu")},{label:"重命名",tips:"F2",fn:()=>B(E),disabled:b,icon:_e("youjianzhongmingming")},{line:!0},{label:"全部选择",fn:()=>me()},{label:"全部清除",fn:()=>ge()},{label:"反转选择",fn:()=>z()}]}),{createColor:F}=$e(),H=e=>{Be("Mx_Color",{call:(s,n)=>{w("color",F({...ze(s,n)}),c)},color:e})};Re();const re=e=>{w("lineType",e,c)};let I=!1;const q=e=>{e=typeof e=="boolean"?e:I,w("visible",e,f.value.map((s,n)=>n)),I=!e,Y[3].name=I?"开启所有图层":"关闭所有图层"};g("_OpenAllLayer",()=>{q(!0),i()}),g("_SelOffLayer",async()=>{const e=await S.userSelect("选择需要关闭图层的实体"),s=new Set;e.forEach(n=>{if(n.type===ue.kMxCAD){const t=n.getMcDbEntity();if(!t)return;const a=f.value.findIndex(({name:l})=>l===t.layer);a>=0&&s.add(a)}}),w("visible",!1,Array.from(s)),i()}),g("_layer_recovery",rt),g("_layer_putCurrent",async()=>{const e=new X;e.setMessage("选择将使其图层将成为当前图层的对象");const s=await e.go();if(!s||!s.isValid())return;const n=s.getMcDbEntity();if(!n)return;const t=f.value.findIndex(({name:a})=>n.layer===a);t<0||ye(t)});const x=async e=>{const{dialog:s}=ke();s.showDialog(!0,e);try{return await new Promise((n,t)=>{s.onConfirm(n),s.onCancel(t)})}catch{return[""]}};g("_layer_matching",async()=>{const e=await S.userSelect("选择需要修改图层的对象"),s=new X;s.setMessage("选择匹配图层的对象"),s.setKeyWords("[名称(N)]");const n=await s.go();let t="";if(s.isKeyWordPicked("N")){const{dialog:a}=ke();a.showDialog(!0);try{t=(await x())[0]}catch{return}}else{if(!n||!n.isValid())return;const a=n.getMcDbEntity();if(!a)return;t=a.layer}t!==""&&(e.forEach(a=>{const l=a.getMcDbEntity();l&&(l.layer=t)}),de.getCurrentMxCAD().updateDisplay())}),g("_layer_setEntToCurrentLayer",async()=>{const e=await S.userSelect("选择需要修改图层的对象"),s=we().getCurrentlyLayerName();e.forEach(n=>{const t=n.getMcDbEntity();t&&(t.layer=s)}),de.getCurrentMxCAD().updateDisplay()}),g("_layer_CopyObjectsToNewLayer",async()=>{const e=await S.userSelect("选择要复制的对象"),s=new X;s.setMessage("选择目标图层上的对象"),s.setKeyWords("[名称(N)]");const n=await s.go();let t="";if(s.isKeyWordPicked("N"))t=(await x())[0];else{if(!n||!n.isValid())return;const k=n.getMcDbEntity();if(!k)return;t=k.layer}const a=new Je;a.setMessage("指定基点");let l=await a.go();if(!l)return;a.setMessage("指定位移的第二个点"),a.setUserDraw((k,d)=>{e.forEach(y=>{if(!l)return;const o=y.clone();o&&(o.layer=t,o.move(l,k),d.drawMcDbEntity(o))})});let _=await a.go();e.forEach(k=>{if(!l||!_)return;const d=k.clone();if(!d)return;d.move(l,_);const o=de.getCurrentMxCAD().drawEntity(d).getMcDbEntity();o&&(o.layer=t)})}),g("_layer_freeze",async()=>{const e=await S.userSelect("选择需要冻结图层的对象"),s=new Set;e.forEach(n=>{if(n.type===ue.kMxCAD){const t=n.getMcDbEntity();if(!t)return;const a=f.value.findIndex(({name:l})=>l===t.layer);a>=0&&s.add(a)}}),w("freeze",!0,Array.from(s),!0)}),g("_layer_thawedAll",()=>{w("freeze",!1,f.value.map((e,s)=>s),!0)});const Q=async e=>{const s=await S.userSelect(`选择需要${e?"锁定":"解锁"}图层的对象`),n=new Set;s.forEach(t=>{if(t.type===ue.kMxCAD){const a=t.getMcDbEntity();if(!a)return;const l=f.value.findIndex(({name:_})=>_===a.layer);l>=0&&n.add(l)}}),w("lock",e,Array.from(n),!0)};g("_layer_lock",()=>Q(!0)),g("_layer_unlock",()=>Q(!1)),g("_layer_combined",async()=>{let e,s=await S.userSelect("选择要合并的图层上的对象",null,async(d,y)=>{e=y,y.setKeyWords("[命名(N)]")}),n=[];if(console.log(s),e&&e.isKeyWordPicked("N"))n=await x({isMultiple:!0});else if(s){const d=new Set;s.forEach(y=>{const o=y.getMcDbEntity();o&&d.add(o.layer)}),n=Array.from(d)}if(n.length>0&&n[0]==="")return;ee.acutPrintf(`
选定的图层:`+n.join(","));const t=new X;t.setMessage("选择目标图层上的对象"),t.setKeyWords("名称(N)");const a=await t.go();let l="";if(t.isKeyWordPicked("N"))l=(await x())[0];else{if(!a||!a.isValid())return;const d=a.getMcDbEntity();if(!d)return;l=d.layer}ee.acutPrintf(`
将要把`+n.length+'个图层合并到图层"'+l+'"中。');const _=new be;if(_.setMessage("是否继续?"),_.setKeyWords("[是(Y)/否(N)]"),(await _.go())?.toLocaleUpperCase()==="Y"){const d=new xe;d.allSelect(),d.forEach(o=>{const p=o.getMcDbEntity();p&&n.includes(p.layer)&&(p.layer=l)});const y=n.map(o=>f.value.findIndex(p=>p.name===o));te(y),i()}}),g("_layer_remove",async()=>{let e,s=await S.userSelect("选择要合并的图层上的对象",null,async(y,o)=>{e=o,o.setKeyWords("[命名(N)]")}),n=[];if(e&&e.isKeyWordPicked("N"))n=await x({isMultiple:!0});else if(s){const y=new Set;s.forEach(o=>{const p=o.getMcDbEntity();p&&y.add(p.layer)}),n=Array.from(y)}if(n.length>0&&n[0]==="")return;const t=new be;t.setMessage("删除图层上的对象"),t.setKeyWords("[删除(D)/不删除对象改为当前图层(C)]");const a=await t.go();if(t.getStatus()===Ge.kCancel)return;const l=a?.toLocaleUpperCase()==="D",_=we().getCurrentlyLayerName(),k=n.map(y=>f.value.findIndex(o=>o.name===y)),d=te(k);if(i(),!d)ee.acutPrintf(`删除图层失败
命令`);else{const y=new xe;y.allSelect();const o=d.map(({name:p})=>p);y.forEach(p=>{const Z=p.getMcDbEntity();Z&&o.includes(Z.layer)&&(l?Z.erase():Z.layer=_)}),ee.acutPrintf(`
已删除的图层:`+o.join(",")+`"
命令`)}});const Y=Le([{name:"新增图层",fun:()=>{R()}},{name:"删除图层",fun:()=>te()},{name:"置为当前",fun:()=>ye()},{name:"关闭所有图层",fun:()=>q()}]),i=()=>{it(at())};return{onClickLayer:$,onClickStopTD:se,onClickLayerName:le,resumeIndex:ie,reverseSelection:z,selectColor:H,selectLineType:re,setIndex:V,onRightClickLayer:ae,initLayerList:lt,setLayerList:i,list:f,bodyRightClickMenuOptions:T,isShiftKeyMultipleChoice:N,isCtrlKeyMultipleChoice:j,btnList:Y,rootId:fe}},ut={class:"d-flex align-center"},dt={class:"d-flex align-center my-2"},yt={class:"w-100",style:{"z-index":"1"}},ft={class:"w-100"},mt={class:"w-20"},gt={class:""},pt={class:""},vt={class:""},ht={class:"w-20"},Ct={class:"w-20"},_t={class:"w-100",ref:"tbody"},wt=["id","onClick","onContextmenu"],bt={class:"text-no-wrap"},xt={style:{width:"18px"},class:"d-inline-block"},kt=["for"],Mt=["disabled","id","onClick","onUpdate:modelValue"],Dt=["onClick"],Lt=["onClick"],St=["onClick"],Nt=["onClick"],At=["onClick"],Et={class:"w-100 my-3",cellpadding:"20"},Vt={class:"w-100"},Tt={class:"w-100"},It={class:"w-auto px-6"},Kt={class:"d-flex justify-end w-auto"},Pt=Se({__name:"index",setup(ne){const N=J(),{onClickLayer:j,onClickStopTD:b,onClickLayerName:se,resumeIndex:D,reverseSelection:$,selectColor:ae,selectLineType:A,setIndex:U,onRightClickLayer:R,initLayerList:E,setLayerList:O,list:B,bodyRightClickMenuOptions:le,btnList:ie,isShiftKeyMultipleChoice:me,isCtrlKeyMultipleChoice:ge,rootId:z}=ct({onAddLayer(){const e=(N.value?.$el).getElementsByClassName("v-table__wrapper")[0];Ve(()=>{e.scrollTo({top:e.scrollHeight-e.clientHeight,behavior:"smooth"})})}}),{isShow:T,showDialog:F}=Fe(!1,"MxLayerManager");Ne(T,i=>{i&&E()});const H=J([]),re=i=>{const{map:e}=i(H.value,(s,n)=>n);e.length>0&&U(e)},I=J(),q=i=>{I.value?.showDialog(!0,i)},x=J(""),Q=qe(i=>{i===null&&(i=""),x.value=i},500),Y=Ae(()=>B.value.filter(i=>i.name.toLowerCase().includes(x.value.toLowerCase())));return(i,e)=>{const s=pe("box-selection"),n=pe("right-click-menu");return m(),h(oe,null,[C(Ye,{maxWidth:"800",ref:"layerDialog",modelValue:u(T),"onUpdate:modelValue":e[4]||(e[4]=t=>Ee(T)?T.value=t:null),title:i.t("536")},{actions:L(()=>e[9]||(e[9]=[r("div",{class:"mt-1"},null,-1)])),default:L(()=>[r("div",ut,[r("div",dt,[(m(!0),h(oe,null,ve(u(ie),(t,a)=>(m(),ce(G,{onClick:t.fun,class:"mx-2 px-2",key:t.name+a},{default:L(()=>[K(v(i.t(t.name)),1)]),_:2},1032,["onClick"]))),128))]),C(Xe,{"model-values":x.value,"onUpdate:modelValue":u(Q),class:"pa-1",clearable:"",density:"compact",label:i.t("63"),variant:"solo","hide-details":"","single-line":""},{"append-inner":L(()=>[C(W,{icon:"$mdi-magnify",class:"v-icon--clickable"})]),_:1},8,["model-values","onUpdate:modelValue","label"])]),M((m(),ce(u(et),{class:"w-100 layer-table",height:"300",ref_key:"boxRef",ref:N,onClick:e[0]||(e[0]=t=>u(D)())},{default:L(()=>[r("thead",yt,[r("tr",ft,[r("th",mt,[e[5]||(e[5]=r("div",{style:{width:"10px"},class:"d-inline-block"},null,-1)),e[6]||(e[6]=K()),r("span",null,v(i.t("188")+i.t("209")),1)]),r("th",gt,v(i.t("223")),1),r("th",pt,v(i.t("224")),1),r("th",vt,v(i.t("225")),1),r("th",ht,v(i.t("226")),1),r("th",Ct,v(i.t("227")),1)])]),M((m(),h("tbody",_t,[(m(!0),h(oe,null,ve(Y.value,(t,a)=>(m(),h("tr",{class:Te(["text-center layer-info",t.isSelect?"active":""]),ref_for:!0,ref_key:"refItems",ref:H,key:t.id,id:t.id.toString(),onClick:he(l=>u(j)(a),["prevent"]),onContextmenu:he(l=>u(R)(a,l),["prevent"])},[M((m(),h("td",bt,[r("div",xt,[t.status?(m(),ce(W,{key:0,icon:"class:iconfont gou",size:"x-small",color:"#16FD21"})):Ie("",!0)]),r("label",{class:"d-inline-block",for:t.id.toString()},[M(r("input",{disabled:t.id===u(z),id:t.id.toString(),class:"text-truncate text-center",type:"text",onClick:l=>u(se)(l,a),"onUpdate:modelValue":l=>t.name=l},null,8,Mt),[[Ke,t.name]])],8,kt)])),[[P]]),M((m(),h("td",{class:"text-orange",onClick:l=>u(b)(l,t,"visible",!t.visible,a)},[C(W,{icon:t.visible?"yanjing1":"yanjing"},null,8,["icon"])],8,Dt)),[[P,void 0,void 0,{prevent:!0}]]),M((m(),h("td",{class:"text-orange",onClick:l=>u(b)(l,t,"lock",!t.lock,a)},[C(W,{icon:t.lock?"suo":"jiesuo1"},null,8,["icon"])],8,Lt)),[[P]]),M((m(),h("td",{onClick:l=>u(b)(l,t,"print",!t.print,a)},[C(W,{icon:t.print?"dayin":"budayinbiaoqian"},null,8,["icon"])],8,St)),[[P]]),M((m(),h("td",{class:"d-flex align-center justify-center",onClick:l=>u(ae)(t.color)},[r("div",{class:"colorBox mr-2",style:Pe({background:`${t.color.color}`})},null,4),r("span",null,v(u(He)(t.color.name)),1)],8,Nt)),[[P]]),M((m(),h("td",{class:"w-20 text-truncate",onClick:l=>q(t.lineType)},[K(v(t.lineType.name),1)],8,At)),[[P]])],42,wt))),128))])),[[s,re]])]),_:1})),[[n,u(le)]]),C(tt,{color:"#576375",class:"border-opacity-75"}),r("table",Et,[r("thead",Vt,[r("tr",Tt,[e[7]||(e[7]=r("th",{class:"w-20"},null,-1)),r("th",It,[C(G,{onClick:e[1]||(e[1]=t=>u($)())},{default:L(()=>[K(v(i.t("228")),1)]),_:1})]),e[8]||(e[8]=r("th",{class:"w-50"},null,-1)),r("th",Kt,[C(G,{isAction:"",primary:"",onClick:e[2]||(e[2]=t=>(u(O)(),u(F)(!1)))},{default:L(()=>[K(v(i.t("229")),1)]),_:1}),C(G,{class:"ml-10 mr-6",onClick:e[3]||(e[3]=t=>u(F)())},{default:L(()=>[K(v(i.t("230")),1)]),_:1})])])])])]),_:1},8,["modelValue","title"]),C(Ze,{ref_key:"linearManager",ref:I,onChange:u(A)},null,8,["onChange"])],64)}}}),Qt=Qe(Pt,[["__scopeId","data-v-be664fe3"]]);export{Qt as default};
