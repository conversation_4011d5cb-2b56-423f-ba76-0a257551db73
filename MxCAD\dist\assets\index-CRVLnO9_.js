import{Q as w,U as p,Z as u,V as c}from"./index-CzBriCFR.js";import{M as y}from"./index-itnQ6avM.js";import{a1 as B,L as C,G as x,H as d,I as v,h as I,ab as L,ac as D,a as F,j as M}from"./vuetify-BqCp6y38.js";import{h as T,d as j,a3 as r,a4 as t,u as G,B as N,_ as o,a0 as m,m as e,Q as s,V as l,$ as R,a5 as S,F as Q}from"./vue-Cj9QYd7Z.js";import"./mxcad-DrgW2waE.js";import"./mxdraw-BQ5HhRCY.js";import"./handsontable-Ch5RdAT_.js";import"./mapbox-gl-DQAr7S0z.js";const U={class:"px-3 mt-2"},W={class:"d-flex justify-space-between w-100"},$={class:"d-flex justify-space-between align-center mt-2"},P=T({__name:"index",setup(A){const{isShow:n,showDialog:_}=w(!1,"Mx_FindText_old"),V=[{name:"确定",fun:()=>{},primary:!0},{name:"关闭",fun:()=>_(!1)}],b=j([{title:"小数",value:1},{title:"小数",value:2},{title:"小数",value:3},{title:"小数",value:3},{title:"小数",value:1},{title:"小数",value:2},{title:"小数",value:3},{title:"小数",value:3}]);return(a,f)=>(o(),r(y,{title:a.t("652"),"max-width":"450",modelValue:G(n),"onUpdate:modelValue":f[0]||(f[0]=i=>N(n)?n.value=i:null),footerBtnList:V},{default:t(()=>[m("div",U,[e(p,{"key-name":"W",colon:""},{default:t(()=>[s(l(a.t("653")),1)]),_:1}),e(B,{class:"mb-1",items:[]},{append:t(()=>[e(u,null,{default:t(()=>[s(l(a.t("256")),1)]),_:1})]),_:1}),e(p,{"key-name":"W",colon:""},{default:t(()=>[s(l(a.t("654")),1)]),_:1}),e(C,{class:"",items:[]},{append:t(()=>[e(u,null,{default:t(()=>[s(l(a.t("264")),1)]),_:1})]),_:1}),e(c,{title:a.t("655")},{default:t(()=>[e(x,{class:""},{default:t(()=>[m("div",W,[e(d,{label:a.t("656"),value:0},null,8,["label"]),e(d,{label:a.t("657"),value:1},null,8,["label"]),e(d,{label:a.t("658"),value:2},null,8,["label"])])]),_:1}),m("div",$,[e(v,{class:""},{label:t(()=>[s(l(a.t("659")),1)]),_:1}),e(u,null,{default:t(()=>[s(l(a.t("660")),1)]),_:1})])]),_:1},8,["title"]),e(c,{title:a.t("661")},{default:t(()=>[e(v,{class:""},{label:t(()=>[s(l(a.t("662")),1)]),_:1}),e(I,{border:"",height:"140",density:"compact",variant:"text"},{default:t(()=>[e(L,{multiple:"",class:"mr-8"},{default:t(()=>[(o(!0),R(Q,null,S(b.value,(i,h)=>(o(),r(D,null,{default:t(({isSelected:g,toggle:k})=>[(o(),r(F,{key:h,value:i,"active-class":"bg-light-blue-darken-2",active:g,onClick:k},{default:t(()=>[e(M,{textContent:l(i.title)},null,8,["textContent"])]),_:2},1032,["value","active","onClick"]))]),_:2},1024))),256))]),_:1})]),_:1})]),_:1},8,["title"])])]),_:1},8,["title","modelValue"]))}});export{P as default};
