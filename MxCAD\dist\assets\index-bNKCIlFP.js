import{M as C}from"./index-itnQ6avM.js";import{u as U}from"./hooks-IXT-OJS-.js";import{T as k}from"./TextEllipsis-C4aaa1NU.js";import{n as D,p as A,z as E,_ as B}from"./index-CzBriCFR.js";import{b as F,J as T,S as z,t as L}from"./vuetify-BqCp6y38.js";import{h as N,d as P,c as R,a3 as c,a4 as f,u as g,_ as l,a0 as s,m as p,V as _,$ as m,a5 as M,F as j,a9 as H}from"./vue-Cj9QYd7Z.js";import"./mxcad-DrgW2waE.js";import"./mxdraw-BQ5HhRCY.js";import"./handsontable-Ch5RdAT_.js";import"./mapbox-gl-DQAr7S0z.js";const J={class:"mt-2"},X={style:{"z-index":"1"}},$={class:"text-center"},q={class:"text-left"},G={style:{height:"26px"}},K={class:"d-flex justify-center",style:{width:"30px"}},O={key:0,class:"border_box"},Q={style:{height:"3px"}},W=N({__name:"index",setup(Y){const{dialog:r}=U(),n=P([]),x=e=>{if(e===0)return"class:iconfont gou";if(e===1)return"class:iconfont cha"},v=e=>{if(e===0)return"#33CD2A";if(e===1)return"#ff0000"};r.onReveal(e=>{const t=[];e.externalReference.forEach(a=>{t.push({name:a,uploadState:3,progress:0,type:"ref",hash:e.hash})}),e.images.forEach(a=>{t.push({name:a,uploadState:3,progress:0,hash:e.hash,type:"img"})}),n.value=t});const y=()=>{let{baseUrl:e=""}=D()||{};e.substring(0,16)=="http://localhost"&&(e=A()+e.substring(16));const t=document.createElement("input");t.setAttribute("type","file"),t.setAttribute("accept",".dwg,image/*"),t.style.display="none",t.setAttribute("multiple","multiple"),t.setAttribute("capture","camera"),document.body.appendChild(t),t.onchange=()=>{if(!t.files||t.files.length<1)return;const a=Array.from(t.files);n.value.filter(o=>a.some(d=>{if(d.name===o.name)return o.source=d,!0})).forEach(o=>{const{type:d,name:w,source:h,hash:V}=o;o.uploadState=2;const u=new FormData;h&&u.append("file",h),u.append("src_dwgfile_hash",V),u.append("ext_ref_file",w),E({url:e+(d==="img"?"/mxcad/up_ext_reference_image":"/mxcad/up_ext_reference_dwg"),method:"post",headers:{"Content-Type":"multipart/form-data"},data:u,onUploadProgress(i){i.total&&(o.progress=i.loaded/i.total*100,console.log(o.progress))}}).then(i=>{i.data.code===0?(o.uploadState=0,t.remove()):(o.uploadState=1,t.remove())},()=>{o.uploadState=1,t.remove()})})},setTimeout(()=>{t.click()},100)},S=R(()=>n.value.some(e=>e.uploadState===2)),b=[{name:"选择文件",fun:y,disabled:()=>n.value.every(e=>e.uploadState===0)},{name:"取消",fun:()=>{r.cancel(!1),r.showDialog(!1)}},{name:"继续打开文件",fun:()=>{r.confirm(!0),r.showDialog(!1)}}];return(e,t)=>(l(),c(C,{title:e.t("642"),"max-width":"450",modelValue:g(r).isShow.value,"onUpdate:modelValue":t[0]||(t[0]=a=>g(r).isShow.value=a),footerBtnList:b},{default:f(()=>[s("div",J,[p(z,{height:"150",class:"mb-2",hover:!1},{default:f(()=>[s("thead",X,[s("tr",null,[s("th",null,_(e.t("643")),1),s("th",$,_(e.t("644")),1)])]),s("tbody",q,[(l(!0),m(j,null,M(n.value,(a,o)=>(l(),m("tr",G,[s("td",null,[s("div",K,[a.uploadState!==2?(l(),m("div",O,[p(F,{icon:x(a.uploadState),color:v(a.uploadState),size:"16"},null,8,["icon","color"])])):(l(),c(T,{key:1,rotate:360,size:16,width:2,"model-value":a.progress,color:"teal",indeterminate:a.progress===100},null,8,["model-value","indeterminate"]))])]),s("td",null,[p(k,{class:"text-left",width:285,text:a.name,"slice-num":10},null,8,["text"])])]))),256))])]),_:1}),s("div",Q,[S.value?(l(),c(L,{key:0,indeterminate:"",color:"green"})):H("",!0)])])]),_:1},8,["title","modelValue"]))}}),ie=B(W,[["__scopeId","data-v-89b132bf"]]);export{ie as default};
