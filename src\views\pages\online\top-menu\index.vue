<script setup>
import ListDialog from "@/components/ListDialog/index.vue";
import { ElMessage } from "element-plus";
import {
  copyEngineeringReuse,
  selectEngineeringByCondition,
} from "@/api/desginManage/GccsDialog.js";
import useAppStore from "@/store/modules/app.js";
import Hamburger from "@/components/Hamburger/index.vue";
import { useIframeCommunication } from "@/hooks/useIframeCommunication.js";
import { useRoute } from "vue-router";
import {
  getQianVersionId,
  getAllRodType,
  getBasicForm,
  getCableChannelDetails,
  intervalAddFile,
  getTowerDetails,
  chengZip
} from "@/api/insertSag/index.js";
import { getTuQianFileByPath } from "@/api/onlineDesign/index.js";
import { getLegendState } from "../commonData.js";
import { readStationData } from "@/api/insertSag/index.js";
const { sendMessage, cleanup } = useIframeCommunication();
const urlRoute = new useRoute();
const appStore = useAppStore();
const taskId = new URLSearchParams(new URL(window.location.href).search).get(
    "id"
  );
  const callChildC = inject('callChildC');
const isCollapse = computed(() => !appStore.sidebar.opened);
const { proxy } = getCurrentInstance();
const listNavbar = ref([]);
const dialogPositionStyle = ref({});
const flag = ref(false);
/**
 * type: 'onlycad' 仅在cad页面运行功能 搭配使用  cmd:命令
 * type: 'sdkcad' 功能在sdk和cad页面通用 搭配使用 cmd: 命令  cmd_sdk: 命令
 * type: 'backgroundcad' 使用cad功能 无限制在sdk还是cad页面  搭配使用  cmd:命令
 * type: 'MsgCad' 通过发送消息传递数据内容的cad命令
 */
const listData = ref([
{
    name: "带电作业",
    value: "a1.png",
  },
  {
    name: "线路绘制",
    value: "a1.png",
  },
  {
    name: "柱上变压器绘制",
    value: "a2.png",
  },
  {
    name: "柱上设备绘制",
    value: "a3.png",
  },
  {
    name: "拉线绘制",
    value: "a11.png",
  },

  {
    name: "附属设施绘制",
    value: "a4.png",
  },
  {
    name: "同杆并架绘制",
    value: "a5.png",
  },
  {
    name: "导线绘制",
    value: "a6.png",
  },
  {
    name: "交叉跨域",
    value: "a7.png",
    cmd: "KY",
  },
  {
    name: "插入杆塔",
    value: "a8.png",
  },
  {
    name: "低压下户",
    value: "a9.png",
  },
  {
    name: "接地绘制",
    value: "a10.png",
  },
]);
let list1 = ref([
  {
    name: "绘制电缆路径",
    value: "b1.png",
  },
  {
    name: "插入电缆头",
    value: "b2.png",
  },
  {
    name: "绘制电缆分支箱",
    value: "b3.png",
  },
  {
    name: "绘制电缆通道",
    value: "b4.png",
  },
  {
    name: "绘制电缆井",
    value: "b5.png",
  },
  {
    name: "通道内敷设位置图",
    value: "b6.png",
  },
]);
let list2 = ref([
  {
    name: "绘制路径图元",
    value: "c1.png",
  },
  {
    name: "电气一次图设计",
    value: "c2.png",
    component: "ElectricalSchematicDesign",
  },
  // cmd: "JGFZ",
  // content: "JGFZH_Draw",
  //   type:"onlycad",
  //   cmd:"JGFZH_Draw"
  {
    name: "电气平面布置图绘制",
    value: "c3.png",
  
  },
  {
    name: "间隔距离校验",
    value: "c4.png",
    content: "JGJLJY",
    type:"onlycad",
    cmd:"JGJLJY"
  },
  {
    name: "变压器增容",
    value: "c5.png",
  },
]);
let list3 = ref([
  {
    name: "标注设置管理",
    value: "d1.png",
  },
  {
    name: "标注设备",
    value: "d2.png",
    component: "EquipmentAnnotation",
    componentType: "main",
  },
  {
    name: "标注电缆",
    value: "d3.png",
    component: "CableAnnotation",
    componentType: "main",
  },
  {
    name: "标注附属设备",
    value: "d4.png",
    component: 'EquipmentAncillaryFacilities',
    componentType: 'main'
  },
  {
    name: "标注图例",
    value: "d5.png",
  },
  {
    name: "标注其他",
    value: "d6.png",
    component: 'ElseAnnotation',
    componentType: 'main'
  },
]);
let list4 = ref([
  {
    name: "绘制其他图元",
    value: "f1.png",
  },
  {
    name: "自动匹配图纸",
    value: "f2.png",
  },
  {
    name: "插入图纸",
    value: "f3.png",
  },
  {
    name: "图签编辑",
    value: "f4.png",
    component: "TitleBlockEdit",
  },
  {
    name: "插入图框",
    value: "f5.png",
  },
  {
    name: "图纸拆分",
    value: "f6.png",
    component: "DrawingSplitting",
    componentType: "main",
  },
  {
    name: "图纸目录",
    value: "f7.png",
  },
  {
    name: "图纸分幅",
    value: "f8.png",
  },
  {
    name: "批量打印",
    value: "f9.png",
  },
  // {
  //   name: "插入背景图",
  //   value: 'f10.png',
  // },
  // {
  //   name: "下载地图",
  //   value: 'f12.png',
  // },
  // {
  //   name: "分阶段数字化移交",
  //   value: 'f13.png',
  // },
  // {
  //   name: "竣工数字化移交",
  //   value: 'f14.png',
  // },
  // {
  //   name: "推送图模数据",
  //   value: 'f24.png',
  // },
  {
    name: "导入测绘数据",
    value: "f25.png",
  },
  {
    name: "导入移动勘测数据",
    value: "f15.png",
  },
  {
    name: "设计合理性分析",
    value: "f16.png",
  },
  {
    name: "设计完整性分析",
    value: "f16.png",
  },
  {
    name: "三率自查",
    value: "f16.png",
  },
  {
    name: "设备筛选",
    value: "f17.png",
    component: "EquipmentScreening",
    componentType: "main",
  },
  {
    name: "设备选型",
    value: "f18.png",
    component: "PlantModelSelection",
    componentType: "main",
  },
  {
    name: "数据刷新",
    value: "f19.png",
  },
  {
    name: "线夹匹配",
    value: "f20.png",
  },
  {
    name: "修改比例",
    value: "f21.png",
  },
  {
    name: "编号重排",
    value: "f22.png",
    component: "NumberingRearrangement",
  },
  {
    name: "格式刷",
    value: "f23.png",
  },
]);
let list5 = ref([
  {
    name: "杆塔明细表",
    value: "e1.png",
    type: "MsgCad",
    content: "GTMX",
  },
  {
    name: "杆型一览图",
    value: "e2.png",
    type: "MsgCad",
    content: "GXYL",
  },
  {
    name: "基础形式一览图",
    value: "e3.png",
    type: "MsgCad",
    content: "JCYL",
  },
  {
    name: "应力弧垂表",
    value: "e4.png",
  },
  {
    name: "钢管杆明细表",
    value: "e5.png",
  },
  {
    name: "电缆附属设施统计表",
    value: "e6.png",
  },
  {
    name: "电缆明细表",
    value: "e17.png",
  },
  {
    name: "电缆通道土建表",
    value: "e7.png",
    type: "MsgCad",
    content: "DLTDTJ",
  },
  {
    name: "导出工程设计成果",
    value: "e8.png",
  },
  {
    name: "主要设备材料清单",
    value: "e9.png",
  },
  {
    name: "拆旧物资清册",
    value: "e11.png",
  },
  {
    name: "利旧物资表",
    value: "e12.png",
  },
  {
    name: "技经提资",
    value: "e13.png",
  },
]);
let list7 = ref([
  {
    name: "回退",
    cmd: "Mx_Undo",
    cmd_sdk: "undo",
    type: "sdkcad",
    value: "",
  },
  {
    name: "重做",
    cmd: "Mx_Redo",
    cmd_sdk: "redo",
    type: "sdkcad",
    value: "",
  },
  {
    name: "删除",
    cmd: "Mx_Erase",
    cmd_sdk: "removeDevice",
    type: "sdkcad",
    value: "",
  },
]);
const list = ref([
  // {
  //   name: "项目管理",
  //   flag: true,
  //   children: [
  //     // {
  //     //   name: "成果复用",
  //     //   value: "b1.png",
  //     //   cmd: "SDK_versionExtend",
  //     // },
  //     // {
  //     //   name: "成果上报",
  //     //   value: 'b1.png',
  //     //   cmd: ''
  //     // },
  //   ],
  // },
  {
    name: "架空线路设计",
    flag: true,
    children: listData,
  },
  {
    name: "电缆线路设计",
    flag: true,
    children: list1,
  },
  {
    name: "配电站房设计",
    flag: true,
    children: list2,
  },
  {
    name: "标注管理",
    flag: true,
    children: list3,
  },
  {
    name: "工具管理",
    flag: true,
    children: list4,
  },
  {
    name: "成果输出管理",
    flag: true,
    children: list5,
  },
  {
    name: "文件(F)",
    flag: false,
    children: [],
  },
  {
    name: "编辑(E)",
    flag: true,
    children: list7,
  },
  {
    name: "插入(I)",
    flag: false,
    children: [],
  },
  {
    name: "视图(V)",
    flag: false,
    children: [],
  },
  {
    name: "格式(O)",
    flag: false,
    children: [],
  },
  {
    name: "工具(A)",
    flag: false,
    children: [],
  },
]);
const info = ref({});
const active = ref();
const handleClick = (item, index) => {
  console.log("handleClick");
  info.value = {
    item: item,
    index: index,
  };
  active.value = index;
  const li = document.querySelectorAll(".li")[index];
  const list = document.querySelectorAll(".li .onlineFlase");
  list.forEach((lit) => {
    lit.style.removeProperty("background-image");
  });
  if (li && li.classList.contains("active")) {
    li.classList.remove("active");
    flag.value = false;
    appStore.clickData(false);
  } else {
    li.classList.add("active");
    // 获取指定索引的 li 元素
    const litElements = document.querySelectorAll(".li")[index];
    // 获取该元素的所有子元素
    const directChildren = litElements.children; // 只获取直接子元素
    // 创建一个数组，用来存储包含 'onlineFlase' 类的子元素
    const matchingChildren = [];
    // 遍历所有直接子元素，检查是否包含 'onlineFlase' 类
    Array.from(directChildren).forEach((child) => {
      if (child.classList.contains("onlineFlase")) {
        matchingChildren.push(child); // 如果包含 'onlineFlase' 类，将该子元素加入数组
      }
    });
    // 输出所有匹配的子元素
    if (matchingChildren.length > 0) {
      console.log('找到以下包含 "onlineFlase" 类的子元素：');
      matchingChildren.forEach((child) => {
        child.style.setProperty(
          "background-image",
          "url(/src/assets/images/在线设计/whiteRight.png)",
          "important"
        );
      });
    } else {
      // console.log('没有找到包含 "onlineFlase" 类的子元素');
    }
    flag.value = true;
  }
  // 获取当前点击的 .li 元素
  const liElement = document.querySelectorAll(".li")[index];
  // 获取 .li 元素的位置信息
  console.log("liElement", liElement);
  const rect = liElement.getBoundingClientRect();
  console.log("rect", rect);
  let widthBox = 0;
  if (item.name == "工具管理") {
    widthBox = 190;
  } else {
    widthBox = 184;
  }
  let widRect = 0;
  console.log("isCollapse.value", isCollapse.value);
  /*if (isCollapse.value) {
    widRect = rect.left - 275;
  } else {
    widRect = rect.left - 10;
  }*/
  widRect = rect.left - 10;
  // 计算 list-dialog 组件的位置
  dialogPositionStyle.value = {
    position: "absolute", // 设置为绝对定位
    left: `${widRect}px`, // 水平定位
    top: `${rect.bottom + window.scrollY}px`, // 垂直定位（显示在 .li 元素下方）
    width: `${widthBox}px`, // 设置弹框宽度（可以根据需求动态调整）
  };

  listNavbar.value = [];
  if (active.value == 0) {
    listNavbar.value = listData.value;
  } else if (active.value == 1) {
    listNavbar.value = list1.value;
  } else if (active.value == 2) {
    listNavbar.value = list2.value;
  } else if (active.value == 3) {
    listNavbar.value = list3.value;
  } else if (active.value == 4) {
    listNavbar.value = list4.value;
  } else if (active.value == 5) {
    listNavbar.value = list5.value;
  } else if (active.value == 7) {
    listNavbar.value = list7.value;
  } else {
    listNavbar.value = [];
  }
};
const  queryStationData=(id)=>{
readStationData(taskId,id).then(res=>{
    if(res.data){
      let list=[]
      let arr=JSON.parse(res.data.privatepropertys)
    if(arr.HighVoltage?.length>0){
      list.push(...arr.HighVoltage)
    }else{
      proxy.$message.warning('当前站房无间隔,请添加间隔')
      return;
    }
    
    let arr1=[]
    for (let index = 0; index < list.length; index++) {
    let obj={}
      obj.name = 'G'+(index+1);
      arr1.push(obj)
    }
   const params = {
      content: "JGFZH_Draw",
      type:"onlycad",
      options: {
        tableData: arr1,
        name:'JGFZH_Draw'
      }
  }
  sendMessage(cadAppRef.value, params, (res) => {
  })
    }else{
      proxy.$message.warning('当前站房无间隔,请添加间隔')
      return;
    }
  })
}

const buildZip=()=>{
  chengZip(taskId).then(blob=>{
    if(blob.type=='application/json'){
      proxy.$message.warning('dwg文件生成失败')
      return
    }
    const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = '导出工程设计成果';
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
  })
}

const oniframeMessage = (param) => {
  const myiframe =appStore.iframe
  if (myiframe && myiframe.contentWindow) {
    myiframe.contentWindow.postMessage(param, "*");
    // 先移除之前的事件监听器，防止重复绑定
    window.removeEventListener('message', handleMessage);
    // 添加新的事件监听器
    window.addEventListener('message', handleMessage);
  }
}
const handleMessage = async (event) => {
  console.log("🚀 ~ handleMessage ~ event:", event)
  
  // 1. 验证消息结构
  if (!isValidCadMessage(event)) return;

  try {
    let obj= event.data.params.formData
    const formData = new FormData();
    formData.append('taskId', taskId);
      const newFileName = `电气平面布置图.dwg`;
      formData.append('files', obj.files[0], newFileName);
      intervalAddFile(formData).then(res=>{
        if (res?.code === 200) {
      proxy.$message.success("保存成功");
      callChildC?.();
    }
      })
  } catch (error) {
    console.error("文件处理失败:", error);
  }
};

// 辅助函数：验证消息结构
function isValidCadMessage(event) {
  return event.data?.type === 'parentCad' && 
         event.data?.params?.content === '电气平面布置图绘制';
}

const cadAppRef = inject("cadAppRef");
const updateList = async (show, item) => {
  console.log("当前点击菜单", item);
  const myiframe = document.querySelector("#myiframe").querySelector("iframe");
  const myiframehide = document.querySelector("#myiframehide");
  if (!show && item.name === "成果复用") {
    // const { stage, proProcessId, proCode, engCode, workCode, processCode, id } =
    //   urlRoute.query;
    // if (!stage || parseInt(stage) <= 2) {
    //   // 需求、可研不能复用
    //   ElMessage.warning("当前阶段不能复用！");
    //   return;
    // }
    // const params = {
    //   projectWholeprocessId: proProcessId ?? "",
    //   projectCode: proCode ?? "",
    //   workOrdercode: "", //workCode ?? '',
    //   engineeringCode: engCode ?? "",
    //   processCode: "", //processCode ?? '',
    //   stage: parseInt(stage) - 1, //上一个阶段
    // };
    // console.log("查询上一个阶段的工程版本", params);
    // selectEngineeringByCondition(params).then((res) => {
    //   if (res.code === 200 && res.data && res.data.length > 0) {
    //     if (!res.data[0].versionId) {
    //       ElMessage.warning("未能找到上一阶段的工程版本，无法复用！");
    //       removeActiveMenuItem();
    //     } else {
    //       // 接口调用成功在调用sdk工程复用
    //       copyEngineeringReuse({ id: id ,id2:''}).then((res1) => {
    //         console.log("接口工程复用engineeringReuse", res1);
    //         if (res1.code === 200) {
    //           const targetProjectName = urlRoute.query.projectName;
    //           const matchedItem = res.data.find(item => item.projectName === targetProjectName);
    //           let options = {};
    //         if (matchedItem) {
    //             options.extendVersionId = matchedItem.versionId;
    //           } else {
    //             ElMessage.warning("未找到匹配的工程版本！");
    //             removeActiveMenuItem();
    //             return;
    //           }
    //           sendMessage(
    //             cadAppRef.value,
    //             { type: "greeting", content: item.cmd, options },
    //             (res2) => {
    //               // getQianVersionId(params.projectWholeprocessId,params.stage).then(res2=>{
    //               //   if(res2.code===200){
    //               ElMessage.success("成功复用成功！");
    //               //   }
    //               // })
    //               console.log("子页面返回的数据", res2);
    //               removeActiveMenuItem();
    //             }
    //           );
    //         } else {
    //           removeActiveMenuItem();
    //         }
    //       });
    //     }
    //   }
    //   console.log("selectEngineeringByCondition", res);
    // });
    //return
  } else if (item.cmd && myiframe && myiframe.contentWindow) {
    const msgType = item.type || "greeting";
    const content =
      item.type === "sdkcad" ? item.cmd_sdk + "|" + item.cmd : item.cmd;
    myiframe.contentWindow.postMessage({ type: msgType, content }, "*");
  } else if (item.type === "MsgCad" && myiframehide && myiframehide.contentWindow) {
    if (item.content === "GXYL") {
      const res = await getAllRodType({ taskId: urlRoute.query.id });
      let fileBlobArr = [];
      if(res.code==200){
        if(res.data.length===0){
          proxy.$message.warning("目前暂无数据可生成");
          return
        }
        for (let i = 0; i < res.data.length; i++) {
                if (res.data[i].drawPath) {
                  res.data[i].fileBlob = await getTuQianFileByPath({
                    filePath: res.data[i].drawPath,
                  });
                }
                fileBlobArr.push({
                  fileBlob: res.data[i].fileBlob,
                  drawPath: res.data[i].drawPath,
                });
              }
              postMessageMsgCadReport(item, res.data, fileBlobArr);
      }else{
        proxy.$message.warning(res.msg);
      }
      
    } else if (item.content === "JCYL") {
      const res = await getBasicForm({ taskId: urlRoute.query.id });
      let fileBlobArr = [];
      if(res.code==200){
        if(res.data.length===0){
          proxy.$message.warning("目前暂无数据可生成");
        }else{
          for (let i = 0; i < res.data.length; i++) {
        if (res.data[i].draw) {
          res.data[i].fileBlob = await getTuQianFileByPath({
            filePath: res.data[i].draw,
          });
        }
        fileBlobArr.push({
          fileBlob: res.data[i].fileBlob,
          drawPath: res.data[i].draw,
        });
      }
      postMessageMsgCadReport(item, res.data, fileBlobArr);
        }
      }else{
        proxy.$message.warning(res.msg);
      }
      
    } else if (item.content === "DLTDTJ") {
      getCableChannelDetails({projectId:urlRoute.query.id }).then(res=>{
        if(res.code==200){
          if(res.data.length===0){
          proxy.$message.warning("目前暂无数据可生成");
        }else{
          res.data.forEach(element => {
            element.state = getLegendState(element.state)
          });
          postMessageMsgCadReport(item, res.data, []);
        }
        }else{
          proxy.$message.warning(res.msg);
        }
        
      })
    } else if(item.content === "GTMX") {
      getTowerDetails({taskId:urlRoute.query.id }).then(res=>{
        if(res.code==200){
          if(res.data.length===0){
          proxy.$message.warning("目前暂无数据可生成");
        }else{
          res.data.forEach(element => {
            element.towerStatus = getLegendState(element.towerStatus)
            element.wireStatus = getLegendState(element.wireStatus)
            element.towerPoleStatus = getLegendState(element.towerPoleStatus)
          });
          postMessageMsgCadReport(item, res.data, []);
        }
        }else{
          proxy.$message.warning(res.msg);
        }
        
      })
    }
  }else if(item.name == "导出工程设计成果"){
      buildZip()
    }
  else if(item.name == "电气平面布置图绘制"){
       const params = {
    content: "Mx_ModelSelection",
    type: "onlycad",
    options:{
      name:"JGFZH_Draw"
    }
  }
  sendMessage(cadAppRef.value, params, (res) => {
    if(res.id) {
      queryStationData(res.id)
    }
  })
    }
  appStore.clickMap(item);
  appStore.clickData(true);
  flag.value = show;
  removeActiveMenuItem();
};

const postMessageMsgCadReport = (item, data, fileBlobArr)=>{
  myiframehide.contentWindow.postMessage({
      type: item.type,
      content: item.content,
      formData: {
        fileBlobArr,
        tableData: JSON.stringify(data),
        countyOrganisationName: urlRoute.query.countyOrganisationName,
        projectName: urlRoute.query.projectName,
        stage: urlRoute.query.stage,
        proCode: urlRoute.query.proCode,
        proId: urlRoute.query.id,
        content: item.content,
        offsetX: 0,
      },
    },
    "*"
  );
}

const removeActiveMenuItem = () => {
  const li = document.querySelectorAll(".li")[active.value];
  li.classList.remove("active");
};

function toggleSideBar(item) {
  if (item == 2) {
    appStore.toggleSideBar("undefined", 1);
  } else {
    appStore.toggleSideBar();
  }
}
</script>

<template>
  <div class="tags-view-container">
    <hamburger
      id="hamburger-container"
      :is-active="!isCollapse"
      class="hamburger-container"
      @toggleClick="toggleSideBar(1)"
    />
    <div class="view-container">
      <el-scrollbar>
        <div class="ul">
          <span
            v-for="(item, index) in list"
            :key="index"
            :class="active === index ? 'active' : ''"
            class="li"
            @click="handleClick(item, index)"
          >
            {{ item.name }}
            <div :class="item.flag ? 'onlineFlase' : ''"></div>
          </span>
        </div>
      </el-scrollbar>
      <list-dialog
        :dialogPositionStyle="dialogPositionStyle"
        :flag="flag"
        :info="info"
        :list="list[info.index]?.children || []"
        @update="updateList"
      ></list-dialog>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.hamburger-container {
  line-height: 46px;
  height: 100%;
  float: left;
  cursor: pointer;
  transition: background 0.3s;
  -webkit-tap-highlight-color: transparent;

  &:hover {
    background: rgba(0, 0, 0, 0.025);
  }
}

.tags-view-container {
  height: 50px;
  width: 100%;
  background: var(--tags-bg, #e9f1f4);
  // border-bottom: 1px solid var(--tags-item-border, #d8dce5);
  // box-shadow: 0 1px 3px 0 rgba(0, 0, 0, .12), 0 0 3px 0 rgba(0, 0, 0, .04);

  .tags-view-wrapper {
    .tags-view-item {
      display: inline-block;
      position: relative;
      cursor: pointer;
      height: 26px;
      line-height: 26px;
      border: 1px solid var(--tags-item-border, #d8dce5);
      color: var(--tags-item-text, #495060);
      background: var(--tags-item-bg, #fff);
      padding: 0 8px;
      font-size: 12px;
      margin-left: 5px;
      margin-top: 4px;

      &:first-of-type {
        margin-left: 15px;
      }

      &:last-of-type {
        margin-right: 15px;
      }

      &.active {
        background-color: #42b983;
        color: #fff;
        border-color: #42b983;

        &::before {
          content: "";
          background: #fff;
          display: inline-block;
          width: 8px;
          height: 8px;
          border-radius: 50%;
          position: relative;
          margin-right: 5px;
        }
      }
    }
  }

  .contextmenu {
    margin: 0;
    background: var(--el-bg-color-overlay, #fff);
    z-index: 3000;
    position: absolute;
    list-style-type: none;
    padding: 5px 0;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 400;
    color: var(--tags-item-text, #333);
    box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, 0.3);
    border: 1px solid var(--el-border-color-light, #e4e7ed);

    li {
      margin: 0;
      padding: 7px 16px;
      cursor: pointer;

      &:hover {
        background: var(--tags-item-hover, #eee);
      }
    }
  }
}

.view-container {
  //height: 30px;
  width: 100%;
  background: var(--tags-bg, #e9f1f4);
  padding-left: 5px;

  .ul {
    width: 100%;
    //padding: 0;
    height: 50px;
    padding: 8px 0;
    // list-style: none;
    margin: 0;
    // display: flex;
    // justify-content: space-between;
    // justify-content: flex-start;
    font-size: 13px;
    font-weight: 400;
    position: relative;
    //overflow-x: auto;
    white-space: nowrap;

    :first-child {
      width: 120px;
    }

    .li {
      display: inline-block;
      font-family: "Noto Sans SC", sans-serif;
      width: 110px;
      font-weight: 500;
      //height: 30px;
      // background: url("@/assets/images/nocheck-navbar2.png") no-repeat 100% 100%;
      text-align: center;
      line-height: 30px;
      background: #ffffff;
      border-radius: 6px 6px 6px 6px;
      border: 1px solid #0e8b8d;
      cursor: pointer;
      position: relative;
      margin-left: 5px;
    }

    .active {
      // background: url("@/assets/images/check-navbar2.png") no-repeat 100% 100%;
      background: #0e8b8d;
      color: #ffffff;
    }

    .onlineFlag {
      width: 12px;
      height: 12px;
      background: url("@/assets/images/在线设计/whiteRight.png");
      position: absolute;
      right: 0;
      top: 9px;
      background-size: 100% 100%;
    }

    .onlineFlase {
      width: 12px;
      height: 12px;
      background: url("@/assets/images/在线设计/right.png");
      position: absolute;
      right: 0;
      top: 9px;
      background-size: 100% 100%;
    }
  }
}
</style>

<style lang="scss">
//reset element css of el-icon-close
.tags-view-wrapper {
  .tags-view-item {
    .el-icon-close {
      width: 16px;
      height: 16px;
      vertical-align: 2px;
      border-radius: 50%;
      text-align: center;
      transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
      transform-origin: 100% 50%;

      &:before {
        transform: scale(0.6);
        display: inline-block;
        vertical-align: -3px;
      }

      &:hover {
        background-color: var(--tags-close-hover, #b4bccc);
        color: #fff;
        width: 12px !important;
        height: 12px !important;
      }
    }
  }
}
</style>
