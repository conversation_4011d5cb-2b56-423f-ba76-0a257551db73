import request from '@/utils/request'

// 架空——绘制按钮保存
export function saveAllInfo(data) {
    if(typeof(data.privatepropertys) === 'object') data.privatepropertys = JSON.stringify(data.privatepropertys)
    if(typeof(data.equipmentInfo) === 'object') data.equipmentInfo = JSON.stringify(data.equipmentInfo)
    if(typeof(data.topologyrelations) === 'object') data.topologyrelations = JSON.stringify(data.topologyrelations)
    return request({
        url: '/engineering/saveAllInfo',
        method: 'post',
        data: data
    })
}

// 区别在于infos的参数为数组类型，可传输多个
export function saveAllInfos(data) {
    if(Array.isArray(data)){
        data.forEach(item => {
            if(typeof(item.privatepropertys) === 'object') item.privatepropertys = JSON.stringify(item.privatepropertys)
            if(typeof(item.equipmentInfo) === 'object') item.equipmentInfo = JSON.stringify(item.equipmentInfo)
            if(typeof(item.topologyrelations) === 'object') item.topologyrelations = JSON.stringify(item.topologyrelations)
        })
    } 
    return request({
        url: '/engineering/saveAllInfos',
        method: 'post',
        data: data
    })
}
//根据sdk的DEV_ID获取状态
export function getEquipmentState(data) {
    return request({
        url: '/rodLineJointDraw/getEquipmentState',
        method: 'get',
        params: data
    })
}
//柱上设备保存接口
export function saveEquipmentInfos(data) {
    return request({
        url: '/rodLineJointDraw/saveEquipmentInfos',
        method: 'post',
        data: data
    })
}

//撤销 - 重做 接口
export function undoEquipmentInfoByFlag(data) {
    return request({
        url: '/rodLineJointDraw/undoEquipmentInfoByFlag',
        method: 'post',
        data: data
    })
}
//插入杆塔保存接口
export function saveOrUpdateEquipmentInfos(data) {
    return request({
        url: '/rodLineJointDraw/saveOrUpdateEquipmentInfos',
        method: 'post',
        data: data
    })
}
//求电缆通道 个数
export function getWellCount(data) {
    return request({
        url: '/rodLineJointDraw/getWellCount',
        method: 'get',
        params: data
    })
}