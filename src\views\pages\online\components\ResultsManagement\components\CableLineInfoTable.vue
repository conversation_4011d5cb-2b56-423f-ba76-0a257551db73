<template>
<data-dialog
    dataWidth="1200"
    dataHeight="400px"
    @close="closeDialog"
    v-if="appStore.mapIndex == '电缆明细表'"
  >
  <template #header>
      <h4 style="margin-left: 10px; margin-top: 0px; line-height: 39px">
        电缆明细表
      </h4>
    </template>
    <template #body>
      <div class="material">
        <div style="margin-left: 10px">
          <el-button icon="Paperclip" type="text" @click="exportExcel">生成报表</el-button>
        </div>
        <el-table :data="tableData" border class="input-table"  size="small" style="width: 100%">
          <el-table-column width="50" align="center" label="序号" prop="number" ></el-table-column>
          <el-table-column width="100" align="center" label="线路名称" prop="lineName" :formatter="changeValue"></el-table-column>
          <el-table-column width="100" align="center" label="电缆型号" prop="cableType" :formatter="changeValue"></el-table-column>
          <el-table-column align="center" label="起止设备" >
            <el-table-column align="center" label="起始设备名称" prop="startUserNumber" :formatter="changeValue"></el-table-column>
            <el-table-column align="center" label="终止设备名称" prop="endUserNumber" :formatter="changeValue"></el-table-column>
          </el-table-column>
          <el-table-column align="center" label="电缆长度（米）" >
            <el-table-column align="center" label="新建长度" prop="newLength" :formatter="changeValue"></el-table-column>
            <el-table-column align="center" label="利旧长度" prop="oldLength" :formatter="changeValue"></el-table-column>
            <el-table-column align="center" label="拆旧长度" prop="removeLength" :formatter="changeValue"></el-table-column>
          </el-table-column>
          <el-table-column align="center" label="电缆终端（套）" >
            <el-table-column align="center" label="户外终端" prop="outdoorTerminalNum" :formatter="changeValue"></el-table-column>
            <el-table-column align="center" label="户内终端" prop="indoorTerminalNum" :formatter="changeValue"></el-table-column>
            <el-table-column align="center" label="设备终端" prop="equipmentTerminalNum" :formatter="changeValue"></el-table-column>
            <el-table-column align="center" label="中间接头" prop="midJointNum" :formatter="changeValue"></el-table-column>
          </el-table-column>
          <el-table-column align="center" label="电缆设备（台）" >
            <el-table-column align="center" label="电缆对接箱" prop="cableDockingNum" :formatter="changeValue"></el-table-column>
            <el-table-column align="center" label="电缆分支箱" prop="cableBranchNum" :formatter="changeValue"></el-table-column>
          </el-table-column>
        </el-table>
      </div>
    </template>
  </data-dialog>
</template>
<script setup> 
import DataDialog from "@/components/DataDialog/index.vue";
import useAppStore from "@/store/modules/app.js";
import { useRoute } from "vue-router";
import {
  generatorDlmxReport, getCableDetails
} from "@/api/insertSag/index.js";
import { inject } from 'vue';
import { ElLoading } from 'element-plus'
const { proxy } = getCurrentInstance();
const route = useRoute();
const appStore = useAppStore();
const taskId = route.query.id;
const tableData=ref([])
const callChildC = inject('callChildC');

const closeDialog = () => {
  appStore.mapIndex = "";
};
const exportExcel = async () => {
  if (tableData.value.length === 0) {
    proxy.$message.warning("目前暂无数据可生成");
    return; // 如果为空，直接返回
  }
  oniframeMessage({
    type: "cadPreview",
    content: "Mx_dlmx",
    formData: {
      content: "电缆明细表",
      tableData: JSON.stringify(tableData.value),
      countyOrganisationName: route.query.countyOrganisationName,
      projectName: route.query.projectName,
      stage: route.query.stage,
      proCode: route.query.proCode,
    },
  });
};

const oniframeMessage = (param) => {
  const myiframe = appStore.iframeHide
  console.log("🚀 ~ oniframeMessage ~ appStore.iframe:", appStore.iframe)
  if (myiframe && myiframe.contentWindow) {
    myiframe.contentWindow.postMessage(param, "*");
    // 先移除之前的事件监听器，防止重复绑定
    window.removeEventListener('message', handleMessage);
    // 添加新的事件监听器
    window.addEventListener('message', handleMessage);
  }
}
// 事件处理函数
const handleMessage = (event) => {
  if (event.data.type === 'parentCad' && event.data.params.content === '电缆明细表') {
    const loading = ElLoading.service({
        lock: true,
        text: '报表正在生成中，请稍等...',
        background: 'rgba(0, 0, 0, 0.7)',
      })
    const files = event.data.params.formData.files
    const dataExcel = JSON.parse(event.data.params.formData.tableData).map(item => {
      return {
        xh: item.number,
        dlmc: item.lineName,
        dlxh: item.cableType,
        qssbmc: item.startUserNumber,
        zzsbmc: item.endUserNumber,
        xjcd: item.newLength,
        ljfs: item.oldLength,
        zjcd: item.removeLength,
        hwzd: item.outdoorTerminalNum,
        hnzd: item.indoorTerminalNum,
        sbzd: item.equipmentTerminalNum,
        zjjt: item.midJointNum,
        dldjx: item.cableDockingNum,
        dlfzx: item.cableBranchNum
      }
    })
    const fileFormData = new FormData()
    files.forEach(item => {
      fileFormData.append('multipartFile', item)
    })
    fileFormData.append("dlmxList", JSON.stringify(dataExcel));
    fileFormData.append('prjTaskInfoId', route.query.id)
    fileFormData.append('stage', route.query.stage)
    console.log(dataExcel)
    generatorDlmxReport(fileFormData).then(res => {
      if (res.code === 200) {
        proxy.$message.success("保存成功");
        loading.close()
        if (callChildC) {
        callChildC();
      }
      } else {
        proxy.$message.error(res.msg);
        loading.close()
      }
    })
  }
}
const changeValue = (row, column, cellValue) => {
  return cellValue === 0 ? '' : cellValue
}

const queryList=()=>{
  getCableDetails({taskId}).then(res=>{
    tableData.value = res.data
    tableData.value.forEach((item, index) => {
      item.number = index + 1;
    });
  })
}
watch(
  () => appStore.mapIndex,
  (newInfo, oldInfo) => {
    if (newInfo == "电缆明细表") {
      queryList();
    }
  }
);
</script>
<style lang="scss" scoped>
@use '../../index' as *;
</style>