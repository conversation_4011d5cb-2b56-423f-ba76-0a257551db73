<script setup>
import { McObject } from "mxcad";
import { downloadFileDWG } from "@/api/task/index.js";
import AddInterval from "./add.vue";
import { useCAdApp } from "@/hooks/useCAdApp.js";
import { useIframeCommunication } from "@/hooks/useIframeCommunication";
import { getTxyl } from "@/api/insertSag/index.js";
import useAppStore from "@/store/modules/app.js";
const appStore = useAppStore();
const { sendMessage, cleanup } = useIframeCommunication();
// 创建mxcad示例对象
const mxcad = new McObject();
const { cadAppSrc } = useCAdApp();
const props = defineProps({
  name: {
    type: String,
    required: true,
  },
});
const cadIframeSrc = cadAppSrc({
  isPreview: "1",
});
const cadIframeRef = ref(null);

  
const getDwgFile = () => {
  nextTick(() => {
    setTimeout(() => {
      const params = {
        type: "cadPreview",
        content: "Mx_splicing",
        formData: {
          tableData: JSON.stringify(tableData.value),
          isFlags: true
        },
      };
      sendMessage(cadIframeRef.value, params, (res) => {
      });
    }, 2000);
  });
};



const tableData = ref([]);
const addVisible = ref(false);
const addHandle = () => {
  addVisible.value = true;
};
const updateHandle = (event) => {
  update(event).then((updatedEvent) => {
    tableData.value = updatedEvent;
    if(appStore.intervalLowList.length > 0)  tableData.value=[...tableData.value,...appStore.intervalLowList]
    getDwgFile();
  });
};
const update = async (event) => {
  try {
    // 创建 event 的副本以避免直接修改原始数据
    const updatedEvent = [...event];

    for (let i = 0; i < updatedEvent.length; i++) {
      const item = updatedEvent[i];
      const jgcsRes = await getTxyl(item.drawingid);

      // 将文件转换为 Base64
      const base64Data = await fileToBase64(jgcsRes);

      // 将 base64Data 添加到当前 event 项中
      updatedEvent[i] = {
        ...item,
        file: base64Data, // 添加 file 字段
      };
    }

    return updatedEvent; // 返回更新后的 event 数组
  } catch (error) {
    console.error("处理过程中出错:", error);
    throw error; // 可以选择重新抛出错误或返回原始 event
  }
};

const fileToBase64 = (file) => {
  return new Promise((resolve, reject) => {
    // 添加文件类型检查
    if (!(file instanceof Blob || file instanceof File)) {
      return reject(new Error("输入不是有效的文件对象"));
    }

    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
  });
};
onMounted(() => {
  if(props.name==='2'){
      tableData.value = appStore.intervalLowList
    getDwgFile();
    }
});
const refresh = () => {
  getDwgFile();
};
watch(
  () => tableData.value,
  (newValue) => {
    tableData.value = newValue.sort((a, b) => a.order - b.order);
    appStore.setIntervalLowList(tableData.value,props.name)
  },
  { deep: true }
);

</script>

<template>
  <div class="">
    <div class="toolbar">
      <el-button type="primary" @click="addHandle">添加间隔</el-button>
      <!-- <el-button>删除间隔</el-button> -->
    </div>
    <el-table
      :data="appStore.intervalLowList"
      border
      stripe
      max-height="400px"
      style="width: 100%"
    >
      <el-table-column type="index" label="" align="center" width="50" />
      <el-table-column
        show-overflow-tooltip
        prop="materialcodeerp"
        label="物料编码"
        align="center"
        width=""
      />
      <el-table-column
        show-overflow-tooltip
        prop="materialdescription"
        label="物料描述"
        align="center"
        width=""
      />
      <el-table-column
        show-overflow-tooltip
        prop="extensiondescription"
        label="扩展描述"
        align="center"
        width=""
      />
      <el-table-column
        show-overflow-tooltip
        prop="intervalname"
        label="方案名称"
        align="center"
        width=""
      />
      <el-table-column
        show-overflow-tooltip
        prop=""
        label="设备状态"
        align="center"
        width=""
      >
    <template #default="scope">
      <el-select
                    style="width: 200px"
                    v-model="scope.row.state"
                    placeholder="请选择"
                  >
                    <el-option
                      label="新增"
                      value="New"
                    />
                    <el-option
                      label="拆除"
                      value="Remove"
                    />
                    <el-option
                      label="原有"
                      value="Original"
                    />
                  
                  </el-select>
    </template>
    </el-table-column>
      <el-table-column
        show-overflow-tooltip
        label="间隔名称"
        align="center"
        width=""
      >
      <template #default="scope">
          <el-input v-model="scope.row.intervalnames" placeholder="请输入间隔名称" ></el-input>
        </template>
    </el-table-column>
     <el-table-column
        show-overflow-tooltip
        label="间隔编号"
        align="center"
        width=""
      >
      <template #default="scope">
          <el-input v-model="scope.row.type" placeholder="请输入间隔编号" ></el-input>
        </template>
    </el-table-column> 
      <el-table-column
        show-overflow-tooltip
        prop="order"
        label="顺序调整"
        align="center"
        width=""
      >
        <template #default="scope">
          <el-input v-model="scope.row.order" type="number" min="1"></el-input>
        </template>
      </el-table-column>
    </el-table>
    <div class="general-box">
      <div class="general-box-header">
        <div class="general-box-title">系统配置图预览</div>

        <div class="general-box-right">
          <!-- <el-button type="primary" @click="refresh" text>刷新</el-button> -->
        </div>
      </div>
      <div style="height: 300px" class="general-box-container">
        <!-- <canvas style="width: 100%;height: 300px;" height="300px" id="myCanvas"></canvas> -->
        <iframe
          ref="cadIframeRef"
          :src="cadIframeSrc"
          style="width: 100%; height: 100%"
        />
      </div>
    </div>
    <add-interval
      @update="updateHandle"
      :named="props.name"
      v-if="addVisible"
      v-model:visible="addVisible"
    />
  </div>
</template>

<style scoped lang="scss">
.toolbar {
  padding: 8px 0;
}
</style>
