import { MxCpp } from "mxcad";

// 画路径
function drawPath() {
    const mxcad = MxCpp.getCurrentMxCAD();
    // 创建新画布
    mxcad.newFile();
    //用路径 绘制矩形框
    //定义一个路径的开始点
    mxcad.pathMoveTo(0, 300);
    //路径的一下个点
    mxcad.pathLineTo(100, 300);
    //路径的一下个点
    mxcad.pathLineTo(100, 400);
    //路径的一下个点
    mxcad.pathLineTo(0, 400);
    //把路径设置成闭合
    mxcad.pathMakeClosed();
    //生成一个矩形框的多义线
    mxcad.drawPathToPolyline();

    //定义一个路径的开始点
    mxcad.pathMoveTo(170, 320)
    mxcad.pathLineTo(200, 320)
    mxcad.pathLineTo(200, 340)
    mxcad.pathLineTo(170, 340)
    // 把上面定义的路径定义填充排除区域.
    mxcad.pathMakeExclude(true)
    mxcad.pathMoveTo(150, 300)
    mxcad.pathLineTo(250, 300)
    mxcad.pathLineTo(250, 400)
    mxcad.pathLineTo(150, 400)
    mxcad.pathMakeClosed();
    // 将路径生成一个填充 这里的参数是图案缩放比例
    mxcad.drawPathToHatch(1);

    mxcad.zoomAll();
    mxcad.zoomScale(0.8)
};

// 调用画路径的函数
drawPath();