<script setup>
import {useRoute} from "vue-router";
let { proxy } = getCurrentInstance();
const route = useRoute();
const appStore = useAppStore();
import DataDialog from "@/components/DataDialog/index.vue";
import useAppStore from "@/store/modules/app.js";
import {drawingSplitting} from "@/api/desginManage/ToolManagement.js";
const closeDialog = () => {
  appStore.mapIndex = "";
};
const treeRef = ref(null);
const checkList = ref([])
const treeData = ref([])
const handleNodeClick = () => {}
const handleCheckChange = (data, checked, indeterminate) => {
  // const selectedLevel3Nodes = proxy.$refs.treeRef.getCheckedNodes()
  // const level2Ids = selectedLevel3Nodes.filter(node => {
  //   const level = getNodeLevel(node, treeData.value)
  //   return level === 3
  // }).map(node => node.drawingid)
  // checkList.value = level2Ids
}
// 递归查找节点的 level
const getNodeLevel = (node, data, level = 1) => {
  for (let item of data) {
    if (item === node) return level
    if (item.children) {
      const childLevel = getNodeLevel(node, item.children, level + 1)
      if (childLevel) return childLevel
    }
  }
  return null
}
const dataList = () => {
  drawingSplitting({ taskid: route.query.id }).then(res => {

  })
}
watch(
    () => appStore.mapIndex,
    (newInfo, oldInfo) => {
      if (newInfo == "图纸拆分") {
        dataList()
      }
    }
);
</script>

<template>
  <!-- 图纸拆分 -->
  <data-dialog
      @close="closeDialog"
      dataWidth="830px"
      v-if="appStore.mapIndex == '图纸拆分'"
  >
    <template #header>
      <h4 style="margin-left: 10px; margin-top: 0px; line-height: 39px">
        图纸拆分
      </h4>
    </template>
    <template #body>
      <div class="DrawingSplitting-box">
        <el-tree
            ref="treeRef"
            show-checkbox
            node-key="drawingid"
            default-expand-all
            :data="treeData"
            :props="{ label: 'assessmenttype', children: 'children' }"
            @node-click="handleNodeClick"
            @check-change="handleCheckChange"
        ></el-tree>
      </div>
      <div style="background: #fff; height: 50px" class="line">
        <div class="line-bnt">拆分图纸</div>
      </div>
    </template>
  </data-dialog>
</template>

<style scoped lang="scss">
@use '../../index' as *;
.DrawingSplitting-box {
  width: 600px;
  height: 300px;
}
</style>