import {MxFun} from "mxdraw";
import {
  pictureFrame,
} from "../src/mapBox/draw/components/drawPictureFrame";
function Mx_bztl(value) {
  const array = value.map((item) => {
    return {
      ...item,
      spanClass: item.legendname,
      spanWidth: 30,
      fileName: `/assets/ModuleLegens/${item.legendname}.mxweb`,
      text: item.legendname,
    };
  });
  pictureFrame(array);
}
export function init() {
  MxFun.addCommand("Mx_bztl", Mx_bztl);
}