import request from '@/utils/request'

// 成果输出管理-主要设备材料清单-获取材料亲测信息
export function getMaterialsByIdAndUnitType(params) {
    return request({
        url: '/system/specification/getMaterialsByIdAndUnitType',
        method: 'post',
        data: params
    })
}

// 成果输出管理-主要设备材料清单-查询对应物料数据
export function getMaterials(data) {
    return request({
        url: '/base/legendType/getMaterials',
        method: 'post',
        data: data
    })
}

// 成果输出管理-主要设备材料清单-查询对应物料数据
export function getAllMaterialTypes(params) {
    return request({
        url: '/base/legendType/getAllMaterialTypes',
        method: 'get',
        params: params
    })
}

// 成果输出管理-主要设备材料清单-新增对应物料数据
export function addProjectmaterials(data) {
    return request({
        url: '/system/specification/addProjectmaterials',
        method: 'post',
        data: data
    })
}

// 成果输出管理-主要设备材料清单-删除对应物料数据
export function deleteProjectmaterials(data) {
    return request({
        url: '/system/specification/deleteProjectmaterials',
        method: 'post',
        data: data
    })
}
//获取建设规模
export function getScaleStatistics(params) {
    return request({
        url: `/rodLineJointDraw/getScaleStatistics`,
        method: 'get',
        params: params
    })
}

// 成果输出管理-主要设备材料清单-生成报表
export function exportProjectmaterials(data) {
    return request({
        url: '/system/specification/generatorReport',
        method: 'post',
        data: data,
        headers: {
            "Content-Type": "multipart/form-data",
        }
    })
}
