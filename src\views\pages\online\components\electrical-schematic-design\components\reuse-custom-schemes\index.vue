<script setup>
const visible = defineModel('visible', {type: Boolean, default: false})

const emit = defineEmits(['submit'])

const form = reactive({})
const closeVisible = () => {
  visible.value = false
}
const onSubmit = () => {
  // visible.value = false
  emit('submit', form)
}
</script>

<template>
  <el-dialog
      v-model="visible"
      title="方案选择"
      width="30%"
      draggable
      overflow
      append-to-body
      @close="closeVisible"
  >
    <el-form ref="formRef" :model="form" label-width="100">
      <el-form-item label="额定电流" prop="dl">
        <el-select v-model="form.dl" clearable>
        </el-select>
      </el-form-item>
      <el-form-item label="方案编号" prop="">
        <el-select v-model="form.dl" clearable>
        </el-select>
      </el-form-item>
      <el-form-item label="自定义方案" prop="">
        <el-select v-model="form.dl" clearable>
        </el-select>
      </el-form-item>
      <el-form-item label="站房名称" prop="">
        <el-input v-model="form.dl" clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="状态" prop="">
        <el-select v-model="form.dl" clearable>
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeVisible">取消</el-button>
        <el-button type="primary" @click="onSubmit">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">

</style>
