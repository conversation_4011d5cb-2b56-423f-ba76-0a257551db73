export function useState() {
    const stateOption = [
        {
            label: '新建',
            value: 'New',
        },
        {
            label: '原有',
            value: 'Original',
        },
        {
            label: '拆除',
            value: 'Remove',
        },
        {
            label: '改造',
            value: 'Reform',
        },
    ]

    const stateGather = {
        New:  [
            {
                label: '新建',
                value: 'New',
            },
            {
                label: '利旧',
                value: 'NewUsing',
            },
        ],
        Original:  [
            {
                label: '原有',
                value: 'Original',
            },
        ],
        Remove: [
            {
                label: '拆除利旧',
                value: 'RemoveUsing',
            },
            {
                label: '拆除报废',
                value: 'RemoveScrap',
            },
            {
                label: '拆除回收',
                value: 'RemoveAbandon',
            },
        ],
        Reform: [
            {
                label: '新建',
                value: 'New',
            },
            {
                label: '原有',
                value: 'Original',
            },
            {
                label: '利旧',
                value: 'NewUsing',
            },
            {
                label: '拆除利旧',
                value: 'RemoveUsing',
            },
            {
                label: '拆除报废',
                value: 'RemoveScrap',
            },
            {
                label: '拆除回收',
                value: 'RemoveAbandon',
            },
        ]
    }

    return {
        stateOption,
        stateGather
    }
}
