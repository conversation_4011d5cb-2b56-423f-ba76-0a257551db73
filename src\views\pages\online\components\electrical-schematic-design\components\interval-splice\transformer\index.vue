<script setup>
import {
  getByq
} from "@/api/insertSag/index.js";
import { watch } from "vue";
import useAppStore from "@/store/modules/app.js";
const appStore = useAppStore();
const form = reactive({
  dl:'10kV',
  deviceType:'干式'
})


const tableData = ref([])
const queryList=()=>{
  getByq({
    key: form.deviceType,
    voltage: form.dl,
  }).then((res) => {
    res.data.forEach(element => { 
      element.state='New';
    });
    tableData.value = res.data;

    if(appStore.byqList?.length) {
  const tableDataMap = appStore.byqList?.reduce((map, item) => {
    if(item.materialsprojectid) map.set(item.materialsprojectid, item);
    return map;
  }, new Map());

  tableData.value.forEach(item => {
    const sourceItem = tableDataMap?.get(item.materialsprojectid);
    if(sourceItem?.num !== undefined) {
      item.num = sourceItem.num;
    }
  });
}
  });
}
const changeNumList=ref([])

onMounted(() => {
    queryList();
});
//监听数组是否有变化 储存到仓库
watch(
  () => tableData.value,
  (newValue) => {
    appStore.setByqList(newValue)
  },
  { deep: true }
);

</script>

<template>
  <el-form ref="formRef" :model="form" :inline="true">
    <el-form-item label="电压等级" prop="">
      <el-select
                    style="width: 200px"
                    v-model="form.dl"
                    placeholder="请选择"
                  >
                    <el-option
                      label="10kV"
                      value="10kV"
                    />
                    <el-option
                      label="20kV"
                      value="20kV"
                    />
                  
                  </el-select>
    </el-form-item>
    <el-form-item label="设备类别" prop="">
      <el-select
                    style="width: 200px"
                    v-model="form.deviceType"
                    placeholder="请选择"
                  >
                    <el-option
                      label="干式"
                      value="干式"
                    />
                    <el-option
                      label="油浸"
                      value="油浸"
                    />
                  
                  </el-select>
    </el-form-item>
  </el-form>
   <el-table :data="tableData" border stripe max-height="400px" style="width: 100%">
      <el-table-column type="index" label="序号" align="center" width="70"/>
      <el-table-column prop="materialcodeerp" label="物料编码" align="center" width=""/>
      <el-table-column prop="materialdescription" label="物料描述" align="center" width=""/>
      <el-table-column prop="extensiondescription" label="扩展描述" align="center" width=""/>
      <el-table-column prop="extensiondescription" label="设备数量" align="center" width="">
        <template #default="scope">
         <el-input  v-model="scope.row.num" placeholder="请输入数量" type="number" min="1" ></el-input>
        </template>
      </el-table-column>
      <el-table-column  label="设备状态"  align="center">
        <template #default="scope">
        <el-select
          v-model="scope.row.state"
          placeholder="请选择"
          style="width: 150px"
        >
          <el-option label="新建" value="New" />
          <el-option label="拆除" value="Remove" />
          <el-option label="原有" value="Original" />
        </el-select>
      </template>
      </el-table-column>
      </el-table>
</template>

<style scoped lang="scss">

</style>
