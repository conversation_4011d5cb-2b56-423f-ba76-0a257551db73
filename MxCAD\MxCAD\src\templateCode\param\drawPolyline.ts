import { MxCpp, McDbPolyline, McGePoint3d } from "mxcad"

//画多段线
function drawPolyline() {
  const mxcad = MxCpp.getCurrentMxCAD();
  // 创建新画布
  mxcad.newFile();
 
  // 画多段线
  const polyline = new McDbPolyline()
  polyline.isClosed = true // 设置闭合
  polyline.constantWidth = 5 // 设置线宽
  // 添加多段线顶点
  polyline.addVertexAt(new McGePoint3d(800, 300, 0))
  polyline.addVertexAt(new McGePoint3d(900, 300, 0))
  polyline.addVertexAt(new McGePoint3d(900, 400, 0))
  mxcad.drawEntity(polyline)

  mxcad.zoomAll()
  mxcad.zoomScale(0.8)
}

// 调用画多段线的方法
drawPolyline()