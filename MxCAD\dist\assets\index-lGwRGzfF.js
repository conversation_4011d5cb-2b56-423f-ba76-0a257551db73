import{d as W,w as Ne,h as $e,a0 as Ve,a3 as be,m as p,$ as d,a1 as S,V,F as Re,a4 as Je,H as Ge,W as Ke,u as c,Q as B,B as H,n as Ue}from"./vue-DfH9C9Rx.js";import{M as Te}from"./index-8X61wlK0.js";import{s as L,O as Qe,ad as z,k as O,u as Z,ae as qe,A as Ze,af as _e,ag as et,M as Me,a as Ee,n as ce,W as fe}from"./index-D95UjFey.js";import{e as tt,N as le,g as Be,i as at,a2 as lt,h as De,y as U,M as $}from"./mxcad-CfPpL1Bn.js";import{M as Se,D as Oe,o as nt,t as ot,u as st}from"./mxdraw-C_n_7lEs.js";import"./mapbox-gl-PN3pDJHq.js";import{p as rt}from"./print-B4h2MorP.js";import{S as it,c as Ae,C as ut,E as Le,J as dt,K as ze,i as ae,V as Ie,a as je}from"./vuetify-B_xYg4qv.js";import"./xlsx-Ck5MUZvf.js";function ct(b){b.sort((g,v)=>g.minPt.y===v.minPt.y?g.minPt.x-v.minPt.x:g.minPt.y-v.minPt.y);let D=[];for(const g of b){let v=!0;for(const x of D)if(g.minPt.x>=x.minPt.x&&g.minPt.y>=x.minPt.y&&g.maxPt.x<=x.maxPt.x&&g.maxPt.y<=x.maxPt.y){v=!1;break}v&&D.push(g)}return D}const pt=async()=>{const b=await tt.userSelect(L("462")),D=[],g=(i,m,C)=>(m.x-i.x)*(m.x-C.x)+(m.y-i.y)*(m.y-C.y)==0,v=(i,m,C,y)=>g(i,m,C),x=i=>{if(i instanceof le){const m=i.numVerts();if(m===0||(i.isClosed?m>4:m>5))return;for(let y=0;y<m;y++)if(i.getBulgeAt(y)>.001)return;const C=i.getPointAt(0).val;if(v(C,i.getPointAt(1).val,i.getPointAt(2).val,i.isClosed?C:i.getPointAt(3).val)){const{minPt:y,maxPt:P}=i.getBoundingBox();D.push({minPt:y,maxPt:P})}}},T=i=>{const m=i.blockTableRecordId.getMcDbBlockTableRecord();m&&m.getAllEntityId().forEach(C=>{const y=C.getMcDbEntity();y&&(y instanceof le&&x(y),y instanceof Be&&T(y))})};return b.forEach(i=>{const m=i.getMcDbEntity();m instanceof le?x(m):m instanceof Be&&T(m)}),ct(D).filter(({minPt:i,maxPt:m})=>{const C=new at;return C.imp.userSelect(i.x,i.y,m.x,m.y,lt(),!1),C.count()!==0})};function We(b,D,g,v){const x=Math.abs(v.x-g.x),T=Math.abs(v.y-g.y);let A;return b/x<D/T?A=x/b:A=T/D,A}function mt(b,D,g,v,x){const T=b*x,A=D*x,i=(g.x+v.x)/2,m=(g.y+v.y)/2,C=new U(i-T/2,m+A/2),y=new U(i+T/2,m-A/2);return[C,y]}const ft=()=>{const b=W(!0),D=W(0),g=W(0),v=W(0),x=W(0),T=["A1","A2","A3","A4","自定义16.55x23.90"],A=W("A4"),i=()=>{switch(A.value){case"A1":return{w:594,h:841};case"A2":return{w:420,h:594};case"A3":return{w:297,h:420};case"A4":return{w:210,h:297};case"自定义16.55x23.90":return{w:165.5,h:239,nw:130,nh:190}}},m=["横向","纵向"],C=W("横向"),y=W(1),P=W(1);let J,ne,_=P.value/y.value;const pe=()=>{let{w:a,h:t}=i();const e=P.value/y.value;if(Math.abs(e-_)<1e-6)return;_=e;const[l,o]=mt(a,t,J,ne,e);D.value=O(l?.x||0,4),g.value=O(l?.y||0,4),v.value=O(o?.x||0,4),x.value=O(o?.y||0,4),F.pt1=l,F.pt2=o,F.w=a,F.h=t},ye=()=>{let{w:a,h:t}=i();const e=We(a,t,J,ne);_=e,P.value=O(y.value*e,4),pe()},oe=[];let F;const Y=(a,t)=>{J=a,ne=t;let{w:e,h:l,nw:o,nh:h}=i();if(C.value==="横向"){let I=e;e=l,l=I,I=o,o=h,h=I}F&&oe.push(F);const M=We(e,l,a,t);return _=M,P.value=O(y.value*M,4),F={pt1:a,pt2:t,w:e,h:l,size:A.value,paperOrientation:C.value,printParameterMillimeter:y.value,printParametersCADDrawingUnits:P.value,isDrawingBoundary:b.value},D.value=O(a?.x||0,4),g.value=O(a?.y||0,4),v.value=O(t?.x||0,4),x.value=O(t?.y||0,4),F},se=()=>{const{minPt:a,maxPt:t}=$.getCurrentDatabase().currentSpace.getBoundingBox();return Y(a,t)};Ze(()=>{b.value&&se()});const ge=()=>{const{pt1:a,pt3:t}=$.getCurrentMxCAD().getViewCADCoord();return Y(a,t)},he=()=>{typeof b.value=="boolean"&&(b.value?se():ge())},we=()=>{const a=oe.pop();if(!a)return Z().error(L("463"));const{pt1:t,pt2:e,size:l}=a;return F=a,D.value=O(t?.x||0,4),g.value=O(t?.y||0,4),v.value=O(e?.x||0,4),x.value=O(e?.y||0,4),A.value=l,C.value=a.paperOrientation,y.value=a.printParameterMillimeter,P.value=a.printParametersCADDrawingUnits,b.value=a.isDrawingBoundary,b.value=null,Z().success(L("464"))},ve=async()=>{z.isShow.value=!1;const a=()=>{z.isShow.value=!0},t=new De;t.clearLastInputPoint(),t.setMessage(`
`+L("317")+":"),t.disableAllTrace();let e=await t.go();if(!e)return a();t.setMessage(`
`+L("326")+":"),t.setUserDraw((o,h)=>{if(!e)return;h.setColor(16711680);let w=new le;w.addVertexAt(e),w.addVertexAt(new U(e.x,o.y)),w.addVertexAt(o),w.addVertexAt(new U(o.x,e.y)),w.constantWidth=Se.screenCoordLong2Doc(2),w.isClosed=!0,h.drawMcDbEntity(w);let M=[];M.push(e.toVector3()),M.push(new THREE.Vector3(e.x,o.y)),M.push(o.toVector3()),M.push(new THREE.Vector3(o.x,e.y)),h.setColor(12868),h.drawSolid(M,.5)}),t.setDisableOsnap(!0),t.setDisableOrthoTrace(!0),t.setDynamicInputType(Oe.kXYCoordInput);let l=await t.go();if(!l)return a();Y(e,l),b.value=null,z.isShow.value=!0,Z().success(L("465"))},re=async()=>{function a(s,k,f,E){const X=Math.abs(E.x-f.x),de=Math.abs(E.y-f.y);let Q;X>de?Q=X/s:Q=de/k;const q=s*Q,me=k*Q;return new U(f.x+q,f.y-me)}z.isShow.value=!1;const t=()=>{z.isShow.value=!0},e=new De;e.clearLastInputPoint(),e.setMessage(`
`+L("466")+":"),e.disableAllTrace();let{w:l,h:o,nw:h,nh:w}=i();const M=P.value/y.value;(s=>{if(l=l*s,o=o*s,h&&(h=h*s),w&&(w=w*s),C.value==="横向"){let f=l;l=o,o=f,f=h,h=w,w=f}})(M);let R=await e.go();if(!R)return t();e.setMessage(`
`+L("467")+":"),e.setUserDraw((s,k)=>{if(!R)return;k.setColor(16711680),s=a(l,o,R,s);let f=new le;f.addVertexAt(R),f.addVertexAt(new U(R.x,s.y)),f.addVertexAt(s),f.addVertexAt(new U(s.x,R.y)),f.constantWidth=Se.screenCoordLong2Doc(2),f.isClosed=!0,k.drawMcDbEntity(f);let E=[];E.push(R.toVector3()),E.push(new THREE.Vector3(R.x,s.y)),E.push(s.toVector3()),E.push(new THREE.Vector3(s.x,R.y)),k.setColor(12868),k.drawSolid(E,.5)}),e.setDisableOsnap(!0),e.setDisableOrthoTrace(!0),e.setDynamicInputType(Oe.kXYCoordInput);let N=await e.go();if(!N)return t();N=a(l,o,R,N),Y(R,N),b.value=null,z.isShow.value=!0,Z().success(L("465"))},Ce=async()=>{z.isShow.value=!1;const a=P.value/y.value;let{w:t,h:e,nw:l,nh:o}=i();(s=>{if(t=t*s,e=e*s,l&&(l=l*s),o&&(o=o*s),C.value==="横向"){let f=t;t=e,e=f,f=l,l=o,o=f}})(a);const w=new De;w.clearLastInputPoint(),w.disableAllTrace(),w.setMessage(`
`+L("325")),w.setKeyWords("");const M=$.getCurrentMxCAD();w.setUserDraw((s,k)=>{k.setColor(16711680);let f=new le,E=s.clone(),X=new U(s.x,s.y+e),de=new U(s.x+t,s.y+e),Q=new U(s.x+t,s.y);f.addVertexAt(E),f.addVertexAt(X),f.addVertexAt(de),f.addVertexAt(Q),f.constantWidth=Se.screenCoordLong2Doc(2),f.isClosed=!0,k.drawMcDbEntity(f);let q=[];if(q.push(E.toVector3()),q.push(X.toVector3()),q.push(de.toVector3()),q.push(Q.toVector3()),k.setColor(12868),k.drawSolid(q,.5),l&&o&&l>0&&o>0){let me=s.clone(),ke=new U(s.x,s.y+e),He=new U(s.x+t,s.y+e),Xe=new U(s.x+t,s.y),te=[];te.push(me.toVector3()),te.push(ke.toVector3()),te.push(He.toVector3()),te.push(Xe.toVector3()),te.push(me.toVector3());let Fe=M.mxdraw.viewCoordLong2Cad(3),Ye=nt.createDashedLines(te,16777215,Fe*2,Fe);k.drawEntity(Ye)}});let I=await w.go();if(!I)return z.isShow.value=!0;let R=I.clone(),N=new U(I.x+t,I.y+e);Y(R,N),z.isShow.value=!0,b.value=null,Z().success(L("465"))},ie=(a,t,e,l,o=!1)=>new Promise(h=>{let w={width:""+e,height:""+l,roate_angle:0,bd_pt1_x:""+a.x,bd_pt1_y:""+a.y,bd_pt2_x:""+t.x,bd_pt2_y:""+t.y};const M=et(),I=()=>{M&&_e("MxFullScreen")};{let{baseUrl:R="",mxfilepath:N="",printPdfUrl:s=""}=qe()||{};$.getCurrentMxCAD().saveFileToUrl(s,(k,f)=>{try{let E=JSON.parse(f);if(E.ret=="ok"){let X=R+N+E.file;if(o)return h(X);rt(X),I(),Z().success(L("468")),h(X)}else console.log(f),Z().error(L("469")),h(!1)}catch{console.log("Mx: sserverResult error"),h(!1)}},void 0,JSON.stringify(w))}}),ue=()=>{const{pt1:a,pt2:t,w:e,h:l}=F||se();ie(a,t,e,l),z.isShow.value=!1},xe=(a,t,e=16711680)=>{const l=new st;return l.pt1=a.toVector3(),l.pt2=t.toVector3(),l.setLineWidth(10),l.color=e,l.top(),l},G=Qe(!1,"Mx_batch_PrintDialog");let K=[];const n=W([]);let r=[];const u=async()=>{z.isShow.value=!1,G.isShow.value=!1,r=await pt(),n.value=r.map((a,t)=>({name:L("470")+t,index:t})),K=r.filter(a=>!!a).map((a,t)=>{const{minPt:e,maxPt:l}=a,o=xe(e,l),h=$.getCurrentMxCAD();h.getMxDrawObject().addMxEntity(o);const w=new U((e.x+l.x)/2,(e.y+l.y)/2),M=new ot;return M.text=t.toString(),M.position=w.toVector3(),M.color=16711680,M.height=e.distanceTo(l)*.5,h.getMxDrawObject().addMxEntity(M),[M,o]}),G.isShow.value=!0};function j(a,t){var e=document.createElement("a");e.setAttribute("href",a),e.setAttribute("download",t),document.body.appendChild(e),e.click(),document.body.removeChild(e)}const ee=async()=>{let{w:a,h:t}=i();for(let e=0;e<r.length;e++){const{minPt:l,maxPt:o}=r[e]||{};if(!l||!o)continue;const h=await ie(l,o,a,t,!0);h&&j(h,n.value[e].name)}G.isShow.value=!1},Pe=()=>{const a=$.getCurrentMxCAD();K.forEach(([t,e])=>{a.mxdraw.eraseMxEntity(t.objectId()),a.mxdraw.eraseMxEntity(e.objectId())}),K=[],r=[],n.value=[],a.updateDisplay()};return Ne(G.isShow,a=>{a||Pe()}),{dialog:z,isDrawingBoundary:b,lowerLeftCornerCoordinateX:D,lowerLeftCornerCoordinateY:g,upperRightCornerCoordinateX:v,upperRightCornerCoordinateY:x,sheetSizes:T,sheetSize:A,paperOrientations:m,paperOrientation:C,printParameterMillimeter:y,printParametersCADDrawingUnits:P,scopeHistory:oe,callLastTimeScopeHistory:we,callFreeChoiceOfRange:ve,callFixedProportionalSelection:re,callFixedDrawingSizeSelection:Ce,callPrint:ue,updateDrawingBoundary:he,updatePrintParameters:pe,updateSize:ye,universalBatchPrinting:ee,frameIndexArr:n,removeFrame:a=>{const[t,e]=K[a],l=$.getCurrentMxCAD();l.mxdraw.eraseMxEntity(t.objectId()),l.mxdraw.eraseMxEntity(e.objectId()),l.updateDisplay(),r.splice(a,1),K.splice(a,1),n.value.splice(a,1)},removeFramesRectBoxArr:Pe,frameRecognition:u,batchPrintingDialog:G,framePrint:a=>{if(!r[a])return;const{maxPt:t,minPt:e}=r[a];let{w:l,h:o}=i();ie(e,t,l,o,!0)},positioningFrame:a=>{if(!r[a])return;let{maxPt:t,minPt:e}=r[a];const l=$.getCurrentMxCAD(),o=e.distanceTo(t)*.1;t=t.clone(),e=e.clone(),e.x-=o,e.y-=o,t.x+=o,t.y+=o,l.zoomW(e,t)}}},yt=["onUpdate:modelValue"],gt={class:"d-flex flex-column"},ht={class:"h-100 mt-2"},wt={class:"d-flex align-center flex-column"},Rt=$e({__name:"index",setup(b){const{dialog:D,isDrawingBoundary:g,lowerLeftCornerCoordinateX:v,lowerLeftCornerCoordinateY:x,upperRightCornerCoordinateX:T,upperRightCornerCoordinateY:A,sheetSizes:i,sheetSize:m,paperOrientations:C,paperOrientation:y,printParameterMillimeter:P,printParametersCADDrawingUnits:J,callLastTimeScopeHistory:ne,callFreeChoiceOfRange:_,callFixedProportionalSelection:pe,callFixedDrawingSizeSelection:ye,callPrint:oe,updateDrawingBoundary:F,updatePrintParameters:Y,updateSize:se,universalBatchPrinting:ge,frameIndexArr:he,removeFrame:we,frameRecognition:ve,batchPrintingDialog:re,framePrint:Ce,positioningFrame:ie}=ft(),{isShow:ue,showDialog:xe}=D,G=[{name:"生成打印PDF",fun:oe,primary:!0},{name:"取消",fun:()=>xe(!1)}],K=[{name:"批量下载",fun:ge,primary:!0},{name:"取消",fun:()=>re.showDialog(!1)}];return(n,r)=>(Ve(),be(Re,null,[p(Te,{title:n.t("606"),modelValue:c(re).isShow.value,"onUpdate:modelValue":r[0]||(r[0]=u=>c(re).isShow.value=u),"max-width":"600",footerBtnList:K},{default:d(()=>[p(it,{density:"compact","fixed-header":"",height:300,class:"attribute_table",style:{"table-layout":"fixed"}},{default:d(()=>[S("thead",null,[S("tr",null,[S("th",null,"pdf"+V(n.t("209")),1),S("th",null,V(n.t("607")),1)])]),S("tbody",null,[(Ve(!0),be(Re,null,Je(c(he),(u,j)=>(Ve(),be("tr",{key:j},[S("td",null,[Ge(S("input",{class:"w-100 h-100","onUpdate:modelValue":ee=>u.name=ee},null,8,yt),[[Ke,u.name]])]),S("td",null,[p(Ae,{onClick:ee=>c(we)(j)},{default:d(()=>[B(V(n.t("219")),1)]),_:2},1032,["onClick"]),p(Ae,{onClick:ee=>c(Ce)(j),class:"ml-1"},{default:d(()=>[B(V(n.t("225")),1)]),_:2},1032,["onClick"]),p(Ae,{onClick:ee=>c(ie)(j),class:"ml-1"},{default:d(()=>[B(V(n.t("608")),1)]),_:2},1032,["onClick"])])]))),128))])]),_:1})]),_:1},8,["title","modelValue"]),p(Te,{title:n.t("225"),modelValue:c(ue),"onUpdate:modelValue":r[10]||(r[10]=u=>H(ue)?ue.value=u:null),"max-width":"600",footerBtnList:G},{default:d(()=>[p(ut,{align:"stretch"},{default:d(()=>[p(Le,{cols:"6","align-self":"start"},{default:d(()=>[p(Me,{title:n.t("609"),class:"mt-2"},{default:d(()=>[p(dt,{modelValue:c(g),"onUpdate:modelValue":[r[1]||(r[1]=u=>H(g)?g.value=u:null),c(F)],inline:!1},{default:d(()=>[p(ze,{value:!0,onClick:c(F)},{label:d(()=>[p(Ee,{class:"","key-name":"W"},{default:d(()=>[B(V(n.t("231")+n.t("232")),1)]),_:1})]),_:1},8,["onClick"]),p(ze,{class:"mt-1",value:!1,onClick:c(F)},{label:d(()=>[p(Ee,{class:"","key-name":"R"},{default:d(()=>[B(V(n.t("233")+n.t("234")),1)]),_:1})]),_:1},8,["onClick"])]),_:1},8,["modelValue","onUpdate:modelValue"]),S("div",gt,[p(ce,{class:"mt-2",onClick:c(ne)},{default:d(()=>[B(V(n.t("235")+n.t("236")),1)]),_:1},8,["onClick"]),p(ce,{class:"mt-1",onClick:c(_)},{default:d(()=>[B(V(n.t("237")+n.t("238")),1)]),_:1},8,["onClick"]),p(ce,{class:"mt-1",onClick:c(pe)},{default:d(()=>[B(V(n.t("239")+n.t("240")+n.t("238")),1)]),_:1},8,["onClick"]),p(ce,{class:"mt-1",onClick:c(ye)},{default:d(()=>[B(V(n.t("239")+n.t("231")+n.t("241")+n.t("238")),1)]),_:1},8,["onClick"]),p(ce,{class:"mt-1",onClick:c(ve)},{default:d(()=>[B(V(n.t("610")+" "+n.t("611")),1)]),_:1},8,["onClick"])]),p(ae,{modelValue:c(v),"onUpdate:modelValue":r[2]||(r[2]=u=>H(v)?v.value=u:null),class:"mt-2",type:"number"},{prepend:d(()=>[S("span",null,V(n.t("242")+n.t("243")+n.t("244"))+"X:",1)]),_:1},8,["modelValue"]),p(ae,{modelValue:c(x),"onUpdate:modelValue":r[3]||(r[3]=u=>H(x)?x.value=u:null),class:"mt-1",type:"number"},{prepend:d(()=>[S("span",null,V(n.t("242")+n.t("243")+n.t("244"))+"Y:",1)]),_:1},8,["modelValue"]),p(ae,{modelValue:c(T),"onUpdate:modelValue":r[4]||(r[4]=u=>H(T)?T.value=u:null),class:"mt-1",type:"number"},{prepend:d(()=>[S("span",null,V(n.t("245")+n.t("243")+n.t("244"))+"X:",1)]),_:1},8,["modelValue"]),p(ae,{modelValue:c(A),"onUpdate:modelValue":r[5]||(r[5]=u=>H(A)?A.value=u:null),class:"mt-1",type:"number"},{prepend:d(()=>[S("span",null,V(n.t("245")+n.t("243")+n.t("244"))+"Y:",1)]),_:1},8,["modelValue"])]),_:1},8,["title"])]),_:1}),p(Le,{cols:"6","align-self":"start"},{default:d(()=>[S("div",ht,[p(Me,{title:n.t("231")+n.t("246")},{default:d(()=>[p(Ie,{modelValue:c(m),"onUpdate:modelValue":[r[6]||(r[6]=u=>H(m)?m.value=u:null),c(se)],items:c(i),class:"mr-1 my-2"},{prepend:d(()=>[S("span",null,V(n.t("231")+n.t("241"))+":",1)]),selection:d(({item:u})=>[B(V(c(fe)(u.title)),1)]),item:d(({item:u,props:j})=>[p(je,Ue(j,{title:c(fe)(u.title)}),null,16,["title"])]),_:1},8,["modelValue","items","onUpdate:modelValue"]),p(Ie,{modelValue:c(y),"onUpdate:modelValue":[r[7]||(r[7]=u=>H(y)?y.value=u:null),c(F)],items:c(C),class:"mr-1 my-2"},{prepend:d(()=>[S("span",null,V(n.t("231")+n.t("247"))+":",1)]),selection:d(({item:u})=>[B(V(c(fe)(u.title)),1)]),item:d(({item:u,props:j})=>[p(je,Ue(j,{title:c(fe)(u.title)}),null,16,["title"])]),_:1},8,["modelValue","items","onUpdate:modelValue"])]),_:1},8,["title"]),p(Me,{class:"my-4 py-4",title:n.t("225")+n.t("248")},{default:d(()=>[S("div",wt,[p(ae,{modelValue:c(P),"onUpdate:modelValue":r[8]||(r[8]=u=>H(P)?P.value=u:null),modelModifiers:{lazy:!0},min:"0","onUpdate:focused":c(Y),class:"w-75 mr-1",type:"number"},{append:d(()=>[B(V(n.t("249")),1)]),_:1},8,["modelValue","onUpdate:focused"]),r[11]||(r[11]=S("span",null,"=",-1)),p(ae,{modelValue:c(J),"onUpdate:modelValue":r[9]||(r[9]=u=>H(J)?J.value=u:null),modelModifiers:{lazy:!0},min:"0","onUpdate:focused":c(Y),class:"w-75 ml-1 mr-1",type:"number"},{append:d(()=>[B(V("CAD"+n.t("250")+n.t("251")),1)]),_:1},8,["modelValue","onUpdate:focused"])])]),_:1},8,["title"])])]),_:1})]),_:1})]),_:1},8,["title","modelValue"])],64))}});export{Rt as default};
