import {MxCpp,McDbAttributeDefinition,McGeVector3d,McGeMatrix3d, McDbPolyline,McDbText,McDb, MxCADUiPrPoint, McDbBlockReference, McGePoint3d, McDbLine, McDbBlockTableRecord, MxCADResbuf, MxCADUiPrEntity, MxCADUiPrKeyWord} from "mxcad";
import {MxFun} from "mxdraw";

// async function JGFZ() {
//     let getEnt = new MxCADUiPrEntity();
//     const filter = new MxCADResbuf();
//     filter.AddMcDbEntityTypes("INSERT");
//     getEnt.setFilter(filter);
//     getEnt.setMessage("选择目标");
//     let id = await getEnt.go();
//     console.log(id);
//     if (!id) return;
//     const ent = id.getMcDbEntity();
//     console.log('JGFZ',ent)
//     const stationHandle = ent.getxDataString("stationHandle")

//     const volName = ent.getxDataString("volName")
//     if(!stationHandle.ret || !volName.ret) return

//     console.log('JGFZ-data', stationHandle, volName)


//     MxFun.postMessageToParentFrame({
//         cmd: "JGFZ",
//         params: {
//             stationHandle: stationHandle.val,
//             volName: volName.val
//         },
//     });
// }

// data
async function JGFZH_Draw(data) {
    const dataList = data
    const getKey = new MxCADUiPrKeyWord
    getKey.setMessage("输入绘制类型")
    getKey.setKeyWords('[单列布置(1)/双列面对面布置(2)/双列背对背布置(3)]')
    const keyVal: string = await getKey.go()
    let getPoint = new MxCADUiPrPoint();
    const pt = await getPoint.go();
    const blockList = []
    for (let index = 0; index < dataList.length; index++) {
        const entId = await drawBlock(pt, dataList[index].name, keyVal, index)
        blockList.push(entId)
    }
}

const drawBlock = async (pt: McGePoint3d, text:string, keyVal: string, index: number) => {
    const config = {
        squareSize: 20,      // 正方形尺寸
        textHeight: 8,       // 文字高度
        lowerBox: {
            width: 20,       // 矮框宽度
            height:  2       // 矮框高度
        },
        spacing: {
            x: 20,           // 水平间距
            y: 26            // 垂直间距
        }
    };

    const mxcad = MxCpp.getCurrentMxCAD();


    const point = pt.clone()

    // 创建图块记录
    const blkTable = mxcad.getDatabase().getBlockTable();
    const blkRecId = blkTable.add(new McDbBlockTableRecord());
    const blkRecord = blkRecId.getMcDbBlockTableRecord();

    // ================= 1. 绘制正方形框 =================
    const squareSize = config.squareSize;
    const squarePoly = new McDbPolyline();

    // 添加四个顶点（闭合路径）
    squarePoly.addVertexAt(new McGePoint3d(-squareSize/2, squareSize/2)); // 左上
    squarePoly.addVertexAt(new McGePoint3d(squareSize/2, squareSize/2));  // 右上
    squarePoly.addVertexAt(new McGePoint3d(squareSize/2, -squareSize/2)); // 右下
    squarePoly.addVertexAt(new McGePoint3d(-squareSize/2, -squareSize/2));// 左下
    squarePoly.isClosed = true;
    blkRecord.appendAcDbEntity(squarePoly);

    // ================= 2. 添加中心文字 =================
    const textEntity = new McDbText();
    textEntity.textString = text;
    textEntity.position = new McGePoint3d(0, 0, 0);  // 中心坐标
    textEntity.height = 10;                           // 文字高度
    textEntity.horizontalMode = McDb.TextHorzMode.kTextCenter
    textEntity.verticalMode = McDb.TextVertMode.kTextVertMid
    blkRecord.appendAcDbEntity(textEntity);


    // ================= 3. 绘制下方矮框 =================
    const lowerBoxHeight = config.lowerBox.height;   // 矮框高度
    const lowerBoxWidth = config.lowerBox.width;    // 矮框宽度 

    const lowerPoly = new McDbPolyline();
    lowerPoly.isClosed = true;


    let offsetX = 0, offsetY = 0;
    const col = Math.floor( (keyVal !== '1' ? 2 : 1));

    const innerWidth = config.squareSize;
    switch (keyVal) {
        case '1':
            offsetX = index * config.spacing.x;
            lowerPoly.addVertexAt(new McGePoint3d(-lowerBoxWidth/2, -squareSize/2));           // 连接点
            lowerPoly.addVertexAt(new McGePoint3d(-lowerBoxWidth/2, -squareSize/2 - lowerBoxHeight)); // 左下
            lowerPoly.addVertexAt(new McGePoint3d(lowerBoxWidth/2, -squareSize/2 - lowerBoxHeight));  // 右下
            lowerPoly.addVertexAt(new McGePoint3d(lowerBoxWidth/2, -squareSize/2));
            break;
        case '2': // 双列面对面布局
            offsetX = Math.floor(index / 2) * config.spacing.x;
            // 垂直方向分上下两行，间距为方块高度+矮框高度
            offsetY = (index % 2 === 0)
                ? config.squareSize/2 + config.lowerBox.height  // 上侧元素
                : -config.squareSize/2 - config.lowerBox.height; // 下侧元素

            // 绘制内侧矮框（占方块宽度的80%）
            const lowerY = (index % 2 === 0)
                ? -config.squareSize/2   // 上侧元素矮框在底部
                : (config.squareSize/2) + config.lowerBox.height;    // 下侧元素矮框在顶部

            lowerPoly.addVertexAt(new McGePoint3d(-innerWidth/2, lowerY));
            lowerPoly.addVertexAt(new McGePoint3d(-innerWidth/2, lowerY - config.lowerBox.height));
            lowerPoly.addVertexAt(new McGePoint3d(innerWidth/2, lowerY - config.lowerBox.height));
            lowerPoly.addVertexAt(new McGePoint3d(innerWidth/2, lowerY));
            break;

        case '3': // 双列面对面布局
            offsetX = Math.floor(index / 2) * config.spacing.x;
            // 垂直方向分上下两行，间距为方块高度+矮框高度
            offsetY = (index % 2 === 0)
                ? config.squareSize/2 + config.lowerBox.height  // 上侧元素
                : -config.squareSize/2 - config.lowerBox.height; // 下侧元素

            // 绘制内侧矮框（占方块宽度的80%）
            const outerY = (index % 2 === 0)
                ? config.squareSize/2   // 上侧元素矮框在底部
                : -(config.squareSize/2) + config.lowerBox.height;    // 下侧元素矮框在顶部

            lowerPoly.addVertexAt(new McGePoint3d(-innerWidth/2, outerY));
            lowerPoly.addVertexAt(new McGePoint3d(-innerWidth/2, outerY - config.lowerBox.height));
            lowerPoly.addVertexAt(new McGePoint3d(innerWidth/2, outerY - config.lowerBox.height));
            lowerPoly.addVertexAt(new McGePoint3d(innerWidth/2, outerY));
            break;
    }
    blkRecord.appendAcDbEntity(lowerPoly);

    // ================= 4. 插入块参照 =================
    blkRecord.origin = new McGePoint3d(0, 0, 0); // 基点保持中心对齐
    const blkRef = new McDbBlockReference();
    blkRef.blockTableRecordId = blkRecId;

    // blkRef.position = point;  // 使用传入的坐标作为插入点
    blkRef.position = new McGePoint3d(
        pt.x + offsetX,
        pt.y + offsetY,
        pt.z
    );

    blkRef.setxDataString('drawType', keyVal)
    const entId = mxcad.drawEntity(blkRef);
    return entId
}
export function init() {
    // MxFun.addCommand("JGFZ", JGFZ);
    MxFun.addCommand("JGFZH_Draw", JGFZH_Draw);
}