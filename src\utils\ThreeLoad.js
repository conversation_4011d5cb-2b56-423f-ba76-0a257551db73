﻿import * as THREE from 'three';
//const mycanvas = document.getElementById('mycanvas');
//mycanvas.addEventListener("mousewheel", ScaleCanvas, false);
function CanvasEvent(ele, parent) {
    var Ele = ele;
    // 鼠标拖动
    Ele.onmousedown = function (ev) {
        console.log(ev);
        var vector = new THREE.Vector3();
        vector.set(
          (ev.clientX / Ele.clientWidth) * 2 - 1,
          -(ev.clientY / Ele.clientHeight) * 2 + 1,
          0.5);
        vector.unproject(parent.camera);
        Ele.onmousemove = function (ev1) {
            let vector1 = new THREE.Vector3();
            vector1.set(
              (ev1.clientX / Ele.clientWidth) * 2 - 1,
              -(ev1.clientY / Ele.clientHeight) * 2 + 1,
              0.5
            );
            vector1.unproject(parent.camera);
            let vector2 = new THREE.Vector3((vector.x - vector1.x), (vector.y - vector1.y), 0);
            parent.MoveView(vector2);
            vector = new THREE.Vector3();
            vector.set(
              (ev1.clientX / Ele.clientWidth) * 2 - 1,
              -(ev1.clientY / Ele.clientHeight) * 2 + 1,
              0.5);
            vector.unproject(parent.camera);
        }

    }
    Ele.onmouseup = function () {
        Ele.onmousemove = null;
    }
    Ele.onmouseleave = function () {
        Ele.onmousemove = null;
    }
    // 滚轮放缩
    Ele.onmousewheel = function (event) {
        let e = event;
        let delta = Math.max(-1, Math.min(1, (e.wheelDelta || -e.detail)));
        let hei = parent.currentViewRecord.height;
        let wid = parent.currentViewRecord.width;
        let center = parent.currentViewRecord.center;
        let scaleWidth = parent.mycanvas.clientWidth / parent.mycanvas.clientHeight;
        let vector = new THREE.Vector3();
        //let mycanvasLeft = parent.mycanvas.clientWidth;
        let mycanvas = parent.mycanvas.clientHeight;
        let rect = parent.mycanvas.getBoundingClientRect();
        let mycanvasLeft = rect.left;
        let mycanvasTop = rect.top;
        vector.set(
        ((event.clientX - mycanvasLeft) / parent.mycanvas.clientWidth) * 2 - 1,//
        -((event.clientY - mycanvasTop) / parent.mycanvas.clientHeight) * 2 + 1,//
        0.5);
        vector.unproject(parent.camera);
        let leftBottom = new THREE.Vector3(center.x - wid * 0.5, center.y - hei * 0.5, 0);
        let currCenter, currHei;
        if (delta === -1) {
            currHei = hei * 1.25;
            let currWid = wid * 1.25;
            let leftDis = Math.abs(vector.x - leftBottom.x) / hei * currHei;
            let botDis = Math.abs(vector.y - leftBottom.y) / wid * currWid;
            currCenter = new THREE.Vector3(vector.x - leftDis + currWid * 0.5, vector.y - botDis + currHei * 0.5, 0);
        } else if (delta === 1) {
            currHei = hei * 0.8;
            let currWid = wid * 0.8;
            let leftDis = Math.abs(vector.x - leftBottom.x) / hei * currHei;
            let botDis = Math.abs(vector.y - leftBottom.y) / wid * currWid;
            currCenter = new THREE.Vector3(vector.x - leftDis + currWid * 0.5, vector.y - botDis + currHei * 0.5, 0);
        }
        parent.Render(currCenter, currHei);
    }
    //当div视图发生改变的时候 能够将视图进行重新
    Ele.onresize = function () {
        parent.Render(parent.currentViewRecord.center, parent.currentViewRecord.hei);
    }
}
const Document = function (mycanvaId) {
    // 新增属性
    this.MaxCount = 65536;
    this.LineGeom = [];// 定义一个对象 { Geom: 缓冲对象 positions: 点集对象 colors: 颜色的集合 index: 表示点的连接的方式 }
    this.PolylineGeom = []; // 定义多线段 显示  { Geom: 缓冲对象 positions: 点集对象 colors: 颜色的集合 index: 表示点的连接的方式 }
    this.ArcGeom = [];// 添加 圆弧的集合{Geom: 缓冲对象 positions:点的集合 Colors:颜色的集合,Indexs 线的连接方式}
    this.EllipseGeom = []; // 添加椭圆弧的选择集合
    this.MyEllipse = new THREE.EllipseCurve();// 用于计算椭圆的点计算

    this.currentViewRecord = { center: { x: 0, y: 0, z: 0 }, height: 100, width: 100, scaleWidth: 1 };
    this.scene = new THREE.Scene();
    this.camera = null;
    let scope = this;
    this.mycanvas = document.getElementById(mycanvaId);
    this.renderer = new THREE.WebGLRenderer({
        canvas: scope.mycanvas,
        antialias: true,//antialias:true/false是否开启反锯齿
        precision: "highp",//precision:highp/mediump/lowp着色精度选择
        alpha: true,//alpha:true/false是否可以设置背景色透明
        premultipliedAlpha: false,
    });
    let light1 = new THREE.DirectionalLight(0xFF0000, 1.0, 0);
    light1.position.set(100, 100, 200);
    this.scene.add(light1);
    this.EventView = new CanvasEvent(this.mycanvas, this);
    this.renderer.setSize(this.mycanvas.clientWidth, this.mycanvas.clientHeight);
    // this.scene.background = new THREE.Color(  0xf0f0f0 );
    this.scene.background = new THREE.Color(0x363636);
    this.load = new THREE.FontLoader();
    this.font = undefined;// 获得当前文字的字体对象
    this.notYetDBtext = [];// 暂时没有添加到图形空间的实体
    this.fontGeom=new THREE.Geometry();
    // 添加字体数量
    this.fontIndex = 0;
    this.fontGeomMatrimal = [];
    this.fontMaterial = new THREE.MeshBasicMaterial({
        color: new THREE.Color(1, 1, 1),
        transparent: true,
        opacity: 1,
        side: THREE.DoubleSide
    });
    // 初始化加载字体文件 
    this.load.load("custom://bhne/Font/SimHei_Regular.json", function (font1) {
        if (font1 === undefined) return;
        scope.font = font1;
        let count = scope.notYetDBtext.length;
        //添加字体没有添加的对象
        for (let i = 0; i < count; i++) {
            scope.AddText(scope.notYetDBtext[0].position, scope.notYetDBtext[0].textStr, scope.notYetDBtext[0].hei, scope.notYetDBtext[0].widScale,scope.notYetDBtext[0].rotate, scope.notYetDBtext[0].color);
            scope.notYetDBtext.shift();
        }
        // let text = new THREE.Mesh(this.fontGeom, this.fontGeomMatrimal);
        // scope.scene.add(text);
        scope.UpdateRender();
        //scope.renderer
    });
}
Document.prototype.AddLine = function (start, end, color) {
    //let material = new THREE.MeshBasicMaterial( { color: 0xfefefe, wireframe: true, opacity: 0.5 } );
    let geometry = new THREE.Geometry();
    geometry.vertices.push(start);
    geometry.vertices.push(end);
    let material = new THREE.LineBasicMaterial({ color: new Color(color.r, color.g, color.b).ColorRGB2Hex(), opacity: 2, transparent: true });
    let line = new THREE.Line(geometry, material);
    this.scene.add(line);
};
//添加多线段的支持
Document.prototype.AddPolyLine = function (vectors, color) {
    let materival = new THREE.LineBasicMaterial({ color: new Color(color.r, color.g, color.b).ColorRGB2Hex() });
    let geometry = new THREE.Geometry();
    for (let i = 0 ; i < vectors.length; i++) {
        geometry.vertices.push(vectors[i]);
    }
    let line = new THREE.Line(geometry, materival);
    //let width= THREE.view.width;
    this.scene.add(line);
}
// 渲染頁面 顯示的高度会影响界面的大小 center 影响位置
Document.prototype.Render = function (center, hei) {
    let scaleWidth = this.mycanvas.clientWidth / this.mycanvas.clientHeight;
    // 记录实际的长宽高 保存实际的记录
    this.currentViewRecord.scaleWidth = scaleWidth;
    this.currentViewRecord.width = hei * scaleWidth;
    this.currentViewRecord.height = hei;
    this.currentViewRecord.center.x = center.x;
    this.currentViewRecord.center.y = center.y;
    this.currentViewRecord.center.z = center.z;
    let camera = new THREE.OrthographicCamera(-hei * scaleWidth * 0.55 + center.x, hei * scaleWidth * 0.55 + center.x, hei * 0.55 + center.y, -hei * 0.55 + center.y, 1, 10000);
    camera.position.x = 0;
    camera.position.y = 0;
    camera.position.z = 700;
    this.renderer.setSize(this.mycanvas.clientWidth, this.mycanvas.clientHeight);
    this.renderer.render(this.scene, camera);
    this.camera = camera;
}
// 从新刷新Render
Document.prototype.UpdateRender = function () {
    this.renderer.render(this.scene, this.camera);
}
// 绘制圆
Document.prototype.AddCircle = function (center, radius, color) {
    if (center === undefined || radius === undefined) {
        console.log("圆弧半径或者中心点为空!");
        return;
    }
    // 暂时将圆用80多边形进行代替
    let segments = 80;
    let material = new THREE.LineBasicMaterial();//{ color: new Color(color.r, color.g, color.b).ColorRGB2Hex() });
    let geome = new THREE.CircleGeometry(radius, segments);
    geome.vertices.shift();
    let circle1 = new THREE.LineLoop(geome, material);
    circle1.position.x = center.x;
    circle1.position.y = center.y;
    circle1.position.z = center.z;
    this.scene.add(circle1);
}
// 添加绘制圆弧
Document.prototype.AddArc = function (center, radius, start, angle, color) {
    if (center === null || radius === null) {
        console.log("圆弧半径或者中心点为空!");
        return;
    }
    // 暂时将圆和圆弧用80多边形进行代替
    let segments = 80;
    let material = new THREE.LineBasicMaterial();// { color: new Color(color.r, color.g, color.b).ColorRGB2Hex() });
    let geome = new THREE.CircleGeometry(radius, segments, 0, angle);
    geome.vertices.shift();
    let arc1 = new THREE.Line(geome, material);
    arc1.rotation.z = start;
    arc1.position.x = center.x;
    arc1.position.y = center.y;
    arc1.position.z = center.z;
    arc1.closed = false;
    this.scene.add(arc1);
}
// 添加橢圓弧
Document.prototype.AddEllipse = function (position, xRadius, yRadius, rotate, color) {
    let material = new THREE.LineBasicMaterial({ color: new Color(color.r, color.g, color.b).ColorRGB2Hex() });
    let curve = new THREE.EllipseCurve(
      position.x, position.y,
      xRadius, yRadius,
      0, 2.0 * Math.PI,
      false, 0);
    
    let points = curve.getPoints(50);
    let geometry = new THREE.BufferGeometry().setFromPoints(points);
    let line = new THREE.Line(geometry, material);
    //scene.add( line );
    this.scene.add(line);
}
//添加橢圓弧
Document.prototype.AddEllipseArc = function (position, xRadius, yRadius, start, end, rotate, startPoint, endPoint, color) {
    let material = new THREE.LineBasicMaterial();
    let curve = new THREE.EllipseCurve(
      position.x, position.y,
      xRadius, yRadius,
      start, end,
      false, 0);
    let mat = new THREE.Matrix4();
    // mat.makeBasis(position.x, position.y, 0);
    mat.makeRotationAxis(new THREE.Vector3(0, 0, 1), rotate);
    let points = curve.getPoints(100);
    // points.unshift(new THREE.Vector3(startPoint.x, startPoint.y, 0));
    // points.push(new THREE.Vector3(endPoint.x, endPoint.y, 0));
    let geometry = new THREE.BufferGeometry().setFromPoints(points);
    let line = new THREE.Line(geometry, material);
    line.applyMatrix(mat);
    this.scene.add(line);
}
// 添加文字字体 textStr
Document.prototype.AddText = function (position, textStr, hei, widScale, rotate, color) {
    if (this.font === undefined) {
        this.notYetDBtext.push({ position: position, textStr: textStr, hei: hei, widScale: widScale, rotate: rotate, color: color });
        return;
        }
      let matLite=new THREE.MeshBasicMaterial({
            color: color,
            transparent: true,
            opacity: 1,
            side: THREE.DoubleSide
        });
      let shapes= this.font.generateShapes(textStr, hei);
      let geometry=new THREE.ShapeBufferGeometry(shapes);
        // geometry.position.x=position.x;
        // geometry.position.y=position.y;
        // geometry.position.z=0;
        let xMid, yMid, text;
        //计算边界框的大小
        // geometry.computeBoundingBox();
        // xMid=-0.5*(geometry.boundingBox.max.x-geometry.boundingBox.min.x);
        // yMid=-0.5*(geometry.boundingBox.max.y-geometry.boundingBox.min.y);
        // geometry.translate(position.x, position.y, 0);
       
        let mat = new THREE.Matrix4();
        mat.makeRotationAxis(new THREE.Vector3(0, 0, 1), rotate);
    // mat.makeBasis(position.x, position.y, 0);
        //mat.set([widScale, 0, 0, 0,
        //         0, 1, 0, 0,
        //         0, 0, 1, 0,
        //         position.x, position.y, 0, 0
    //]);
       mat.elements[0] = widScale;
       mat.elements[12] = position.x;
       mat.elements[13] = position.y;
       //geometry.applyMatrix(mat);
        //geometry.applyMatrix4(mat);
        text = new THREE.Mesh(geometry, matLite);
        // text.applyMatrix(mat);
        text.applyMatrix(mat);
        // text.position.x = position.x;
        // text.position.y = position.y;
        text.position.z = 0;
        // this.fontGeom.merge(geometry, mat, this.fontIndex);
        // this.fontIndex++;
        // this.fontGeomMatrimal.push(text);
        this.scene.add(text);
}
Document.prototype.MoveView = function (vector) {
    let left = this.camera.left + vector.x;
    let right = this.camera.right + vector.x;
    let top = this.camera.top + vector.y;
    let bottom = this.camera.bottom + vector.y;
    this.currentViewRecord.center = new THREE.Vector3(left * 0.5 + right * 0.5, bottom * 0.5 + top * 0.5, 0);
    this.camera = new THREE.OrthographicCamera(left, right, top, bottom, 1, 10000);
    this.camera.position.x = 0;
    this.camera.position.y = 0;
    this.camera.position.z = 700;
    this.renderer.setSize(this.mycanvas.clientWidth, this.mycanvas.clientHeight);
    this.renderer.render(this.scene, this.camera);
}

// 显示线
Document.prototype.AddLine1 = function (startPoint, endPoint, color) {
    let isNeedCreateGeom = true;
    let curGeomObj;
    for (let i = 0, count = this.LineGeom.length; i < count; i++) {
        let tempGeom = this.LineGeom[i];
        // 表示的是点数
        let count1 = tempGeom.Positions.length / 3;
        if (count1 * 3 + 6 < this.MaxCount) {
            curGeomObj = tempGeom;
            isNeedCreateGeom = false;
            break;
        }
    }
    if (isNeedCreateGeom) {
        let tempGeom = {};
        tempGeom.Geom = new THREE.BufferGeometry();
        tempGeom.Positions = [];
        tempGeom.Colors = [];
        tempGeom.Indexs = [];
        curGeomObj = tempGeom;
        this.LineGeom.push(tempGeom);
    }
    let count1 = curGeomObj.Positions.length / 3;
    curGeomObj.Positions.push(startPoint.x, startPoint.y, startPoint.z);
    curGeomObj.Positions.push(endPoint.x, endPoint.y, endPoint.z);
    curGeomObj.Colors.push(color[0], color[1], color[2]);
    curGeomObj.Colors.push(color[0], color[1], color[2]);
    curGeomObj.Indexs.push(count1, count1 + 1);
    curGeomObj.Geom.setIndex(curGeomObj.Indexs);
    curGeomObj.Geom.removeAttribute('position');
    curGeomObj.Geom.removeAttribute('color');
    curGeomObj.Geom.addAttribute('position', new THREE.Float32BufferAttribute(curGeomObj.Positions, 3));
    curGeomObj.Geom.addAttribute('color', new THREE.Float32BufferAttribute(curGeomObj.Colors, 3));
    // let point =new THREE.Vector3();
    // point.x
}
// 显示多线段
// vertor 表示的是一个数据
Document.prototype.AddPolyline1 = function (vectors, color) {
    let curGeomObj;
    let isNeedCreateGeom = true;
    for (let i = 0, count = this.PolylineGeom.length; i < count; i++) {
        let tempGeomObj = this.PolylineGeom[i];
        let count1 = tempGeomObj.Positions.length / 3;
        if (count1 * 3 + vectors.length * 3 < this.MaxCount) {
            isNeedCreateGeom = false;
            curGeomObj = tempGeomObj;
            break;
        }
    }
    if (isNeedCreateGeom) {
        let tempGeomObj = {};
        tempGeomObj.Geom = new THREE.BufferGeometry();
        tempGeomObj.Positions = [];
        tempGeomObj.Indexs = [];
        tempGeomObj.Colors = [];
        this.PolylineGeom.push(tempGeomObj);
        curGeomObj = tempGeomObj;
    }
    let count1 = curGeomObj.Positions.length / 3;

    for (let i = 0, count = vectors.length; i < count; i++) {
        let tempVector = vectors[i];
        curGeomObj.Positions.push(tempVector.x);
        curGeomObj.Positions.push(tempVector.y);
        curGeomObj.Positions.push(tempVector.z);
        curGeomObj.Colors.push(color[0]);
        curGeomObj.Colors.push(color[1]);
        curGeomObj.Colors.push(color[2]);
        if (i === 0 || i === count - 1) {
            curGeomObj.Indexs.push(count1 + i);
        } else {
            curGeomObj.Indexs.push(count1 + i, count1 + i);
        }
    }
    curGeomObj.Geom.removeAttribute('position');
    curGeomObj.Geom.removeAttribute('color');
    curGeomObj.Geom.addAttribute('position', new THREE.Float32BufferAttribute(curGeomObj.Positions, 3));
    curGeomObj.Geom.addAttribute('color', new THREE.Float32BufferAttribute(curGeomObj.Colors, 3));
    curGeomObj.Geom.setIndex(curGeomObj.Indexs);
}

// 绘制圆弧
Document.prototype.AddArc1 = function (center, start, angle, radius, rotate, color) {
    let curGeomObj;
    let isNeedCreateGeom = true;
    if (center === null || center === undefined || start === null || start === undefined ||
    angle === null || angle === undefined || radius === null || radius === undefined ||
        rotate === null || rotate === undefined) {
        console.log('参数AddArc方法参数不全！');
        return;
    }
    let points = this.GetArcPoints1(radius, center, 80, start, angle, rotate);
    let pointNum = points.length / 3;
    // 添加
    for (let i = 0, count = this.ArcGeom.length; i < count; i++) {
        let tempGeomObj = this.ArcGeom[i];
        let count1 = tempGeomObj.Positions.length / 3;
        if (count1 * 3 + pointNum * 3 < this.MaxCount) {
            isNeedCreateGeom = false;
            curGeomObj = tempGeomObj;
            break;
        }
    }
    if (isNeedCreateGeom) {
        let tempGeomObj = {};
        tempGeomObj.Geom = new THREE.BufferGeometry();
        tempGeomObj.Positions = [];
        tempGeomObj.Indexs = [];
        tempGeomObj.Colors = [];
        this.ArcGeom.push(tempGeomObj);
        curGeomObj = tempGeomObj;
    }
    let count1 = curGeomObj.Positions.length / 3;
    for (let i = 0, count = pointNum; i < count; i++) {
        curGeomObj.Positions.push(points[i * 3]);
        curGeomObj.Positions.push(points[i * 3 + 1]);
        curGeomObj.Positions.push(points[i * 3] + 2);
        curGeomObj.Colors.push(color[0]);
        curGeomObj.Colors.push(color[1]);
        curGeomObj.Colors.push(color[2]);
        if (i === 0 || i === count - 1) {
            curGeomObj.Indexs.push(count1 + i);
        } else {
            curGeomObj.Indexs.push(count1 + i, count1 + i);
        }
    }
    curGeomObj.Geom.removeAttribute('position');
    curGeomObj.Geom.removeAttribute('color');
    curGeomObj.Geom.addAttribute('position', new THREE.Float32BufferAttribute(curGeomObj.Positions, 3));
    curGeomObj.Geom.addAttribute('color', new THREE.Float32BufferAttribute(curGeomObj.Colors, 3));
    curGeomObj.Geom.setIndex(curGeomObj.Indexs);
}

// 通过 center THREE.Vectors 是点集
Document.prototype.GetArcPoints1 = function (radius, center, segments, thetaStart, thetaLength, rotate) {
    radius = radius || 1;
    segments = segments !== undefined ? Math.max(3, segments) : 8;
    thetaStart = thetaStart !== undefined ? thetaStart : 0;
    thetaLength = thetaLength !== undefined ? thetaLength : Math.PI * 2;
    let vertices = [];
    let vertex = new THREE.Vector3();
    let mat = new THREE.Matrix4();
    let moveMat = new THREE.Matrix4();
    moveMat.set([
        1, 0, 0, 0,
        0, 1, 0, 0,
        0, 0, 1, 0,
        center.x, center.y, 0, 0,
    ]);
    mat.makeRotationAxis(new THREE.Vector3(0, 0, 1), rotate);
    mat.elements[12] = center.x;
    mat.elements[13] = center.y;
    for (let s = 0, i = 3; s <= segments; s++, i += 3) {
        let segment = thetaStart + s / segments * thetaLength;
        vertex.x = radius * Math.cos(segment);
        vertex.y = radius * Math.sin(segment);
        vertex.applyMatrix4(mat);
        // vertex.applyMatrix4(moveMat);
        vertices.push(vertex.x, vertex.y, vertex.z);
    }
    return vertices;
}

// 添加 添加椭圆弧
Document.prototype.AddEllipse1 = function (center, radiusX, radiusY, thetaStart, thetaEnd, rotate, color) {
    let ellipseGeom = this.AddArrayList1(this.EllipseGeom, 81);
    this.MyEllipse.aClockwise = false;
    this.MyEllipse.aRotation = rotate;
    this.MyEllipse.aStartAngle = thetaStart;
    this.MyEllipse.aEndAngle = thetaEnd;
    this.MyEllipse.aX = center.x;
    this.MyEllipse.aY = center.y;
    this.MyEllipse.xRadius = radiusX;
    this.MyEllipse.yRadius = radiusY;
    let points = this.MyEllipse.getPoints(80);
    // 添加一个
    let count = ellipseGeom.Positions.length / 3;
    for (let i = 0; i < 81; i++) {
        ellipseGeom.Positions.push(points[i].x, points[i].y, 0);
        ellipseGeom.Colors.push(color[0], color[1], color[2]);
        if (i === 0 || i == 80) {
            ellipseGeom.Indexs.push(count + i);
        } else {
            ellipseGeom.Indexs.push(count + i, count + i);
        }
    }
    ellipseGeom.Geom.removeAttribute('position');
    ellipseGeom.Geom.removeAttribute('color');
    ellipseGeom.Geom.addAttribute('color', new THREE.Float32BufferAttribute(ellipseGeom.Colors, 3));
    ellipseGeom.Geom.addAttribute('position', new THREE.Float32BufferAttribute(ellipseGeom.Positions, 3));
    ellipseGeom.Geom.setIndex(ellipseGeom.Indexs);
    // ellipse.aClockwise
}
// 添加一个 线型的集合
Document.prototype.AddArrayList1 = function (curGeomList, pointNum) {
    let curGeomObj;
    let isNeedCreateGeom = true;
    // pointNum = points.length / 3;
    // 添加
    for (let i = 0, count = curGeomList.length; i < count; i++) {
        let tempGeomObj = curGeomList[i];
        let count1 = tempGeomObj.Positions.length / 3;
        if (count1 * 3 + pointNum * 3 < this.MaxCount) {
            isNeedCreateGeom = false;
            curGeomObj = tempGeomObj;
            break;
        }
    }
    if (isNeedCreateGeom) {
        let tempGeomObj = {};
        tempGeomObj.Geom = new THREE.BufferGeometry();
        tempGeomObj.Positions = [];
        tempGeomObj.Indexs = [];
        tempGeomObj.Colors = [];
        curGeomList.push(tempGeomObj);
        curGeomObj = tempGeomObj;
    }
    return curGeomObj;
}

// 获取线型当中的实体 将线型添加到模型中
Document.prototype.DrawingCurve = function () {
  let geomList = [];
  this.PushGeom(this.LineGeom, geomList);
  this.PushGeom(this.PolylineGeom, geomList);
  this.PushGeom(this.ArcGeom, geomList);
  this.PushGeom(this.EllipseGeom, geomList);
  this.PushGeom(this.MyEllipse, geomList);
  let material = new THREE.LineBasicMaterial({ vertexColors: THREE.VertexColors });
  for (let i = 0; i < geomList.length; i++) {
      let line = new THREE.LineSegments(geomList[i], material);
      this.scene.add(line);
  }
}
// 
Document.prototype.PushGeom=function(list,all){
    for (let i = 0; i < list.length; i++) {
            let Geom = list[i].Geom;
            if(Geom !== null || Geom !== undefined ){
               all.push(Geom);
            }
        }
}




/*
* 定义一个Color类方便颜色之间的转换
**/
var Color = function (r, g, b) {
    this.r = r;
    this.g = g;
    this.b = b;
}
//将RGB 转为 16进制的值
Color.prototype.ColorRGB2Hex = function () {
    let hex = "#" + ((1 << 24) + (this.r << 16) + (this.g << 8) + this.b).toString(16).slice(1);
    return hex;
}
//const doc = new Document(mycanvas);
//doc.Render(new THREE.Vector3(0, 0, 0), 700);
//let color = new THREE.Color(0, 0, 0);
//调用该方法的函数 调用该函数的jsevent对象
export function Drawing(str,jsEvent) {
    var doc = new Document("mycanvasNew");
    console.log(new Date().toLocaleString());
    
    let data = Date.now;
    console.log(new Date().toLocaleString());
    doc.Render(new THREE.Vector3(0, 0, 0), 700);
    let myDB;
    try {
        myDB = JSON.parse(str);
    } catch (err) {
        doc.renderer.clear();
        return;
    }
    if (myDB == null) {
        doc.renderer.clear();
        return;
    }
    let obj = myDB.dic;
    let i = 0;
    for (let p in obj) {
        if (p === "DBText") {
            let textList = obj[p];
            for (p1 in textList) {
                let item = textList[p1];
                doc.AddText(item.position, item.textStr, item.hei, item.widScale, item.rotate, item.color);
            }
        } else if (p === "Line") {
            let textList = obj[p];
            for (p1 in textList) {
                let item = textList[p1];
                // doc.AddLine(item.start, item.end, item.color)
                doc.AddLine1(item.start, item.end, [item.color.r/255,item.color.g/255,item.color.b/255])

            }
        } else if (p === "Circle") {
            let textList = obj[p];
            for (p1 in textList) {
                let item = textList[p1];
                //  doc.AddCircle(item.center, item.radius, item.color)
                doc.AddArc1(item.center, 0, Math.PI * 2, item.radius,0, [1, 1, 1])
            }
        } else if (p === "Arc") {
            let textList = obj[p];
            for (p1 in textList) {
                let item = textList[p1];
                // doc.AddArc(item.center, item.radius, item.start, item.angle, item.color);
                doc.AddArc1(item.center, item.start, item.angle, item.radius, 0, [1, 1, 1])
            }
        } else if (p === "Ellipse") {
            let textList = obj[p];
            for (p1 in textList) {
                let item = textList[p1];
                // doc.AddEllipseArc(item.center, item.xRadius, item.yRadius, item.start, item.end, item.rotate, item.startPoint, item.endPoint, item.color);
                doc.AddEllipse1(item.center, item.xRadius, item.yRadius, item.start, item.end, item.rotate, item.startPoint, item.endPoint, [item.color.r/255, item.color.g/255, item.color.b/255]);
            }
        } else if (p === "") {
        }
        i++;
        //清空 缓存
        if (i > 0 && i % 1500 === 0) {
            // jsEvent.ClearMenaryCache();
        }
    }
    let viewHei = Math.abs(myDB.ext.MinPoint.Y - myDB.ext.MaxPoint.Y);
    let viewWid = Math.abs(myDB.ext.MinPoint.X - myDB.ext.MaxPoint.X);
    let boxHei = doc.mycanvas.clientHeight;
    let boxWid = doc.mycanvas.clientWidth;
    let finalHei = 1;
    if (viewHei / viewWid <= boxHei / boxWid) {
        finalHei = boxHei * viewWid / boxWid;
    }
    if (viewHei / viewWid > boxHei / boxWid) {
        finalHei = Math.abs(myDB.ext.MaxPoint.Y - myDB.ext.MinPoint.Y);
    }
    doc.DrawingCurve();
    doc.Render(
    new THREE.Vector3(
    0.5 * (myDB.ext.MinPoint.X + myDB.ext.MaxPoint.X),
    0.5 * (myDB.ext.MinPoint.Y + myDB.ext.MaxPoint.Y),
    0
    ), Math.abs(
        finalHei
    ));
    //清空 缓存
    //console.log(new Date().toLocaleString());
    //jsEvent.ClearMenaryCache();
}

//const doc = new Document(mycanvas);
//doc.Render(new THREE.Vector3(0, 0, 0), 700);
//let color = new THREE.Color(0, 0, 0);
//调用该方法的函数 调用该函数的jsevent对象
function previewDraw(canvasID, str, jsEvent) {
    var doc = new Document(canvasID);
    console.log(new Date().toLocaleString());

    let data = Date.now;
    console.log(new Date().toLocaleString());
    doc.Render(new THREE.Vector3(0, 0, 0), 700);
    let myDB;
    try {
        myDB = JSON.parse(str);
    } catch (err) {
        doc.renderer.clear();
        return;
    }
    if (myDB == null) {
        doc.renderer.clear();
        return;
    }
    let obj = myDB.dic;
    let i = 0;
    for (let p in obj) {
        if (p === "DBText") {
            let textList = obj[p];
            for (p1 in textList) {
                let item = textList[p1];
                doc.AddText(item.position, item.textStr, item.hei, item.widScale, item.rotate, item.color);
            }
        } else if (p === "Line") {
            let textList = obj[p];
            for (p1 in textList) {
                let item = textList[p1];
                // doc.AddLine(item.start, item.end, item.color)
                doc.AddLine1(item.start, item.end, [item.color.r / 255, item.color.g / 255, item.color.b / 255])

            }
        } else if (p === "Circle") {
            let textList = obj[p];
            for (p1 in textList) {
                let item = textList[p1];
                //  doc.AddCircle(item.center, item.radius, item.color)
                doc.AddArc1(item.center, 0, Math.PI * 2, item.radius, 0, [1, 1, 1])
            }
        } else if (p === "Arc") {
            let textList = obj[p];
            for (p1 in textList) {
                let item = textList[p1];
                // doc.AddArc(item.center, item.radius, item.start, item.angle, item.color);
                doc.AddArc1(item.center, item.start, item.angle, item.radius, 0, [1, 1, 1])
            }
        } else if (p === "Ellipse") {
            let textList = obj[p];
            for (p1 in textList) {
                let item = textList[p1];
                // doc.AddEllipseArc(item.center, item.xRadius, item.yRadius, item.start, item.end, item.rotate, item.startPoint, item.endPoint, item.color);
                doc.AddEllipse1(item.center, item.xRadius, item.yRadius, item.start, item.end, item.rotate, item.startPoint, item.endPoint, [item.color.r / 255, item.color.g / 255, item.color.b / 255]);
            }
        } else if (p === "") {
        }
        i++;
        //清空 缓存
        if (i > 0 && i % 1500 === 0) {
            // jsEvent.ClearMenaryCache();
        }
    }
    let viewHei = Math.abs(myDB.ext.MinPoint.Y - myDB.ext.MaxPoint.Y);
    let viewWid = Math.abs(myDB.ext.MinPoint.X - myDB.ext.MaxPoint.X);
    let boxHei = doc.mycanvas.clientHeight;
    let boxWid = doc.mycanvas.clientWidth;
    let finalHei = 1;
    if (viewHei / viewWid <= boxHei / boxWid) {
        finalHei = boxHei * viewWid / boxWid;
    }
    if (viewHei / viewWid > boxHei / boxWid) {
        finalHei = Math.abs(myDB.ext.MaxPoint.Y - myDB.ext.MinPoint.Y);
    }
    doc.DrawingCurve();
    doc.Render(
    new THREE.Vector3(
    0.5 * (myDB.ext.MinPoint.X + myDB.ext.MaxPoint.X),
    0.5 * (myDB.ext.MinPoint.Y + myDB.ext.MaxPoint.Y),
    0
    ), Math.abs(
        finalHei
    ));
    //清空 缓存
    console.log(new Date().toLocaleString());
    jsEvent.ClearMenaryCache();
}