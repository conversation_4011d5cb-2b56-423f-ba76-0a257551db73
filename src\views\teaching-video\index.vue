<script setup>
import img from '@/assets/images/login-background.jpg'
import { onMounted, ref, watch } from 'vue';
import {getFileList, previewFile} from "@/api/data-file-management/index.js";
import Player from "xgplayer";
// import "xgplayer/dist/index.min.css";
const list = ref([])
const dialogVisible = ref(false)
const currentVideoUrl = ref('')
let playerInstance = null

const videoRef = ref(null)
const loading = ref(false)
const error = ref(null)

// 视频配置
const assetURL = ref(null)
const mimeCodec = 'video/mp4; codecs="avc1.42E01E, mp4a.40.2"'
const totalSegments = 100

// 状态变量
const mediaSource = ref(null)
const sourceBuffer = ref(null)
const segmentLength = ref(0)
const segmentDuration = ref(0)
const bytesFetched = ref(0)
const requestedSegments = ref(Array(totalSegments).fill(false))

const initM=()=>{
  previewFile({fileId:assetURL.value}).then(res=>{
    // videoRef.value.src=res.
  })
}

// 初始化 MediaSource
const initMediaSource = () => {
  if (!('MediaSource' in window) || !MediaSource.isTypeSupported(mimeCodec)) {
    error.value = `不支持的 MIME 类型或编解码器: ${mimeCodec}`
    return
  }

  mediaSource.value = new MediaSource()
  videoRef.value.src = URL.createObjectURL(mediaSource.value)
  mediaSource.value.addEventListener('sourceopen', onSourceOpen)
}

// 源打开事件处理
const onSourceOpen = async () => {
  try {
    loading.value = true
    sourceBuffer.value = mediaSource.value.addSourceBuffer(mimeCodec)
    
    const fileLength = await getFileLength(assetURL.value)
    console.log(`${(fileLength / 1024 / 1024).toFixed(2)} MB`)
    
    segmentLength.value = Math.round(fileLength / totalSegments)
    await fetchRange(assetURL.value, 0, segmentLength.value, appendSegment)
    requestedSegments.value[0] = true
    
    // 添加事件监听
    videoRef.value.addEventListener('timeupdate', checkBuffer)
    videoRef.value.addEventListener('canplay', onCanPlay)
    videoRef.value.addEventListener('seeking', onSeek)
    
  } catch (err) {
    error.value = `初始化失败: ${err.message}`
  } finally {
    loading.value = false
  }
}

// 获取文件长度
const getFileLength = (url) => {
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest()
    xhr.open('head', url)
    xhr.onload = () => {
      const length = xhr.getResponseHeader('content-length')
      console.log("🚀 ~ returnnewPromise ~ length:", length)
      if (length) resolve(Number(length))
      else reject(new Error('无法获取文件长度'))
    }
    xhr.onerror = () => reject(new Error('请求失败'))
    xhr.send()
  })
}

// 获取范围数据
const fetchRange = (url, start, end, callback) => {
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest()
    xhr.open('get', url)
    xhr.responseType = 'arraybuffer'
    xhr.setRequestHeader('Range', `bytes=${start}-${end}`)
    xhr.onload = () => {
      bytesFetched.value += end - start + 1
      callback(xhr.response)
      resolve()
    }
    xhr.onerror = () => reject(new Error('获取数据失败'))
    xhr.send()
  })
}

// 追加片段到缓冲区
const appendSegment = async (chunk) => {
  const arrayBuffer = chunk;
const blob = new Blob([arrayBuffer], { type: 'video/mp4' }); // 根据实际类型调整 MIME
// 2. 生成 Blob URL
const blobUrl = URL.createObjectURL(blob);
videoRef.value.src = blobUrl;
return
}

// 检查缓冲区
const checkBuffer = () => {
  const currentSegment = getCurrentSegment()
  
  if (currentSegment === totalSegments && haveAllSegments()) {
    mediaSource.value.endOfStream()
    videoRef.value.removeEventListener('timeupdate', checkBuffer)
  } else if (shouldFetchNextSegment(currentSegment)) {
    requestedSegments.value[currentSegment] = true
    fetchRange(
      assetURL.value, 
      bytesFetched.value, 
      bytesFetched.value + segmentLength.value, 
      appendSegment
    )
  }
}

// 获取当前片段
const getCurrentSegment = () => {
  return Math.floor(videoRef.value.currentTime / segmentDuration.value) + 1
}

// 检查是否所有片段都已加载
const haveAllSegments = () => {
  return requestedSegments.value.every(val => val)
}

// 检查是否应该获取下一个片段
const shouldFetchNextSegment = (currentSegment) => {
  return (
    videoRef.value.currentTime > segmentDuration.value * currentSegment * 0.8 &&
    !requestedSegments.value[currentSegment]
  )
}

// 视频可以播放时
const onCanPlay = () => {
  segmentDuration.value = videoRef.value.duration / totalSegments
  videoRef.value.play().catch(err => {
    error.value = `自动播放失败: ${err.message}`
  })
}

// 搜索事件处理
const onSeek = () => {
  if (mediaSource.value.readyState === 'open') {
    sourceBuffer.value.abort()
  }
}

// 清理事件监听
const cleanup = () => {
  if (videoRef.value) {
    videoRef.value.removeEventListener('timeupdate', checkBuffer)
    videoRef.value.removeEventListener('canplay', onCanPlay)
    videoRef.value.removeEventListener('seeking', onSeek)
  }
  if (mediaSource.value) {
    mediaSource.value.removeEventListener('sourceopen', onSourceOpen)
  }
} 




let mpUrl=ref('')

const initPlayer = (e) => {
  if(playerInstance) playerInstance.destroy()
  const url = getUrl(e.id)
  mpUrl.value=url
 
}



onBeforeUnmount(() => {
  cleanup()
})






const handleVideoClick = async (item) => {
  /*try {
    const res = await previewFile({ fileId: item.id })
    currentVideoUrl.value = res.data
    dialogVisible.value = true
    nextTick(() => initPlayer(res.data))
  } catch (error) {
    console.error('播放失败', error)
  }*/
  dialogVisible.value = true
assetURL.value=getUrl(item.id)
// initM()
  // nextTick(() => initMediaSource())
    // nextTick(() => initMonted(item))
}

const typeOptions = ref([
  { value: '', label: '全部', checked: true },
  { value: 1, label: '架空设计', checked: false },
  { value: 2, label: '电缆设计', checked: false },
  { value: 3, label: '配电', checked: false },
  { value: 4, label: '通用工具', checked: false },
  { value: 5, label: '报表输出', checked: false },
  { value: 6, label: '其他', checked: false }
])

const form = reactive({
  fileModule: '1',
  fileType: ''
})


const getList = () => {
  loading.value = true;
  const params = {
    ...form
  }
  getFileList(params).then((res) => {
    list.value = res.data;
    console.log('list', list.value)
  }).finally(()=> {
    loading.value = false
  })
}

const getUrl = ((id) => {
  if(!id) return ''
  return `${import.meta.env.VITE_APP_BASE_API}fileHandler/previewFile?fileId=1904131162352070658`
})
const getImgUrl = ((id) => {
  if(!id) return ''
  return `${import.meta.env.VITE_APP_BASE_API}fileHandler/previewFileOriginal?fileId=${id}`
})


const onTypesTagChange = (checked, e) => {
  typeOptions.value.forEach(item => {
    if(item.value === e.value && checked){
      item.checked = checked
      form.fileType = e.value
    } else {
      item.checked = false
    }
  })
  getList()
}
const diaClose=()=>{
  dialogVisible.value = false;
  var video=document.querySelector('video');
  video.pause();
}
onMounted(() => {
  getList();
});
</script>

<template>
  <div class="app-container">
    <div class="types-container">
      <div class="">视频类型：</div>
      <div class="types-container-content">
        <el-check-tag v-for="item in typeOptions" :key="item.value" :checked="item.checked" @change="e => onTypesTagChange(e, item)">{{item.label}}</el-check-tag>
      </div>
    </div>
    <div class="video-header">
      <div class="video-header-left"><h3 class="">教学视频</h3></div>
      <div class="video-header-right"></div>
    </div>
    <el-row v-if="list.length" v-loading="loading" :gutter="20">
      <el-col
        v-for="item in list"
        :key="item.id"
        :lg="6" :md="8" :sm="12" :xs="24"
      >
        <div class="list-card-item" @click="handleVideoClick(item)">
          <div class="cover-wrapper">
            <el-image :src="getImgUrl(item.reservedField)"  class="cover-image" />
<!--            <span class="duration-tag">{{ item.duration }}</span>-->
          </div>
          <div class="content-wrapper">
            <h4 class="title">{{ item.fileName }}</h4>
            <div class="meta-info">
<!--              <span><i class="el-icon-video-play" /> {{ item.views }}</span>-->
              <span>{{ item.insertTime }}</span>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
    <el-empty v-else description="暂无数据" />

    <el-dialog v-model="dialogVisible" @close="diaClose" title="视频播放" width="60%">
      <!-- <video ref="videoRef" controls width="100%"></video> 
      <div v-if="loading">加载中...</div>
      <div v-if="error" class="error">{{ error }}</div> -->
      <video src="http://wghgl-test.oss-zj-3-a.ops.sgmc.sgcc.com.cn/onlinedesign/2025-03-24/一图三态系统演示最终版.mp4" autoplay controls width="100%" ></video>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
 #progress-container {
      height: 5px;
      background: #eee;
      margin-bottom: 10px;
    }
    #progress-bar {
      height: 100%;
      background: #2196F3;
      width: 0%;
      transition: width 0.3s;
    }
.types-container {
  display: flex;
  align-items: center;
  padding: 16px;
  .types-container-content {
    display: flex;
    gap: 8px;
  }
}
.video-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
}
.list-card-item {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);
  transition: transform 0.3s;
  cursor: pointer;
  margin-bottom: 20px;

  &:hover {
    transform: translateY(-5px);
  }

  .cover-wrapper {
    position: relative;
    padding-top: 56.25%;
    
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      opacity: 0;
      transition: opacity 0.3s ease;
      border-radius: 8px 8px 0 0;
    }
    
    &::before {
      content: '▶';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: white;
      font-size: 24px;
      opacity: 0;
      transition: all 0.3s ease;
      z-index: 1;
      text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    }
    
    &:hover::after,
    &:hover::before {
      opacity: 1;
    }
    
    .cover-image {
      position: absolute;
      top: 0;
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 8px 8px 0 0;
    }
    
    .duration-tag {
      position: absolute;
      right: 8px;
      bottom: 8px;
      background: rgba(0,0,0,0.7);
      color: white;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
    }
  }

  .content-wrapper {
    padding: 12px;

    .title {
      margin: 0 0 8px;
      font-size: 14px;
      line-height: 1.4;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    .meta-info {
      display: flex;
      justify-content: space-between;
      color: #999;
      font-size: 12px;

      i {
        margin-right: 4px;
      }
    }
  }
}

.pagination-wrapper {
  margin-top: 24px;
  justify-content: center;
}
</style>