<script setup>
import {useIframeCommunication} from "@/hooks/useIframeCommunication.js";
import {getEquipmentInfoString} from "@/api/annotation/index.js";
import {useRoute} from "vue-router";
import {ElMessage} from "element-plus";

console.log('标注设备')
const route = useRoute()

const taskId = route.query.id
const { sendMessage, cleanup } = useIframeCommunication();

const cadAppRef = inject("cadAppRef");

const init = () => {
  const msg = {
    content: "Ancillary_Facilities_Annotation",
    type: "onlycad",
    options:{name:'JGFZH_Draw'}
  }
  sendMessage(cadAppRef.value, msg, (res => {
    if(res.type!="showMessage"){
      
    }
    // ElMessage.error('没有找到对应设备 ')
  }))
}

init()

</script>

<template>

</template>

<style lang="scss" scoped>

</style>