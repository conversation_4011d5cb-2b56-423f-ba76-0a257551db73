import { MxCADUiPrEntity } from "mxcad";

// 读取扩展数据。
async function MxTest_ReadxData() {
    // 选择对象
    let selEntity = new MxCADUiPrEntity();
    selEntity.setMessage("选择对象");
    let id = await selEntity.go();
    if (!id.isValid()) return;
    
    // 获取对象
    let ent = id.getMcDbEntity();
    if (ent === null) return; 

    // 得到对象的扩展数据
    let data = ent.getxData();
    //data.PrintData();
    data.forEach((val, type, dxf) => {
        console.log(JSON.stringify({ val: val, type: type, dxf: dxf }));
    })

    //let data = ent.getxDataString("DataName");
    //MxFun.acutPrintf(data.val + "\n");
};

// 调用读取扩展数据的方法
MxTest_ReadxData()