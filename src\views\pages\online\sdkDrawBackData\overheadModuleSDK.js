import { saveAllInfo, saveAllInfos, getEquipmentState} from "@/api/desginManage/draw.js";
import { voltageLevelData, getLegendTypeKeyByTowerType, getTowerArrangementKeyByLoop, getNoModuleTypeKey, getlegendTypeKey } from "@/views/pages/online/commonData.js";
import { getEquipmentModel } from "@/views/pages/online/saveModelInfo.js";
import { ElMessage } from "element-plus";
import { calculateAngle } from "@/views/pages/online/geometricCalculation.js";
import { getPole, getNoModuleIdByVoltageLegendType } from "@/api/onlineDesign/matchData.js";
import useProjectInfoStore from "@/store/modules/projectInfo.js";
import { generateUuid } from "@/utils";
import { pa } from "element-plus/es/locales.mjs";

// 杆塔线路绘制结束
export const drawEndTowerLine = async (params,info) => {
  if(!params) return

  console.log('drawEndTowerLine', params)
  const { data } = params
  const projectInfoStore = useProjectInfoStore();
  const taskId = new URLSearchParams(new URL(window.location.href).search).get(
    "id"
  );
  //获取地理条件设置 气象区
  const geographicCondition = projectInfoStore.getGeographicConditionsSetting(taskId)
  //获取基础参数杆塔排列方式
  const towerlineSetting = projectInfoStore.getBaseSetting('towerlineSetting')
  const paramsSaveAllInfos = new Map()
  const towerAutoModuleInfos = []//记录杆塔坐标，自动选型参数
  data.forEach((item) => {
    if(item.CLASS_NAME === "PWPolePSR") {
      if(typeof(item?.extendData) !== 'object' && typeof(item?.extendData) !== 'undefined'){
        item.extendData = JSON.parse(item.extendData)
      }
        if(info&&item.LONGITUDE){
                item.extendData=info
              }
      //根据回路数获取基础参数杆塔排列方式属性key
      const towerArrangement = getTowerArrangementKeyByLoop(item.extendData?.loop)
      //根据物理杆塔得到运行杆塔，一个物理杆可能对应多个运行杆
      const tempTowers = data.filter((o) => o.POLE_PSR_ID === item.DEV_ID) //运行杆
      //杆塔拓扑关系
      const towerTopologyrelations = {
        AssociatedIn: [], // 进线拓扑
        AssociatedOut: [], // 出线拓扑
        AssociatedParent: [], //
        AssociatedChild: [], //
        AssociatedLabel: [], // 目前不涉及
        AssociatedLabelOwner: [], // 目前不涉及
        AssociatedFile: [], // 目前不涉及
      }
      const Voltagerating = voltageLevelData.find(
        (v) => v.id === item.extendData?.voltageLevel
      )?.text;
      const lineLegendTypeKey = Voltagerating ? (Voltagerating === "380V" ? "TY_DY_380DHJKX" : Voltagerating === "220V" ? "TY_DY_220DHJKX" : "TY_DHJKX") : ''
      const inLines = []
      const outLines = []
      //推导杆型号需要的参数
      console.log(item,'hhhhhhhhhhhh')
      let pointX=item.SHAPE.split(' ')[1].split('(').join('')
      let pointY=item.SHAPE.split(' ')[2].split(')').join('')
      console.log(pointX,pointY)
      const towerAutoModuleInfo = {towerGuid: item.DEV_ID, point: [pointX, pointY, 0], autoModule: item.extendData?.checkList2.indexOf("1") > -1, inLines, outLines, towerArrangement}
      for(let i=0; i< tempTowers.length; i++){//运行杆 统计导线
        const tempTower = tempTowers[i]
        const inLine = data.find((o) => o.END_POLE_ID === tempTower.DEV_ID) //上游导线
        const outLine = data.find((o) => o.START_POLE_ID === tempTower.DEV_ID) //下游导线
        //导线拓扑关系
        const lineTopologyrelations = {
            AssociatedIn: [], // 进线拓扑
            AssociatedOut: [], // 出线拓扑
            AssociatedParent: [], //
            AssociatedChild: [], //
            AssociatedLabel: [], // 目前不涉及
            AssociatedLabelOwner: [], // 目前不涉及
            AssociatedFile: [], // 目前不涉及
        }
        if(inLine){
          inLines.push(inLine)
          towerTopologyrelations.AssociatedIn.push(inLine.DEV_ID)//当前杆塔进线是上游导线
          const towerIn = data.find(o=>o.DEV_ID === inLine.START_POLE_ID)?.POLE_PSR_ID//物理杆
          //上游导线在返回的数据里是否存在进线杆塔
          if(towerIn){ lineTopologyrelations.AssociatedIn.push(towerIn) }
          //上游导线的出线是当前杆塔
          lineTopologyrelations.AssociatedOut.push(item.DEV_ID)
          const coordinates = inLine.SHAPE.match(/(\d+\.\d+)\s+(\d+\.\d+)/g)
          //导线数据
          const line1 = getEquipmentModel("Line", coordinates, {
            moduleId: item.extendData.lines[i].lineModuleId, // 顶层id 需要调接口获取
            legendTypeKey: lineLegendTypeKey, // 图元类型
            legendState: item.extendData.lines[i].lineState, // 状态
            legendGuidKey: inLine.DEV_ID,
            engineeringId: taskId,
          })
          line1.privatepropertys = JSON.stringify({
            Voltagerating,// 电压等级
            Span: inLine.SPAN.toFixed(2),
            TDDJ: inLine.SPAN.toFixed(2),
            LineNumber: item.extendData.lines[i].lineName, //线路名称
          })
          line1.topologyrelations = JSON.stringify({...lineTopologyrelations })
          paramsSaveAllInfos.set(inLine.DEV_ID, line1)
        }
        if(outLine){
          outLines.push(outLine)
          towerTopologyrelations.AssociatedOut.push(outLine.DEV_ID)//当前杆塔出线是下游导线
          const towerOut = data.find(o=>o.DEV_ID === outLine.END_POLE_ID)?.POLE_PSR_ID//物理杆
          //下游导线在返回的数据里是否存在出线杆塔
          if(towerOut){ lineTopologyrelations.AssociatedOut.push(towerOut) }
          //下游导线的进线是当前杆塔
          lineTopologyrelations.AssociatedIn.push(item.DEV_ID)
          const coordinates = outLine.SHAPE.match(/(\d+\.\d+)\s+(\d+\.\d+)/g)
          console.log(coordinates,'coordinatescoordinatescoordinates')
          //导线数据
          const line1 = getEquipmentModel("Line", coordinates, {
            moduleId: item.extendData?.lines[i].lineModuleId, // 顶层id 需要调接口获取
            legendTypeKey: lineLegendTypeKey, // 图元类型
            legendState: item.extendData?.lines[i].lineState, // 状态
            legendGuidKey: outLine.DEV_ID,
            engineeringId: taskId,
          })
          line1.privatepropertys = JSON.stringify({
            Voltagerating,// 电压等级
            Span: outLine.SPAN,
            TDDJ: outLine.SPAN,
            LineNumber: item.extendData?.lines[i].lineName, //线路名称
          }),
          line1.topologyrelations = JSON.stringify({...lineTopologyrelations })
          paramsSaveAllInfos.set(outLine.DEV_ID, line1)
        }
      }
      let userNumber = item.extendData?.ganPrefix + item?.extendData?.ganStart + item?.extendData?.ganSuffix
      if(item.extendData?.checkList1 === "水泥双杆"){
        userNumber = userNumber + "A," + userNumber + "B"
      }
      //是否终端杆
      let isTerminalPole = (towerTopologyrelations.AssociatedIn.length === 0 || towerTopologyrelations.AssociatedOut.length === 0)
      //推导杆型需要的参数
      towerAutoModuleInfo.params = {
        "angle": 0,//杆塔角度
        "pointID": item.DEV_ID,
        "arrayMode": towerlineSetting[towerArrangement[3]],//读基础参数
        "cementMaterialID": item.extendData?.cement,//杆塔水泥杆物料ID
        "gtlb": item.extendData?.checkList1,//杆塔类别
        isTerminalPole, //是否终端杆
        "loopNumber": item?.extendData?.loop,//回路数
        "meteorological": geographicCondition.GeographicName, //气象条件
        "spanLength": 0, //总档距长度
        "strainLength": Number(item.extendData?.drawingSetting || 0), //耐张档距长度
        "equipType": item.extendData?.checkList1,
        "tddj": Math.max(...towerAutoModuleInfo.inLines.map(o=>o.SPAN)) ?? 0,
        "voltage": Voltagerating,
        "zxArr": [ towerlineSetting[towerArrangement[0]] ],//读基础参数 直线
        "zjArr": [ towerlineSetting[towerArrangement[1]] ],//读基础参数 转角
        "nzArr": [ towerlineSetting[towerArrangement[2]] ],//读基础参数 耐张
        "zdArr": [ towerlineSetting[towerArrangement[3]] ] //读基础参数 终端
      }
      towerAutoModuleInfos.push(towerAutoModuleInfo)
      //杆塔的数据
      let equipmentIdPointX=item.SHAPE.split(' ')[1].split('(').join('')
      let equipmentIdPointY=item.SHAPE.split(' ')[2].split(')').join('')
      const sng = getEquipmentModel("Point", `${equipmentIdPointX} ${equipmentIdPointY}`, {
        legendTypeKey: getLegendTypeKeyByTowerType(item.extendData?.checkList1 || '水泥杆'), // 图元类型
        legendState: item.extendData?.status, // 状态
        legendGuidKey: item.DEV_ID,
        engineeringId: taskId,
      })
      sng.privatepropertys = JSON.stringify(item.extendData ? {
        Longitude: item.LONGITUDE,
        Latitude: item.LATITUDE,
        GTowerHeght: item?.extendData?.gggGg, //钢管杆杆高（m）
        // 物料ID	 CementMaterialID
        // 角度描述	RotateDescribe
        // 角度类型	AngleType
        // 转角	Rotate
        TowerType: item?.extendData?.checkList1, //杆塔类型
        TTowerHeght: item?.extendData?.gggGg, //塔高
        TZ: item?.extendData?.soil, //土质
        DX: item?.extendData?.terrain, //地形
        Voltagerating,// 电压等级
        HDCD: item?.extendData?.crossLength, //横担长度
        JC: item?.extendData?.foundation, //基础
        Modality: item?.extendData?.gggXs, //钢管杆形式
        // 余线	Yu
        LineNumber: item?.extendData?.lines.map(o=>o.lineName).join(','), //线路名称
        UserNumber: userNumber //编号
      } : {})
      sng.topologyrelations = JSON.stringify({...towerTopologyrelations })
      paramsSaveAllInfos.set(item.DEV_ID, sng)
    }
  });
  console.log("杆塔数据：", towerAutoModuleInfos)
  for(let i=0; i<towerAutoModuleInfos.length; i++){
    const towerAutoModuleInfo = towerAutoModuleInfos[i]
    //自动选型 非终端杆 求角度
    if(towerAutoModuleInfo.autoModule && !towerAutoModuleInfo.params.isTerminalPole){
      let p1, p2, p3
      p2 = towerAutoModuleInfo.point
      if(i === 0){
        p1 = [...towerAutoModuleInfo.inLines[0].SHAPE.match(/(\d+\.\d+)\s+(\d+\.\d+)/g)[0].split(' '), 0]
      }
      else{
        p1 = towerAutoModuleInfos[i-1].point
      }
      if((i === 0 && towerAutoModuleInfos.length === 1) || (i + 1) === towerAutoModuleInfos.length){
        p3 = [...towerAutoModuleInfo.outLines[0].SHAPE.match(/(\d+\.\d+)\s+(\d+\.\d+)/g)[1].split(' '), 0]
      }
      else{
        p3 = towerAutoModuleInfos[i+1].point
      }
      towerAutoModuleInfo.params.angle = calculateAngle(p1, p2, p3) * 180 / Math.PI
      if (towerAutoModuleInfo.params.angle <= 15)
      {
        if (towerAutoModuleInfo.params.spanLength >= towerAutoModuleInfo.params.strainLength)
        {//耐张  累计长度大于耐张档距
          towerAutoModuleInfo.params.arrayMode = towerlineSetting[towerAutoModuleInfo.towerArrangement[2]]
        }
        else
        {//直线
          towerAutoModuleInfo.params.arrayMode = towerlineSetting[towerAutoModuleInfo.towerArrangement[0]]
        }
      }
      else
      {//转角
        towerAutoModuleInfo.params.arrayMode = towerlineSetting[towerAutoModuleInfo.towerArrangement[1]]
      }
    }
    towerAutoModuleInfo.params.spanLength += towerAutoModuleInfo.params.tddj
    //未选型
    let noModuleID = await getNoModuleIdByVoltageLegendType(towerAutoModuleInfo.params.voltage || '10kV', paramsSaveAllInfos.get(towerAutoModuleInfo.towerGuid).legendTypeKey)
    if(towerAutoModuleInfo.autoModule){
      const poleRes = await getPole(towerAutoModuleInfo.params)
      console.log(poleRes)
      if(poleRes.code === 200){
        if(poleRes.data.moduleDes === '未选型'){
          paramsSaveAllInfos.get(towerAutoModuleInfo.towerGuid).moduleId = noModuleID
          console.log('getNoModuleId', noModuleID)
        } else{
          paramsSaveAllInfos.get(towerAutoModuleInfo.towerGuid).moduleId = poleRes.data.assID
        }
      }
    } else if(towerAutoModuleInfo.params.voltage){
      paramsSaveAllInfos.get(towerAutoModuleInfo.towerGuid).moduleId = noModuleID
    }
  }
  console.log(paramsSaveAllInfos, "paramsparamsparamsparams");
  saveAllInfos([...paramsSaveAllInfos.values()]).then((res) => {
    if (res.code === 200) {
        ElMessage.success("保存成功");
    } else {
        ElMessage.error(res.msg);
    }
  });
}

/** 绘制柱上设备
 * @param {*} res sdk返回数据
 * @param {*} listForm 页面表单数据
 * @param {*} drawInfo 绘制对象数据
 */
export const drawEndZSSB = (res, listForm, drawInfo) =>{
  const taskId = new URLSearchParams(new URL(window.location.href).search).get(
    "id"
  );
  drawInfo.sdkClassname=drawInfo.sdkClassname.split('-')[0]
  let sdkArr = res.params.result.data;
  let poleInfo = res.params.result.poleInfo;
  const sdkList = sdkArr.find(
    (item) => getBeforeDash(drawInfo.sdkClassname) === item.CLASS_NAME
  );
  const params = []
  const zssb = getEquipmentModel("ParentMiddle", `${sdkList.X} ${sdkList.Y}`, {
    moduleId: listForm.deviceModel, // 顶层id
    legendTypeKey: drawInfo.legendtypekey, // 图元类型
    legendState: listForm.status, // 状态
    legendGuidKey: sdkList.DEV_ID, // 设备id
    engineeringId: taskId,
  })
  zssb.privatepropertys = JSON.stringify({
    UserNumber: listForm.deviceCode, // 编号
    Has_IntelligentSwitch: listForm.isFlag, // 是否智能化
    Voltagerating: listForm.voltageLevel,
  })
  zssb.topologyrelations.AssociatedParent = [poleInfo?.DEV_ID]
  let pointXY={
    X:poleInfo.SHAPE.match(/(\d+\.\d+)\s+(\d+\.\d+)/g)[0].split(' ')[0],
    Y:poleInfo.SHAPE.match(/(\d+\.\d+)\s+(\d+\.\d+)/g)[0].split(' ')[1],

  }
  params.push(zssb)
  const sng = getEquipmentModel("Point", `${pointXY.X} ${pointXY.Y}`,{
    legendTypeKey: getlegendTypeKey(poleInfo.CLASS_NAME), // 图元类型
    legendGuidKey: poleInfo.DEV_ID, // 设备id
    engineeringId: taskId,
  })
  sng.topologyrelations.AssociatedChild = [sdkList?.DEV_ID]
  params.push(sng)
  console.log("处理完后的数据", params);
  saveAllInfos(params).then((res) => {
    if (res.code === 200) {
      ElMessage.success("保存成功");
    } else {
      ElMessage.error(res.msg);
    }
  });
}
function getBeforeDash(str) {
  const dashIndex = str.indexOf('-');
  return dashIndex === -1 ? str : str.slice(0, dashIndex);
}
/** 插入柱上设备绘制结束（电缆上杆）
 * @param {*} e 
 */
export const drawEndDlSg = (res, listForm, drawInfo) => {
  // TODO  接口待调整  选择的杆塔判断是否已经存在
  if(!res || res.result.length === 0){
    return
  }
  const taskId = new URLSearchParams(new URL(window.location.href).search).get(
    "id"
  );
  const selectEntity = res.result[0]
  const AssociatedParent = [selectEntity.DEV_ID];
  const legendGuidKey = generateUuid();
  let legendState = "";
  getEquipmentState({ equipmentId: selectEntity.DEV_ID, taskId: taskId }).then((res) => {
    if (res.data !== "") {
      legendState = res.data;
    } else {
      legendState = "Original"; 
    }
    const params = []
    const sng = getEquipmentModel("Point", `${selectEntity.X} ${selectEntity.Y}`, {
      legendTypeKey: getlegendTypeKey(selectEntity.CLASS_NAME), // 图元类型
      legendState,
      legendGuidKey: selectEntity.DEV_ID, // 设备id
      engineeringId: taskId,
    })
    sng.topologyrelations.AssociatedChild = [legendGuidKey]
    params.push(sng)

    const sg = getEquipmentModel("ParentMiddle", `${selectEntity.X} ${selectEntity.Y}`, {
      moduleId: listForm.deviceModel, // 顶层id
      legendTypeKey: drawInfo.legendtypekey, // 图元类型
      legendState: listForm.status, // 状态
      legendGuidKey, // 设备id
      engineeringId: taskId,
    })
    sg.privatepropertys = JSON.stringify({
      UserNumber: listForm.deviceCode, // 编号
      Has_IntelligentSwitch: listForm.isFlag, // 是否智能化
    })
    sg.topologyrelations.AssociatedParent = AssociatedParent
    params.push(sg)
    
    console.log(params, "paramsparamsparamsparams");
    saveAllInfos(params).then((res) => {
      if (res.code === 200) {
        ElMessage.success("保存成功");
      } else {
        ElMessage.error(res.msg);
      }
    });
  });
};

/** 接地绘制结束
 * @param {*} e 
 */
export const drawEndjdhz = ( listForm,list, drawInfo) => {
  console.log(listForm,list,drawInfo,'数据')
  const taskId = new URLSearchParams(new URL(window.location.href).search).get(
    "id"
  );
  let className=list.sdkClassName.split('-')
  const selectEntity = drawInfo.find(o=>o.CLASS_NAME === className[0]) //绘制的接地
  console.log(selectEntity)
  let legendState = "";
  getEquipmentState({ equipmentId: selectEntity.DEV_ID, taskId: taskId }).then((res) => {
    if (res.data !== "") {
      legendState = res.data;
    } else {
      legendState = "Original"; 
    }
    const params = []
    const jd = getEquipmentModel("ParentMiddle", `${selectEntity.X} ${selectEntity.Y}`, {
      moduleId: listForm.groundingModel, // 顶层id
      legendTypeKey: className[1], // 图元类型
      legendState: listForm.status, // 状态
      legendGuidKey: selectEntity.DEV_ID, // 设备id
      engineeringId: taskId,
    })
    jd.topologyrelations.AssociatedParent = [selectEntity.INSTALL_SITE_ID]
    params.push(jd)
    const sng = getEquipmentModel("Point", `${selectEntity.X} ${selectEntity.Y}`, {
      legendTypeKey: getlegendTypeKey(selectEntity.INSTALL_SITE_CLASS_NAME), // 图元类型
      legendState: legendState, // 状态
      legendGuidKey:selectEntity.INSTALL_SITE_ID, // 设备id
      engineeringId: taskId,
    })
    sng.topologyrelations.AssociatedChild = [selectEntity.DEV_ID]
    params.push(sng)
    console.log(params, "paramsparamsparamsparams");
    saveAllInfos(params).then((res) => {
      if (res.code === 200) {
        ElMessage.success("保存成功");
      } else {
        ElMessage.error(res.msg);
      }
    });
  });
}
 /**  绘制拉线结束
 * @param {*} res 
 * @param {*} listForm 
 * @param {*} sdkClassName 
 * @returns 
 */
export const drawEndLX = (res, listForm, sdkClassName) =>{
  if(!res || res.data.length === 0){
    return
  }
  const datas = sdkClassName.split('-')
  const lxSDK = res.data.find(o=>o.CLASS_NAME === datas[0])//得到sdk拉线数据
  if(!lxSDK){
    ElMessage.error('未能查询到拉线数据：' + datas[0]);
    return
  }
  const taskId = new URLSearchParams(new URL(window.location.href).search).get(
    "id"
  );
  const params = []
  const lx = getEquipmentModel("ParentMiddle",`${lxSDK.X} ${lxSDK.Y}`, {
    moduleId: listForm.lxfa, // 顶层id
    legendTypeKey: datas[1], // 图元类型
    legendState: listForm.status, // 状态
    legendGuidKey: lxSDK.DEV_ID, // 设备id
    engineeringId: taskId,
  })
  lx.privatepropertys = JSON.stringify({
    Voltagerating: voltageLevelData.find(o=>o.value === listForm.voltageLevel)?.text, // 电压等级
  })
  lx.topologyrelations.AssociatedParent = [lxSDK.INSTALL_SITE_ID]
  params.push(lx)

  const sng = getEquipmentModel("Point", `${lxSDK.X} ${lxSDK.Y}`, {
    legendTypeKey: getlegendTypeKey(lxSDK.INSTALL_SITE_CLASS_NAME), // 图元类型
    legendGuidKey: lxSDK.INSTALL_SITE_ID, // 设备id
    engineeringId: taskId,
  })
  sng.topologyrelations.AssociatedChild = [lxSDK.DEV_ID]
  params.push(sng)
   
  console.log("处理完后的数据", params);
  saveAllInfos(params).then((res) => {
    if (res.code === 200) {
      ElMessage.success("保存成功");
    } else {
      ElMessage.error(res.msg);
    }
    });
}
/** 增加附属设施-验电接地环
 * @param {*} e 
 */
export const drawEndydjdh = (listForm, list, drawInfo) => {
  console.log(listForm,list,drawInfo,'数据')
  const taskId = new URLSearchParams(new URL(window.location.href).search).get(
   "id"
 );
 const datas = list.sdkClassname.split('-')[0]
 const selectEntity = drawInfo.find(o=>o.CLASS_NAME===datas) //绘制的接地
 console.log(selectEntity)
 let legendState = "";
 getEquipmentState({ equipmentId: selectEntity.DEV_ID, taskId: taskId }).then((res) => {
    if (res.data !== "") {
      legendState = res.data;
    } else {
      legendState = "Original"; 
    }
    const params = []
    const fs = getEquipmentModel("ParentMiddle", `${selectEntity.X} ${selectEntity.Y}`, {
      moduleId: listForm.deviceModel, // 顶层id
      legendTypeKey: list.legendtypekey, // 图元类型
      legendState: listForm.status, // 状态
      legendGuidKey: selectEntity.DEV_ID, // 设备id
      engineeringId: taskId,
    })
    fs.topologyrelations.AssociatedParent = [selectEntity.INSTALL_SITE_ID]
    params.push(fs)

    const sng = getEquipmentModel("Point", `${selectEntity.X} ${selectEntity.Y}`, {
      legendTypeKey: getlegendTypeKey(selectEntity.INSTALL_SITE_CLASS_NAME), // 图元类型
      legendState: legendState, // 状态
      legendGuidKey:selectEntity.INSTALL_SITE_ID, // 设备id
      engineeringId: taskId,
    })
    sng.topologyrelations.AssociatedChild = [selectEntity.DEV_ID]
    params.push(sng)
   
    console.log(params, "paramsparamsparamsparams");
    saveAllInfos(params).then((res) => {
    if (res.code === 200) {
      ElMessage.success("保存成功");
    } else {
      ElMessage.error(res.msg);
    }
   });
 });
}
/** 绘制导线结束
 * 三种情况 
 * 1、连接两个 不存在关联的杆塔
 * 2、在存在线路关联的杆塔上 绘制
 * 3、同时存在两种情况
 * @param {*} res 
 * @param {*} listForm 
 * @param {*} lineInfo {name: '线路1', key: 'New', sk: '1102'}
 */
export const drawEndDXHZ = async (res, listForm, lineInfo) => {
  console.log(res, listForm, '数据')
  if(!res) return
  const taskId = new URLSearchParams(new URL(window.location.href).search).get(
    "id"
  );
  const params = []
  const findLineKeys = [["HEAD_DEV_ID", "HEAD_DEV_CLASS_NAME"], ["TAIL_DEV_ID", "TAIL_DEV_CLASS_NAME"]]
  const findCableKeys = [["START_DEV_ID", "START_DEV_CLASS_NAME"], ["END_DEV_ID", "END_DEV_CLASS_NAME"]]
  const sdkArr = res.data;
  const sdkLines = sdkArr.filter(o=> (o.CLASS_NAME === "PWConductorSecPSR" || o.CLASS_NAME === "PWCableSecPSR") && o.addFlag) //绘制的导线
  for(let itemLine of sdkLines) {
    let coordinates = itemLine.SHAPE.match(/(\d+\.\d+)\s+(\d+\.\d+)/g)
    const lineLegendTypeKey = getlegendTypeKey(itemLine.CLASS_NAME, itemLine.VOLTAGELEVEL_ID)// 图元类型
    const lineData = getEquipmentModel("Line", coordinates, {legendTypeKey: lineLegendTypeKey, legendGuidKey: itemLine.DEV_ID, engineeringId: taskId, legendState: lineInfo[0].key, moduleId: lineInfo[0].sk })
    lineData.privatepropertys = {
      Voltagerating: voltageLevelData.find(o=>o.id === listForm.voltageLevel)?.text,// 电压等级
      Span: itemLine.SPAN ?? itemLine.LENGTH, 
      TDDJ: itemLine.SPAN ?? itemLine.LENGTH,
      LineNumber: lineInfo[0].name, //线路名称
    }
    if(itemLine.CLASS_NAME === 'PWCableSecPSR') {//如果是电缆 型号为空
      let noModuleID = await getNoModuleIdByVoltageLegendType(lineData.privatepropertys.Voltagerating, lineLegendTypeKey)
      lineData.moduleId = noModuleID
    }
    const tpKeys = itemLine.CLASS_NAME === "PWConductorSecPSR" ? findLineKeys : findCableKeys
    for(let i = 0; i < tpKeys.length; i++) {// i===0 首端  i===1 末端
      let startID = itemLine[tpKeys[i][0]]
      if(itemLine[tpKeys[i][1]] === "PWPolesitePSR"){// 起始关联的运行杆需要找到对应的物理杆
        startID = sdkArr.find(o=>o.DEV_ID === itemLine[tpKeys[i][0]]).POLE_PSR_ID
      }
      lineData.topologyrelations[i === 0 ? 'AssociatedIn' : 'AssociatedOut'].push(startID)
      const findStart = params.find(o=>o.legendGuidKey === startID)
      if(findStart){
        findStart.topologyrelations[i === 0 ? 'AssociatedOut' : 'AssociatedIn'].push(itemLine.DEV_ID)
      } else {
        const towerSDK = sdkArr.find(o=>o.DEV_ID === startID)
        const legendTypeKey = getlegendTypeKey(towerSDK.CLASS_NAME)
        let legendState = await getEquipmentState({ equipmentId: startID, taskId: taskId })
        legendState = legendState.data || "Original";
        //sdk没有专门的导线绘制，提供的兼容方法也可以绘制出杆塔
        if(legendTypeKey === "TY_SNG" && legendState === "Original"){
          legendState = "New"
        }
         if(towerSDK.BLOCK_ID&&towerSDK.BLOCK_ID.split('').includes('-')){//暂时这么区分原有杆塔
          legendState = "Original"
        }
        let pointXY={
          X:towerSDK.SHAPE.match(/(\d+\.\d+)\s+(\d+\.\d+)/g)[0].split(' ')[0],
          Y:towerSDK.SHAPE.match(/(\d+\.\d+)\s+(\d+\.\d+)/g)[0].split(' ')[1],

        }
        console.log(towerSDK.SHAPE.match(/(\d+\.\d+)\s+(\d+\.\d+)/g)[0].split(' ')[0],'ddddddddddd')
        console.log(towerSDK,'towerSDKtowerSDKtowerSDK')
        const tower = getEquipmentModel("Point", `${pointXY.X} ${pointXY.Y}`, {legendTypeKey, legendGuidKey: startID, engineeringId: taskId, legendState })
        let noModuleID = await getNoModuleIdByVoltageLegendType(lineData.privatepropertys.Voltagerating, legendTypeKey)
        tower.moduleId = noModuleID
        tower.topologyrelations[i === 0 ? 'AssociatedOut' : 'AssociatedIn'].push(itemLine.DEV_ID)
        params.push(tower)
      }
    }
    params.push(lineData)
  }
  for(let element of params){
    element.privatepropertys = JSON.stringify(element.privatepropertys)
    element.equipmentInfo = JSON.stringify(element.equipmentInfo)
    element.topologyrelations = JSON.stringify(element.topologyrelations)
  }
  console.log(params, "paramsparamsparamsparams");
  saveAllInfos(params).then((res) => {
    if (res.code === 200) {
      ElMessage.success("保存成功");
    } else {
      ElMessage.error(res.msg);
    }
  });
}

/** 绘制变压器结束
 * @param {*} res 
 * @param {*} listForm 
 * @param {*} drawInfo 
 */
export const drawEndZsbyq = (list, lineForm) => {
  const taskId = new URLSearchParams(new URL(window.location.href).search).get(
    "id"
  );
  const mapList=list?.data
  const poleInfo=list?.poleInfo
  // PWOPTransformerPSR 代表柱上变的类不会变的
  const mapBox = mapList.find(
    (element) => element.CLASS_NAME === "PWOPTransformerPSR"
  );
  const mapBoxParent = mapList.find(
    (element) => element.CLASS_NAME === "PWPolePSR"
  );
  let pointXY={
                X:mapBox.SHAPE.split(' ')[1].split('(').join(''),
                Y:mapBox.SHAPE.split(' ')[2].split(')').join('')
              }
  // UserNumber: 编号 TQMC：台区
  const params = {
    moduleId: lineForm.column, // 顶层id
    legendTypeKey: "TY_ZSBYQ", // 图元类型
    legendState: lineForm.status, // 状态
    legendGuidKey: mapBox.DEV_ID,
    engineeringId: taskId,
    privatepropertys: JSON.stringify({
      UserNumber: lineForm.towerCode,
      TQMC: lineForm.tower,
    }),
    topologyrelations: JSON.stringify({
      AssociatedIn: [], // 变压器不涉及
      AssociatedOut: [], // 变压器不涉及
      AssociatedParent: [poleInfo?.DEV_ID], // 绘制父设备的DEV_ID
      AssociatedChild: [], // 后端处理
      AssociatedLabel: [], // 目前不涉及
      AssociatedLabelOwner: [], // 目前不涉及
      AssociatedFile: [], // 目前不涉及
    }),
   
    equipmentInfo: JSON.stringify({
      positionInfo: {
        pointType: "Point",
        points: [
          {
            XYZ: [
              {
                coordinate: `${pointXY.X},${pointXY.Y},0`,
              },
            ],
          },
        ],
        angle: 0,
      },
    }),
  };
  console.log(params, "paramsparamsparamsparams");
  saveAllInfo(params).then((res) => {
    if (res.code === 200) {
      ElMessage.success("保存成功");
    } else {
      ElMessage.error(res.msg);
    }
  });
};