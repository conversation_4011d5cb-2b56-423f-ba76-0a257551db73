<template>
    <data-dialog @close="closeDialog" dataWidth="850" v-if="appStore.mapIndex == '三率自查'">
        <template #header>
            <h4 style="margin-left: 10px; margin-top: 0px; line-height: 39px">
                三率自查
            </h4>
        </template>
        <template #body>
            <div class="small-material" style="background: #e4f2f2">
                <div style="padding: 10px">
                    <div class="basicinfo-table">
                        <div class="basicinfo-tableItems">
                            <el-row>
                                <el-col :span="5">图元总数</el-col>
                                <el-col :span="3">{{threeSelfinspection.elementTotal}}</el-col>
                                <el-col :span="5" class="table-title">标准图元数量</el-col>
                                <el-col :span="3">{{threeSelfinspection.standardElementNum}}</el-col>
                                <el-col :span="5" class="table-title">典设方案应用率</el-col>
                                <el-col :span="3" style="text-align: center;">{{threeSelfinspection.schemeApplicationRate}}</el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="5" class="table-title">物料总数</el-col>
                                <el-col :span="3">{{threeSelfinspection.materialTotal}}</el-col>
                                <el-col :span="5" class="table-title">标准物料数量</el-col>
                                <el-col :span="3">{{threeSelfinspection.standardmaterialNum}}</el-col>
                                <el-col :span="5" class="table-title">标准物料应用率</el-col>
                                <el-col :span="3" style="text-align: center;">{{threeSelfinspection.standardmaterialApplicationRate}}</el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="5" class="table-title">设备总数</el-col>
                                <el-col :span="3">{{threeSelfinspection.deviceTotal}}</el-col>
                                <el-col :span="5" class="table-title">标准化定制设备数量</el-col>
                                <el-col :span="3">{{threeSelfinspection.standardDeviceNum}}</el-col>
                                <el-col :span="5" class="table-title">标准化定制设备应用率</el-col>
                                <el-col :span="3" style="text-align: center;">{{threeSelfinspection.deviceApplicationRate}}</el-col>
                            </el-row>
                        </div>
                    </div>
                </div>
                <div style="padding: 10px">
                    <el-tabs type="border-card" class="threePane-tabs">
                        <el-tab-pane>
                            <template #label>
                                <span class="custom-tabs-label">
                                    <el-icon>
                                        <calendar />
                                    </el-icon>
                                    <span>典设方案应用情况</span>
                                </span>
                            </template>
                            <el-table class="small-size" border style="margin-top: -2px; height: 400px; width:800px"
                                size="small" :data="threeSelfinspection.schemeApplicationList">
                                <el-table-column align="center" prop="legendType" label="图元名称">
                                </el-table-column>
                                <el-table-column align="center" prop="moduleCode" label="模块编号">
                                </el-table-column>
                                <el-table-column align="center" prop="legendNum" label="图元数量">
                                </el-table-column>
                                <el-table-column align="center" prop="isStandardDrawing" label="方案图纸是否满足">
                                    <template #default="scope">
                                        <span v-if="scope.row.isStandardDrawing!='满足'" style="color:red;">{{ scope.row.isStandardDrawing }}</span>
                                        <span v-if="scope.row.isStandardDrawing=='满足'" style="color:darkturquoise;">{{ scope.row.isStandardDrawing }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column align="center" prop="isStandardLegend" label="是否标准图元">
                                    <template #default="scope">
                                        <span v-if="scope.row.isStandardLegend!='是'" style="color:red;">{{ scope.row.isStandardLegend }}</span>
                                        <span v-if="scope.row.isStandardLegend=='是'" style="color:darkturquoise;">{{ scope.row.isStandardLegend }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column align="center" prop="voltage" label="电压等级">
                                </el-table-column>
                                <el-table-column align="center" prop="equipmentNo" label="设备编号">
                                </el-table-column>
                            </el-table>
                        </el-tab-pane>
                        <el-tab-pane>
                            <template #label>
                                <span class="custom-tabs-label">
                                    <el-icon>
                                        <calendar />
                                    </el-icon>
                                    <span>标准物料应用情况</span>
                                </span>
                            </template>
                            <el-table class="small-size" border style="margin-top: -2px; height: 400px; width: 800px"
                                size="small" :data="threeSelfinspection.standardmaterialApplicationList">
                                <el-table-column align="center" prop="materialType" label="物料类别">
                                </el-table-column>
                                <el-table-column align="center" prop="materialName" label="设备名称">
                                </el-table-column>
                                <el-table-column align="center" prop="spec" label="设备描述">
                                </el-table-column>
                                <el-table-column align="center" prop="materialERPCode" label="物料编码">
                                </el-table-column>
                                <el-table-column align="center" prop="technicalProtocol" label="固化id">
                                </el-table-column>
                                <el-table-column align="center" prop="num" label="应用数量">
                                </el-table-column>
                                <el-table-column align="center" prop="unit" label="单位">
                                </el-table-column>
                                <el-table-column align="center" prop="isDonnor" label="供应性质">
                                    <template #default="scope">
                                        <span v-if="scope.row.isDonnor!='甲供'" style="color:red;">{{ scope.row.isDonnor }}</span>
                                        <span v-if="scope.row.isDonnor=='甲供'" style="color:darkturquoise;">{{ scope.row.isDonnor }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column align="center" prop="isStandard" label="标准物料">
                                    <template #default="scope">
                                        <span v-if="scope.row.isStandard!='是'" style="color:red;">{{ scope.row.isStandard }}</span>
                                        <span v-if="scope.row.isStandard=='是'" style="color:darkturquoise;">{{ scope.row.isStandard }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column align="center" prop="voltage" label="电压等级">
                                </el-table-column>
                            </el-table>
                        </el-tab-pane>
                        <el-tab-pane>
                            <template #label>
                                <span class="custom-tabs-label">
                                    <el-icon>
                                        <calendar />
                                    </el-icon>
                                    <span>标准化定制设备应用情况</span>
                                </span>
                            </template>
                            <el-table class="small-size" border style="margin-top: -2px; height: 400px; width: 800px"
                                size="small" :data="threeSelfinspection.deviceApplicationList">
                                <el-table-column align="center" prop="materialType" label="物料类别">
                                </el-table-column>
                                <el-table-column align="center" prop="materialName" label="设备名称">
                                </el-table-column>
                                <el-table-column align="center" prop="spec" label="设备描述">
                                </el-table-column>
                                <el-table-column align="center" prop="materialERPCode" label="物料编码">
                                </el-table-column>
                                <el-table-column align="center" prop="technicalProtocol" label="固化id">
                                </el-table-column>
                                <el-table-column align="center" prop="num" label="应用数量">
                                </el-table-column>
                                <el-table-column align="center" prop="unit" label="单位">
                                </el-table-column>
                                <el-table-column align="center" prop="isDonnor" label="供应性质">
                                    <template #default="scope">
                                        <span v-if="scope.row.isDonnor!='甲供'" style="color:red;">{{ scope.row.isDonnor }}</span>
                                        <span v-if="scope.row.isDonnor=='甲供'" style="color:darkturquoise;">{{ scope.row.isDonnor }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column align="center" prop="isStandard" label="标准物料">
                                    <template #default="scope">
                                        <span v-if="scope.row.isStandard!='是'" style="color:red;">{{ scope.row.isStandard }}</span>
                                        <span v-if="scope.row.isStandard=='是'" style="color:darkturquoise;">{{ scope.row.isStandard }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column align="center" prop="voltage" label="电压等级">
                                </el-table-column>
                            </el-table>
                        </el-tab-pane>
                    </el-tabs>
                </div>
            </div>
        </template>
    </data-dialog></template>
<script setup>
import DataDialog from "@/components/DataDialog/index.vue";
import useAppStore from "@/store/modules/app.js";
import { Calendar } from '@element-plus/icons-vue'
import {
    selectThreeSelfinspection
} from "@/api/desginManage/ToolManagement.js";
const route = useRoute();

const appStore = useAppStore();
const closeDialog = () => {
    appStore.mapIndex = "";
};
const activeName = ref('first')
const handleClick = () => {

}
const threeSelfinspection = ref({
    elementTotal:"",
    standardElementNum:"",
    schemeApplicationRate:"",
    materialTotal:"",
    standardmaterialNum:"",
    standardmaterialApplicationRate:"",
    deviceTotal:"",
    standardDeviceNum:"",
    deviceApplicationRate:"",
    schemeApplicationList: [],
    standardmaterialApplicationList: [],
    deviceApplicationList: [],
})
const checkedNz = ref(true)
const dataList = () => {
    selectThreeSelfinspection({taskid:route.query.id}).then(res => {
    // treeData.value = convertcurrent(res.data)
    console.log(res.data.data)
    threeSelfinspection.value=res.data.data
  })
}
watch(
    () => appStore.mapIndex,
    (newInfo, oldInfo) => {
        console.log(newInfo, oldInfo);
        if (newInfo == "三率自查") {
            dataList()
      }
    }
);
</script>
<style scoped lang="scss">
@use '../../index' as *;

::v-deep .el-tabs__header {
    margin: 0;
}

.threePane-tabs>.el-tabs__content {
    padding: 32px;
    color: #6b778c;
    font-size: 32px;
    font-weight: 600;
}

.threePane-tabs .custom-tabs-label .el-icon {
    vertical-align: middle;
}

.threePane-tabs .custom-tabs-label span {
    vertical-align: middle;
    margin-left: 4px;
}
</style>