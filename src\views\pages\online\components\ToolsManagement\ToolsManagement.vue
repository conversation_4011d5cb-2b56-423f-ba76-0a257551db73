<template>
  <!-- 五.工具管理 -->
  <!-- 1.绘制其他图元 -->
  <OtherGraphic></OtherGraphic>
  <!-- 2.获取现状数据 -->
  <!-- <data-dialog v-if="appStore.mapIndex == '获取现状数据'">
    <template #header>
      <h4 style="margin-left: 10px; margin-top: 0px; line-height: 39px">
        获取现状数据
      </h4>
    </template>
    <template #body>
      <div class="line">
        <div class="line-item">
          <span> 下载地图类型 </span>
        </div>
        <div class="line-input">
          <el-input
            class="in-item"
            v-model="storeForm.materialInfo"
            placeholder=""
          ></el-input>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 变电站名称 </span>
        </div>
        <div class="line-input">
          <el-input
            class="in-item"
            v-model="storeForm.materialInfo"
            placeholder=""
          ></el-input>
        </div>
      </div>
      <div class="material" style="background: #e4f2f2; padding-left: 10px">
        <div style="display: flex; justify-content: flex-start">
          <div
            @click="obtainFlag = 1"
            :class="obtainFlag == 1 ? 'active' : ''"
            style="
              width: 100px;
              height: 30px;
              background: transparent;
              color: black;
              text-align: center;
              line-height: 30px;
              cursor: pointer;
              border-radius: 5px 5px 0 0;
              border: 1px solid #199092;
              font-size: 13px;
            "
          >
            变电站数据
          </div>
          <div
            @click="obtainFlag = 2"
            :class="obtainFlag == 2 ? 'active' : ''"
            style="
              width: 100px;
              height: 30px;
              background: transparent;
              color: black;
              text-align: center;
              line-height: 30px;
              cursor: pointer;
              border-radius: 5px 5px 0 0;
              border: 1px solid #199092;
              font-size: 13px;
            "
          >
            线路数据
          </div>
        </div>
        <el-table
          border
          style="margin-top: -2px; margin-left: -2px"
          size="small"
          :data="data"
        >
          <el-table-column align="center" type="selection" width="50" />
          <el-table-column
            align="center"
            prop="sk"
            label="工程名称"
            width="318"
          >
          </el-table-column>
        </el-table>
      </div>
      <div style="background: #e4f2f2; height: 50px" class="line">
        <div class="line-bnt">下载</div>
      </div>
    </template>
  </data-dialog> -->
  <!-- 3.竣工数字化移交 -->
  <data-dialog
      v-if="appStore.mapIndex == '竣工数字化移交'"
      @close="closeDialog"
  >
    <template #header>
      <h4 style="margin-left: 10px; margin-top: 0px; line-height: 39px">
        现状数据投产
      </h4>
    </template>
    <template #body>
      <div class="line">
        <div class="line-item">
          <span> 推荐项目编码 </span>
        </div>
        <div class="line-input">
          <el-input
              v-model="storeForm.materialInfo"
              class="in-item"
              placeholder=""
          ></el-input>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 计划开工时间 </span>
        </div>
        <div class="line-input">
          <el-input
              v-model="storeForm.materialInfo"
              class="in-item"
              placeholder=""
          ></el-input>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 计划完工时间 </span>
        </div>
        <div class="line-input">
          <el-input
              v-model="storeForm.materialInfo"
              class="in-item"
              placeholder=""
          ></el-input>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 当前计划编号 </span>
        </div>
        <div class="line-input">
          <el-input
              v-model="storeForm.materialInfo"
              class="in-item"
              placeholder=""
          ></el-input>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 当前工作内容 </span>
        </div>
        <div class="line-input">
          <el-input
              v-model="storeForm.materialInfo"
              class="in-item"
              placeholder=""
          ></el-input>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 当前工作负责人 </span>
        </div>
        <div class="line-input">
          <el-input
              v-model="storeForm.materialInfo"
              class="in-item"
              placeholder=""
          ></el-input>
        </div>
      </div>
      <div class="line" style="background: #e4f2f2; height: 50px">
        <div class="line-bnt">获取计划</div>
        <div
            style="
              margin-left: 30px;
              background: #e4f2f2;
              border: 2px solid #199092;
              color: #199092;
              width: 84px;
              height: 30px;
              line-height: 27px;
              cursor: pointer;
              border-radius: 5px;
              font-size: 12px;
            "
        >
          异动单创建
        </div>
      </div>
    </template>
  </data-dialog>

  <!-- 设计合理性分析 -->
  <Reasonable></Reasonable>

  <!-- 设计完整性分析 -->
  <Integrity></Integrity>
  <!-- 三率自查 -->
  <ThreeSelfInspection></ThreeSelfInspection>


  <!-- 导入勘绘数据 -->
  <data-dialog
      v-if="appStore.mapIndex == '导入勘绘数据'"
      dataWidth="700px"
      @close="closeDialog"
  >
    <template #header>
      <h4 style="margin-left: 10px; margin-top: 0px; line-height: 39px">
        导入测绘数据
      </h4>
    </template>
    <template #body>
      <div
          class="meter-draw"
          style="margin: 10px 10px 0 10px; border: 1px solid #badddd"
      >
        <div
            class="draw"
            style="
              height: 50px;
              display: flex;
              justify-content: flex-start;
              align-items: center;
            "
        >
          <span>路段</span>
          <span>坐标系</span>
          <el-input
              v-model="coordinateSystem"
              placeholder="请输入坐标系"
              style="width: 140px; height: 25px; margin-left: 10px"
          ></el-input>
          <span>测绘文件类型</span>
          <el-input
              v-model="coordinateSystem"
              placeholder="请输入坐标系"
              style="width: 140px; height: 25px; margin-left: 10px"
          ></el-input>
          <div
              style="
                background: #0e8b8d;
                color: white;
                padding: 5px 10px;
                border-radius: 5px;
                cursor: pointer;
                font-size: 10px;
                font-weight: bold;
                margin-left: 10px;
              "
          >
            选择文件
          </div>
        </div>
        <el-table
            :data="data"
            border
            style="margin-top: -2px; height: 300px; width: 700px"
        >
          <el-table-column
              align="center"
              label="序号"
              width="55"
          ></el-table-column>
          <el-table-column align="center" label="点类型">
            <template #default="scope"></template>
          </el-table-column>
          <el-table-column align="center" label="顺序调整">
            <template #default="scope"></template>
          </el-table-column>
        </el-table>
      </div>
      <div class="draw-footer">
        <el-checkbox
            :text-color="red"
            label="自动绘制路径图"
            style="margin-left: 10px"
        ></el-checkbox>
        <div
            style="
              background: #0e8b8d;
              color: white;
              width: 60px;
              height: 20px;
              border-radius: 5px;
              cursor: pointer;
              font-size: 10px;
              font-weight: bold;
              margin-left: 10px;
              line-height: 20px;
              text-align: center;
            "
        >
          选择文件
        </div>
        <div
            style="
              background: transparent;
              color: #249496;
              width: 60px;
              height: 20px;
              border-radius: 5px;
              cursor: pointer;
              font-size: 10px;
              font-weight: bold;
              margin-left: 10px;
              border: 1px solid #249496;
              line-height: 20px;
              text-align: center;
            "
        >
          下一步
        </div>
      </div>
    </template>
  </data-dialog>

  <!-- 插入图纸 -->
  <InsertDrawing></InsertDrawing>
  <!-- 线夹匹配 -->
  <WireClipMatching></WireClipMatching>
  <!-- 插入图框-->
  <InsertFrame></InsertFrame>
  <!--自动匹配图纸-->
  <AutomaticallyMatchDrawings></AutomaticallyMatchDrawings>
  <!--图纸拆分-->
<!--  <DrawingSplitting></DrawingSplitting>-->
  <!--图纸目录-->
  <drawingList></drawingList>
  <!--批量打印-->
  <BatchPrint></BatchPrint>
</template>
<script setup>
import DataDialog from "@/components/DataDialog/index.vue";
import OtherGraphic from "./components/OtherGraphic.vue"
import InsertDrawing from "./components/InsertDrawing.vue"
import WireClipMatching from "./components/WireClipMatching.vue"
import InsertFrame from "./components/InsertFrame.vue"
import ThreeSelfInspection from "./components/ThreeSelfInspection.vue"
import Reasonable from "./components/Reasonable.vue"
import Integrity from "./components/Integrity.vue"
import AutomaticallyMatchDrawings from "./components/AutomaticallyMatchDrawings.vue"
import DrawingSplitting from "./components/DrawingSplitting.vue"
import drawingList from "./components/drawingList.vue"
import BatchPrint from "./components/BatchPrint.vue"
import useAppStore from "@/store/modules/app.js";
import {
  gssRefreshFormatPainter,
  ifUseFormatPainter,
  refreshLegendTypeKeyFormatPainter
} from "@/api/desginManage/ToolManagement.js";
let { proxy } = getCurrentInstance();
const appStore = useAppStore();
const taskId = new URLSearchParams(new URL(window.location.href).search).get(
    "id"
  );
const storeForm = ref({
  materialInfo: ''
})
const data = ref([
  {
    name: "线路1",
    key: "New",
    sk: " JG/TH",
    ksd: "删除",
  },
])
const obtainFlag = ref(1);
const closeDialog = () => {
  appStore.mapIndex = "";
};
const gssFun = () => {
  oniframeMessage({
    type: "onlycad",
    content: "Mx_FormatPainterOne",
  })
  appStore.mapIndex = "";
}
// 处理点击事件
const oniframeMessage = (param) => {
  const myiframe = document.querySelector("#myiframe").querySelector("iframe");
  if (myiframe && myiframe.contentWindow) {
    myiframe.contentWindow.postMessage(param, "*");
    // 先移除之前的事件监听器，防止重复绑定
    window.removeEventListener('message', handleMessage);
    // 添加新的事件监听器
    window.addEventListener('message', handleMessage);
  }
}
const handleMessage = (event) => {
  if (event.data.type === 'parentCad') {
    if (event.data.params.content === '格式刷图元模板校验') {
      const id = event.data.params.formData.id
      const ent = event.data.params.formData.ent
      console.log(id, 'ididid')
      ifUseFormatPainter({id:id.id,taskId:taskId}).then(res => {
        if (res.code === 200) {
          oniframeMessage({
            type: "MsgCad",
            content: "Mx_FormatPainterGss",
            formData: {
              id: id,
              ent: ent
            }
          })
        } else {
          proxy.$message.error(res.msg);
        }
      })
    } else if (event.data.params.content === '格式刷图元校验哪些可以刷') {
      const arr = event.data.params.formData.arr.map(item => item.id)
      const MbId = event.data.params.formData.MbId
      refreshLegendTypeKeyFormatPainter({ templateId: MbId.id, equipmentIds: arr,taskId:taskId }).then(res => {
        if (res.code === 200) {
          const code = JSON.parse(res.data.template.privatepropertys).UserNumber
          if (res.data.refreshIds.length > 0) {
            oniframeMessage({
              type: "MsgCad",
              content: "Mx_FormatPainterBoss",
              formData: {
                arr: res.data.refreshIds,
                code: code
              }
            })
          } else {
            proxy.$message.error('框选得设备无法使用格式刷');
          }
        }
      })


    } else if (event.data.params.content === '格式刷图元最终数据') {
      const arr = event.data.params.formData.arr
      console.log(arr, '格式刷图元最终数据')
      gssRefreshFormatPainter({ equipment: arr,taskId:taskId }).then(res => {
        if (res.code === 200) {
          proxy.$message.success("操作成功");
        } else {
          proxy.$message.error(res.msg);
        }
      })
    }
  }
}
watch(
    () => appStore.mapIndex,
    (newInfo, oldInfo) => {
      console.log(newInfo, oldInfo, '111');
      if (newInfo == "格式刷") {
        console.log('格式刷')
        gssFun()
      }
    }
);
</script>

<style lang="scss" scoped>
@use '../index' as *;
</style>
