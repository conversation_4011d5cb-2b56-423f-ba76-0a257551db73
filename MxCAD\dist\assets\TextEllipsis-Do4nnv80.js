import{h as _,c as r,a0 as s,a3 as n,a1 as m,V as o,a2 as u,F as v,Q as g}from"./vue-DfH9C9Rx.js";import{_ as w}from"./index-D95UjFey.js";const B={key:0,className:"mx_text_parent_main"},N={class:"mx_text_next_span"},S=_({__name:"TextEllipsis",props:{text:{},width:{default:100},size:{},family:{},sliceNum:{default:6}},setup(e){const i=document.body.currentStyle||document.defaultView.getComputedStyle(document.body,""),d=parseInt(i.fontSize),x=parseInt(i.fontFamily);function f(t){const l=document.createElement("canvas").getContext("2d");l.font=`${e.size||d}px ${e.family||x}`;const a=l.measureText(t),y=Math.abs(a.actualBoundingBoxLeft)+Math.abs(a.actualBoundingBoxRight);return Math.max(a.width,y)}const c=r(()=>f(e.text)),p=r(()=>c.value>e.width);return(t,h)=>(s(),n("div",{class:"mx_text_parent",style:u({width:t.width+"px"})},[p.value?(s(),n("div",B,[m("span",{class:"mx_text_prev_span",style:u({width:c.value})},o(t.text.slice(0,-t.sliceNum)),5),m("span",N,o(t.text.slice(-t.sliceNum)),1)])):(s(),n(v,{key:1},[g(o(t.text),1)],64))],4))}}),T=w(S,[["__scopeId","data-v-66f61507"]]);export{T};
