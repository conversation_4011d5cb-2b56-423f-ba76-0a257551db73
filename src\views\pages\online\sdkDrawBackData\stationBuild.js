import { ElMessage } from "element-plus";
import { getEquipmentModel } from "@/views/pages/online/saveModelInfo.js";
import {
  intervalAdd
} from "@/api/insertSag/index.js";
// 站房路径图元绘制结束
export const stationBuild = (hightList, lowList,byqList, dawInfo,ids,name) => {
  console.log("🚀 ~ stationBuild ~ hightList:", hightList,lowList,byqList,dawInfo,ids)
  const taskId = new URLSearchParams(new URL(window.location.href).search).get(
    "id"
  );
  // dawInfo.blockId
  const paramsData={
    moduleid:  '', // 顶层id
    legendtypekey: 'Frame', // 图元类型
    equipmentId: dawInfo , // 设备id
    taskid: taskId,
    equipmentInfo:JSON.stringify({}),
    privatepropertys:  JSON.stringify({
      HighVoltage: hightList,
      LowVoltage: lowList,
      BYQ: byqList,
      ids:ids,
      name:name
    }),
    topologyrelations:JSON.stringify({})
  }
  console.log("处理完后的数据", paramsData);
  intervalAdd(paramsData).then((res) => {
    if (res.code === 200) {
      ElMessage.success("保存成功");
    } else {
      ElMessage.error(res.msg);
    }
  });
}
