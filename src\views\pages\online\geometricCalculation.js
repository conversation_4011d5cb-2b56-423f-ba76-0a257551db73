/**
 * 计算两个向量之间的夹角 弧度
 * @param {Array} p1 - 第一个点的坐标 [x1, y1]
 * @param {Array} p2 - 第二个点的坐标 [x2, y2]
 * @param {Array} p3 - 第三个点的坐标 [x3, y3]
 * @returns {Number} 夹角的弧度值
 */
export const calculateAngle = (p1, p2, p3) =>{
    // 向量 p1p2
    const vectorA = [p2[0] - p1[0], p2[1] - p1[1]];
    // 向量 p2p3
    const vectorB = [p3[0] - p2[0], p3[1] - p2[1]];
  
    // 向量的点积
    const dotProduct = vectorA[0] * vectorB[0] + vectorA[1] * vectorB[1];
    // 向量的模
    const magnitudeA = Math.sqrt(vectorA[0] ** 2 + vectorA[1] ** 2);
    const magnitudeB = Math.sqrt(vectorB[0] ** 2 + vectorB[1] ** 2);
  
    // 计算夹角的弧度值
    const angle = Math.acos(dotProduct / (magnitudeA * magnitudeB));
  
    return angle;
  }

  //比较两点坐标是否相同
  export const comparePoints=(point1,point2)=>{
  if(point1[0]===point2[0]&&point1[1]===point2[1]&&point1[2]===point2[2]){
    return true
  }else{
    return false
  }
  }