import path from 'path'
import { defineConfig } from 'vite'
import jsx from "@vitejs/plugin-vue-jsx"
import vue from "@vitejs/plugin-vue"
export default ({ mode }) => {
  return defineConfig({
    optimizeDeps: {
      exclude: ['vue', 'mxcad', 'mxdraw']
    },
    build: {
      sourcemap: false,
      target: "es2015",
      cssCodeSplit: true,
      rollupOptions: {
        input: path.resolve(__dirname, 'src/index.ts'),
        external: ["vue", "mxcad", "mxdraw", "vue-i18n", 'pinia', 'vuetify', 'axios'],
        output: {
          format: "iife",
          globals: {
            vue: 'Vue',
            mxcad: "MxCAD",
            mxdraw: "Mx",
            "vue-i18n": "VueI18n",
            "pinia": "pinia",
            "vuetify": "vuetify",
            "axios": "axios"
          },
          entryFileNames: 'test.js',
          chunkFileNames: 'test.js',
          assetFileNames: 'test.[ext]',
        },
      },

      outDir: "../dist/plugins",
      minify: mode !== "debug",
    },

    server: {
      port: 3366,
      fs: {
        strict: false // 允许访问项目根目录之外的文件
      },
      headers: {
        // 'Cross-Origin-Opener-Policy': 'same-origin',
        // 'Cross-Origin-Embedder-Policy': 'require-corp'
        'Cross-Origin-Embedder-Policy': 'unsafe-none',
        'Cross-Origin-Opener-Policy': 'unsafe-none',
      },
    },
    define: {
      __VUE_PROD_DEVTOOLS__: mode !== "debug",
    },
    plugins: [vue(), jsx()],
  })
}
