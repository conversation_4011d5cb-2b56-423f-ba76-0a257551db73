<script setup>
import {useIframeCommunication} from "@/hooks/useIframeCommunication.js";
import {getEquipmentInfoString, getEquipmentNote, getExplanatoryDrawings} from "@/api/annotation/index.js";
import {useRoute} from "vue-router";
import {ElMessage} from "element-plus";

console.log('标注设备')
const route = useRoute()

const taskId = route.query.id
const { sendMessage, cleanup } = useIframeCommunication();

const cadAppRef = inject("cadAppRef");

const init = () => {
  const msg = {
    content: "Else_Annotation",
    type: "onlycad",
    options:{name:'JGFZH_Draw'}
  }
  sendMessage(cadAppRef.value, msg, (res => {
    if(res.keyVal) {
      switch (res.keyVal) {
        case '2':
          drawLengthAnnotationHandle(res.idList)
          break
        case '3':
          drawingExplainHandle()
          break
        case '4':
          drawCablePassageLabelHandle(res.idList)
          break
      }
    }
  }))
}

const drawCablePassageLabelHandle = (idList) => {
  console.log('idList', idList)
  const newList=[...new Set(idList)]
  const data = {
    taskId,
    equipmentIds: newList,
    type: 'TY_FSFS'
  }
  getEquipmentInfoString(data).then(res => {
    if(res.code === 200 && res.data.length) {
      const msg = {
        cmd: "Draw_Cable_Passage_Label",
        type: "sendStringToExecute",
        param: {
          labelList: res.data
        }
      }
      sendMessage(cadAppRef.value, msg, (res => {
        console.log('Draw_Cable_Passage_Label',res)
      }))
    }
  })
}

const drawLengthAnnotationHandle = (idList) => {
  const [startEquipmentId, endEquipmentId] = idList
  const data = {
    taskId,
    startEquipmentId,
    endEquipmentId
  }
  getEquipmentNote(data).then(res => {
    if(res.code === 200 && res.data) {
      const msg = {
        cmd: "Draw_Length_Annotation",
        type: "sendStringToExecute",
        param: {
          text: res.data
        }
      }
      sendMessage(cadAppRef.value, msg, (res => {
        console.log('Draw_Cable_Passage_Label',res)
      }))
    }
  })
}

const drawingExplainHandle = () => {
  const data = {
    projectId: taskId
  }
  getExplanatoryDrawings(data).then(res => {
    if(res.code === 200 && res.msg) {
      const msg = {
        cmd: "Draw_Drawing_Explain",
        type: "sendStringToExecute",
        param: {
          text: res.msg
        }
      }
      sendMessage(cadAppRef.value, msg, (res => {
      }))
    }
})
}

init()

</script>

<template>

</template>

<style lang="scss" scoped>

</style>