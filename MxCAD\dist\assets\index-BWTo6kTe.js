import{s as n,X as O}from"./index-CzBriCFR.js";import{a4 as D,e as M,y as d,B as u,x as c,M as U}from"./mxcad-DrgW2waE.js";import{U as A,M as k}from"./mxdraw-BQ5HhRCY.js";async function b(){let o=new D;o.setMessage(n("336")+n("191")+":"),o.setUserInputControls(A.kNoZeroResponseAccepted|A.kNoNegativeResponseAccepted);let l=await o.go();if(l==null)return;o.setMessage(n("336")+n("192")+":");let f=await o.go();if(f==null)return;let s=await M.getCorner(n("336")+n("388"));if(!s)return;let i=s.pt2.x-s.pt1.x,a=s.pt2.y-s.pt1.y,x=await M.userSelect(n("238")+n("389")),e=M.getMcDbEntitysBoundingBox(x);if(!e)return;let t=new d;i>0?t.x=e.minPt.x:t.x=e.maxPt.x,a>0?t.y=e.minPt.y:t.y=e.maxPt.y,z({iColNum:f,iRowNum:l,dColOffset:i,dRowOffset:a,aryId:x,dAng:0})}const z=o=>{const{iRowNum:l=0,iColNum:f=0,dColOffset:s=0,dRowOffset:i=0,aryId:a=[],dAng:x=0}=o;let e=M.getMcDbEntitysBoundingBox(a);if(!e)return;let t=new d;s>0?t.x=e.minPt.x:t.x=e.maxPt.x,i>0?t.y=e.minPt.y:t.y=e.maxPt.y;let g=new u().setToRotation(x*Math.PI/180,c.kZAxis,t),P=5e4,C=0;for(let y=0;y<l;y++){let B=new c(0,i*y,0),N=new u().setToTranslation(B);for(let m=0;m<f;m++){if(y==0&&m==0)continue;let R=new c(s*m,0,0),T=new u().setToTranslation(R),r=new d(t.x,t.y,t.z);r.transformBy(N),r.transformBy(T),r.transformBy(g);let I=new u().setToTranslation(new c(r.x-t.x,r.y-t.y,r.z-t.z));for(let p=0;p<a.length;p++){let w=a[p].clone();if(w&&(w.transformBy(I),U.getCurrentMxCAD().drawEntity(w),C++,C>P)){k.acutPrintf(n("390")+P+n("391")+`
`);return}}O()}}};export{b as M,z as r};
