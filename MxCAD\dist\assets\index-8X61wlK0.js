import{h as B,a0 as l,_ as t,$ as s,m as o,aa as d,a1 as i,u as v,V as n,U as w,a5 as b,ab as r,a3 as f,a4 as T,n as D,Q as M,F as $}from"./vue-DfH9C9Rx.js";import{aA as A,aB as S,n as L,a as N,_ as z}from"./index-D95UjFey.js";import{a3 as E,c as I,b as P,a4 as j,a5 as F,f as K,a6 as Q}from"./vuetify-B_xYg4qv.js";const U={class:"d-flex justify-space-between align-center w-100 py-1"},X={class:"title_box"},Y=["src"],q={class:"ml-2"},G={key:1},H=B({__name:"index",props:{title:{default:"标题"},logo:{},cardClass:{},cardTextClass:{},cardActionClass:{},footerBtnList:{default:()=>[]},modelValue:{type:Boolean,default:!1},keys:{},isShowCloseBtn:{type:Boolean,default:!0}},emits:["update:modelValue","clickClose"],setup(c,{expose:p,emit:g}){const m=g,{dialog:C,dialogTitleEl:y,getMoveX:h,getMoveY:_,bindKeys:k}=A();k(c.keys,()=>c.modelValue);const V=e=>{m("update:modelValue",e)};return p({getMoveX:h,getMoveY:_}),(e,u)=>(l(),t(Q,{ref_key:"dialog",ref:C,"model-value":e.modelValue,contained:"",persistent:"","no-click-animation":"","retain-focus":""},{default:s(()=>[o(K,{class:r(["w-100 h-100 rounded-0",e.cardClass||"bg-dialog-card box-shadow"])},{default:s(()=>[o(E,{class:"pa-0",ref_key:"dialogTitleEl",ref:y},{default:s(()=>[d(e.$slots,"header",{},()=>[i("div",U,[i("div",X,[d(e.$slots,"header-icon",{},()=>[i("img",{class:"ml-1",alt:"logo",width:"24",height:"24",src:e.logo||v(S)()},null,8,Y)],!0),i("span",q,n(e.title),1)]),e.isShowCloseBtn?(l(),t(I,{key:0,size:"24px",class:"mr-2",variant:"plain",onClick:u[0]||(u[0]=w(a=>{V(!1),m("clickClose")},["stop"]))},{default:s(()=>[o(P,{size:"16px",icon:"cha"})]),_:1})):b("",!0)])],!0)]),_:3},512),o(j,{class:r([e.cardTextClass||"mx-1 px-2  bg-dialog-card-text rounded-t","py-0 mx-dialog-text"])},{default:s(()=>[d(e.$slots,"default",{},void 0,!0)]),_:3},8,["class"]),d(e.$slots,"actions",{},()=>[o(F,{class:r(["mx-1 mt-0 mb-1 py-0 px-2 d-flex justify-end",e.cardActionClass||"bg-dialog-card-text"])},{default:s(()=>[(l(!0),f($,null,T(e.footerBtnList,(a,x)=>(l(),t(L,{key:x+a.name,onClick:a.fun,isAction:"",primary:a.primary,disabled:a.disabled?a.disabled():!1,class:r(["ml-3 px-2",a.disabled&&a.disabled()?"disabled":""])},{default:s(()=>[a.labelProps?(l(),t(N,D({key:0,ref_for:!0},a.labelProps,{noTextCaption:""}),{default:s(()=>[M(n(e.t(a.name)),1)]),_:2},1040)):(l(),f("span",G,n(e.t(a.name)),1))]),_:2},1032,["onClick","primary","disabled","class"]))),128))]),_:1},8,["class"])],!0)]),_:3},8,["class"])]),_:3},8,["model-value"]))}}),W=z(H,[["__scopeId","data-v-c38cd96f"]]);export{W as M};
