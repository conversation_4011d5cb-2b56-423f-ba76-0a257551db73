<template>
  <data-dialog dataWidth="1200" @close="closeDialog" v-if="appStore.mapIndex == '利旧物资表'">
    <template #header>
      <h4 style="margin-left: 10px; margin-top: 0px; line-height: 39px">
        利旧物资表
      </h4>
    </template>
    <template #body>
      <div class="material">
        <div style="margin-left: 10px">
          <el-button icon="Paperclip" type="text" @click="exportExcel">生成报表</el-button>
        </div>
        <el-table :data="tableData" border class="input-table" height="70vh" size="small" style="width: 100%">
          <el-table-column width="50" align="center" label="序号" prop="number"></el-table-column>
          <el-table-column align="center" label="物料类别" prop="materialCategory"></el-table-column>
          <el-table-column align="center" label="物料名称" prop="materialName"></el-table-column>
          <el-table-column align="center" label="物料规格" prop="materialSpecification"></el-table-column>
          <el-table-column width="100" align="center" label="利旧数量" prop="newReusedQuantity"></el-table-column>
          <el-table-column width="100" align="center" label="拆除利旧数量" prop="removedReusedQuantity"></el-table-column>
          <el-table-column width="50" align="center" label="单位" prop="unit"></el-table-column>
          <el-table-column width="50" align="center" label="年限"></el-table-column>
          <el-table-column width="100" align="center" label="备注" prop="remarks"></el-table-column>
        </el-table>
      </div>
    </template>
  </data-dialog>
</template>
<script setup>
import DataDialog from "@/components/DataDialog/index.vue";
import useAppStore from "@/store/modules/app.js";
import { useIframeCommunication } from "@/hooks/useIframeCommunication";
import { useRoute } from "vue-router";
import {
  generatorLjwzmxReport, inventoryMaterials
} from "@/api/insertSag/index.js";
import { inject } from 'vue';
const { proxy } = getCurrentInstance();
const route = useRoute();
const appStore = useAppStore();
const { sendMessage, cleanup } = useIframeCommunication();
const taskId = route.query.id;
const tableData = ref([])
const cadIframeRef = ref(null)
const callChildC = inject('callChildC');

const closeDialog = () => {
  appStore.mapIndex = "";
};
const fileToBase64 = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file); // 将文件转换为 Base64
    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
  });
};
const exportExcel = async () => {
  try {
    // 异步获取数据
    //     const res = await autoMatchingPreview({ drawingId: 2714 });

    //      // 将文件转换为 Base64
    //      const base64Data = await fileToBase64(res);

    // // 将 Base64 数据赋值给 tableData.value 的每个元素的 file 属性
    // if (Array.isArray(tableData.value)) {
    //   tableData.value = tableData.value.map((element) => ({
    //     ...element,
    //     file: base64Data, // 使用 Base64 字符串
    //   }));
    // }

    //     console.log("🚀 ~ tableData.value:", tableData.value);

    //     // 检查 tableData.value 是否为空
    if (tableData.value.length === 0) {
      proxy.$message.warning("目前暂无数据可生成");
      return; // 如果为空，直接返回
    }
    // 发送消息
    oniframeMessage({
      type: "cadPreview",
      content: "Mx_ljqc",
      formData: {
        tableData: JSON.stringify(tableData.value),
        countyOrganisationName: route.query.countyOrganisationName,
        projectName: route.query.projectName,
        stage: route.query.stage,
        proCode: route.query.proCode,
      },
    });
  } catch (error) {
    console.error("🚀 ~ exportExcel ~ error:", error);
    proxy.$message.error("数据生成失败，请稍后重试");
  }
};
// 处理点击事件
const oniframeMessage = (param) => {
  const myiframe = appStore.iframeHide
  console.log("🚀 ~ oniframeMessage ~ appStore.iframe:", appStore.iframe)
  if (myiframe && myiframe.contentWindow) {
    myiframe.contentWindow.postMessage(param, "*");
    // 先移除之前的事件监听器，防止重复绑定
    window.removeEventListener('message', handleMessage);
    // 添加新的事件监听器
    window.addEventListener('message', handleMessage);
  }
}
const queryList = () => {
  inventoryMaterials({ projectId: taskId, unitType: "ERP", materialStates: ["RemoveUsing", "NewUsing"] }).then(res => {
    res.data.forEach((item, index) => {
      item.number = index + 1
    })
    tableData.value = res.data
  })
}
// 事件处理函数
const handleMessage = (event) => {
  console.log("🚀 ~ handleMessage ~ event:", event)
  if (event.data.type === 'parentCad' && event.data.params.content === '利旧物资表') {
    const files = event.data.params.formData.files
    console.log(files, 'filesfiles')
    const tableData = JSON.parse(event.data.params.formData.tableData)
    const tableDataNew = tableData.map((item, index) => {
      return {
        xh: index + 1,
        wllb: item.materialCategory,
        wlmc: item.materialName,
        wlgg: item.materialSpecification,
        ljsl: item.newReusedQuantity,
        ccljsl: item.removedReusedQuantity,
        dw: item.unit,
        nx: "",
        bz: item.remarks
      }
    })
    const fileFormData = new FormData()
    files.forEach(item => {
      fileFormData.append('multipartFile', item)
    })
    fileFormData.append("ljwzmxList", JSON.stringify(tableDataNew));
    fileFormData.append('prjTaskInfoId', route.query.id)
    fileFormData.append('stage', route.query.stage)
    fileFormData.append('equipmentInfos', JSON.stringify({ th: `${route.query.proCode}-1` }))
    generatorLjwzmxReport(fileFormData).then(res => {
      if (res.code === 200) {
        proxy.$message.success("保存成功");
      } else {
        proxy.$message.error(res.msg);
      }
      if (callChildC) {
        callChildC();
      }
    })
  }
}
watch(
  () => appStore.mapIndex,
  (newInfo, oldInfo) => {
    if (newInfo == "利旧物资表") {
      queryList();
    }
  }
);
</script>
<style lang="scss" scoped>
@use '../../index' as *;
</style>