<script setup>
import {getDLSGModuleByTowerZj} from "@/api/baseDate/onlineDesign";
import {ElMessage} from "element-plus";

const visible = defineModel('visible', {type: Boolean, default: false});

const props = defineProps({
  type: {
    type: String,
    default: 'first'
  }
})

const emit = defineEmits(['updateParams'])

const towerOption = ref([])
const getData = () => {
  const params = {}
  getDLSGModuleByTowerZj(params).then(res => {
    towerOption.value = res.data
  })
}

const formRef = ref()
const form = reactive({moduleid: ''})
const rules = reactive({
  moduleid: [
    {required: true, message: '请输入', trigger: 'change'},
  ]
})

const loading = ref(false)
const onSubmit = (formEl) => {
  if (!formEl) return
  formEl.validate((valid) => {
    if (!valid) return
      emit('updateParams', form)
      visible.value = false
  })
}

const resetForm = (formEl) => {
  if (!formEl) return
  formEl.resetFields()
}

const closeVisible = () => {
  resetForm(formRef.value)
}
</script>

<template>
  <el-dialog
      title="杆塔"
      v-model="visible"
      width="30%"
      append-to-body
      destroy-on-close
      @open="getData"
      @close="closeVisible"
  >
    <el-form ref="formRef" :model="form" :rules="rules">
      <el-form-item label="名称" prop="moduleid">
        <el-select v-model="form.moduleid" placeholder="请选择">
          <el-option v-for="item in towerOption" :label="item.modulename" :value="item.moduleid"/>
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" :loading="loading" @click="onSubmit(formRef)">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">

</style>
