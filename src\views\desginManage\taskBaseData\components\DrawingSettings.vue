<template>
  <div style="display: flex;">
    <div style="border: 0.5px solid #50adaa;margin-right: 10px">
      <div class="title">列表</div>
      <el-divider class="divider5"/>
      <el-tree
          style="width: 150px;height: 70vh;"
          ref="treeRef"
          node-key="LegendTypeKey"
          :data="data"
          :props="defaultProps"
          @node-click="handleNodeClick"
      >
        <template #default="{ node, data }">
        <span class="custom-tree-node">
          <span style="display: flex;align-items: center"><el-icon><Document/></el-icon>{{ node.label }}</span>
        </span>
        </template>
      </el-tree>
    </div>
    <div style="width: 100%;border: 0.5px solid #50adaa">
      <div class="title">设置</div>
      <el-divider class="divider5"/>
      <el-table :data="tableData" border style="width: 100%;height: 70vh">
        <el-table-column prop="LegendName" label="名称"/>
        <el-table-column prop="" label="颜色">
          <template #default="scope">
            <el-color-picker v-model="scope.row.Color"/>
          </template>
        </el-table-column>
        <el-table-column prop="" label="图元样式" align="center">
          <template #default="scope">
            <el-image
                style="width: 50px; height: 50px"
                :src="getImageUrl(scope.row.LegendName)"
                fit="contain"/>
          </template>
        </el-table-column>
        <el-table-column prop="" label="比例因子">
          <template #default="scope">
            <el-input type="number" v-model="scope.row.RcaleFactor"/>
          </template>
        </el-table-column>
        <el-table-column prop="" label="线宽">
          <template #default="scope">
            <el-input type="number" v-model="scope.row.LineWidth"/>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>
<script setup>

const treeRef = ref()

const defaultProps = ref({
  children: 'children',
  label: 'LegendTypeName',
})
// tree点击事件
const handleNodeClick = (val) => {
  console.log(val, 'tree点击事件')
}

const data = defineModel('baseData', {type: Array, required: true});

const tableData = computed(() => {
  return treeRef.value?.getCurrentNode()?.SettingList || []
})

const getImageUrl = (legendName) => {
  return new URL(`/src/assets/images/device/${legendName}.jpg`, import.meta.url).href;
};

</script>

<style scoped lang="scss">
.title {
  margin: 10px;
  color: #50adaa;
  font-weight: bold;
}
</style>
