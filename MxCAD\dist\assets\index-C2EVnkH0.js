import{N as se,I as oe,O as ne,C as ie,a as m,M as F,n as v,aR as ue,_ as de}from"./index-D95UjFey.js";import{M as R}from"./index-8X61wlK0.js";import{C as B,E as f,a as me,m as re,k as pe,V as N,j as ve,i as g}from"./vuetify-B_xYg4qv.js";import{h as fe,d as q,c,w as z,K as ye,a0 as k,a3 as E,m as l,$ as t,a1 as r,V as u,u as s,Q as d,H as Ve,_ as H,F as O,a4 as ge,ab as ce,B as _,A as ke}from"./vue-DfH9C9Rx.js";import"./mxcad-CfPpL1Bn.js";import"./mxdraw-C_n_7lEs.js";import"./mapbox-gl-PN3pDJHq.js";import"./xlsx-Ck5MUZvf.js";const we={class:"px-3 mt-2"},be={class:"h-100"},Ce={class:"h-100"},Se={class:"w-50"},xe={class:"w-50"},Fe={class:"mr-1"},Be={class:"ml-5"},Ne=fe({__name:"index",setup(_e){const{isShow:y,showDialog:P}=ue,U=se(),{list:D,current:V,index:n,fontNames:T,fontStyles:A,isUpdate:W,newStyleName:w}=oe(U),{setIndex:j,putCurrent:K,add:Q,remove:X,apply:L,init:Y}=U,G=[{name:"应用",fun:()=>{L()},disabled:()=>!W.value,primary:!0,labelProps:{"key-name":"A"}},{name:"关闭",fun:()=>P(!1)}],J={a:()=>{L()}},{isShow:b,showDialog:C}=ne(!1),{open:Z}=ie(),S=q("所有样式"),h=c(()=>S.value==="正在使用样式"),i=c(()=>h.value?[V.value]:D.value),ee=c(()=>i.value[n.value]===V.value),x=q(!1),le=()=>{x.value=!0,ke(()=>{x.value=!1})};z(h,()=>{n.value=i.value.indexOf(V.value)}),z(y,e=>{e&&Y()});const I=()=>{Q(),C(!1),le()},te={enter:I},p=c(()=>D.value[n.value].isBigFont),$=e=>((e.includes("\\")||e.includes("/"))&&(e=e.split(/[\\\/]/).pop()||e),p.value&&(e=e.toLocaleLowerCase(),e=e.endsWith(".shx")?e:e+".shx"),e);return(e,a)=>{const ae=ye("scroll-bottom");return k(),E(O,null,[l(R,{title:e.t("277"),"max-width":"600",modelValue:s(y),"onUpdate:modelValue":a[9]||(a[9]=o=>_(y)?y.value=o:null),footerBtnList:G,keys:J},{default:t(()=>[r("div",we,[r("p",null,u(e.t("658"))+": "+u(s(V).name),1),l(B,{"align-stretch":""},{default:t(()=>[l(f,{cols:"4",class:"h-100","align-self":"start"},{default:t(()=>[r("div",be,[l(m,{"key-name":"S",colon:""},{default:t(()=>[d(u(e.t("362")),1)]),_:1}),Ve((k(),H(pe,{density:"compact",class:"overflow-y py-0 list-border",height:"230"},{default:t(()=>[(k(!0),E(O,null,ge(i.value,(o,M)=>(k(),H(me,{key:o.id,onClick:Ue=>s(j)(M),value:o,class:ce(["pa-0 mb-0",s(n)===M?"bg-light-blue-darken-2":""]),"min-height":"18",height:"18"},{prepend:t(()=>a[14]||(a[14]=[r("div",{style:{width:"24px"}},null,-1)])),default:t(()=>[l(re,{textContent:u(o.name)},null,8,["textContent"])]),_:2},1032,["onClick","value","class"]))),128))]),_:1})),[[ae,x.value]]),l(N,{class:"mt-2",modelValue:S.value,"onUpdate:modelValue":a[0]||(a[0]=o=>S.value=o),items:["所有样式","正在使用样式"]},null,8,["modelValue"])])]),_:1}),l(f,{cols:"6",class:"h-100"},{default:t(()=>[r("div",Ce,[l(F,{title:e.t("593"),class:"mb-1"},{default:t(()=>[l(B,null,{default:t(()=>[r("div",Se,[l(m,{"key-name":p.value?"X":"B",class:"ml-1"},{default:t(()=>[d(u(p.value?e.t("659"):e.t("660")),1)]),_:1},8,["key-name"]),l(N,{class:"mx-1","menu-props":{maxHeight:"200px"},items:s(T),"model-value":$((i.value[s(n)].isBigFont?i.value[s(n)].fileName:i.value[s(n)].typeFace)||s(T)[0]),"onUpdate:modelValue":a[1]||(a[1]=o=>{i.value[s(n)].isBigFont?i.value[s(n)].fileName=o:i.value[s(n)].typeFace=o})},null,8,["items","model-value"])]),r("div",xe,[l(m,{"key-name":p.value?"B":"Y",class:"ml-1"},{default:t(()=>[d(u(p.value?e.t("661"):e.t("662")),1)]),_:1},8,["key-name"]),l(N,{class:"mx-1","menu-props":{maxHeight:"200px"},items:s(A),"model-value":$(i.value[s(n)].isBigFont?i.value[s(n)].bigFontFileName||s(A)[0]:"常规"),"onUpdate:modelValue":a[2]||(a[2]=o=>{i.value[s(n)].isBigFont&&(i.value[s(n)].bigFontFileName=o)})},null,8,["items","model-value"])])]),_:1}),l(ve,{class:"mt-2","model-value":p.value,"onUpdate:modelValue":a[3]||(a[3]=o=>{o!==null&&(i.value[s(n)].isBigFont=o)})},{label:t(()=>[l(m,{"key-name":"U"},{default:t(()=>[d(u(e.t("663")),1)]),_:1})]),_:1},8,["model-value"])]),_:1},8,["title"]),l(F,{title:e.t("241"),class:"mt-4"},{default:t(()=>[l(g,{modelValue:i.value[s(n)].textSize,"onUpdate:modelValue":a[4]||(a[4]=o=>i.value[s(n)].textSize=o),type:"number",min:"0",step:"0.001"},{prepend:t(()=>[l(m,{"key-name":"T"},{default:t(()=>[d(u(e.t("478")),1)]),_:1})]),_:1},8,["modelValue"])]),_:1},8,["title"]),l(F,{title:e.t("664"),class:"mt-4"},{default:t(()=>[l(g,{modelValue:i.value[s(n)].xScale,"onUpdate:modelValue":a[5]||(a[5]=o=>i.value[s(n)].xScale=o),type:"number",step:"0.001"},{prepend:t(()=>[l(m,{"key-name":"W",colon:""},{default:t(()=>[d(u(e.t("665")),1)]),_:1})]),_:1},8,["modelValue"]),l(g,{class:"mt-2",modelValue:i.value[s(n)].obliquingAngle,"onUpdate:modelValue":a[6]||(a[6]=o=>i.value[s(n)].obliquingAngle=o),type:"number",step:"0.001"},{prepend:t(()=>[l(m,{"key-name":"O",colon:""},{default:t(()=>[d(u(e.t("666")),1)]),_:1})]),_:1},8,["modelValue"])]),_:1},8,["title"])])]),_:1}),l(f,{cols:"2","align-self":"start"},{default:t(()=>[l(v,{class:"mt-5 w-100",isAction:""},{default:t(()=>[l(m,{"key-name":"C",onClick:s(K)},{default:t(()=>[d(u(e.t("508")),1)]),_:1},8,["onClick"])]),_:1}),l(v,{class:"mt-4 w-100",isAction:""},{default:t(()=>[l(m,{"key-name":"N",onClick:a[7]||(a[7]=o=>s(C)())},{default:t(()=>[d(u(e.t("270")),1)]),_:1})]),_:1}),l(v,{class:"mt-4 w-100",isAction:"",onClick:a[8]||(a[8]=o=>s(Z)({text:e.t("667")+":"+e.t("362")+"1?",define:()=>s(X)()})),disabled:ee.value},{default:t(()=>[l(m,{"key-name":"D"},{default:t(()=>[d(u(e.t("219")),1)]),_:1})]),_:1},8,["disabled"])]),_:1})]),_:1})])]),_:1},8,["title","modelValue"]),l(R,{title:e.t("668"),modelValue:s(b),"onUpdate:modelValue":a[13]||(a[13]=o=>_(b)?b.value=o:null),"max-width":"300",keys:te},{actions:t(()=>a[15]||(a[15]=[r("div",{class:"pa-1"},null,-1)])),default:t(()=>[l(B,{class:"py-3"},{default:t(()=>[l(f,{cols:"7","align-self":"start"},{default:t(()=>[l(g,{class:"mt-1 ml-8",modelValue:s(w),"onUpdate:modelValue":a[10]||(a[10]=o=>_(w)?w.value=o:null)},{prepend:t(()=>[r("span",Fe,u(e.t("669"))+":",1)]),_:1},8,["modelValue"])]),_:1}),l(f,{cols:"5"},{default:t(()=>[r("div",Be,[l(v,{primary:"",onClick:a[11]||(a[11]=o=>I()),isAction:""},{default:t(()=>[d(u(e.t("229")),1)]),_:1}),l(v,{class:"mt-2",onClick:a[12]||(a[12]=o=>s(C)()),isAction:""},{default:t(()=>[d(u(e.t("512")),1)]),_:1})])]),_:1})]),_:1})]),_:1},8,["title","modelValue"])],64)}}}),Re=de(Ne,[["__scopeId","data-v-f69b2514"]]);export{Re as default};
