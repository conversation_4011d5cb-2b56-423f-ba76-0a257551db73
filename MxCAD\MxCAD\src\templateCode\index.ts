import drawLine from "./param/drawLine?raw"
import drawCircle from "./param/drawCircle?raw"
import drawPoint from "./param/drawPoint?raw"
import drawImg from "./param/drawImg?raw"
import drawArc from "./param/drawArc?raw"
import drawPolyline from "./param/drawPolyline?raw"
import drawEllipse from "./param/drawEllipse?raw"
import drawText from "./param/drawText?raw"
import drawMtext from "./param/drawMtext?raw"
import drawDimension from "./param/drawDimension?raw"
import drawPath from "./param/drawPath?raw"
import drawHatch from "./param/drawHatch?raw"
import drawBlock from "./param/drawBlock?raw"
import drawCustom from "./param/drawCustom?raw"
import drawTypeLine from "./param/drawTypeLine?raw"
import drawTypeText from "./param/drawTypeText?raw"
import Mx_drawArc from "./interactive/Mx_drawArc?raw"
import Mx_drawLine from "./interactive/Mx_drawLine?raw"
import Mx_drawText from "./interactive/Mx_drawText?raw"
import Mx_drawCircle from "./interactive/Mx_drawCircle?raw"
import Mx_drawImg from "./interactive/Mx_drawImg?raw"
import Mx_drawPolyline from "./interactive/Mx_drawPolyline?raw"
import Mx_drawMxDbEllipse from "./interactive/Mx_drawMxDbEllipse?raw"
import Mx_drawHatchFormPoint from "./interactive/Mx_drawHatchFormPoint?raw"
import Mx_InsertBlock from "./interactive/Mx_InsertBlock?raw"
import Mx_drawCustomEntity from "./interactive/Mx_drawCustomEntity?raw"
import TestGetAllEntity from "./dataBase/TestGetAllEntity?raw"
import TestAddLayer from "./dataBase/TestAddLayer?raw"
import TestGetAllBlock from "./dataBase/TestGetAllBlock?raw"
import TestGetAllLayer from "./dataBase/TestGetAllLayer?raw"
import TestGetAllLinetype from "./dataBase/TestGetAllLinetype?raw"
import TestGetAllTextStyle from "./dataBase/TestGetAllTextStyle?raw"
import TestReadxData from "./dataBase/TestReadxData?raw"
import TestWritexData from "./dataBase/TestWritexData?raw"
import TestGetNamedObjectsDictionary from "./dataBase/TestGetNamedObjectsDictionary?raw"

export default [
    {
        type: "参数绘图",
        name: "画直线",
        code: drawLine,
    },
    {
        type: "参数绘图",
        name: "画圆",
        code: drawCircle,
    },
    {
        type: "参数绘图",
        name: "画点",
        code: drawPoint,
    },
    {
        type: "参数绘图",
        name: "画圆弧",
        code: drawArc,
    },
    {
        type: "参数绘图",
        name: "画图片",
        code: drawImg,
    },
    {
        type: "参数绘图",
        name: "画多段线",
        code: drawPolyline,
    },
    {
        type: "参数绘图",
        name: "画椭圆",
        code: drawEllipse,
    },
    {
        type: "参数绘图",
        name: "画文字",
        code: drawText,
    },
    {
        type: "参数绘图",
        name: "画多行文字",
        code: drawMtext,
    },
    {
        type: "参数绘图",
        name: "画尺寸标注",
        code: drawDimension,
    },
    {
        type: "参数绘图",
        name: "画路径",
        code: drawPath,
    },
    {
        type: "参数绘图",
        name: "画填充",
        code: drawHatch,
    },
    {
        type: "参数绘图",
        name: "画图块",
        code: drawBlock,
    },
    {
        type: "参数绘图",
        name: "画文本自定义实体",
        code: drawCustom,
    },
    {
        type: "参数绘图",
        name: "画多种线型的直线",
        code: drawTypeLine,
    },
    {
        type: "参数绘图",
        name: "画多种文字样式的文字",
        code: drawTypeText,
    },
    {
        type: "交互绘图",
        name: "取点画圆弧",
        code: Mx_drawArc,
    },
    {
        type: "交互绘图",
        name: "取点画直线",
        code: Mx_drawLine,
    },
    {
        type: "交互绘图",
        name: "取点画文字",
        code: Mx_drawText,
    },
    {
        type: "交互绘图",
        name: "取点画圆",
        code: Mx_drawCircle,
    },
    {
        type: "交互绘图",
        name: "取点画图片",
        code: Mx_drawImg,
    },
    {
        type: "交互绘图",
        name: "取点画多段线",
        code: Mx_drawPolyline,
    },
    {
        type: "交互绘图",
        name: "取点画椭圆",
        code: Mx_drawMxDbEllipse,
    },
    {
        type: "交互绘图",
        name: "选点填充",
        code: Mx_drawHatchFormPoint,
    },
    {
        type: "交互绘图",
        name: "插入图块",
        code: Mx_InsertBlock,
    },
    {
        type: "交互绘图",
        name: "绘制矩形",
        code: Mx_drawCustomEntity,
    },
    {
        type: "图纸数据库",
        name: "得到图纸所有对象",
        code: TestGetAllEntity,
    },
    {
        type: "图纸数据库",
        name: "添加图层",
        code: TestAddLayer,
    },
    {
        type: "图纸数据库",
        name: "得到图纸所有图块",
        code: TestGetAllBlock,
    },
    {
        type: "图纸数据库",
        name: "得到图纸所有图层",
        code: TestGetAllLayer,
    },
    {
        type: "图纸数据库",
        name: "得到图纸所有线型样式",
        code: TestGetAllLinetype,
    },
    {
        type: "图纸数据库",
        name: "得到图纸所有文字样式",
        code: TestGetAllTextStyle,
    },
    {
        type: "图纸数据库",
        name: "读扩展数据",
        code: TestReadxData,
    },
    {
        type: "图纸数据库",
        name: "写入扩展数据",
        code: TestWritexData,
    },
    {
        type: "图纸数据库",
        name: "得到命名字典",
        code: TestGetNamedObjectsDictionary,
    },
]