<script setup>
import {Search} from "@element-plus/icons-vue";
import {useCAdApp} from '@/hooks/useCAdApp.js'
import {getMaterialDrawings, getMaterialsByType} from "@/api/onlineDesign/index.js";
import {downloadFileDWG} from "@/api/task/index.js";
import {useIframeCommunication} from "@/hooks/useIframeCommunication.js";
import {ElMessage} from "element-plus";

const visible = defineModel('visible', {type: Boolean, default: false})

const {cadAppSrc} = useCAdApp()
const {sendMessage} = useIframeCommunication()


const iframeSrc = cadAppSrc({
  isPreview: '1'
})

const props = defineProps({
  currentId: {
    type: String,
    default: ''
  }
})
const emit = defineEmits(['submit'])


const drawList = ref([])
const handleNodeClick = (data) => {
  console.log(data)
  getDwgFile(data)
}
const defaultProps = {
  children: 'children',
  label: 'drawingname',
}

const cadIframeRef = ref(null)

const getDwgFile = (data) => {
  const id = data.drawingid
  if (!id) return
  downloadFileDWG(id).then(res => {
    nextTick(() => {
      setTimeout(() => {
        const params = {
          type: "cadPreview",
          content: "Mx_cadPreview",
          formData: {
            openUrl: res,
          }
        }
        sendMessage(cadIframeRef.value,params, (res => {
        }))
      }, 2000)
    })
  })
}

const form = reactive({
  searchName: '',
  searchValue: ''
})

const tableData = ref([]
)

const getTableData = () => {
  const {searchValue, ...rest} = form
  let value = []
  if (searchValue) {
    value = searchValue.split(' ').filter(item => item)
  }

  const params = {
    materialsprojectid: props.currentId,
    searchValue: value,
    ...rest
  }
  getMaterialsByType(params).then(res => {
    tableData.value = res.data
  })
}

const currentModuleTableRow = ref(null)

const handleCurrentChange = (val) => {
  console.log('handleCurrentChange', val)
  if(val) {
    currentModuleTableRow.value = val
    getDrawData()
  }
}

const getDrawData = () => {
  const params = {
    materialsProjectId: currentModuleTableRow.value.materialsprojectid
  }
  getMaterialDrawings(params).then(res => {
    drawList.value = res.data
  })
}

const query = () => {
  getTableData()
}



const onSubmit = () => {
  if(!currentModuleTableRow.value) return ElMessage.error('请选择！')

  emit('submit', currentModuleTableRow.value)
  visible.value = false
}

const closeVisible = () => {
  visible.value = false
}

onMounted(() => {
  getTableData()
})
</script>

<template>
  <el-dialog
      v-model="visible"
      append-to-body
      draggable
      overflow
      title="物料选型"
      width="60%"
      @close="closeVisible"
  >
    <div class="" style="">
          <el-form :model="form" inline>
            <el-form-item label="" prop="searchName">
              <el-select v-model="form.searchName" clearable style="width: 160px;">
                <el-option label="规格型号" value="spec"></el-option>
                <el-option label="物料名称" value="materialname"></el-option>
                <el-option label="erp编码" value="materialcodeerp"></el-option>
                <el-option label="技术规范ID" value="technicalprotocol"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="" prop="searchValue">
              <el-input v-model="form.searchValue" placeholder="请输入关键字">
                <template #append>
                  <el-button :icon="Search" @click="query"/>
                </template>
              </el-input>
            </el-form-item>
          </el-form>
          <el-table :data="tableData" :height="350" border highlight-current-row stripe style="width: 100%;margin-bottom: 8px"  @current-change="handleCurrentChange">
            <el-table-column align="center" label="物料名称" prop="materialname" width=""/>
            <el-table-column align="center" label="规格型号" prop="spec" width=""/>
            <el-table-column align="center" label="单位" prop="designunit" width="80"/>
            <el-table-column align="center" label="ERP编码" prop="materialcodeerp" width=""/>
            <el-table-column align="center" label="技术规范ID" prop="technicalprotocol" width=""/>
          </el-table>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card header="图纸列表">
          <el-tree
              :data="drawList"
              :props="defaultProps"
              highlight-current
              style="height: 200px"
              @node-click="handleNodeClick"
          >
            <template #default="{ node, data }">
              <el-tooltip
                  :content="node.label"
                  effect="dark"
                  placement="right"
              >
                <el-text class="" truncated>
                  {{ node.label }}
                </el-text>
              </el-tooltip>
            </template>
          </el-tree>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card header="预览">
            <div class="" style="height: 200px">
              <iframe
                  ref="cadIframeRef"
                  :src="iframeSrc"
                  style="width: 100%;height: 100%"
              />
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeVisible">取消</el-button>
        <el-button type="primary" @click="onSubmit">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>

</style>
