import{M as y}from"./index-8X61wlK0.js";import{a as p,n as u,M as c,aQ as w}from"./index-D95UjFey.js";import{$ as B,i as C,J as L,K as d,j as v,k as M,a9 as x,aa as D,a as F,m as I}from"./vuetify-B_xYg4qv.js";import{h as j,d as N,a0 as o,_ as r,$ as t,a1 as m,m as e,Q as s,V as l,a3 as R,a4 as T,F as $,u as G,B as Q}from"./vue-DfH9C9Rx.js";import"./mxcad-CfPpL1Bn.js";import"./mxdraw-C_n_7lEs.js";import"./mapbox-gl-PN3pDJHq.js";import"./xlsx-Ck5MUZvf.js";const S={class:"px-3 mt-2"},W={class:"d-flex justify-space-between w-100"},A={class:"d-flex justify-space-between align-center mt-2"},X=j({__name:"index",setup(E){const{isShow:n,showDialog:_}=w,V=[{name:"确定",fun:()=>{},primary:!0},{name:"关闭",fun:()=>_(!1)}],b=N([{title:"小数",value:1},{title:"小数",value:2},{title:"小数",value:3},{title:"小数",value:3},{title:"小数",value:1},{title:"小数",value:2},{title:"小数",value:3},{title:"小数",value:3}]);return(a,f)=>(o(),r(y,{title:a.t("647"),"max-width":"450",modelValue:G(n),"onUpdate:modelValue":f[0]||(f[0]=i=>Q(n)?n.value=i:null),footerBtnList:V},{default:t(()=>[m("div",S,[e(p,{"key-name":"W",colon:""},{default:t(()=>[s(l(a.t("648")),1)]),_:1}),e(B,{class:"mb-1",items:[]},{append:t(()=>[e(u,null,{default:t(()=>[s(l(a.t("256")),1)]),_:1})]),_:1}),e(p,{"key-name":"W",colon:""},{default:t(()=>[s(l(a.t("649")),1)]),_:1}),e(C,{class:"",items:[]},{append:t(()=>[e(u,null,{default:t(()=>[s(l(a.t("264")),1)]),_:1})]),_:1}),e(c,{title:a.t("650")},{default:t(()=>[e(L,{class:""},{default:t(()=>[m("div",W,[e(d,{label:a.t("651"),value:0},null,8,["label"]),e(d,{label:a.t("652"),value:1},null,8,["label"]),e(d,{label:a.t("653"),value:2},null,8,["label"])])]),_:1}),m("div",A,[e(v,{class:""},{label:t(()=>[s(l(a.t("654")),1)]),_:1}),e(u,null,{default:t(()=>[s(l(a.t("655")),1)]),_:1})])]),_:1},8,["title"]),e(c,{title:a.t("656")},{default:t(()=>[e(v,{class:""},{label:t(()=>[s(l(a.t("657")),1)]),_:1}),e(M,{border:"",height:"140",density:"compact",variant:"text"},{default:t(()=>[e(x,{multiple:"",class:"mr-8"},{default:t(()=>[(o(!0),R($,null,T(b.value,(i,h)=>(o(),r(D,null,{default:t(({isSelected:k,toggle:g})=>[(o(),r(F,{key:h,value:i,"active-class":"bg-light-blue-darken-2",active:k,onClick:g},{default:t(()=>[e(I,{textContent:l(i.title)},null,8,["textContent"])]),_:2},1032,["value","active","onClick"]))]),_:2},1024))),256))]),_:1})]),_:1})]),_:1},8,["title"])])]),_:1},8,["title","modelValue"]))}});export{X as default};
