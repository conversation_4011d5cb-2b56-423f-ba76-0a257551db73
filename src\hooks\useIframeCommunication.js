// useIframeCommunication.js
import {onMounted, onUnmounted} from "vue";
import { getLogSetting } from "@/utils/logSetting"
// let defaultIframeRef = document.querySelector("#myiframe").querySelector("iframe")

export function useIframeCommunication() {
    let listeners = [];
    // 发送消息到子页面
    /**
     * @param {*} containerRef iframe容器
     * @param {Object} message 参数
     * @param {function} callback 回调函数
     */
        const sendMessage = (containerRef, message, callback) => {
        console.log('iframeRef.value', containerRef)
        const iframe = containerRef.contentWindow
        if (!iframe) {
            console.warn("Iframe not found");
            return;
        }

        // 生成唯一消息 ID
        const messageId = `msg_${Date.now()}_${Math.random()}`;
        console.log('messageId', messageId);
        
        // 创建监听器
        const handleMessage = (event) => {
            if(getLogSetting('handleMessage')) console.log('父页面handleMessage', event.data);
            // 校验来源和消息 ID
            if (event.data.messageId === messageId) {
                callback?.(event.data.params); // 调用回调函数
                // 移除当前监听器
                // listeners = listeners.filter((listener) => listener !== handleMessage);
                listeners.concat(listeners.filter((listener) => listener !== handleMessage))
                // window.removeEventListener("message", handleMessage);
            }
        };

        // 添加全局监听
        window.addEventListener("message", handleMessage);
        listeners.push(handleMessage);

        // 发送消息到 iframe
        iframe.postMessage({...message, id: messageId}, "*");
    };

    // 清理监听器
    const cleanup = () => {
        listeners.forEach((listener) => {
            window.removeEventListener("message", listener);
        });
        listeners = [];
    };

    onMounted(() => {})

    // 注册生命周期钩子
    onUnmounted(() => {
        console.log('onUnmounted, useIframeCommunication')
        cleanup()
    });

    return {sendMessage, cleanup};
}
