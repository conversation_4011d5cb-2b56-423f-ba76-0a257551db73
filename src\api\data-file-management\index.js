import request from '@/utils/request'

// 通用保存
export function uploadFiles(data) {
    return request({
        url: '/fileHandler/uploadFiles',
        method: 'post',
        data,
        headers: {
            "Content-Type": "multipart/form-data",
        },
    })
}

export function downFile(params) {
    return request({
        url: '/fileHandler/downFile',
        method: 'get',
        params,
        responseType: 'blob'
    })
}

export function deleteFile(params) {
    return request({
        url: '/fileHandler/deleteFile',
        method: 'get',
        params,
    })
}

export function previewFile(params) {
    return request({
        url: '/fileHandler/previewFile',
        method: 'get',
        params,
        responseType: 'blob'
    })
}

export function getFileList(params) {
    return request({
        url: '/fileHandler/getFileList',
        method: 'get',
        params,
    })
}