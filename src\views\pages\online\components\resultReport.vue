<template>
  <data-dialog
    @close="closeDialog"
    dataWidth="700"
    v-if="
      appStore.mapIndex == '成果上报' &&
      (stage == 2 || stage == 3 || stage == 4)
    "
  >
    <template #header>
      <h4 style="margin-left: 10px; margin-top: 0px; line-height: 39px">
        成果上报
      </h4>
    </template>
    <template #body>
      <el-row class="line">
        <el-col :span="4" class="line-item">
          <span> 停电线路 </span>
        </el-col>
        <el-col :span="8" class="line-input">
          <el-input v-model="form.backLine" placeholder="请输入文本"></el-input>
        </el-col>
        <el-col :span="4" class="line-item">
          <span> 停电范围 </span>
        </el-col>
        <el-col :span="8" class="line-input">
          <el-input
            v-model="form.backRange"
            placeholder="请输入文本"
          ></el-input>
        </el-col>
      </el-row>
      <el-row class="line">
        <el-col :span="4" class="line-item">
          <span> 停电区域工程量 </span>
        </el-col>
        <el-col :span="8" class="line-input">
          <el-input v-model="form.backJob" placeholder="请输入文本"></el-input>
        </el-col>
        <el-col :span="4" class="line-item">
          <span>配变数量</span>
        </el-col>
        <el-col :span="8" class="line-input">
          <el-input
            v-model="form.backNum"
            type="number"
            min="1"
            placeholder="请输入整数"
          ></el-input>
        </el-col>
      </el-row>
      <el-row class="line">
        <el-col :span="4" class="line-item">
          <span>预计停电时长</span>
        </el-col>
        <el-col :span="8" class="line-input">
          <el-input
            v-model="form.backTime"
            type="number"
            min="1"
            placeholder="请输入整数"
          ></el-input>
        </el-col>
        <el-col :span="4" class="line-item">
          <span> 预计停电时户数 </span>
        </el-col>
        <el-col :span="8" class="line-input">
          <el-input
            v-model="form.backNumber"
            type="number"
            min="1"
            placeholder="请输入整数"
          ></el-input>
        </el-col>
      </el-row>
      <el-row class="line">
        <el-col :span="4" class="line-item" style="height: 115px">
          <span>未开展不停电作业原因分析</span>
        </el-col>
        <el-col :span="20" class="line-input" style="height: auto">
          <el-input
            v-model="form.backReason"
            type="textarea"
            :rows="5"
            placeholder="请输入文本"
          ></el-input>
        </el-col>
      </el-row>
      <div
        style="background: #e4f2f2; height: 50px; justify-content: center"
        class="line"
      >
        <div class="line-bnt" @click="click">绘制</div>
      </div>
    </template>
  </data-dialog>
</template>
<script setup>
import DataDialog from "@/components/DataDialog/index.vue";
import useAppStore from "@/store/modules/app.js";
import { useRoute } from "vue-router";
import { chengGuoShangBao } from "@/api/insertSag/index.js";
const { proxy } = getCurrentInstance();
import { inject } from "vue";
const route = useRoute();
const appStore = useAppStore();
const cadAppRef = inject("cadAppRef");
const taskId = route.query.id;
const stage = route.query.stage;
const callChildC = inject("callChildC");
const closeDialog = () => {
  appStore.mapIndex = "";
};
const form = ref({
  backLine: "",
  backRange: "",
  backJob: "",
  backNum: "",
  backTime: "",
  backNumber: "",
  backReason: "",
});
const click = () => {
  queryList();
};
const queryList = () => {
  chengGuoShangBao({
    PowerFailureLine: form.value.backLine,
    PowerFailureAre: form.value.backRange,
    PowerFailureQuantities: form.value.backJob,
    TransformerNum: form.value.backNum,
    PowerFailureDuration: form.value.backTime,
    PowerFailureUserNum: form.value.backNumber,
    PowerFailurereason: form.value.backReason,
    ywwym: taskId,
    stage: stage,
  }).then((res) => {
    if(res.code===200){
      proxy.$modal.msgSuccess("操作成功");
    }
  });
};
watch(
  () => appStore.mapIndex,
  (newInfo, oldInfo) => {
    if (newInfo == "成果上报") {
      if (stage == 2 || stage == 3 || stage == 4) {
        queryList();
      }
    }
  }
);
</script>

<style lang="scss" scoped>
@use "./index" as *;
</style>
