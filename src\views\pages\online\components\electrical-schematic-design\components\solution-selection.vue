<script setup>

const visible = defineModel('visible', {type: Boolean, default: false})

const formRef = ref(null)

const form = reactive({
  name: '',
})
const rules = reactive({
  name: [
    {required: true, message: '请输入', trigger: 'blur'}
  ]
})

const resetForm = (formEl) => {
  if (!formEl) return
  formEl.resetFields()
}

const closeVisible = () => {
  visible.value = false
  resetForm(formRef.value)
}


const onSubmit = async (formEl) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (!valid) return
    visible.value = false
  })
}

</script>

<template>
  <el-dialog
      v-model="visible"
      title="方案选择"
      width="25%"
      draggable
      overflow
      @close="closeVisible"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100">
      <el-row>
        <el-col>
          <el-form-item label="方案类别" prop="">
            <el-select v-model="form.region" placeholder="请选择">
              <!--              <el-option label="Zone one" value="shanghai"/>-->
            </el-select>
          </el-form-item>
        </el-col>
        <el-col>
          <el-form-item label="方案编号">
            <el-select v-model="form.region" placeholder="请选择">
            </el-select>
          </el-form-item>
        </el-col>
        <el-col>
          <el-form-item label="站房名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入站房编号">
            </el-input>
          </el-form-item>
        </el-col>
        <el-col>
          <el-form-item label="状态">
            <el-select v-model="form.region" placeholder="请选择">
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeVisible">取消</el-button>
        <el-button type="primary" @click="onSubmit(formRef)">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
</style>
