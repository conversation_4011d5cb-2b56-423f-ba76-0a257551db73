{"defaultTheme": "dark", "themes": {"light": {"colors": {"surface": "#FFFFFF", "background": "#E0E0E0", "accent": "#545555", "prominent": "#FFFFFF", "hover_herder_btn": "#B3B3B3", "modification": "#FFFFFF", "transition": "#EFEFEF", "undertint": "#3A4352", "depth": "#D3D1D1", "on-undertint-bg": "#000", "undertint-bg": "#f0f0f0", "depth-bg": "#EBE9E9", "inverse": "#000000", "inverse1": "#FFFFFF", "console": "#787878", "on-console": "#fff", "dialog-card": "#FFFFFF", "dialog-card-text": "#E0E0E0", "hover_electron_view_tab_btn_hover": "#DBDBDB", "hover_electron_view_tab_btn_action": "#FFFFFF", "nav_bg": "#DBDBDB", "nav_bg_active": "#787878", "sketches_color": "#E8E8E8", "toolbar_header": "#99b4d1"}, "variables": {"border-color": "#000000", "border-opacity": 0.12, "high-emphasis-opacity": 0.87, "medium-emphasis-opacity": 1, "disabled-opacity": 0.38, "idle-opacity": 0.04, "hover-opacity": 0.2, "focus-opacity": 0.12, "selected-opacity": 0.08, "activated-opacity": 0.1, "pressed-opacity": 0.12, "dragged-opacity": 0.08, "theme-kbd": "#212529", "theme-on-kbd": "#FFFFFF", "theme-code": "#F5F5F5", "theme-on-code": "#000000", "theme-tbody": "#fff", "theme-btn-hover": "#E0E0E0", "theme-undertint-bg-overlay-multiplier": 0.25}}, "dark": {"colors": {"surface": "#212832", "background": "#212832", "accent": "#545555", "prominent": "#3A4352", "hover_herder_btn": "#3A4352", "modification": "#1E252F", "transition": "#5D6675", "undertint": "#AEABAB", "on-undertint": "#fff", "depth": "#5D6675", "on-depth": "#fff", "undertint-bg": "#2E3440", "depth-bg": "#454F61", "inverse": "#FFFFFF", "inverse1": "#000000", "console": "#787878", "on-console": "#fff", "dialog-card": "#212832", "dialog-card-text": "#3A4352", "hover_electron_view_tab_btn_hover": "#3A4352", "hover_electron_view_tab_btn_action": "#616975", "nav_bg": "#3A4352", "nav_bg_active": "#282A2D", "sketches_color": "#3A4352", "toolbar_header": "#2B3647"}, "variables": {"border-color": "#FFFFFF", "border-opacity": 0.12, "high-emphasis-opacity": 0.87, "medium-emphasis-opacity": 1, "disabled-opacity": 0.38, "idle-opacity": 0.1, "hover-opacity": 0.2, "focus-opacity": 0.12, "selected-opacity": 0.08, "activated-opacity": 0.1, "pressed-opacity": 0.16, "dragged-opacity": 0.08, "theme-kbd": "#212529", "theme-on-kbd": "#FFFFFF", "theme-code": "#343434", "theme-on-code": "#CCCCCC", "theme-tbody": "#343b48", "theme-btn-hover": "#616975", "theme-surface-variant": "#424242", "theme-on-surface-variant": "#ffffff"}}}}