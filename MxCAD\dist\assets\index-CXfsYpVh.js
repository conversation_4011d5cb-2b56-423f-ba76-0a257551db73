import{M as X}from"./index-8X61wlK0.js";import{a1 as z,O as lt,n as J,a2 as ot,_ as it,b as j,M as k,a as M,c as st,a7 as at,s as B,u as rt,D as _t}from"./index-D95UjFey.js";import{d as U,h as q,a0 as E,_ as Y,$ as n,a1 as c,H as mt,W as pt,u as r,B as L,m as t,Q as w,V as S,a3 as Q,a4 as gt,n as ut,F as K}from"./vue-DfH9C9Rx.js";import{c as P,d as ct,h as Z,C as T,E as R,V as ft,b as dt,v as G}from"./vuetify-B_xYg4qv.js";import{h as At,e as $,M as vt,c as St,Q as wt}from"./mxcad-CfPpL1Bn.js";import{M as ht}from"./mxdraw-C_n_7lEs.js";import"./mapbox-gl-PN3pDJHq.js";import"./xlsx-Ck5MUZvf.js";const Dt=""+new URL("ACAD_ISO02W100-CgczrAa0.png",import.meta.url).href,bt=""+new URL("ACAD_ISO03W100-AzcC3_-f.png",import.meta.url).href,Ct=""+new URL("ACAD_ISO04W100-IbgtVBfA.png",import.meta.url).href,It=""+new URL("ACAD_ISO05W100-CEGXYqwd.png",import.meta.url).href,Rt=""+new URL("ACAD_ISO06W100-DJBzAH1k.png",import.meta.url).href,Lt=""+new URL("ACAD_ISO07W100-D_Mm-GmR.png",import.meta.url).href,Ut=""+new URL("ACAD_ISO08W100-Cq7PdjEU.png",import.meta.url).href,Ot=""+new URL("ACAD_ISO09W100-HuEA3bgs.png",import.meta.url).href,Nt=""+new URL("ACAD_ISO10W100-CpfOB3wW.png",import.meta.url).href,Wt=""+new URL("ACAD_ISO11W100-C-YvJa_U.png",import.meta.url).href,Vt=""+new URL("ACAD_ISO12W100-BJObCzYl.png",import.meta.url).href,xt=""+new URL("ACAD_ISO13W100-CnVTTEXT.png",import.meta.url).href,Bt=""+new URL("ACAD_ISO14W100-Dt_4eew3.png",import.meta.url).href,Et=""+new URL("ACAD_ISO15W100-DQCL7ZHX.png",import.meta.url).href,yt=""+new URL("ANGLE-C0JXMnJI.png",import.meta.url).href,kt=""+new URL("ANSI31-DNoWrKeq.png",import.meta.url).href,Mt=""+new URL("ANSI32-C1D8NgmN.png",import.meta.url).href,Pt=""+new URL("ANSI33-ChicYIJo.png",import.meta.url).href,Tt=""+new URL("ANSI34-VGH_akWK.png",import.meta.url).href,Ft=""+new URL("ANSI35-Di7w2V2Y.png",import.meta.url).href,Ht=""+new URL("ANSI36-XpcWXjaS.png",import.meta.url).href,jt=""+new URL("ANSI37-D7GYIXmO.png",import.meta.url).href,Yt=""+new URL("ANSI38-BFp_zzVO.png",import.meta.url).href,Gt=""+new URL("AR-B816-DX1HtyXm.png",import.meta.url).href,$t=""+new URL("AR-B816C-CIka6EB3.png",import.meta.url).href,Xt=""+new URL("AR-SAND-BpLNDMKH.png",import.meta.url).href,zt=""+new URL("BRASS-5al0fgci.png",import.meta.url).href,Jt=""+new URL("BRSTONE-BxLw_BHN.png",import.meta.url).href,qt=""+new URL("CLAY-9bOpp4wn.png",import.meta.url).href,Qt=""+new URL("CROSS-PtiPbaRk.png",import.meta.url).href,Kt=""+new URL("DASH-CZmcszsI.png",import.meta.url).href,Zt=""+new URL("DOLMIT-Ds4N0lwQ.png",import.meta.url).href,te=""+new URL("DOTS-DFwG-nhe.png",import.meta.url).href,ee=""+new URL("EARTH-C5ec_3YI.png",import.meta.url).href,ne=""+new URL("ESCHER-DfDvcwpb.png",import.meta.url).href,le=""+new URL("GRASS-Bos1Dol8.png",import.meta.url).href,oe=""+new URL("GRATE-ntK-iw_6.png",import.meta.url).href,ie=""+new URL("HEX-DvwF6L-t.png",import.meta.url).href,se=""+new URL("HONEY-BwjEJk47.png",import.meta.url).href,ae=""+new URL("INSUL-DdipjSEk.png",import.meta.url).href,re=""+new URL("SOLID-BE-LInwT.png",import.meta.url).href;function tt(s){const _=[];let l=null;return s.split(`
`).forEach(g=>{if(g.trim()!=="")if(g.startsWith("*")){l&&(l.value+=")",_.push(l));const u=g.substring(1).split(",").map(m=>m.trim());l={id:u[0],name:u[1],value:"(",imgPath:new URL(Object.assign({"../imgs/ACAD_ISO02W100.png":Dt,"../imgs/ACAD_ISO03W100.png":bt,"../imgs/ACAD_ISO04W100.png":Ct,"../imgs/ACAD_ISO05W100.png":It,"../imgs/ACAD_ISO06W100.png":Rt,"../imgs/ACAD_ISO07W100.png":Lt,"../imgs/ACAD_ISO08W100.png":Ut,"../imgs/ACAD_ISO09W100.png":Ot,"../imgs/ACAD_ISO10W100.png":Nt,"../imgs/ACAD_ISO11W100.png":Wt,"../imgs/ACAD_ISO12W100.png":Vt,"../imgs/ACAD_ISO13W100.png":xt,"../imgs/ACAD_ISO14W100.png":Bt,"../imgs/ACAD_ISO15W100.png":Et,"../imgs/ANGLE.png":yt,"../imgs/ANSI31.png":kt,"../imgs/ANSI32.png":Mt,"../imgs/ANSI33.png":Pt,"../imgs/ANSI34.png":Tt,"../imgs/ANSI35.png":Ft,"../imgs/ANSI36.png":Ht,"../imgs/ANSI37.png":jt,"../imgs/ANSI38.png":Yt,"../imgs/AR-B816.png":Gt,"../imgs/AR-B816C.png":$t,"../imgs/AR-SAND.png":Xt,"../imgs/BRASS.png":zt,"../imgs/BRSTONE.png":Jt,"../imgs/CLAY.png":qt,"../imgs/CROSS.png":Qt,"../imgs/DASH.png":Kt,"../imgs/DOLMIT.png":Zt,"../imgs/DOTS.png":te,"../imgs/EARTH.png":ee,"../imgs/ESCHER.png":ne,"../imgs/GRASS.png":le,"../imgs/GRATE.png":oe,"../imgs/HEX.png":ie,"../imgs/HONEY.png":se,"../imgs/INSUL.png":ae,"../imgs/SOLID.png":re})[`../imgs/${u[0]}.png`],import.meta.url).href}}else{if(!l){console.error("Invalid pattern file format. Missing pattern start marker (*)");return}const u=g.trim().split(",").map(h=>h.startsWith(".")?"0"+h:h).join(),m=(l.value==="("?"(":" (")+u+")";l.value+=m}}),l&&_.push(l),_}var y=(s=>(s.ANSI="ANSI",s.ISO="ISO",s.ANY="ANY",s))(y||{});const F={ANSI:new URL(""+new URL("mx-CCuJTE_q.pat",import.meta.url).href,import.meta.url).href,ISO:new URL(""+new URL("mxiso-BxzaJzF8.pat",import.meta.url).href,import.meta.url).href,ANY:new URL(""+new URL("mxuser-tWg2Arwx.pat",import.meta.url).href,import.meta.url).href},d=U(),N=U(0),H={};Object.keys(F).forEach(async s=>{const _=F[s],v=await(await fetch(_)).blob(),g=await z(v);H[s]=tt(g),d.value||V(s)});const W=U(""),V=s=>{W.value=F[s],d.value=H[s];let _=0;x.value&&(_=d.value.indexOf(x.value)),x.value=d.value[_]},x=U(),_e=s=>{if(d.value){const _=d.value.indexOf(s);_>=0&&(N.value=_)}x.value=s},me=()=>({patContent:d,activeIndex:N,defaultPatContents:H,switchPath:V,filePath:W,item:x,onchange:_e}),pe={class:"px-3"},ge={class:"d-flex algin-center mt-3"},ue={class:"mt-2"},ce={class:"fill_box"},fe=["onClick"],de={class:"d-inline-block text-truncate"},Ae=q({__name:"FillSelectDialog",emits:["change"],setup(s,{expose:_,emit:l}){const{isShow:v,showDialog:g}=lt(!1),u=U(),m=()=>{u.value&&u.value.click()},h=async p=>{const o=p.target,i=o.files;if(!i)return;const I=i[0],b=await ot(I);if(typeof b!="object")return;o.value="";const O=await z(b);d.value=tt(O)},C=l,D=()=>{if(d.value){const p=d.value[N.value];C("change",p)}g(!1)},A=[{name:"确定",fun:D,primary:!0},{name:"关闭",fun:()=>g(!1)}];return _({showDialog:g}),(p,o)=>(E(),Y(X,{title:p.t("595"),"max-width":"400",modelValue:r(v),"onUpdate:modelValue":o[4]||(o[4]=i=>L(v)?v.value=i:null),footerBtnList:A},{default:n(()=>[c("div",pe,[c("div",ge,[mt(c("input",{class:"form__inset w-100",disabled:!0,"onUpdate:modelValue":o[0]||(o[0]=i=>L(W)?W.value=i:null)},null,512),[[pt,r(W)]]),t(J,{onClick:m,class:"ml-1"},{default:n(()=>[w(S(p.t("269"))+"(F)...",1)]),_:1})]),c("input",{type:"file",ref_key:"fillFileSelect",ref:u,onChange:h,style:{display:"none"},accept:".pat"},null,544),c("div",ue,[t(P,{density:"compact",class:"mr-2",onClick:o[1]||(o[1]=i=>r(V)(r(y).ANSI))},{default:n(()=>o[5]||(o[5]=[w("ANSI")])),_:1}),t(P,{density:"compact",class:"mr-2",onClick:o[2]||(o[2]=i=>r(V)(r(y).ISO))},{default:n(()=>o[6]||(o[6]=[w("ISO")])),_:1}),t(P,{density:"compact",onClick:o[3]||(o[3]=i=>r(V)(r(y).ANY))},{default:n(()=>[w(S(p.t("596")),1)]),_:1})]),c("div",ce,[(E(!0),Q(K,null,gt(r(d),(i,I)=>(E(),Y(ct,{text:i.id+" "+i.name,location:"bottom","open-delay":800},{activator:n(({props:b})=>[c("div",ut({ref_for:!0},b,{class:["fill_pattern",r(N)===I?"fill_pattern_active":""],onClick:O=>N.value=I,onDblclick:D}),[t(Z,{src:i.imgPath||"",width:"32",height:"32"},null,8,["src"]),c("span",de,S(i.id),1)],16,fe)]),_:2},1032,["text"]))),256))])])]),_:1},8,["title","modelValue"]))}}),ve=it(Ae,[["__scopeId","data-v-7f7d957f"]]),Se={class:"px-3"},we={class:"d-flex align-center"},he={class:"mr-2"},Ne=q({__name:"index",setup(s){const{isShow:_,showDialog:l}=at,v=U(),{patContent:g,onchange:u,item:m}=me(),h=e=>{v.value?.showDialog(e)},C=j(0,"PatternFillingDialog_angle"),D=j(11,"PatternFillingDialog_proportion");let A,p;const o=async()=>{l(!1);const e=new At;e.clearLastInputPoint(),e.setMessage(`
`+B("指定填充区域内部一点")+":"),e.disableAllTrace(!0),e.setDisableOsnap(!0);const a=await e.go();a&&(p=a,A="point"),l(!0)};let i;const I=async()=>{l(!1);const e=await $.userSelect(B("选择对象"));if(e&&e.length>0){const f=e[0].getMcDbEntity();f&&(i=f),A="object"}l(!0)},b=async()=>{if(A==="point"&&p){const e=$.builderHatchFromPoint(p);if(!e){ht.acutPrintf(B("没有找到闭合区域")+`
`),rt().error(B("没有找到闭合区域"));return}let a=vt.getCurrentMxCAD();m.value&&(a.addPatternDefinition(m.value.id,m.value.value),a.drawPatternDefinition=m.value.id),e.patternAngle=C.value,a.drawHatch(e,D.value*10),l(!1),_t(),A=p=void 0}A==="object"&&i&&(i instanceof St&&new wt,i=A=void 0,l(!1))},O=()=>{A=i=p=void 0,l(!1)},et=[{name:"确定",fun:b,primary:!0,disabled:()=>typeof A>"u"},{name:"关闭",fun:O}],nt={enter:()=>{document.activeElement?.tagName!=="INPUT"&&b()},esc:O,k:o,b:I};return(e,a)=>(E(),Q(K,null,[t(X,{title:e.t("597"),"max-width":"360",modelValue:r(_),"onUpdate:modelValue":a[4]||(a[4]=f=>L(_)?_.value=f:null),footerBtnList:et,keys:nt},{default:n(()=>[c("div",Se,[t(k,{title:e.t("598"),class:"mt-2"},{default:n(()=>[t(T,null,{default:n(()=>[t(R,{cols:"8"},{default:n(()=>[t(ft,{items:r(g),"item-title":"id",modelValue:r(m),"onUpdate:modelValue":[a[0]||(a[0]=f=>L(m)?m.value=f:null),r(u)],"return-object":""},{prepend:n(()=>[w(S(e.t("362"))+": ",1)]),_:1},8,["items","modelValue","onUpdate:modelValue"])]),_:1}),t(R,{cols:"4"},{default:n(()=>[t(J,{style:{"min-width":"60px"},onClick:a[1]||(a[1]=f=>h(!0))},{default:n(()=>[t(dt,{icon:"class:iconfont more"})]),_:1})]),_:1})]),_:1}),t(T,null,{default:n(()=>[t(R,{cols:"8"},{default:n(()=>[c("div",we,[c("span",he,S(e.t("599"))+":",1),t(Z,{src:r(m)?.imgPath||"",width:"32",height:"32",style:{flex:"unset"}},null,8,["src"])])]),_:1}),t(R,{cols:"4"})]),_:1})]),_:1},8,["title"]),t(k,{title:e.t("600"),class:"mt-2"},{default:n(()=>[t(T,null,{default:n(()=>[t(R,{cols:"6"},{default:n(()=>[t(M,{"key-name":"G",colon:""},{default:n(()=>[w(S(e.t("281")),1)]),_:1}),t(G,{modelValue:r(C),"onUpdate:modelValue":a[2]||(a[2]=f=>L(C)?C.value=f:null),items:[0,5,10,15,20,30,45,60,90,95,100,120,135,150]},null,8,["modelValue"])]),_:1}),t(R,{cols:"6"},{default:n(()=>[t(M,{"key-name":"S",colon:""},{default:n(()=>[w(S(e.t("240")),1)]),_:1}),t(G,{modelValue:r(D),"onUpdate:modelValue":a[3]||(a[3]=f=>L(D)?D.value=f:null),items:[.25,.5,.75,1,1.25,1.5,1.75,2,11]},null,8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["title"]),t(k,{title:e.t("601"),class:"mt-2"},{default:n(()=>[c("div",null,[t(st,{onClick:o}),t(M,{"key-name":"K"},{default:n(()=>[w(S(e.t("602"))+": "+S(e.t("216")),1)]),_:1})])]),_:1},8,["title"])])]),_:1},8,["title","modelValue"]),t(ve,{ref_key:"fillSelectDialog",ref:v,onChange:r(u)},null,8,["onChange"])],64))}});export{Ne as default};
