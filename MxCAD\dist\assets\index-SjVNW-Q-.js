import{d as G,h as H,a3 as Q,a4 as l,u as r,B as k,_ as W,a0 as s,m as e,Q as u,V as n,H as L,I as P}from"./vue-Cj9QYd7Z.js";import{f as A,s as M,l as z,e as K,Q as X,U as i,V as S,W as V,_ as Y}from"./index-CzBriCFR.js";import{M as $}from"./index-itnQ6avM.js";import{r as q}from"./index-BWTo6kTe.js";import{e as E,h as J,k as Z}from"./mxcad-DrgW2waE.js";import{B as o,G as ee,H as N,z as _,L as p,K as T,V as le,I as te}from"./vuetify-BqCp6y38.js";import"./mxdraw-BQ5HhRCY.js";import"./handsontable-Ch5RdAT_.js";import"./mapbox-gl-DQAr7S0z.js";const F=""+new URL("jingao-DVjn0eNy.png",import.meta.url).href,se=c=>{const b=A(!0,"Mx_ArrayDialog_is_array_rect"),h=A(5,"Mx_ArrayDialog_row"),v=A(5,"Mx_ArrayDialog_col"),C=A(200,"Mx_ArrayDialog_row_offset"),x=A(200,"Mx_ArrayDialog_col_offset"),g=A(0,"Mx_ArrayDialog_angle");let f=[];const w=G(0),j=async()=>(c(!1),f=await E.userSelect(M("选择陈列对象")),w.value=f.length,c(!0),f),D=async y=>{c(!1);const m=new J;m.clearLastInputPoint(),y&&m.setMessage(y);const U=await m.go();if(!U)return c(!0);m.setUserDraw((t,a)=>{a.drawLine(t.toVector3(),U?.toVector3())});const R=await m.go();return R?(c(!0),U.distanceTo(R)):c(!0)},O=async()=>{c(!1);const y=new Z;y.clearLastInputPoint(),y.setMessage(M("指定整列角度"));const m=await y.go();typeof m=="number"&&(g.value=z(m,3)),c(!0)},I=K();return{isArrayRect:b,row:h,col:v,rowOffset:C,colOffset:x,angle:g,selectCount:w,selectObject:j,getLength:D,getAngle:O,onConfirmClick:()=>{if(b.value){if(w.value<=0)return I.error(M("未选择需要阵列对象")+"!");q({iColNum:v.value,iRowNum:h.value,dAng:g.value,aryId:f,dColOffset:x.value,dRowOffset:C.value})}c(!1)}}},ae={class:"px-3"},oe={class:"d-flex justify-center align-center"},ne={class:"d-flex justify-center align-center"},ue={class:"px-2"},de={class:"w-100"},ie={class:"w-100 mt-3"},re={class:"w-50"},ce={class:"w-25"},fe={class:"w-25 ml-1"},me={class:"d-flex flex-column justify-center align-center h-100"},_e={class:"w-100"},pe={class:"w-50"},ge={class:"w-25"},ve={class:"d-flex flex-column algin-center justify-center ml-4"},ye={class:"mr-12"},Ve={class:"px-3"},we={class:"d-flex flex-column algin-center justify-center ml-4"},ke={class:"h-100 pl-2"},be={class:"d-flex algin-center ml-2 mb-1",style:{"line-height":"1.5rem"}},Ce={class:"mt-2"},xe=H({__name:"index",setup(c){const{isShow:b,showDialog:h}=X(!1,"Mx_ArrayDialog"),{isArrayRect:v,row:C,col:x,rowOffset:g,colOffset:f,angle:w,selectCount:j,selectObject:D,getLength:O,getAngle:I,onConfirmClick:B}=se(h),y=async()=>{const t=await O(M("指定行间距"));t&&(g.value=t)},m=async()=>{const t=await O(M("指定列间距"));t&&(f.value=t)},U=async()=>{const t=await O(M("指定单位单元"));t&&(f.value=t,g.value=t)},R=[{name:"确定",fun:B,primary:!0},{name:"取消",fun:()=>h(!1)}];return(t,a)=>(W(),Q($,{title:t.t("290"),modelValue:r(b),"onUpdate:modelValue":a[6]||(a[6]=d=>k(b)?b.value=d:null),"max-width":"520",footerBtnList:R},{default:l(()=>[s("div",ae,[e(_,{class:"mt-1","align-stretch":""},{default:l(()=>[e(o,{cols:7,class:"h-100","align-self":"start"},{default:l(()=>[e(ee,{modelValue:r(v),"onUpdate:modelValue":a[0]||(a[0]=d=>k(v)?v.value=d:null),inline:""},{default:l(()=>[e(N,{value:!0,class:"mr-12"},{label:l(()=>[e(i,{"key-name":"R"},{default:l(()=>[u(n(t.t("189")),1)]),_:1})]),_:1}),e(N,{value:!1},{label:l(()=>[e(i,{"key-name":"P"},{default:l(()=>[u(n(t.t("190")),1)]),_:1})]),_:1})]),_:1},8,["modelValue"]),L(s("div",null,[e(_,null,{default:l(()=>[e(o,{cols:"6"},{default:l(()=>[e(p,{class:"mt-2 ml-2",type:"number",modelValue:r(C),"onUpdate:modelValue":a[1]||(a[1]=d=>k(C)?C.value=d:null)},{prepend:l(()=>[s("div",oe,[a[7]||(a[7]=s("div",{class:"box box-row mr-1"},[s("div",{class:"box-line"})],-1)),e(i,{"key-name":"W",colon:""},{default:l(()=>[u(n(t.t("191")),1)]),_:1})])]),_:1},8,["modelValue"])]),_:1}),e(o,{cols:"6"},{default:l(()=>[e(p,{class:"mt-2 ml-2",type:"number",modelValue:r(x),"onUpdate:modelValue":a[2]||(a[2]=d=>k(x)?x.value=d:null)},{prepend:l(()=>[s("div",ne,[a[8]||(a[8]=s("div",{class:"box box-col mr-1"},[s("div",{class:"box-line"})],-1)),e(i,{"key-name":"O",colon:""},{default:l(()=>[u(n(t.t("192")),1)]),_:1})])]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(S,{class:"mt-1",title:t.t("475")},{default:l(()=>[s("div",ue,[e(_,{class:"mt-2"},{default:l(()=>[e(o,{cols:"4"},{default:l(()=>[s("p",de,[e(i,{"key-name":"F",colon:""},{default:l(()=>[u(n(t.t("193")),1)]),_:1})]),s("p",ie,[e(i,{"key-name":"M",colon:""},{default:l(()=>[u(n(t.t("194")),1)]),_:1})])]),_:1}),e(o,{cols:"8",class:"d-flex"},{default:l(()=>[s("div",re,[e(p,{class:"",type:"number",modelValue:r(g),"onUpdate:modelValue":a[3]||(a[3]=d=>k(g)?g.value=d:null)},null,8,["modelValue"]),e(p,{class:"mt-3",type:"number",modelValue:r(f),"onUpdate:modelValue":a[4]||(a[4]=d=>k(f)?f.value=d:null)},null,8,["modelValue"])]),s("div",ce,[e(V,{class:"h-100",onClick:U})]),s("div",fe,[s("div",me,[e(V,{onClick:y}),e(V,{class:"mt-2",onClick:m})])])]),_:1})]),_:1}),e(_,null,{default:l(()=>[e(o,{cols:"4"},{default:l(()=>[s("p",_e,[e(i,{"key-name":"A",colon:""},{default:l(()=>[u(n(t.t("195")),1)]),_:1})])]),_:1}),e(o,{cols:"8",class:"d-flex"},{default:l(()=>[s("div",pe,[e(p,{class:"mt-1",type:"number",step:"0.01",modelValue:r(w),"onUpdate:modelValue":a[5]||(a[5]=d=>k(w)?w.value=d:null)},null,8,["modelValue"])]),s("div",ge,[e(V,{class:"mt-1",onClick:r(I)},null,8,["onClick"])]),a[9]||(a[9]=s("div",{class:"w-25 ml-1"},null,-1))]),_:1})]),_:1}),e(_,{class:"mt-2"},{default:l(()=>[e(o,{cols:"3"},{default:l(()=>[s("div",ve,[e(T,{src:F,alt:t.t("196"),width:"24px"},null,8,["alt"]),u(" "+n(t.t("196")),1)])]),_:1}),e(o,{cols:"9"},{default:l(()=>[u(n(t.t("197")),1)]),_:1})]),_:1})])]),_:1},8,["title"])],512),[[P,r(v)]]),L(s("div",null,[e(_,{class:"d-flex mx-3 mt-1"},{default:l(()=>[s("p",ye,n(t.t("198"))+":",1),e(p,null,{prepend:l(()=>a[10]||(a[10]=[s("span",{class:""},"X:",-1)])),_:1}),e(p,{class:"ml-1"},{prepend:l(()=>a[11]||(a[11]=[s("span",{class:""},"Y:",-1)])),_:1}),e(V)]),_:1}),e(S,{title:t.t("476")},{default:l(()=>[s("div",Ve,[e(i,{"key-name":"M",colon:""},{default:l(()=>[u(n(t.t("199")),1)]),_:1}),e(le,{"bg-color":"grey-lighten-2",class:"",items:[]}),e(_,null,{default:l(()=>[e(o,{cols:"6"},{default:l(()=>[e(i,{"key-name":"I",colon:""},{default:l(()=>[u(n(t.t("200")),1)]),_:1})]),_:1}),e(o,{cols:"3"},{default:l(()=>[e(p,{class:"mt-1"})]),_:1}),e(o,{cols:"3"})]),_:1}),e(_,null,{default:l(()=>[e(o,{cols:"6"},{default:l(()=>[e(i,{"key-name":"F",colon:""},{default:l(()=>[u(n(t.t("201")),1)]),_:1})]),_:1}),e(o,{cols:"3"},{default:l(()=>[e(p,{class:"mt-1"})]),_:1}),e(o,{cols:"3"},{default:l(()=>[e(V)]),_:1})]),_:1}),e(_,null,{default:l(()=>[e(o,{cols:"6"},{default:l(()=>[e(i,{"key-name":"B",colon:""},{default:l(()=>[u(n(t.t("202")),1)]),_:1})]),_:1}),e(o,{cols:"3"},{default:l(()=>[e(p,{class:"mt-1",disabled:""})]),_:1}),e(o,{cols:"3"},{default:l(()=>[e(V,{disabled:""})]),_:1})]),_:1}),e(_,{class:"mt-2"},{default:l(()=>[e(o,{cols:"3"},{default:l(()=>[s("div",we,[e(T,{src:F,alt:t.t("196"),width:"24px"},null,8,["alt"]),u(" "+n(t.t("196")),1)])]),_:1}),e(o,{cols:"9"},{default:l(()=>[u(n(t.t("203")),1)]),_:1})]),_:1})])]),_:1},8,["title"]),e(te,{class:"mt-2"},{label:l(()=>[e(i,{"key-name":"T"},{default:l(()=>[u(n(t.t("204")),1)]),_:1})]),_:1})],512),[[P,!r(v)]])]),_:1}),e(o,{cols:5,"align-self":"start"},{default:l(()=>[s("div",ke,[s("div",be,[e(V,{onClick:r(D)},null,8,["onClick"]),e(i,{"key-name":"S"},{default:l(()=>[u(n(t.t("205")),1)]),_:1})]),s("p",Ce,n(t.t("206")+r(j)+t.t("207")+t.t("208")),1),a[12]||(a[12]=s("div",{class:"obj-box"},null,-1))])]),_:1})]),_:1})])]),_:1},8,["title","modelValue"]))}}),Be=Y(xe,[["__scopeId","data-v-227c3e98"]]);export{Be as default};
