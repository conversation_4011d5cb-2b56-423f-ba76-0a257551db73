import { Mx<PERSON><PERSON>, DetailedResult, DynamicInputType } from "mxdraw";
import { McDbBlockReference, MxCADUiPrPoint, MxCpp, McDbPolyline, McDbMText, MxCADUiPrAngle, McGeVector3d, McDbEntity, McGePoint3d, McDb, MxCADUiPrEntity, MxCADUiPrKeyWord } from "mxcad";
import { formData } from './../mapBox/customize'

export function init(){
  MxFun.addCommand("JGFZ", jgfz);
}

const jgfz = async () => {
    const intervalStationId = 1
    const drawingScale = 100
    InsertBlock(intervalStationId, drawingScale)
}
// 插入图块
async function InsertBlock(intervalStationId, drawingScale) {
    const intervalCount = 7
    const intervals = []
    for (let i = 0; i < intervalCount; i++) {
        const polyline = new McDbPolyline();
        polyline.addVertexAt(new McGePoint3d(0,0,0));
        polyline.addVertexAt(new McGePoint3d(0,0,0));
        polyline.addVertexAt(new McGePoint3d(0,0,0));
        polyline.addVertexAt(new McGePoint3d(0,0,0));
        polyline.isClosed = true

        const text = new McDbMText();
        text.textHeight = 2.5;//MxFun.screenCoordLong2Doc(2.5);
        text.location = new McGePoint3d(0,0,0);
        text.attachment = McDb.AttachmentPoint.kMiddleCenter;
        text.contents = `G${i+1}`;
        intervals.push({pl: polyline, Length: 1052, Depth: 640, txt: text})
    }
    //选择图元
    let getEnt = new MxCADUiPrEntity();
    getEnt.setMessage("选择站房图元");
    let entId = await getEnt.go();
    if(!entId.id) return;
    // 通过ID对象得到图形数据对象
    let ent = entId.getMcDbEntity();

    //选择方式
    const getKey = new MxCADUiPrKeyWord()
    getKey.setMessage("输入平面布置图选项")
    getKey.setKeyWords("[单列布置(1)/双列面对面布置(2)/双列背对背布置(3)]")
    const key = await getKey.go()
    if(getKey.getDetailedResult() === DetailedResult.kCodeAbort) return
    if(getKey.getDetailedResult() === DetailedResult.kEcsIn) return
    if(getKey.getDetailedResult() === DetailedResult.kNewCommadIn) return
    
    //选择位置
    let getPoint = new MxCADUiPrPoint();
    getPoint.setMessage("指定插入点");
    
    // 动态绘制图块
    getPoint.setUserDraw((pt, worldDraw) => {
        let basePT = new McGePoint3d(pt.x, pt.y, 0);
        if(key === '1'){
            //单列布置
            for (let i = 0; i < intervals.length; i++)
            {
                const item = intervals[i];
                const length = item.Length / drawingScale;
                const depth = item.Depth / drawingScale;
                item.txt.location = new McGePoint3d(basePT.x + (length / 2.0), basePT.y + (depth / 2.0), 0);//间隔编号 位置
                item.pl.setPointAt(0, new McGePoint3d(basePT.x, basePT.y));
                item.pl.setPointAt(1, new McGePoint3d(basePT.x + length, basePT.y));
                item.pl.setPointAt(2, new McGePoint3d(basePT.x + length, basePT.y + depth));
                item.pl.setPointAt(3, new McGePoint3d(basePT.x, basePT.y + depth));
                basePT = basePT.addvec(new McGeVector3d(length, 0, 0))
            }
        } else if(key === '2'){
            //双列面对面布置
            let num = 0;
            for (let i = num; i < Math.ceil(intervals.length / 2.0); i++)
            {
                const item = intervals[i];
                const length = item.Length / drawingScale;
                const depth = item.Depth / drawingScale;
                item.txt.location = new McGePoint3d(basePT.x + (length / 2.0), basePT.y + (depth / 2.0), 0);//间隔编号 位置
                item.pl.setPointAt(0, new McGePoint3d(basePT.x, basePT.y));
                item.pl.setPointAt(1, new McGePoint3d(basePT.x + length, basePT.y));
                item.pl.setPointAt(2, new McGePoint3d(basePT.x + length, basePT.y + depth));
                item.pl.setPointAt(3, new McGePoint3d(basePT.x, basePT.y + depth));
                // item.pl.TransformBy(mt);
                basePT = basePT.addvec(new McGeVector3d(length, 0, 0))
                num++;
            }
            const distance = 20;
            basePT = new McGePoint3d(basePT.x , basePT.y-distance, 0);//第二排间隔柜 起始基点（向下平移 两排间隔柜最小距离）
            for (let i = num; i < intervals.length; i++)
            {
                const item = intervals[i];
                const length = item.Length / drawingScale;
                const depth = item.Depth / drawingScale;
                item.txt.location = new McGePoint3d(basePT.x - (length / 2.0), basePT.y - (depth / 2.0), 0);//间隔编号 位置
                item.txt.rotation = Math.PI;
                item.pl.setPointAt(0, new McGePoint3d(basePT.x, basePT.y, 0));
                item.pl.setPointAt(1, new McGePoint3d(basePT.x - length, basePT.y, 0));
                item.pl.setPointAt(2, new McGePoint3d(basePT.x - length, basePT.y - depth, 0));
                item.pl.setPointAt(3, new McGePoint3d(basePT.x, basePT.y - depth, 0));
                // item.pl.TransformBy(mt);
                basePT = basePT.addvec(new McGeVector3d(-length, 0, 0))
                num++;
            }
        } else if(key === '3'){
            //双列背对背布置
            let num = 0;
            for (let i = num; i < Math.ceil(intervals.length / 2.0); i++)
            {
                const item = intervals[i];
                const length = item.Length / drawingScale;
                const depth = item.Depth / drawingScale;
                item.txt.location = new McGePoint3d(basePT.x + (length / 2.0), basePT.y - (depth / 2.0), 0);//间隔编号 位置
                item.txt.rotation = Math.PI;
                item.pl.setPointAt(0, new McGePoint3d(basePT.x, basePT.y));
                item.pl.setPointAt(1, new McGePoint3d(basePT.x + length, basePT.y));
                item.pl.setPointAt(2, new McGePoint3d(basePT.x + length, basePT.y - depth));
                item.pl.setPointAt(3, new McGePoint3d(basePT.x, basePT.y - depth));
                // item.pl.TransformBy(mt);
                basePT = basePT.addvec(new McGeVector3d(length, 0, 0))
                num++;
            }
            let distance1 = countDistance(intervals) / drawingScale + 10;
            basePT = new McGePoint3d(basePT.x , basePT.y-distance1);//第二排间隔柜 起始基点（向下平移 两排间隔柜最小距离）
            for (let i = num; i < intervals.length; i++)
            {
                const item = intervals[i];
                const length = item.Length / drawingScale;
                const depth = item.Depth / drawingScale;
                item.txt.location = new McGePoint3d(basePT.x - (length / 2.0), basePT.y + (depth / 2.0), 0);//间隔编号 位置
                item.pl.setPointAt(0, new McGePoint3d(basePT.x, basePT.y));
                item.pl.setPointAt(1, new McGePoint3d(basePT.x - length, basePT.y));
                item.pl.setPointAt(2, new McGePoint3d(basePT.x - length, basePT.y + depth));
                item.pl.setPointAt(3, new McGePoint3d(basePT.x, basePT.y + depth));
                // item.pl.TransformBy(mt);
                basePT = basePT.addvec(new McGeVector3d(-length, 0, 0))
                num++;
            }
        }
        for (let interval of intervals) {
            worldDraw.drawMcDbEntity(interval.pl);
            worldDraw.drawMcDbEntity(interval.txt);
        }
    })
    const insertPT = await getPoint.go();
    if (!insertPT) return;

    // 绘制实体
    const mxcad = MxCpp.getCurrentMxCAD();
    for (let interval of intervals) {
        mxcad.drawEntity(interval.pl);
        mxcad.drawEntity(interval.txt);
    }
}

/// <summary>
/// 计算第二排间隔平移距离（仅支持双列背对背布置模式）
/// </summary>
/// <param name="_listPline"></param>
/// <returns></returns>
const countDistance = (intervals) => {
    let num = 0;
    let maxDistanceOne = 0;//第一排间隔柜 最大深度
    //双列背对背布置
    for (let i = 0; i < Math.ceil(intervals.length / 2.0); i++)
    {
        if (intervals[i].Depth > maxDistanceOne) 
        {
            maxDistanceOne = intervals[i].Depth ;
        }
        num++;
    }
    let maxDistanceTwo = 0;//第二排间隔柜 最大深度
    for (let i = num; i < intervals.length; i++)
    {
        if (intervals[i].Depth > maxDistanceTwo)
        {
            maxDistanceTwo = intervals[i].Depth ;
        }
        num++;
    }
    return maxDistanceOne + maxDistanceTwo;
}