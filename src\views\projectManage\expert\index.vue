<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true">
         <el-form-item label="专家名称" prop="projectName">
            <el-input
               v-model="queryParams.userName"
               placeholder="请输入专家名称"
               clearable
               style="width: 200px"
            />
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
         </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
         <el-col :span="1.5">
            <el-button
               type="primary"
               plain
               icon="Plus"
               @click="handleAdd"
            >新增</el-button>
         </el-col>
      </el-row>
      <el-table v-loading="loading" stripe highlight-current-row :data="versionList" @current-change="handleCurrentChange">
        <el-table-column type="index" width="55" align="center" label="序号"/>
        <el-table-column label="专家名称" show-overflow-tooltip align="center" prop="projectName" />
        <el-table-column label="联系方式" align="center" prop="projectName" />
        <el-table-column label="省公司" align="center" prop="cityOrganisationName" />
        <el-table-column label="归属公司" align="center" prop="countyOrganisationName" />
        <el-table-column label="操作" width="180" align="center" class-name="small-padding fixed-width">
          <template #default="scope">           
            <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:post:edit']">修改</el-button>
            <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:post:remove']">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
         v-show="total > 0"
         :total="total"
         v-model:page="queryParams.pageNum"
         v-model:limit="queryParams.pageSize"
         @pagination="getList"
      />
      <!--新增或修改对话框-->
      <el-dialog :title="title" v-model="open" width="400" append-to-body>
        <el-form :model="form" :rules="rules" ref="versionRef" label-width="80px">
          <el-form-item label="归属公司" prop="cityOrganisationCode">
            <el-tree-select v-model="form.companyId" :data="deptOptions" :props="{ value: 'id', label: 'label', children: 'children' }" value-key="id" placeholder="请选择归属部门" check-strictly @node-click="handleNodeClick"/>
            <!-- <el-select v-model="form.provinceId" @change="provinceChange" placeholder="请选择">
              <el-option v-for="dict in provinceOptions" :key="dict.deptId" :label="dict.deptName" :value="dict.deptId"></el-option>
            </el-select> -->
          </el-form-item>
          <!-- <el-form-item label="供电公司" prop="cityOrganisationCode">
            <el-select v-model="form.companyId" @change="cityChange" placeholder="请选择">
              <el-option v-for="dict in CityCompanyOptions" :key="dict.deptId" :label="dict.deptName" :value="dict.deptId"></el-option>
            </el-select>
          </el-form-item> -->
          <el-form-item label="专家人员" prop="cityOrganisationCode">
            <el-select v-model="form.userId" @change="provinceChange" placeholder="请选择">
              <el-option v-for="dict in usersOptions" :key="dict.userId" :label="dict.userName" :value="dict.userId"></el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="归属公司" prop="countyOrganisationCode">
            <el-input
               v-model="form.companyName"
               clearable
            />
          </el-form-item>  -->
          <el-form-item label="手机号" prop="countyOrganisationCode">
            <el-input
               v-model="form.userIphone"
               placeholder="请输入手机号"
               clearable
            />
          </el-form-item> 
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
         </div>
        </template>
      </el-dialog>
  </div>
</template>  
<script setup name="dataVersion">
const { proxy } = getCurrentInstance();
import { getList as getListByEngineer, addOrUpdate, delEngineering } from "@/api/taskManage/taskProjectInfo";
import { deptTreeSelect, listUser, getListByDept } from "@/api/system/user";
import { ref } from 'vue';
import useUserStore from '@/store/modules/user'

const userStore = useUserStore()
const loading = ref(false)
const total = ref(0)
const versionList = ref([])
const title = ref("");
const open = ref(false)
const deptOptions = ref([])
const usersOptions = ref([])
const data = reactive({
  form: {
    objId:'',
    provinceId: '',
    companyId: '',
    userId: undefined,
    userIphone: undefined,
    companyName: ''
  },
  queryParams: {
   pageNum: 1,
   pageSize: 10,
   userName: undefined
  },
  companyInfo: {
   companyId: undefined,
   companyName: undefined,
  },
  rules: {
    userId: [{ required: true, message: "专家姓名不能为空", trigger: "blur" }],
    userIphone: [{ required: true, message: "联系方式不能为空", trigger: "blur" }]
  },
});
const { queryParams,form,companyInfo,rules } = toRefs(data);

/** 查询部门下拉树结构 */
function getDeptTree() {
  deptTreeSelect().then(response => {
    deptOptions.value = response.data;
  });
};

/** 查询用户列表 */
function handleNodeClick(data) {
  let queryParams = {
    deptId: data.id
  }
  loading.value = true;
  getListByDept(queryParams).then(res => {
    loading.value = false;
    usersOptions.value = res.data;
  });
};


/**查询事件 */
function handleQuery() {
   queryParams.value.pageNum = 1
   getList()
}

/**重置查询参数 */
function resetQuery() {
   queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    companyId: undefined,
    versionName: undefined
  }
  getList()
}

/**新增专家信息 */
function handleAdd() {
  getDeptTree()
  open.value = true
  form.value.projectCodeParent = ""
  title.value = '选择专家信息'
}

/**选中列表 */
function handleCurrentChange(row) {
  if (row) {
    form.value.projectCodeParent = row.objId
  }
}

/**修改版本 */
function handleUpdate(row) {
  getProvinceTree()
  open.value = true
  title.value = '修改工程信息'
  form.value = row
  if (form.value.cityOrganisationCode) {
    provinceChange(form.value.cityOrganisationCode)
  }
  if (form.value.countyOrganisationCode) {
    cityChange(form.value.countyOrganisationCode)
  }
  if (form.value.projectDepthType.length > 0) {
    form.value.projectDepthType = form.value.projectDepthType.split(',')
  }
}

/**删除版本 */
function handleDelete(row){
   proxy.$modal.confirm('是否确认删除项目编号为"' + row.projectCode + '"的数据项?').then(function() {
    const param = {
      objId: row.objId
    }
    return delEngineering(param);
  }).then((req) => {
    console.log(req)
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch((err) => {
    console.log(req)
  });
}

/**获取工程信息数据 */
function getList() {
  loading.value = true;
  getListByEngineer(queryParams.value).then(response => {
    versionList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  }).catch(() => {
    loading.value = false;
  })
}

/**保存新版本 */
function submitForm() {
   proxy.$refs["versionRef"].validate(valid => {
      if (valid) {
         const provinceOption = provinceOptions.value.find(option => option.powerCompanyId == form.value.cityOrganisationCode)
         form.value.cityOrganisationName = provinceOption.powerCompanyName
         const cityCompanyOption = CityCompanyOptions.value.find(option => option.powerCompanyId == form.value.countyOrganisationCode)
         console.log(CityCompanyOptions.value)
         form.value.countyOrganisationName = cityCompanyOption.powerCompanyName
         let _str = '';
         form.value.projectDepthType.forEach(element => {
          _str += element + ','
         });
         form.value.projectDepthType = _str.substring(0,_str.lastIndexOf(','))
         addOrUpdate(form.value).then(res => {
          getList()
          proxy.$modal.msgSuccess("保存成功");
          cancel()
        })        
      }
   })
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
};

/** 重置操作表单 */
function reset() {
  form.value = {
    objId:'',
    projectCode: undefined,
    projectName: undefined,
    projectCodeParent: undefined,
    compilingOrganisationName: undefined,
    compilerName: undefined,
    compilerNumber: undefined,
    isStandard: undefined,
    cityOrganisationName: undefined,
    countyOrganisationName: undefined,
    cityOrganisationCode: undefined,
    countyOrganisationCode: undefined,
    createYear: undefined,
    voltageLevel: undefined,
    assetNature: undefined,
    projectType: undefined,
    powersupplyStationOrganisationName: undefined,
    projectDepthType: undefined,
    state: undefined,
    rollback: undefined,
    stage: undefined
  };
  proxy.resetForm("versionRef");
};
getList()
</script>