import Vue, { VNode } from 'vue'



declare global {
  const MxPluginContext: typeof import("./MxPluginContext").default
  const mxcad: typeof import("mxcad")
  const Mx: typeof import("mxdraw")
  interface Window {
    MxPluginContext: typeof import("./MxPluginContext").default;
    mxcad:  typeof import("mxcad");
    Mx: typeof import("mxdraw")
  }
  namespace JSX {
    interface Element extends VNode { }
    interface ElementClass extends Vue { }
    interface IntrinsicElements {
      [elem: string]: any
    }
  }

}
