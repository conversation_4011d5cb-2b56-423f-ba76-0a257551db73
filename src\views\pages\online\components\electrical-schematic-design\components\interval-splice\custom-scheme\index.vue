<script setup>
const visible = defineModel('visible', {type: Boolean, default: false})
const closeVisible = () => {
  visible.value = false
}

const form = reactive({})

const tableData = ref([
  {}
])

</script>

<template>
  <el-dialog
      v-model="visible"
      title="自定义方案"
      width="50%"
      draggable
      overflow
      append-to-body
      @close="closeVisible"
  >
    <el-form ref="formRef" :model="form" :inline="true">
      <el-form-item label="方案编号" prop="">
        <el-input v-model="form.dl" clearable/>
      </el-form-item>
      <el-form-item label="方案名称" prop="">
        <el-input v-model="form.dl" clearable/>
      </el-form-item>
      <el-form-item>
        <el-button type="primary">
          添加
        </el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="20">
      <el-col :span="12">
        <div style="margin-bottom: 8px" class="">
          <el-button type="danger">删除方案</el-button>
        </div>
        <el-table :data="tableData" border stripe height="300px" style="width: 100%">
          <el-table-column type="selection" label="" align="center" width="55"/>
          <el-table-column prop="" label="自定义方案名称" align="center" width=""/>
          <el-table-column prop="" label="操作" align="center" width="100">
            <template #default="scope">
              <el-button type="primary" text>覆盖</el-button>
            </template>
          </el-table-column>
        </el-table>

      </el-col>
      <el-col :span="12">
        右侧区域
      </el-col>
    </el-row>
  </el-dialog>
</template>

<style scoped lang="scss">

</style>
