import { McDbBlockReference, MxCADUiPrPoint, MxCpp } from "mxcad";

function getHostUrl(): string {
    let host = window.location.hostname;
    if (host.substring(0, 4) != "http") {
        host = document.location.protocol + "//" + host;
    }
    return host;
}

// 插入图块
async function MxTest_InsertBlock() {
    let baseUrl = "http://localhost:3000/mxcad/"
    if (baseUrl.substring(0, 16) == "http://localhost") {
      baseUrl = getHostUrl() + baseUrl.substring(16);
    }
    // 设置图块路径
    let blkFilePath = baseUrl + "tree.mxweb";

    // 插入图块文件
    let mxcad = MxCpp.App.getCurrentMxCAD();
    let blkrecId = await mxcad.insertBlock(blkFilePath, "tree");
    if (!blkrecId.isValid()) {
        // 插入图块
        return;
    }

    // 设置图块大小
    let blkRef = new McDbBlockReference();
    blkRef.blockTableRecordId = blkrecId;
    let box = blkRef.getBoundingBox();
    if (box.ret) {
        let dLen = box.maxPt.distanceTo(box.minPt);
        if (dLen > 0.00001) {
            blkRef.setScale(mxcad.getMxDrawObject().screenCoordLong2Doc(100) / dLen);
        }
    }

    // 指定插入基点
    let getPoint = new MxCADUiPrPoint();
    getPoint.setMessage("\指定插入基点");

    // 动态绘制图块
    getPoint.setUserDraw((v, worldDraw) => {
        blkRef.position = v;
        worldDraw.drawMcDbEntity(blkRef);
    });

    let pt = await getPoint.go();
    if (!pt) return;
    blkRef.position = pt;
    // 绘制图块
    let newBlkRefId = mxcad.drawEntity(blkRef);
    if (!newBlkRefId.isValid) {
        console.log("insert error");
        return;
    }
};

// 调用插入图块方法
MxTest_InsertBlock();