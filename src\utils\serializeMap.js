// 自定义序列化函数（支持复杂键和值）
export const serializeMap = (map) => {
    const mapArray = Array.from(map).map(([key, value]) => {
      return [
        typeof key === 'object' ? JSON.stringify(key) : key,
        typeof value === 'object' ? JSON.stringify(value) : value,
      ];
    });
    return JSON.stringify(mapArray);
}
  
// 自定义反序列化函数（支持复杂键和值）
export const deserializeMap = (jsonString) => {
    const mapArray = JSON.parse(jsonString).map(([key, value]) => {
        return [
            typeof key === 'string' && key.startsWith('{') ? JSON.parse(key) : key,
            typeof value === 'string' && value.startsWith('{') ? JSON.parse(value) : value,
        ];
    });
    return new Map(mapArray);
}