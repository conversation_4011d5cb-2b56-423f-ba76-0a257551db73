import { McDbText, MxCADUiPrDist, MxCADUiPrPoint, MxCADUiPrString, MxCpp } from "mxcad";

async function Mx_drawText() {
    // 创建文字对象
    const text = new McDbText();

    // 设置文字内容
    const getStr = new MxCADUiPrString()
    getStr.setMessage("请输入文字内容")
    const str = await getStr.go()
    if (!str) return
    text.textString = str;

    // 设置文字高度
    const getDist = new MxCADUiPrDist()
    getDist.setMessage("请输入文字高度")
    const height = await getDist.go()
    if (!height) return
    text.height = getDist.value();

    // 设置文字高度
    const getPoint = new MxCADUiPrPoint()
    getPoint.setMessage("请点击确定文字位置")
    getPoint.setUserDraw((pt, pw) => {
        text.position = pt;
        text.alignmentPoint = pt;
        pw.drawMcDbEntity(text)
    })
    const position = await getPoint.go()
    if (!position) return
    text.position = position;
    text.alignmentPoint = position

    const mxcad = MxCpp.getCurrentMxCAD();
    mxcad.drawEntity(text);
};

// 调用绘制文字的方法
Mx_drawText();