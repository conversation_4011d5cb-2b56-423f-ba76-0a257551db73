<template>
  <div class="app-container home">
    <el-table
         v-loading="loading"
         stripe
         :data="taskList"
         row-key="menuId"
         :default-expand-all="isExpandAll"
         :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      >
         <el-table-column prop="menuName" label="任务名称" :show-overflow-tooltip="true"></el-table-column>
         <el-table-column prop="orderNum" label="所属项目"></el-table-column>
         <el-table-column prop="perms" label="供电单位" width="120"></el-table-column>
         <el-table-column prop="submitUser" label="提出人员" width="120"></el-table-column>
         <el-table-column prop="solveUser" label="处理人员" width="120"></el-table-column>
         <el-table-column prop="createDate" label="创建日期" width="120"></el-table-column>
      </el-table>
      <pagination
         v-show="total > 0"
         :total="total"
         v-model:page="queryParams.pageNum"
         v-model:limit="queryParams.pageSize"
         @pagination="getList"
      />
  </div>
</template>

<script setup name="Index">
  const loading = ref(false);
  const isExpandAll = ref(false);
  const taskList = ref([{
   menuName:'浙江项目设计任务',
   orderNum:'浙江华云项目',
   perms:'xxx单位',
   submitUser:'姜波',
   solveUser:'田旭',
   createDate:'2024-12-11'
  },{
   menuName:'xxx项目信息维护',
   orderNum:'浙江华云项目',
   perms:'xxx单位',
   submitUser:'姜波',
   solveUser:'田旭',
   createDate:'2024-12-11'
  }]);
  const total = ref(2);
  const data = reactive({
   queryParams: {
      pageNum: 1,
      pageSize: 10,
   },
  })
  const { queryParams } = toRefs(data);

  function getList(){

  }

// 在组件挂载时初始化 iframe
onMounted(() => {

});

</script>

<style scoped lang="scss">
.home {
  height: calc(100vh - 86px);
}

</style>
