import{M as x}from"./index-8X61wlK0.js";import{C as k,I as _,n as f}from"./index-D95UjFey.js";import{h as g,z as T,a0 as a,_ as n,$ as s,u as t,a3 as m,Q as r,V as c,a5 as u,a1 as C,F as V,L as B,B as D}from"./vue-DfH9C9Rx.js";import"./vuetify-B_xYg4qv.js";import"./mxcad-CfPpL1Bn.js";import"./mxdraw-C_n_7lEs.js";import"./mapbox-gl-PN3pDJHq.js";import"./xlsx-Ck5MUZvf.js";const w={class:"d-flex justify-center algin-center py-7"},M={key:0,class:"d-flex justify-space-evenly py-3"},I=g({__name:"index",setup(N){const p=k(),{options:e,isShow:i}=_(p),{showDialog:d}=p,y=()=>{e.value.define&&e.value.define(),d(!1)},v=()=>{e.value.cancel&&e.value.cancel(),d(!1)};return T(()=>{e.value.mounted&&e.value.mounted()}),(S,o)=>(a(),n(x,{title:t(e).title,modelValue:t(i),"onUpdate:modelValue":o[2]||(o[2]=l=>D(i)?i.value=l:null),"max-width":"250"},{actions:s(()=>[t(e).defineTitle||t(e).cancelTitle?(a(),m("div",M,[typeof t(e).defineTitle=="string"?(a(),n(f,{key:0,isAction:"",primary:"",onClick:o[0]||(o[0]=l=>y())},{default:s(()=>[r(c(t(e).defineTitle),1)]),_:1})):u("",!0),typeof t(e).cancelTitle=="string"?(a(),n(f,{key:1,isAction:"",onClick:o[1]||(o[1]=l=>v())},{default:s(()=>[r(c(t(e).cancelTitle),1)]),_:1})):u("",!0)])):u("",!0)]),default:s(()=>[C("div",w,[typeof t(e).text=="string"?(a(),m(V,{key:0},[r(c(t(e).text),1)],64)):(a(),n(B(t(e).text),{key:1}))])]),_:1},8,["title","modelValue"]))}});export{I as default};
