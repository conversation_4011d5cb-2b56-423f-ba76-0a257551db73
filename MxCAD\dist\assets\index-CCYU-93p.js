import{M as C}from"./index-8X61wlK0.js";import{M as u,n as a,a as m,az as L}from"./index-D95UjFey.js";import{E as c,k as g,S as w,C as A,R as B}from"./vuetify-B_xYg4qv.js";import{h as D,d as r,c as k,a0 as f,_ as M,$ as t,a1 as l,m as e,Q as i,a3 as h,a4 as S,H as b,ab as R,V,F as j,u as E,B as F}from"./vue-DfH9C9Rx.js";import"./mxcad-CfPpL1Bn.js";import"./mxdraw-C_n_7lEs.js";import"./mapbox-gl-PN3pDJHq.js";import"./xlsx-Ck5MUZvf.js";const I={class:"px-3 ShortcutKeySettingsDialog"},N={class:"d-flex justify-end my-2"},$={class:"w-100 text-left"},q=["onClick"],z={class:"d-flex justify-start my-2"},T={class:"d-flex justify-space-between"},H={class:"d-flex justify-space-between my-2"},et=D({__name:"index",setup(K){const{isShow:n,showDialog:Q}=L,p=r([{title:"功能",type:"",value:0,list:[{name:"A",cmd:"S",cmdList:[{cmd:"f"},{cmd:"l"}]},{name:"B",cmd:"S",cmdList:[{cmd:"a"},{cmd:"d"}]}]},{title:"绘图",type:"",value:1,list:[{name:"画线",cmd:"MxLine",cmdList:[{cmd:"line"},{cmd:"l"}]},{name:"画圆弧",cmd:"Mx_Arc",cmdList:[{cmd:"arc"},{cmd:"_arc"}]}]}]),v=r(0),_=k(()=>p.value[v.value].list),o=r(0),x=k(()=>_.value[o.value].cmdList);return(U,s)=>(f(),M(C,{title:"命令行快捷命令设置","max-width":"800",modelValue:E(n),"onUpdate:modelValue":s[0]||(s[0]=d=>F(n)?n.value=d:null)},{actions:t(()=>s[9]||(s[9]=[l("div",{class:"my-1"},null,-1)])),default:t(()=>[l("div",I,[e(A,{class:"mt-2","align-stretch":"","no-gutters":""},{default:t(()=>[e(c,{cols:"3","align-self":"stretch",class:"h-100"},{default:t(()=>[e(u,{title:"分类"},{default:t(()=>[e(g,{class:"mt-1",height:"300px",items:p.value,"active-class":"active",selected:[v.value]},null,8,["items","selected"])]),_:1}),l("div",N,[e(a,{isAction:"",class:"mr-4"},{default:t(()=>[e(m,{"key-name":"E"},{default:t(()=>s[1]||(s[1]=[i("导出设置文件")])),_:1})]),_:1})])]),_:1}),e(c,{cols:"6","align-self":"stretch",style:{height:"300px"}},{default:t(()=>[e(u,{title:"功能列表"},{default:t(()=>[e(w,{class:"mt-1 mx-table",height:"300px"},{default:t(()=>[s[2]||(s[2]=l("thead",null,[l("tr",null,[l("th",null,"功能名称"),l("th",null,"快捷键")])],-1)),l("tbody",$,[(f(!0),h(j,null,S(_.value,(d,y)=>b((f(),h("tr",{key:d.cmd,class:R(o.value===y?"active":""),onClick:G=>o.value=y},[l("td",null,V(d.name),1),l("td",null,V(d.cmd),1)],10,q)),[[B]])),128))])]),_:1})]),_:1}),l("div",z,[e(a,{isAction:"",class:"ml-4"},{default:t(()=>[e(m,{"key-name":"I"},{default:t(()=>s[3]||(s[3]=[i("导入设置文件")])),_:1})]),_:1}),e(a,{isAction:"",class:"ml-8"},{default:t(()=>[e(m,{"key-name":"R"},{default:t(()=>s[4]||(s[4]=[i("恢复默认设置")])),_:1})]),_:1})])]),_:1}),e(c,{cols:"3","align-self":"stretch",class:"h-100"},{default:t(()=>[e(u,{title:"快捷命令"},{default:t(()=>[l("div",T,[e(a,null,{default:t(()=>[e(m,{"key-name":"A"},{default:t(()=>s[5]||(s[5]=[i("新增")])),_:1})]),_:1}),e(a,{class:"ml-2"},{default:t(()=>[e(m,{"key-name":"D"},{default:t(()=>s[6]||(s[6]=[i("删除")])),_:1})]),_:1})]),e(g,{class:"mt-1",height:"279px",items:x.value,"item-title":"cmd","item-value":"cmd","active-class":"active",selected:[x.value[0].cmd]},null,8,["items","selected"])]),_:1}),l("div",H,[e(a,{primary:"",isAction:""},{default:t(()=>s[7]||(s[7]=[i(" 确定 ")])),_:1}),e(a,{class:"ml-2",isAction:""},{default:t(()=>s[8]||(s[8]=[i(" 取消 ")])),_:1})])]),_:1})]),_:1})])]),_:1},8,["modelValue"]))}});export{et as default};
