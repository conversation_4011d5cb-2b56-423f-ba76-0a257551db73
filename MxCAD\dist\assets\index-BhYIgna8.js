import{M as H}from"./index-8X61wlK0.js";import{b as A,f as L,h as r,l as Q,a as P,j as X,m as G,n as Y,M as v,o as u,u as q,s as U,p as c}from"./index-D95UjFey.js";import{M as J,h as N,y as S,k as K,a as O}from"./mxcad-CfPpL1Bn.js";import{h as W}from"./mxdraw-C_n_7lEs.js";import{v as _,b as ee,E as T,h as te,j as k,i as w,U as le,C as ae}from"./vuetify-B_xYg4qv.js";import{h as se,d as I,r as oe,a0 as ne,_ as ie,$ as a,a1 as m,m as l,Q as f,V as p,u as s,B as C}from"./vue-DfH9C9Rx.js";import"./mapbox-gl-PN3pDJHq.js";import"./xlsx-Ck5MUZvf.js";const de={class:"px-3"},ue={class:"d-flex algin-center mt-2 w-75"},me={class:""},re={class:""},pe={class:""},fe={class:""},Ue=se({__name:"index",setup(Ve){const{isShow:D,showDialog:B}=G,R=I(1),i=oe({x:0,y:0}),V=A(!1,"Mx_AttachPictureDialog_isGetPt"),b=A(!1,"Mx_AttachPictureDialog_isGetZoomRatio"),y=A(!1,"Mx_AttachPictureDialog_isGetRotationAngle"),M=I(1),x=I(0),E=[{name:"确定",fun:async()=>{if(!r.value)return;if(r.value.url==="")return q().warning(U("请先选择图片"));B(!1);const o=J.getCurrentMxCAD();if(V.value){const n=new N;n.setMessage(U("指定插入点"));const d=await n.go();if(!d)return;i.x=d.x,i.y=d.y}if(b.value){const n=new N,d=new S(i.x,i.y);n.setBasePt(d),n.setMessage(U("指定缩放比例")),n.setUserDraw((j,z)=>{const h=j.distanceTo(d)/u.width,F=u.width*h,$=u.height*h,Z=new THREE.Vector3(d.x+F,d.y+$);z.drawRect(d.toVector3(),Z)});const g=await n.go();if(!g)return;M.value=c(g.distanceTo(d)/u.width,3)}if(y.value){const n=new K;n.setMessage(U("指定旋转角度"));const d=new S(i.x,i.y);n.setBasePt(d);const g=await n.go();if(!g)return;n.getDetailedResult()===W.kCoordIn?x.value=c(g,3):x.value=c(g/(Math.PI/180),3)}let t=o.drawImage(i.x,i.y,u.width*M.value,u.height*M.value,x.value,r.value.fileName||r.value.url,!0,u.width,u.height).getMcDbEntity();if(t){const n=t.trueColor;t.trueColor=new O(n.red,n.green,n.blue,R.value*255)}},primary:!0},{name:"关闭",fun:()=>B(!1)}];return(o,e)=>(ne(),ie(H,{title:o.t("473"),modelValue:s(D),"onUpdate:modelValue":e[13]||(e[13]=t=>C(D)?D.value=t:null),"max-width":"600",footerBtnList:E},{default:a(()=>[m("div",de,[m("div",ue,[l(_,{class:"mt-1 mr-2","return-object":"","item-title":"fileName","item-value":"url",items:s(L),modelValue:s(r),"onUpdate:modelValue":[e[0]||(e[0]=t=>C(r)?r.value=t:null),e[1]||(e[1]=t=>s(Q)(t))],modelModifiers:{lazy:!0}},{prepend:a(()=>[m("span",me,[f(p(o.t("209"))+"(",1),e[14]||(e[14]=m("span",{class:"text-decoration-underline"},"N",-1)),e[15]||(e[15]=f(") "))])]),_:1},8,["items","modelValue"]),l(Y,{onClick:e[2]||(e[2]=t=>s(X)(s(G)))},{default:a(()=>[l(P,{"key-name":"B"},{default:a(()=>[f(p(o.t("210")),1)]),_:1}),e[16]||(e[16]=f()),l(ee,{icon:"class:iconfont more"})]),_:1})]),l(ae,{"align-stretch":"",class:"mt-2"},{default:a(()=>[l(T,{cols:8,"align-self":"auto"},{default:a(()=>[l(v,{title:o.t("474"),class:"h-100"},{default:a(()=>[l(te,{src:s(r)?.url,crossorigin:"anonymous",height:"425px"},null,8,["src"])]),_:1},8,["title"])]),_:1}),l(T,{cols:4,"align-self":"auto"},{default:a(()=>[l(v,{title:o.t("475")},{default:a(()=>[l(k,{modelValue:s(V),"onUpdate:modelValue":e[3]||(e[3]=t=>C(V)?V.value=t:null)},{label:a(()=>[l(P,{"key-name":"S"},{default:a(()=>[f(p(o.t("215")),1)]),_:1})]),_:1},8,["modelValue"]),l(w,{class:"mt-1",modelValue:i.x,"onUpdate:modelValue":e[4]||(e[4]=t=>i.x=t),disabled:s(V)},{prepend:a(()=>e[17]||(e[17]=[m("span",{class:""}," X:",-1)])),_:1},8,["modelValue","disabled"]),l(w,{class:"mt-1",modelValue:i.y,"onUpdate:modelValue":e[5]||(e[5]=t=>i.y=t),disabled:s(V)},{prepend:a(()=>e[18]||(e[18]=[m("span",{class:""}," Y:",-1)])),_:1},8,["modelValue","disabled"])]),_:1},8,["title"]),l(v,{title:o.t("44")},{default:a(()=>[l(k,{modelValue:s(b),"onUpdate:modelValue":e[6]||(e[6]=t=>C(b)?b.value=t:null)},{label:a(()=>[l(P,{"key-name":"S"},{default:a(()=>[f(p(o.t("215")),1)]),_:1})]),_:1},8,["modelValue"]),l(w,{class:"mt-1 ml-1",type:"number",disabled:s(b),modelValue:M.value,"onUpdate:modelValue":e[7]||(e[7]=t=>M.value=t)},null,8,["disabled","modelValue"])]),_:1},8,["title"]),l(v,{title:o.t("476")},{default:a(()=>[l(w,{class:"mt-1",type:"number",modelValue:s(u).width,"onUpdate:modelValue":e[8]||(e[8]=t=>s(u).width=t)},{prepend:a(()=>[m("span",re,p(o.t("477"))+":",1)]),_:1},8,["modelValue"]),l(w,{class:"mt-1",type:"number",modelValue:s(u).height,"onUpdate:modelValue":e[9]||(e[9]=t=>s(u).height=t)},{prepend:a(()=>[m("span",pe,p(o.t("478"))+":",1)]),_:1},8,["modelValue"])]),_:1},8,["title"]),l(v,{title:o.t("479")},{default:a(()=>[l(k,{class:"",modelValue:s(y),"onUpdate:modelValue":e[10]||(e[10]=t=>C(y)?y.value=t:null)},{label:a(()=>[l(P,{"key-name":"S"},{default:a(()=>[f(p(o.t("215")),1)]),_:1})]),_:1},8,["modelValue"]),l(w,{class:"mt-1",type:"number",modelValue:x.value,"onUpdate:modelValue":e[11]||(e[11]=t=>x.value=t),disabled:s(y)},{prepend:a(()=>[m("span",fe,p(o.t("281"))+":",1)]),_:1},8,["modelValue","disabled"])]),_:1},8,["title"]),l(v,{title:o.t("836")},{default:a(()=>[l(le,{modelValue:R.value,"onUpdate:modelValue":e[12]||(e[12]=t=>R.value=t),max:1,min:0,step:.2,"thumb-label":""},null,8,["modelValue"])]),_:1},8,["title"])]),_:1})]),_:1})])]),_:1},8,["title","modelValue"]))}});export{Ue as default};
