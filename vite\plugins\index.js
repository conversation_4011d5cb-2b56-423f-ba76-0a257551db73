import vue from '@vitejs/plugin-vue'

import createAutoImport from './auto-import'
import createSvgIcon from './svg-icon'
import createCompression from './compression'
import createSetupExtend from './setup-extend'
import {lazyImport, VxeResolver} from 'vite-plugin-lazy-import'

export default function createVitePlugins(viteEnv, isBuild = false) {
    const vitePlugins = [vue()]
    vitePlugins.push(createAutoImport())
    vitePlugins.push(createSetupExtend())
    vitePlugins.push(createSvgIcon(isBuild))
    vitePlugins.push(
        lazyImport({
            resolvers: [
                VxeResolver({
                    libraryName: 'vxe-table'
                }),
                VxeResolver({
                    libraryName: 'vxe-pc-ui'
                })
            ]
        })
    )
    isBuild && vitePlugins.push(...createCompression(viteEnv))
    console.log('vitePlugins', vitePlugins)
    return vitePlugins
}
