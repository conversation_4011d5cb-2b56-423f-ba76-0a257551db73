///////////////////////////////////////////////////////////////////////////////
//版权所有（C）2002-2022，成都梦想凯德科技有限公司。
//本软件代码及其文档和相关资料归成都梦想凯德科技有限公司,应用包含本软件的程序必须包括以下版权声明
//此应用程序应与成都梦想凯德科技有限公司达成协议，使用本软件、其文档或相关材料
//https://www.mxdraw.com/
///////////////////////////////////////////////////////////////////////////////
import { ColorIndexType, McCmColor, McDb, McGePoint3d, McGeVector3d, McObject, McDbMText, MxCpp } from "mxcad";
import {valueEquals} from "element-plus";
export class MxDrawTableData {
  public m_aryColumn: { name: string, w: number, h: number }[][] = [[]];
  public m_allData: string[][] = [];

  public m_dColumnHeight: number = 2.5;
  public m_dRowHeight: number = 2.0;
  public m_titleHeight: number = 2.5;

  public m_dRowLineWdith: number = 0.0;
  public m_dColumnLineWidth: number = 0.05;

  public m_dRowTextHeight: number = 0.55;
  public m_dColumnTextHeight: number = 1.0;

  public m_dRowHeights: number[] = []

  public addColumn(index: number, sName: string, dW: number, dH: number) {
    if(!this.m_aryColumn[index]){
      this.m_aryColumn[index] = []
    }
    this.m_aryColumn[index].push({ name: sName, w: dW, h: dH });
  }

  public addRow(ary: string[], rowHeight: number = 0) {
    if(rowHeight <= 0){
      rowHeight = this.m_dRowHeight
    }
    this.m_dRowHeights.push(rowHeight)
    this.m_allData.push(ary);
  }

  public setRowTextHeight(h: number) {
    this.m_dRowTextHeight = h
  }

  public setRowTitleHeight(h: number) {
    this.m_dColumnHeight = h
  }
};

export class MxDrawTable {
  public data: MxDrawTableData = new MxDrawTableData;
  public pt: McGePoint3d = new McGePoint3d;
  public dScale: number = 1.0;
  public mxcad: McObject = MxCpp.getCurrentMxCAD();

  public draw(pt: McGePoint3d, dScale: number): boolean {
    this.pt = pt;
    this.dScale = dScale;
    if (this.data.m_aryColumn.length == 0)
      return false;
    if (this.data.m_allData.length == 0)
      return false;
    this.DrawTableHead();

    this.DrawContent();
    return true;
  }
  private DrawTableHead() {
    let m_data = this.data;
    let m_instPt = this.pt.clone();
    let curPt = this.pt.clone();
    let vecTmp = new McGeVector3d(0, 1, 0).mult(this.Scale(this.data.m_dColumnHeight))
    console.log(vecTmp)
    let dLineWidth = this.Scale(this.data.m_dColumnLineWidth);

    let dAllW = 0.0;
    let dAllH = 0.0;
    let kXAxis = new McGeVector3d(1, 0, 0);
    for (let i = 0; i < m_data.m_aryColumn.length; i++) {
      let cols = m_data.m_aryColumn[i];
      dAllW = 0.0;
      for (let ii = 0; ii < cols.length; ii++) {
        let col = cols[ii];
        let colWidth = this.Scale(col.w)
        this.DrawLine(curPt, curPt.clone().subvec(vecTmp), dLineWidth);//左边
        this.DrawMCText(
          curPt.clone().addvec(kXAxis.clone().mult(colWidth * 0.5)).subvec(vecTmp.clone().mult(0.5)), 
          col.name, 
          this.Scale(col.h || m_data.m_dColumnTextHeight), 
          colWidth);
        curPt.addvec(kXAxis.clone().mult(colWidth));
        dAllW += colWidth;
      }
      dAllH += this.Scale(m_data.m_dColumnHeight);
      curPt.subvec(kXAxis.clone().mult(dAllW)).subvec(vecTmp)//重置左上角

      this.DrawLine(curPt, curPt.clone().addvec(kXAxis.clone().mult(dAllW)), dLineWidth);//下边
    }
    m_data.m_titleHeight = dAllH
    let right_top = m_instPt.clone().addvec(kXAxis.clone().mult(dAllW))
    let right_bottom = right_top.clone().subvec(new McGeVector3d(0, 1, 0).mult(dAllH))
    let left_bottom = m_instPt.clone().subvec(new McGeVector3d(0, 1, 0).mult(dAllH))
    this.DrawLine(right_top, right_bottom, dLineWidth);//右边
    this.DrawLine(m_instPt, right_top, dLineWidth);//上边
    // this.DrawLine(left_bottom, right_bottom, dLineWidth);//下边
    return true;
  }
  private DrawContent() {
  
    let m_data = this.data;
    // console.log("🚀 ~ MxDrawTable ~ DrawContent ~ m_data:", m_data)
    let m_instPt = this.pt;

    let dAllW = 0.0;
    let cols = m_data.m_aryColumn[m_data.m_aryColumn.length - 1];
    for (let ii = 0; ii < cols.length; ii++) {
      dAllW += this.Scale(cols[ii].w);
    }
    let dLineWidth = this.Scale(this.data.m_dRowLineWdith);

    let kXAxis = new McGeVector3d(1, 0, 0);
    let kYAxis = new McGeVector3d(0, 1, 0);
    let instPt = m_instPt.clone().subvec(kYAxis.clone().mult(this.Scale(m_data.m_titleHeight)));
    let lastTitleData = m_data.m_aryColumn[m_data.m_aryColumn.length-1]
    for (let i = 0; i < m_data.m_allData.length; i++) {
      let curPt = instPt.clone();
      let vecTmp = kYAxis.clone().mult(this.Scale(m_data.m_dRowHeights[i]));
      for (let j = 0; j < lastTitleData.length; j++) {
          let col = lastTitleData[j];
          this.DrawLine(curPt, curPt.clone().subvec(vecTmp), dLineWidth);//左边
          this.DrawMCText(curPt.clone().addvec(
            kXAxis.clone().mult(this.Scale(col.w * 0.5))).subvec(vecTmp.clone().mult(0.5)), 
            m_data.m_allData[i][j], 
            this.Scale(m_data.m_dRowTextHeight), 
            this.Scale(col.w));
          curPt.addvec(kXAxis.clone().mult(this.Scale(col.w)));
      }
      // 绘制最后的线
      this.DrawLine(curPt, curPt.clone().subvec(vecTmp), dLineWidth);
      this.DrawLine(instPt, instPt.clone().addvec(kXAxis.clone().mult(dAllW)), dLineWidth);
      this.DrawLine(instPt.clone().subvec(vecTmp), instPt.clone().addvec(kXAxis.clone().mult(dAllW)).subvec(vecTmp), dLineWidth);
      instPt.subvec(vecTmp);
    }
    return true;
  }

  private DrawLine(pt1: McGePoint3d, pt2: McGePoint3d, dLineWidth: number) {
    this.mxcad.drawLineWidth = dLineWidth;
    this.mxcad.drawColorIndex = ColorIndexType.kMagenta;
    this.mxcad.drawColor = new McCmColor(0, 0, 0);;
    this.mxcad.drawLine(pt1.x, pt1.y, pt2.x, pt2.y);
  }

  private DrawMCText(pt1: McGePoint3d, sTxt: string, dTextHeight: number, width: number) {
    this.mxcad.drawColorIndex = 0;
    this.mxcad.drawColor = new McCmColor(0,0,0);
    let mtextObjId = this.mxcad.drawMText(pt1.x, pt1.y, sTxt, dTextHeight, width, 0, McDb.AttachmentPoint.kMiddleCenter);
    // let ent = mtextObjId.getMcDbEntity();
    // if (ent instanceof McDbMText) {
    //   let mtxt = (ent as McDbMText);
    //   console.log(`${sTxt}，高度${mtxt.textHeight}, 内容:${mtxt.contents}`,mtxt.getBoundingBox())
    // }
  }

  private Scale(dL: number): number {
    return dL * this.dScale;
  }

  public setRowTextHeight(h: number){
    this.data.setRowTextHeight(h)
  }
};
