import{M as ee}from"./index-8X61wlK0.js";import{b as A,z as $,s as c,k as G,A as te,a as _,M as Q,c as X,B as ae,C as le,u as I,D as se,_ as oe}from"./index-D95UjFey.js";import{h as ne,i as ie,M as h,j as ce,g as re,y as ue}from"./mxcad-CfPpL1Bn.js";import{d as N,r as de,h as me,a0 as K,_ as fe,$ as a,a1 as B,m as e,Q as y,V as k,u as l,B as S,ab as Y,a3 as Z}from"./vue-DfH9C9Rx.js";import{v as be,E as q,j as W,i as H,J as pe,K as F,b as ke,C as _e}from"./vuetify-B_xYg4qv.js";import"./mxdraw-C_n_7lEs.js";import"./mapbox-gl-PN3pDJHq.js";import"./xlsx-Ck5MUZvf.js";var T=(r=>(r[r.HOLD=0]="HOLD",r[r.CONVERT_TO_BLOCK=1]="CONVERT_TO_BLOCK",r[r.DELETE=2]="DELETE",r))(T||{});const ye=()=>{const r=N(""),D=N([]);te(()=>{D.value=$()});const b=A(!1,"Mx_CreateBlocksDialog_is_specify_on_screen_get_base_point"),u=de({x:0,y:0,z:0}),d=async()=>{const m=new ne;m.clearLastInputPoint(),m.setMessage(c("461"));const f=await m.go();if(!f)return;const{x:C,y:p,z:x}=f;return u.x=G(C,3),u.y=G(p,3),u.z=G(x,3),f},O=A(!1,"Mx_CreateBlocksDialog_is_specify_on_screen_select_object"),n=A(2,"Mx_CreateBlocksDialog_select_Object_operation_type"),M=N(!0),i=N(0);let v;const g=async()=>{if(v=new ie,i.value=0,!await v.userSelect(c("376")))return;const m=v.count();if(i.value=m,m===0){M.value=!0;return}return M.value=!1,v},V=()=>{const f=h.getCurrentMxCAD().getDatabase().getBlockTable(),C=new ce;return C.name=r.value,f.add(C)},P=(m,f,C)=>{f.getMcDbBlockTableRecord()?.getAllEntityId().forEach(w=>{w.getMcDbEntity()?.erase()});let p=f.getMcDbBlockTableRecord();if(!p)return;m.forEach(w=>{const j=w.getMcDbEntity();if(!j)return;const z=j.clone();p&&p.appendAcDbEntity(z)}),p.origin=C;let x=new re;return x.blockTableRecordId=f,x.position=C,x},L=N("");return{blockName:r,blockNames:D,getBlockNames:$,basePoint:u,getMouseClickGetBasePoint:d,isSpecifyOnScreenGetBasePoint:b,isNoSelectObject:M,isSpecifyOnScreenSelectObject:O,selectObjectOperationType:n,selectObject:g,createBlock:V,loadBlock:P,explainText:L,selectCount:i}},ve={class:"px-3"},ge={class:"f-flex justify-center align-center mt-2"},Ce={class:"f-flex justify-center align-center mt-2"},Ve={class:"d-flex flex-column"},xe={key:0,class:"f-flex justify-center align-center mt-2"},Be={key:1},De=me({__name:"index",setup(r){const{isShow:D,showDialog:b}=ae;let u;const{blockName:d,blockNames:O,basePoint:n,getMouseClickGetBasePoint:M,isSpecifyOnScreenGetBasePoint:i,isNoSelectObject:v,isSpecifyOnScreenSelectObject:g,selectObjectOperationType:V,selectObject:P,selectCount:L,createBlock:m,loadBlock:f,explainText:C}=ye(),p=()=>{n.x=0,n.y=0,n.z=0,d.value="",u=void 0,v.value=!0,O.value=$()},x=async()=>{b(!1),await M(),b(!0)},w=async()=>{b(!1),u=await P(),b(!0)},z=[{name:"确定",fun:async()=>{const{open:o}=le();let t=!1;const s=O.value.includes(d.value);if(d.value==="")return I().error(c("图块名不能为空")+"!");if(s)try{await new Promise((E,R)=>{o({title:c("是否替换该图块"),text:c("已定义")+d.value+c("的")+c("图块")+"，"+c("是否替换")+"?",define:()=>{t=!0,E()},cancel:()=>{t=!1,R()},defineTitle:c("是"),cancelTitle:c("否")})})}catch{return}let U;if(t?U=h.getCurrentMxCAD().getDatabase().getBlockTable().get(d.value):U=m(),i.value&&(b(!1),await M()),g.value&&(b(!1),u=await P()),!u||v.value)return I().error(c("没有为块")+d.value+c("选择对象")+"!");const J=f(u,U,new ue(n.x,n.y,n.z));if(!J)return I().error(c("创建块失败")+"!");V.value!==T.HOLD&&(u.forEach(E=>{const R=E.getMcDbEntity();R&&R.erase()}),se()),V.value===T.CONVERT_TO_BLOCK&&h.getCurrentMxCAD().drawEntity(J),b(!1),p()},primary:!0},{name:"关闭",fun:()=>{b(!1),p()}}];return(o,t)=>(K(),fe(ee,{title:o.t("213"),onClickClose:p,modelValue:l(D),"onUpdate:modelValue":t[7]||(t[7]=s=>S(D)?D.value=s:null),"max-width":"400",footerBtnList:z},{default:a(()=>[B("div",ve,[e(be,{class:"mt-2",modelValue:l(d),"onUpdate:modelValue":t[0]||(t[0]=s=>S(d)?d.value=s:null),items:l(O)},{prepend:a(()=>[e(_,{"key-name":"N"},{default:a(()=>[y(k(o.t("209")),1)]),_:1})]),_:1},8,["modelValue","items"]),e(_e,{"align-stretch":""},{default:a(()=>[e(q,{cols:6,"align-self":"stretch"},{default:a(()=>[e(Q,{title:o.t("214"),class:"h-100"},{default:a(()=>[e(W,{modelValue:l(i),"onUpdate:modelValue":t[1]||(t[1]=s=>S(i)?i.value=s:null)},{label:a(()=>[e(_,{"key-name":"S"},{default:a(()=>[y(k(o.t("215")),1)]),_:1})]),_:1},8,["modelValue"]),B("div",ge,[e(X,{disabled:l(i),onClick:x},null,8,["disabled"]),e(_,{class:Y(l(i)?"text-disabled":""),"key-name":"S"},{default:a(()=>[y(k(o.t("216")),1)]),_:1},8,["class"])]),e(H,{class:"mt-3",modelValue:l(n).x,"onUpdate:modelValue":t[2]||(t[2]=s=>l(n).x=s),type:"number",disabled:l(i)},{prepend:a(()=>t[8]||(t[8]=[B("span",{class:""}," X:",-1)])),_:1},8,["modelValue","disabled"]),e(H,{class:"mt-1",modelValue:l(n).y,"onUpdate:modelValue":t[3]||(t[3]=s=>l(n).y=s),type:"number",disabled:l(i)},{prepend:a(()=>t[9]||(t[9]=[B("span",{class:""}," Y: ",-1)])),_:1},8,["modelValue","disabled"]),e(H,{class:"mt-1",modelValue:l(n).z,"onUpdate:modelValue":t[4]||(t[4]=s=>l(n).z=s),type:"number",disabled:l(i)},{prepend:a(()=>t[10]||(t[10]=[B("span",{class:""}," Z:",-1)])),_:1},8,["modelValue","disabled"])]),_:1},8,["title"])]),_:1}),e(q,{cols:6,"align-self":"stretch"},{default:a(()=>[e(Q,{title:o.t("208"),class:"h-100"},{default:a(()=>[e(W,{modelValue:l(g),"onUpdate:modelValue":t[5]||(t[5]=s=>S(g)?g.value=s:null)},{label:a(()=>[e(_,{class:"","key-name":"S"},{default:a(()=>[y(k(o.t("215")),1)]),_:1})]),_:1},8,["modelValue"]),B("div",Ce,[e(X,{disabled:l(g),onClick:w},null,8,["disabled"]),e(_,{class:Y(l(g)?"text-disabled":""),"key-name":"I"},{default:a(()=>[y(k(o.t("205")),1)]),_:1},8,["class"])]),e(pe,{column:"",class:"mt-2",modelValue:l(V),"onUpdate:modelValue":t[6]||(t[6]=s=>S(V)?V.value=s:null)},{default:a(()=>[B("div",Ve,[e(F,{value:l(T).HOLD,class:"mt-1"},{label:a(()=>[e(_,{class:"","key-name":"R"},{default:a(()=>[y(k(o.t("217")),1)]),_:1})]),_:1},8,["value"]),e(F,{value:l(T).CONVERT_TO_BLOCK,class:"mt-1"},{label:a(()=>[e(_,{class:"","key-name":"C"},{default:a(()=>[y(k(o.t("218")),1)]),_:1})]),_:1},8,["value"]),e(F,{value:l(T).DELETE,class:"mt-1"},{label:a(()=>[e(_,{class:"","key-name":"D"},{default:a(()=>[y(k(o.t("219")),1)]),_:1})]),_:1},8,["value"])])]),_:1},8,["modelValue"]),l(v)?(K(),Z("div",xe,[e(ke,{icon:"jinggao"}),e(_,{class:"","key-name":"I"},{default:a(()=>[y(k(o.t("220")),1)]),_:1})])):(K(),Z("div",Be,k(o.t("221")+":"+l(L)+o.t("207")),1))]),_:1},8,["title"])]),_:1})]),_:1})])]),_:1},8,["title","modelValue"]))}}),Pe=oe(De,[["__scopeId","data-v-d1b3eb1d"]]);export{Pe as default};
