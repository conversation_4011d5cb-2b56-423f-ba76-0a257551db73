import{M as D}from"./index-8X61wlK0.js";import{H as b,M as F,h as M}from"./mxcad-CfPpL1Bn.js";import{aO as U,aP as C}from"./index-D95UjFey.js";import{i as d}from"./vuetify-B_xYg4qv.js";import{h as P,d as u,a0 as _,_ as I,$ as l,a1 as r,V as n,m,Q as p,u as k,B as H}from"./vue-DfH9C9Rx.js";import"./mxdraw-C_n_7lEs.js";import"./mapbox-gl-PN3pDJHq.js";import"./xlsx-Ck5MUZvf.js";const N={class:"d-flex align-center justify-space-between my-2"},S={for:"exportTableInputId",class:"d-flex align-center"},W={style:{width:"60px"}},j={class:"d-flex"},G=P({__name:"index",setup(q){const{isShow:c,showDialog:f}=U,v=[{name:"确定",fun:async()=>{await g(),f(!1)},primary:!0},{name:"关闭",fun:()=>f(!1)}],y=u(4),x=u(4),w=u(50),h=u(20),g=async()=>{try{const t=new b,e=T(x.value,y.value);t.setFromArray(e),t.setStyle({rowHeight:h.value,colWidth:w.value}),requestAnimationFrame(async()=>{await V(t)})}catch(t){console.error("创建表格失败:",t)}},A=async t=>{const e=t.target;if(!e.files||e.files.length===0)return;const a=e.files[0];try{const o=await B(a),i=await C.readFromArrayBuffer(o),s=new b;s.setFromWorkbook(i),f(!1),requestAnimationFrame(async()=>{await V(s)})}catch(o){console.error("导入表格失败:",o)}finally{e.value=""}},T=(t,e)=>{const a=[];for(let o=0;o<t;o++){const i=[];for(let s=0;s<e;s++)i.push("");a.push(i)}return a},B=t=>new Promise((e,a)=>{const o=new FileReader;o.onload=()=>e(o.result),o.onerror=a,o.readAsArrayBuffer(t)}),V=async t=>{try{const e=F.getCurrentMxCAD(),a=new M;a.setMessage(`
指定表格插入点:`);const o=await a.go();if(!o)return;t.setPosition(o),e.drawEntity(t),e.updateDisplay(),console.log("表格插入成功!")}catch(e){console.error("插入表格失败:",e)}};return(t,e)=>(_(),I(D,{title:t.t("289"),"max-width":"800",modelValue:k(c),"onUpdate:modelValue":e[4]||(e[4]=a=>H(c)?c.value=a:null),footerBtnList:v},{default:l(()=>[r("div",N,[r("label",S,[r("div",W,n(t.t("933"))+":",1),r("input",{id:"exportTableInputId",class:"mb-1",accept:".xls,.xlsx,.xlsm,.csv,.xlsb",type:"file",onChange:A},null,32)]),r("div",j,[m(d,{modelValue:x.value,"onUpdate:modelValue":e[0]||(e[0]=a=>x.value=a),type:"number",class:"pa-0 ml-1",style:{"max-width":"80px"}},{prepend:l(()=>[p(n(t.t("643"))+":",1)]),_:1},8,["modelValue"]),m(d,{modelValue:y.value,"onUpdate:modelValue":e[1]||(e[1]=a=>y.value=a),type:"number",class:"pa-0",style:{"max-width":"80px"}},{prepend:l(()=>[p(n(t.t("644"))+":",1)]),_:1},8,["modelValue"]),m(d,{modelValue:w.value,"onUpdate:modelValue":e[2]||(e[2]=a=>w.value=a),type:"number",class:"pa-0 mr-4",style:{"max-width":"120px"},density:"compact","hide-details":""},{prepend:l(()=>[p(n(t.t("934"))+":",1)]),_:1},8,["modelValue"]),m(d,{modelValue:h.value,"onUpdate:modelValue":e[3]||(e[3]=a=>h.value=a),type:"number",class:"pa-0",style:{"max-width":"120px"},density:"compact","hide-details":""},{prepend:l(()=>[p(n(t.t("935"))+":",1)]),_:1},8,["modelValue"])])])]),_:1},8,["title","modelValue"]))}});export{G as default};
