import {MxFun} from "mxdraw";
import { Mc<PERSON>b,  MxCpp, McDbText,McGePoint3d, McCmColor} from 'mxcad'
function markingOfCementPoleProducts(data) {
  console.log(data,'DataList')
  let dataList=JSON.parse(data)
  let mxcad = MxCpp.App.getCurrentMxCAD();

  let blockTable =mxcad.getDatabase().getBlockTable()
  let aryId=blockTable.getAllRecordId()
  aryId.forEach(ids => {
      let blkRec=ids.getMcDbBlockTableRecord()
      blkRec.getAllEntityId().forEach((id,index) => {
              let ent =id.getMcDbEntity()
              const equId = ent.getxDataString('equipmentId')?.val
              const XYZ = ent.getxDataString('PointXY')?.val
              const legendtypekey=ent.getxDataString('legendtypekey')?.val
              let num=10
              if(legendtypekey=='TY_ZSBYQ'){
                num=15
              }
              dataList.forEach(item => {
                  if(equId==item.equipmentId){
                      const text=new McDbText()
                      text.widthFactor=1
                      text.horizontalMode=McDb.TextHorzMode.kTextCenter
                      text.verticalMode=McDb.TextVertMode.kTextBottom
                      text.textString=item.spec
                      text.position=text.alignmentPoint=new McGePoint3d(Number(XYZ.split(',')[0]),Number(XYZ.split(',')[1])+num)
                      text.trueColor=new McCmColor(255,0,255)
                      text.height=5
                      mxcad.drawEntity(text) 
                  }
              });
                      
      })
  });
}
export function init() {
    MxFun.addCommand("Marking_OfCementPole_Products", markingOfCementPoleProducts);
}
