import{Q as p,Z as f,V as c,_ as x}from"./index-CzBriCFR.js";import{M as _}from"./index-itnQ6avM.js";import{a1 as V,B as i,t as g,z as D,c as v,b as B}from"./vuetify-BqCp6y38.js";import{h,a3 as w,a4 as e,u as b,B as y,_ as C,a0 as o,m as t,Q as n,V as r}from"./vue-Cj9QYd7Z.js";import"./mxcad-DrgW2waE.js";import"./mxdraw-BQ5HhRCY.js";import"./handsontable-Ch5RdAT_.js";import"./mapbox-gl-DQAr7S0z.js";const M={class:"px-3"},k={class:"d-flex overflow-y-auto",style:{"max-height":"300px",height:"300px"}},z={class:"d-flex flex-column text-center mx-2"},I=h({__name:"index",setup(N){const{isShow:a,showDialog:d}=p(!1,"showDWGCutDialog"),u=[{name:"开始转换",fun:()=>{},primary:!0},{name:"关闭",fun:()=>d(!1)}];return(l,s)=>(C(),w(_,{title:l.t("528"),modelValue:b(a),"onUpdate:modelValue":s[0]||(s[0]=m=>y(a)?a.value=m:null),"max-width":"600",footerBtnList:u},{default:e(()=>[o("div",M,[t(V,{class:"mt-1",items:[]},{prepend:e(()=>[n(r(l.t("529")),1)]),append:e(()=>[t(f,null,{default:e(()=>[n(r(l.t("530")),1)]),_:1})]),_:1}),t(D,{class:"mt-1"},{default:e(()=>[t(i,{cols:"5"},{default:e(()=>s[1]||(s[1]=[o("span",null,"正在转换: 2021.5.11套房图纸",-1)])),_:1}),t(i,{cols:"7"},{default:e(()=>[t(g,{"model-value":"30",color:"#28D238","bg-color":"#D9D9D9",height:"24",class:"linear-border"})]),_:1})]),_:1}),t(c,{title:"剪切区域"},{default:e(()=>[o("div",k,[s[3]||(s[3]=o("div",{class:"d-flex flex-column text-center mx-2"},[o("div",{class:"cut-box bg-black"}),o("span",null,"2021.5.11套房图纸-1")],-1)),o("div",z,[t(v,{size:"120px",variant:"text",color:"#6D6B6B"},{default:e(()=>[t(B,{icon:"class:iconfont plus",size:"100px"})]),_:1}),s[2]||(s[2]=o("span",null,"添加",-1))])])]),_:1})])]),_:1},8,["title","modelValue"]))}}),T=x(I,[["__scopeId","data-v-8873d8cb"]]);export{T as default};
