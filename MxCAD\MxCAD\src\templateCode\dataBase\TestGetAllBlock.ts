import { MxCpp } from "mxcad";

function MxTest_GetAllBlock() {
    // 获取当前mxcad对象
    let mxcad = MxCpp.App.getCurrentMxCAD();
    // 获取数据库块表
    let blockTable = mxcad.getDatabase().getBlockTable();
    // 获取所有块表记录的id数组
    let aryId = blockTable.getAllRecordId();
    // 遍历所有块表记录
    aryId.forEach((id) => {
        let blkRec = id.getMcDbBlockTableRecord();
        if (blkRec === null) return;
        console.log(blkRec);
        console.log("blkRec.name:" + blkRec.name);
        console.log("blkRec.origin:" + blkRec.origin);
    });
};

// 调用获取所有图块的方法
MxTest_GetAllBlock();
