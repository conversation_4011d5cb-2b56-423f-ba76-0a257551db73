import{w as U,e as ql,o as Ze,F as ae,r as at,c as m,a as Te,t as ln,b as An,i as mc,C as gc,s as Y,u as tt,g as hc,d as H,p as we,f as ge,h as yc,j as ns,k as on,l as Me,m as s,n as F,q as Je,v as Xl,x as as,y as bc,z as Qe,A as ke,B as wn,D,T as Sc,E as Zl,G as Et,H as Ie,I as bt,J as ls,K as st,L as kc,M as Cc,N as xc,O as wc,P as Vc,Q as Ft,R as Pc,S as Ic,U as Xo,V as pc,W as _c}from"./vue-DfH9C9Rx.js";function nt(e,t){let n;function a(){n=ql(),n.run(()=>t.length?t(()=>{n?.stop(),a()}):t())}U(e,l=>{l&&!n?a():l||(n?.stop(),n=void 0)},{immediate:!0}),Ze(()=>{n?.stop()})}const Se=typeof window<"u",Jl=Se&&"IntersectionObserver"in window,Ac=Se&&("ontouchstart"in window||window.navigator.maxTouchPoints>0),Zo=Se&&"EyeDropper"in window;function Jo(e,t,n){Lc(e,t),t.set(e,n)}function Lc(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")}function Tc(e,t,n){return e.set(os(e,t),n),n}function zt(e,t){return e.get(os(e,t))}function os(e,t,n){if(typeof e=="function"?e===t:e.has(t))return arguments.length<3?t:n;throw new TypeError("Private element is not present on this object")}function is(e,t,n){const a=t.length-1;if(a<0)return e===void 0?n:e;for(let l=0;l<a;l++){if(e==null)return n;e=e[t[l]]}return e==null||e[t[a]]===void 0?n:e[t[a]]}function wt(e,t){if(e===t)return!0;if(e instanceof Date&&t instanceof Date&&e.getTime()!==t.getTime()||e!==Object(e)||t!==Object(t))return!1;const n=Object.keys(e);return n.length!==Object.keys(t).length?!1:n.every(a=>wt(e[a],t[a]))}function Xt(e,t,n){return e==null||!t||typeof t!="string"?n:e[t]!==void 0?e[t]:(t=t.replace(/\[(\w+)\]/g,".$1"),t=t.replace(/^\./,""),is(e,t.split("."),n))}function Re(e,t,n){if(t===!0)return e===void 0?n:e;if(t==null||typeof t=="boolean")return n;if(e!==Object(e)){if(typeof t!="function")return n;const l=t(e,n);return typeof l>"u"?n:l}if(typeof t=="string")return Xt(e,t,n);if(Array.isArray(t))return is(e,t,n);if(typeof t!="function")return n;const a=t(e,n);return typeof a>"u"?n:a}function Ct(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return Array.from({length:e},(n,a)=>t+a)}function K(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"px";if(!(e==null||e===""))return isNaN(+e)?String(e):isFinite(+e)?`${Number(e)}${t}`:void 0}function ss(e){return e!==null&&typeof e=="object"&&!Array.isArray(e)}function Qo(e){let t;return e!==null&&typeof e=="object"&&((t=Object.getPrototypeOf(e))===Object.prototype||t===null)}function Ql(e){if(e&&"$el"in e){const t=e.$el;return t?.nodeType===Node.TEXT_NODE?t.nextElementSibling:t}return e}const ei=Object.freeze({enter:13,tab:9,delete:46,esc:27,space:32,up:38,down:40,left:37,right:39,end:35,home:36,del:46,backspace:8,insert:45,pageup:33,pagedown:34,shift:16}),ml=Object.freeze({enter:"Enter",tab:"Tab",delete:"Delete",esc:"Escape",space:"Space",up:"ArrowUp",down:"ArrowDown",left:"ArrowLeft",right:"ArrowRight",end:"End",home:"Home",del:"Delete",backspace:"Backspace",insert:"Insert",pageup:"PageUp",pagedown:"PageDown",shift:"Shift"});function rs(e){return Object.keys(e)}function jt(e,t){return t.every(n=>e.hasOwnProperty(n))}function eo(e,t){const n={},a=new Set(Object.keys(e));for(const l of t)a.has(l)&&(n[l]=e[l]);return n}function gl(e,t,n){const a=Object.create(null),l=Object.create(null);for(const o in e)t.some(i=>i instanceof RegExp?i.test(o):i===o)&&!n?.some(i=>i===o)?a[o]=e[o]:l[o]=e[o];return[a,l]}function Ee(e,t){const n={...e};return t.forEach(a=>delete n[a]),n}function Va(e,t){const n={};return t.forEach(a=>n[a]=e[a]),n}const us=/^on[^a-z]/,Pa=e=>us.test(e),Bc=["onAfterscriptexecute","onAnimationcancel","onAnimationend","onAnimationiteration","onAnimationstart","onAuxclick","onBeforeinput","onBeforescriptexecute","onChange","onClick","onCompositionend","onCompositionstart","onCompositionupdate","onContextmenu","onCopy","onCut","onDblclick","onFocusin","onFocusout","onFullscreenchange","onFullscreenerror","onGesturechange","onGestureend","onGesturestart","onGotpointercapture","onInput","onKeydown","onKeypress","onKeyup","onLostpointercapture","onMousedown","onMousemove","onMouseout","onMouseover","onMouseup","onMousewheel","onPaste","onPointercancel","onPointerdown","onPointerenter","onPointerleave","onPointermove","onPointerout","onPointerover","onPointerup","onReset","onSelect","onSubmit","onTouchcancel","onTouchend","onTouchmove","onTouchstart","onTransitioncancel","onTransitionend","onTransitionrun","onTransitionstart","onWheel"],Dc=["ArrowUp","ArrowDown","ArrowRight","ArrowLeft","Enter","Escape","Tab"," "];function Mc(e){return e.isComposing&&Dc.includes(e.key)}function $t(e){const[t,n]=gl(e,[us]),a=Ee(t,Bc),[l,o]=gl(n,["class","style","id",/^data-/]);return Object.assign(l,t),Object.assign(o,a),[l,o]}function Pe(e){return e==null?[]:Array.isArray(e)?e:[e]}function Ec(e,t){let n=0;const a=function(){for(var l=arguments.length,o=new Array(l),i=0;i<l;i++)o[i]=arguments[i];clearTimeout(n),n=setTimeout(()=>e(...o),tt(t))};return a.clear=()=>{clearTimeout(n)},a.immediate=e,a}function Be(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:1;return Math.max(t,Math.min(n,e))}function ti(e){const t=e.toString().trim();return t.includes(".")?t.length-t.indexOf(".")-1:0}function ni(e,t){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:"0";return e+n.repeat(Math.max(0,t-e.length))}function ai(e,t){return(arguments.length>2&&arguments[2]!==void 0?arguments[2]:"0").repeat(Math.max(0,t-e.length))+e}function Fc(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1;const n=[];let a=0;for(;a<e.length;)n.push(e.substr(a,t)),a+=t;return n}function li(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1e3;if(e<t)return`${e} B`;const n=t===1024?["Ki","Mi","Gi"]:["k","M","G"];let a=-1;for(;Math.abs(e)>=t&&a<n.length-1;)e/=t,++a;return`${e.toFixed(1)} ${n[a]}B`}function qe(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=arguments.length>2?arguments[2]:void 0;const a={};for(const l in e)a[l]=e[l];for(const l in t){const o=e[l],i=t[l];if(Qo(o)&&Qo(i)){a[l]=qe(o,i,n);continue}if(n&&Array.isArray(o)&&Array.isArray(i)){a[l]=n(o,i);continue}a[l]=i}return a}function cs(e){return e.map(t=>t.type===ae?cs(t.children):t).flat()}function Gt(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"";if(Gt.cache.has(e))return Gt.cache.get(e);const t=e.replace(/[^a-z]/gi,"-").replace(/\B([A-Z])/g,"-$1").toLowerCase();return Gt.cache.set(e,t),t}Gt.cache=new Map;function Cn(e,t){if(!t||typeof t!="object")return[];if(Array.isArray(t))return t.map(n=>Cn(e,n)).flat(1);if(t.suspense)return Cn(e,t.ssContent);if(Array.isArray(t.children))return t.children.map(n=>Cn(e,n)).flat(1);if(t.component){if(Object.getOwnPropertySymbols(t.component.provides).includes(e))return[t.component];if(t.component.subTree)return Cn(e,t.component.subTree).flat(1)}return[]}var sa=new WeakMap,yn=new WeakMap;class Oc{constructor(t){Jo(this,sa,[]),Jo(this,yn,0),this.size=t}push(t){zt(sa,this)[zt(yn,this)]=t,Tc(yn,this,(zt(yn,this)+1)%this.size)}values(){return zt(sa,this).slice(zt(yn,this)).concat(zt(sa,this).slice(0,zt(yn,this)))}}function $c(e){return"touches"in e?{clientX:e.touches[0].clientX,clientY:e.touches[0].clientY}:{clientX:e.clientX,clientY:e.clientY}}function to(e){const t=at({}),n=m(e);return Te(()=>{for(const a in n.value)t[a]=n.value[a]},{flush:"sync"}),ln(t)}function ga(e,t){return e.includes(t)}function ds(e){return e[2].toLowerCase()+e.slice(3)}const We=()=>[Function,Array];function oi(e,t){return t="on"+An(t),!!(e[t]||e[`${t}Once`]||e[`${t}Capture`]||e[`${t}OnceCapture`]||e[`${t}CaptureOnce`])}function no(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),a=1;a<t;a++)n[a-1]=arguments[a];if(Array.isArray(e))for(const l of e)l(...n);else typeof e=="function"&&e(...n)}function Rn(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;const n=["button","[href]",'input:not([type="hidden"])',"select","textarea","[tabindex]"].map(a=>`${a}${t?':not([tabindex="-1"])':""}:not([disabled])`).join(", ");return[...e.querySelectorAll(n)]}function vs(e,t,n){let a,l=e.indexOf(document.activeElement);const o=t==="next"?1:-1;do l+=o,a=e[l];while((!a||a.offsetParent==null||!(n?.(a)??!0))&&l<e.length&&l>=0);return a}function Ut(e,t){const n=Rn(e);if(!t)(e===document.activeElement||!e.contains(document.activeElement))&&n[0]?.focus();else if(t==="first")n[0]?.focus();else if(t==="last")n.at(-1)?.focus();else if(typeof t=="number")n[t]?.focus();else{const a=vs(n,t);a?a.focus():Ut(e,t==="next"?"first":"last")}}function ra(e){return e==null||typeof e=="string"&&e.trim()===""}function fs(){}function Vn(e,t){if(!(Se&&typeof CSS<"u"&&typeof CSS.supports<"u"&&CSS.supports(`selector(${t})`)))return null;try{return!!e&&e.matches(t)}catch{return null}}function Ia(e){return e.some(t=>mc(t)?t.type===gc?!1:t.type!==ae||Ia(t.children):!0)?e:null}function Rc(e,t){if(!Se||e===0)return t(),()=>{};const n=window.setTimeout(t,e);return()=>window.clearTimeout(n)}function Nc(e,t){const n=e.clientX,a=e.clientY,l=t.getBoundingClientRect(),o=l.left,i=l.top,r=l.right,u=l.bottom;return n>=o&&n<=r&&a>=i&&a<=u}function ha(){const e=Y(),t=n=>{e.value=n};return Object.defineProperty(t,"value",{enumerable:!0,get:()=>e.value,set:n=>e.value=n}),Object.defineProperty(t,"el",{enumerable:!0,get:()=>Ql(e.value)}),t}function ya(e){const t=e.key.length===1,n=!e.ctrlKey&&!e.metaKey&&!e.altKey;return t&&n}const ms=["top","bottom"],Hc=["start","end","left","right"];function hl(e,t){let[n,a]=e.split(" ");return a||(a=ga(ms,n)?"start":ga(Hc,n)?"top":"center"),{side:yl(n,t),align:yl(a,t)}}function yl(e,t){return e==="start"?t?"right":"left":e==="end"?t?"left":"right":e}function ll(e){return{side:{center:"center",top:"bottom",bottom:"top",left:"right",right:"left"}[e.side],align:e.align}}function ol(e){return{side:e.side,align:{center:"center",top:"bottom",bottom:"top",left:"right",right:"left"}[e.align]}}function ii(e){return{side:e.align,align:e.side}}function si(e){return ga(ms,e.side)?"y":"x"}class Kt{constructor(t){let{x:n,y:a,width:l,height:o}=t;this.x=n,this.y=a,this.width=l,this.height=o}get top(){return this.y}get bottom(){return this.y+this.height}get left(){return this.x}get right(){return this.x+this.width}}function ri(e,t){return{x:{before:Math.max(0,t.left-e.left),after:Math.max(0,e.right-t.right)},y:{before:Math.max(0,t.top-e.top),after:Math.max(0,e.bottom-t.bottom)}}}function gs(e){return Array.isArray(e)?new Kt({x:e[0],y:e[1],width:0,height:0}):e.getBoundingClientRect()}function ao(e){const t=e.getBoundingClientRect(),n=getComputedStyle(e),a=n.transform;if(a){let l,o,i,r,u;if(a.startsWith("matrix3d("))l=a.slice(9,-1).split(/, /),o=+l[0],i=+l[5],r=+l[12],u=+l[13];else if(a.startsWith("matrix("))l=a.slice(7,-1).split(/, /),o=+l[0],i=+l[3],r=+l[4],u=+l[5];else return new Kt(t);const d=n.transformOrigin,c=t.x-r-(1-o)*parseFloat(d),f=t.y-u-(1-i)*parseFloat(d.slice(d.indexOf(" ")+1)),v=o?t.width/o:e.offsetWidth+1,g=i?t.height/i:e.offsetHeight+1;return new Kt({x:c,y:f,width:v,height:g})}else return new Kt(t)}function Yt(e,t,n){if(typeof e.animate>"u")return{finished:Promise.resolve()};let a;try{a=e.animate(t,n)}catch{return{finished:Promise.resolve()}}return typeof a.finished>"u"&&(a.finished=new Promise(l=>{a.onfinish=()=>{l(a)}})),a}const fa=new WeakMap;function zc(e,t){Object.keys(t).forEach(n=>{if(Pa(n)){const a=ds(n),l=fa.get(e);if(t[n]==null)l?.forEach(o=>{const[i,r]=o;i===a&&(e.removeEventListener(a,r),l.delete(o))});else if(!l||![...l].some(o=>o[0]===a&&o[1]===t[n])){e.addEventListener(a,t[n]);const o=l||new Set;o.add([a,t[n]]),fa.has(e)||fa.set(e,o)}}else t[n]==null?e.removeAttribute(n):e.setAttribute(n,t[n])})}function Wc(e,t){Object.keys(t).forEach(n=>{if(Pa(n)){const a=ds(n),l=fa.get(e);l?.forEach(o=>{const[i,r]=o;i===a&&(e.removeEventListener(a,r),l.delete(o))})}else e.removeAttribute(n)})}const bn=2.4,ui=.2126729,ci=.7151522,di=.072175,jc=.55,Yc=.58,Gc=.57,Uc=.62,ua=.03,vi=1.45,Kc=5e-4,qc=1.25,Xc=1.25,fi=.078,mi=12.82051282051282,ca=.06,gi=.001;function hi(e,t){const n=(e.r/255)**bn,a=(e.g/255)**bn,l=(e.b/255)**bn,o=(t.r/255)**bn,i=(t.g/255)**bn,r=(t.b/255)**bn;let u=n*ui+a*ci+l*di,d=o*ui+i*ci+r*di;if(u<=ua&&(u+=(ua-u)**vi),d<=ua&&(d+=(ua-d)**vi),Math.abs(d-u)<Kc)return 0;let c;if(d>u){const f=(d**jc-u**Yc)*qc;c=f<gi?0:f<fi?f-f*mi*ca:f-ca}else{const f=(d**Uc-u**Gc)*Xc;c=f>-gi?0:f>-fi?f-f*mi*ca:f+ca}return c*100}function Zc(e,t){t=Array.isArray(t)?t.slice(0,-1).map(n=>`'${n}'`).join(", ")+` or '${t.at(-1)}'`:`'${t}'`}const ba=.20689655172413793,Jc=e=>e>ba**3?Math.cbrt(e):e/(3*ba**2)+4/29,Qc=e=>e>ba?e**3:3*ba**2*(e-4/29);function hs(e){const t=Jc,n=t(e[1]);return[116*n-16,500*(t(e[0]/.95047)-n),200*(n-t(e[2]/1.08883))]}function ys(e){const t=Qc,n=(e[0]+16)/116;return[t(n+e[1]/500)*.95047,t(n),t(n-e[2]/200)*1.08883]}const ed=[[3.2406,-1.5372,-.4986],[-.9689,1.8758,.0415],[.0557,-.204,1.057]],td=e=>e<=.0031308?e*12.92:1.055*e**(1/2.4)-.055,nd=[[.4124,.3576,.1805],[.2126,.7152,.0722],[.0193,.1192,.9505]],ad=e=>e<=.04045?e/12.92:((e+.055)/1.055)**2.4;function bs(e){const t=Array(3),n=td,a=ed;for(let l=0;l<3;++l)t[l]=Math.round(Be(n(a[l][0]*e[0]+a[l][1]*e[1]+a[l][2]*e[2]))*255);return{r:t[0],g:t[1],b:t[2]}}function lo(e){let{r:t,g:n,b:a}=e;const l=[0,0,0],o=ad,i=nd;t=o(t/255),n=o(n/255),a=o(a/255);for(let r=0;r<3;++r)l[r]=i[r][0]*t+i[r][1]*n+i[r][2]*a;return l}function bl(e){return!!e&&/^(#|var\(--|(rgb|hsl)a?\()/.test(e)}function ld(e){return bl(e)&&!/^((rgb|hsl)a?\()?var\(--/.test(e)}const yi=/^(?<fn>(?:rgb|hsl)a?)\((?<values>.+)\)/,od={rgb:(e,t,n,a)=>({r:e,g:t,b:n,a}),rgba:(e,t,n,a)=>({r:e,g:t,b:n,a}),hsl:(e,t,n,a)=>bi({h:e,s:t,l:n,a}),hsla:(e,t,n,a)=>bi({h:e,s:t,l:n,a}),hsv:(e,t,n,a)=>pt({h:e,s:t,v:n,a}),hsva:(e,t,n,a)=>pt({h:e,s:t,v:n,a})};function vt(e){if(typeof e=="number")return{r:(e&16711680)>>16,g:(e&65280)>>8,b:e&255};if(typeof e=="string"&&yi.test(e)){const{groups:t}=e.match(yi),{fn:n,values:a}=t,l=a.split(/,\s*/).map(o=>o.endsWith("%")&&["hsl","hsla","hsv","hsva"].includes(n)?parseFloat(o)/100:parseFloat(o));return od[n](...l)}else if(typeof e=="string"){let t=e.startsWith("#")?e.slice(1):e;return[3,4].includes(t.length)?t=t.split("").map(n=>n+n).join(""):[6,8].includes(t.length),ws(t)}else if(typeof e=="object"){if(jt(e,["r","g","b"]))return e;if(jt(e,["h","s","l"]))return pt(oo(e));if(jt(e,["h","s","v"]))return pt(e)}throw new TypeError(`Invalid color: ${e==null?e:String(e)||e.constructor.name}
Expected #hex, #hexa, rgb(), rgba(), hsl(), hsla(), object or number`)}function pt(e){const{h:t,s:n,v:a,a:l}=e,o=r=>{const u=(r+t/60)%6;return a-a*n*Math.max(Math.min(u,4-u,1),0)},i=[o(5),o(3),o(1)].map(r=>Math.round(r*255));return{r:i[0],g:i[1],b:i[2],a:l}}function bi(e){return pt(oo(e))}function pa(e){if(!e)return{h:0,s:1,v:1,a:1};const t=e.r/255,n=e.g/255,a=e.b/255,l=Math.max(t,n,a),o=Math.min(t,n,a);let i=0;l!==o&&(l===t?i=60*(0+(n-a)/(l-o)):l===n?i=60*(2+(a-t)/(l-o)):l===a&&(i=60*(4+(t-n)/(l-o)))),i<0&&(i=i+360);const r=l===0?0:(l-o)/l,u=[i,r,l];return{h:u[0],s:u[1],v:u[2],a:e.a}}function Ss(e){const{h:t,s:n,v:a,a:l}=e,o=a-a*n/2,i=o===1||o===0?0:(a-o)/Math.min(o,1-o);return{h:t,s:i,l:o,a:l}}function oo(e){const{h:t,s:n,l:a,a:l}=e,o=a+n*Math.min(a,1-a),i=o===0?0:2-2*a/o;return{h:t,s:i,v:o,a:l}}function ks(e){let{r:t,g:n,b:a,a:l}=e;return l===void 0?`rgb(${t}, ${n}, ${a})`:`rgba(${t}, ${n}, ${a}, ${l})`}function Cs(e){return ks(pt(e))}function da(e){const t=Math.round(e).toString(16);return("00".substr(0,2-t.length)+t).toUpperCase()}function xs(e){let{r:t,g:n,b:a,a:l}=e;return`#${[da(t),da(n),da(a),l!==void 0?da(Math.round(l*255)):""].join("")}`}function ws(e){e=id(e);let[t,n,a,l]=Fc(e,2).map(o=>parseInt(o,16));return l=l===void 0?l:l/255,{r:t,g:n,b:a,a:l}}function Vs(e){const t=ws(e);return pa(t)}function Ps(e){return xs(pt(e))}function id(e){return e.startsWith("#")&&(e=e.slice(1)),e=e.replace(/([^0-9a-f])/gi,"F"),(e.length===3||e.length===4)&&(e=e.split("").map(t=>t+t).join("")),e.length!==6&&(e=ni(ni(e,6),8,"F")),e}function sd(e,t){const n=hs(lo(e));return n[0]=n[0]+t*10,bs(ys(n))}function rd(e,t){const n=hs(lo(e));return n[0]=n[0]-t*10,bs(ys(n))}function Sl(e){const t=vt(e);return lo(t)[1]}function ud(e,t){const n=Sl(e),a=Sl(t),l=Math.max(n,a),o=Math.min(n,a);return(l+.05)/(o+.05)}function Is(e){const t=Math.abs(hi(vt(0),vt(e)));return Math.abs(hi(vt(16777215),vt(e)))>Math.min(t,50)?"#fff":"#000"}function L(e,t){return n=>Object.keys(e).reduce((a,l)=>{const i=typeof e[l]=="object"&&e[l]!=null&&!Array.isArray(e[l])?e[l]:{type:e[l]};return n&&l in n?a[l]={...i,default:n[l]}:a[l]=i,t&&!a[l].source&&(a[l].source=t),a},{})}const X=L({class:[String,Array,Object],style:{type:[String,Array,Object],default:null}},"component");function _e(e,t){const n=hc();if(!n)throw new Error(`[Vuetify] ${e} must be called from inside a setup function`);return n}function Vt(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"composables";const t=_e(e).type;return Gt(t?.aliasName||t?.name)}let ps=0,ma=new WeakMap;function je(){const e=_e("getUid");if(ma.has(e))return ma.get(e);{const t=ps++;return ma.set(e,t),t}}je.reset=()=>{ps=0,ma=new WeakMap};function cd(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:_e("injectSelf");const{provides:n}=t;if(n&&e in n)return n[e]}const Pn=Symbol.for("vuetify:defaults");function dd(e){return H(e)}function io(){const e=ge(Pn);if(!e)throw new Error("[Vuetify] Could not find defaults instance");return e}function pe(e,t){const n=io(),a=H(e),l=m(()=>{if(tt(t?.disabled))return n.value;const i=tt(t?.scoped),r=tt(t?.reset),u=tt(t?.root);if(a.value==null&&!(i||r||u))return n.value;let d=qe(a.value,{prev:n.value});if(i)return d;if(r||u){const c=Number(r||1/0);for(let f=0;f<=c&&!(!d||!("prev"in d));f++)d=d.prev;return d&&typeof u=="string"&&u in d&&(d=qe(qe(d,{prev:d}),d[u])),d}return d.prev?qe(d.prev,d):d});return we(Pn,l),l}function vd(e,t){return typeof e.props?.[t]<"u"||typeof e.props?.[Gt(t)]<"u"}function _s(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0,n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:io();const a=_e("useDefaults");if(t=t??a.type.name??a.type.__name,!t)throw new Error("[Vuetify] Could not determine component name");const l=m(()=>n.value?.[e._as??t]),o=new Proxy(e,{get(u,d){const c=Reflect.get(u,d);return d==="class"||d==="style"?[l.value?.[d],c].filter(f=>f!=null):typeof d=="string"&&!vd(a.vnode,d)?l.value?.[d]!==void 0?l.value?.[d]:n.value?.global?.[d]!==void 0?n.value?.global?.[d]:c:c}}),i=Y();Te(()=>{if(l.value){const u=Object.entries(l.value).filter(d=>{let[c]=d;return c.startsWith(c[0].toUpperCase())});i.value=u.length?Object.fromEntries(u):void 0}else i.value=void 0});function r(){const u=cd(Pn,a);we(Pn,m(()=>i.value?qe(u?.value??{},i.value):u?.value))}return{props:o,provideSubDefaults:r}}function fd(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;const{props:n,provideSubDefaults:a}=_s(e,t);return a(),n}function rt(e){if(e._setup=e._setup??e.setup,!e.name)return e;if(e._setup){e.props=L(e.props??{},e.name)();const t=Object.keys(e.props).filter(n=>n!=="class"&&n!=="style");e.filterProps=function(a){return eo(a,t)},e.props._as=String,e.setup=function(a,l){const o=io();if(!o.value)return e._setup(a,l);const{props:i,provideSubDefaults:r}=_s(a,a._as??e.name,o),u=e._setup(i,l);return r(),u}}return e}function M(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;return t=>(e?rt:yc)(t)}function md(e,t){return t.props=e,t}function Pt(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"div",n=arguments.length>2?arguments[2]:void 0;return M()({name:n??An(ns(e.replace(/__/g,"-"))),props:{tag:{type:String,default:t},...X()},setup(a,l){let{slots:o}=l;return()=>on(a.tag,{class:[e,a.class],style:a.style},o.default?.())}})}function As(e){if(typeof e.getRootNode!="function"){for(;e.parentNode;)e=e.parentNode;return e!==document?null:document}const t=e.getRootNode();return t!==document&&t.getRootNode({composed:!0})!==document?null:t}const Nn="cubic-bezier(0.4, 0, 0.2, 1)",gd="cubic-bezier(0.0, 0, 0.2, 1)",hd="cubic-bezier(0.4, 0, 1, 1)";function Si(e,t,n){return Object.keys(e).filter(a=>Pa(a)&&a.endsWith(t)).reduce((a,l)=>(a[l.slice(0,-t.length)]=o=>e[l](o,n(o)),a),{})}function so(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;for(;e;){if(t?yd(e):ro(e))return e;e=e.parentElement}return document.scrollingElement}function Sa(e,t){const n=[];if(t&&e&&!t.contains(e))return n;for(;e&&(ro(e)&&n.push(e),e!==t);)e=e.parentElement;return n}function ro(e){if(!e||e.nodeType!==Node.ELEMENT_NODE)return!1;const t=window.getComputedStyle(e);return t.overflowY==="scroll"||t.overflowY==="auto"&&e.scrollHeight>e.clientHeight}function yd(e){if(!e||e.nodeType!==Node.ELEMENT_NODE)return!1;const t=window.getComputedStyle(e);return["scroll","auto"].includes(t.overflowY)}function bd(e){for(;e;){if(window.getComputedStyle(e).position==="fixed")return!0;e=e.offsetParent}return!1}function O(e){const t=_e("useRender");t.render=e}function Q(e,t,n){let a=arguments.length>3&&arguments[3]!==void 0?arguments[3]:f=>f,l=arguments.length>4&&arguments[4]!==void 0?arguments[4]:f=>f;const o=_e("useProxiedModel"),i=H(e[t]!==void 0?e[t]:n),r=Gt(t),d=r!==t?m(()=>(e[t],!!((o.vnode.props?.hasOwnProperty(t)||o.vnode.props?.hasOwnProperty(r))&&(o.vnode.props?.hasOwnProperty(`onUpdate:${t}`)||o.vnode.props?.hasOwnProperty(`onUpdate:${r}`))))):m(()=>(e[t],!!(o.vnode.props?.hasOwnProperty(t)&&o.vnode.props?.hasOwnProperty(`onUpdate:${t}`))));nt(()=>!d.value,()=>{U(()=>e[t],f=>{i.value=f})});const c=m({get(){const f=e[t];return a(d.value?f:i.value)},set(f){const v=l(f),g=Me(d.value?e[t]:i.value);g===v||a(g)===f||(i.value=v,o?.emit(`update:${t}`,v))}});return Object.defineProperty(c,"externalValue",{get:()=>d.value?e[t]:i.value}),c}const Sd={badge:"Badge",open:"Open",close:"Close",dismiss:"Dismiss",confirmEdit:{ok:"OK",cancel:"Cancel"},dataIterator:{noResultsText:"No matching records found",loadingText:"Loading items..."},dataTable:{itemsPerPageText:"Rows per page:",ariaLabel:{sortDescending:"Sorted descending.",sortAscending:"Sorted ascending.",sortNone:"Not sorted.",activateNone:"Activate to remove sorting.",activateDescending:"Activate to sort descending.",activateAscending:"Activate to sort ascending."},sortBy:"Sort by"},dataFooter:{itemsPerPageText:"Items per page:",itemsPerPageAll:"All",nextPage:"Next page",prevPage:"Previous page",firstPage:"First page",lastPage:"Last page",pageText:"{0}-{1} of {2}"},dateRangeInput:{divider:"to"},datePicker:{itemsSelected:"{0} selected",range:{title:"Select dates",header:"Enter dates"},title:"Select date",header:"Enter date",input:{placeholder:"Enter date"}},noDataText:"No data available",carousel:{prev:"Previous visual",next:"Next visual",ariaLabel:{delimiter:"Carousel slide {0} of {1}"}},calendar:{moreEvents:"{0} more",today:"Today"},input:{clear:"Clear {0}",prependAction:"{0} prepended action",appendAction:"{0} appended action",otp:"Please enter OTP character {0}"},fileInput:{counter:"{0} files",counterSize:"{0} files ({1} in total)"},timePicker:{am:"AM",pm:"PM",title:"Select Time"},pagination:{ariaLabel:{root:"Pagination Navigation",next:"Next page",previous:"Previous page",page:"Go to page {0}",currentPage:"Page {0}, Current page",first:"First page",last:"Last page"}},stepper:{next:"Next",prev:"Previous"},rating:{ariaLabel:{item:"Rating {0} of {1}"}},loading:"Loading...",infiniteScroll:{loadMore:"Load more",empty:"No more"}},ki="$vuetify.",Ci=(e,t)=>e.replace(/\{(\d+)\}/g,(n,a)=>String(t[+a])),Ls=(e,t,n)=>function(a){for(var l=arguments.length,o=new Array(l>1?l-1:0),i=1;i<l;i++)o[i-1]=arguments[i];if(!a.startsWith(ki))return Ci(a,o);const r=a.replace(ki,""),u=e.value&&n.value[e.value],d=t.value&&n.value[t.value];let c=Xt(u,r,null);return c||(`${a}${e.value}`,c=Xt(d,r,null)),c||(c=a),typeof c!="string"&&(c=a),Ci(c,o)};function Ts(e,t){return(n,a)=>new Intl.NumberFormat([e.value,t.value],a).format(n)}function il(e,t,n){const a=Q(e,t,e[t]??n.value);return a.value=e[t]??n.value,U(n,l=>{e[t]==null&&(a.value=n.value)}),a}function Bs(e){return t=>{const n=il(t,"locale",e.current),a=il(t,"fallback",e.fallback),l=il(t,"messages",e.messages);return{name:"vuetify",current:n,fallback:a,messages:l,t:Ls(n,a,l),n:Ts(n,a),provide:Bs({current:n,fallback:a,messages:l})}}}function kd(e){const t=Y(e?.locale??"en"),n=Y(e?.fallback??"en"),a=H({en:Sd,...e?.messages});return{name:"vuetify",current:t,fallback:n,messages:a,t:Ls(t,n,a),n:Ts(t,n),provide:Bs({current:t,fallback:n,messages:a})}}const In=Symbol.for("vuetify:locale");function Cd(e){return e.name!=null}function xd(e){const t=e?.adapter&&Cd(e?.adapter)?e?.adapter:kd(e),n=Pd(t,e);return{...t,...n}}function Ae(){const e=ge(In);if(!e)throw new Error("[Vuetify] Could not find injected locale instance");return e}function wd(e){const t=ge(In);if(!t)throw new Error("[Vuetify] Could not find injected locale instance");const n=t.provide(e),a=Id(n,t.rtl,e),l={...n,...a};return we(In,l),l}function Vd(){return{af:!1,ar:!0,bg:!1,ca:!1,ckb:!1,cs:!1,de:!1,el:!1,en:!1,es:!1,et:!1,fa:!0,fi:!1,fr:!1,hr:!1,hu:!1,he:!0,id:!1,it:!1,ja:!1,km:!1,ko:!1,lv:!1,lt:!1,nl:!1,no:!1,pl:!1,pt:!1,ro:!1,ru:!1,sk:!1,sl:!1,srCyrl:!1,srLatn:!1,sv:!1,th:!1,tr:!1,az:!1,uk:!1,vi:!1,zhHans:!1,zhHant:!1}}function Pd(e,t){const n=H(t?.rtl??Vd()),a=m(()=>n.value[e.current.value]??!1);return{isRtl:a,rtl:n,rtlClasses:m(()=>`v-locale--is-${a.value?"rtl":"ltr"}`)}}function Id(e,t,n){const a=m(()=>n.rtl??t.value[e.current.value]??!1);return{isRtl:a,rtl:t,rtlClasses:m(()=>`v-locale--is-${a.value?"rtl":"ltr"}`)}}function Fe(){const e=ge(In);if(!e)throw new Error("[Vuetify] Could not find injected rtl instance");return{isRtl:e.isRtl,rtlClasses:e.rtlClasses}}const _a={"001":1,AD:1,AE:6,AF:6,AG:0,AI:1,AL:1,AM:1,AN:1,AR:1,AS:0,AT:1,AU:1,AX:1,AZ:1,BA:1,BD:0,BE:1,BG:1,BH:6,BM:1,BN:1,BR:0,BS:0,BT:0,BW:0,BY:1,BZ:0,CA:0,CH:1,CL:1,CM:1,CN:1,CO:0,CR:1,CY:1,CZ:1,DE:1,DJ:6,DK:1,DM:0,DO:0,DZ:6,EC:1,EE:1,EG:6,ES:1,ET:0,FI:1,FJ:1,FO:1,FR:1,GB:1,"GB-alt-variant":0,GE:1,GF:1,GP:1,GR:1,GT:0,GU:0,HK:0,HN:0,HR:1,HU:1,ID:0,IE:1,IL:0,IN:0,IQ:6,IR:6,IS:1,IT:1,JM:0,JO:6,JP:0,KE:0,KG:1,KH:0,KR:0,KW:6,KZ:1,LA:0,LB:1,LI:1,LK:1,LT:1,LU:1,LV:1,LY:6,MC:1,MD:1,ME:1,MH:0,MK:1,MM:0,MN:1,MO:0,MQ:1,MT:0,MV:5,MX:0,MY:1,MZ:0,NI:0,NL:1,NO:1,NP:0,NZ:1,OM:6,PA:0,PE:0,PH:0,PK:0,PL:1,PR:0,PT:0,PY:0,QA:6,RE:1,RO:1,RS:1,RU:1,SA:0,SD:6,SE:1,SG:0,SI:1,SK:1,SM:1,SV:0,SY:6,TH:0,TJ:1,TM:1,TR:1,TT:0,TW:0,UA:1,UM:0,US:0,UY:1,UZ:1,VA:1,VE:0,VI:0,VN:1,WS:0,XK:1,YE:0,ZA:0,ZW:0};function pd(e,t,n){const a=[];let l=[];const o=Ds(e),i=Ms(e),r=n??_a[t.slice(-2).toUpperCase()]??0,u=(o.getDay()-r+7)%7,d=(i.getDay()-r+7)%7;for(let c=0;c<u;c++){const f=new Date(o);f.setDate(f.getDate()-(u-c)),l.push(f)}for(let c=1;c<=i.getDate();c++){const f=new Date(e.getFullYear(),e.getMonth(),c);l.push(f),l.length===7&&(a.push(l),l=[])}for(let c=1;c<7-d;c++){const f=new Date(i);f.setDate(f.getDate()+c),l.push(f)}return l.length>0&&a.push(l),a}function _d(e,t,n){const a=n??_a[t.slice(-2).toUpperCase()]??0,l=new Date(e);for(;l.getDay()!==a;)l.setDate(l.getDate()-1);return l}function Ad(e,t){const n=new Date(e),a=((_a[t.slice(-2).toUpperCase()]??0)+6)%7;for(;n.getDay()!==a;)n.setDate(n.getDate()+1);return n}function Ds(e){return new Date(e.getFullYear(),e.getMonth(),1)}function Ms(e){return new Date(e.getFullYear(),e.getMonth()+1,0)}function Ld(e){const t=e.split("-").map(Number);return new Date(t[0],t[1]-1,t[2])}const Td=/^([12]\d{3}-([1-9]|0[1-9]|1[0-2])-([1-9]|0[1-9]|[12]\d|3[01]))$/;function Es(e){if(e==null)return new Date;if(e instanceof Date)return e;if(typeof e=="string"){let t;if(Td.test(e))return Ld(e);if(t=Date.parse(e),!isNaN(t))return new Date(t)}return null}const xi=new Date(2e3,0,2);function Bd(e,t){const n=t??_a[e.slice(-2).toUpperCase()]??0;return Ct(7).map(a=>{const l=new Date(xi);return l.setDate(xi.getDate()+n+a),new Intl.DateTimeFormat(e,{weekday:"narrow"}).format(l)})}function Dd(e,t,n,a){const l=Es(e)??new Date,o=a?.[t];if(typeof o=="function")return o(l,t,n);let i={};switch(t){case"fullDate":i={year:"numeric",month:"long",day:"numeric"};break;case"fullDateWithWeekday":i={weekday:"long",year:"numeric",month:"long",day:"numeric"};break;case"normalDate":const r=l.getDate(),u=new Intl.DateTimeFormat(n,{month:"long"}).format(l);return`${r} ${u}`;case"normalDateWithWeekday":i={weekday:"short",day:"numeric",month:"short"};break;case"shortDate":i={month:"short",day:"numeric"};break;case"year":i={year:"numeric"};break;case"month":i={month:"long"};break;case"monthShort":i={month:"short"};break;case"monthAndYear":i={month:"long",year:"numeric"};break;case"monthAndDate":i={month:"long",day:"numeric"};break;case"weekday":i={weekday:"long"};break;case"weekdayShort":i={weekday:"short"};break;case"dayOfMonth":return new Intl.NumberFormat(n).format(l.getDate());case"hours12h":i={hour:"numeric",hour12:!0};break;case"hours24h":i={hour:"numeric",hour12:!1};break;case"minutes":i={minute:"numeric"};break;case"seconds":i={second:"numeric"};break;case"fullTime":i={hour:"numeric",minute:"numeric",second:"numeric",hour12:!0};break;case"fullTime12h":i={hour:"numeric",minute:"numeric",second:"numeric",hour12:!0};break;case"fullTime24h":i={hour:"numeric",minute:"numeric",second:"numeric",hour12:!1};break;case"fullDateTime":i={year:"numeric",month:"long",day:"numeric",hour:"numeric",minute:"numeric",second:"numeric",hour12:!0};break;case"fullDateTime12h":i={year:"numeric",month:"long",day:"numeric",hour:"numeric",minute:"numeric",second:"numeric",hour12:!0};break;case"fullDateTime24h":i={year:"numeric",month:"long",day:"numeric",hour:"numeric",minute:"numeric",second:"numeric",hour12:!1};break;case"keyboardDate":i={year:"numeric",month:"2-digit",day:"2-digit"};break;case"keyboardDateTime":i={year:"numeric",month:"2-digit",day:"2-digit",hour:"numeric",minute:"numeric",second:"numeric",hour12:!1};break;case"keyboardDateTime12h":i={year:"numeric",month:"2-digit",day:"2-digit",hour:"numeric",minute:"numeric",second:"numeric",hour12:!0};break;case"keyboardDateTime24h":i={year:"numeric",month:"2-digit",day:"2-digit",hour:"numeric",minute:"numeric",second:"numeric",hour12:!1};break;default:i=o??{timeZone:"UTC",timeZoneName:"short"}}return new Intl.DateTimeFormat(n,i).format(l)}function Md(e,t){const n=e.toJsDate(t),a=n.getFullYear(),l=ai(String(n.getMonth()+1),2,"0"),o=ai(String(n.getDate()),2,"0");return`${a}-${l}-${o}`}function Ed(e){const[t,n,a]=e.split("-").map(Number);return new Date(t,n-1,a)}function Fd(e,t){const n=new Date(e);return n.setMinutes(n.getMinutes()+t),n}function Od(e,t){const n=new Date(e);return n.setHours(n.getHours()+t),n}function $d(e,t){const n=new Date(e);return n.setDate(n.getDate()+t),n}function Rd(e,t){const n=new Date(e);return n.setDate(n.getDate()+t*7),n}function Nd(e,t){const n=new Date(e);return n.setDate(1),n.setMonth(n.getMonth()+t),n}function Hd(e){return e.getFullYear()}function zd(e){return e.getMonth()}function Wd(e){return e.getDate()}function jd(e){return new Date(e.getFullYear(),e.getMonth()+1,1)}function Yd(e){return new Date(e.getFullYear(),e.getMonth()-1,1)}function Gd(e){return e.getHours()}function Ud(e){return e.getMinutes()}function Kd(e){return new Date(e.getFullYear(),0,1)}function qd(e){return new Date(e.getFullYear(),11,31)}function Xd(e,t){return ka(e,t[0])&&Qd(e,t[1])}function Zd(e){const t=new Date(e);return t instanceof Date&&!isNaN(t.getTime())}function ka(e,t){return e.getTime()>t.getTime()}function Jd(e,t){return ka(kl(e),kl(t))}function Qd(e,t){return e.getTime()<t.getTime()}function wi(e,t){return e.getTime()===t.getTime()}function ev(e,t){return e.getDate()===t.getDate()&&e.getMonth()===t.getMonth()&&e.getFullYear()===t.getFullYear()}function tv(e,t){return e.getMonth()===t.getMonth()&&e.getFullYear()===t.getFullYear()}function nv(e,t){return e.getFullYear()===t.getFullYear()}function av(e,t,n){const a=new Date(e),l=new Date(t);switch(n){case"years":return a.getFullYear()-l.getFullYear();case"quarters":return Math.floor((a.getMonth()-l.getMonth()+(a.getFullYear()-l.getFullYear())*12)/4);case"months":return a.getMonth()-l.getMonth()+(a.getFullYear()-l.getFullYear())*12;case"weeks":return Math.floor((a.getTime()-l.getTime())/(1e3*60*60*24*7));case"days":return Math.floor((a.getTime()-l.getTime())/(1e3*60*60*24));case"hours":return Math.floor((a.getTime()-l.getTime())/(1e3*60*60));case"minutes":return Math.floor((a.getTime()-l.getTime())/(1e3*60));case"seconds":return Math.floor((a.getTime()-l.getTime())/1e3);default:return a.getTime()-l.getTime()}}function lv(e,t){const n=new Date(e);return n.setHours(t),n}function ov(e,t){const n=new Date(e);return n.setMinutes(t),n}function iv(e,t){const n=new Date(e);return n.setMonth(t),n}function sv(e,t){const n=new Date(e);return n.setDate(t),n}function rv(e,t){const n=new Date(e);return n.setFullYear(t),n}function kl(e){return new Date(e.getFullYear(),e.getMonth(),e.getDate(),0,0,0,0)}function uv(e){return new Date(e.getFullYear(),e.getMonth(),e.getDate(),23,59,59,999)}class cv{constructor(t){this.locale=t.locale,this.formats=t.formats}date(t){return Es(t)}toJsDate(t){return t}toISO(t){return Md(this,t)}parseISO(t){return Ed(t)}addMinutes(t,n){return Fd(t,n)}addHours(t,n){return Od(t,n)}addDays(t,n){return $d(t,n)}addWeeks(t,n){return Rd(t,n)}addMonths(t,n){return Nd(t,n)}getWeekArray(t,n){return pd(t,this.locale,n?Number(n):void 0)}startOfWeek(t,n){return _d(t,this.locale,n?Number(n):void 0)}endOfWeek(t){return Ad(t,this.locale)}startOfMonth(t){return Ds(t)}endOfMonth(t){return Ms(t)}format(t,n){return Dd(t,n,this.locale,this.formats)}isEqual(t,n){return wi(t,n)}isValid(t){return Zd(t)}isWithinRange(t,n){return Xd(t,n)}isAfter(t,n){return ka(t,n)}isAfterDay(t,n){return Jd(t,n)}isBefore(t,n){return!ka(t,n)&&!wi(t,n)}isSameDay(t,n){return ev(t,n)}isSameMonth(t,n){return tv(t,n)}isSameYear(t,n){return nv(t,n)}setMinutes(t,n){return ov(t,n)}setHours(t,n){return lv(t,n)}setMonth(t,n){return iv(t,n)}setDate(t,n){return sv(t,n)}setYear(t,n){return rv(t,n)}getDiff(t,n,a){return av(t,n,a)}getWeekdays(t){return Bd(this.locale,t?Number(t):void 0)}getYear(t){return Hd(t)}getMonth(t){return zd(t)}getDate(t){return Wd(t)}getNextMonth(t){return jd(t)}getPreviousMonth(t){return Yd(t)}getHours(t){return Gd(t)}getMinutes(t){return Ud(t)}startOfDay(t){return kl(t)}endOfDay(t){return uv(t)}startOfYear(t){return Kd(t)}endOfYear(t){return qd(t)}}const Fs=Symbol.for("vuetify:date-options"),Vi=Symbol.for("vuetify:date-adapter");function dv(e,t){const n=qe({adapter:cv,locale:{af:"af-ZA",bg:"bg-BG",ca:"ca-ES",ckb:"",cs:"cs-CZ",de:"de-DE",el:"el-GR",en:"en-US",et:"et-EE",fa:"fa-IR",fi:"fi-FI",hr:"hr-HR",hu:"hu-HU",he:"he-IL",id:"id-ID",it:"it-IT",ja:"ja-JP",ko:"ko-KR",lv:"lv-LV",lt:"lt-LT",nl:"nl-NL",no:"no-NO",pl:"pl-PL",pt:"pt-PT",ro:"ro-RO",ru:"ru-RU",sk:"sk-SK",sl:"sl-SI",srCyrl:"sr-SP",srLatn:"sr-SP",sv:"sv-SE",th:"th-TH",tr:"tr-TR",az:"az-AZ",uk:"uk-UA",vi:"vi-VN",zhHans:"zh-CN",zhHant:"zh-TW"}},e);return{options:n,instance:Os(n,t)}}function Os(e,t){const n=at(typeof e.adapter=="function"?new e.adapter({locale:e.locale[t.current.value]??t.current.value,formats:e.formats}):e.adapter);return U(t.current,a=>{n.locale=e.locale[a]??a??n.locale}),n}function Ln(){const e=ge(Fs);if(!e)throw new Error("[Vuetify] Could not find injected date options");const t=Ae();return Os(e,t)}function vv(e,t){const n=e.toJsDate(t);let a=n.getFullYear(),l=new Date(a,0,1);if(n<l)a=a-1,l=new Date(a,0,1);else{const r=new Date(a+1,0,1);n>=r&&(a=a+1,l=r)}const o=Math.abs(n.getTime()-l.getTime()),i=Math.ceil(o/(1e3*60*60*24));return Math.floor(i/7)+1}const Aa=["sm","md","lg","xl","xxl"],Cl=Symbol.for("vuetify:display"),Pi={mobileBreakpoint:"lg",thresholds:{xs:0,sm:600,md:960,lg:1280,xl:1920,xxl:2560}},fv=function(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:Pi;return qe(Pi,e)};function Ii(e){return Se&&!e?window.innerWidth:typeof e=="object"&&e.clientWidth||0}function pi(e){return Se&&!e?window.innerHeight:typeof e=="object"&&e.clientHeight||0}function _i(e){const t=Se&&!e?window.navigator.userAgent:"ssr";function n(h){return!!t.match(h)}const a=n(/android/i),l=n(/iphone|ipad|ipod/i),o=n(/cordova/i),i=n(/electron/i),r=n(/chrome/i),u=n(/edge/i),d=n(/firefox/i),c=n(/opera/i),f=n(/win/i),v=n(/mac/i),g=n(/linux/i);return{android:a,ios:l,cordova:o,electron:i,chrome:r,edge:u,firefox:d,opera:c,win:f,mac:v,linux:g,touch:Ac,ssr:t==="ssr"}}function mv(e,t){const{thresholds:n,mobileBreakpoint:a}=fv(e),l=Y(pi(t)),o=Y(_i(t)),i=at({}),r=Y(Ii(t));function u(){l.value=pi(),r.value=Ii()}function d(){u(),o.value=_i()}return Te(()=>{const c=r.value<n.sm,f=r.value<n.md&&!c,v=r.value<n.lg&&!(f||c),g=r.value<n.xl&&!(v||f||c),h=r.value<n.xxl&&!(g||v||f||c),b=r.value>=n.xxl,y=c?"xs":f?"sm":v?"md":g?"lg":h?"xl":"xxl",S=typeof a=="number"?a:n[a],w=r.value<S;i.xs=c,i.sm=f,i.md=v,i.lg=g,i.xl=h,i.xxl=b,i.smAndUp=!c,i.mdAndUp=!(c||f),i.lgAndUp=!(c||f||v),i.xlAndUp=!(c||f||v||g),i.smAndDown=!(v||g||h||b),i.mdAndDown=!(g||h||b),i.lgAndDown=!(h||b),i.xlAndDown=!b,i.name=y,i.height=l.value,i.width=r.value,i.mobile=w,i.mobileBreakpoint=a,i.platform=o.value,i.thresholds=n}),Se&&window.addEventListener("resize",u,{passive:!0}),{...ln(i),update:d,ssr:!!t}}const sn=L({mobile:{type:Boolean,default:!1},mobileBreakpoint:[Number,String]},"display");function ut(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Vt();const n=ge(Cl);if(!n)throw new Error("Could not find Vuetify display injection");const a=m(()=>{if(e.mobile!=null)return e.mobile;if(!e.mobileBreakpoint)return n.mobile.value;const o=typeof e.mobileBreakpoint=="number"?e.mobileBreakpoint:n.thresholds.value[e.mobileBreakpoint];return n.width.value<o}),l=m(()=>t?{[`${t}--mobile`]:a.value}:{});return{...n,displayClasses:l,mobile:a}}const $s=Symbol.for("vuetify:goto");function Rs(){return{container:void 0,duration:300,layout:!1,offset:0,easing:"easeInOutCubic",patterns:{linear:e=>e,easeInQuad:e=>e**2,easeOutQuad:e=>e*(2-e),easeInOutQuad:e=>e<.5?2*e**2:-1+(4-2*e)*e,easeInCubic:e=>e**3,easeOutCubic:e=>--e**3+1,easeInOutCubic:e=>e<.5?4*e**3:(e-1)*(2*e-2)*(2*e-2)+1,easeInQuart:e=>e**4,easeOutQuart:e=>1- --e**4,easeInOutQuart:e=>e<.5?8*e**4:1-8*--e**4,easeInQuint:e=>e**5,easeOutQuint:e=>1+--e**5,easeInOutQuint:e=>e<.5?16*e**5:1+16*--e**5}}}function gv(e){return uo(e)??(document.scrollingElement||document.body)}function uo(e){return typeof e=="string"?document.querySelector(e):Ql(e)}function sl(e,t,n){if(typeof e=="number")return t&&n?-e:e;let a=uo(e),l=0;for(;a;)l+=t?a.offsetLeft:a.offsetTop,a=a.offsetParent;return l}function hv(e,t){return{rtl:t.isRtl,options:qe(Rs(),e)}}async function Ai(e,t,n,a){const l=n?"scrollLeft":"scrollTop",o=qe(a?.options??Rs(),t),i=a?.rtl.value,r=(typeof e=="number"?e:uo(e))??0,u=o.container==="parent"&&r instanceof HTMLElement?r.parentElement:gv(o.container),d=typeof o.easing=="function"?o.easing:o.patterns[o.easing];if(!d)throw new TypeError(`Easing function "${o.easing}" not found.`);let c;if(typeof r=="number")c=sl(r,n,i);else if(c=sl(r,n,i)-sl(u,n,i),o.layout){const h=window.getComputedStyle(r).getPropertyValue("--v-layout-top");h&&(c-=parseInt(h,10))}c+=o.offset,c=yv(u,c,!!i,!!n);const f=u[l]??0;if(c===f)return Promise.resolve(c);const v=performance.now();return new Promise(g=>requestAnimationFrame(function h(b){const S=(b-v)/o.duration,w=Math.floor(f+(c-f)*d(Be(S,0,1)));if(u[l]=w,S>=1&&Math.abs(w-u[l])<10)return g(c);if(S>2)return g(u[l]);requestAnimationFrame(h)}))}function Ns(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};const t=ge($s),{isRtl:n}=Fe();if(!t)throw new Error("[Vuetify] Could not find injected goto instance");const a={...t,rtl:m(()=>t.rtl.value||n.value)};async function l(o,i){return Ai(o,qe(e,i),!1,a)}return l.horizontal=async(o,i)=>Ai(o,qe(e,i),!0,a),l}function yv(e,t,n,a){const{scrollWidth:l,scrollHeight:o}=e,[i,r]=e===document.scrollingElement?[window.innerWidth,window.innerHeight]:[e.offsetWidth,e.offsetHeight];let u,d;return a?n?(u=-(l-i),d=0):(u=0,d=l-i):(u=0,d=o+-r),Math.max(Math.min(t,d),u)}const bv={collapse:"mdi-chevron-up",complete:"mdi-check",cancel:"mdi-close-circle",close:"mdi-close",delete:"mdi-close-circle",clear:"mdi-close-circle",success:"mdi-check-circle",info:"mdi-information",warning:"mdi-alert-circle",error:"mdi-close-circle",prev:"mdi-chevron-left",next:"mdi-chevron-right",checkboxOn:"mdi-checkbox-marked",checkboxOff:"mdi-checkbox-blank-outline",checkboxIndeterminate:"mdi-minus-box",delimiter:"mdi-circle",sortAsc:"mdi-arrow-up",sortDesc:"mdi-arrow-down",expand:"mdi-chevron-down",menu:"mdi-menu",subgroup:"mdi-menu-down",dropdown:"mdi-menu-down",radioOn:"mdi-radiobox-marked",radioOff:"mdi-radiobox-blank",edit:"mdi-pencil",ratingEmpty:"mdi-star-outline",ratingFull:"mdi-star",ratingHalf:"mdi-star-half-full",loading:"mdi-cached",first:"mdi-page-first",last:"mdi-page-last",unfold:"mdi-unfold-more-horizontal",file:"mdi-paperclip",plus:"mdi-plus",minus:"mdi-minus",calendar:"mdi-calendar",treeviewCollapse:"mdi-menu-down",treeviewExpand:"mdi-menu-right",eyeDropper:"mdi-eyedropper"},Sv={component:e=>on(co,{...e,class:"mdi"})},ie=[String,Function,Object,Array],xl=Symbol.for("vuetify:icons"),La=L({icon:{type:ie},tag:{type:String,required:!0}},"icon"),wl=M()({name:"VComponentIcon",props:La(),setup(e,t){let{slots:n}=t;return()=>{const a=e.icon;return s(e.tag,null,{default:()=>[e.icon?s(a,null,null):n.default?.()]})}}}),Ta=rt({name:"VSvgIcon",inheritAttrs:!1,props:La(),setup(e,t){let{attrs:n}=t;return()=>s(e.tag,F(n,{style:null}),{default:()=>[s("svg",{class:"v-icon__svg",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",role:"img","aria-hidden":"true"},[Array.isArray(e.icon)?e.icon.map(a=>Array.isArray(a)?s("path",{d:a[0],"fill-opacity":a[1]},null):s("path",{d:a},null)):s("path",{d:e.icon},null)])]})}}),kv=rt({name:"VLigatureIcon",props:La(),setup(e){return()=>s(e.tag,null,{default:()=>[e.icon]})}}),co=rt({name:"VClassIcon",props:La(),setup(e){return()=>s(e.tag,{class:e.icon},null)}});function Cv(){return{svg:{component:Ta},class:{component:co}}}function xv(e){const t=Cv(),n=e?.defaultSet??"mdi";return n==="mdi"&&!t.mdi&&(t.mdi=Sv),qe({defaultSet:n,sets:t,aliases:{...bv,vuetify:["M8.2241 14.2009L12 21L22 3H14.4459L8.2241 14.2009Z",["M7.26303 12.4733L7.00113 12L2 3H12.5261C12.5261 3 12.5261 3 12.5261 3L7.26303 12.4733Z",.6]],"vuetify-outline":"svg:M7.26 12.47 12.53 3H2L7.26 12.47ZM14.45 3 8.22 14.2 12 21 22 3H14.45ZM18.6 5 12 16.88 10.51 14.2 15.62 5ZM7.26 8.35 5.4 5H9.13L7.26 8.35Z","vuetify-play":["m6.376 13.184-4.11-7.192C1.505 4.66 2.467 3 4.003 3h8.532l-.953 1.576-.006.01-.396.677c-.429.732-.214 1.507.194 2.015.404.503 1.092.878 1.869.806a3.72 3.72 0 0 1 1.005.022c.276.053.434.143.523.237.138.146.38.635-.25 2.09-.893 1.63-1.553 1.722-1.847 1.677-.213-.033-.468-.158-.756-.406a4.95 4.95 0 0 1-.8-.927c-.39-.564-1.04-.84-1.66-.846-.625-.006-1.316.27-1.693.921l-.478.826-.911 1.506Z",["M9.093 11.552c.046-.079.144-.15.32-.148a.53.53 0 0 1 .43.207c.285.414.636.847 1.046 1.2.405.35.914.662 1.516.754 1.334.205 2.502-.698 3.48-2.495l.014-.028.013-.03c.687-1.574.774-2.852-.005-3.675-.37-.391-.861-.586-1.333-.676a5.243 5.243 0 0 0-1.447-.044c-.173.016-.393-.073-.54-.257-.145-.18-.127-.316-.082-.392l.393-.672L14.287 3h5.71c1.536 0 2.499 1.659 1.737 2.992l-7.997 13.996c-.768 1.344-2.706 1.344-3.473 0l-3.037-5.314 1.377-2.278.004-.006.004-.007.481-.831Z",.6]]}},e)}const wv=e=>{const t=ge(xl);if(!t)throw new Error("Missing Vuetify Icons provide!");return{iconData:m(()=>{const a=tt(e);if(!a)return{component:wl};let l=a;if(typeof l=="string"&&(l=l.trim(),l.startsWith("$")&&(l=t.aliases?.[l.slice(1)])),Array.isArray(l))return{component:Ta,icon:l};if(typeof l!="string")return{component:wl,icon:l};const o=Object.keys(t.sets).find(u=>typeof l=="string"&&l.startsWith(`${u}:`)),i=o?l.slice(o.length+1):l;return{component:t.sets[o??t.defaultSet].component,icon:i}})}},Hn=Symbol.for("vuetify:theme"),me=L({theme:String},"theme");function Li(){return{defaultTheme:"light",variations:{colors:[],lighten:0,darken:0},themes:{light:{dark:!1,colors:{background:"#FFFFFF",surface:"#FFFFFF","surface-bright":"#FFFFFF","surface-light":"#EEEEEE","surface-variant":"#424242","on-surface-variant":"#EEEEEE",primary:"#1867C0","primary-darken-1":"#1F5592",secondary:"#48A9A6","secondary-darken-1":"#018786",error:"#B00020",info:"#2196F3",success:"#4CAF50",warning:"#FB8C00"},variables:{"border-color":"#000000","border-opacity":.12,"high-emphasis-opacity":.87,"medium-emphasis-opacity":.6,"disabled-opacity":.38,"idle-opacity":.04,"hover-opacity":.04,"focus-opacity":.12,"selected-opacity":.08,"activated-opacity":.12,"pressed-opacity":.12,"dragged-opacity":.08,"theme-kbd":"#212529","theme-on-kbd":"#FFFFFF","theme-code":"#F5F5F5","theme-on-code":"#000000"}},dark:{dark:!0,colors:{background:"#121212",surface:"#212121","surface-bright":"#ccbfd6","surface-light":"#424242","surface-variant":"#a3a3a3","on-surface-variant":"#424242",primary:"#2196F3","primary-darken-1":"#277CC1",secondary:"#54B6B2","secondary-darken-1":"#48A9A6",error:"#CF6679",info:"#2196F3",success:"#4CAF50",warning:"#FB8C00"},variables:{"border-color":"#FFFFFF","border-opacity":.12,"high-emphasis-opacity":1,"medium-emphasis-opacity":.7,"disabled-opacity":.5,"idle-opacity":.1,"hover-opacity":.04,"focus-opacity":.12,"selected-opacity":.08,"activated-opacity":.12,"pressed-opacity":.16,"dragged-opacity":.08,"theme-kbd":"#212529","theme-on-kbd":"#FFFFFF","theme-code":"#343434","theme-on-code":"#CCCCCC"}}}}}function Vv(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:Li();const t=Li();if(!e)return{...t,isDisabled:!0};const n={};for(const[a,l]of Object.entries(e.themes??{})){const o=l.dark||a==="dark"?t.themes?.dark:t.themes?.light;n[a]=qe(o,l)}return qe(t,{...e,themes:n})}function Pv(e){const t=Vv(e),n=H(t.defaultTheme),a=H(t.themes),l=m(()=>{const c={};for(const[f,v]of Object.entries(a.value)){const g=c[f]={...v,colors:{...v.colors}};if(t.variations)for(const h of t.variations.colors){const b=g.colors[h];if(b)for(const y of["lighten","darken"]){const S=y==="lighten"?sd:rd;for(const w of Ct(t.variations[y],1))g.colors[`${h}-${y}-${w}`]=xs(S(vt(b),w))}}for(const h of Object.keys(g.colors)){if(/^on-[a-z]/.test(h)||g.colors[`on-${h}`])continue;const b=`on-${h}`,y=vt(g.colors[h]);g.colors[b]=Is(y)}}return c}),o=m(()=>l.value[n.value]),i=m(()=>{const c=[];o.value?.dark&&Wt(c,":root",["color-scheme: dark"]),Wt(c,":root",Ti(o.value));for(const[h,b]of Object.entries(l.value))Wt(c,`.v-theme--${h}`,[`color-scheme: ${b.dark?"dark":"normal"}`,...Ti(b)]);const f=[],v=[],g=new Set(Object.values(l.value).flatMap(h=>Object.keys(h.colors)));for(const h of g)/^on-[a-z]/.test(h)?Wt(v,`.${h}`,[`color: rgb(var(--v-theme-${h})) !important`]):(Wt(f,`.bg-${h}`,[`--v-theme-overlay-multiplier: var(--v-theme-${h}-overlay-multiplier)`,`background-color: rgb(var(--v-theme-${h})) !important`,`color: rgb(var(--v-theme-on-${h})) !important`]),Wt(v,`.text-${h}`,[`color: rgb(var(--v-theme-${h})) !important`]),Wt(v,`.border-${h}`,[`--v-border-color: var(--v-theme-${h})`]));return c.push(...f,...v),c.map((h,b)=>b===0?h:`    ${h}`).join("")});function r(){return{style:[{children:i.value,id:"vuetify-theme-stylesheet",nonce:t.cspNonce||!1}]}}function u(c){if(t.isDisabled)return;const f=c._context.provides.usehead;if(f)if(f.push){const v=f.push(r);Se&&U(i,()=>{v.patch(r)})}else Se?(f.addHeadObjs(m(r)),Te(()=>f.updateDOM())):f.addHeadObjs(r());else{let g=function(){if(typeof document<"u"&&!v){const h=document.createElement("style");h.type="text/css",h.id="vuetify-theme-stylesheet",t.cspNonce&&h.setAttribute("nonce",t.cspNonce),v=h,document.head.appendChild(v)}v&&(v.innerHTML=i.value)},v=Se?document.getElementById("vuetify-theme-stylesheet"):null;Se?U(i,g,{immediate:!0}):g()}}const d=m(()=>t.isDisabled?void 0:`v-theme--${n.value}`);return{install:u,isDisabled:t.isDisabled,name:n,themes:a,current:o,computedThemes:l,themeClasses:d,styles:i,global:{name:n,current:o}}}function be(e){_e("provideTheme");const t=ge(Hn,null);if(!t)throw new Error("Could not find Vuetify theme injection");const n=m(()=>e.theme??t.name.value),a=m(()=>t.themes.value[n.value]),l=m(()=>t.isDisabled?void 0:`v-theme--${n.value}`),o={...t,name:n,current:a,themeClasses:l};return we(Hn,o),o}function vo(){_e("useTheme");const e=ge(Hn,null);if(!e)throw new Error("Could not find Vuetify theme injection");return e}function Wt(e,t,n){e.push(`${t} {
`,...n.map(a=>`  ${a};
`),`}
`)}function Ti(e){const t=e.dark?2:1,n=e.dark?1:2,a=[];for(const[l,o]of Object.entries(e.colors)){const i=vt(o);a.push(`--v-theme-${l}: ${i.r},${i.g},${i.b}`),l.startsWith("on-")||a.push(`--v-theme-${l}-overlay-multiplier: ${Sl(o)>.18?t:n}`)}for(const[l,o]of Object.entries(e.variables)){const i=typeof o=="string"&&o.startsWith("#")?vt(o):void 0,r=i?`${i.r}, ${i.g}, ${i.b}`:void 0;a.push(`--v-${l}: ${r??o}`)}return a}function yt(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"content";const n=ha(),a=H();if(Se){const l=new ResizeObserver(o=>{e?.(o,l),o.length&&(t==="content"?a.value=o[0].contentRect:a.value=o[0].target.getBoundingClientRect())});Je(()=>{l.disconnect()}),U(()=>n.el,(o,i)=>{i&&(l.unobserve(i),a.value=void 0),o&&l.observe(o)},{flush:"post"})}return{resizeRef:n,contentRect:Xl(a)}}const zn=Symbol.for("vuetify:layout"),Hs=Symbol.for("vuetify:layout-item"),Bi=1e3,zs=L({overlaps:{type:Array,default:()=>[]},fullHeight:Boolean},"layout"),rn=L({name:{type:String},order:{type:[Number,String],default:0},absolute:Boolean},"layout-item");function fo(){const e=ge(zn);if(!e)throw new Error("[Vuetify] Could not find injected layout");return{getLayoutItem:e.getLayoutItem,mainRect:e.mainRect,mainStyles:e.mainStyles}}function un(e){const t=ge(zn);if(!t)throw new Error("[Vuetify] Could not find injected layout");const n=e.id??`layout-item-${je()}`,a=_e("useLayoutItem");we(Hs,{id:n});const l=Y(!1);as(()=>l.value=!0),bc(()=>l.value=!1);const{layoutItemStyles:o,layoutItemScrimStyles:i}=t.register(a,{...e,active:m(()=>l.value?!1:e.active.value),id:n});return Je(()=>t.unregister(n)),{layoutItemStyles:o,layoutRect:t.layoutRect,layoutItemScrimStyles:i}}const Iv=(e,t,n,a)=>{let l={top:0,left:0,right:0,bottom:0};const o=[{id:"",layer:{...l}}];for(const i of e){const r=t.get(i),u=n.get(i),d=a.get(i);if(!r||!u||!d)continue;const c={...l,[r.value]:parseInt(l[r.value],10)+(d.value?parseInt(u.value,10):0)};o.push({id:i,layer:c}),l=c}return o};function Ws(e){const t=ge(zn,null),n=m(()=>t?t.rootZIndex.value-100:Bi),a=H([]),l=at(new Map),o=at(new Map),i=at(new Map),r=at(new Map),u=at(new Map),{resizeRef:d,contentRect:c}=yt(),f=m(()=>{const x=new Map,I=e.overlaps??[];for(const k of I.filter(C=>C.includes(":"))){const[C,T]=k.split(":");if(!a.value.includes(C)||!a.value.includes(T))continue;const P=l.get(C),_=l.get(T),B=o.get(C),N=o.get(T);!P||!_||!B||!N||(x.set(T,{position:P.value,amount:parseInt(B.value,10)}),x.set(C,{position:_.value,amount:-parseInt(N.value,10)}))}return x}),v=m(()=>{const x=[...new Set([...i.values()].map(k=>k.value))].sort((k,C)=>k-C),I=[];for(const k of x){const C=a.value.filter(T=>i.get(T)?.value===k);I.push(...C)}return Iv(I,l,o,r)}),g=m(()=>!Array.from(u.values()).some(x=>x.value)),h=m(()=>v.value[v.value.length-1].layer),b=m(()=>({"--v-layout-left":K(h.value.left),"--v-layout-right":K(h.value.right),"--v-layout-top":K(h.value.top),"--v-layout-bottom":K(h.value.bottom),...g.value?void 0:{transition:"none"}})),y=m(()=>v.value.slice(1).map((x,I)=>{let{id:k}=x;const{layer:C}=v.value[I],T=o.get(k),P=l.get(k);return{id:k,...C,size:Number(T.value),position:P.value}})),S=x=>y.value.find(I=>I.id===x),w=_e("createLayout"),p=Y(!1);Qe(()=>{p.value=!0}),we(zn,{register:(x,I)=>{let{id:k,order:C,position:T,layoutSize:P,elementSize:_,active:B,disableTransitions:N,absolute:z}=I;i.set(k,C),l.set(k,T),o.set(k,P),r.set(k,B),N&&u.set(k,N);const ee=Cn(Hs,w?.vnode).indexOf(x);ee>-1?a.value.splice(ee,0,k):a.value.push(k);const Z=m(()=>y.value.findIndex(q=>q.id===k)),$=m(()=>n.value+v.value.length*2-Z.value*2),E=m(()=>{const q=T.value==="left"||T.value==="right",ue=T.value==="right",le=T.value==="bottom",oe=_.value??P.value,W=oe===0?"%":"px",se={[T.value]:0,zIndex:$.value,transform:`translate${q?"X":"Y"}(${(B.value?0:-(oe===0?100:oe))*(ue||le?-1:1)}${W})`,position:z.value||n.value!==Bi?"absolute":"fixed",...g.value?void 0:{transition:"none"}};if(!p.value)return se;const ce=y.value[Z.value];if(!ce)throw new Error(`[Vuetify] Could not find layout item "${k}"`);const De=f.value.get(k);return De&&(ce[De.position]+=De.amount),{...se,height:q?`calc(100% - ${ce.top}px - ${ce.bottom}px)`:_.value?`${_.value}px`:void 0,left:ue?void 0:`${ce.left}px`,right:ue?`${ce.right}px`:void 0,top:T.value!=="bottom"?`${ce.top}px`:void 0,bottom:T.value!=="top"?`${ce.bottom}px`:void 0,width:q?_.value?`${_.value}px`:void 0:`calc(100% - ${ce.left}px - ${ce.right}px)`}}),R=m(()=>({zIndex:$.value-1}));return{layoutItemStyles:E,layoutItemScrimStyles:R,zIndex:$}},unregister:x=>{i.delete(x),l.delete(x),o.delete(x),r.delete(x),u.delete(x),a.value=a.value.filter(I=>I!==x)},mainRect:h,mainStyles:b,getLayoutItem:S,items:y,layoutRect:c,rootZIndex:n});const A=m(()=>["v-layout",{"v-layout--full-height":e.fullHeight}]),V=m(()=>({zIndex:t?n.value:void 0,position:t?"relative":void 0,overflow:t?"hidden":void 0}));return{layoutClasses:A,layoutStyles:V,getLayoutItem:S,items:y,layoutRect:c,layoutRef:d}}function js(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};const{blueprint:t,...n}=e,a=qe(t,n),{aliases:l={},components:o={},directives:i={}}=a,r=dd(a.defaults),u=mv(a.display,a.ssr),d=Pv(a.theme),c=xv(a.icons),f=xd(a.locale),v=dv(a.date,f),g=hv(a.goTo,f);return{install:b=>{for(const y in i)b.directive(y,i[y]);for(const y in o)b.component(y,o[y]);for(const y in l)b.component(y,rt({...l[y],name:y,aliasName:l[y].name}));if(d.install(b),b.provide(Pn,r),b.provide(Cl,u),b.provide(Hn,d),b.provide(xl,c),b.provide(In,f),b.provide(Fs,v.options),b.provide(Vi,v.instance),b.provide($s,g),Se&&a.ssr)if(b.$nuxt)b.$nuxt.hook("app:suspense:resolve",()=>{u.update()});else{const{mount:y}=b;b.mount=function(){const S=y(...arguments);return ke(()=>u.update()),b.mount=y,S}}je.reset(),b.mixin({computed:{$vuetify(){return at({defaults:Sn.call(this,Pn),display:Sn.call(this,Cl),theme:Sn.call(this,Hn),icons:Sn.call(this,xl),locale:Sn.call(this,In),date:Sn.call(this,Vi)})}}})},defaults:r,display:u,theme:d,icons:c,locale:f,date:v,goTo:g}}const Ys="3.7.2";js.version=Ys;function Sn(e){const t=this.$,n=t.parent?.provides??t.vnode.appContext?.provides;if(n&&e in n)return n[e]}const t1=Object.freeze(Object.defineProperty({__proto__:null,createVuetify:js,useDate:Ln,useDefaults:fd,useDisplay:ut,useGoTo:Ns,useLayout:fo,useLocale:Ae,useRtl:Fe,useTheme:vo,version:Ys},Symbol.toStringTag,{value:"Module"}));function mo(e){return to(()=>{const t=[],n={};if(e.value.background)if(bl(e.value.background)){if(n.backgroundColor=e.value.background,!e.value.text&&ld(e.value.background)){const a=vt(e.value.background);if(a.a==null||a.a===1){const l=Is(a);n.color=l,n.caretColor=l}}}else t.push(`bg-${e.value.background}`);return e.value.text&&(bl(e.value.text)?(n.color=e.value.text,n.caretColor=e.value.text):t.push(`text-${e.value.text}`)),{colorClasses:t,colorStyles:n}})}function Ge(e,t){const n=m(()=>({text:wn(e)?e.value:t?e[t]:null})),{colorClasses:a,colorStyles:l}=mo(n);return{textColorClasses:a,textColorStyles:l}}function Ce(e,t){const n=m(()=>({background:wn(e)?e.value:t?e[t]:null})),{colorClasses:a,colorStyles:l}=mo(n);return{backgroundColorClasses:a,backgroundColorStyles:l}}const pv=["x-small","small","default","large","x-large"],It=L({size:{type:[String,Number],default:"default"}},"size");function Tn(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Vt();return to(()=>{let n,a;return ga(pv,e.size)?n=`${t}--size-${e.size}`:e.size&&(a={width:K(e.size),height:K(e.size)}),{sizeClasses:n,sizeStyles:a}})}const re=L({tag:{type:String,default:"div"}},"tag"),_v=L({color:String,disabled:Boolean,start:Boolean,end:Boolean,icon:ie,...X(),...It(),...re({tag:"i"}),...me()},"VIcon"),ye=M()({name:"VIcon",props:_v(),setup(e,t){let{attrs:n,slots:a}=t;const l=H(),{themeClasses:o}=be(e),{iconData:i}=wv(m(()=>l.value||e.icon)),{sizeClasses:r}=Tn(e),{textColorClasses:u,textColorStyles:d}=Ge(D(e,"color"));return O(()=>{const c=a.default?.();c&&(l.value=cs(c).filter(v=>v.type===Sc&&v.children&&typeof v.children=="string")[0]?.children);const f=!!(n.onClick||n.onClickOnce);return s(i.value.component,{tag:e.tag,icon:i.value.icon,class:["v-icon","notranslate",o.value,r.value,u.value,{"v-icon--clickable":f,"v-icon--disabled":e.disabled,"v-icon--start":e.start,"v-icon--end":e.end},e.class],style:[r.value?void 0:{fontSize:K(e.size),height:K(e.size),width:K(e.size)},d.value,e.style],role:f?"button":void 0,"aria-hidden":!f,tabindex:f?e.disabled?-1:0:void 0},{default:()=>[c]})}),{}}}),Av=L({disabled:Boolean,group:Boolean,hideOnLeave:Boolean,leaveAbsolute:Boolean,mode:String,origin:String},"transition");function ct(e,t,n){return M()({name:e,props:Av({mode:n,origin:t}),setup(a,l){let{slots:o}=l;const i={onBeforeEnter(r){a.origin&&(r.style.transformOrigin=a.origin)},onLeave(r){if(a.leaveAbsolute){const{offsetTop:u,offsetLeft:d,offsetWidth:c,offsetHeight:f}=r;r._transitionInitialStyles={position:r.style.position,top:r.style.top,left:r.style.left,width:r.style.width,height:r.style.height},r.style.position="absolute",r.style.top=`${u}px`,r.style.left=`${d}px`,r.style.width=`${c}px`,r.style.height=`${f}px`}a.hideOnLeave&&r.style.setProperty("display","none","important")},onAfterLeave(r){if(a.leaveAbsolute&&r?._transitionInitialStyles){const{position:u,top:d,left:c,width:f,height:v}=r._transitionInitialStyles;delete r._transitionInitialStyles,r.style.position=u||"",r.style.top=d||"",r.style.left=c||"",r.style.width=f||"",r.style.height=v||""}}};return()=>{const r=a.group?Zl:Et;return on(r,{name:a.disabled?"":e,css:!a.disabled,...a.group?void 0:{mode:a.mode},...a.disabled?{}:i},o.default)}}})}function Gs(e,t){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:"in-out";return M()({name:e,props:{mode:{type:String,default:n},disabled:Boolean,group:Boolean},setup(a,l){let{slots:o}=l;const i=a.group?Zl:Et;return()=>on(i,{name:a.disabled?"":e,css:!a.disabled,...a.disabled?{}:t},o.default)}})}function Us(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"";const n=(arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1)?"width":"height",a=ns(`offset-${n}`);return{onBeforeEnter(i){i._parent=i.parentNode,i._initialStyle={transition:i.style.transition,overflow:i.style.overflow,[n]:i.style[n]}},onEnter(i){const r=i._initialStyle;i.style.setProperty("transition","none","important"),i.style.overflow="hidden";const u=`${i[a]}px`;i.style[n]="0",i.offsetHeight,i.style.transition=r.transition,e&&i._parent&&i._parent.classList.add(e),requestAnimationFrame(()=>{i.style[n]=u})},onAfterEnter:o,onEnterCancelled:o,onLeave(i){i._initialStyle={transition:"",overflow:i.style.overflow,[n]:i.style[n]},i.style.overflow="hidden",i.style[n]=`${i[a]}px`,i.offsetHeight,requestAnimationFrame(()=>i.style[n]="0")},onAfterLeave:l,onLeaveCancelled:l};function l(i){e&&i._parent&&i._parent.classList.remove(e),o(i)}function o(i){const r=i._initialStyle[n];i.style.overflow=i._initialStyle.overflow,r!=null&&(i.style[n]=r),delete i._initialStyle}}const Lv=L({target:[Object,Array]},"v-dialog-transition"),Ba=M()({name:"VDialogTransition",props:Lv(),setup(e,t){let{slots:n}=t;const a={onBeforeEnter(l){l.style.pointerEvents="none",l.style.visibility="hidden"},async onEnter(l,o){await new Promise(v=>requestAnimationFrame(v)),await new Promise(v=>requestAnimationFrame(v)),l.style.visibility="";const{x:i,y:r,sx:u,sy:d,speed:c}=Mi(e.target,l),f=Yt(l,[{transform:`translate(${i}px, ${r}px) scale(${u}, ${d})`,opacity:0},{}],{duration:225*c,easing:gd});Di(l)?.forEach(v=>{Yt(v,[{opacity:0},{opacity:0,offset:.33},{}],{duration:225*2*c,easing:Nn})}),f.finished.then(()=>o())},onAfterEnter(l){l.style.removeProperty("pointer-events")},onBeforeLeave(l){l.style.pointerEvents="none"},async onLeave(l,o){await new Promise(v=>requestAnimationFrame(v));const{x:i,y:r,sx:u,sy:d,speed:c}=Mi(e.target,l);Yt(l,[{},{transform:`translate(${i}px, ${r}px) scale(${u}, ${d})`,opacity:0}],{duration:125*c,easing:hd}).finished.then(()=>o()),Di(l)?.forEach(v=>{Yt(v,[{},{opacity:0,offset:.2},{opacity:0}],{duration:125*2*c,easing:Nn})})},onAfterLeave(l){l.style.removeProperty("pointer-events")}};return()=>e.target?s(Et,F({name:"dialog-transition"},a,{css:!1}),n):s(Et,{name:"dialog-transition"},n)}});function Di(e){const t=e.querySelector(":scope > .v-card, :scope > .v-sheet, :scope > .v-list")?.children;return t&&[...t]}function Mi(e,t){const n=gs(e),a=ao(t),[l,o]=getComputedStyle(t).transformOrigin.split(" ").map(S=>parseFloat(S)),[i,r]=getComputedStyle(t).getPropertyValue("--v-overlay-anchor-origin").split(" ");let u=n.left+n.width/2;i==="left"||r==="left"?u-=n.width/2:(i==="right"||r==="right")&&(u+=n.width/2);let d=n.top+n.height/2;i==="top"||r==="top"?d-=n.height/2:(i==="bottom"||r==="bottom")&&(d+=n.height/2);const c=n.width/a.width,f=n.height/a.height,v=Math.max(1,c,f),g=c/v||0,h=f/v||0,b=a.width*a.height/(window.innerWidth*window.innerHeight),y=b>.12?Math.min(1.5,(b-.12)*10+1):1;return{x:u-(l+a.left),y:d-(o+a.top),sx:g,sy:h,speed:y}}const Tv=ct("fab-transition","center center","out-in"),Bv=ct("dialog-bottom-transition"),Dv=ct("dialog-top-transition"),Wn=ct("fade-transition"),go=ct("scale-transition"),Mv=ct("scroll-x-transition"),Ev=ct("scroll-x-reverse-transition"),Fv=ct("scroll-y-transition"),Ov=ct("scroll-y-reverse-transition"),$v=ct("slide-x-transition"),Rv=ct("slide-x-reverse-transition"),ho=ct("slide-y-transition"),Nv=ct("slide-y-reverse-transition"),Da=Gs("expand-transition",Us()),yo=Gs("expand-x-transition",Us("",!0)),Hv=L({defaults:Object,disabled:Boolean,reset:[Number,String],root:[Boolean,String],scoped:Boolean},"VDefaultsProvider"),ve=M(!1)({name:"VDefaultsProvider",props:Hv(),setup(e,t){let{slots:n}=t;const{defaults:a,disabled:l,reset:o,root:i,scoped:r}=ln(e);return pe(a,{reset:o,root:i,scoped:r,disabled:l}),()=>n.default?.()}}),Vl=Symbol.for("vuetify:list");function Ks(){const e=ge(Vl,{hasPrepend:Y(!1),updateHasPrepend:()=>null}),t={hasPrepend:Y(!1),updateHasPrepend:n=>{n&&(t.hasPrepend.value=n)}};return we(Vl,t),e}function qs(){return ge(Vl,null)}const bo=e=>{const t={activate:n=>{let{id:a,value:l,activated:o}=n;return a=Me(a),e&&!l&&o.size===1&&o.has(a)||(l?o.add(a):o.delete(a)),o},in:(n,a,l)=>{let o=new Set;if(n!=null)for(const i of Pe(n))o=t.activate({id:i,value:!0,activated:new Set(o),children:a,parents:l});return o},out:n=>Array.from(n)};return t},Xs=e=>{const t=bo(e);return{activate:a=>{let{activated:l,id:o,...i}=a;o=Me(o);const r=l.has(o)?new Set([o]):new Set;return t.activate({...i,id:o,activated:r})},in:(a,l,o)=>{let i=new Set;if(a!=null){const r=Pe(a);r.length&&(i=t.in(r.slice(0,1),l,o))}return i},out:(a,l,o)=>t.out(a,l,o)}},zv=e=>{const t=bo(e);return{activate:a=>{let{id:l,activated:o,children:i,...r}=a;return l=Me(l),i.has(l)?o:t.activate({id:l,activated:o,children:i,...r})},in:t.in,out:t.out}},Wv=e=>{const t=Xs(e);return{activate:a=>{let{id:l,activated:o,children:i,...r}=a;return l=Me(l),i.has(l)?o:t.activate({id:l,activated:o,children:i,...r})},in:t.in,out:t.out}},jv={open:e=>{let{id:t,value:n,opened:a,parents:l}=e;if(n){const o=new Set;o.add(t);let i=l.get(t);for(;i!=null;)o.add(i),i=l.get(i);return o}else return a.delete(t),a},select:()=>null},Zs={open:e=>{let{id:t,value:n,opened:a,parents:l}=e;if(n){let o=l.get(t);for(a.add(t);o!=null&&o!==t;)a.add(o),o=l.get(o);return a}else a.delete(t);return a},select:()=>null},Yv={open:Zs.open,select:e=>{let{id:t,value:n,opened:a,parents:l}=e;if(!n)return a;const o=[];let i=l.get(t);for(;i!=null;)o.push(i),i=l.get(i);return new Set(o)}},So=e=>{const t={select:n=>{let{id:a,value:l,selected:o}=n;if(a=Me(a),e&&!l){const i=Array.from(o.entries()).reduce((r,u)=>{let[d,c]=u;return c==="on"&&r.push(d),r},[]);if(i.length===1&&i[0]===a)return o}return o.set(a,l?"on":"off"),o},in:(n,a,l)=>{let o=new Map;for(const i of n||[])o=t.select({id:i,value:!0,selected:new Map(o),children:a,parents:l});return o},out:n=>{const a=[];for(const[l,o]of n.entries())o==="on"&&a.push(l);return a}};return t},Js=e=>{const t=So(e);return{select:a=>{let{selected:l,id:o,...i}=a;o=Me(o);const r=l.has(o)?new Map([[o,l.get(o)]]):new Map;return t.select({...i,id:o,selected:r})},in:(a,l,o)=>{let i=new Map;return a?.length&&(i=t.in(a.slice(0,1),l,o)),i},out:(a,l,o)=>t.out(a,l,o)}},Gv=e=>{const t=So(e);return{select:a=>{let{id:l,selected:o,children:i,...r}=a;return l=Me(l),i.has(l)?o:t.select({id:l,selected:o,children:i,...r})},in:t.in,out:t.out}},Uv=e=>{const t=Js(e);return{select:a=>{let{id:l,selected:o,children:i,...r}=a;return l=Me(l),i.has(l)?o:t.select({id:l,selected:o,children:i,...r})},in:t.in,out:t.out}},Kv=e=>{const t={select:n=>{let{id:a,value:l,selected:o,children:i,parents:r}=n;a=Me(a);const u=new Map(o),d=[a];for(;d.length;){const f=d.shift();o.set(Me(f),l?"on":"off"),i.has(f)&&d.push(...i.get(f))}let c=Me(r.get(a));for(;c;){const f=i.get(c),v=f.every(h=>o.get(Me(h))==="on"),g=f.every(h=>!o.has(Me(h))||o.get(Me(h))==="off");o.set(c,v?"on":g?"off":"indeterminate"),c=Me(r.get(c))}return e&&!l&&Array.from(o.entries()).reduce((v,g)=>{let[h,b]=g;return b==="on"&&v.push(h),v},[]).length===0?u:o},in:(n,a,l)=>{let o=new Map;for(const i of n||[])o=t.select({id:i,value:!0,selected:new Map(o),children:a,parents:l});return o},out:(n,a)=>{const l=[];for(const[o,i]of n.entries())i==="on"&&!a.has(o)&&l.push(o);return l}};return t},jn=Symbol.for("vuetify:nested"),Qs={id:Y(),root:{register:()=>null,unregister:()=>null,parents:H(new Map),children:H(new Map),open:()=>null,openOnSelect:()=>null,activate:()=>null,select:()=>null,activatable:H(!1),selectable:H(!1),opened:H(new Set),activated:H(new Set),selected:H(new Map),selectedValues:H([]),getPath:()=>[]}},qv=L({activatable:Boolean,selectable:Boolean,activeStrategy:[String,Function,Object],selectStrategy:[String,Function,Object],openStrategy:[String,Object],opened:null,activated:null,selected:null,mandatory:Boolean},"nested"),Xv=e=>{let t=!1;const n=H(new Map),a=H(new Map),l=Q(e,"opened",e.opened,h=>new Set(h),h=>[...h.values()]),o=m(()=>{if(typeof e.activeStrategy=="object")return e.activeStrategy;if(typeof e.activeStrategy=="function")return e.activeStrategy(e.mandatory);switch(e.activeStrategy){case"leaf":return zv(e.mandatory);case"single-leaf":return Wv(e.mandatory);case"independent":return bo(e.mandatory);case"single-independent":default:return Xs(e.mandatory)}}),i=m(()=>{if(typeof e.selectStrategy=="object")return e.selectStrategy;if(typeof e.selectStrategy=="function")return e.selectStrategy(e.mandatory);switch(e.selectStrategy){case"single-leaf":return Uv(e.mandatory);case"leaf":return Gv(e.mandatory);case"independent":return So(e.mandatory);case"single-independent":return Js(e.mandatory);case"classic":default:return Kv(e.mandatory)}}),r=m(()=>{if(typeof e.openStrategy=="object")return e.openStrategy;switch(e.openStrategy){case"list":return Yv;case"single":return jv;case"multiple":default:return Zs}}),u=Q(e,"activated",e.activated,h=>o.value.in(h,n.value,a.value),h=>o.value.out(h,n.value,a.value)),d=Q(e,"selected",e.selected,h=>i.value.in(h,n.value,a.value),h=>i.value.out(h,n.value,a.value));Je(()=>{t=!0});function c(h){const b=[];let y=h;for(;y!=null;)b.unshift(y),y=a.value.get(y);return b}const f=_e("nested"),v=new Set,g={id:Y(),root:{opened:l,activatable:D(e,"activatable"),selectable:D(e,"selectable"),activated:u,selected:d,selectedValues:m(()=>{const h=[];for(const[b,y]of d.value.entries())y==="on"&&h.push(b);return h}),register:(h,b,y)=>{if(v.has(h)){c(h).join(" -> "),c(b).concat(h).join(" -> ");return}else v.add(h);b&&h!==b&&a.value.set(h,b),y&&n.value.set(h,[]),b!=null&&n.value.set(b,[...n.value.get(b)||[],h])},unregister:h=>{if(t)return;v.delete(h),n.value.delete(h);const b=a.value.get(h);if(b){const y=n.value.get(b)??[];n.value.set(b,y.filter(S=>S!==h))}a.value.delete(h)},open:(h,b,y)=>{f.emit("click:open",{id:h,value:b,path:c(h),event:y});const S=r.value.open({id:h,value:b,opened:new Set(l.value),children:n.value,parents:a.value,event:y});S&&(l.value=S)},openOnSelect:(h,b,y)=>{const S=r.value.select({id:h,value:b,selected:new Map(d.value),opened:new Set(l.value),children:n.value,parents:a.value,event:y});S&&(l.value=S)},select:(h,b,y)=>{f.emit("click:select",{id:h,value:b,path:c(h),event:y});const S=i.value.select({id:h,value:b,selected:new Map(d.value),children:n.value,parents:a.value,event:y});S&&(d.value=S),g.root.openOnSelect(h,b,y)},activate:(h,b,y)=>{if(!e.activatable)return g.root.select(h,!0,y);f.emit("click:activate",{id:h,value:b,path:c(h),event:y});const S=o.value.activate({id:h,value:b,activated:new Set(u.value),children:n.value,parents:a.value,event:y});S&&(u.value=S)},children:n,parents:a,getPath:c}};return we(jn,g),g.root},er=(e,t)=>{const n=ge(jn,Qs),a=Symbol(je()),l=m(()=>e.value!==void 0?e.value:a),o={...n,id:l,open:(i,r)=>n.root.open(l.value,i,r),openOnSelect:(i,r)=>n.root.openOnSelect(l.value,i,r),isOpen:m(()=>n.root.opened.value.has(l.value)),parent:m(()=>n.root.parents.value.get(l.value)),activate:(i,r)=>n.root.activate(l.value,i,r),isActivated:m(()=>n.root.activated.value.has(Me(l.value))),select:(i,r)=>n.root.select(l.value,i,r),isSelected:m(()=>n.root.selected.value.get(Me(l.value))==="on"),isIndeterminate:m(()=>n.root.selected.value.get(l.value)==="indeterminate"),isLeaf:m(()=>!n.root.children.value.get(l.value)),isGroupActivator:n.isGroupActivator};return!n.isGroupActivator&&n.root.register(l.value,n.id.value,t),Je(()=>{!n.isGroupActivator&&n.root.unregister(l.value)}),t&&we(jn,o),o},Zv=()=>{const e=ge(jn,Qs);we(jn,{...e,isGroupActivator:!0})};function cn(){const e=Y(!1);return Qe(()=>{window.requestAnimationFrame(()=>{e.value=!0})}),{ssrBootStyles:m(()=>e.value?void 0:{transition:"none !important"}),isBooted:Xl(e)}}const St=L({transition:{type:[Boolean,String,Object],default:"fade-transition",validator:e=>e!==!0}},"transition"),Xe=(e,t)=>{let{slots:n}=t;const{transition:a,disabled:l,group:o,...i}=e,{component:r=o?Zl:Et,...u}=typeof a=="object"?a:{};return on(r,F(typeof a=="string"?{name:l?"":a}:u,typeof a=="string"?{}:Object.fromEntries(Object.entries({disabled:l,group:o}).filter(d=>{let[c,f]=d;return f!==void 0})),i),n)},Jv=rt({name:"VListGroupActivator",setup(e,t){let{slots:n}=t;return Zv(),()=>n.default?.()}}),Qv=L({activeColor:String,baseColor:String,color:String,collapseIcon:{type:ie,default:"$collapse"},expandIcon:{type:ie,default:"$expand"},prependIcon:ie,appendIcon:ie,fluid:Boolean,subgroup:Boolean,title:String,value:null,...X(),...re()},"VListGroup"),Pl=M()({name:"VListGroup",props:Qv(),setup(e,t){let{slots:n}=t;const{isOpen:a,open:l,id:o}=er(D(e,"value"),!0),i=m(()=>`v-list-group--id-${String(o.value)}`),r=qs(),{isBooted:u}=cn();function d(g){g.stopPropagation(),l(!a.value,g)}const c=m(()=>({onClick:d,class:"v-list-group__header",id:i.value})),f=m(()=>a.value?e.collapseIcon:e.expandIcon),v=m(()=>({VListItem:{active:a.value,activeColor:e.activeColor,baseColor:e.baseColor,color:e.color,prependIcon:e.prependIcon||e.subgroup&&f.value,appendIcon:e.appendIcon||!e.subgroup&&f.value,title:e.title,value:e.value}}));return O(()=>s(e.tag,{class:["v-list-group",{"v-list-group--prepend":r?.hasPrepend.value,"v-list-group--fluid":e.fluid,"v-list-group--subgroup":e.subgroup,"v-list-group--open":a.value},e.class],style:e.style},{default:()=>[n.activator&&s(ve,{defaults:v.value},{default:()=>[s(Jv,null,{default:()=>[n.activator({props:c.value,isOpen:a.value})]})]}),s(Xe,{transition:{component:Da},disabled:!u.value},{default:()=>[Ie(s("div",{class:"v-list-group__items",role:"group","aria-labelledby":i.value},[n.default?.()]),[[bt,a.value]])]})]})),{isOpen:a}}}),ef=L({opacity:[Number,String],...X(),...re()},"VListItemSubtitle"),tr=M()({name:"VListItemSubtitle",props:ef(),setup(e,t){let{slots:n}=t;return O(()=>s(e.tag,{class:["v-list-item-subtitle",e.class],style:[{"--v-list-item-subtitle-opacity":e.opacity},e.style]},n)),{}}}),nr=Pt("v-list-item-title"),Oe=L({height:[Number,String],maxHeight:[Number,String],maxWidth:[Number,String],minHeight:[Number,String],minWidth:[Number,String],width:[Number,String]},"dimension");function $e(e){return{dimensionStyles:m(()=>{const n={},a=K(e.height),l=K(e.maxHeight),o=K(e.maxWidth),i=K(e.minHeight),r=K(e.minWidth),u=K(e.width);return a!=null&&(n.height=a),l!=null&&(n.maxHeight=l),o!=null&&(n.maxWidth=o),i!=null&&(n.minHeight=i),r!=null&&(n.minWidth=r),u!=null&&(n.width=u),n})}}function tf(e){return{aspectStyles:m(()=>{const t=Number(e.aspectRatio);return t?{paddingBottom:String(1/t*100)+"%"}:void 0})}}const ar=L({aspectRatio:[String,Number],contentClass:null,inline:Boolean,...X(),...Oe()},"VResponsive"),Il=M()({name:"VResponsive",props:ar(),setup(e,t){let{slots:n}=t;const{aspectStyles:a}=tf(e),{dimensionStyles:l}=$e(e);return O(()=>s("div",{class:["v-responsive",{"v-responsive--inline":e.inline},e.class],style:[l.value,e.style]},[s("div",{class:"v-responsive__sizer",style:a.value},null),n.additional?.(),n.default&&s("div",{class:["v-responsive__content",e.contentClass]},[n.default()])])),{}}}),Ve=L({rounded:{type:[Boolean,Number,String],default:void 0},tile:Boolean},"rounded");function Le(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Vt();return{roundedClasses:m(()=>{const a=wn(e)?e.value:e.rounded,l=wn(e)?e.value:e.tile,o=[];if(a===!0||a==="")o.push(`${t}--rounded`);else if(typeof a=="string"||a===0)for(const i of String(a).split(" "))o.push(`rounded-${i}`);else(l||a===!1)&&o.push("rounded-0");return o})}}function nf(e,t){if(!Jl)return;const n=t.modifiers||{},a=t.value,{handler:l,options:o}=typeof a=="object"?a:{handler:a,options:{}},i=new IntersectionObserver(function(){let r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],u=arguments.length>1?arguments[1]:void 0;const d=e._observe?.[t.instance.$.uid];if(!d)return;const c=r.some(f=>f.isIntersecting);l&&(!n.quiet||d.init)&&(!n.once||c||d.init)&&l(c,r,u),c&&n.once?lr(e,t):d.init=!0},o);e._observe=Object(e._observe),e._observe[t.instance.$.uid]={init:!1,observer:i},i.observe(e)}function lr(e,t){const n=e._observe?.[t.instance.$.uid];n&&(n.observer.unobserve(e),delete e._observe[t.instance.$.uid])}const Ma={mounted:nf,unmounted:lr},or=L({absolute:Boolean,alt:String,cover:Boolean,color:String,draggable:{type:[Boolean,String],default:void 0},eager:Boolean,gradient:String,lazySrc:String,options:{type:Object,default:()=>({root:void 0,rootMargin:void 0,threshold:void 0})},sizes:String,src:{type:[String,Object],default:""},crossorigin:String,referrerpolicy:String,srcset:String,position:String,...ar(),...X(),...Ve(),...St()},"VImg"),_t=M()({name:"VImg",directives:{intersect:Ma},props:or(),emits:{loadstart:e=>!0,load:e=>!0,error:e=>!0},setup(e,t){let{emit:n,slots:a}=t;const{backgroundColorClasses:l,backgroundColorStyles:o}=Ce(D(e,"color")),{roundedClasses:i}=Le(e),r=_e("VImg"),u=Y(""),d=H(),c=Y(e.eager?"loading":"idle"),f=Y(),v=Y(),g=m(()=>e.src&&typeof e.src=="object"?{src:e.src.src,srcset:e.srcset||e.src.srcset,lazySrc:e.lazySrc||e.src.lazySrc,aspect:Number(e.aspectRatio||e.src.aspect||0)}:{src:e.src,srcset:e.srcset,lazySrc:e.lazySrc,aspect:Number(e.aspectRatio||0)}),h=m(()=>g.value.aspect||f.value/v.value||0);U(()=>e.src,()=>{b(c.value!=="idle")}),U(h,(_,B)=>{!_&&B&&d.value&&A(d.value)}),ls(()=>b());function b(_){if(!(e.eager&&_)&&!(Jl&&!_&&!e.eager)){if(c.value="loading",g.value.lazySrc){const B=new Image;B.src=g.value.lazySrc,A(B,null)}g.value.src&&ke(()=>{n("loadstart",d.value?.currentSrc||g.value.src),setTimeout(()=>{if(!r.isUnmounted)if(d.value?.complete){if(d.value.naturalWidth||S(),c.value==="error")return;h.value||A(d.value,null),c.value==="loading"&&y()}else h.value||A(d.value),w()})})}}function y(){r.isUnmounted||(w(),A(d.value),c.value="loaded",n("load",d.value?.currentSrc||g.value.src))}function S(){r.isUnmounted||(c.value="error",n("error",d.value?.currentSrc||g.value.src))}function w(){const _=d.value;_&&(u.value=_.currentSrc||_.src)}let p=-1;Je(()=>{clearTimeout(p)});function A(_){let B=arguments.length>1&&arguments[1]!==void 0?arguments[1]:100;const N=()=>{if(clearTimeout(p),r.isUnmounted)return;const{naturalHeight:z,naturalWidth:j}=_;z||j?(f.value=j,v.value=z):!_.complete&&c.value==="loading"&&B!=null?p=window.setTimeout(N,B):(_.currentSrc.endsWith(".svg")||_.currentSrc.startsWith("data:image/svg+xml"))&&(f.value=1,v.value=1)};N()}const V=m(()=>({"v-img__img--cover":e.cover,"v-img__img--contain":!e.cover})),x=()=>{if(!g.value.src||c.value==="idle")return null;const _=s("img",{class:["v-img__img",V.value],style:{objectPosition:e.position},src:g.value.src,srcset:g.value.srcset,alt:e.alt,crossorigin:e.crossorigin,referrerpolicy:e.referrerpolicy,draggable:e.draggable,sizes:e.sizes,ref:d,onLoad:y,onError:S},null),B=a.sources?.();return s(Xe,{transition:e.transition,appear:!0},{default:()=>[Ie(B?s("picture",{class:"v-img__picture"},[B,_]):_,[[bt,c.value==="loaded"]])]})},I=()=>s(Xe,{transition:e.transition},{default:()=>[g.value.lazySrc&&c.value!=="loaded"&&s("img",{class:["v-img__img","v-img__img--preload",V.value],style:{objectPosition:e.position},src:g.value.lazySrc,alt:e.alt,crossorigin:e.crossorigin,referrerpolicy:e.referrerpolicy,draggable:e.draggable},null)]}),k=()=>a.placeholder?s(Xe,{transition:e.transition,appear:!0},{default:()=>[(c.value==="loading"||c.value==="error"&&!a.error)&&s("div",{class:"v-img__placeholder"},[a.placeholder()])]}):null,C=()=>a.error?s(Xe,{transition:e.transition,appear:!0},{default:()=>[c.value==="error"&&s("div",{class:"v-img__error"},[a.error()])]}):null,T=()=>e.gradient?s("div",{class:"v-img__gradient",style:{backgroundImage:`linear-gradient(${e.gradient})`}},null):null,P=Y(!1);{const _=U(h,B=>{B&&(requestAnimationFrame(()=>{requestAnimationFrame(()=>{P.value=!0})}),_())})}return O(()=>{const _=Il.filterProps(e);return Ie(s(Il,F({class:["v-img",{"v-img--absolute":e.absolute,"v-img--booting":!P.value},l.value,i.value,e.class],style:[{width:K(e.width==="auto"?f.value:e.width)},o.value,e.style]},_,{aspectRatio:h.value,"aria-label":e.alt,role:e.alt?"img":void 0}),{additional:()=>s(ae,null,[s(x,null,null),s(I,null,null),s(T,null,null),s(k,null,null),s(C,null,null)]),default:a.default}),[[st("intersect"),{handler:b,options:e.options},null,{once:!0}]])}),{currentSrc:u,image:d,state:c,naturalWidth:f,naturalHeight:v}}}),lt=L({border:[Boolean,Number,String]},"border");function dt(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Vt();return{borderClasses:m(()=>{const a=wn(e)?e.value:e.border,l=[];if(a===!0||a==="")l.push(`${t}--border`);else if(typeof a=="string"||a===0)for(const o of String(a).split(" "))l.push(`border-${o}`);return l})}}const af=[null,"default","comfortable","compact"],ze=L({density:{type:String,default:"default",validator:e=>af.includes(e)}},"density");function et(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Vt();return{densityClasses:m(()=>`${t}--density-${e.density}`)}}const lf=["elevated","flat","tonal","outlined","text","plain"];function Rt(e,t){return s(ae,null,[e&&s("span",{key:"overlay",class:`${t}__overlay`},null),s("span",{key:"underlay",class:`${t}__underlay`},null)])}const mt=L({color:String,variant:{type:String,default:"elevated",validator:e=>lf.includes(e)}},"variant");function dn(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Vt();const n=m(()=>{const{variant:o}=tt(e);return`${t}--variant-${o}`}),{colorClasses:a,colorStyles:l}=mo(m(()=>{const{variant:o,color:i}=tt(e);return{[["elevated","flat"].includes(o)?"background":"text"]:i}}));return{colorClasses:a,colorStyles:l,variantClasses:n}}const of=L({start:Boolean,end:Boolean,icon:ie,image:String,text:String,...lt(),...X(),...ze(),...Ve(),...It(),...re(),...me(),...mt({variant:"flat"})},"VAvatar"),ft=M()({name:"VAvatar",props:of(),setup(e,t){let{slots:n}=t;const{themeClasses:a}=be(e),{borderClasses:l}=dt(e),{colorClasses:o,colorStyles:i,variantClasses:r}=dn(e),{densityClasses:u}=et(e),{roundedClasses:d}=Le(e),{sizeClasses:c,sizeStyles:f}=Tn(e);return O(()=>s(e.tag,{class:["v-avatar",{"v-avatar--start":e.start,"v-avatar--end":e.end},a.value,l.value,o.value,u.value,d.value,c.value,r.value,e.class],style:[i.value,f.value,e.style]},{default:()=>[n.default?s(ve,{key:"content-defaults",defaults:{VImg:{cover:!0,src:e.image},VIcon:{icon:e.icon}}},{default:()=>[n.default()]}):e.image?s(_t,{key:"image",src:e.image,alt:"",cover:!0},null):e.icon?s(ye,{key:"icon",icon:e.icon},null):e.text,Rt(!1,"v-avatar")]})),{}}}),Ne=L({elevation:{type:[Number,String],validator(e){const t=parseInt(e);return!isNaN(t)&&t>=0&&t<=24}}},"elevation");function Ye(e){return{elevationClasses:m(()=>{const n=wn(e)?e.value:e.elevation,a=[];return n==null||a.push(`elevation-${n}`),a})}}function sf(){const e=_e("useRoute");return m(()=>e?.proxy?.$route)}function ir(){return _e("useRouter")?.proxy?.$router}function Zn(e,t){const n=kc("RouterLink"),a=m(()=>!!(e.href||e.to)),l=m(()=>a?.value||oi(t,"click")||oi(e,"click"));if(typeof n=="string"||!("useLink"in n)){const f=D(e,"href");return{isLink:a,isClickable:l,href:f,linkProps:at({href:f})}}const o=m(()=>({...e,to:D(()=>e.to||"")})),i=n.useLink(o.value),r=m(()=>e.to?i:void 0),u=sf(),d=m(()=>r.value?e.exact?u.value?r.value.isExactActive?.value&&wt(r.value.route.value.query,u.value.query):r.value.isExactActive?.value??!1:r.value.isActive?.value??!1:!1),c=m(()=>e.to?r.value?.route.value.href:e.href);return{isLink:a,isClickable:l,isActive:d,route:r.value?.route,navigate:r.value?.navigate,href:c,linkProps:at({href:c,"aria-current":m(()=>d.value?"page":void 0)})}}const Jn=L({href:String,replace:Boolean,to:[String,Object],exact:Boolean},"router");let rl=!1;function rf(e,t){let n=!1,a,l;Se&&(ke(()=>{window.addEventListener("popstate",o),a=e?.beforeEach((i,r,u)=>{rl?n?t(u):u():setTimeout(()=>n?t(u):u()),rl=!0}),l=e?.afterEach(()=>{rl=!1})}),Ze(()=>{window.removeEventListener("popstate",o),a?.(),l?.()}));function o(i){i.state?.replaced||(n=!0,setTimeout(()=>n=!1))}}const pl=Symbol("rippleStop"),uf=80;function Ei(e,t){e.style.transform=t,e.style.webkitTransform=t}function _l(e){return e.constructor.name==="TouchEvent"}function sr(e){return e.constructor.name==="KeyboardEvent"}const cf=function(e,t){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},a=0,l=0;if(!sr(e)){const f=t.getBoundingClientRect(),v=_l(e)?e.touches[e.touches.length-1]:e;a=v.clientX-f.left,l=v.clientY-f.top}let o=0,i=.3;t._ripple?.circle?(i=.15,o=t.clientWidth/2,o=n.center?o:o+Math.sqrt((a-o)**2+(l-o)**2)/4):o=Math.sqrt(t.clientWidth**2+t.clientHeight**2)/2;const r=`${(t.clientWidth-o*2)/2}px`,u=`${(t.clientHeight-o*2)/2}px`,d=n.center?r:`${a-o}px`,c=n.center?u:`${l-o}px`;return{radius:o,scale:i,x:d,y:c,centerX:r,centerY:u}},Ca={show(e,t){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};if(!t?._ripple?.enabled)return;const a=document.createElement("span"),l=document.createElement("span");a.appendChild(l),a.className="v-ripple__container",n.class&&(a.className+=` ${n.class}`);const{radius:o,scale:i,x:r,y:u,centerX:d,centerY:c}=cf(e,t,n),f=`${o*2}px`;l.className="v-ripple__animation",l.style.width=f,l.style.height=f,t.appendChild(a);const v=window.getComputedStyle(t);v&&v.position==="static"&&(t.style.position="relative",t.dataset.previousPosition="static"),l.classList.add("v-ripple__animation--enter"),l.classList.add("v-ripple__animation--visible"),Ei(l,`translate(${r}, ${u}) scale3d(${i},${i},${i})`),l.dataset.activated=String(performance.now()),setTimeout(()=>{l.classList.remove("v-ripple__animation--enter"),l.classList.add("v-ripple__animation--in"),Ei(l,`translate(${d}, ${c}) scale3d(1,1,1)`)},0)},hide(e){if(!e?._ripple?.enabled)return;const t=e.getElementsByClassName("v-ripple__animation");if(t.length===0)return;const n=t[t.length-1];if(n.dataset.isHiding)return;n.dataset.isHiding="true";const a=performance.now()-Number(n.dataset.activated),l=Math.max(250-a,0);setTimeout(()=>{n.classList.remove("v-ripple__animation--in"),n.classList.add("v-ripple__animation--out"),setTimeout(()=>{e.getElementsByClassName("v-ripple__animation").length===1&&e.dataset.previousPosition&&(e.style.position=e.dataset.previousPosition,delete e.dataset.previousPosition),n.parentNode?.parentNode===e&&e.removeChild(n.parentNode)},300)},l)}};function rr(e){return typeof e>"u"||!!e}function Yn(e){const t={},n=e.currentTarget;if(!(!n?._ripple||n._ripple.touched||e[pl])){if(e[pl]=!0,_l(e))n._ripple.touched=!0,n._ripple.isTouch=!0;else if(n._ripple.isTouch)return;if(t.center=n._ripple.centered||sr(e),n._ripple.class&&(t.class=n._ripple.class),_l(e)){if(n._ripple.showTimerCommit)return;n._ripple.showTimerCommit=()=>{Ca.show(e,n,t)},n._ripple.showTimer=window.setTimeout(()=>{n?._ripple?.showTimerCommit&&(n._ripple.showTimerCommit(),n._ripple.showTimerCommit=null)},uf)}else Ca.show(e,n,t)}}function Fi(e){e[pl]=!0}function it(e){const t=e.currentTarget;if(t?._ripple){if(window.clearTimeout(t._ripple.showTimer),e.type==="touchend"&&t._ripple.showTimerCommit){t._ripple.showTimerCommit(),t._ripple.showTimerCommit=null,t._ripple.showTimer=window.setTimeout(()=>{it(e)});return}window.setTimeout(()=>{t._ripple&&(t._ripple.touched=!1)}),Ca.hide(t)}}function ur(e){const t=e.currentTarget;t?._ripple&&(t._ripple.showTimerCommit&&(t._ripple.showTimerCommit=null),window.clearTimeout(t._ripple.showTimer))}let Gn=!1;function cr(e){!Gn&&(e.keyCode===ei.enter||e.keyCode===ei.space)&&(Gn=!0,Yn(e))}function dr(e){Gn=!1,it(e)}function vr(e){Gn&&(Gn=!1,it(e))}function fr(e,t,n){const{value:a,modifiers:l}=t,o=rr(a);if(o||Ca.hide(e),e._ripple=e._ripple??{},e._ripple.enabled=o,e._ripple.centered=l.center,e._ripple.circle=l.circle,ss(a)&&a.class&&(e._ripple.class=a.class),o&&!n){if(l.stop){e.addEventListener("touchstart",Fi,{passive:!0}),e.addEventListener("mousedown",Fi);return}e.addEventListener("touchstart",Yn,{passive:!0}),e.addEventListener("touchend",it,{passive:!0}),e.addEventListener("touchmove",ur,{passive:!0}),e.addEventListener("touchcancel",it),e.addEventListener("mousedown",Yn),e.addEventListener("mouseup",it),e.addEventListener("mouseleave",it),e.addEventListener("keydown",cr),e.addEventListener("keyup",dr),e.addEventListener("blur",vr),e.addEventListener("dragstart",it,{passive:!0})}else!o&&n&&mr(e)}function mr(e){e.removeEventListener("mousedown",Yn),e.removeEventListener("touchstart",Yn),e.removeEventListener("touchend",it),e.removeEventListener("touchmove",ur),e.removeEventListener("touchcancel",it),e.removeEventListener("mouseup",it),e.removeEventListener("mouseleave",it),e.removeEventListener("keydown",cr),e.removeEventListener("keyup",dr),e.removeEventListener("dragstart",it),e.removeEventListener("blur",vr)}function df(e,t){fr(e,t,!1)}function vf(e){delete e._ripple,mr(e)}function ff(e,t){if(t.value===t.oldValue)return;const n=rr(t.oldValue);fr(e,t,n)}const Nt={mounted:df,unmounted:vf,updated:ff},mf=L({active:{type:Boolean,default:void 0},activeClass:String,activeColor:String,appendAvatar:String,appendIcon:ie,baseColor:String,disabled:Boolean,lines:[Boolean,String],link:{type:Boolean,default:void 0},nav:Boolean,prependAvatar:String,prependIcon:ie,ripple:{type:[Boolean,Object],default:!0},slim:Boolean,subtitle:[String,Number],title:[String,Number],value:null,onClick:We(),onClickOnce:We(),...lt(),...X(),...ze(),...Oe(),...Ne(),...Ve(),...Jn(),...re(),...me(),...mt({variant:"text"})},"VListItem"),At=M()({name:"VListItem",directives:{Ripple:Nt},props:mf(),emits:{click:e=>!0},setup(e,t){let{attrs:n,slots:a,emit:l}=t;const o=Zn(e,n),i=m(()=>e.value===void 0?o.href.value:e.value),{activate:r,isActivated:u,select:d,isOpen:c,isSelected:f,isIndeterminate:v,isGroupActivator:g,root:h,parent:b,openOnSelect:y,id:S}=er(i,!1),w=qs(),p=m(()=>e.active!==!1&&(e.active||o.isActive?.value||(h.activatable.value?u.value:f.value))),A=m(()=>e.link!==!1&&o.isLink.value),V=m(()=>!e.disabled&&e.link!==!1&&(e.link||o.isClickable.value||!!w&&(h.selectable.value||h.activatable.value||e.value!=null))),x=m(()=>e.rounded||e.nav),I=m(()=>e.color??e.activeColor),k=m(()=>({color:p.value?I.value??e.baseColor:e.baseColor,variant:e.variant}));U(()=>o.isActive?.value,q=>{q&&b.value!=null&&h.open(b.value,!0),q&&y(q)},{immediate:!0});const{themeClasses:C}=be(e),{borderClasses:T}=dt(e),{colorClasses:P,colorStyles:_,variantClasses:B}=dn(k),{densityClasses:N}=et(e),{dimensionStyles:z}=$e(e),{elevationClasses:j}=Ye(e),{roundedClasses:ee}=Le(x),Z=m(()=>e.lines?`v-list-item--${e.lines}-line`:void 0),$=m(()=>({isActive:p.value,select:d,isOpen:c.value,isSelected:f.value,isIndeterminate:v.value}));function E(q){l("click",q),V.value&&(o.navigate?.(q),!g&&(h.activatable.value?r(!u.value,q):(h.selectable.value||e.value!=null)&&d(!f.value,q)))}function R(q){(q.key==="Enter"||q.key===" ")&&(q.preventDefault(),q.target.dispatchEvent(new MouseEvent("click",q)))}return O(()=>{const q=A.value?"a":e.tag,ue=a.title||e.title!=null,le=a.subtitle||e.subtitle!=null,oe=!!(e.appendAvatar||e.appendIcon),W=!!(oe||a.append),se=!!(e.prependAvatar||e.prependIcon),ce=!!(se||a.prepend);return w?.updateHasPrepend(ce),e.activeColor&&Zc("active-color",["color","base-color"]),Ie(s(q,F({class:["v-list-item",{"v-list-item--active":p.value,"v-list-item--disabled":e.disabled,"v-list-item--link":V.value,"v-list-item--nav":e.nav,"v-list-item--prepend":!ce&&w?.hasPrepend.value,"v-list-item--slim":e.slim,[`${e.activeClass}`]:e.activeClass&&p.value},C.value,T.value,P.value,N.value,j.value,Z.value,ee.value,B.value,e.class],style:[_.value,z.value,e.style],tabindex:V.value?w?-2:0:void 0,onClick:E,onKeydown:V.value&&!A.value&&R},o.linkProps),{default:()=>[Rt(V.value||p.value,"v-list-item"),ce&&s("div",{key:"prepend",class:"v-list-item__prepend"},[a.prepend?s(ve,{key:"prepend-defaults",disabled:!se,defaults:{VAvatar:{density:e.density,image:e.prependAvatar},VIcon:{density:e.density,icon:e.prependIcon},VListItemAction:{start:!0}}},{default:()=>[a.prepend?.($.value)]}):s(ae,null,[e.prependAvatar&&s(ft,{key:"prepend-avatar",density:e.density,image:e.prependAvatar},null),e.prependIcon&&s(ye,{key:"prepend-icon",density:e.density,icon:e.prependIcon},null)]),s("div",{class:"v-list-item__spacer"},null)]),s("div",{class:"v-list-item__content","data-no-activator":""},[ue&&s(nr,{key:"title"},{default:()=>[a.title?.({title:e.title})??e.title]}),le&&s(tr,{key:"subtitle"},{default:()=>[a.subtitle?.({subtitle:e.subtitle})??e.subtitle]}),a.default?.($.value)]),W&&s("div",{key:"append",class:"v-list-item__append"},[a.append?s(ve,{key:"append-defaults",disabled:!oe,defaults:{VAvatar:{density:e.density,image:e.appendAvatar},VIcon:{density:e.density,icon:e.appendIcon},VListItemAction:{end:!0}}},{default:()=>[a.append?.($.value)]}):s(ae,null,[e.appendIcon&&s(ye,{key:"append-icon",density:e.density,icon:e.appendIcon},null),e.appendAvatar&&s(ft,{key:"append-avatar",density:e.density,image:e.appendAvatar},null)]),s("div",{class:"v-list-item__spacer"},null)])]}),[[st("ripple"),V.value&&e.ripple]])}),{activate:r,isActivated:u,isGroupActivator:g,isSelected:f,list:w,select:d,root:h,id:S}}}),gf=L({color:String,inset:Boolean,sticky:Boolean,title:String,...X(),...re()},"VListSubheader"),gr=M()({name:"VListSubheader",props:gf(),setup(e,t){let{slots:n}=t;const{textColorClasses:a,textColorStyles:l}=Ge(D(e,"color"));return O(()=>{const o=!!(n.default||e.title);return s(e.tag,{class:["v-list-subheader",{"v-list-subheader--inset":e.inset,"v-list-subheader--sticky":e.sticky},a.value,e.class],style:[{textColorStyles:l},e.style]},{default:()=>[o&&s("div",{class:"v-list-subheader__text"},[n.default?.()??e.title])]})}),{}}}),hf=L({color:String,inset:Boolean,length:[Number,String],opacity:[Number,String],thickness:[Number,String],vertical:Boolean,...X(),...me()},"VDivider"),Qn=M()({name:"VDivider",props:hf(),setup(e,t){let{attrs:n,slots:a}=t;const{themeClasses:l}=be(e),{textColorClasses:o,textColorStyles:i}=Ge(D(e,"color")),r=m(()=>{const u={};return e.length&&(u[e.vertical?"height":"width"]=K(e.length)),e.thickness&&(u[e.vertical?"borderRightWidth":"borderTopWidth"]=K(e.thickness)),u});return O(()=>{const u=s("hr",{class:[{"v-divider":!0,"v-divider--inset":e.inset,"v-divider--vertical":e.vertical},l.value,o.value,e.class],style:[r.value,i.value,{"--v-border-opacity":e.opacity},e.style],"aria-orientation":!n.role||n.role==="separator"?e.vertical?"vertical":"horizontal":void 0,role:`${n.role||"separator"}`},null);return a.default?s("div",{class:["v-divider__wrapper",{"v-divider__wrapper--vertical":e.vertical,"v-divider__wrapper--inset":e.inset}]},[u,s("div",{class:"v-divider__content"},[a.default()]),u]):u}),{}}}),yf=L({items:Array,returnObject:Boolean},"VListChildren"),hr=M()({name:"VListChildren",props:yf(),setup(e,t){let{slots:n}=t;return Ks(),()=>n.default?.()??e.items?.map(a=>{let{children:l,props:o,type:i,raw:r}=a;if(i==="divider")return n.divider?.({props:o})??s(Qn,o,null);if(i==="subheader")return n.subheader?.({props:o})??s(gr,o,null);const u={subtitle:n.subtitle?c=>n.subtitle?.({...c,item:r}):void 0,prepend:n.prepend?c=>n.prepend?.({...c,item:r}):void 0,append:n.append?c=>n.append?.({...c,item:r}):void 0,title:n.title?c=>n.title?.({...c,item:r}):void 0},d=Pl.filterProps(o);return l?s(Pl,F({value:o?.value},d),{activator:c=>{let{props:f}=c;const v={...o,...f,value:e.returnObject?r:o.value};return n.header?n.header({props:v}):s(At,v,u)},default:()=>s(hr,{items:l,returnObject:e.returnObject},n)}):n.item?n.item({props:o}):s(At,F(o,{value:e.returnObject?r:o.value}),u)})}}),yr=L({items:{type:Array,default:()=>[]},itemTitle:{type:[String,Array,Function],default:"title"},itemValue:{type:[String,Array,Function],default:"value"},itemChildren:{type:[Boolean,String,Array,Function],default:"children"},itemProps:{type:[Boolean,String,Array,Function],default:"props"},returnObject:Boolean,valueComparator:{type:Function,default:wt}},"list-items");function Mt(e,t){const n=Re(t,e.itemTitle,t),a=Re(t,e.itemValue,n),l=Re(t,e.itemChildren),o=e.itemProps===!0?typeof t=="object"&&t!=null&&!Array.isArray(t)?"children"in t?Ee(t,["children"]):t:void 0:Re(t,e.itemProps),i={title:n,value:a,...o};return{title:String(i.title??""),value:i.value,props:i,children:Array.isArray(l)?br(e,l):void 0,raw:t}}function br(e,t){const n=[];for(const a of t)n.push(Mt(e,a));return n}function ko(e){const t=m(()=>br(e,e.items)),n=m(()=>t.value.some(o=>o.value===null));function a(o){return n.value||(o=o.filter(i=>i!==null)),o.map(i=>e.returnObject&&typeof i=="string"?Mt(e,i):t.value.find(r=>e.valueComparator(i,r.value))||Mt(e,i))}function l(o){return e.returnObject?o.map(i=>{let{raw:r}=i;return r}):o.map(i=>{let{value:r}=i;return r})}return{items:t,transformIn:a,transformOut:l}}function bf(e){return typeof e=="string"||typeof e=="number"||typeof e=="boolean"}function Sf(e,t){const n=Re(t,e.itemType,"item"),a=bf(t)?t:Re(t,e.itemTitle),l=Re(t,e.itemValue,void 0),o=Re(t,e.itemChildren),i=e.itemProps===!0?Ee(t,["children"]):Re(t,e.itemProps),r={title:a,value:l,...i};return{type:n,title:r.title,value:r.value,props:r,children:n==="item"&&o?Sr(e,o):void 0,raw:t}}function Sr(e,t){const n=[];for(const a of t)n.push(Sf(e,a));return n}function kf(e){return{items:m(()=>Sr(e,e.items))}}const Cf=L({baseColor:String,activeColor:String,activeClass:String,bgColor:String,disabled:Boolean,expandIcon:String,collapseIcon:String,lines:{type:[Boolean,String],default:"one"},slim:Boolean,nav:Boolean,"onClick:open":We(),"onClick:select":We(),"onUpdate:opened":We(),...qv({selectStrategy:"single-leaf",openStrategy:"list"}),...lt(),...X(),...ze(),...Oe(),...Ne(),itemType:{type:String,default:"type"},...yr(),...Ve(),...re(),...me(),...mt({variant:"text"})},"VList"),Ea=M()({name:"VList",props:Cf(),emits:{"update:selected":e=>!0,"update:activated":e=>!0,"update:opened":e=>!0,"click:open":e=>!0,"click:activate":e=>!0,"click:select":e=>!0},setup(e,t){let{slots:n}=t;const{items:a}=kf(e),{themeClasses:l}=be(e),{backgroundColorClasses:o,backgroundColorStyles:i}=Ce(D(e,"bgColor")),{borderClasses:r}=dt(e),{densityClasses:u}=et(e),{dimensionStyles:d}=$e(e),{elevationClasses:c}=Ye(e),{roundedClasses:f}=Le(e),{children:v,open:g,parents:h,select:b,getPath:y}=Xv(e),S=m(()=>e.lines?`v-list--${e.lines}-line`:void 0),w=D(e,"activeColor"),p=D(e,"baseColor"),A=D(e,"color");Ks(),pe({VListGroup:{activeColor:w,baseColor:p,color:A,expandIcon:D(e,"expandIcon"),collapseIcon:D(e,"collapseIcon")},VListItem:{activeClass:D(e,"activeClass"),activeColor:w,baseColor:p,color:A,density:D(e,"density"),disabled:D(e,"disabled"),lines:D(e,"lines"),nav:D(e,"nav"),slim:D(e,"slim"),variant:D(e,"variant")}});const V=Y(!1),x=H();function I(B){V.value=!0}function k(B){V.value=!1}function C(B){!V.value&&!(B.relatedTarget&&x.value?.contains(B.relatedTarget))&&_()}function T(B){const N=B.target;if(!(!x.value||["INPUT","TEXTAREA"].includes(N.tagName))){if(B.key==="ArrowDown")_("next");else if(B.key==="ArrowUp")_("prev");else if(B.key==="Home")_("first");else if(B.key==="End")_("last");else return;B.preventDefault()}}function P(B){V.value=!0}function _(B){if(x.value)return Ut(x.value,B)}return O(()=>s(e.tag,{ref:x,class:["v-list",{"v-list--disabled":e.disabled,"v-list--nav":e.nav,"v-list--slim":e.slim},l.value,o.value,r.value,u.value,c.value,S.value,f.value,e.class],style:[i.value,d.value,e.style],tabindex:e.disabled||V.value?-1:0,role:"listbox","aria-activedescendant":void 0,onFocusin:I,onFocusout:k,onFocus:C,onKeydown:T,onMousedown:P},{default:()=>[s(hr,{items:a.value,returnObject:e.returnObject},n)]})),{open:g,select:b,focus:_,children:v,parents:h,getPath:y}}}),xf=Pt("v-list-img"),wf=L({start:Boolean,end:Boolean,...X(),...re()},"VListItemAction"),Vf=M()({name:"VListItemAction",props:wf(),setup(e,t){let{slots:n}=t;return O(()=>s(e.tag,{class:["v-list-item-action",{"v-list-item-action--start":e.start,"v-list-item-action--end":e.end},e.class],style:e.style},n)),{}}}),Pf=L({start:Boolean,end:Boolean,...X(),...re()},"VListItemMedia"),If=M()({name:"VListItemMedia",props:Pf(),setup(e,t){let{slots:n}=t;return O(()=>s(e.tag,{class:["v-list-item-media",{"v-list-item-media--start":e.start,"v-list-item-media--end":e.end},e.class],style:e.style},n)),{}}}),pf=L({text:String,onClick:We(),...X(),...me()},"VLabel"),Bn=M()({name:"VLabel",props:pf(),setup(e,t){let{slots:n}=t;return O(()=>s("label",{class:["v-label",{"v-label--clickable":!!e.onClick},e.class],style:e.style,onClick:e.onClick},[e.text,n.default?.()])),{}}}),kr=Symbol.for("vuetify:selection-control-group"),Co=L({color:String,disabled:{type:Boolean,default:null},defaultsTarget:String,error:Boolean,id:String,inline:Boolean,falseIcon:ie,trueIcon:ie,ripple:{type:[Boolean,Object],default:!0},multiple:{type:Boolean,default:null},name:String,readonly:{type:Boolean,default:null},modelValue:null,type:String,valueComparator:{type:Function,default:wt},...X(),...ze(),...me()},"SelectionControlGroup"),_f=L({...Co({defaultsTarget:"VSelectionControl"})},"VSelectionControlGroup"),Cr=M()({name:"VSelectionControlGroup",props:_f(),emits:{"update:modelValue":e=>!0},setup(e,t){let{slots:n}=t;const a=Q(e,"modelValue"),l=je(),o=m(()=>e.id||`v-selection-control-group-${l}`),i=m(()=>e.name||o.value),r=new Set;return we(kr,{modelValue:a,forceUpdate:()=>{r.forEach(u=>u())},onForceUpdate:u=>{r.add(u),Ze(()=>{r.delete(u)})}}),pe({[e.defaultsTarget]:{color:D(e,"color"),disabled:D(e,"disabled"),density:D(e,"density"),error:D(e,"error"),inline:D(e,"inline"),modelValue:a,multiple:m(()=>!!e.multiple||e.multiple==null&&Array.isArray(a.value)),name:i,falseIcon:D(e,"falseIcon"),trueIcon:D(e,"trueIcon"),readonly:D(e,"readonly"),ripple:D(e,"ripple"),type:D(e,"type"),valueComparator:D(e,"valueComparator")}}),O(()=>s("div",{class:["v-selection-control-group",{"v-selection-control-group--inline":e.inline},e.class],style:e.style,role:e.type==="radio"?"radiogroup":void 0},[n.default?.()])),{}}}),Fa=L({label:String,baseColor:String,trueValue:null,falseValue:null,value:null,...X(),...Co()},"VSelectionControl");function Af(e){const t=ge(kr,void 0),{densityClasses:n}=et(e),a=Q(e,"modelValue"),l=m(()=>e.trueValue!==void 0?e.trueValue:e.value!==void 0?e.value:!0),o=m(()=>e.falseValue!==void 0?e.falseValue:!1),i=m(()=>!!e.multiple||e.multiple==null&&Array.isArray(a.value)),r=m({get(){const g=t?t.modelValue.value:a.value;return i.value?Pe(g).some(h=>e.valueComparator(h,l.value)):e.valueComparator(g,l.value)},set(g){if(e.readonly)return;const h=g?l.value:o.value;let b=h;i.value&&(b=g?[...Pe(a.value),h]:Pe(a.value).filter(y=>!e.valueComparator(y,l.value))),t?t.modelValue.value=b:a.value=b}}),{textColorClasses:u,textColorStyles:d}=Ge(m(()=>{if(!(e.error||e.disabled))return r.value?e.color:e.baseColor})),{backgroundColorClasses:c,backgroundColorStyles:f}=Ce(m(()=>r.value&&!e.error&&!e.disabled?e.color:e.baseColor)),v=m(()=>r.value?e.trueIcon:e.falseIcon);return{group:t,densityClasses:n,trueValue:l,falseValue:o,model:r,textColorClasses:u,textColorStyles:d,backgroundColorClasses:c,backgroundColorStyles:f,icon:v}}const Ot=M()({name:"VSelectionControl",directives:{Ripple:Nt},inheritAttrs:!1,props:Fa(),emits:{"update:modelValue":e=>!0},setup(e,t){let{attrs:n,slots:a}=t;const{group:l,densityClasses:o,icon:i,model:r,textColorClasses:u,textColorStyles:d,backgroundColorClasses:c,backgroundColorStyles:f,trueValue:v}=Af(e),g=je(),h=Y(!1),b=Y(!1),y=H(),S=m(()=>e.id||`input-${g}`),w=m(()=>!e.disabled&&!e.readonly);l?.onForceUpdate(()=>{y.value&&(y.value.checked=r.value)});function p(I){w.value&&(h.value=!0,Vn(I.target,":focus-visible")!==!1&&(b.value=!0))}function A(){h.value=!1,b.value=!1}function V(I){I.stopPropagation()}function x(I){if(!w.value){y.value&&(y.value.checked=r.value);return}e.readonly&&l&&ke(()=>l.forceUpdate()),r.value=I.target.checked}return O(()=>{const I=a.label?a.label({label:e.label,props:{for:S.value}}):e.label,[k,C]=$t(n),T=s("input",F({ref:y,checked:r.value,disabled:!!e.disabled,id:S.value,onBlur:A,onFocus:p,onInput:x,"aria-disabled":!!e.disabled,"aria-label":e.label,type:e.type,value:v.value,name:e.name,"aria-checked":e.type==="checkbox"?r.value:void 0},C),null);return s("div",F({class:["v-selection-control",{"v-selection-control--dirty":r.value,"v-selection-control--disabled":e.disabled,"v-selection-control--error":e.error,"v-selection-control--focused":h.value,"v-selection-control--focus-visible":b.value,"v-selection-control--inline":e.inline},o.value,e.class]},k,{style:e.style}),[s("div",{class:["v-selection-control__wrapper",u.value],style:d.value},[a.default?.({backgroundColorClasses:c,backgroundColorStyles:f}),Ie(s("div",{class:["v-selection-control__input"]},[a.input?.({model:r,textColorClasses:u,textColorStyles:d,backgroundColorClasses:c,backgroundColorStyles:f,inputNode:T,icon:i.value,props:{onFocus:p,onBlur:A,id:S.value}})??s(ae,null,[i.value&&s(ye,{key:"icon",icon:i.value},null),T])]),[[st("ripple"),e.ripple&&[!e.disabled&&!e.readonly,null,["center","circle"]]]])]),I&&s(Bn,{for:S.value,onClick:V},{default:()=>[I]})])}),{isFocused:h,input:y}}}),xr=L({indeterminate:Boolean,indeterminateIcon:{type:ie,default:"$checkboxIndeterminate"},...Fa({falseIcon:"$checkboxOff",trueIcon:"$checkboxOn"})},"VCheckboxBtn"),Lt=M()({name:"VCheckboxBtn",props:xr(),emits:{"update:modelValue":e=>!0,"update:indeterminate":e=>!0},setup(e,t){let{slots:n}=t;const a=Q(e,"indeterminate"),l=Q(e,"modelValue");function o(u){a.value&&(a.value=!1)}const i=m(()=>a.value?e.indeterminateIcon:e.falseIcon),r=m(()=>a.value?e.indeterminateIcon:e.trueIcon);return O(()=>{const u=Ee(Ot.filterProps(e),["modelValue"]);return s(Ot,F(u,{modelValue:l.value,"onUpdate:modelValue":[d=>l.value=d,o],class:["v-checkbox-btn",e.class],style:e.style,type:"checkbox",falseIcon:i.value,trueIcon:r.value,"aria-checked":a.value?"mixed":void 0}),n)}),{}}});function wr(e){const{t}=Ae();function n(a){let{name:l}=a;const o={prepend:"prependAction",prependInner:"prependAction",append:"appendAction",appendInner:"appendAction",clear:"clear"}[l],i=e[`onClick:${l}`],r=i&&o?t(`$vuetify.input.${o}`,e.label??""):void 0;return s(ye,{icon:e[`${l}Icon`],"aria-label":r,onClick:i},null)}return{InputIcon:n}}const Lf=L({active:Boolean,color:String,messages:{type:[Array,String],default:()=>[]},...X(),...St({transition:{component:ho,leaveAbsolute:!0,group:!0}})},"VMessages"),Vr=M()({name:"VMessages",props:Lf(),setup(e,t){let{slots:n}=t;const a=m(()=>Pe(e.messages)),{textColorClasses:l,textColorStyles:o}=Ge(m(()=>e.color));return O(()=>s(Xe,{transition:e.transition,tag:"div",class:["v-messages",l.value,e.class],style:[o.value,e.style],role:"alert","aria-live":"polite"},{default:()=>[e.active&&a.value.map((i,r)=>s("div",{class:"v-messages__message",key:`${r}-${a.value}`},[n.message?n.message({message:i}):i]))]})),{}}}),ea=L({focused:Boolean,"onUpdate:focused":We()},"focus");function Tt(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Vt();const n=Q(e,"focused"),a=m(()=>({[`${t}--focused`]:n.value}));function l(){n.value=!0}function o(){n.value=!1}return{focusClasses:a,isFocused:n,focus:l,blur:o}}const Pr=Symbol.for("vuetify:form"),Tf=L({disabled:Boolean,fastFail:Boolean,readonly:Boolean,modelValue:{type:Boolean,default:null},validateOn:{type:String,default:"input"}},"form");function Bf(e){const t=Q(e,"modelValue"),n=m(()=>e.disabled),a=m(()=>e.readonly),l=Y(!1),o=H([]),i=H([]);async function r(){const c=[];let f=!0;i.value=[],l.value=!0;for(const v of o.value){const g=await v.validate();if(g.length>0&&(f=!1,c.push({id:v.id,errorMessages:g})),!f&&e.fastFail)break}return i.value=c,l.value=!1,{valid:f,errors:i.value}}function u(){o.value.forEach(c=>c.reset())}function d(){o.value.forEach(c=>c.resetValidation())}return U(o,()=>{let c=0,f=0;const v=[];for(const g of o.value)g.isValid===!1?(f++,v.push({id:g.id,errorMessages:g.errorMessages})):g.isValid===!0&&c++;i.value=v,t.value=f>0?!1:c===o.value.length?!0:null},{deep:!0,flush:"post"}),we(Pr,{register:c=>{let{id:f,vm:v,validate:g,reset:h,resetValidation:b}=c;o.value.some(y=>y.id===f),o.value.push({id:f,validate:g,reset:h,resetValidation:b,vm:Cc(v),isValid:null,errorMessages:[]})},unregister:c=>{o.value=o.value.filter(f=>f.id!==c)},update:(c,f,v)=>{const g=o.value.find(h=>h.id===c);g&&(g.isValid=f,g.errorMessages=v)},isDisabled:n,isReadonly:a,isValidating:l,isValid:t,items:o,validateOn:D(e,"validateOn")}),{errors:i,isDisabled:n,isReadonly:a,isValidating:l,isValid:t,items:o,validate:r,reset:u,resetValidation:d}}function Oa(){return ge(Pr,null)}const Ir=L({disabled:{type:Boolean,default:null},error:Boolean,errorMessages:{type:[Array,String],default:()=>[]},maxErrors:{type:[Number,String],default:1},name:String,label:String,readonly:{type:Boolean,default:null},rules:{type:Array,default:()=>[]},modelValue:null,validateOn:String,validationValue:null,...ea()},"validation");function pr(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Vt(),n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:je();const a=Q(e,"modelValue"),l=m(()=>e.validationValue===void 0?a.value:e.validationValue),o=Oa(),i=H([]),r=Y(!0),u=m(()=>!!(Pe(a.value===""?null:a.value).length||Pe(l.value===""?null:l.value).length)),d=m(()=>!!(e.disabled??o?.isDisabled.value)),c=m(()=>!!(e.readonly??o?.isReadonly.value)),f=m(()=>e.errorMessages?.length?Pe(e.errorMessages).concat(i.value).slice(0,Math.max(0,+e.maxErrors)):i.value),v=m(()=>{let V=(e.validateOn??o?.validateOn.value)||"input";V==="lazy"&&(V="input lazy"),V==="eager"&&(V="input eager");const x=new Set(V?.split(" ")??[]);return{input:x.has("input"),blur:x.has("blur")||x.has("input")||x.has("invalid-input"),invalidInput:x.has("invalid-input"),lazy:x.has("lazy"),eager:x.has("eager")}}),g=m(()=>e.error||e.errorMessages?.length?!1:e.rules.length?r.value?i.value.length||v.value.lazy?null:!0:!i.value.length:!0),h=Y(!1),b=m(()=>({[`${t}--error`]:g.value===!1,[`${t}--dirty`]:u.value,[`${t}--disabled`]:d.value,[`${t}--readonly`]:c.value})),y=_e("validation"),S=m(()=>e.name??tt(n));ls(()=>{o?.register({id:S.value,vm:y,validate:A,reset:w,resetValidation:p})}),Je(()=>{o?.unregister(S.value)}),Qe(async()=>{v.value.lazy||await A(!v.value.eager),o?.update(S.value,g.value,f.value)}),nt(()=>v.value.input||v.value.invalidInput&&g.value===!1,()=>{U(l,()=>{if(l.value!=null)A();else if(e.focused){const V=U(()=>e.focused,x=>{x||A(),V()})}})}),nt(()=>v.value.blur,()=>{U(()=>e.focused,V=>{V||A()})}),U([g,f],()=>{o?.update(S.value,g.value,f.value)});async function w(){a.value=null,await ke(),await p()}async function p(){r.value=!0,v.value.lazy?i.value=[]:await A(!v.value.eager)}async function A(){let V=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1;const x=[];h.value=!0;for(const I of e.rules){if(x.length>=+(e.maxErrors??1))break;const C=await(typeof I=="function"?I:()=>I)(l.value);if(C!==!0){if(C!==!1&&typeof C!="string"){console.warn(`${C} is not a valid value. Rule functions must return boolean true or a string.`);continue}x.push(C||"")}}return i.value=x,h.value=!1,r.value=V,i.value}return{errorMessages:f,isDirty:u,isDisabled:d,isReadonly:c,isPristine:r,isValid:g,isValidating:h,reset:w,resetValidation:p,validate:A,validationClasses:b}}const Bt=L({id:String,appendIcon:ie,centerAffix:{type:Boolean,default:!0},prependIcon:ie,hideDetails:[Boolean,String],hideSpinButtons:Boolean,hint:String,persistentHint:Boolean,messages:{type:[Array,String],default:()=>[]},direction:{type:String,default:"horizontal",validator:e=>["horizontal","vertical"].includes(e)},"onClick:prepend":We(),"onClick:append":We(),...X(),...ze(),...Va(Oe(),["maxWidth","minWidth","width"]),...me(),...Ir()},"VInput"),Ue=M()({name:"VInput",props:{...Bt()},emits:{"update:modelValue":e=>!0},setup(e,t){let{attrs:n,slots:a,emit:l}=t;const{densityClasses:o}=et(e),{dimensionStyles:i}=$e(e),{themeClasses:r}=be(e),{rtlClasses:u}=Fe(),{InputIcon:d}=wr(e),c=je(),f=m(()=>e.id||`input-${c}`),v=m(()=>`${f.value}-messages`),{errorMessages:g,isDirty:h,isDisabled:b,isReadonly:y,isPristine:S,isValid:w,isValidating:p,reset:A,resetValidation:V,validate:x,validationClasses:I}=pr(e,"v-input",f),k=m(()=>({id:f,messagesId:v,isDirty:h,isDisabled:b,isReadonly:y,isPristine:S,isValid:w,isValidating:p,reset:A,resetValidation:V,validate:x})),C=m(()=>e.errorMessages?.length||!S.value&&g.value.length?g.value:e.hint&&(e.persistentHint||e.focused)?e.hint:e.messages);return O(()=>{const T=!!(a.prepend||e.prependIcon),P=!!(a.append||e.appendIcon),_=C.value.length>0,B=!e.hideDetails||e.hideDetails==="auto"&&(_||!!a.details);return s("div",{class:["v-input",`v-input--${e.direction}`,{"v-input--center-affix":e.centerAffix,"v-input--hide-spin-buttons":e.hideSpinButtons},o.value,r.value,u.value,I.value,e.class],style:[i.value,e.style]},[T&&s("div",{key:"prepend",class:"v-input__prepend"},[a.prepend?.(k.value),e.prependIcon&&s(d,{key:"prepend-icon",name:"prepend"},null)]),a.default&&s("div",{class:"v-input__control"},[a.default?.(k.value)]),P&&s("div",{key:"append",class:"v-input__append"},[e.appendIcon&&s(d,{key:"append-icon",name:"append"},null),a.append?.(k.value)]),B&&s("div",{class:"v-input__details"},[s(Vr,{id:v.value,active:_,messages:C.value},{message:a.message}),a.details?.(k.value)])])}),{reset:A,resetValidation:V,validate:x,isValid:w,errorMessages:g}}}),Df=L({...Bt(),...Ee(xr(),["inline"])},"VCheckbox"),Mf=M()({name:"VCheckbox",inheritAttrs:!1,props:Df(),emits:{"update:modelValue":e=>!0,"update:focused":e=>!0},setup(e,t){let{attrs:n,slots:a}=t;const l=Q(e,"modelValue"),{isFocused:o,focus:i,blur:r}=Tt(e),u=je(),d=m(()=>e.id||`checkbox-${u}`);return O(()=>{const[c,f]=$t(n),v=Ue.filterProps(e),g=Lt.filterProps(e);return s(Ue,F({class:["v-checkbox",e.class]},c,v,{modelValue:l.value,"onUpdate:modelValue":h=>l.value=h,id:d.value,focused:o.value,style:e.style}),{...a,default:h=>{let{id:b,messagesId:y,isDisabled:S,isReadonly:w,isValid:p}=h;return s(Lt,F(g,{id:b.value,"aria-describedby":y.value,disabled:S.value,readonly:w.value},f,{error:p.value===!1,modelValue:l.value,"onUpdate:modelValue":A=>l.value=A,onFocus:i,onBlur:r}),a)}})}),{}}}),vn=L({modelValue:{type:null,default:void 0},multiple:Boolean,mandatory:[Boolean,String],max:Number,selectedClass:String,disabled:Boolean},"group"),fn=L({value:null,disabled:Boolean,selectedClass:String},"group-item");function mn(e,t){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;const a=_e("useGroupItem");if(!a)throw new Error("[Vuetify] useGroupItem composable must be used inside a component setup function");const l=je();we(Symbol.for(`${t.description}:id`),l);const o=ge(t,null);if(!o){if(!n)return o;throw new Error(`[Vuetify] Could not find useGroup injection with symbol ${t.description}`)}const i=D(e,"value"),r=m(()=>!!(o.disabled.value||e.disabled));o.register({id:l,value:i,disabled:r},a),Je(()=>{o.unregister(l)});const u=m(()=>o.isSelected(l)),d=m(()=>o.items.value[0].id===l),c=m(()=>o.items.value[o.items.value.length-1].id===l),f=m(()=>u.value&&[o.selectedClass.value,e.selectedClass]);return U(u,v=>{a.emit("group:selected",{value:v})},{flush:"sync"}),{id:l,isSelected:u,isFirst:d,isLast:c,toggle:()=>o.select(l,!u.value),select:v=>o.select(l,v),selectedClass:f,value:i,disabled:r,group:o}}function Ht(e,t){let n=!1;const a=at([]),l=Q(e,"modelValue",[],v=>v==null?[]:_r(a,Pe(v)),v=>{const g=Ff(a,v);return e.multiple?g:g[0]}),o=_e("useGroup");function i(v,g){const h=v,b=Symbol.for(`${t.description}:id`),S=Cn(b,o?.vnode).indexOf(g);tt(h.value)==null&&(h.value=S,h.useIndexAsValue=!0),S>-1?a.splice(S,0,h):a.push(h)}function r(v){if(n)return;u();const g=a.findIndex(h=>h.id===v);a.splice(g,1)}function u(){const v=a.find(g=>!g.disabled);v&&e.mandatory==="force"&&!l.value.length&&(l.value=[v.id])}Qe(()=>{u()}),Je(()=>{n=!0}),xc(()=>{for(let v=0;v<a.length;v++)a[v].useIndexAsValue&&(a[v].value=v)});function d(v,g){const h=a.find(b=>b.id===v);if(!(g&&h?.disabled))if(e.multiple){const b=l.value.slice(),y=b.findIndex(w=>w===v),S=~y;if(g=g??!S,S&&e.mandatory&&b.length<=1||!S&&e.max!=null&&b.length+1>e.max)return;y<0&&g?b.push(v):y>=0&&!g&&b.splice(y,1),l.value=b}else{const b=l.value.includes(v);if(e.mandatory&&b)return;l.value=g??!b?[v]:[]}}function c(v){if(e.multiple,l.value.length){const g=l.value[0],h=a.findIndex(S=>S.id===g);let b=(h+v)%a.length,y=a[b];for(;y.disabled&&b!==h;)b=(b+v)%a.length,y=a[b];if(y.disabled)return;l.value=[a[b].id]}else{const g=a.find(h=>!h.disabled);g&&(l.value=[g.id])}}const f={register:i,unregister:r,selected:l,select:d,disabled:D(e,"disabled"),prev:()=>c(a.length-1),next:()=>c(1),isSelected:v=>l.value.includes(v),selectedClass:m(()=>e.selectedClass),items:m(()=>a),getItemIndex:v=>Ef(a,v)};return we(t,f),f}function Ef(e,t){const n=_r(e,[t]);return n.length?e.findIndex(a=>a.id===n[0]):-1}function _r(e,t){const n=[];return t.forEach(a=>{const l=e.find(i=>wt(a,i.value)),o=e[a];l?.value!=null?n.push(l.id):o!=null&&n.push(o.id)}),n}function Ff(e,t){const n=[];return t.forEach(a=>{const l=e.findIndex(o=>o.id===a);if(~l){const o=e[l];n.push(o.value!=null?o.value:l)}}),n}function Of(e){let{selectedElement:t,containerElement:n,isRtl:a,isHorizontal:l}=e;const o=Un(l,n),i=Ar(l,a,n),r=Un(l,t),u=Lr(l,t),d=r*.4;return i>u?u-d:i+o<u+r?u-o+r+d:i}function $f(e){let{selectedElement:t,containerElement:n,isHorizontal:a}=e;const l=Un(a,n),o=Lr(a,t),i=Un(a,t);return o-l/2+i/2}function Oi(e,t){return t?.[e?"scrollWidth":"scrollHeight"]||0}function Rf(e,t){return t?.[e?"clientWidth":"clientHeight"]||0}function Ar(e,t,n){if(!n)return 0;const{scrollLeft:a,offsetWidth:l,scrollWidth:o}=n;return e?t?o-l+a:a:n.scrollTop}function Un(e,t){return t?.[e?"offsetWidth":"offsetHeight"]||0}function Lr(e,t){return t?.[e?"offsetLeft":"offsetTop"]||0}const Tr=Symbol.for("vuetify:v-slide-group"),xo=L({centerActive:Boolean,direction:{type:String,default:"horizontal"},symbol:{type:null,default:Tr},nextIcon:{type:ie,default:"$next"},prevIcon:{type:ie,default:"$prev"},showArrows:{type:[Boolean,String],validator:e=>typeof e=="boolean"||["always","desktop","mobile"].includes(e)},...X(),...sn({mobile:null}),...re(),...vn({selectedClass:"v-slide-group-item--active"})},"VSlideGroup"),Kn=M()({name:"VSlideGroup",props:xo(),emits:{"update:modelValue":e=>!0},setup(e,t){let{slots:n}=t;const{isRtl:a}=Fe(),{displayClasses:l,mobile:o}=ut(e),i=Ht(e,e.symbol),r=Y(!1),u=Y(0),d=Y(0),c=Y(0),f=m(()=>e.direction==="horizontal"),{resizeRef:v,contentRect:g}=yt(),{resizeRef:h,contentRect:b}=yt(),y=Ns(),S=m(()=>({container:v.el,duration:200,easing:"easeOutQuart"})),w=m(()=>i.selected.value.length?i.items.value.findIndex(E=>E.id===i.selected.value[0]):-1),p=m(()=>i.selected.value.length?i.items.value.findIndex(E=>E.id===i.selected.value[i.selected.value.length-1]):-1);if(Se){let E=-1;U(()=>[i.selected.value,g.value,b.value,f.value],()=>{cancelAnimationFrame(E),E=requestAnimationFrame(()=>{if(g.value&&b.value){const R=f.value?"width":"height";d.value=g.value[R],c.value=b.value[R],r.value=d.value+1<c.value}if(w.value>=0&&h.el){const R=h.el.children[p.value];V(R,e.centerActive)}})})}const A=Y(!1);function V(E,R){let q=0;R?q=$f({containerElement:v.el,isHorizontal:f.value,selectedElement:E}):q=Of({containerElement:v.el,isHorizontal:f.value,isRtl:a.value,selectedElement:E}),x(q)}function x(E){if(!Se||!v.el)return;const R=Un(f.value,v.el),q=Ar(f.value,a.value,v.el);if(!(Oi(f.value,v.el)<=R||Math.abs(E-q)<16)){if(f.value&&a.value&&v.el){const{scrollWidth:le,offsetWidth:oe}=v.el;E=le-oe-E}f.value?y.horizontal(E,S.value):y(E,S.value)}}function I(E){const{scrollTop:R,scrollLeft:q}=E.target;u.value=f.value?q:R}function k(E){if(A.value=!0,!(!r.value||!h.el)){for(const R of E.composedPath())for(const q of h.el.children)if(q===R){V(q);return}}}function C(E){A.value=!1}let T=!1;function P(E){!T&&!A.value&&!(E.relatedTarget&&h.el?.contains(E.relatedTarget))&&N(),T=!1}function _(){T=!0}function B(E){if(!h.el)return;function R(q){E.preventDefault(),N(q)}f.value?E.key==="ArrowRight"?R(a.value?"prev":"next"):E.key==="ArrowLeft"&&R(a.value?"next":"prev"):E.key==="ArrowDown"?R("next"):E.key==="ArrowUp"&&R("prev"),E.key==="Home"?R("first"):E.key==="End"&&R("last")}function N(E){if(!h.el)return;let R;if(!E)R=Rn(h.el)[0];else if(E==="next"){if(R=h.el.querySelector(":focus")?.nextElementSibling,!R)return N("first")}else if(E==="prev"){if(R=h.el.querySelector(":focus")?.previousElementSibling,!R)return N("last")}else E==="first"?R=h.el.firstElementChild:E==="last"&&(R=h.el.lastElementChild);R&&R.focus({preventScroll:!0})}function z(E){const R=f.value&&a.value?-1:1,q=(E==="prev"?-R:R)*d.value;let ue=u.value+q;if(f.value&&a.value&&v.el){const{scrollWidth:le,offsetWidth:oe}=v.el;ue+=le-oe}x(ue)}const j=m(()=>({next:i.next,prev:i.prev,select:i.select,isSelected:i.isSelected})),ee=m(()=>{switch(e.showArrows){case"always":return!0;case"desktop":return!o.value;case!0:return r.value||Math.abs(u.value)>0;case"mobile":return o.value||r.value||Math.abs(u.value)>0;default:return!o.value&&(r.value||Math.abs(u.value)>0)}}),Z=m(()=>Math.abs(u.value)>1),$=m(()=>{if(!v.value)return!1;const E=Oi(f.value,v.el),R=Rf(f.value,v.el);return E-R-Math.abs(u.value)>1});return O(()=>s(e.tag,{class:["v-slide-group",{"v-slide-group--vertical":!f.value,"v-slide-group--has-affixes":ee.value,"v-slide-group--is-overflowing":r.value},l.value,e.class],style:e.style,tabindex:A.value||i.selected.value.length?-1:0,onFocus:P},{default:()=>[ee.value&&s("div",{key:"prev",class:["v-slide-group__prev",{"v-slide-group__prev--disabled":!Z.value}],onMousedown:_,onClick:()=>Z.value&&z("prev")},[n.prev?.(j.value)??s(Wn,null,{default:()=>[s(ye,{icon:a.value?e.nextIcon:e.prevIcon},null)]})]),s("div",{key:"container",ref:v,class:"v-slide-group__container",onScroll:I},[s("div",{ref:h,class:"v-slide-group__content",onFocusin:k,onFocusout:C,onKeydown:B},[n.default?.(j.value)])]),ee.value&&s("div",{key:"next",class:["v-slide-group__next",{"v-slide-group__next--disabled":!$.value}],onMousedown:_,onClick:()=>$.value&&z("next")},[n.next?.(j.value)??s(Wn,null,{default:()=>[s(ye,{icon:a.value?e.prevIcon:e.nextIcon},null)]})])]})),{selected:i.selected,scrollTo:z,scrollOffset:u,focus:N,hasPrev:Z,hasNext:$}}}),Br=Symbol.for("vuetify:v-chip-group"),Nf=L({column:Boolean,filter:Boolean,valueComparator:{type:Function,default:wt},...xo(),...X(),...vn({selectedClass:"v-chip--selected"}),...re(),...me(),...mt({variant:"tonal"})},"VChipGroup"),Hf=M()({name:"VChipGroup",props:Nf(),emits:{"update:modelValue":e=>!0},setup(e,t){let{slots:n}=t;const{themeClasses:a}=be(e),{isSelected:l,select:o,next:i,prev:r,selected:u}=Ht(e,Br);return pe({VChip:{color:D(e,"color"),disabled:D(e,"disabled"),filter:D(e,"filter"),variant:D(e,"variant")}}),O(()=>{const d=Kn.filterProps(e);return s(Kn,F(d,{class:["v-chip-group",{"v-chip-group--column":e.column},a.value,e.class],style:e.style}),{default:()=>[n.default?.({isSelected:l,select:o,next:i,prev:r,selected:u.value})]})}),{}}}),zf=L({activeClass:String,appendAvatar:String,appendIcon:ie,closable:Boolean,closeIcon:{type:ie,default:"$delete"},closeLabel:{type:String,default:"$vuetify.close"},draggable:Boolean,filter:Boolean,filterIcon:{type:String,default:"$complete"},label:Boolean,link:{type:Boolean,default:void 0},pill:Boolean,prependAvatar:String,prependIcon:ie,ripple:{type:[Boolean,Object],default:!0},text:String,modelValue:{type:Boolean,default:!0},onClick:We(),onClickOnce:We(),...lt(),...X(),...ze(),...Ne(),...fn(),...Ve(),...Jn(),...It(),...re({tag:"span"}),...me(),...mt({variant:"tonal"})},"VChip"),Dn=M()({name:"VChip",directives:{Ripple:Nt},props:zf(),emits:{"click:close":e=>!0,"update:modelValue":e=>!0,"group:selected":e=>!0,click:e=>!0},setup(e,t){let{attrs:n,emit:a,slots:l}=t;const{t:o}=Ae(),{borderClasses:i}=dt(e),{colorClasses:r,colorStyles:u,variantClasses:d}=dn(e),{densityClasses:c}=et(e),{elevationClasses:f}=Ye(e),{roundedClasses:v}=Le(e),{sizeClasses:g}=Tn(e),{themeClasses:h}=be(e),b=Q(e,"modelValue"),y=mn(e,Br,!1),S=Zn(e,n),w=m(()=>e.link!==!1&&S.isLink.value),p=m(()=>!e.disabled&&e.link!==!1&&(!!y||e.link||S.isClickable.value)),A=m(()=>({"aria-label":o(e.closeLabel),onClick(I){I.preventDefault(),I.stopPropagation(),b.value=!1,a("click:close",I)}}));function V(I){a("click",I),p.value&&(S.navigate?.(I),y?.toggle())}function x(I){(I.key==="Enter"||I.key===" ")&&(I.preventDefault(),V(I))}return()=>{const I=S.isLink.value?"a":e.tag,k=!!(e.appendIcon||e.appendAvatar),C=!!(k||l.append),T=!!(l.close||e.closable),P=!!(l.filter||e.filter)&&y,_=!!(e.prependIcon||e.prependAvatar),B=!!(_||l.prepend),N=!y||y.isSelected.value;return b.value&&Ie(s(I,F({class:["v-chip",{"v-chip--disabled":e.disabled,"v-chip--label":e.label,"v-chip--link":p.value,"v-chip--filter":P,"v-chip--pill":e.pill},h.value,i.value,N?r.value:void 0,c.value,f.value,v.value,g.value,d.value,y?.selectedClass.value,e.class],style:[N?u.value:void 0,e.style],disabled:e.disabled||void 0,draggable:e.draggable,tabindex:p.value?0:void 0,onClick:V,onKeydown:p.value&&!w.value&&x},S.linkProps),{default:()=>[Rt(p.value,"v-chip"),P&&s(yo,{key:"filter"},{default:()=>[Ie(s("div",{class:"v-chip__filter"},[l.filter?s(ve,{key:"filter-defaults",disabled:!e.filterIcon,defaults:{VIcon:{icon:e.filterIcon}}},l.filter):s(ye,{key:"filter-icon",icon:e.filterIcon},null)]),[[bt,y.isSelected.value]])]}),B&&s("div",{key:"prepend",class:"v-chip__prepend"},[l.prepend?s(ve,{key:"prepend-defaults",disabled:!_,defaults:{VAvatar:{image:e.prependAvatar,start:!0},VIcon:{icon:e.prependIcon,start:!0}}},l.prepend):s(ae,null,[e.prependIcon&&s(ye,{key:"prepend-icon",icon:e.prependIcon,start:!0},null),e.prependAvatar&&s(ft,{key:"prepend-avatar",image:e.prependAvatar,start:!0},null)])]),s("div",{class:"v-chip__content","data-no-activator":""},[l.default?.({isSelected:y?.isSelected.value,selectedClass:y?.selectedClass.value,select:y?.select,toggle:y?.toggle,value:y?.value.value,disabled:e.disabled})??e.text]),C&&s("div",{key:"append",class:"v-chip__append"},[l.append?s(ve,{key:"append-defaults",disabled:!k,defaults:{VAvatar:{end:!0,image:e.appendAvatar},VIcon:{end:!0,icon:e.appendIcon}}},l.append):s(ae,null,[e.appendIcon&&s(ye,{key:"append-icon",end:!0,icon:e.appendIcon},null),e.appendAvatar&&s(ft,{key:"append-avatar",end:!0,image:e.appendAvatar},null)])]),T&&s("button",F({key:"close",class:"v-chip__close",type:"button"},A.value),[l.close?s(ve,{key:"close-defaults",defaults:{VIcon:{icon:e.closeIcon,size:"x-small"}}},l.close):s(ye,{key:"close-icon",icon:e.closeIcon,size:"x-small"},null)])]}),[[st("ripple"),p.value&&e.ripple,null]])}}});function ul(e,t){return{x:e.x+t.x,y:e.y+t.y}}function Wf(e,t){return{x:e.x-t.x,y:e.y-t.y}}function $i(e,t){if(e.side==="top"||e.side==="bottom"){const{side:n,align:a}=e,l=a==="left"?0:a==="center"?t.width/2:a==="right"?t.width:a,o=n==="top"?0:n==="bottom"?t.height:n;return ul({x:l,y:o},t)}else if(e.side==="left"||e.side==="right"){const{side:n,align:a}=e,l=n==="left"?0:n==="right"?t.width:n,o=a==="top"?0:a==="center"?t.height/2:a==="bottom"?t.height:a;return ul({x:l,y:o},t)}return ul({x:t.width/2,y:t.height/2},t)}const Dr={static:Gf,connected:Kf},jf=L({locationStrategy:{type:[String,Function],default:"static",validator:e=>typeof e=="function"||e in Dr},location:{type:String,default:"bottom"},origin:{type:String,default:"auto"},offset:[Number,String,Array]},"VOverlay-location-strategies");function Yf(e,t){const n=H({}),a=H();Se&&nt(()=>!!(t.isActive.value&&e.locationStrategy),o=>{U(()=>e.locationStrategy,o),Ze(()=>{window.removeEventListener("resize",l),a.value=void 0}),window.addEventListener("resize",l,{passive:!0}),typeof e.locationStrategy=="function"?a.value=e.locationStrategy(t,e,n)?.updateLocation:a.value=Dr[e.locationStrategy](t,e,n)?.updateLocation});function l(o){a.value?.(o)}return{contentStyles:n,updateLocation:a}}function Gf(){}function Uf(e,t){const n=ao(e);return t?n.x+=parseFloat(e.style.right||0):n.x-=parseFloat(e.style.left||0),n.y-=parseFloat(e.style.top||0),n}function Kf(e,t,n){(Array.isArray(e.target.value)||bd(e.target.value))&&Object.assign(n.value,{position:"fixed",top:0,[e.isRtl.value?"right":"left"]:0});const{preferredAnchor:l,preferredOrigin:o}=to(()=>{const h=hl(t.location,e.isRtl.value),b=t.origin==="overlap"?h:t.origin==="auto"?ll(h):hl(t.origin,e.isRtl.value);return h.side===b.side&&h.align===ol(b).align?{preferredAnchor:ii(h),preferredOrigin:ii(b)}:{preferredAnchor:h,preferredOrigin:b}}),[i,r,u,d]=["minWidth","minHeight","maxWidth","maxHeight"].map(h=>m(()=>{const b=parseFloat(t[h]);return isNaN(b)?1/0:b})),c=m(()=>{if(Array.isArray(t.offset))return t.offset;if(typeof t.offset=="string"){const h=t.offset.split(" ").map(parseFloat);return h.length<2&&h.push(0),h}return typeof t.offset=="number"?[t.offset,0]:[0,0]});let f=!1;const v=new ResizeObserver(()=>{f&&g()});U([e.target,e.contentEl],(h,b)=>{let[y,S]=h,[w,p]=b;w&&!Array.isArray(w)&&v.unobserve(w),y&&!Array.isArray(y)&&v.observe(y),p&&v.unobserve(p),S&&v.observe(S)},{immediate:!0}),Ze(()=>{v.disconnect()});function g(){if(f=!1,requestAnimationFrame(()=>f=!0),!e.target.value||!e.contentEl.value)return;const h=gs(e.target.value),b=Uf(e.contentEl.value,e.isRtl.value),y=Sa(e.contentEl.value),S=12;y.length||(y.push(document.documentElement),e.contentEl.value.style.top&&e.contentEl.value.style.left||(b.x-=parseFloat(document.documentElement.style.getPropertyValue("--v-body-scroll-x")||0),b.y-=parseFloat(document.documentElement.style.getPropertyValue("--v-body-scroll-y")||0)));const w=y.reduce((P,_)=>{const B=_.getBoundingClientRect(),N=new Kt({x:_===document.documentElement?0:B.x,y:_===document.documentElement?0:B.y,width:_.clientWidth,height:_.clientHeight});return P?new Kt({x:Math.max(P.left,N.left),y:Math.max(P.top,N.top),width:Math.min(P.right,N.right)-Math.max(P.left,N.left),height:Math.min(P.bottom,N.bottom)-Math.max(P.top,N.top)}):N},void 0);w.x+=S,w.y+=S,w.width-=S*2,w.height-=S*2;let p={anchor:l.value,origin:o.value};function A(P){const _=new Kt(b),B=$i(P.anchor,h),N=$i(P.origin,_);let{x:z,y:j}=Wf(B,N);switch(P.anchor.side){case"top":j-=c.value[0];break;case"bottom":j+=c.value[0];break;case"left":z-=c.value[0];break;case"right":z+=c.value[0];break}switch(P.anchor.align){case"top":j-=c.value[1];break;case"bottom":j+=c.value[1];break;case"left":z-=c.value[1];break;case"right":z+=c.value[1];break}return _.x+=z,_.y+=j,_.width=Math.min(_.width,u.value),_.height=Math.min(_.height,d.value),{overflows:ri(_,w),x:z,y:j}}let V=0,x=0;const I={x:0,y:0},k={x:!1,y:!1};let C=-1;for(;!(C++>10);){const{x:P,y:_,overflows:B}=A(p);V+=P,x+=_,b.x+=P,b.y+=_;{const N=si(p.anchor),z=B.x.before||B.x.after,j=B.y.before||B.y.after;let ee=!1;if(["x","y"].forEach(Z=>{if(Z==="x"&&z&&!k.x||Z==="y"&&j&&!k.y){const $={anchor:{...p.anchor},origin:{...p.origin}},E=Z==="x"?N==="y"?ol:ll:N==="y"?ll:ol;$.anchor=E($.anchor),$.origin=E($.origin);const{overflows:R}=A($);(R[Z].before<=B[Z].before&&R[Z].after<=B[Z].after||R[Z].before+R[Z].after<(B[Z].before+B[Z].after)/2)&&(p=$,ee=k[Z]=!0)}}),ee)continue}B.x.before&&(V+=B.x.before,b.x+=B.x.before),B.x.after&&(V-=B.x.after,b.x-=B.x.after),B.y.before&&(x+=B.y.before,b.y+=B.y.before),B.y.after&&(x-=B.y.after,b.y-=B.y.after);{const N=ri(b,w);I.x=w.width-N.x.before-N.x.after,I.y=w.height-N.y.before-N.y.after,V+=N.x.before,b.x+=N.x.before,x+=N.y.before,b.y+=N.y.before}break}const T=si(p.anchor);return Object.assign(n.value,{"--v-overlay-anchor-origin":`${p.anchor.side} ${p.anchor.align}`,transformOrigin:`${p.origin.side} ${p.origin.align}`,top:K(cl(x)),left:e.isRtl.value?void 0:K(cl(V)),right:e.isRtl.value?K(cl(-V)):void 0,minWidth:K(T==="y"?Math.min(i.value,h.width):i.value),maxWidth:K(Ri(Be(I.x,i.value===1/0?0:i.value,u.value))),maxHeight:K(Ri(Be(I.y,r.value===1/0?0:r.value,d.value)))}),{available:I,contentBox:b}}return U(()=>[l.value,o.value,t.offset,t.minWidth,t.minHeight,t.maxWidth,t.maxHeight],()=>g()),ke(()=>{const h=g();if(!h)return;const{available:b,contentBox:y}=h;y.height>b.y&&requestAnimationFrame(()=>{g(),requestAnimationFrame(()=>{g()})})}),{updateLocation:g}}function cl(e){return Math.round(e*devicePixelRatio)/devicePixelRatio}function Ri(e){return Math.ceil(e*devicePixelRatio)/devicePixelRatio}let Al=!0;const xa=[];function qf(e){!Al||xa.length?(xa.push(e),Ll()):(Al=!1,e(),Ll())}let Ni=-1;function Ll(){cancelAnimationFrame(Ni),Ni=requestAnimationFrame(()=>{const e=xa.shift();e&&e(),xa.length?Ll():Al=!0})}const Mr={none:null,close:Jf,block:Qf,reposition:em},Xf=L({scrollStrategy:{type:[String,Function],default:"block",validator:e=>typeof e=="function"||e in Mr}},"VOverlay-scroll-strategies");function Zf(e,t){if(!Se)return;let n;Te(async()=>{n?.stop(),t.isActive.value&&e.scrollStrategy&&(n=ql(),await new Promise(a=>setTimeout(a)),n.active&&n.run(()=>{typeof e.scrollStrategy=="function"?e.scrollStrategy(t,e,n):Mr[e.scrollStrategy]?.(t,e,n)}))}),Ze(()=>{n?.stop()})}function Jf(e){function t(n){e.isActive.value=!1}Er(e.targetEl.value??e.contentEl.value,t)}function Qf(e,t){const n=e.root.value?.offsetParent,a=[...new Set([...Sa(e.targetEl.value,t.contained?n:void 0),...Sa(e.contentEl.value,t.contained?n:void 0)])].filter(i=>!i.classList.contains("v-overlay-scroll-blocked")),l=window.innerWidth-document.documentElement.offsetWidth,o=(i=>ro(i)&&i)(n||document.documentElement);o&&e.root.value.classList.add("v-overlay--scroll-blocked"),a.forEach((i,r)=>{i.style.setProperty("--v-body-scroll-x",K(-i.scrollLeft)),i.style.setProperty("--v-body-scroll-y",K(-i.scrollTop)),i!==document.documentElement&&i.style.setProperty("--v-scrollbar-offset",K(l)),i.classList.add("v-overlay-scroll-blocked")}),Ze(()=>{a.forEach((i,r)=>{const u=parseFloat(i.style.getPropertyValue("--v-body-scroll-x")),d=parseFloat(i.style.getPropertyValue("--v-body-scroll-y")),c=i.style.scrollBehavior;i.style.scrollBehavior="auto",i.style.removeProperty("--v-body-scroll-x"),i.style.removeProperty("--v-body-scroll-y"),i.style.removeProperty("--v-scrollbar-offset"),i.classList.remove("v-overlay-scroll-blocked"),i.scrollLeft=-u,i.scrollTop=-d,i.style.scrollBehavior=c}),o&&e.root.value.classList.remove("v-overlay--scroll-blocked")})}function em(e,t,n){let a=!1,l=-1,o=-1;function i(r){qf(()=>{const u=performance.now();e.updateLocation.value?.(r),a=(performance.now()-u)/(1e3/60)>2})}o=(typeof requestIdleCallback>"u"?r=>r():requestIdleCallback)(()=>{n.run(()=>{Er(e.targetEl.value??e.contentEl.value,r=>{a?(cancelAnimationFrame(l),l=requestAnimationFrame(()=>{l=requestAnimationFrame(()=>{i(r)})})):i(r)})})}),Ze(()=>{typeof cancelIdleCallback<"u"&&cancelIdleCallback(o),cancelAnimationFrame(l)})}function Er(e,t){const n=[document,...Sa(e)];n.forEach(a=>{a.addEventListener("scroll",t,{passive:!0})}),Ze(()=>{n.forEach(a=>{a.removeEventListener("scroll",t)})})}const Tl=Symbol.for("vuetify:v-menu"),wo=L({closeDelay:[Number,String],openDelay:[Number,String]},"delay");function Vo(e,t){let n=()=>{};function a(i){n?.();const r=Number(i?e.openDelay:e.closeDelay);return new Promise(u=>{n=Rc(r,()=>{t?.(i),u(i)})})}function l(){return a(!0)}function o(){return a(!1)}return{clearDelay:n,runOpenDelay:l,runCloseDelay:o}}const tm=L({target:[String,Object],activator:[String,Object],activatorProps:{type:Object,default:()=>({})},openOnClick:{type:Boolean,default:void 0},openOnHover:Boolean,openOnFocus:{type:Boolean,default:void 0},closeOnContentClick:Boolean,...wo()},"VOverlay-activator");function nm(e,t){let{isActive:n,isTop:a,contentEl:l}=t;const o=_e("useActivator"),i=H();let r=!1,u=!1,d=!0;const c=m(()=>e.openOnFocus||e.openOnFocus==null&&e.openOnHover),f=m(()=>e.openOnClick||e.openOnClick==null&&!e.openOnHover&&!c.value),{runOpenDelay:v,runCloseDelay:g}=Vo(e,k=>{k===(e.openOnHover&&r||c.value&&u)&&!(e.openOnHover&&n.value&&!a.value)&&(n.value!==k&&(d=!0),n.value=k)}),h=H(),b={onClick:k=>{k.stopPropagation(),i.value=k.currentTarget||k.target,n.value||(h.value=[k.clientX,k.clientY]),n.value=!n.value},onMouseenter:k=>{k.sourceCapabilities?.firesTouchEvents||(r=!0,i.value=k.currentTarget||k.target,v())},onMouseleave:k=>{r=!1,g()},onFocus:k=>{Vn(k.target,":focus-visible")!==!1&&(u=!0,k.stopPropagation(),i.value=k.currentTarget||k.target,v())},onBlur:k=>{u=!1,k.stopPropagation(),g()}},y=m(()=>{const k={};return f.value&&(k.onClick=b.onClick),e.openOnHover&&(k.onMouseenter=b.onMouseenter,k.onMouseleave=b.onMouseleave),c.value&&(k.onFocus=b.onFocus,k.onBlur=b.onBlur),k}),S=m(()=>{const k={};if(e.openOnHover&&(k.onMouseenter=()=>{r=!0,v()},k.onMouseleave=()=>{r=!1,g()}),c.value&&(k.onFocusin=()=>{u=!0,v()},k.onFocusout=()=>{u=!1,g()}),e.closeOnContentClick){const C=ge(Tl,null);k.onClick=()=>{n.value=!1,C?.closeParents()}}return k}),w=m(()=>{const k={};return e.openOnHover&&(k.onMouseenter=()=>{d&&(r=!0,d=!1,v())},k.onMouseleave=()=>{r=!1,g()}),k});U(a,k=>{k&&(e.openOnHover&&!r&&(!c.value||!u)||c.value&&!u&&(!e.openOnHover||!r))&&!l.value?.contains(document.activeElement)&&(n.value=!1)}),U(n,k=>{k||setTimeout(()=>{h.value=void 0})},{flush:"post"});const p=ha();Te(()=>{p.value&&ke(()=>{i.value=p.el})});const A=ha(),V=m(()=>e.target==="cursor"&&h.value?h.value:A.value?A.el:Fr(e.target,o)||i.value),x=m(()=>Array.isArray(V.value)?void 0:V.value);let I;return U(()=>!!e.activator,k=>{k&&Se?(I=ql(),I.run(()=>{am(e,o,{activatorEl:i,activatorEvents:y})})):I&&I.stop()},{flush:"post",immediate:!0}),Ze(()=>{I?.stop()}),{activatorEl:i,activatorRef:p,target:V,targetEl:x,targetRef:A,activatorEvents:y,contentEvents:S,scrimEvents:w}}function am(e,t,n){let{activatorEl:a,activatorEvents:l}=n;U(()=>e.activator,(u,d)=>{if(d&&u!==d){const c=r(d);c&&i(c)}u&&ke(()=>o())},{immediate:!0}),U(()=>e.activatorProps,()=>{o()}),Ze(()=>{i()});function o(){let u=arguments.length>0&&arguments[0]!==void 0?arguments[0]:r(),d=arguments.length>1&&arguments[1]!==void 0?arguments[1]:e.activatorProps;u&&zc(u,F(l.value,d))}function i(){let u=arguments.length>0&&arguments[0]!==void 0?arguments[0]:r(),d=arguments.length>1&&arguments[1]!==void 0?arguments[1]:e.activatorProps;u&&Wc(u,F(l.value,d))}function r(){let u=arguments.length>0&&arguments[0]!==void 0?arguments[0]:e.activator;const d=Fr(u,t);return a.value=d?.nodeType===Node.ELEMENT_NODE?d:void 0,a.value}}function Fr(e,t){if(!e)return;let n;if(e==="parent"){let a=t?.proxy?.$el?.parentNode;for(;a?.hasAttribute("data-no-activator");)a=a.parentNode;n=a}else typeof e=="string"?n=document.querySelector(e):"$el"in e?n=e.$el:n=e;return n}function Or(){if(!Se)return Y(!1);const{ssr:e}=ut();if(e){const t=Y(!1);return Qe(()=>{t.value=!0}),t}else return Y(!0)}const Po=L({eager:Boolean},"lazy");function Io(e,t){const n=Y(!1),a=m(()=>n.value||e.eager||t.value);U(t,()=>n.value=!0);function l(){e.eager||(n.value=!1)}return{isBooted:n,hasContent:a,onAfterLeave:l}}function gn(){const t=_e("useScopeId").vnode.scopeId;return{scopeId:t?{[t]:""}:void 0}}const Hi=Symbol.for("vuetify:stack"),On=at([]);function lm(e,t,n){const a=_e("useStack"),l=!n,o=ge(Hi,void 0),i=at({activeChildren:new Set});we(Hi,i);const r=Y(+t.value);nt(e,()=>{const c=On.at(-1)?.[1];r.value=c?c+10:+t.value,l&&On.push([a.uid,r.value]),o?.activeChildren.add(a.uid),Ze(()=>{if(l){const f=Me(On).findIndex(v=>v[0]===a.uid);On.splice(f,1)}o?.activeChildren.delete(a.uid)})});const u=Y(!0);l&&Te(()=>{const c=On.at(-1)?.[0]===a.uid;setTimeout(()=>u.value=c)});const d=m(()=>!i.activeChildren.size);return{globalTop:Xl(u),localTop:d,stackStyles:m(()=>({zIndex:r.value}))}}function om(e){return{teleportTarget:m(()=>{const n=e();if(n===!0||!Se)return;const a=n===!1?document.body:typeof n=="string"?document.querySelector(n):n;if(a==null)return;let l=[...a.children].find(o=>o.matches(".v-overlay-container"));return l||(l=document.createElement("div"),l.className="v-overlay-container",a.appendChild(l)),l})}}function im(){return!0}function $r(e,t,n){if(!e||Rr(e,n)===!1)return!1;const a=As(t);if(typeof ShadowRoot<"u"&&a instanceof ShadowRoot&&a.host===e.target)return!1;const l=(typeof n.value=="object"&&n.value.include||(()=>[]))();return l.push(t),!l.some(o=>o?.contains(e.target))}function Rr(e,t){return(typeof t.value=="object"&&t.value.closeConditional||im)(e)}function sm(e,t,n){const a=typeof n.value=="function"?n.value:n.value.handler;e.shadowTarget=e.target,t._clickOutside.lastMousedownWasOutside&&$r(e,t,n)&&setTimeout(()=>{Rr(e,n)&&a&&a(e)},0)}function zi(e,t){const n=As(e);t(document),typeof ShadowRoot<"u"&&n instanceof ShadowRoot&&t(n)}const rm={mounted(e,t){const n=l=>sm(l,e,t),a=l=>{e._clickOutside.lastMousedownWasOutside=$r(l,e,t)};zi(e,l=>{l.addEventListener("click",n,!0),l.addEventListener("mousedown",a,!0)}),e._clickOutside||(e._clickOutside={lastMousedownWasOutside:!1}),e._clickOutside[t.instance.$.uid]={onClick:n,onMousedown:a}},beforeUnmount(e,t){e._clickOutside&&(zi(e,n=>{if(!n||!e._clickOutside?.[t.instance.$.uid])return;const{onClick:a,onMousedown:l}=e._clickOutside[t.instance.$.uid];n.removeEventListener("click",a,!0),n.removeEventListener("mousedown",l,!0)}),delete e._clickOutside[t.instance.$.uid])}};function um(e){const{modelValue:t,color:n,...a}=e;return s(Et,{name:"fade-transition",appear:!0},{default:()=>[e.modelValue&&s("div",F({class:["v-overlay__scrim",e.color.backgroundColorClasses.value],style:e.color.backgroundColorStyles.value},a),null)]})}const ta=L({absolute:Boolean,attach:[Boolean,String,Object],closeOnBack:{type:Boolean,default:!0},contained:Boolean,contentClass:null,contentProps:null,disabled:Boolean,opacity:[Number,String],noClickAnimation:Boolean,modelValue:Boolean,persistent:Boolean,scrim:{type:[Boolean,String],default:!0},zIndex:{type:[Number,String],default:2e3},...tm(),...X(),...Oe(),...Po(),...jf(),...Xf(),...me(),...St()},"VOverlay"),xt=M()({name:"VOverlay",directives:{ClickOutside:rm},inheritAttrs:!1,props:{_disableGlobalStack:Boolean,...ta()},emits:{"click:outside":e=>!0,"update:modelValue":e=>!0,afterEnter:()=>!0,afterLeave:()=>!0},setup(e,t){let{slots:n,attrs:a,emit:l}=t;const o=_e("VOverlay"),i=H(),r=H(),u=H(),d=Q(e,"modelValue"),c=m({get:()=>d.value,set:W=>{W&&e.disabled||(d.value=W)}}),{themeClasses:f}=be(e),{rtlClasses:v,isRtl:g}=Fe(),{hasContent:h,onAfterLeave:b}=Io(e,c),y=Ce(m(()=>typeof e.scrim=="string"?e.scrim:null)),{globalTop:S,localTop:w,stackStyles:p}=lm(c,D(e,"zIndex"),e._disableGlobalStack),{activatorEl:A,activatorRef:V,target:x,targetEl:I,targetRef:k,activatorEvents:C,contentEvents:T,scrimEvents:P}=nm(e,{isActive:c,isTop:w,contentEl:u}),{teleportTarget:_}=om(()=>{const W=e.attach||e.contained;if(W)return W;const se=A?.value?.getRootNode()||o.proxy?.$el?.getRootNode();return se instanceof ShadowRoot?se:!1}),{dimensionStyles:B}=$e(e),N=Or(),{scopeId:z}=gn();U(()=>e.disabled,W=>{W&&(c.value=!1)});const{contentStyles:j,updateLocation:ee}=Yf(e,{isRtl:g,contentEl:u,target:x,isActive:c});Zf(e,{root:i,contentEl:u,targetEl:I,isActive:c,updateLocation:ee});function Z(W){l("click:outside",W),e.persistent?ue():c.value=!1}function $(W){return c.value&&S.value&&(!e.scrim||W.target===r.value||W instanceof MouseEvent&&W.shadowTarget===r.value)}Se&&U(c,W=>{W?window.addEventListener("keydown",E):window.removeEventListener("keydown",E)},{immediate:!0}),Je(()=>{Se&&window.removeEventListener("keydown",E)});function E(W){W.key==="Escape"&&S.value&&(e.persistent?ue():(c.value=!1,u.value?.contains(document.activeElement)&&A.value?.focus()))}const R=ir();nt(()=>e.closeOnBack,()=>{rf(R,W=>{S.value&&c.value?(W(!1),e.persistent?ue():c.value=!1):W()})});const q=H();U(()=>c.value&&(e.absolute||e.contained)&&_.value==null,W=>{if(W){const se=so(i.value);se&&se!==document.scrollingElement&&(q.value=se.scrollTop)}});function ue(){e.noClickAnimation||u.value&&Yt(u.value,[{transformOrigin:"center"},{transform:"scale(1.03)"},{transformOrigin:"center"}],{duration:150,easing:Nn})}function le(){l("afterEnter")}function oe(){b(),l("afterLeave")}return O(()=>s(ae,null,[n.activator?.({isActive:c.value,targetRef:k,props:F({ref:V},C.value,e.activatorProps)}),N.value&&h.value&&s(wc,{disabled:!_.value,to:_.value},{default:()=>[s("div",F({class:["v-overlay",{"v-overlay--absolute":e.absolute||e.contained,"v-overlay--active":c.value,"v-overlay--contained":e.contained},f.value,v.value,e.class],style:[p.value,{"--v-overlay-opacity":e.opacity,top:K(q.value)},e.style],ref:i},z,a),[s(um,F({color:y,modelValue:c.value&&!!e.scrim,ref:r},P.value),null),s(Xe,{appear:!0,persisted:!0,transition:e.transition,target:x.value,onAfterEnter:le,onAfterLeave:oe},{default:()=>[Ie(s("div",F({ref:u,class:["v-overlay__content",e.contentClass],style:[B.value,j.value]},T.value,e.contentProps),[n.default?.({isActive:c})]),[[bt,c.value],[st("click-outside"),{handler:Z,closeConditional:$,include:()=>[A.value]}]])]})])]})])),{activatorEl:A,scrimEl:r,target:x,animateClick:ue,contentEl:u,globalTop:S,localTop:w,updateLocation:ee}}}),dl=Symbol("Forwarded refs");function vl(e,t){let n=e;for(;n;){const a=Reflect.getOwnPropertyDescriptor(n,t);if(a)return a;n=Object.getPrototypeOf(n)}}function gt(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),a=1;a<t;a++)n[a-1]=arguments[a];return e[dl]=n,new Proxy(e,{get(l,o){if(Reflect.has(l,o))return Reflect.get(l,o);if(!(typeof o=="symbol"||o.startsWith("$")||o.startsWith("__"))){for(const i of n)if(i.value&&Reflect.has(i.value,o)){const r=Reflect.get(i.value,o);return typeof r=="function"?r.bind(i.value):r}}},has(l,o){if(Reflect.has(l,o))return!0;if(typeof o=="symbol"||o.startsWith("$")||o.startsWith("__"))return!1;for(const i of n)if(i.value&&Reflect.has(i.value,o))return!0;return!1},set(l,o,i){if(Reflect.has(l,o))return Reflect.set(l,o,i);if(typeof o=="symbol"||o.startsWith("$")||o.startsWith("__"))return!1;for(const r of n)if(r.value&&Reflect.has(r.value,o))return Reflect.set(r.value,o,i);return!1},getOwnPropertyDescriptor(l,o){const i=Reflect.getOwnPropertyDescriptor(l,o);if(i)return i;if(!(typeof o=="symbol"||o.startsWith("$")||o.startsWith("__"))){for(const r of n){if(!r.value)continue;const u=vl(r.value,o)??("_"in r.value?vl(r.value._?.setupState,o):void 0);if(u)return u}for(const r of n){const u=r.value&&r.value[dl];if(!u)continue;const d=u.slice();for(;d.length;){const c=d.shift(),f=vl(c.value,o);if(f)return f;const v=c.value&&c.value[dl];v&&d.push(...v)}}}}})}const Nr=L({id:String,submenu:Boolean,...Ee(ta({closeDelay:250,closeOnContentClick:!0,locationStrategy:"connected",location:void 0,openDelay:300,scrim:!1,scrollStrategy:"reposition",transition:{component:Ba}}),["absolute"])},"VMenu"),pn=M()({name:"VMenu",props:Nr(),emits:{"update:modelValue":e=>!0},setup(e,t){let{slots:n}=t;const a=Q(e,"modelValue"),{scopeId:l}=gn(),{isRtl:o}=Fe(),i=je(),r=m(()=>e.id||`v-menu-${i}`),u=H(),d=ge(Tl,null),c=Y(new Set);we(Tl,{register(){c.value.add(i)},unregister(){c.value.delete(i)},closeParents(y){setTimeout(()=>{!c.value.size&&!e.persistent&&(y==null||u.value?.contentEl&&!Nc(y,u.value.contentEl))&&(a.value=!1,d?.closeParents())},40)}}),Je(()=>d?.unregister()),as(()=>a.value=!1);async function f(y){const S=y.relatedTarget,w=y.target;await ke(),a.value&&S!==w&&u.value?.contentEl&&u.value?.globalTop&&![document,u.value.contentEl].includes(w)&&!u.value.contentEl.contains(w)&&Rn(u.value.contentEl)[0]?.focus()}U(a,y=>{y?(d?.register(),document.addEventListener("focusin",f,{once:!0})):(d?.unregister(),document.removeEventListener("focusin",f))});function v(y){d?.closeParents(y)}function g(y){if(!e.disabled)if(y.key==="Tab"||y.key==="Enter"&&!e.closeOnContentClick){if(y.key==="Enter"&&(y.target instanceof HTMLTextAreaElement||y.target instanceof HTMLInputElement&&y.target.closest("form")))return;y.key==="Enter"&&y.preventDefault(),vs(Rn(u.value?.contentEl,!1),y.shiftKey?"prev":"next",w=>w.tabIndex>=0)||(a.value=!1,u.value?.activatorEl?.focus())}else e.submenu&&y.key===(o.value?"ArrowRight":"ArrowLeft")&&(a.value=!1,u.value?.activatorEl?.focus())}function h(y){if(e.disabled)return;const S=u.value?.contentEl;S&&a.value?y.key==="ArrowDown"?(y.preventDefault(),y.stopImmediatePropagation(),Ut(S,"next")):y.key==="ArrowUp"?(y.preventDefault(),y.stopImmediatePropagation(),Ut(S,"prev")):e.submenu&&(y.key===(o.value?"ArrowRight":"ArrowLeft")?a.value=!1:y.key===(o.value?"ArrowLeft":"ArrowRight")&&(y.preventDefault(),Ut(S,"first"))):(e.submenu?y.key===(o.value?"ArrowLeft":"ArrowRight"):["ArrowDown","ArrowUp"].includes(y.key))&&(a.value=!0,y.preventDefault(),setTimeout(()=>setTimeout(()=>h(y))))}const b=m(()=>F({"aria-haspopup":"menu","aria-expanded":String(a.value),"aria-owns":r.value,onKeydown:h},e.activatorProps));return O(()=>{const y=xt.filterProps(e);return s(xt,F({ref:u,id:r.value,class:["v-menu",e.class],style:e.style},y,{modelValue:a.value,"onUpdate:modelValue":S=>a.value=S,absolute:!0,activatorProps:b.value,location:e.location??(e.submenu?"end":"bottom"),"onClick:outside":v,onKeydown:g},l),{activator:n.activator,default:function(){for(var S=arguments.length,w=new Array(S),p=0;p<S;p++)w[p]=arguments[p];return s(ve,{root:"VMenu"},{default:()=>[n.default?.(...w)]})}})}),gt({id:r,ΨopenChildren:c},u)}}),cm=L({active:Boolean,disabled:Boolean,max:[Number,String],value:{type:[Number,String],default:0},...X(),...St({transition:{component:ho}})},"VCounter"),$a=M()({name:"VCounter",functional:!0,props:cm(),setup(e,t){let{slots:n}=t;const a=m(()=>e.max?`${e.value} / ${e.max}`:String(e.value));return O(()=>s(Xe,{transition:e.transition},{default:()=>[Ie(s("div",{class:["v-counter",{"text-error":e.max&&!e.disabled&&parseFloat(e.value)>parseFloat(e.max)},e.class],style:e.style},[n.default?n.default({counter:a.value,max:e.max,value:e.value}):a.value]),[[bt,e.active]])]})),{}}}),dm=L({floating:Boolean,...X()},"VFieldLabel"),$n=M()({name:"VFieldLabel",props:dm(),setup(e,t){let{slots:n}=t;return O(()=>s(Bn,{class:["v-field-label",{"v-field-label--floating":e.floating},e.class],style:e.style,"aria-hidden":e.floating||void 0},n)),{}}});function Ra(e,t){const n=H(),a=Y(!1);if(Jl){const l=new IntersectionObserver(o=>{a.value=!!o.find(i=>i.isIntersecting)},t);Je(()=>{l.disconnect()}),U(n,(o,i)=>{i&&(l.unobserve(i),a.value=!1),o&&l.observe(o)},{flush:"post"})}return{intersectionRef:n,isIntersecting:a}}const Wi={center:"center",top:"bottom",bottom:"top",left:"right",right:"left"},Dt=L({location:String},"location");function hn(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,n=arguments.length>2?arguments[2]:void 0;const{isRtl:a}=Fe();return{locationStyles:m(()=>{if(!e.location)return{};const{side:o,align:i}=hl(e.location.split(" ").length>1?e.location:`${e.location} center`,a.value);function r(d){return n?n(d):0}const u={};return o!=="center"&&(t?u[Wi[o]]=`calc(100% - ${r(o)}px)`:u[o]=0),i!=="center"?t?u[Wi[i]]=`calc(100% - ${r(i)}px)`:u[i]=0:(o==="center"?u.top=u.left="50%":u[{top:"left",bottom:"left",left:"top",right:"top"}[o]]="50%",u.transform={top:"translateX(-50%)",bottom:"translateX(-50%)",left:"translateY(-50%)",right:"translateY(-50%)",center:"translate(-50%, -50%)"}[o]),u})}}const vm=L({absolute:Boolean,active:{type:Boolean,default:!0},bgColor:String,bgOpacity:[Number,String],bufferValue:{type:[Number,String],default:0},bufferColor:String,bufferOpacity:[Number,String],clickable:Boolean,color:String,height:{type:[Number,String],default:4},indeterminate:Boolean,max:{type:[Number,String],default:100},modelValue:{type:[Number,String],default:0},opacity:[Number,String],reverse:Boolean,stream:Boolean,striped:Boolean,roundedBar:Boolean,...X(),...Dt({location:"top"}),...Ve(),...re(),...me()},"VProgressLinear"),Na=M()({name:"VProgressLinear",props:vm(),emits:{"update:modelValue":e=>!0},setup(e,t){let{slots:n}=t;const a=Q(e,"modelValue"),{isRtl:l,rtlClasses:o}=Fe(),{themeClasses:i}=be(e),{locationStyles:r}=hn(e),{textColorClasses:u,textColorStyles:d}=Ge(e,"color"),{backgroundColorClasses:c,backgroundColorStyles:f}=Ce(m(()=>e.bgColor||e.color)),{backgroundColorClasses:v,backgroundColorStyles:g}=Ce(m(()=>e.bufferColor||e.bgColor||e.color)),{backgroundColorClasses:h,backgroundColorStyles:b}=Ce(e,"color"),{roundedClasses:y}=Le(e),{intersectionRef:S,isIntersecting:w}=Ra(),p=m(()=>parseFloat(e.max)),A=m(()=>parseFloat(e.height)),V=m(()=>Be(parseFloat(e.bufferValue)/p.value*100,0,100)),x=m(()=>Be(parseFloat(a.value)/p.value*100,0,100)),I=m(()=>l.value!==e.reverse),k=m(()=>e.indeterminate?"fade-transition":"slide-x-transition"),C=Se&&window.matchMedia?.("(forced-colors: active)").matches;function T(P){if(!S.value)return;const{left:_,right:B,width:N}=S.value.getBoundingClientRect(),z=I.value?N-P.clientX+(B-N):P.clientX-_;a.value=Math.round(z/N*p.value)}return O(()=>s(e.tag,{ref:S,class:["v-progress-linear",{"v-progress-linear--absolute":e.absolute,"v-progress-linear--active":e.active&&w.value,"v-progress-linear--reverse":I.value,"v-progress-linear--rounded":e.rounded,"v-progress-linear--rounded-bar":e.roundedBar,"v-progress-linear--striped":e.striped},y.value,i.value,o.value,e.class],style:[{bottom:e.location==="bottom"?0:void 0,top:e.location==="top"?0:void 0,height:e.active?K(A.value):0,"--v-progress-linear-height":K(A.value),...e.absolute?r.value:{}},e.style],role:"progressbar","aria-hidden":e.active?"false":"true","aria-valuemin":"0","aria-valuemax":e.max,"aria-valuenow":e.indeterminate?void 0:x.value,onClick:e.clickable&&T},{default:()=>[e.stream&&s("div",{key:"stream",class:["v-progress-linear__stream",u.value],style:{...d.value,[I.value?"left":"right"]:K(-A.value),borderTop:`${K(A.value/2)} dotted`,opacity:parseFloat(e.bufferOpacity),top:`calc(50% - ${K(A.value/4)})`,width:K(100-V.value,"%"),"--v-progress-linear-stream-to":K(A.value*(I.value?1:-1))}},null),s("div",{class:["v-progress-linear__background",C?void 0:c.value],style:[f.value,{opacity:parseFloat(e.bgOpacity),width:e.stream?0:void 0}]},null),s("div",{class:["v-progress-linear__buffer",C?void 0:v.value],style:[g.value,{opacity:parseFloat(e.bufferOpacity),width:K(V.value,"%")}]},null),s(Et,{name:k.value},{default:()=>[e.indeterminate?s("div",{class:"v-progress-linear__indeterminate"},[["long","short"].map(P=>s("div",{key:P,class:["v-progress-linear__indeterminate",P,C?void 0:h.value],style:b.value},null))]):s("div",{class:["v-progress-linear__determinate",C?void 0:h.value],style:[b.value,{width:K(x.value,"%")}]},null)]}),n.default&&s("div",{class:"v-progress-linear__content"},[n.default({value:x.value,buffer:V.value})])]})),{}}}),Ha=L({loading:[Boolean,String]},"loader");function na(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Vt();return{loaderClasses:m(()=>({[`${t}--loading`]:e.loading}))}}function aa(e,t){let{slots:n}=t;return s("div",{class:`${e.name}__loader`},[n.default?.({color:e.color,isActive:e.active})||s(Na,{absolute:e.absolute,active:e.active,color:e.color,height:"2",indeterminate:!0},null)])}const fm=["underlined","outlined","filled","solo","solo-inverted","solo-filled","plain"],la=L({appendInnerIcon:ie,bgColor:String,clearable:Boolean,clearIcon:{type:ie,default:"$clear"},active:Boolean,centerAffix:{type:Boolean,default:void 0},color:String,baseColor:String,dirty:Boolean,disabled:{type:Boolean,default:null},error:Boolean,flat:Boolean,label:String,persistentClear:Boolean,prependInnerIcon:ie,reverse:Boolean,singleLine:Boolean,variant:{type:String,default:"filled",validator:e=>fm.includes(e)},"onClick:clear":We(),"onClick:appendInner":We(),"onClick:prependInner":We(),...X(),...Ha(),...Ve(),...me()},"VField"),Mn=M()({name:"VField",inheritAttrs:!1,props:{id:String,...ea(),...la()},emits:{"update:focused":e=>!0,"update:modelValue":e=>!0},setup(e,t){let{attrs:n,emit:a,slots:l}=t;const{themeClasses:o}=be(e),{loaderClasses:i}=na(e),{focusClasses:r,isFocused:u,focus:d,blur:c}=Tt(e),{InputIcon:f}=wr(e),{roundedClasses:v}=Le(e),{rtlClasses:g}=Fe(),h=m(()=>e.dirty||e.active),b=m(()=>!e.singleLine&&!!(e.label||l.label)),y=je(),S=m(()=>e.id||`input-${y}`),w=m(()=>`${S.value}-messages`),p=H(),A=H(),V=H(),x=m(()=>["plain","underlined"].includes(e.variant)),{backgroundColorClasses:I,backgroundColorStyles:k}=Ce(D(e,"bgColor")),{textColorClasses:C,textColorStyles:T}=Ge(m(()=>e.error||e.disabled?void 0:h.value&&u.value?e.color:e.baseColor));U(h,N=>{if(b.value){const z=p.value.$el,j=A.value.$el;requestAnimationFrame(()=>{const ee=ao(z),Z=j.getBoundingClientRect(),$=Z.x-ee.x,E=Z.y-ee.y-(ee.height/2-Z.height/2),R=Z.width/.75,q=Math.abs(R-ee.width)>1?{maxWidth:K(R)}:void 0,ue=getComputedStyle(z),le=getComputedStyle(j),oe=parseFloat(ue.transitionDuration)*1e3||150,W=parseFloat(le.getPropertyValue("--v-field-label-scale")),se=le.getPropertyValue("color");z.style.visibility="visible",j.style.visibility="hidden",Yt(z,{transform:`translate(${$}px, ${E}px) scale(${W})`,color:se,...q},{duration:oe,easing:Nn,direction:N?"normal":"reverse"}).finished.then(()=>{z.style.removeProperty("visibility"),j.style.removeProperty("visibility")})})}},{flush:"post"});const P=m(()=>({isActive:h,isFocused:u,controlRef:V,blur:c,focus:d}));function _(N){N.target!==document.activeElement&&N.preventDefault()}function B(N){N.key!=="Enter"&&N.key!==" "||(N.preventDefault(),N.stopPropagation(),e["onClick:clear"]?.(new MouseEvent("click")))}return O(()=>{const N=e.variant==="outlined",z=!!(l["prepend-inner"]||e.prependInnerIcon),j=!!(e.clearable||l.clear),ee=!!(l["append-inner"]||e.appendInnerIcon||j),Z=()=>l.label?l.label({...P.value,label:e.label,props:{for:S.value}}):e.label;return s("div",F({class:["v-field",{"v-field--active":h.value,"v-field--appended":ee,"v-field--center-affix":e.centerAffix??!x.value,"v-field--disabled":e.disabled,"v-field--dirty":e.dirty,"v-field--error":e.error,"v-field--flat":e.flat,"v-field--has-background":!!e.bgColor,"v-field--persistent-clear":e.persistentClear,"v-field--prepended":z,"v-field--reverse":e.reverse,"v-field--single-line":e.singleLine,"v-field--no-label":!Z(),[`v-field--variant-${e.variant}`]:!0},o.value,I.value,r.value,i.value,v.value,g.value,e.class],style:[k.value,e.style],onClick:_},n),[s("div",{class:"v-field__overlay"},null),s(aa,{name:"v-field",active:!!e.loading,color:e.error?"error":typeof e.loading=="string"?e.loading:e.color},{default:l.loader}),z&&s("div",{key:"prepend",class:"v-field__prepend-inner"},[e.prependInnerIcon&&s(f,{key:"prepend-icon",name:"prependInner"},null),l["prepend-inner"]?.(P.value)]),s("div",{class:"v-field__field","data-no-activator":""},[["filled","solo","solo-inverted","solo-filled"].includes(e.variant)&&b.value&&s($n,{key:"floating-label",ref:A,class:[C.value],floating:!0,for:S.value,style:T.value},{default:()=>[Z()]}),s($n,{ref:p,for:S.value},{default:()=>[Z()]}),l.default?.({...P.value,props:{id:S.value,class:"v-field__input","aria-describedby":w.value},focus:d,blur:c})]),j&&s(yo,{key:"clear"},{default:()=>[Ie(s("div",{class:"v-field__clearable",onMousedown:$=>{$.preventDefault(),$.stopPropagation()}},[s(ve,{defaults:{VIcon:{icon:e.clearIcon}}},{default:()=>[l.clear?l.clear({...P.value,props:{onKeydown:B,onFocus:d,onBlur:c,onClick:e["onClick:clear"]}}):s(f,{name:"clear",onKeydown:B,onFocus:d,onBlur:c},null)]})]),[[bt,e.dirty]])]}),ee&&s("div",{key:"append",class:"v-field__append-inner"},[l["append-inner"]?.(P.value),e.appendInnerIcon&&s(f,{key:"append-icon",name:"appendInner"},null)]),s("div",{class:["v-field__outline",C.value],style:T.value},[N&&s(ae,null,[s("div",{class:"v-field__outline__start"},null),b.value&&s("div",{class:"v-field__outline__notch"},[s($n,{ref:A,floating:!0,for:S.value},{default:()=>[Z()]})]),s("div",{class:"v-field__outline__end"},null)]),x.value&&b.value&&s($n,{ref:A,floating:!0,for:S.value},{default:()=>[Z()]})])])}),{controlRef:V}}});function po(e){const t=Object.keys(Mn.props).filter(n=>!Pa(n)&&n!=="class"&&n!=="style");return eo(e,t)}const mm=["color","file","time","date","datetime-local","week","month"],za=L({autofocus:Boolean,counter:[Boolean,Number,String],counterValue:[Number,Function],prefix:String,placeholder:String,persistentPlaceholder:Boolean,persistentCounter:Boolean,suffix:String,role:String,type:{type:String,default:"text"},modelModifiers:Object,...Bt(),...la()},"VTextField"),Zt=M()({name:"VTextField",directives:{Intersect:Ma},inheritAttrs:!1,props:za(),emits:{"click:control":e=>!0,"mousedown:control":e=>!0,"update:focused":e=>!0,"update:modelValue":e=>!0},setup(e,t){let{attrs:n,emit:a,slots:l}=t;const o=Q(e,"modelValue"),{isFocused:i,focus:r,blur:u}=Tt(e),d=m(()=>typeof e.counterValue=="function"?e.counterValue(o.value):typeof e.counterValue=="number"?e.counterValue:(o.value??"").toString().length),c=m(()=>{if(n.maxlength)return n.maxlength;if(!(!e.counter||typeof e.counter!="number"&&typeof e.counter!="string"))return e.counter}),f=m(()=>["plain","underlined"].includes(e.variant));function v(x,I){!e.autofocus||!x||I[0].target?.focus?.()}const g=H(),h=H(),b=H(),y=m(()=>mm.includes(e.type)||e.persistentPlaceholder||i.value||e.active);function S(){b.value!==document.activeElement&&b.value?.focus(),i.value||r()}function w(x){a("mousedown:control",x),x.target!==b.value&&(S(),x.preventDefault())}function p(x){S(),a("click:control",x)}function A(x){x.stopPropagation(),S(),ke(()=>{o.value=null,no(e["onClick:clear"],x)})}function V(x){const I=x.target;if(o.value=I.value,e.modelModifiers?.trim&&["text","search","password","tel","url"].includes(e.type)){const k=[I.selectionStart,I.selectionEnd];ke(()=>{I.selectionStart=k[0],I.selectionEnd=k[1]})}}return O(()=>{const x=!!(l.counter||e.counter!==!1&&e.counter!=null),I=!!(x||l.details),[k,C]=$t(n),{modelValue:T,...P}=Ue.filterProps(e),_=po(e);return s(Ue,F({ref:g,modelValue:o.value,"onUpdate:modelValue":B=>o.value=B,class:["v-text-field",{"v-text-field--prefixed":e.prefix,"v-text-field--suffixed":e.suffix,"v-input--plain-underlined":f.value},e.class],style:e.style},k,P,{centerAffix:!f.value,focused:i.value}),{...l,default:B=>{let{id:N,isDisabled:z,isDirty:j,isReadonly:ee,isValid:Z}=B;return s(Mn,F({ref:h,onMousedown:w,onClick:p,"onClick:clear":A,"onClick:prependInner":e["onClick:prependInner"],"onClick:appendInner":e["onClick:appendInner"],role:e.role},_,{id:N.value,active:y.value||j.value,dirty:j.value||e.dirty,disabled:z.value,focused:i.value,error:Z.value===!1}),{...l,default:$=>{let{props:{class:E,...R}}=$;const q=Ie(s("input",F({ref:b,value:o.value,onInput:V,autofocus:e.autofocus,readonly:ee.value,disabled:z.value,name:e.name,placeholder:e.placeholder,size:1,type:e.type,onFocus:S,onBlur:u},R,C),null),[[st("intersect"),{handler:v},null,{once:!0}]]);return s(ae,null,[e.prefix&&s("span",{class:"v-text-field__prefix"},[s("span",{class:"v-text-field__prefix__text"},[e.prefix])]),l.default?s("div",{class:E,"data-no-activator":""},[l.default(),q]):Vc(q,{class:E}),e.suffix&&s("span",{class:"v-text-field__suffix"},[s("span",{class:"v-text-field__suffix__text"},[e.suffix])])])}})},details:I?B=>s(ae,null,[l.details?.(B),x&&s(ae,null,[s("span",null,null),s($a,{active:e.persistentCounter||i.value,value:d.value,max:c.value,disabled:e.disabled},l.counter)])]):void 0})}),gt({},g,h,b)}}),gm=L({renderless:Boolean,...X()},"VVirtualScrollItem"),Hr=M()({name:"VVirtualScrollItem",inheritAttrs:!1,props:gm(),emits:{"update:height":e=>!0},setup(e,t){let{attrs:n,emit:a,slots:l}=t;const{resizeRef:o,contentRect:i}=yt(void 0,"border");U(()=>i.value?.height,r=>{r!=null&&a("update:height",r)}),O(()=>e.renderless?s(ae,null,[l.default?.({itemRef:o})]):s("div",F({ref:o,class:["v-virtual-scroll__item",e.class],style:e.style},n),[l.default?.()]))}}),hm=-1,ym=1,fl=100,zr=L({itemHeight:{type:[Number,String],default:null},height:[Number,String]},"virtual");function Wr(e,t){const n=ut(),a=Y(0);Te(()=>{a.value=parseFloat(e.itemHeight||0)});const l=Y(0),o=Y(Math.ceil((parseInt(e.height)||n.height.value)/(a.value||16))||1),i=Y(0),r=Y(0),u=H(),d=H();let c=0;const{resizeRef:f,contentRect:v}=yt();Te(()=>{f.value=u.value});const g=m(()=>u.value===document.documentElement?n.height.value:v.value?.height||parseInt(e.height)||0),h=m(()=>!!(u.value&&d.value&&g.value&&a.value));let b=Array.from({length:t.value.length}),y=Array.from({length:t.value.length});const S=Y(0);let w=-1;function p($){return b[$]||a.value}const A=Ec(()=>{const $=performance.now();y[0]=0;const E=t.value.length;for(let R=1;R<=E-1;R++)y[R]=(y[R-1]||0)+p(R-1);S.value=Math.max(S.value,performance.now()-$)},S),V=U(h,$=>{$&&(V(),c=d.value.offsetTop,A.immediate(),z(),~w&&ke(()=>{Se&&window.requestAnimationFrame(()=>{ee(w),w=-1})}))});Ze(()=>{A.clear()});function x($,E){const R=b[$],q=a.value;a.value=q?Math.min(a.value,E):E,(R!==E||q!==a.value)&&(b[$]=E,A())}function I($){return $=Be($,0,t.value.length-1),y[$]||0}function k($){return bm(y,$)}let C=0,T=0,P=0;U(g,($,E)=>{E&&(z(),$<E&&requestAnimationFrame(()=>{T=0,z()}))});function _(){if(!u.value||!d.value)return;const $=u.value.scrollTop,E=performance.now();E-P>500?(T=Math.sign($-C),c=d.value.offsetTop):T=$-C,C=$,P=E,z()}function B(){!u.value||!d.value||(T=0,P=0,z())}let N=-1;function z(){cancelAnimationFrame(N),N=requestAnimationFrame(j)}function j(){if(!u.value||!g.value)return;const $=C-c,E=Math.sign(T),R=Math.max(0,$-fl),q=Be(k(R),0,t.value.length),ue=$+g.value+fl,le=Be(k(ue)+1,q+1,t.value.length);if((E!==hm||q<l.value)&&(E!==ym||le>o.value)){const oe=I(l.value)-I(q),W=I(le)-I(o.value);Math.max(oe,W)>fl?(l.value=q,o.value=le):(q<=0&&(l.value=q),le>=t.value.length&&(o.value=le))}i.value=I(l.value),r.value=I(t.value.length)-I(o.value)}function ee($){const E=I($);!u.value||$&&!E?w=$:u.value.scrollTop=E}const Z=m(()=>t.value.slice(l.value,o.value).map(($,E)=>({raw:$,index:E+l.value})));return U(t,()=>{b=Array.from({length:t.value.length}),y=Array.from({length:t.value.length}),A.immediate(),z()},{deep:!0}),{calculateVisibleItems:z,containerRef:u,markerRef:d,computedItems:Z,paddingTop:i,paddingBottom:r,scrollToIndex:ee,handleScroll:_,handleScrollend:B,handleItemResize:x}}function bm(e,t){let n=e.length-1,a=0,l=0,o=null,i=-1;if(e[n]<t)return n;for(;a<=n;)if(l=a+n>>1,o=e[l],o>t)n=l-1;else if(o<t)i=l,a=l+1;else return o===t?l:a;return i}const Sm=L({items:{type:Array,default:()=>[]},renderless:Boolean,...zr(),...X(),...Oe()},"VVirtualScroll"),Wa=M()({name:"VVirtualScroll",props:Sm(),setup(e,t){let{slots:n}=t;const a=_e("VVirtualScroll"),{dimensionStyles:l}=$e(e),{calculateVisibleItems:o,containerRef:i,markerRef:r,handleScroll:u,handleScrollend:d,handleItemResize:c,scrollToIndex:f,paddingTop:v,paddingBottom:g,computedItems:h}=Wr(e,D(e,"items"));return nt(()=>e.renderless,()=>{function b(){const S=(arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1)?"addEventListener":"removeEventListener";i.value===document.documentElement?(document[S]("scroll",u,{passive:!0}),document[S]("scrollend",d)):(i.value?.[S]("scroll",u,{passive:!0}),i.value?.[S]("scrollend",d))}Qe(()=>{i.value=so(a.vnode.el,!0),b(!0)}),Ze(b)}),O(()=>{const b=h.value.map(y=>s(Hr,{key:y.index,renderless:e.renderless,"onUpdate:height":S=>c(y.index,S)},{default:S=>n.default?.({item:y.raw,index:y.index,...S})}));return e.renderless?s(ae,null,[s("div",{ref:r,class:"v-virtual-scroll__spacer",style:{paddingTop:K(v.value)}},null),b,s("div",{class:"v-virtual-scroll__spacer",style:{paddingBottom:K(g.value)}},null)]):s("div",{ref:i,class:["v-virtual-scroll",e.class],onScrollPassive:u,onScrollend:d,style:[l.value,e.style]},[s("div",{ref:r,class:"v-virtual-scroll__container",style:{paddingTop:K(v.value),paddingBottom:K(g.value)}},[b])])}),{calculateVisibleItems:o,scrollToIndex:f}}});function _o(e,t){const n=Y(!1);let a;function l(r){cancelAnimationFrame(a),n.value=!0,a=requestAnimationFrame(()=>{a=requestAnimationFrame(()=>{n.value=!1})})}async function o(){await new Promise(r=>requestAnimationFrame(r)),await new Promise(r=>requestAnimationFrame(r)),await new Promise(r=>requestAnimationFrame(r)),await new Promise(r=>{if(n.value){const u=U(n,()=>{u(),r()})}else r()})}async function i(r){if(r.key==="Tab"&&t.value?.focus(),!["PageDown","PageUp","Home","End"].includes(r.key))return;const u=e.value?.$el;if(!u)return;(r.key==="Home"||r.key==="End")&&u.scrollTo({top:r.key==="Home"?0:u.scrollHeight,behavior:"smooth"}),await o();const d=u.querySelectorAll(":scope > :not(.v-virtual-scroll__spacer)");if(r.key==="PageDown"||r.key==="Home"){const c=u.getBoundingClientRect().top;for(const f of d)if(f.getBoundingClientRect().top>=c){f.focus();break}}else{const c=u.getBoundingClientRect().bottom;for(const f of[...d].reverse())if(f.getBoundingClientRect().bottom<=c){f.focus();break}}}return{onScrollPassive:l,onKeydown:i}}const Ao=L({chips:Boolean,closableChips:Boolean,closeText:{type:String,default:"$vuetify.close"},openText:{type:String,default:"$vuetify.open"},eager:Boolean,hideNoData:Boolean,hideSelected:Boolean,listProps:{type:Object},menu:Boolean,menuIcon:{type:ie,default:"$dropdown"},menuProps:{type:Object},multiple:Boolean,noDataText:{type:String,default:"$vuetify.noDataText"},openOnClear:Boolean,itemColor:String,...yr({itemChildren:!1})},"Select"),km=L({...Ao(),...Ee(za({modelValue:null,role:"combobox"}),["validationValue","dirty","appendInnerIcon"]),...St({transition:{component:Ba}})},"VSelect"),Lo=M()({name:"VSelect",props:km(),emits:{"update:focused":e=>!0,"update:modelValue":e=>!0,"update:menu":e=>!0},setup(e,t){let{slots:n}=t;const{t:a}=Ae(),l=H(),o=H(),i=H(),r=Q(e,"menu"),u=m({get:()=>r.value,set:$=>{r.value&&!$&&o.value?.ΨopenChildren.size||(r.value=$)}}),{items:d,transformIn:c,transformOut:f}=ko(e),v=Q(e,"modelValue",[],$=>c($===null?[null]:Pe($)),$=>{const E=f($);return e.multiple?E:E[0]??null}),g=m(()=>typeof e.counterValue=="function"?e.counterValue(v.value):typeof e.counterValue=="number"?e.counterValue:v.value.length),h=Oa(),b=m(()=>v.value.map($=>$.value)),y=Y(!1),S=m(()=>u.value?e.closeText:e.openText);let w="",p;const A=m(()=>e.hideSelected?d.value.filter($=>!v.value.some(E=>e.valueComparator(E,$))):d.value),V=m(()=>e.hideNoData&&!A.value.length||e.readonly||h?.isReadonly.value),x=m(()=>({...e.menuProps,activatorProps:{...e.menuProps?.activatorProps||{},"aria-haspopup":"listbox"}})),I=H(),k=_o(I,l);function C($){e.openOnClear&&(u.value=!0)}function T(){V.value||(u.value=!u.value)}function P($){ya($)&&_($)}function _($){if(!$.key||e.readonly||h?.isReadonly.value)return;["Enter"," ","ArrowDown","ArrowUp","Home","End"].includes($.key)&&$.preventDefault(),["Enter","ArrowDown"," "].includes($.key)&&(u.value=!0),["Escape","Tab"].includes($.key)&&(u.value=!1),$.key==="Home"?I.value?.focus("first"):$.key==="End"&&I.value?.focus("last");const E=1e3;if(e.multiple||!ya($))return;const R=performance.now();R-p>E&&(w=""),w+=$.key.toLowerCase(),p=R;const q=d.value.find(ue=>ue.title.toLowerCase().startsWith(w));if(q!==void 0){v.value=[q];const ue=A.value.indexOf(q);Se&&window.requestAnimationFrame(()=>{ue>=0&&i.value?.scrollToIndex(ue)})}}function B($){let E=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;if(!$.props.disabled)if(e.multiple){const R=v.value.findIndex(ue=>e.valueComparator(ue.value,$.value)),q=E??!~R;if(~R){const ue=q?[...v.value,$]:[...v.value];ue.splice(R,1),v.value=ue}else q&&(v.value=[...v.value,$])}else{const R=E!==!1;v.value=R?[$]:[],ke(()=>{u.value=!1})}}function N($){I.value?.$el.contains($.relatedTarget)||(u.value=!1)}function z(){e.eager&&i.value?.calculateVisibleItems()}function j(){y.value&&l.value?.focus()}function ee($){y.value=!0}function Z($){if($==null)v.value=[];else if(Vn(l.value,":autofill")||Vn(l.value,":-webkit-autofill")){const E=d.value.find(R=>R.title===$);E&&B(E)}else l.value&&(l.value.value="")}return U(u,()=>{if(!e.hideSelected&&u.value&&v.value.length){const $=A.value.findIndex(E=>v.value.some(R=>e.valueComparator(R.value,E.value)));Se&&window.requestAnimationFrame(()=>{$>=0&&i.value?.scrollToIndex($)})}}),U(()=>e.items,($,E)=>{u.value||y.value&&!E.length&&$.length&&(u.value=!0)}),O(()=>{const $=!!(e.chips||n.chip),E=!!(!e.hideNoData||A.value.length||n["prepend-item"]||n["append-item"]||n["no-data"]),R=v.value.length>0,q=Zt.filterProps(e),ue=R||!y.value&&e.label&&!e.persistentPlaceholder?void 0:e.placeholder;return s(Zt,F({ref:l},q,{modelValue:v.value.map(le=>le.props.value).join(", "),"onUpdate:modelValue":Z,focused:y.value,"onUpdate:focused":le=>y.value=le,validationValue:v.externalValue,counterValue:g.value,dirty:R,class:["v-select",{"v-select--active-menu":u.value,"v-select--chips":!!e.chips,[`v-select--${e.multiple?"multiple":"single"}`]:!0,"v-select--selected":v.value.length,"v-select--selection-slot":!!n.selection},e.class],style:e.style,inputmode:"none",placeholder:ue,"onClick:clear":C,"onMousedown:control":T,onBlur:N,onKeydown:_,"aria-label":a(S.value),title:a(S.value)}),{...n,default:()=>s(ae,null,[s(pn,F({ref:o,modelValue:u.value,"onUpdate:modelValue":le=>u.value=le,activator:"parent",contentClass:"v-select__content",disabled:V.value,eager:e.eager,maxHeight:310,openOnClick:!1,closeOnContentClick:!1,transition:e.transition,onAfterEnter:z,onAfterLeave:j},x.value),{default:()=>[E&&s(Ea,F({ref:I,selected:b.value,selectStrategy:e.multiple?"independent":"single-independent",onMousedown:le=>le.preventDefault(),onKeydown:P,onFocusin:ee,tabindex:"-1","aria-live":"polite",color:e.itemColor??e.color},k,e.listProps),{default:()=>[n["prepend-item"]?.(),!A.value.length&&!e.hideNoData&&(n["no-data"]?.()??s(At,{title:a(e.noDataText)},null)),s(Wa,{ref:i,renderless:!0,items:A.value},{default:le=>{let{item:oe,index:W,itemRef:se}=le;const ce=F(oe.props,{ref:se,key:W,onClick:()=>B(oe,null)});return n.item?.({item:oe,index:W,props:ce})??s(At,F(ce,{role:"option"}),{prepend:De=>{let{isSelected:xe}=De;return s(ae,null,[e.multiple&&!e.hideSelected?s(Lt,{key:oe.value,modelValue:xe,ripple:!1,tabindex:"-1"},null):void 0,oe.props.prependAvatar&&s(ft,{image:oe.props.prependAvatar},null),oe.props.prependIcon&&s(ye,{icon:oe.props.prependIcon},null)])}})}}),n["append-item"]?.()]})]}),v.value.map((le,oe)=>{function W(xe){xe.stopPropagation(),xe.preventDefault(),B(le,!1)}const se={"onClick:close":W,onKeydown(xe){xe.key!=="Enter"&&xe.key!==" "||(xe.preventDefault(),xe.stopPropagation(),W(xe))},onMousedown(xe){xe.preventDefault(),xe.stopPropagation()},modelValue:!0,"onUpdate:modelValue":void 0},ce=$?!!n.chip:!!n.selection,De=ce?Ia($?n.chip({item:le,index:oe,props:se}):n.selection({item:le,index:oe})):void 0;if(!(ce&&!De))return s("div",{key:le.value,class:"v-select__selection"},[$?n.chip?s(ve,{key:"chip-defaults",defaults:{VChip:{closable:e.closableChips,size:"small",text:le.title}}},{default:()=>[De]}):s(Dn,F({key:"chip",closable:e.closableChips,size:"small",text:le.title,disabled:le.props.disabled},se),null):De??s("span",{class:"v-select__selection-text"},[le.title,e.multiple&&oe<v.value.length-1&&s("span",{class:"v-select__selection-comma"},[Ft(",")])])])})]),"append-inner":function(){for(var le=arguments.length,oe=new Array(le),W=0;W<le;W++)oe[W]=arguments[W];return s(ae,null,[n["append-inner"]?.(...oe),e.menuIcon?s(ye,{class:"v-select__menu-icon",icon:e.menuIcon},null):void 0])}})}),gt({isFocused:y,menu:u,select:B},l)}}),jr=L({baseColor:String,divided:Boolean,...lt(),...X(),...ze(),...Ne(),...Ve(),...re(),...me(),...mt()},"VBtnGroup"),Bl=M()({name:"VBtnGroup",props:jr(),setup(e,t){let{slots:n}=t;const{themeClasses:a}=be(e),{densityClasses:l}=et(e),{borderClasses:o}=dt(e),{elevationClasses:i}=Ye(e),{roundedClasses:r}=Le(e);pe({VBtn:{height:"auto",baseColor:D(e,"baseColor"),color:D(e,"color"),density:D(e,"density"),flat:!0,variant:D(e,"variant")}}),O(()=>s(e.tag,{class:["v-btn-group",{"v-btn-group--divided":e.divided},a.value,o.value,l.value,i.value,r.value,e.class],style:e.style},n))}}),To=Symbol.for("vuetify:v-btn-toggle"),Cm=L({...jr(),...vn()},"VBtnToggle"),xm=M()({name:"VBtnToggle",props:Cm(),emits:{"update:modelValue":e=>!0},setup(e,t){let{slots:n}=t;const{isSelected:a,next:l,prev:o,select:i,selected:r}=Ht(e,To);return O(()=>{const u=Bl.filterProps(e);return s(Bl,F({class:["v-btn-toggle",e.class]},u,{style:e.style}),{default:()=>[n.default?.({isSelected:a,next:l,prev:o,select:i,selected:r})]})}),{next:l,prev:o,select:i}}}),wm=L({bgColor:String,color:String,indeterminate:[Boolean,String],modelValue:{type:[Number,String],default:0},rotate:{type:[Number,String],default:0},width:{type:[Number,String],default:4},...X(),...It(),...re({tag:"div"}),...me()},"VProgressCircular"),_n=M()({name:"VProgressCircular",props:wm(),setup(e,t){let{slots:n}=t;const a=20,l=2*Math.PI*a,o=H(),{themeClasses:i}=be(e),{sizeClasses:r,sizeStyles:u}=Tn(e),{textColorClasses:d,textColorStyles:c}=Ge(D(e,"color")),{textColorClasses:f,textColorStyles:v}=Ge(D(e,"bgColor")),{intersectionRef:g,isIntersecting:h}=Ra(),{resizeRef:b,contentRect:y}=yt(),S=m(()=>Math.max(0,Math.min(100,parseFloat(e.modelValue)))),w=m(()=>Number(e.width)),p=m(()=>u.value?Number(e.size):y.value?y.value.width:Math.max(w.value,32)),A=m(()=>a/(1-w.value/p.value)*2),V=m(()=>w.value/p.value*A.value),x=m(()=>K((100-S.value)/100*l));return Te(()=>{g.value=o.value,b.value=o.value}),O(()=>s(e.tag,{ref:o,class:["v-progress-circular",{"v-progress-circular--indeterminate":!!e.indeterminate,"v-progress-circular--visible":h.value,"v-progress-circular--disable-shrink":e.indeterminate==="disable-shrink"},i.value,r.value,d.value,e.class],style:[u.value,c.value,e.style],role:"progressbar","aria-valuemin":"0","aria-valuemax":"100","aria-valuenow":e.indeterminate?void 0:S.value},{default:()=>[s("svg",{style:{transform:`rotate(calc(-90deg + ${Number(e.rotate)}deg))`},xmlns:"http://www.w3.org/2000/svg",viewBox:`0 0 ${A.value} ${A.value}`},[s("circle",{class:["v-progress-circular__underlay",f.value],style:v.value,fill:"transparent",cx:"50%",cy:"50%",r:a,"stroke-width":V.value,"stroke-dasharray":l,"stroke-dashoffset":0},null),s("circle",{class:"v-progress-circular__overlay",fill:"transparent",cx:"50%",cy:"50%",r:a,"stroke-width":V.value,"stroke-dasharray":l,"stroke-dashoffset":x.value},null)]),n.default&&s("div",{class:"v-progress-circular__content"},[n.default({value:S.value})])]})),{}}}),Vm=["static","relative","fixed","absolute","sticky"],En=L({position:{type:String,validator:e=>Vm.includes(e)}},"position");function Fn(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Vt();return{positionClasses:m(()=>e.position?`${t}--${e.position}`:void 0)}}function Pm(e,t){U(()=>e.isActive?.value,n=>{e.isLink.value&&n&&t&&ke(()=>{t(!0)})},{immediate:!0})}const ja=L({active:{type:Boolean,default:void 0},activeColor:String,baseColor:String,symbol:{type:null,default:To},flat:Boolean,icon:[Boolean,String,Function,Object],prependIcon:ie,appendIcon:ie,block:Boolean,readonly:Boolean,slim:Boolean,stacked:Boolean,ripple:{type:[Boolean,Object],default:!0},text:String,...lt(),...X(),...ze(),...Oe(),...Ne(),...fn(),...Ha(),...Dt(),...En(),...Ve(),...Jn(),...It(),...re({tag:"button"}),...me(),...mt({variant:"elevated"})},"VBtn"),he=M()({name:"VBtn",props:ja(),emits:{"group:selected":e=>!0},setup(e,t){let{attrs:n,slots:a}=t;const{themeClasses:l}=be(e),{borderClasses:o}=dt(e),{densityClasses:i}=et(e),{dimensionStyles:r}=$e(e),{elevationClasses:u}=Ye(e),{loaderClasses:d}=na(e),{locationStyles:c}=hn(e),{positionClasses:f}=Fn(e),{roundedClasses:v}=Le(e),{sizeClasses:g,sizeStyles:h}=Tn(e),b=mn(e,e.symbol,!1),y=Zn(e,n),S=m(()=>e.active!==void 0?e.active:y.isLink.value?y.isActive?.value:b?.isSelected.value),w=m(()=>S.value?e.activeColor??e.color:e.color),p=m(()=>({color:b?.isSelected.value&&(!y.isLink.value||y.isActive?.value)||!b||y.isActive?.value?w.value??e.baseColor:e.baseColor,variant:e.variant})),{colorClasses:A,colorStyles:V,variantClasses:x}=dn(p),I=m(()=>b?.disabled.value||e.disabled),k=m(()=>e.variant==="elevated"&&!(e.disabled||e.flat||e.border)),C=m(()=>{if(!(e.value===void 0||typeof e.value=="symbol"))return Object(e.value)===e.value?JSON.stringify(e.value,null,0):e.value});function T(P){I.value||y.isLink.value&&(P.metaKey||P.ctrlKey||P.shiftKey||P.button!==0||n.target==="_blank")||(y.navigate?.(P),b?.toggle())}return Pm(y,b?.select),O(()=>{const P=y.isLink.value?"a":e.tag,_=!!(e.prependIcon||a.prepend),B=!!(e.appendIcon||a.append),N=!!(e.icon&&e.icon!==!0);return Ie(s(P,F({type:P==="a"?void 0:"button",class:["v-btn",b?.selectedClass.value,{"v-btn--active":S.value,"v-btn--block":e.block,"v-btn--disabled":I.value,"v-btn--elevated":k.value,"v-btn--flat":e.flat,"v-btn--icon":!!e.icon,"v-btn--loading":e.loading,"v-btn--readonly":e.readonly,"v-btn--slim":e.slim,"v-btn--stacked":e.stacked},l.value,o.value,A.value,i.value,u.value,d.value,f.value,v.value,g.value,x.value,e.class],style:[V.value,r.value,c.value,h.value,e.style],"aria-busy":e.loading?!0:void 0,disabled:I.value||void 0,tabindex:e.loading||e.readonly?-1:void 0,onClick:T,value:C.value},y.linkProps),{default:()=>[Rt(!0,"v-btn"),!e.icon&&_&&s("span",{key:"prepend",class:"v-btn__prepend"},[a.prepend?s(ve,{key:"prepend-defaults",disabled:!e.prependIcon,defaults:{VIcon:{icon:e.prependIcon}}},a.prepend):s(ye,{key:"prepend-icon",icon:e.prependIcon},null)]),s("span",{class:"v-btn__content","data-no-activator":""},[!a.default&&N?s(ye,{key:"content-icon",icon:e.icon},null):s(ve,{key:"content-defaults",disabled:!N,defaults:{VIcon:{icon:e.icon}}},{default:()=>[a.default?.()??e.text]})]),!e.icon&&B&&s("span",{key:"append",class:"v-btn__append"},[a.append?s(ve,{key:"append-defaults",disabled:!e.appendIcon,defaults:{VIcon:{icon:e.appendIcon}}},a.append):s(ye,{key:"append-icon",icon:e.appendIcon},null)]),!!e.loading&&s("span",{key:"loader",class:"v-btn__loader"},[a.loader?.()??s(_n,{color:typeof e.loading=="boolean"?void 0:e.loading,indeterminate:!0,width:"2"},null)])]}),[[Nt,!I.value&&e.ripple,"",{center:!!e.icon}]])}),{group:b}}}),Yr=M()({name:"VCardActions",props:X(),setup(e,t){let{slots:n}=t;return pe({VBtn:{slim:!0,variant:"text"}}),O(()=>s("div",{class:["v-card-actions",e.class],style:e.style},[n.default?.()])),{}}}),Im=L({opacity:[Number,String],...X(),...re()},"VCardSubtitle"),Gr=M()({name:"VCardSubtitle",props:Im(),setup(e,t){let{slots:n}=t;return O(()=>s(e.tag,{class:["v-card-subtitle",e.class],style:[{"--v-card-subtitle-opacity":e.opacity},e.style]},n)),{}}}),Ur=Pt("v-card-title"),pm=L({appendAvatar:String,appendIcon:ie,prependAvatar:String,prependIcon:ie,subtitle:[String,Number],title:[String,Number],...X(),...ze()},"VCardItem"),Kr=M()({name:"VCardItem",props:pm(),setup(e,t){let{slots:n}=t;return O(()=>{const a=!!(e.prependAvatar||e.prependIcon),l=!!(a||n.prepend),o=!!(e.appendAvatar||e.appendIcon),i=!!(o||n.append),r=!!(e.title!=null||n.title),u=!!(e.subtitle!=null||n.subtitle);return s("div",{class:["v-card-item",e.class],style:e.style},[l&&s("div",{key:"prepend",class:"v-card-item__prepend"},[n.prepend?s(ve,{key:"prepend-defaults",disabled:!a,defaults:{VAvatar:{density:e.density,image:e.prependAvatar},VIcon:{density:e.density,icon:e.prependIcon}}},n.prepend):s(ae,null,[e.prependAvatar&&s(ft,{key:"prepend-avatar",density:e.density,image:e.prependAvatar},null),e.prependIcon&&s(ye,{key:"prepend-icon",density:e.density,icon:e.prependIcon},null)])]),s("div",{class:"v-card-item__content"},[r&&s(Ur,{key:"title"},{default:()=>[n.title?.()??e.title]}),u&&s(Gr,{key:"subtitle"},{default:()=>[n.subtitle?.()??e.subtitle]}),n.default?.()]),i&&s("div",{key:"append",class:"v-card-item__append"},[n.append?s(ve,{key:"append-defaults",disabled:!o,defaults:{VAvatar:{density:e.density,image:e.appendAvatar},VIcon:{density:e.density,icon:e.appendIcon}}},n.append):s(ae,null,[e.appendIcon&&s(ye,{key:"append-icon",density:e.density,icon:e.appendIcon},null),e.appendAvatar&&s(ft,{key:"append-avatar",density:e.density,image:e.appendAvatar},null)])])])}),{}}}),_m=L({opacity:[Number,String],...X(),...re()},"VCardText"),qr=M()({name:"VCardText",props:_m(),setup(e,t){let{slots:n}=t;return O(()=>s(e.tag,{class:["v-card-text",e.class],style:[{"--v-card-text-opacity":e.opacity},e.style]},n)),{}}}),Am=L({appendAvatar:String,appendIcon:ie,disabled:Boolean,flat:Boolean,hover:Boolean,image:String,link:{type:Boolean,default:void 0},prependAvatar:String,prependIcon:ie,ripple:{type:[Boolean,Object],default:!0},subtitle:[String,Number],text:[String,Number],title:[String,Number],...lt(),...X(),...ze(),...Oe(),...Ne(),...Ha(),...Dt(),...En(),...Ve(),...Jn(),...re(),...me(),...mt({variant:"elevated"})},"VCard"),Lm=M()({name:"VCard",directives:{Ripple:Nt},props:Am(),setup(e,t){let{attrs:n,slots:a}=t;const{themeClasses:l}=be(e),{borderClasses:o}=dt(e),{colorClasses:i,colorStyles:r,variantClasses:u}=dn(e),{densityClasses:d}=et(e),{dimensionStyles:c}=$e(e),{elevationClasses:f}=Ye(e),{loaderClasses:v}=na(e),{locationStyles:g}=hn(e),{positionClasses:h}=Fn(e),{roundedClasses:b}=Le(e),y=Zn(e,n),S=m(()=>e.link!==!1&&y.isLink.value),w=m(()=>!e.disabled&&e.link!==!1&&(e.link||y.isClickable.value));return O(()=>{const p=S.value?"a":e.tag,A=!!(a.title||e.title!=null),V=!!(a.subtitle||e.subtitle!=null),x=A||V,I=!!(a.append||e.appendAvatar||e.appendIcon),k=!!(a.prepend||e.prependAvatar||e.prependIcon),C=!!(a.image||e.image),T=x||k||I,P=!!(a.text||e.text!=null);return Ie(s(p,F({class:["v-card",{"v-card--disabled":e.disabled,"v-card--flat":e.flat,"v-card--hover":e.hover&&!(e.disabled||e.flat),"v-card--link":w.value},l.value,o.value,i.value,d.value,f.value,v.value,h.value,b.value,u.value,e.class],style:[r.value,c.value,g.value,e.style],onClick:w.value&&y.navigate,tabindex:e.disabled?-1:void 0},y.linkProps),{default:()=>[C&&s("div",{key:"image",class:"v-card__image"},[a.image?s(ve,{key:"image-defaults",disabled:!e.image,defaults:{VImg:{cover:!0,src:e.image}}},a.image):s(_t,{key:"image-img",cover:!0,src:e.image},null)]),s(aa,{name:"v-card",active:!!e.loading,color:typeof e.loading=="boolean"?void 0:e.loading},{default:a.loader}),T&&s(Kr,{key:"item",prependAvatar:e.prependAvatar,prependIcon:e.prependIcon,title:e.title,subtitle:e.subtitle,appendAvatar:e.appendAvatar,appendIcon:e.appendIcon},{default:a.item,prepend:a.prepend,title:a.title,subtitle:a.subtitle,append:a.append}),P&&s(qr,{key:"text"},{default:()=>[a.text?.()??e.text]}),a.default?.(),a.actions&&s(Yr,null,{default:a.actions}),Rt(w.value,"v-card")]}),[[st("ripple"),w.value&&e.ripple]])}),{}}}),Tm=L({id:String,text:String,...Ee(ta({closeOnBack:!1,location:"end",locationStrategy:"connected",eager:!0,minWidth:0,offset:10,openOnClick:!1,openOnHover:!0,origin:"auto",scrim:!1,scrollStrategy:"reposition",transition:!1}),["absolute","persistent"])},"VTooltip"),Bm=M()({name:"VTooltip",props:Tm(),emits:{"update:modelValue":e=>!0},setup(e,t){let{slots:n}=t;const a=Q(e,"modelValue"),{scopeId:l}=gn(),o=je(),i=m(()=>e.id||`v-tooltip-${o}`),r=H(),u=m(()=>e.location.split(" ").length>1?e.location:e.location+" center"),d=m(()=>e.origin==="auto"||e.origin==="overlap"||e.origin.split(" ").length>1||e.location.split(" ").length>1?e.origin:e.origin+" center"),c=m(()=>e.transition?e.transition:a.value?"scale-transition":"fade-transition"),f=m(()=>F({"aria-describedby":i.value},e.activatorProps));return O(()=>{const v=xt.filterProps(e);return s(xt,F({ref:r,class:["v-tooltip",e.class],style:e.style,id:i.value},v,{modelValue:a.value,"onUpdate:modelValue":g=>a.value=g,transition:c.value,absolute:!0,location:u.value,origin:d.value,persistent:!0,role:"tooltip",activatorProps:f.value,_disableGlobalStack:!0},l),{activator:n.activator,default:function(){for(var g=arguments.length,h=new Array(g),b=0;b<g;b++)h[b]=arguments[b];return n.default?.(...h)??e.text}})}),gt({},r)}}),Dm=L({...X(),...zs({fullHeight:!0}),...me()},"VApp"),Mm=M()({name:"VApp",props:Dm(),setup(e,t){let{slots:n}=t;const a=be(e),{layoutClasses:l,getLayoutItem:o,items:i,layoutRef:r}=Ws(e),{rtlClasses:u}=Fe();return O(()=>s("div",{ref:r,class:["v-application",a.themeClasses.value,l.value,u.value,e.class],style:[e.style]},[s("div",{class:"v-application__wrap"},[n.default?.()])])),{getLayoutItem:o,items:i,theme:a}}}),Xr=L({text:String,...X(),...re()},"VToolbarTitle"),Bo=M()({name:"VToolbarTitle",props:Xr(),setup(e,t){let{slots:n}=t;return O(()=>{const a=!!(n.default||n.text||e.text);return s(e.tag,{class:["v-toolbar-title",e.class],style:e.style},{default:()=>[a&&s("div",{class:"v-toolbar-title__placeholder"},[n.text?n.text():e.text,n.default?.()])]})}),{}}}),Em=[null,"prominent","default","comfortable","compact"],Zr=L({absolute:Boolean,collapse:Boolean,color:String,density:{type:String,default:"default",validator:e=>Em.includes(e)},extended:Boolean,extensionHeight:{type:[Number,String],default:48},flat:Boolean,floating:Boolean,height:{type:[Number,String],default:64},image:String,title:String,...lt(),...X(),...Ne(),...Ve(),...re({tag:"header"}),...me()},"VToolbar"),Dl=M()({name:"VToolbar",props:Zr(),setup(e,t){let{slots:n}=t;const{backgroundColorClasses:a,backgroundColorStyles:l}=Ce(D(e,"color")),{borderClasses:o}=dt(e),{elevationClasses:i}=Ye(e),{roundedClasses:r}=Le(e),{themeClasses:u}=be(e),{rtlClasses:d}=Fe(),c=Y(!!(e.extended||n.extension?.())),f=m(()=>parseInt(Number(e.height)+(e.density==="prominent"?Number(e.height):0)-(e.density==="comfortable"?8:0)-(e.density==="compact"?16:0),10)),v=m(()=>c.value?parseInt(Number(e.extensionHeight)+(e.density==="prominent"?Number(e.extensionHeight):0)-(e.density==="comfortable"?4:0)-(e.density==="compact"?8:0),10):0);return pe({VBtn:{variant:"text"}}),O(()=>{const g=!!(e.title||n.title),h=!!(n.image||e.image),b=n.extension?.();return c.value=!!(e.extended||b),s(e.tag,{class:["v-toolbar",{"v-toolbar--absolute":e.absolute,"v-toolbar--collapse":e.collapse,"v-toolbar--flat":e.flat,"v-toolbar--floating":e.floating,[`v-toolbar--density-${e.density}`]:!0},a.value,o.value,i.value,r.value,u.value,d.value,e.class],style:[l.value,e.style]},{default:()=>[h&&s("div",{key:"image",class:"v-toolbar__image"},[n.image?s(ve,{key:"image-defaults",disabled:!e.image,defaults:{VImg:{cover:!0,src:e.image}}},n.image):s(_t,{key:"image-img",cover:!0,src:e.image},null)]),s(ve,{defaults:{VTabs:{height:K(f.value)}}},{default:()=>[s("div",{class:"v-toolbar__content",style:{height:K(f.value)}},[n.prepend&&s("div",{class:"v-toolbar__prepend"},[n.prepend?.()]),g&&s(Bo,{key:"title",text:e.title},{text:n.title}),n.default?.(),n.append&&s("div",{class:"v-toolbar__append"},[n.append?.()])])]}),s(ve,{defaults:{VTabs:{height:K(v.value)}}},{default:()=>[s(Da,null,{default:()=>[c.value&&s("div",{class:"v-toolbar__extension",style:{height:K(v.value)}},[b])]})]})]})}),{contentHeight:f,extensionHeight:v}}}),Fm=L({scrollTarget:{type:String},scrollThreshold:{type:[String,Number],default:300}},"scroll");function Om(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const{canScroll:n}=t;let a=0,l=0;const o=H(null),i=Y(0),r=Y(0),u=Y(0),d=Y(!1),c=Y(!1),f=m(()=>Number(e.scrollThreshold)),v=m(()=>Be((f.value-i.value)/f.value||0)),g=()=>{const h=o.value;if(!h||n&&!n.value)return;a=i.value,i.value="window"in h?h.pageYOffset:h.scrollTop;const b=h instanceof Window?document.documentElement.scrollHeight:h.scrollHeight;if(l!==b){l=b;return}c.value=i.value<a,u.value=Math.abs(i.value-f.value)};return U(c,()=>{r.value=r.value||i.value}),U(d,()=>{r.value=0}),Qe(()=>{U(()=>e.scrollTarget,h=>{const b=h?document.querySelector(h):window;b&&b!==o.value&&(o.value?.removeEventListener("scroll",g),o.value=b,o.value.addEventListener("scroll",g,{passive:!0}))},{immediate:!0})}),Je(()=>{o.value?.removeEventListener("scroll",g)}),n&&U(n,g,{immediate:!0}),{scrollThreshold:f,currentScroll:i,currentThreshold:u,isScrollActive:d,scrollRatio:v,isScrollingUp:c,savedScroll:r}}const $m=L({scrollBehavior:String,modelValue:{type:Boolean,default:!0},location:{type:String,default:"top",validator:e=>["top","bottom"].includes(e)},...Zr(),...rn(),...Fm(),height:{type:[Number,String],default:64}},"VAppBar"),Rm=M()({name:"VAppBar",props:$m(),emits:{"update:modelValue":e=>!0},setup(e,t){let{slots:n}=t;const a=H(),l=Q(e,"modelValue"),o=m(()=>{const w=new Set(e.scrollBehavior?.split(" ")??[]);return{hide:w.has("hide"),fullyHide:w.has("fully-hide"),inverted:w.has("inverted"),collapse:w.has("collapse"),elevate:w.has("elevate"),fadeImage:w.has("fade-image")}}),i=m(()=>{const w=o.value;return w.hide||w.fullyHide||w.inverted||w.collapse||w.elevate||w.fadeImage||!l.value}),{currentScroll:r,scrollThreshold:u,isScrollingUp:d,scrollRatio:c}=Om(e,{canScroll:i}),f=m(()=>o.value.hide||o.value.fullyHide),v=m(()=>e.collapse||o.value.collapse&&(o.value.inverted?c.value>0:c.value===0)),g=m(()=>e.flat||o.value.fullyHide&&!l.value||o.value.elevate&&(o.value.inverted?r.value>0:r.value===0)),h=m(()=>o.value.fadeImage?o.value.inverted?1-c.value:c.value:void 0),b=m(()=>{if(o.value.hide&&o.value.inverted)return 0;const w=a.value?.contentHeight??0,p=a.value?.extensionHeight??0;return f.value?r.value<u.value||o.value.fullyHide?w+p:w:w+p});nt(m(()=>!!e.scrollBehavior),()=>{Te(()=>{f.value?o.value.inverted?l.value=r.value>u.value:l.value=d.value||r.value<u.value:l.value=!0})});const{ssrBootStyles:y}=cn(),{layoutItemStyles:S}=un({id:e.name,order:m(()=>parseInt(e.order,10)),position:D(e,"location"),layoutSize:b,elementSize:Y(void 0),active:l,absolute:D(e,"absolute")});return O(()=>{const w=Dl.filterProps(e);return s(Dl,F({ref:a,class:["v-app-bar",{"v-app-bar--bottom":e.location==="bottom"},e.class],style:[{...S.value,"--v-toolbar-image-opacity":h.value,height:void 0,...y.value},e.style]},w,{collapse:v.value,flat:g.value}),n)}),{}}}),Nm=L({...ja({icon:"$menu",variant:"text"})},"VAppBarNavIcon"),Hm=M()({name:"VAppBarNavIcon",props:Nm(),setup(e,t){let{slots:n}=t;return O(()=>s(he,F(e,{class:["v-app-bar-nav-icon"]}),n)),{}}}),zm=M()({name:"VAppBarTitle",props:Xr(),setup(e,t){let{slots:n}=t;return O(()=>s(Bo,F(e,{class:"v-app-bar-title"}),n)),{}}}),Jr=Pt("v-alert-title"),Wm=["success","info","warning","error"],jm=L({border:{type:[Boolean,String],validator:e=>typeof e=="boolean"||["top","end","bottom","start"].includes(e)},borderColor:String,closable:Boolean,closeIcon:{type:ie,default:"$close"},closeLabel:{type:String,default:"$vuetify.close"},icon:{type:[Boolean,String,Function,Object],default:null},modelValue:{type:Boolean,default:!0},prominent:Boolean,title:String,text:String,type:{type:String,validator:e=>Wm.includes(e)},...X(),...ze(),...Oe(),...Ne(),...Dt(),...En(),...Ve(),...re(),...me(),...mt({variant:"flat"})},"VAlert"),Ym=M()({name:"VAlert",props:jm(),emits:{"click:close":e=>!0,"update:modelValue":e=>!0},setup(e,t){let{emit:n,slots:a}=t;const l=Q(e,"modelValue"),o=m(()=>{if(e.icon!==!1)return e.type?e.icon??`$${e.type}`:e.icon}),i=m(()=>({color:e.color??e.type,variant:e.variant})),{themeClasses:r}=be(e),{colorClasses:u,colorStyles:d,variantClasses:c}=dn(i),{densityClasses:f}=et(e),{dimensionStyles:v}=$e(e),{elevationClasses:g}=Ye(e),{locationStyles:h}=hn(e),{positionClasses:b}=Fn(e),{roundedClasses:y}=Le(e),{textColorClasses:S,textColorStyles:w}=Ge(D(e,"borderColor")),{t:p}=Ae(),A=m(()=>({"aria-label":p(e.closeLabel),onClick(V){l.value=!1,n("click:close",V)}}));return()=>{const V=!!(a.prepend||o.value),x=!!(a.title||e.title),I=!!(a.close||e.closable);return l.value&&s(e.tag,{class:["v-alert",e.border&&{"v-alert--border":!!e.border,[`v-alert--border-${e.border===!0?"start":e.border}`]:!0},{"v-alert--prominent":e.prominent},r.value,u.value,f.value,g.value,b.value,y.value,c.value,e.class],style:[d.value,v.value,h.value,e.style],role:"alert"},{default:()=>[Rt(!1,"v-alert"),e.border&&s("div",{key:"border",class:["v-alert__border",S.value],style:w.value},null),V&&s("div",{key:"prepend",class:"v-alert__prepend"},[a.prepend?s(ve,{key:"prepend-defaults",disabled:!o.value,defaults:{VIcon:{density:e.density,icon:o.value,size:e.prominent?44:28}}},a.prepend):s(ye,{key:"prepend-icon",density:e.density,icon:o.value,size:e.prominent?44:28},null)]),s("div",{class:"v-alert__content"},[x&&s(Jr,{key:"title"},{default:()=>[a.title?.()??e.title]}),a.text?.()??e.text,a.default?.()]),a.append&&s("div",{key:"append",class:"v-alert__append"},[a.append()]),I&&s("div",{key:"close",class:"v-alert__close"},[a.close?s(ve,{key:"close-defaults",defaults:{VBtn:{icon:e.closeIcon,size:"x-small",variant:"text"}}},{default:()=>[a.close?.({props:A.value})]}):s(he,F({key:"close-btn",icon:e.closeIcon,size:"x-small",variant:"text"},A.value),null)])]})}}}),Gm=(e,t,n)=>e==null||t==null?-1:e.toString().toLocaleLowerCase().indexOf(t.toString().toLocaleLowerCase()),oa=L({customFilter:Function,customKeyFilter:Object,filterKeys:[Array,String],filterMode:{type:String,default:"intersection"},noFilter:Boolean},"filter");function Um(e,t,n){const a=[],l=n?.default??Gm,o=n?.filterKeys?Pe(n.filterKeys):!1,i=Object.keys(n?.customKeyFilter??{}).length;if(!e?.length)return a;e:for(let r=0;r<e.length;r++){const[u,d=u]=Pe(e[r]),c={},f={};let v=-1;if((t||i>0)&&!n?.noFilter){if(typeof u=="object"){const b=o||Object.keys(d);for(const y of b){const S=Re(d,y),w=n?.customKeyFilter?.[y];if(v=w?w(S,t,u):l(S,t,u),v!==-1&&v!==!1)w?c[y]=v:f[y]=v;else if(n?.filterMode==="every")continue e}}else v=l(u,t,u),v!==-1&&v!==!1&&(f.title=v);const g=Object.keys(f).length,h=Object.keys(c).length;if(!g&&!h||n?.filterMode==="union"&&h!==i&&!g||n?.filterMode==="intersection"&&(h!==i||!g))continue}a.push({index:r,matches:{...f,...c}})}return a}function ia(e,t,n,a){const l=H([]),o=H(new Map),i=m(()=>a?.transform?tt(t).map(u=>[u,a.transform(u)]):tt(t));Te(()=>{const u=typeof n=="function"?n():tt(n),d=typeof u!="string"&&typeof u!="number"?"":String(u),c=Um(i.value,d,{customKeyFilter:{...e.customKeyFilter,...tt(a?.customKeyFilter)},default:e.customFilter,filterKeys:e.filterKeys,filterMode:e.filterMode,noFilter:e.noFilter}),f=tt(t),v=[],g=new Map;c.forEach(h=>{let{index:b,matches:y}=h;const S=f[b];v.push(S),g.set(S.value,y)}),l.value=v,o.value=g});function r(u){return o.value.get(u.value)}return{filteredItems:l,filteredMatches:o,getMatches:r}}function Km(e,t,n){if(t==null)return e;if(Array.isArray(t))throw new Error("Multiple matches is not implemented");return typeof t=="number"&&~t?s(ae,null,[s("span",{class:"v-autocomplete__unmask"},[e.substr(0,t)]),s("span",{class:"v-autocomplete__mask"},[e.substr(t,n)]),s("span",{class:"v-autocomplete__unmask"},[e.substr(t+n)])]):e}const qm=L({autoSelectFirst:{type:[Boolean,String]},clearOnSelect:Boolean,search:String,...oa({filterKeys:["title"]}),...Ao(),...Ee(za({modelValue:null,role:"combobox"}),["validationValue","dirty","appendInnerIcon"]),...St({transition:!1})},"VAutocomplete"),Xm=M()({name:"VAutocomplete",props:qm(),emits:{"update:focused":e=>!0,"update:search":e=>!0,"update:modelValue":e=>!0,"update:menu":e=>!0},setup(e,t){let{slots:n}=t;const{t:a}=Ae(),l=H(),o=Y(!1),i=Y(!0),r=Y(!1),u=H(),d=H(),c=Q(e,"menu"),f=m({get:()=>c.value,set:J=>{c.value&&!J&&u.value?.ΨopenChildren.size||(c.value=J)}}),v=Y(-1),g=m(()=>l.value?.color),h=m(()=>f.value?e.closeText:e.openText),{items:b,transformIn:y,transformOut:S}=ko(e),{textColorClasses:w,textColorStyles:p}=Ge(g),A=Q(e,"search",""),V=Q(e,"modelValue",[],J=>y(J===null?[null]:Pe(J)),J=>{const G=S(J);return e.multiple?G:G[0]??null}),x=m(()=>typeof e.counterValue=="function"?e.counterValue(V.value):typeof e.counterValue=="number"?e.counterValue:V.value.length),I=Oa(),{filteredItems:k,getMatches:C}=ia(e,b,()=>i.value?"":A.value),T=m(()=>e.hideSelected?k.value.filter(J=>!V.value.some(G=>G.value===J.value)):k.value),P=m(()=>!!(e.chips||n.chip)),_=m(()=>P.value||!!n.selection),B=m(()=>V.value.map(J=>J.props.value)),N=m(()=>(e.autoSelectFirst===!0||e.autoSelectFirst==="exact"&&A.value===T.value[0]?.title)&&T.value.length>0&&!i.value&&!r.value),z=m(()=>e.hideNoData&&!T.value.length||e.readonly||I?.isReadonly.value),j=H(),ee=_o(j,l);function Z(J){e.openOnClear&&(f.value=!0),A.value=""}function $(){z.value||(f.value=!0)}function E(J){z.value||(o.value&&(J.preventDefault(),J.stopPropagation()),f.value=!f.value)}function R(J){ya(J)&&l.value?.focus()}function q(J){if(e.readonly||I?.isReadonly.value)return;const G=l.value.selectionStart,de=V.value.length;if((v.value>-1||["Enter","ArrowDown","ArrowUp"].includes(J.key))&&J.preventDefault(),["Enter","ArrowDown"].includes(J.key)&&(f.value=!0),["Escape"].includes(J.key)&&(f.value=!1),N.value&&["Enter","Tab"].includes(J.key)&&!V.value.some(ne=>{let{value:te}=ne;return te===T.value[0].value})&&xe(T.value[0]),J.key==="ArrowDown"&&N.value&&j.value?.focus("next"),["Backspace","Delete"].includes(J.key)){if(!e.multiple&&_.value&&V.value.length>0&&!A.value)return xe(V.value[0],!1);if(~v.value){const ne=v.value;xe(V.value[v.value],!1),v.value=ne>=de-1?de-2:ne}else J.key==="Backspace"&&!A.value&&(v.value=de-1)}if(e.multiple){if(J.key==="ArrowLeft"){if(v.value<0&&G>0)return;const ne=v.value>-1?v.value-1:de-1;V.value[ne]?v.value=ne:(v.value=-1,l.value.setSelectionRange(A.value?.length,A.value?.length))}if(J.key==="ArrowRight"){if(v.value<0)return;const ne=v.value+1;V.value[ne]?v.value=ne:(v.value=-1,l.value.setSelectionRange(0,0))}}}function ue(J){if(Vn(l.value,":autofill")||Vn(l.value,":-webkit-autofill")){const G=b.value.find(de=>de.title===J.target.value);G&&xe(G)}}function le(){e.eager&&d.value?.calculateVisibleItems()}function oe(){o.value&&(i.value=!0,l.value?.focus())}function W(J){o.value=!0,setTimeout(()=>{r.value=!0})}function se(J){r.value=!1}function ce(J){(J==null||J===""&&!e.multiple&&!_.value)&&(V.value=[])}const De=Y(!1);function xe(J){let G=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;if(!(!J||J.props.disabled))if(e.multiple){const de=V.value.findIndex(te=>e.valueComparator(te.value,J.value)),ne=G??!~de;if(~de){const te=ne?[...V.value,J]:[...V.value];te.splice(de,1),V.value=te}else ne&&(V.value=[...V.value,J]);e.clearOnSelect&&(A.value="")}else{const de=G!==!1;V.value=de?[J]:[],A.value=de&&!_.value?J.title:"",ke(()=>{f.value=!1,i.value=!0})}}return U(o,(J,G)=>{J!==G&&(J?(De.value=!0,A.value=e.multiple||_.value?"":String(V.value.at(-1)?.props.title??""),i.value=!0,ke(()=>De.value=!1)):(!e.multiple&&A.value==null&&(V.value=[]),f.value=!1,V.value.some(de=>{let{title:ne}=de;return ne===A.value})||(A.value=""),v.value=-1))}),U(A,J=>{!o.value||De.value||(J&&(f.value=!0),i.value=!J)}),U(f,()=>{if(!e.hideSelected&&f.value&&V.value.length){const J=T.value.findIndex(G=>V.value.some(de=>G.value===de.value));Se&&window.requestAnimationFrame(()=>{J>=0&&d.value?.scrollToIndex(J)})}}),U(()=>e.items,(J,G)=>{f.value||o.value&&!G.length&&J.length&&(f.value=!0)}),O(()=>{const J=!!(!e.hideNoData||T.value.length||n["prepend-item"]||n["append-item"]||n["no-data"]),G=V.value.length>0,de=Zt.filterProps(e);return s(Zt,F({ref:l},de,{modelValue:A.value,"onUpdate:modelValue":[ne=>A.value=ne,ce],focused:o.value,"onUpdate:focused":ne=>o.value=ne,validationValue:V.externalValue,counterValue:x.value,dirty:G,onChange:ue,class:["v-autocomplete",`v-autocomplete--${e.multiple?"multiple":"single"}`,{"v-autocomplete--active-menu":f.value,"v-autocomplete--chips":!!e.chips,"v-autocomplete--selection-slot":!!_.value,"v-autocomplete--selecting-index":v.value>-1},e.class],style:e.style,readonly:e.readonly,placeholder:G?void 0:e.placeholder,"onClick:clear":Z,"onMousedown:control":$,onKeydown:q}),{...n,default:()=>s(ae,null,[s(pn,F({ref:u,modelValue:f.value,"onUpdate:modelValue":ne=>f.value=ne,activator:"parent",contentClass:"v-autocomplete__content",disabled:z.value,eager:e.eager,maxHeight:310,openOnClick:!1,closeOnContentClick:!1,transition:e.transition,onAfterEnter:le,onAfterLeave:oe},e.menuProps),{default:()=>[J&&s(Ea,F({ref:j,selected:B.value,selectStrategy:e.multiple?"independent":"single-independent",onMousedown:ne=>ne.preventDefault(),onKeydown:R,onFocusin:W,onFocusout:se,tabindex:"-1","aria-live":"polite",color:e.itemColor??e.color},ee,e.listProps),{default:()=>[n["prepend-item"]?.(),!T.value.length&&!e.hideNoData&&(n["no-data"]?.()??s(At,{title:a(e.noDataText)},null)),s(Wa,{ref:d,renderless:!0,items:T.value},{default:ne=>{let{item:te,index:fe,itemRef:Ke}=ne;const kt=F(te.props,{ref:Ke,key:fe,active:N.value&&fe===0?!0:void 0,onClick:()=>xe(te,null)});return n.item?.({item:te,index:fe,props:kt})??s(At,F(kt,{role:"option"}),{prepend:ht=>{let{isSelected:He}=ht;return s(ae,null,[e.multiple&&!e.hideSelected?s(Lt,{key:te.value,modelValue:He,ripple:!1,tabindex:"-1"},null):void 0,te.props.prependAvatar&&s(ft,{image:te.props.prependAvatar},null),te.props.prependIcon&&s(ye,{icon:te.props.prependIcon},null)])},title:()=>i.value?te.title:Km(te.title,C(te)?.title,A.value?.length??0)})}}),n["append-item"]?.()]})]}),V.value.map((ne,te)=>{function fe(He){He.stopPropagation(),He.preventDefault(),xe(ne,!1)}const Ke={"onClick:close":fe,onKeydown(He){He.key!=="Enter"&&He.key!==" "||(He.preventDefault(),He.stopPropagation(),fe(He))},onMousedown(He){He.preventDefault(),He.stopPropagation()},modelValue:!0,"onUpdate:modelValue":void 0},kt=P.value?!!n.chip:!!n.selection,ht=kt?Ia(P.value?n.chip({item:ne,index:te,props:Ke}):n.selection({item:ne,index:te})):void 0;if(!(kt&&!ht))return s("div",{key:ne.value,class:["v-autocomplete__selection",te===v.value&&["v-autocomplete__selection--selected",w.value]],style:te===v.value?p.value:{}},[P.value?n.chip?s(ve,{key:"chip-defaults",defaults:{VChip:{closable:e.closableChips,size:"small",text:ne.title}}},{default:()=>[ht]}):s(Dn,F({key:"chip",closable:e.closableChips,size:"small",text:ne.title,disabled:ne.props.disabled},Ke),null):ht??s("span",{class:"v-autocomplete__selection-text"},[ne.title,e.multiple&&te<V.value.length-1&&s("span",{class:"v-autocomplete__selection-comma"},[Ft(",")])])])})]),"append-inner":function(){for(var ne=arguments.length,te=new Array(ne),fe=0;fe<ne;fe++)te[fe]=arguments[fe];return s(ae,null,[n["append-inner"]?.(...te),e.menuIcon?s(ye,{class:"v-autocomplete__menu-icon",icon:e.menuIcon,onMousedown:E,onClick:fs,"aria-label":a(h.value),title:a(h.value),tabindex:"-1"},null):void 0])}})}),gt({isFocused:o,isPristine:i,menu:f,search:A,filteredItems:k,select:xe},l)}}),Zm=L({bordered:Boolean,color:String,content:[Number,String],dot:Boolean,floating:Boolean,icon:ie,inline:Boolean,label:{type:String,default:"$vuetify.badge"},max:[Number,String],modelValue:{type:Boolean,default:!0},offsetX:[Number,String],offsetY:[Number,String],textColor:String,...X(),...Dt({location:"top end"}),...Ve(),...re(),...me(),...St({transition:"scale-rotate-transition"})},"VBadge"),Jm=M()({name:"VBadge",inheritAttrs:!1,props:Zm(),setup(e,t){const{backgroundColorClasses:n,backgroundColorStyles:a}=Ce(D(e,"color")),{roundedClasses:l}=Le(e),{t:o}=Ae(),{textColorClasses:i,textColorStyles:r}=Ge(D(e,"textColor")),{themeClasses:u}=vo(),{locationStyles:d}=hn(e,!0,c=>(e.floating?e.dot?2:4:e.dot?8:12)+(["top","bottom"].includes(c)?+(e.offsetY??0):["left","right"].includes(c)?+(e.offsetX??0):0));return O(()=>{const c=Number(e.content),f=!e.max||isNaN(c)?e.content:c<=+e.max?c:`${e.max}+`,[v,g]=gl(t.attrs,["aria-atomic","aria-label","aria-live","role","title"]);return s(e.tag,F({class:["v-badge",{"v-badge--bordered":e.bordered,"v-badge--dot":e.dot,"v-badge--floating":e.floating,"v-badge--inline":e.inline},e.class]},g,{style:e.style}),{default:()=>[s("div",{class:"v-badge__wrapper"},[t.slots.default?.(),s(Xe,{transition:e.transition},{default:()=>[Ie(s("span",F({class:["v-badge__badge",u.value,n.value,l.value,i.value],style:[a.value,r.value,e.inline?{}:d.value],"aria-atomic":"true","aria-label":o(e.label,c),"aria-live":"polite",role:"status"},v),[e.dot?void 0:t.slots.badge?t.slots.badge?.():e.icon?s(ye,{icon:e.icon},null):f]),[[bt,e.modelValue]])]})])]})}),{}}}),Qm=L({color:String,density:String,...X()},"VBannerActions"),Qr=M()({name:"VBannerActions",props:Qm(),setup(e,t){let{slots:n}=t;return pe({VBtn:{color:e.color,density:e.density,slim:!0,variant:"text"}}),O(()=>s("div",{class:["v-banner-actions",e.class],style:e.style},[n.default?.()])),{}}}),eu=Pt("v-banner-text"),eg=L({avatar:String,bgColor:String,color:String,icon:ie,lines:String,stacked:Boolean,sticky:Boolean,text:String,...lt(),...X(),...ze(),...Oe(),...sn({mobile:null}),...Ne(),...Dt(),...En(),...Ve(),...re(),...me()},"VBanner"),tg=M()({name:"VBanner",props:eg(),setup(e,t){let{slots:n}=t;const{backgroundColorClasses:a,backgroundColorStyles:l}=Ce(e,"bgColor"),{borderClasses:o}=dt(e),{densityClasses:i}=et(e),{displayClasses:r,mobile:u}=ut(e),{dimensionStyles:d}=$e(e),{elevationClasses:c}=Ye(e),{locationStyles:f}=hn(e),{positionClasses:v}=Fn(e),{roundedClasses:g}=Le(e),{themeClasses:h}=be(e),b=D(e,"color"),y=D(e,"density");pe({VBannerActions:{color:b,density:y}}),O(()=>{const S=!!(e.text||n.text),w=!!(e.avatar||e.icon),p=!!(w||n.prepend);return s(e.tag,{class:["v-banner",{"v-banner--stacked":e.stacked||u.value,"v-banner--sticky":e.sticky,[`v-banner--${e.lines}-line`]:!!e.lines},h.value,a.value,o.value,i.value,r.value,c.value,v.value,g.value,e.class],style:[l.value,d.value,f.value,e.style],role:"banner"},{default:()=>[p&&s("div",{key:"prepend",class:"v-banner__prepend"},[n.prepend?s(ve,{key:"prepend-defaults",disabled:!w,defaults:{VAvatar:{color:b.value,density:y.value,icon:e.icon,image:e.avatar}}},n.prepend):s(ft,{key:"prepend-avatar",color:b.value,density:y.value,icon:e.icon,image:e.avatar},null)]),s("div",{class:"v-banner__content"},[S&&s(eu,{key:"text"},{default:()=>[n.text?.()??e.text]}),n.default?.()]),n.actions&&s(Qr,{key:"actions"},n.actions)]})})}}),ng=L({baseColor:String,bgColor:String,color:String,grow:Boolean,mode:{type:String,validator:e=>!e||["horizontal","shift"].includes(e)},height:{type:[Number,String],default:56},active:{type:Boolean,default:!0},...lt(),...X(),...ze(),...Ne(),...Ve(),...rn({name:"bottom-navigation"}),...re({tag:"header"}),...vn({selectedClass:"v-btn--selected"}),...me()},"VBottomNavigation"),ag=M()({name:"VBottomNavigation",props:ng(),emits:{"update:active":e=>!0,"update:modelValue":e=>!0},setup(e,t){let{slots:n}=t;const{themeClasses:a}=vo(),{borderClasses:l}=dt(e),{backgroundColorClasses:o,backgroundColorStyles:i}=Ce(D(e,"bgColor")),{densityClasses:r}=et(e),{elevationClasses:u}=Ye(e),{roundedClasses:d}=Le(e),{ssrBootStyles:c}=cn(),f=m(()=>Number(e.height)-(e.density==="comfortable"?8:0)-(e.density==="compact"?16:0)),v=Q(e,"active",e.active),{layoutItemStyles:g}=un({id:e.name,order:m(()=>parseInt(e.order,10)),position:m(()=>"bottom"),layoutSize:m(()=>v.value?f.value:0),elementSize:f,active:v,absolute:D(e,"absolute")});return Ht(e,To),pe({VBtn:{baseColor:D(e,"baseColor"),color:D(e,"color"),density:D(e,"density"),stacked:m(()=>e.mode!=="horizontal"),variant:"text"}},{scoped:!0}),O(()=>s(e.tag,{class:["v-bottom-navigation",{"v-bottom-navigation--active":v.value,"v-bottom-navigation--grow":e.grow,"v-bottom-navigation--shift":e.mode==="shift"},a.value,o.value,l.value,r.value,u.value,d.value,e.class],style:[i.value,g.value,{height:K(f.value)},c.value,e.style]},{default:()=>[n.default&&s("div",{class:"v-bottom-navigation__content"},[n.default()])]})),{}}}),tu=L({fullscreen:Boolean,retainFocus:{type:Boolean,default:!0},scrollable:Boolean,...ta({origin:"center center",scrollStrategy:"block",transition:{component:Ba},zIndex:2400})},"VDialog"),Ml=M()({name:"VDialog",props:tu(),emits:{"update:modelValue":e=>!0,afterEnter:()=>!0,afterLeave:()=>!0},setup(e,t){let{emit:n,slots:a}=t;const l=Q(e,"modelValue"),{scopeId:o}=gn(),i=H();function r(c){const f=c.relatedTarget,v=c.target;if(f!==v&&i.value?.contentEl&&i.value?.globalTop&&![document,i.value.contentEl].includes(v)&&!i.value.contentEl.contains(v)){const g=Rn(i.value.contentEl);if(!g.length)return;const h=g[0],b=g[g.length-1];f===h?b.focus():h.focus()}}Se&&U(()=>l.value&&e.retainFocus,c=>{c?document.addEventListener("focusin",r):document.removeEventListener("focusin",r)},{immediate:!0});function u(){n("afterEnter"),i.value?.contentEl&&!i.value.contentEl.contains(document.activeElement)&&i.value.contentEl.focus({preventScroll:!0})}function d(){n("afterLeave")}return U(l,async c=>{c||(await ke(),i.value.activatorEl?.focus({preventScroll:!0}))}),O(()=>{const c=xt.filterProps(e),f=F({"aria-haspopup":"dialog"},e.activatorProps),v=F({tabindex:-1},e.contentProps);return s(xt,F({ref:i,class:["v-dialog",{"v-dialog--fullscreen":e.fullscreen,"v-dialog--scrollable":e.scrollable},e.class],style:e.style},c,{modelValue:l.value,"onUpdate:modelValue":g=>l.value=g,"aria-modal":"true",activatorProps:f,contentProps:v,role:"dialog",onAfterEnter:u,onAfterLeave:d},o),{activator:a.activator,default:function(){for(var g=arguments.length,h=new Array(g),b=0;b<g;b++)h[b]=arguments[b];return s(ve,{root:"VDialog"},{default:()=>[a.default?.(...h)]})}})}),gt({},i)}}),lg=L({inset:Boolean,...tu({transition:"bottom-sheet-transition"})},"VBottomSheet"),og=M()({name:"VBottomSheet",props:lg(),emits:{"update:modelValue":e=>!0},setup(e,t){let{slots:n}=t;const a=Q(e,"modelValue");return O(()=>{const l=Ml.filterProps(e);return s(Ml,F(l,{contentClass:["v-bottom-sheet__content",e.contentClass],modelValue:a.value,"onUpdate:modelValue":o=>a.value=o,class:["v-bottom-sheet",{"v-bottom-sheet--inset":e.inset},e.class],style:e.style}),n)}),{}}}),ig=L({divider:[Number,String],...X()},"VBreadcrumbsDivider"),nu=M()({name:"VBreadcrumbsDivider",props:ig(),setup(e,t){let{slots:n}=t;return O(()=>s("li",{class:["v-breadcrumbs-divider",e.class],style:e.style},[n?.default?.()??e.divider])),{}}}),sg=L({active:Boolean,activeClass:String,activeColor:String,color:String,disabled:Boolean,title:String,...X(),...Jn(),...re({tag:"li"})},"VBreadcrumbsItem"),au=M()({name:"VBreadcrumbsItem",props:sg(),setup(e,t){let{slots:n,attrs:a}=t;const l=Zn(e,a),o=m(()=>e.active||l.isActive?.value),i=m(()=>o.value?e.activeColor:e.color),{textColorClasses:r,textColorStyles:u}=Ge(i);return O(()=>s(e.tag,{class:["v-breadcrumbs-item",{"v-breadcrumbs-item--active":o.value,"v-breadcrumbs-item--disabled":e.disabled,[`${e.activeClass}`]:o.value&&e.activeClass},r.value,e.class],style:[u.value,e.style],"aria-current":o.value?"page":void 0},{default:()=>[l.isLink.value?s("a",F({class:"v-breadcrumbs-item--link",onClick:l.navigate},l.linkProps),[n.default?.()??e.title]):n.default?.()??e.title]})),{}}}),rg=L({activeClass:String,activeColor:String,bgColor:String,color:String,disabled:Boolean,divider:{type:String,default:"/"},icon:ie,items:{type:Array,default:()=>[]},...X(),...ze(),...Ve(),...re({tag:"ul"})},"VBreadcrumbs"),ug=M()({name:"VBreadcrumbs",props:rg(),setup(e,t){let{slots:n}=t;const{backgroundColorClasses:a,backgroundColorStyles:l}=Ce(D(e,"bgColor")),{densityClasses:o}=et(e),{roundedClasses:i}=Le(e);pe({VBreadcrumbsDivider:{divider:D(e,"divider")},VBreadcrumbsItem:{activeClass:D(e,"activeClass"),activeColor:D(e,"activeColor"),color:D(e,"color"),disabled:D(e,"disabled")}});const r=m(()=>e.items.map(u=>typeof u=="string"?{item:{title:u},raw:u}:{item:u,raw:u}));return O(()=>{const u=!!(n.prepend||e.icon);return s(e.tag,{class:["v-breadcrumbs",a.value,o.value,i.value,e.class],style:[l.value,e.style]},{default:()=>[u&&s("li",{key:"prepend",class:"v-breadcrumbs__prepend"},[n.prepend?s(ve,{key:"prepend-defaults",disabled:!e.icon,defaults:{VIcon:{icon:e.icon,start:!0}}},n.prepend):s(ye,{key:"prepend-icon",start:!0,icon:e.icon},null)]),r.value.map((d,c,f)=>{let{item:v,raw:g}=d;return s(ae,null,[n.item?.({item:v,index:c})??s(au,F({key:c,disabled:c>=f.length-1},typeof v=="string"?{title:v}:v),{default:n.title?()=>n.title?.({item:v,index:c}):void 0}),c<f.length-1&&s(nu,null,{default:n.divider?()=>n.divider?.({item:g,index:c}):void 0})])}),n.default?.()]})}),{}}}),cg=e=>{const{touchstartX:t,touchendX:n,touchstartY:a,touchendY:l}=e,o=.5,i=16;e.offsetX=n-t,e.offsetY=l-a,Math.abs(e.offsetY)<o*Math.abs(e.offsetX)&&(e.left&&n<t-i&&e.left(e),e.right&&n>t+i&&e.right(e)),Math.abs(e.offsetX)<o*Math.abs(e.offsetY)&&(e.up&&l<a-i&&e.up(e),e.down&&l>a+i&&e.down(e))};function dg(e,t){const n=e.changedTouches[0];t.touchstartX=n.clientX,t.touchstartY=n.clientY,t.start?.({originalEvent:e,...t})}function vg(e,t){const n=e.changedTouches[0];t.touchendX=n.clientX,t.touchendY=n.clientY,t.end?.({originalEvent:e,...t}),cg(t)}function fg(e,t){const n=e.changedTouches[0];t.touchmoveX=n.clientX,t.touchmoveY=n.clientY,t.move?.({originalEvent:e,...t})}function mg(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};const t={touchstartX:0,touchstartY:0,touchendX:0,touchendY:0,touchmoveX:0,touchmoveY:0,offsetX:0,offsetY:0,left:e.left,right:e.right,up:e.up,down:e.down,start:e.start,move:e.move,end:e.end};return{touchstart:n=>dg(n,t),touchend:n=>vg(n,t),touchmove:n=>fg(n,t)}}function gg(e,t){const n=t.value,a=n?.parent?e.parentElement:e,l=n?.options??{passive:!0},o=t.instance?.$.uid;if(!a||!o)return;const i=mg(t.value);a._touchHandlers=a._touchHandlers??Object.create(null),a._touchHandlers[o]=i,rs(i).forEach(r=>{a.addEventListener(r,i[r],l)})}function hg(e,t){const n=t.value?.parent?e.parentElement:e,a=t.instance?.$.uid;if(!n?._touchHandlers||!a)return;const l=n._touchHandlers[a];rs(l).forEach(o=>{n.removeEventListener(o,l[o])}),delete n._touchHandlers[a]}const lu={mounted:gg,unmounted:hg},ou=Symbol.for("vuetify:v-window"),iu=Symbol.for("vuetify:v-window-group"),Ya=L({continuous:Boolean,nextIcon:{type:[Boolean,String,Function,Object],default:"$next"},prevIcon:{type:[Boolean,String,Function,Object],default:"$prev"},reverse:Boolean,showArrows:{type:[Boolean,String],validator:e=>typeof e=="boolean"||e==="hover"},touch:{type:[Object,Boolean],default:void 0},direction:{type:String,default:"horizontal"},modelValue:null,disabled:Boolean,selectedClass:{type:String,default:"v-window-item--active"},mandatory:{type:[Boolean,String],default:"force"},...X(),...re(),...me()},"VWindow"),Jt=M()({name:"VWindow",directives:{Touch:lu},props:Ya(),emits:{"update:modelValue":e=>!0},setup(e,t){let{slots:n}=t;const{themeClasses:a}=be(e),{isRtl:l}=Fe(),{t:o}=Ae(),i=Ht(e,iu),r=H(),u=m(()=>l.value?!e.reverse:e.reverse),d=Y(!1),c=m(()=>{const A=e.direction==="vertical"?"y":"x",x=(u.value?!d.value:d.value)?"-reverse":"";return`v-window-${A}${x}-transition`}),f=Y(0),v=H(void 0),g=m(()=>i.items.value.findIndex(A=>i.selected.value.includes(A.id)));U(g,(A,V)=>{const x=i.items.value.length,I=x-1;x<=2?d.value=A<V:A===I&&V===0?d.value=!0:A===0&&V===I?d.value=!1:d.value=A<V}),we(ou,{transition:c,isReversed:d,transitionCount:f,transitionHeight:v,rootRef:r});const h=m(()=>e.continuous||g.value!==0),b=m(()=>e.continuous||g.value!==i.items.value.length-1);function y(){h.value&&i.prev()}function S(){b.value&&i.next()}const w=m(()=>{const A=[],V={icon:l.value?e.nextIcon:e.prevIcon,class:`v-window__${u.value?"right":"left"}`,onClick:i.prev,"aria-label":o("$vuetify.carousel.prev")};A.push(h.value?n.prev?n.prev({props:V}):s(he,V,null):s("div",null,null));const x={icon:l.value?e.prevIcon:e.nextIcon,class:`v-window__${u.value?"left":"right"}`,onClick:i.next,"aria-label":o("$vuetify.carousel.next")};return A.push(b.value?n.next?n.next({props:x}):s(he,x,null):s("div",null,null)),A}),p=m(()=>e.touch===!1?e.touch:{...{left:()=>{u.value?y():S()},right:()=>{u.value?S():y()},start:V=>{let{originalEvent:x}=V;x.stopPropagation()}},...e.touch===!0?{}:e.touch});return O(()=>Ie(s(e.tag,{ref:r,class:["v-window",{"v-window--show-arrows-on-hover":e.showArrows==="hover"},a.value,e.class],style:e.style},{default:()=>[s("div",{class:"v-window__container",style:{height:v.value}},[n.default?.({group:i}),e.showArrows!==!1&&s("div",{class:"v-window__controls"},[w.value])]),n.additional?.({group:i})]}),[[st("touch"),p.value]])),{group:i}}}),yg=L({color:String,cycle:Boolean,delimiterIcon:{type:ie,default:"$delimiter"},height:{type:[Number,String],default:500},hideDelimiters:Boolean,hideDelimiterBackground:Boolean,interval:{type:[Number,String],default:6e3,validator:e=>Number(e)>0},progress:[Boolean,String],verticalDelimiters:[Boolean,String],...Ya({continuous:!0,mandatory:"force",showArrows:!0})},"VCarousel"),bg=M()({name:"VCarousel",props:yg(),emits:{"update:modelValue":e=>!0},setup(e,t){let{slots:n}=t;const a=Q(e,"modelValue"),{t:l}=Ae(),o=H();let i=-1;U(a,u),U(()=>e.interval,u),U(()=>e.cycle,d=>{d?u():window.clearTimeout(i)}),Qe(r);function r(){!e.cycle||!o.value||(i=window.setTimeout(o.value.group.next,+e.interval>0?+e.interval:6e3))}function u(){window.clearTimeout(i),window.requestAnimationFrame(r)}return O(()=>{const d=Jt.filterProps(e);return s(Jt,F({ref:o},d,{modelValue:a.value,"onUpdate:modelValue":c=>a.value=c,class:["v-carousel",{"v-carousel--hide-delimiter-background":e.hideDelimiterBackground,"v-carousel--vertical-delimiters":e.verticalDelimiters},e.class],style:[{height:K(e.height)},e.style]}),{default:n.default,additional:c=>{let{group:f}=c;return s(ae,null,[!e.hideDelimiters&&s("div",{class:"v-carousel__controls",style:{left:e.verticalDelimiters==="left"&&e.verticalDelimiters?0:"auto",right:e.verticalDelimiters==="right"?0:"auto"}},[f.items.value.length>0&&s(ve,{defaults:{VBtn:{color:e.color,icon:e.delimiterIcon,size:"x-small",variant:"text"}},scoped:!0},{default:()=>[f.items.value.map((v,g)=>{const h={id:`carousel-item-${v.id}`,"aria-label":l("$vuetify.carousel.ariaLabel.delimiter",g+1,f.items.value.length),class:["v-carousel__controls__item",f.isSelected(v.id)&&"v-btn--active"],onClick:()=>f.select(v.id,!0)};return n.item?n.item({props:h,item:v}):s(he,F(v,h),null)})]})]),e.progress&&s(Na,{class:"v-carousel__progress",color:typeof e.progress=="string"?e.progress:void 0,modelValue:(f.getItemIndex(a.value)+1)/f.items.value.length*100},null)])},prev:n.prev,next:n.next})}),{}}}),Ga=L({reverseTransition:{type:[Boolean,String],default:void 0},transition:{type:[Boolean,String],default:void 0},...X(),...fn(),...Po()},"VWindowItem"),Qt=M()({name:"VWindowItem",directives:{Touch:lu},props:Ga(),emits:{"group:selected":e=>!0},setup(e,t){let{slots:n}=t;const a=ge(ou),l=mn(e,iu),{isBooted:o}=cn();if(!a||!l)throw new Error("[Vuetify] VWindowItem must be used inside VWindow");const i=Y(!1),r=m(()=>o.value&&(a.isReversed.value?e.reverseTransition!==!1:e.transition!==!1));function u(){!i.value||!a||(i.value=!1,a.transitionCount.value>0&&(a.transitionCount.value-=1,a.transitionCount.value===0&&(a.transitionHeight.value=void 0)))}function d(){i.value||!a||(i.value=!0,a.transitionCount.value===0&&(a.transitionHeight.value=K(a.rootRef.value?.clientHeight)),a.transitionCount.value+=1)}function c(){u()}function f(h){i.value&&ke(()=>{!r.value||!i.value||!a||(a.transitionHeight.value=K(h.clientHeight))})}const v=m(()=>{const h=a.isReversed.value?e.reverseTransition:e.transition;return r.value?{name:typeof h!="string"?a.transition.value:h,onBeforeEnter:d,onAfterEnter:u,onEnterCancelled:c,onBeforeLeave:d,onAfterLeave:u,onLeaveCancelled:c,onEnter:f}:!1}),{hasContent:g}=Io(e,l.isSelected);return O(()=>s(Xe,{transition:v.value,disabled:!o.value},{default:()=>[Ie(s("div",{class:["v-window-item",l.selectedClass.value,e.class],style:e.style},[g.value&&n.default?.()]),[[bt,l.isSelected.value]])]})),{groupItem:l}}}),Sg=L({...or(),...Ga()},"VCarouselItem"),kg=M()({name:"VCarouselItem",inheritAttrs:!1,props:Sg(),setup(e,t){let{slots:n,attrs:a}=t;O(()=>{const l=_t.filterProps(e),o=Qt.filterProps(e);return s(Qt,F({class:["v-carousel-item",e.class]},o),{default:()=>[s(_t,F(a,l),n)]})})}}),Cg=Pt("v-code"),xg=L({color:{type:Object},disabled:Boolean,dotSize:{type:[Number,String],default:10},height:{type:[Number,String],default:150},width:{type:[Number,String],default:300},...X()},"VColorPickerCanvas"),wg=rt({name:"VColorPickerCanvas",props:xg(),emits:{"update:color":e=>!0,"update:position":e=>!0},setup(e,t){let{emit:n}=t;const a=Y(!1),l=H(),o=Y(parseFloat(e.width)),i=Y(parseFloat(e.height)),r=H({x:0,y:0}),u=m({get:()=>r.value,set(y){if(!l.value)return;const{x:S,y:w}=y;r.value=y,n("update:color",{h:e.color?.h??0,s:Be(S,0,o.value)/o.value,v:1-Be(w,0,i.value)/i.value,a:e.color?.a??1})}}),d=m(()=>{const{x:y,y:S}=u.value,w=parseInt(e.dotSize,10)/2;return{width:K(e.dotSize),height:K(e.dotSize),transform:`translate(${K(y-w)}, ${K(S-w)})`}}),{resizeRef:c}=yt(y=>{if(!c.el?.offsetParent)return;const{width:S,height:w}=y[0].contentRect;o.value=S,i.value=w});function f(y,S,w){const{left:p,top:A,width:V,height:x}=w;u.value={x:Be(y-p,0,V),y:Be(S-A,0,x)}}function v(y){y.type==="mousedown"&&y.preventDefault(),!e.disabled&&(g(y),window.addEventListener("mousemove",g),window.addEventListener("mouseup",h),window.addEventListener("touchmove",g),window.addEventListener("touchend",h))}function g(y){if(e.disabled||!l.value)return;a.value=!0;const S=$c(y);f(S.clientX,S.clientY,l.value.getBoundingClientRect())}function h(){window.removeEventListener("mousemove",g),window.removeEventListener("mouseup",h),window.removeEventListener("touchmove",g),window.removeEventListener("touchend",h)}function b(){if(!l.value)return;const y=l.value,S=y.getContext("2d");if(!S)return;const w=S.createLinearGradient(0,0,y.width,0);w.addColorStop(0,"hsla(0, 0%, 100%, 1)"),w.addColorStop(1,`hsla(${e.color?.h??0}, 100%, 50%, 1)`),S.fillStyle=w,S.fillRect(0,0,y.width,y.height);const p=S.createLinearGradient(0,0,0,y.height);p.addColorStop(0,"hsla(0, 0%, 0%, 0)"),p.addColorStop(1,"hsla(0, 0%, 0%, 1)"),S.fillStyle=p,S.fillRect(0,0,y.width,y.height)}return U(()=>e.color?.h,b,{immediate:!0}),U(()=>[o.value,i.value],(y,S)=>{b(),r.value={x:u.value.x*y[0]/S[0],y:u.value.y*y[1]/S[1]}},{flush:"post"}),U(()=>e.color,()=>{if(a.value){a.value=!1;return}r.value=e.color?{x:e.color.s*o.value,y:(1-e.color.v)*i.value}:{x:0,y:0}},{deep:!0,immediate:!0}),Qe(()=>b()),O(()=>s("div",{ref:c,class:["v-color-picker-canvas",e.class],style:e.style,onMousedown:v,onTouchstartPassive:v},[s("canvas",{ref:l,width:o.value,height:i.value},null),e.color&&s("div",{class:["v-color-picker-canvas__dot",{"v-color-picker-canvas__dot--disabled":e.disabled}],style:d.value},null)])),{}}});function Vg(e,t){if(t){const{a:n,...a}=e;return a}return e}function Pg(e,t){if(t==null||typeof t=="string"){const n=Ps(e);return e.a===1?n.slice(0,7):n}if(typeof t=="object"){let n;return jt(t,["r","g","b"])?n=pt(e):jt(t,["h","s","l"])?n=Ss(e):jt(t,["h","s","v"])&&(n=e),Vg(n,!jt(t,["a"])&&e.a===1)}return e}const xn={h:0,s:0,v:0,a:1},El={inputProps:{type:"number",min:0},inputs:[{label:"R",max:255,step:1,getValue:e=>Math.round(e.r),getColor:(e,t)=>({...e,r:Number(t)})},{label:"G",max:255,step:1,getValue:e=>Math.round(e.g),getColor:(e,t)=>({...e,g:Number(t)})},{label:"B",max:255,step:1,getValue:e=>Math.round(e.b),getColor:(e,t)=>({...e,b:Number(t)})},{label:"A",max:1,step:.01,getValue:e=>{let{a:t}=e;return t!=null?Math.round(t*100)/100:1},getColor:(e,t)=>({...e,a:Number(t)})}],to:pt,from:pa},Ig={...El,inputs:El.inputs?.slice(0,3)},Fl={inputProps:{type:"number",min:0},inputs:[{label:"H",max:360,step:1,getValue:e=>Math.round(e.h),getColor:(e,t)=>({...e,h:Number(t)})},{label:"S",max:1,step:.01,getValue:e=>Math.round(e.s*100)/100,getColor:(e,t)=>({...e,s:Number(t)})},{label:"L",max:1,step:.01,getValue:e=>Math.round(e.l*100)/100,getColor:(e,t)=>({...e,l:Number(t)})},{label:"A",max:1,step:.01,getValue:e=>{let{a:t}=e;return t!=null?Math.round(t*100)/100:1},getColor:(e,t)=>({...e,a:Number(t)})}],to:Ss,from:oo},pg={...Fl,inputs:Fl.inputs.slice(0,3)},su={inputProps:{type:"text"},inputs:[{label:"HEXA",getValue:e=>e,getColor:(e,t)=>t}],to:Ps,from:Vs},_g={...su,inputs:[{label:"HEX",getValue:e=>e.slice(0,7),getColor:(e,t)=>t}]},qt={rgb:Ig,rgba:El,hsl:pg,hsla:Fl,hex:_g,hexa:su},Ag=e=>{let{label:t,...n}=e;return s("div",{class:"v-color-picker-edit__input"},[s("input",n,null),s("span",null,[t])])},Lg=L({color:Object,disabled:Boolean,mode:{type:String,default:"rgba",validator:e=>Object.keys(qt).includes(e)},modes:{type:Array,default:()=>Object.keys(qt),validator:e=>Array.isArray(e)&&e.every(t=>Object.keys(qt).includes(t))},...X()},"VColorPickerEdit"),Tg=rt({name:"VColorPickerEdit",props:Lg(),emits:{"update:color":e=>!0,"update:mode":e=>!0},setup(e,t){let{emit:n}=t;const a=m(()=>e.modes.map(o=>({...qt[o],name:o}))),l=m(()=>{const o=a.value.find(r=>r.name===e.mode);if(!o)return[];const i=e.color?o.to(e.color):null;return o.inputs?.map(r=>{let{getValue:u,getColor:d,...c}=r;return{...o.inputProps,...c,disabled:e.disabled,value:i&&u(i),onChange:f=>{const v=f.target;v&&n("update:color",o.from(d(i??o.to(xn),v.value)))}}})});return O(()=>s("div",{class:["v-color-picker-edit",e.class],style:e.style},[l.value?.map(o=>s(Ag,o,null)),a.value.length>1&&s(he,{icon:"$unfold",size:"x-small",variant:"plain",onClick:()=>{const o=a.value.findIndex(i=>i.name===e.mode);n("update:mode",a.value[(o+1)%a.value.length].name)}},null)])),{}}}),Do=Symbol.for("vuetify:v-slider");function Ol(e,t,n){const a=n==="vertical",l=t.getBoundingClientRect(),o="touches"in e?e.touches[0]:e;return a?o.clientY-(l.top+l.height/2):o.clientX-(l.left+l.width/2)}function Bg(e,t){return"touches"in e&&e.touches.length?e.touches[0][t]:"changedTouches"in e&&e.changedTouches.length?e.changedTouches[0][t]:e[t]}const ru=L({disabled:{type:Boolean,default:null},error:Boolean,readonly:{type:Boolean,default:null},max:{type:[Number,String],default:100},min:{type:[Number,String],default:0},step:{type:[Number,String],default:0},thumbColor:String,thumbLabel:{type:[Boolean,String],default:void 0,validator:e=>typeof e=="boolean"||e==="always"},thumbSize:{type:[Number,String],default:20},showTicks:{type:[Boolean,String],default:!1,validator:e=>typeof e=="boolean"||e==="always"},ticks:{type:[Array,Object]},tickSize:{type:[Number,String],default:2},color:String,trackColor:String,trackFillColor:String,trackSize:{type:[Number,String],default:4},direction:{type:String,default:"horizontal",validator:e=>["vertical","horizontal"].includes(e)},reverse:Boolean,...Ve(),...Ne({elevation:2}),ripple:{type:Boolean,default:!0}},"Slider"),uu=e=>{const t=m(()=>parseFloat(e.min)),n=m(()=>parseFloat(e.max)),a=m(()=>+e.step>0?parseFloat(e.step):0),l=m(()=>Math.max(ti(a.value),ti(t.value)));function o(i){if(i=parseFloat(i),a.value<=0)return i;const r=Be(i,t.value,n.value),u=t.value%a.value,d=Math.round((r-u)/a.value)*a.value+u;return parseFloat(Math.min(d,n.value).toFixed(l.value))}return{min:t,max:n,step:a,decimals:l,roundValue:o}},cu=e=>{let{props:t,steps:n,onSliderStart:a,onSliderMove:l,onSliderEnd:o,getActiveThumb:i}=e;const{isRtl:r}=Fe(),u=D(t,"reverse"),d=m(()=>t.direction==="vertical"),c=m(()=>d.value!==u.value),{min:f,max:v,step:g,decimals:h,roundValue:b}=n,y=m(()=>parseInt(t.thumbSize,10)),S=m(()=>parseInt(t.tickSize,10)),w=m(()=>parseInt(t.trackSize,10)),p=m(()=>(v.value-f.value)/g.value),A=D(t,"disabled"),V=m(()=>t.error||t.disabled?void 0:t.thumbColor??t.color),x=m(()=>t.error||t.disabled?void 0:t.trackColor??t.color),I=m(()=>t.error||t.disabled?void 0:t.trackFillColor??t.color),k=Y(!1),C=Y(0),T=H(),P=H();function _(W){const se=t.direction==="vertical",ce=se?"top":"left",De=se?"height":"width",xe=se?"clientY":"clientX",{[ce]:J,[De]:G}=T.value?.$el.getBoundingClientRect(),de=Bg(W,xe);let ne=Math.min(Math.max((de-J-C.value)/G,0),1)||0;return(se?c.value:c.value!==r.value)&&(ne=1-ne),b(f.value+ne*(v.value-f.value))}const B=W=>{o({value:_(W)}),k.value=!1,C.value=0},N=W=>{P.value=i(W),P.value&&(P.value.focus(),k.value=!0,P.value.contains(W.target)?C.value=Ol(W,P.value,t.direction):(C.value=0,l({value:_(W)})),a({value:_(W)}))},z={passive:!0,capture:!0};function j(W){l({value:_(W)})}function ee(W){W.stopPropagation(),W.preventDefault(),B(W),window.removeEventListener("mousemove",j,z),window.removeEventListener("mouseup",ee)}function Z(W){B(W),window.removeEventListener("touchmove",j,z),W.target?.removeEventListener("touchend",Z)}function $(W){N(W),window.addEventListener("touchmove",j,z),W.target?.addEventListener("touchend",Z,{passive:!1})}function E(W){W.preventDefault(),N(W),window.addEventListener("mousemove",j,z),window.addEventListener("mouseup",ee,{passive:!1})}const R=W=>{const se=(W-f.value)/(v.value-f.value)*100;return Be(isNaN(se)?0:se,0,100)},q=D(t,"showTicks"),ue=m(()=>q.value?t.ticks?Array.isArray(t.ticks)?t.ticks.map(W=>({value:W,position:R(W),label:W.toString()})):Object.keys(t.ticks).map(W=>({value:parseFloat(W),position:R(parseFloat(W)),label:t.ticks[W]})):p.value!==1/0?Ct(p.value+1).map(W=>{const se=f.value+W*g.value;return{value:se,position:R(se)}}):[]:[]),le=m(()=>ue.value.some(W=>{let{label:se}=W;return!!se})),oe={activeThumbRef:P,color:D(t,"color"),decimals:h,disabled:A,direction:D(t,"direction"),elevation:D(t,"elevation"),hasLabels:le,isReversed:u,indexFromEnd:c,min:f,max:v,mousePressed:k,numTicks:p,onSliderMousedown:E,onSliderTouchstart:$,parsedTicks:ue,parseMouseMove:_,position:R,readonly:D(t,"readonly"),rounded:D(t,"rounded"),roundValue:b,showTicks:q,startOffset:C,step:g,thumbSize:y,thumbColor:V,thumbLabel:D(t,"thumbLabel"),ticks:D(t,"ticks"),tickSize:S,trackColor:x,trackContainerRef:T,trackFillColor:I,trackSize:w,vertical:d};return we(Do,oe),oe},Dg=L({focused:Boolean,max:{type:Number,required:!0},min:{type:Number,required:!0},modelValue:{type:Number,required:!0},position:{type:Number,required:!0},ripple:{type:[Boolean,Object],default:!0},name:String,...X()},"VSliderThumb"),$l=M()({name:"VSliderThumb",directives:{Ripple:Nt},props:Dg(),emits:{"update:modelValue":e=>!0},setup(e,t){let{slots:n,emit:a}=t;const l=ge(Do),{isRtl:o,rtlClasses:i}=Fe();if(!l)throw new Error("[Vuetify] v-slider-thumb must be used inside v-slider or v-range-slider");const{thumbColor:r,step:u,disabled:d,thumbSize:c,thumbLabel:f,direction:v,isReversed:g,vertical:h,readonly:b,elevation:y,mousePressed:S,decimals:w,indexFromEnd:p}=l,A=m(()=>d.value?void 0:y.value),{elevationClasses:V}=Ye(A),{textColorClasses:x,textColorStyles:I}=Ge(r),{pageup:k,pagedown:C,end:T,home:P,left:_,right:B,down:N,up:z}=ml,j=[k,C,T,P,_,B,N,z],ee=m(()=>u.value?[1,2,3]:[1,5,10]);function Z(E,R){if(!j.includes(E.key))return;E.preventDefault();const q=u.value||.1,ue=(e.max-e.min)/q;if([_,B,N,z].includes(E.key)){const oe=(h.value?[o.value?_:B,g.value?N:z]:p.value!==o.value?[_,z]:[B,z]).includes(E.key)?1:-1,W=E.shiftKey?2:E.ctrlKey?1:0;R=R+oe*q*ee.value[W]}else if(E.key===P)R=e.min;else if(E.key===T)R=e.max;else{const le=E.key===C?1:-1;R=R-le*q*(ue>100?ue/10:10)}return Math.max(e.min,Math.min(e.max,R))}function $(E){const R=Z(E,e.modelValue);R!=null&&a("update:modelValue",R)}return O(()=>{const E=K(p.value?100-e.position:e.position,"%");return s("div",{class:["v-slider-thumb",{"v-slider-thumb--focused":e.focused,"v-slider-thumb--pressed":e.focused&&S.value},e.class,i.value],style:[{"--v-slider-thumb-position":E,"--v-slider-thumb-size":K(c.value)},e.style],role:"slider",tabindex:d.value?-1:0,"aria-label":e.name,"aria-valuemin":e.min,"aria-valuemax":e.max,"aria-valuenow":e.modelValue,"aria-readonly":!!b.value,"aria-orientation":v.value,onKeydown:b.value?void 0:$},[s("div",{class:["v-slider-thumb__surface",x.value,V.value],style:{...I.value}},null),Ie(s("div",{class:["v-slider-thumb__ripple",x.value],style:I.value},null),[[st("ripple"),e.ripple,null,{circle:!0,center:!0}]]),s(go,{origin:"bottom center"},{default:()=>[Ie(s("div",{class:"v-slider-thumb__label-container"},[s("div",{class:["v-slider-thumb__label"]},[s("div",null,[n["thumb-label"]?.({modelValue:e.modelValue})??e.modelValue.toFixed(u.value?w.value:1)])])]),[[bt,f.value&&e.focused||f.value==="always"]])]})])}),{}}}),Mg=L({start:{type:Number,required:!0},stop:{type:Number,required:!0},...X()},"VSliderTrack"),du=M()({name:"VSliderTrack",props:Mg(),emits:{},setup(e,t){let{slots:n}=t;const a=ge(Do);if(!a)throw new Error("[Vuetify] v-slider-track must be inside v-slider or v-range-slider");const{color:l,parsedTicks:o,rounded:i,showTicks:r,tickSize:u,trackColor:d,trackFillColor:c,trackSize:f,vertical:v,min:g,max:h,indexFromEnd:b}=a,{roundedClasses:y}=Le(i),{backgroundColorClasses:S,backgroundColorStyles:w}=Ce(c),{backgroundColorClasses:p,backgroundColorStyles:A}=Ce(d),V=m(()=>`inset-${v.value?"block":"inline"}-${b.value?"end":"start"}`),x=m(()=>v.value?"height":"width"),I=m(()=>({[V.value]:"0%",[x.value]:"100%"})),k=m(()=>e.stop-e.start),C=m(()=>({[V.value]:K(e.start,"%"),[x.value]:K(k.value,"%")})),T=m(()=>r.value?(v.value?o.value.slice().reverse():o.value).map((_,B)=>{const N=_.value!==g.value&&_.value!==h.value?K(_.position,"%"):void 0;return s("div",{key:_.value,class:["v-slider-track__tick",{"v-slider-track__tick--filled":_.position>=e.start&&_.position<=e.stop,"v-slider-track__tick--first":_.value===g.value,"v-slider-track__tick--last":_.value===h.value}],style:{[V.value]:N}},[(_.label||n["tick-label"])&&s("div",{class:"v-slider-track__tick-label"},[n["tick-label"]?.({tick:_,index:B})??_.label])])}):[]);return O(()=>s("div",{class:["v-slider-track",y.value,e.class],style:[{"--v-slider-track-size":K(f.value),"--v-slider-tick-size":K(u.value)},e.style]},[s("div",{class:["v-slider-track__background",p.value,{"v-slider-track__background--opacity":!!l.value||!c.value}],style:{...I.value,...A.value}},null),s("div",{class:["v-slider-track__fill",S.value],style:{...C.value,...w.value}},null),r.value&&s("div",{class:["v-slider-track__ticks",{"v-slider-track__ticks--always-show":r.value==="always"}]},[T.value])])),{}}}),Eg=L({...ea(),...ru(),...Bt(),modelValue:{type:[Number,String],default:0}},"VSlider"),Rl=M()({name:"VSlider",props:Eg(),emits:{"update:focused":e=>!0,"update:modelValue":e=>!0,start:e=>!0,end:e=>!0},setup(e,t){let{slots:n,emit:a}=t;const l=H(),{rtlClasses:o}=Fe(),i=uu(e),r=Q(e,"modelValue",void 0,x=>i.roundValue(x??i.min.value)),{min:u,max:d,mousePressed:c,roundValue:f,onSliderMousedown:v,onSliderTouchstart:g,trackContainerRef:h,position:b,hasLabels:y,readonly:S}=cu({props:e,steps:i,onSliderStart:()=>{a("start",r.value)},onSliderEnd:x=>{let{value:I}=x;const k=f(I);r.value=k,a("end",k)},onSliderMove:x=>{let{value:I}=x;return r.value=f(I)},getActiveThumb:()=>l.value?.$el}),{isFocused:w,focus:p,blur:A}=Tt(e),V=m(()=>b(r.value));return O(()=>{const x=Ue.filterProps(e),I=!!(e.label||n.label||n.prepend);return s(Ue,F({class:["v-slider",{"v-slider--has-labels":!!n["tick-label"]||y.value,"v-slider--focused":w.value,"v-slider--pressed":c.value,"v-slider--disabled":e.disabled},o.value,e.class],style:e.style},x,{focused:w.value}),{...n,prepend:I?k=>s(ae,null,[n.label?.(k)??(e.label?s(Bn,{id:k.id.value,class:"v-slider__label",text:e.label},null):void 0),n.prepend?.(k)]):void 0,default:k=>{let{id:C,messagesId:T}=k;return s("div",{class:"v-slider__container",onMousedown:S.value?void 0:v,onTouchstartPassive:S.value?void 0:g},[s("input",{id:C.value,name:e.name||C.value,disabled:!!e.disabled,readonly:!!e.readonly,tabindex:"-1",value:r.value},null),s(du,{ref:h,start:0,stop:V.value},{"tick-label":n["tick-label"]}),s($l,{ref:l,"aria-describedby":T.value,focused:w.value,min:u.value,max:d.value,modelValue:r.value,"onUpdate:modelValue":P=>r.value=P,position:V.value,elevation:e.elevation,onFocus:p,onBlur:A,ripple:e.ripple,name:e.name},{"thumb-label":n["thumb-label"]})])}})}),{}}}),Fg=L({color:{type:Object},disabled:Boolean,hideAlpha:Boolean,...X()},"VColorPickerPreview"),Og=rt({name:"VColorPickerPreview",props:Fg(),emits:{"update:color":e=>!0},setup(e,t){let{emit:n}=t;const a=new AbortController;Pc(()=>a.abort());async function l(){if(!Zo)return;const o=new window.EyeDropper;try{const i=await o.open({signal:a.signal}),r=Vs(i.sRGBHex);n("update:color",{...e.color??xn,...r})}catch{}}return O(()=>s("div",{class:["v-color-picker-preview",{"v-color-picker-preview--hide-alpha":e.hideAlpha},e.class],style:e.style},[Zo&&s("div",{class:"v-color-picker-preview__eye-dropper",key:"eyeDropper"},[s(he,{onClick:l,icon:"$eyeDropper",variant:"plain",density:"comfortable"},null)]),s("div",{class:"v-color-picker-preview__dot"},[s("div",{style:{background:Cs(e.color??xn)}},null)]),s("div",{class:"v-color-picker-preview__sliders"},[s(Rl,{class:"v-color-picker-preview__track v-color-picker-preview__hue",modelValue:e.color?.h,"onUpdate:modelValue":o=>n("update:color",{...e.color??xn,h:o}),step:0,min:0,max:360,disabled:e.disabled,thumbSize:14,trackSize:8,trackFillColor:"white",hideDetails:!0},null),!e.hideAlpha&&s(Rl,{class:"v-color-picker-preview__track v-color-picker-preview__alpha",modelValue:e.color?.a??1,"onUpdate:modelValue":o=>n("update:color",{...e.color??xn,a:o}),step:1/256,min:0,max:1,disabled:e.disabled,thumbSize:14,trackSize:8,trackFillColor:"white",hideDetails:!0},null)])])),{}}}),$g={base:"#f44336",lighten5:"#ffebee",lighten4:"#ffcdd2",lighten3:"#ef9a9a",lighten2:"#e57373",lighten1:"#ef5350",darken1:"#e53935",darken2:"#d32f2f",darken3:"#c62828",darken4:"#b71c1c",accent1:"#ff8a80",accent2:"#ff5252",accent3:"#ff1744",accent4:"#d50000"},Rg={base:"#e91e63",lighten5:"#fce4ec",lighten4:"#f8bbd0",lighten3:"#f48fb1",lighten2:"#f06292",lighten1:"#ec407a",darken1:"#d81b60",darken2:"#c2185b",darken3:"#ad1457",darken4:"#880e4f",accent1:"#ff80ab",accent2:"#ff4081",accent3:"#f50057",accent4:"#c51162"},Ng={base:"#9c27b0",lighten5:"#f3e5f5",lighten4:"#e1bee7",lighten3:"#ce93d8",lighten2:"#ba68c8",lighten1:"#ab47bc",darken1:"#8e24aa",darken2:"#7b1fa2",darken3:"#6a1b9a",darken4:"#4a148c",accent1:"#ea80fc",accent2:"#e040fb",accent3:"#d500f9",accent4:"#aa00ff"},Hg={base:"#673ab7",lighten5:"#ede7f6",lighten4:"#d1c4e9",lighten3:"#b39ddb",lighten2:"#9575cd",lighten1:"#7e57c2",darken1:"#5e35b1",darken2:"#512da8",darken3:"#4527a0",darken4:"#311b92",accent1:"#b388ff",accent2:"#7c4dff",accent3:"#651fff",accent4:"#6200ea"},zg={base:"#3f51b5",lighten5:"#e8eaf6",lighten4:"#c5cae9",lighten3:"#9fa8da",lighten2:"#7986cb",lighten1:"#5c6bc0",darken1:"#3949ab",darken2:"#303f9f",darken3:"#283593",darken4:"#1a237e",accent1:"#8c9eff",accent2:"#536dfe",accent3:"#3d5afe",accent4:"#304ffe"},Wg={base:"#2196f3",lighten5:"#e3f2fd",lighten4:"#bbdefb",lighten3:"#90caf9",lighten2:"#64b5f6",lighten1:"#42a5f5",darken1:"#1e88e5",darken2:"#1976d2",darken3:"#1565c0",darken4:"#0d47a1",accent1:"#82b1ff",accent2:"#448aff",accent3:"#2979ff",accent4:"#2962ff"},jg={base:"#03a9f4",lighten5:"#e1f5fe",lighten4:"#b3e5fc",lighten3:"#81d4fa",lighten2:"#4fc3f7",lighten1:"#29b6f6",darken1:"#039be5",darken2:"#0288d1",darken3:"#0277bd",darken4:"#01579b",accent1:"#80d8ff",accent2:"#40c4ff",accent3:"#00b0ff",accent4:"#0091ea"},Yg={base:"#00bcd4",lighten5:"#e0f7fa",lighten4:"#b2ebf2",lighten3:"#80deea",lighten2:"#4dd0e1",lighten1:"#26c6da",darken1:"#00acc1",darken2:"#0097a7",darken3:"#00838f",darken4:"#006064",accent1:"#84ffff",accent2:"#18ffff",accent3:"#00e5ff",accent4:"#00b8d4"},Gg={base:"#009688",lighten5:"#e0f2f1",lighten4:"#b2dfdb",lighten3:"#80cbc4",lighten2:"#4db6ac",lighten1:"#26a69a",darken1:"#00897b",darken2:"#00796b",darken3:"#00695c",darken4:"#004d40",accent1:"#a7ffeb",accent2:"#64ffda",accent3:"#1de9b6",accent4:"#00bfa5"},Ug={base:"#4caf50",lighten5:"#e8f5e9",lighten4:"#c8e6c9",lighten3:"#a5d6a7",lighten2:"#81c784",lighten1:"#66bb6a",darken1:"#43a047",darken2:"#388e3c",darken3:"#2e7d32",darken4:"#1b5e20",accent1:"#b9f6ca",accent2:"#69f0ae",accent3:"#00e676",accent4:"#00c853"},Kg={base:"#8bc34a",lighten5:"#f1f8e9",lighten4:"#dcedc8",lighten3:"#c5e1a5",lighten2:"#aed581",lighten1:"#9ccc65",darken1:"#7cb342",darken2:"#689f38",darken3:"#558b2f",darken4:"#33691e",accent1:"#ccff90",accent2:"#b2ff59",accent3:"#76ff03",accent4:"#64dd17"},qg={base:"#cddc39",lighten5:"#f9fbe7",lighten4:"#f0f4c3",lighten3:"#e6ee9c",lighten2:"#dce775",lighten1:"#d4e157",darken1:"#c0ca33",darken2:"#afb42b",darken3:"#9e9d24",darken4:"#827717",accent1:"#f4ff81",accent2:"#eeff41",accent3:"#c6ff00",accent4:"#aeea00"},Xg={base:"#ffeb3b",lighten5:"#fffde7",lighten4:"#fff9c4",lighten3:"#fff59d",lighten2:"#fff176",lighten1:"#ffee58",darken1:"#fdd835",darken2:"#fbc02d",darken3:"#f9a825",darken4:"#f57f17",accent1:"#ffff8d",accent2:"#ffff00",accent3:"#ffea00",accent4:"#ffd600"},Zg={base:"#ffc107",lighten5:"#fff8e1",lighten4:"#ffecb3",lighten3:"#ffe082",lighten2:"#ffd54f",lighten1:"#ffca28",darken1:"#ffb300",darken2:"#ffa000",darken3:"#ff8f00",darken4:"#ff6f00",accent1:"#ffe57f",accent2:"#ffd740",accent3:"#ffc400",accent4:"#ffab00"},Jg={base:"#ff9800",lighten5:"#fff3e0",lighten4:"#ffe0b2",lighten3:"#ffcc80",lighten2:"#ffb74d",lighten1:"#ffa726",darken1:"#fb8c00",darken2:"#f57c00",darken3:"#ef6c00",darken4:"#e65100",accent1:"#ffd180",accent2:"#ffab40",accent3:"#ff9100",accent4:"#ff6d00"},Qg={base:"#ff5722",lighten5:"#fbe9e7",lighten4:"#ffccbc",lighten3:"#ffab91",lighten2:"#ff8a65",lighten1:"#ff7043",darken1:"#f4511e",darken2:"#e64a19",darken3:"#d84315",darken4:"#bf360c",accent1:"#ff9e80",accent2:"#ff6e40",accent3:"#ff3d00",accent4:"#dd2c00"},eh={base:"#795548",lighten5:"#efebe9",lighten4:"#d7ccc8",lighten3:"#bcaaa4",lighten2:"#a1887f",lighten1:"#8d6e63",darken1:"#6d4c41",darken2:"#5d4037",darken3:"#4e342e",darken4:"#3e2723"},th={base:"#607d8b",lighten5:"#eceff1",lighten4:"#cfd8dc",lighten3:"#b0bec5",lighten2:"#90a4ae",lighten1:"#78909c",darken1:"#546e7a",darken2:"#455a64",darken3:"#37474f",darken4:"#263238"},nh={base:"#9e9e9e",lighten5:"#fafafa",lighten4:"#f5f5f5",lighten3:"#eeeeee",lighten2:"#e0e0e0",lighten1:"#bdbdbd",darken1:"#757575",darken2:"#616161",darken3:"#424242",darken4:"#212121"},ah={black:"#000000",white:"#ffffff",transparent:"#ffffff00"},lh={red:$g,pink:Rg,purple:Ng,deepPurple:Hg,indigo:zg,blue:Wg,lightBlue:jg,cyan:Yg,teal:Gg,green:Ug,lightGreen:Kg,lime:qg,yellow:Xg,amber:Zg,orange:Jg,deepOrange:Qg,brown:eh,blueGrey:th,grey:nh,shades:ah},oh=L({swatches:{type:Array,default:()=>ih(lh)},disabled:Boolean,color:Object,maxHeight:[Number,String],...X()},"VColorPickerSwatches");function ih(e){return Object.keys(e).map(t=>{const n=e[t];return n.base?[n.base,n.darken4,n.darken3,n.darken2,n.darken1,n.lighten1,n.lighten2,n.lighten3,n.lighten4,n.lighten5]:[n.black,n.white,n.transparent]})}const sh=rt({name:"VColorPickerSwatches",props:oh(),emits:{"update:color":e=>!0},setup(e,t){let{emit:n}=t;return O(()=>s("div",{class:["v-color-picker-swatches",e.class],style:[{maxHeight:K(e.maxHeight)},e.style]},[s("div",null,[e.swatches.map(a=>s("div",{class:"v-color-picker-swatches__swatch"},[a.map(l=>{const o=vt(l),i=pa(o),r=ks(o);return s("div",{class:"v-color-picker-swatches__color",onClick:()=>i&&n("update:color",i)},[s("div",{style:{background:r}},[e.color&&wt(e.color,i)?s(ye,{size:"x-small",icon:"$success",color:ud(l,"#FFFFFF")>2?"white":"black"},null):void 0])])})]))])])),{}}}),Ua=L({color:String,...lt(),...X(),...Oe(),...Ne(),...Dt(),...En(),...Ve(),...re(),...me()},"VSheet"),en=M()({name:"VSheet",props:Ua(),setup(e,t){let{slots:n}=t;const{themeClasses:a}=be(e),{backgroundColorClasses:l,backgroundColorStyles:o}=Ce(D(e,"color")),{borderClasses:i}=dt(e),{dimensionStyles:r}=$e(e),{elevationClasses:u}=Ye(e),{locationStyles:d}=hn(e),{positionClasses:c}=Fn(e),{roundedClasses:f}=Le(e);return O(()=>s(e.tag,{class:["v-sheet",a.value,l.value,i.value,u.value,c.value,f.value,e.class],style:[o.value,r.value,d.value,e.style]},n)),{}}}),rh=L({canvasHeight:{type:[String,Number],default:150},disabled:Boolean,dotSize:{type:[Number,String],default:10},hideCanvas:Boolean,hideSliders:Boolean,hideInputs:Boolean,mode:{type:String,default:"rgba",validator:e=>Object.keys(qt).includes(e)},modes:{type:Array,default:()=>Object.keys(qt),validator:e=>Array.isArray(e)&&e.every(t=>Object.keys(qt).includes(t))},showSwatches:Boolean,swatches:Array,swatchesMaxHeight:{type:[Number,String],default:150},modelValue:{type:[Object,String]},...Ee(Ua({width:300}),["height","location","minHeight","maxHeight","minWidth","maxWidth"])},"VColorPicker"),uh=rt({name:"VColorPicker",props:rh(),emits:{"update:modelValue":e=>!0,"update:mode":e=>!0},setup(e){const t=Q(e,"mode"),n=H(null),a=Q(e,"modelValue",void 0,u=>{if(u==null||u==="")return null;let d;try{d=pa(vt(u))}catch{return null}return d},u=>u?Pg(u,e.modelValue):null),l=m(()=>a.value?{...a.value,h:n.value??a.value.h}:null),{rtlClasses:o}=Fe();let i=!0;U(a,u=>{if(!i){i=!0;return}u&&(n.value=u.h)},{immediate:!0});const r=u=>{i=!1,n.value=u.h,a.value=u};return Qe(()=>{e.modes.includes(t.value)||(t.value=e.modes[0])}),pe({VSlider:{color:void 0,trackColor:void 0,trackFillColor:void 0}}),O(()=>{const u=en.filterProps(e);return s(en,F({rounded:e.rounded,elevation:e.elevation,theme:e.theme,class:["v-color-picker",o.value,e.class],style:[{"--v-color-picker-color-hsv":Cs({...l.value??xn,a:1})},e.style]},u,{maxWidth:e.width}),{default:()=>[!e.hideCanvas&&s(wg,{key:"canvas",color:l.value,"onUpdate:color":r,disabled:e.disabled,dotSize:e.dotSize,width:e.width,height:e.canvasHeight},null),(!e.hideSliders||!e.hideInputs)&&s("div",{key:"controls",class:"v-color-picker__controls"},[!e.hideSliders&&s(Og,{key:"preview",color:l.value,"onUpdate:color":r,hideAlpha:!t.value.endsWith("a"),disabled:e.disabled},null),!e.hideInputs&&s(Tg,{key:"edit",modes:e.modes,mode:t.value,"onUpdate:mode":d=>t.value=d,color:l.value,"onUpdate:color":r,disabled:e.disabled},null)]),e.showSwatches&&s(sh,{key:"swatches",color:l.value,"onUpdate:color":r,maxHeight:e.swatchesMaxHeight,swatches:e.swatches,disabled:e.disabled},null)]})}),{}}});function ch(e,t,n){if(t==null)return e;if(Array.isArray(t))throw new Error("Multiple matches is not implemented");return typeof t=="number"&&~t?s(ae,null,[s("span",{class:"v-combobox__unmask"},[e.substr(0,t)]),s("span",{class:"v-combobox__mask"},[e.substr(t,n)]),s("span",{class:"v-combobox__unmask"},[e.substr(t+n)])]):e}const dh=L({autoSelectFirst:{type:[Boolean,String]},clearOnSelect:{type:Boolean,default:!0},delimiters:Array,...oa({filterKeys:["title"]}),...Ao({hideNoData:!0,returnObject:!0}),...Ee(za({modelValue:null,role:"combobox"}),["validationValue","dirty","appendInnerIcon"]),...St({transition:!1})},"VCombobox"),vh=M()({name:"VCombobox",props:dh(),emits:{"update:focused":e=>!0,"update:modelValue":e=>!0,"update:search":e=>!0,"update:menu":e=>!0},setup(e,t){let{emit:n,slots:a}=t;const{t:l}=Ae(),o=H(),i=Y(!1),r=Y(!0),u=Y(!1),d=H(),c=H(),f=Q(e,"menu"),v=m({get:()=>f.value,set:G=>{f.value&&!G&&d.value?.ΨopenChildren.size||(f.value=G)}}),g=Y(-1);let h=!1;const b=m(()=>o.value?.color),y=m(()=>v.value?e.closeText:e.openText),{items:S,transformIn:w,transformOut:p}=ko(e),{textColorClasses:A,textColorStyles:V}=Ge(b),x=Q(e,"modelValue",[],G=>w(Pe(G)),G=>{const de=p(G);return e.multiple?de:de[0]??null}),I=Oa(),k=m(()=>!!(e.chips||a.chip)),C=m(()=>k.value||!!a.selection),T=Y(!e.multiple&&!C.value?x.value[0]?.title??"":""),P=m({get:()=>T.value,set:G=>{if(T.value=G??"",!e.multiple&&!C.value&&(x.value=[Mt(e,G)]),G&&e.multiple&&e.delimiters?.length){const de=G.split(new RegExp(`(?:${e.delimiters.join("|")})+`));de.length>1&&(de.forEach(ne=>{ne=ne.trim(),ne&&ce(Mt(e,ne))}),T.value="")}G||(g.value=-1),r.value=!G}}),_=m(()=>typeof e.counterValue=="function"?e.counterValue(x.value):typeof e.counterValue=="number"?e.counterValue:e.multiple?x.value.length:P.value.length);U(T,G=>{h?ke(()=>h=!1):i.value&&!v.value&&(v.value=!0),n("update:search",G)}),U(x,G=>{!e.multiple&&!C.value&&(T.value=G[0]?.title??"")});const{filteredItems:B,getMatches:N}=ia(e,S,()=>r.value?"":P.value),z=m(()=>e.hideSelected?B.value.filter(G=>!x.value.some(de=>de.value===G.value)):B.value),j=m(()=>x.value.map(G=>G.value)),ee=m(()=>(e.autoSelectFirst===!0||e.autoSelectFirst==="exact"&&P.value===z.value[0]?.title)&&z.value.length>0&&!r.value&&!u.value),Z=m(()=>e.hideNoData&&!z.value.length||e.readonly||I?.isReadonly.value),$=H(),E=_o($,o);function R(G){h=!0,e.openOnClear&&(v.value=!0)}function q(){Z.value||(v.value=!0)}function ue(G){Z.value||(i.value&&(G.preventDefault(),G.stopPropagation()),v.value=!v.value)}function le(G){ya(G)&&o.value?.focus()}function oe(G){if(Mc(G)||e.readonly||I?.isReadonly.value)return;const de=o.value.selectionStart,ne=x.value.length;if((g.value>-1||["Enter","ArrowDown","ArrowUp"].includes(G.key))&&G.preventDefault(),["Enter","ArrowDown"].includes(G.key)&&(v.value=!0),["Escape"].includes(G.key)&&(v.value=!1),["Enter","Escape","Tab"].includes(G.key)&&(ee.value&&["Enter","Tab"].includes(G.key)&&!x.value.some(te=>{let{value:fe}=te;return fe===z.value[0].value})&&ce(B.value[0]),r.value=!0),G.key==="ArrowDown"&&ee.value&&$.value?.focus("next"),G.key==="Enter"&&P.value&&(ce(Mt(e,P.value)),C.value&&(T.value="")),["Backspace","Delete"].includes(G.key)){if(!e.multiple&&C.value&&x.value.length>0&&!P.value)return ce(x.value[0],!1);if(~g.value){const te=g.value;ce(x.value[g.value],!1),g.value=te>=ne-1?ne-2:te}else G.key==="Backspace"&&!P.value&&(g.value=ne-1)}if(e.multiple){if(G.key==="ArrowLeft"){if(g.value<0&&de>0)return;const te=g.value>-1?g.value-1:ne-1;x.value[te]?g.value=te:(g.value=-1,o.value.setSelectionRange(P.value.length,P.value.length))}if(G.key==="ArrowRight"){if(g.value<0)return;const te=g.value+1;x.value[te]?g.value=te:(g.value=-1,o.value.setSelectionRange(0,0))}}}function W(){e.eager&&c.value?.calculateVisibleItems()}function se(){i.value&&(r.value=!0,o.value?.focus())}function ce(G){let de=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;if(!(!G||G.props.disabled))if(e.multiple){const ne=x.value.findIndex(fe=>e.valueComparator(fe.value,G.value)),te=de??!~ne;if(~ne){const fe=te?[...x.value,G]:[...x.value];fe.splice(ne,1),x.value=fe}else te&&(x.value=[...x.value,G]);e.clearOnSelect&&(P.value="")}else{const ne=de!==!1;x.value=ne?[G]:[],T.value=ne&&!C.value?G.title:"",ke(()=>{v.value=!1,r.value=!0})}}function De(G){i.value=!0,setTimeout(()=>{u.value=!0})}function xe(G){u.value=!1}function J(G){(G==null||G===""&&!e.multiple&&!C.value)&&(x.value=[])}return U(i,(G,de)=>{if(!(G||G===de)&&(g.value=-1,v.value=!1,P.value)){if(e.multiple){ce(Mt(e,P.value));return}if(!C.value)return;x.value.some(ne=>{let{title:te}=ne;return te===P.value})?T.value="":ce(Mt(e,P.value))}}),U(v,()=>{if(!e.hideSelected&&v.value&&x.value.length){const G=z.value.findIndex(de=>x.value.some(ne=>e.valueComparator(ne.value,de.value)));Se&&window.requestAnimationFrame(()=>{G>=0&&c.value?.scrollToIndex(G)})}}),U(()=>e.items,(G,de)=>{v.value||i.value&&!de.length&&G.length&&(v.value=!0)}),O(()=>{const G=!!(!e.hideNoData||z.value.length||a["prepend-item"]||a["append-item"]||a["no-data"]),de=x.value.length>0,ne=Zt.filterProps(e);return s(Zt,F({ref:o},ne,{modelValue:P.value,"onUpdate:modelValue":[te=>P.value=te,J],focused:i.value,"onUpdate:focused":te=>i.value=te,validationValue:x.externalValue,counterValue:_.value,dirty:de,class:["v-combobox",{"v-combobox--active-menu":v.value,"v-combobox--chips":!!e.chips,"v-combobox--selection-slot":!!C.value,"v-combobox--selecting-index":g.value>-1,[`v-combobox--${e.multiple?"multiple":"single"}`]:!0},e.class],style:e.style,readonly:e.readonly,placeholder:de?void 0:e.placeholder,"onClick:clear":R,"onMousedown:control":q,onKeydown:oe}),{...a,default:()=>s(ae,null,[s(pn,F({ref:d,modelValue:v.value,"onUpdate:modelValue":te=>v.value=te,activator:"parent",contentClass:"v-combobox__content",disabled:Z.value,eager:e.eager,maxHeight:310,openOnClick:!1,closeOnContentClick:!1,transition:e.transition,onAfterEnter:W,onAfterLeave:se},e.menuProps),{default:()=>[G&&s(Ea,F({ref:$,selected:j.value,selectStrategy:e.multiple?"independent":"single-independent",onMousedown:te=>te.preventDefault(),onKeydown:le,onFocusin:De,onFocusout:xe,tabindex:"-1","aria-live":"polite",color:e.itemColor??e.color},E,e.listProps),{default:()=>[a["prepend-item"]?.(),!z.value.length&&!e.hideNoData&&(a["no-data"]?.()??s(At,{title:l(e.noDataText)},null)),s(Wa,{ref:c,renderless:!0,items:z.value},{default:te=>{let{item:fe,index:Ke,itemRef:kt}=te;const ht=F(fe.props,{ref:kt,key:Ke,active:ee.value&&Ke===0?!0:void 0,onClick:()=>ce(fe,null)});return a.item?.({item:fe,index:Ke,props:ht})??s(At,F(ht,{role:"option"}),{prepend:He=>{let{isSelected:ot}=He;return s(ae,null,[e.multiple&&!e.hideSelected?s(Lt,{key:fe.value,modelValue:ot,ripple:!1,tabindex:"-1"},null):void 0,fe.props.prependAvatar&&s(ft,{image:fe.props.prependAvatar},null),fe.props.prependIcon&&s(ye,{icon:fe.props.prependIcon},null)])},title:()=>r.value?fe.title:ch(fe.title,N(fe)?.title,P.value?.length??0)})}}),a["append-item"]?.()]})]}),x.value.map((te,fe)=>{function Ke(ot){ot.stopPropagation(),ot.preventDefault(),ce(te,!1)}const kt={"onClick:close":Ke,onKeydown(ot){ot.key!=="Enter"&&ot.key!==" "||(ot.preventDefault(),ot.stopPropagation(),Ke(ot))},onMousedown(ot){ot.preventDefault(),ot.stopPropagation()},modelValue:!0,"onUpdate:modelValue":void 0},ht=k.value?!!a.chip:!!a.selection,He=ht?Ia(k.value?a.chip({item:te,index:fe,props:kt}):a.selection({item:te,index:fe})):void 0;if(!(ht&&!He))return s("div",{key:te.value,class:["v-combobox__selection",fe===g.value&&["v-combobox__selection--selected",A.value]],style:fe===g.value?V.value:{}},[k.value?a.chip?s(ve,{key:"chip-defaults",defaults:{VChip:{closable:e.closableChips,size:"small",text:te.title}}},{default:()=>[He]}):s(Dn,F({key:"chip",closable:e.closableChips,size:"small",text:te.title,disabled:te.props.disabled},kt),null):He??s("span",{class:"v-combobox__selection-text"},[te.title,e.multiple&&fe<x.value.length-1&&s("span",{class:"v-combobox__selection-comma"},[Ft(",")])])])})]),"append-inner":function(){for(var te=arguments.length,fe=new Array(te),Ke=0;Ke<te;Ke++)fe[Ke]=arguments[Ke];return s(ae,null,[a["append-inner"]?.(...fe),(!e.hideNoData||e.items.length)&&e.menuIcon?s(ye,{class:"v-combobox__menu-icon",icon:e.menuIcon,onMousedown:ue,onClick:fs,"aria-label":l(y.value),title:l(y.value),tabindex:"-1"},null):void 0])}})}),gt({isFocused:i,isPristine:r,menu:v,search:P,selectionIndex:g,filteredItems:B,select:ce},o)}}),fh=L({modelValue:null,color:String,cancelText:{type:String,default:"$vuetify.confirmEdit.cancel"},okText:{type:String,default:"$vuetify.confirmEdit.ok"}},"VConfirmEdit"),mh=M()({name:"VConfirmEdit",props:fh(),emits:{cancel:()=>!0,save:e=>!0,"update:modelValue":e=>!0},setup(e,t){let{emit:n,slots:a}=t;const l=Q(e,"modelValue"),o=H();Te(()=>{o.value=structuredClone(Me(l.value))});const{t:i}=Ae(),r=m(()=>wt(l.value,o.value));function u(){l.value=o.value,n("save",o.value)}function d(){o.value=structuredClone(Me(l.value)),n("cancel")}let c=!1;return O(()=>{const f=s(ae,null,[s(he,{disabled:r.value,variant:"text",color:e.color,onClick:d,text:i(e.cancelText)},null),s(he,{disabled:r.value,variant:"text",color:e.color,onClick:u,text:i(e.okText)},null)]);return s(ae,null,[a.default?.({model:o,save:u,cancel:d,isPristine:r.value,get actions(){return c=!0,f}}),!c&&f])}),{save:u,cancel:d,isPristine:r}}}),vu=L({expandOnClick:Boolean,showExpand:Boolean,expanded:{type:Array,default:()=>[]}},"DataTable-expand"),fu=Symbol.for("vuetify:datatable:expanded");function Ka(e){const t=D(e,"expandOnClick"),n=Q(e,"expanded",e.expanded,r=>new Set(r),r=>[...r.values()]);function a(r,u){const d=new Set(n.value);u?d.add(r.value):d.delete(r.value),n.value=d}function l(r){return n.value.has(r.value)}function o(r){a(r,!l(r))}const i={expand:a,expanded:n,expandOnClick:t,isExpanded:l,toggleExpand:o};return we(fu,i),i}function mu(){const e=ge(fu);if(!e)throw new Error("foo");return e}const Mo=L({groupBy:{type:Array,default:()=>[]}},"DataTable-group"),gu=Symbol.for("vuetify:data-table-group");function Eo(e){return{groupBy:Q(e,"groupBy")}}function qa(e){const{disableSort:t,groupBy:n,sortBy:a}=e,l=H(new Set),o=m(()=>n.value.map(c=>({...c,order:c.order??!1})).concat(t?.value?[]:a.value));function i(c){return l.value.has(c.id)}function r(c){const f=new Set(l.value);i(c)?f.delete(c.id):f.add(c.id),l.value=f}function u(c){function f(v){const g=[];for(const h of v.items)"type"in h&&h.type==="group"?g.push(...f(h)):g.push(h);return g}return f({type:"group",items:c,id:"dummy",key:"dummy",value:"dummy",depth:0})}const d={sortByWithGroups:o,toggleGroup:r,opened:l,groupBy:n,extractRows:u,isGroupOpen:i};return we(gu,d),d}function hu(){const e=ge(gu);if(!e)throw new Error("Missing group!");return e}function gh(e,t){if(!e.length)return[];const n=new Map;for(const a of e){const l=Xt(a.raw,t);n.has(l)||n.set(l,[]),n.get(l).push(a)}return n}function yu(e,t){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0,a=arguments.length>3&&arguments[3]!==void 0?arguments[3]:"root";if(!t.length)return[];const l=gh(e,t[0]),o=[],i=t.slice(1);return l.forEach((r,u)=>{const d=t[0],c=`${a}_${d}_${u}`;o.push({depth:n,id:c,key:d,value:u,items:i.length?yu(r,i,n+1,c):r,type:"group"})}),o}function bu(e,t){const n=[];for(const a of e)"type"in a&&a.type==="group"?(a.value!=null&&n.push(a),(t.has(a.id)||a.value==null)&&n.push(...bu(a.items,t))):n.push(a);return n}function Xa(e,t,n){return{flatItems:m(()=>{if(!t.value.length)return e.value;const l=yu(e.value,t.value.map(o=>o.key));return bu(l,n.value)})}}function Za(e){let{page:t,itemsPerPage:n,sortBy:a,groupBy:l,search:o}=e;const i=_e("VDataTable"),r=m(()=>({page:t.value,itemsPerPage:n.value,sortBy:a.value,groupBy:l.value,search:o.value}));let u=null;U(r,()=>{wt(u,r.value)||(u&&u.search!==r.value.search&&(t.value=1),i.emit("update:options",r.value),u=r.value)},{deep:!0,immediate:!0})}const Fo=L({page:{type:[Number,String],default:1},itemsPerPage:{type:[Number,String],default:10}},"DataTable-paginate"),Su=Symbol.for("vuetify:data-table-pagination");function Oo(e){const t=Q(e,"page",void 0,a=>+(a??1)),n=Q(e,"itemsPerPage",void 0,a=>+(a??10));return{page:t,itemsPerPage:n}}function $o(e){const{page:t,itemsPerPage:n,itemsLength:a}=e,l=m(()=>n.value===-1?0:n.value*(t.value-1)),o=m(()=>n.value===-1?a.value:Math.min(a.value,l.value+n.value)),i=m(()=>n.value===-1||a.value===0?1:Math.ceil(a.value/n.value));Te(()=>{t.value>i.value&&(t.value=i.value)});function r(v){n.value=v,t.value=1}function u(){t.value=Be(t.value+1,1,i.value)}function d(){t.value=Be(t.value-1,1,i.value)}function c(v){t.value=Be(v,1,i.value)}const f={page:t,itemsPerPage:n,startIndex:l,stopIndex:o,pageCount:i,itemsLength:a,nextPage:u,prevPage:d,setPage:c,setItemsPerPage:r};return we(Su,f),f}function hh(){const e=ge(Su);if(!e)throw new Error("Missing pagination!");return e}function ku(e){const t=_e("usePaginatedItems"),{items:n,startIndex:a,stopIndex:l,itemsPerPage:o}=e,i=m(()=>o.value<=0?n.value:n.value.slice(a.value,l.value));return U(i,r=>{t.emit("update:currentItems",r)}),{paginatedItems:i}}const yh={showSelectAll:!1,allSelected:()=>[],select:e=>{let{items:t,value:n}=e;return new Set(n?[t[0]?.value]:[])},selectAll:e=>{let{selected:t}=e;return t}},Cu={showSelectAll:!0,allSelected:e=>{let{currentPage:t}=e;return t},select:e=>{let{items:t,value:n,selected:a}=e;for(const l of t)n?a.add(l.value):a.delete(l.value);return a},selectAll:e=>{let{value:t,currentPage:n,selected:a}=e;return Cu.select({items:n,value:t,selected:a})}},xu={showSelectAll:!0,allSelected:e=>{let{allItems:t}=e;return t},select:e=>{let{items:t,value:n,selected:a}=e;for(const l of t)n?a.add(l.value):a.delete(l.value);return a},selectAll:e=>{let{value:t,allItems:n,selected:a}=e;return xu.select({items:n,value:t,selected:a})}},wu=L({showSelect:Boolean,selectStrategy:{type:[String,Object],default:"page"},modelValue:{type:Array,default:()=>[]},valueComparator:{type:Function,default:wt}},"DataTable-select"),Vu=Symbol.for("vuetify:data-table-selection");function Ja(e,t){let{allItems:n,currentPage:a}=t;const l=Q(e,"modelValue",e.modelValue,S=>new Set(Pe(S).map(w=>n.value.find(p=>e.valueComparator(w,p.value))?.value??w)),S=>[...S.values()]),o=m(()=>n.value.filter(S=>S.selectable)),i=m(()=>a.value.filter(S=>S.selectable)),r=m(()=>{if(typeof e.selectStrategy=="object")return e.selectStrategy;switch(e.selectStrategy){case"single":return yh;case"all":return xu;case"page":default:return Cu}});function u(S){return Pe(S).every(w=>l.value.has(w.value))}function d(S){return Pe(S).some(w=>l.value.has(w.value))}function c(S,w){const p=r.value.select({items:S,value:w,selected:new Set(l.value)});l.value=p}function f(S){c([S],!u([S]))}function v(S){const w=r.value.selectAll({value:S,allItems:o.value,currentPage:i.value,selected:new Set(l.value)});l.value=w}const g=m(()=>l.value.size>0),h=m(()=>{const S=r.value.allSelected({allItems:o.value,currentPage:i.value});return!!S.length&&u(S)}),b=m(()=>r.value.showSelectAll),y={toggleSelect:f,select:c,selectAll:v,isSelected:u,isSomeSelected:d,someSelected:g,allSelected:h,showSelectAll:b};return we(Vu,y),y}function Qa(){const e=ge(Vu);if(!e)throw new Error("Missing selection!");return e}const Pu=L({sortBy:{type:Array,default:()=>[]},customKeySort:Object,multiSort:Boolean,mustSort:Boolean},"DataTable-sort"),Iu=Symbol.for("vuetify:data-table-sort");function el(e){const t=Q(e,"sortBy"),n=D(e,"mustSort"),a=D(e,"multiSort");return{sortBy:t,mustSort:n,multiSort:a}}function tl(e){const{sortBy:t,mustSort:n,multiSort:a,page:l}=e,o=u=>{if(u.key==null)return;let d=t.value.map(f=>({...f}))??[];const c=d.find(f=>f.key===u.key);c?c.order==="desc"?n.value?c.order="asc":d=d.filter(f=>f.key!==u.key):c.order="desc":a.value?d=[...d,{key:u.key,order:"asc"}]:d=[{key:u.key,order:"asc"}],t.value=d,l&&(l.value=1)};function i(u){return!!t.value.find(d=>d.key===u.key)}const r={sortBy:t,toggleSort:o,isSorted:i};return we(Iu,r),r}function pu(){const e=ge(Iu);if(!e)throw new Error("Missing sort!");return e}function Ro(e,t,n,a){const l=Ae();return{sortedItems:m(()=>n.value.length?bh(t.value,n.value,l.current.value,{transform:a?.transform,sortFunctions:{...e.customKeySort,...a?.sortFunctions?.value},sortRawFunctions:a?.sortRawFunctions?.value}):t.value)}}function bh(e,t,n,a){const l=new Intl.Collator(n,{sensitivity:"accent",usage:"sort"});return e.map(i=>[i,a?.transform?a.transform(i):i]).sort((i,r)=>{for(let u=0;u<t.length;u++){let d=!1;const c=t[u].key,f=t[u].order??"asc";if(f===!1)continue;let v=Xt(i[1],c),g=Xt(r[1],c),h=i[0].raw,b=r[0].raw;if(f==="desc"&&([v,g]=[g,v],[h,b]=[b,h]),a?.sortRawFunctions?.[c]){const y=a.sortRawFunctions[c](h,b);if(y==null)continue;if(d=!0,y)return y}if(a?.sortFunctions?.[c]){const y=a.sortFunctions[c](v,g);if(y==null)continue;if(d=!0,y)return y}if(!d){if(v instanceof Date&&g instanceof Date)return v.getTime()-g.getTime();if([v,g]=[v,g].map(y=>y!=null?y.toString().toLocaleLowerCase():y),v!==g)return ra(v)&&ra(g)?0:ra(v)?-1:ra(g)?1:!isNaN(v)&&!isNaN(g)?Number(v)-Number(g):l.compare(v,g)}}return 0}).map(i=>{let[r]=i;return r})}const Sh=L({items:{type:Array,default:()=>[]},itemValue:{type:[String,Array,Function],default:"id"},itemSelectable:{type:[String,Array,Function],default:null},returnObject:Boolean},"DataIterator-items");function kh(e,t){const n=e.returnObject?t:Re(t,e.itemValue),a=Re(t,e.itemSelectable,!0);return{type:"item",value:n,selectable:a,raw:t}}function Ch(e,t){const n=[];for(const a of t)n.push(kh(e,a));return n}function xh(e){return{items:m(()=>Ch(e,e.items))}}const wh=L({search:String,loading:Boolean,...X(),...Sh(),...wu(),...Pu(),...Fo({itemsPerPage:5}),...vu(),...Mo(),...oa(),...re(),...St({transition:{component:Wn,hideOnLeave:!0}})},"VDataIterator"),Vh=M()({name:"VDataIterator",props:wh(),emits:{"update:modelValue":e=>!0,"update:groupBy":e=>!0,"update:page":e=>!0,"update:itemsPerPage":e=>!0,"update:sortBy":e=>!0,"update:options":e=>!0,"update:expanded":e=>!0,"update:currentItems":e=>!0},setup(e,t){let{slots:n}=t;const a=Q(e,"groupBy"),l=D(e,"search"),{items:o}=xh(e),{filteredItems:i}=ia(e,o,l,{transform:R=>R.raw}),{sortBy:r,multiSort:u,mustSort:d}=el(e),{page:c,itemsPerPage:f}=Oo(e),{toggleSort:v}=tl({sortBy:r,multiSort:u,mustSort:d,page:c}),{sortByWithGroups:g,opened:h,extractRows:b,isGroupOpen:y,toggleGroup:S}=qa({groupBy:a,sortBy:r}),{sortedItems:w}=Ro(e,i,g,{transform:R=>R.raw}),{flatItems:p}=Xa(w,a,h),A=m(()=>p.value.length),{startIndex:V,stopIndex:x,pageCount:I,prevPage:k,nextPage:C,setItemsPerPage:T,setPage:P}=$o({page:c,itemsPerPage:f,itemsLength:A}),{paginatedItems:_}=ku({items:p,startIndex:V,stopIndex:x,itemsPerPage:f}),B=m(()=>b(_.value)),{isSelected:N,select:z,selectAll:j,toggleSelect:ee}=Ja(e,{allItems:o,currentPage:B}),{isExpanded:Z,toggleExpand:$}=Ka(e);Za({page:c,itemsPerPage:f,sortBy:r,groupBy:a,search:l});const E=m(()=>({page:c.value,itemsPerPage:f.value,sortBy:r.value,pageCount:I.value,toggleSort:v,prevPage:k,nextPage:C,setPage:P,setItemsPerPage:T,isSelected:N,select:z,selectAll:j,toggleSelect:ee,isExpanded:Z,toggleExpand:$,isGroupOpen:y,toggleGroup:S,items:B.value,groupedItems:_.value}));return O(()=>s(e.tag,{class:["v-data-iterator",{"v-data-iterator--loading":e.loading},e.class],style:e.style},{default:()=>[n.header?.(E.value),s(Xe,{transition:e.transition},{default:()=>[e.loading?s(aa,{key:"loader",name:"v-data-iterator",active:!0},{default:R=>n.loader?.(R)}):s("div",{key:"items"},[_.value.length?n.default?.(E.value):n["no-data"]?.()])]}),n.footer?.(E.value)]})),{}}});function Ph(){const e=H([]);Ic(()=>e.value=[]);function t(n,a){e.value[a]=n}return{refs:e,updateRef:t}}const Ih=L({activeColor:String,start:{type:[Number,String],default:1},modelValue:{type:Number,default:e=>e.start},disabled:Boolean,length:{type:[Number,String],default:1,validator:e=>e%1===0},totalVisible:[Number,String],firstIcon:{type:ie,default:"$first"},prevIcon:{type:ie,default:"$prev"},nextIcon:{type:ie,default:"$next"},lastIcon:{type:ie,default:"$last"},ariaLabel:{type:String,default:"$vuetify.pagination.ariaLabel.root"},pageAriaLabel:{type:String,default:"$vuetify.pagination.ariaLabel.page"},currentPageAriaLabel:{type:String,default:"$vuetify.pagination.ariaLabel.currentPage"},firstAriaLabel:{type:String,default:"$vuetify.pagination.ariaLabel.first"},previousAriaLabel:{type:String,default:"$vuetify.pagination.ariaLabel.previous"},nextAriaLabel:{type:String,default:"$vuetify.pagination.ariaLabel.next"},lastAriaLabel:{type:String,default:"$vuetify.pagination.ariaLabel.last"},ellipsis:{type:String,default:"..."},showFirstLastPage:Boolean,...lt(),...X(),...ze(),...Ne(),...Ve(),...It(),...re({tag:"nav"}),...me(),...mt({variant:"text"})},"VPagination"),Nl=M()({name:"VPagination",props:Ih(),emits:{"update:modelValue":e=>!0,first:e=>!0,prev:e=>!0,next:e=>!0,last:e=>!0},setup(e,t){let{slots:n,emit:a}=t;const l=Q(e,"modelValue"),{t:o,n:i}=Ae(),{isRtl:r}=Fe(),{themeClasses:u}=be(e),{width:d}=ut(),c=Y(-1);pe(void 0,{scoped:!0});const{resizeRef:f}=yt(k=>{if(!k.length)return;const{target:C,contentRect:T}=k[0],P=C.querySelector(".v-pagination__list > *");if(!P)return;const _=T.width,B=P.offsetWidth+parseFloat(getComputedStyle(P).marginRight)*2;c.value=b(_,B)}),v=m(()=>parseInt(e.length,10)),g=m(()=>parseInt(e.start,10)),h=m(()=>e.totalVisible!=null?parseInt(e.totalVisible,10):c.value>=0?c.value:b(d.value,58));function b(k,C){const T=e.showFirstLastPage?5:3;return Math.max(0,Math.floor(+((k-C*T)/C).toFixed(2)))}const y=m(()=>{if(v.value<=0||isNaN(v.value)||v.value>Number.MAX_SAFE_INTEGER)return[];if(h.value<=0)return[];if(h.value===1)return[l.value];if(v.value<=h.value)return Ct(v.value,g.value);const k=h.value%2===0,C=k?h.value/2:Math.floor(h.value/2),T=k?C:C+1,P=v.value-C;if(T-l.value>=0)return[...Ct(Math.max(1,h.value-1),g.value),e.ellipsis,v.value];if(l.value-P>=(k?1:0)){const _=h.value-1,B=v.value-_+g.value;return[g.value,e.ellipsis,...Ct(_,B)]}else{const _=Math.max(1,h.value-3),B=_===1?l.value:l.value-Math.ceil(_/2)+g.value;return[g.value,e.ellipsis,...Ct(_,B),e.ellipsis,v.value]}});function S(k,C,T){k.preventDefault(),l.value=C,T&&a(T,C)}const{refs:w,updateRef:p}=Ph();pe({VPaginationBtn:{color:D(e,"color"),border:D(e,"border"),density:D(e,"density"),size:D(e,"size"),variant:D(e,"variant"),rounded:D(e,"rounded"),elevation:D(e,"elevation")}});const A=m(()=>y.value.map((k,C)=>{const T=P=>p(P,C);if(typeof k=="string")return{isActive:!1,key:`ellipsis-${C}`,page:k,props:{ref:T,ellipsis:!0,icon:!0,disabled:!0}};{const P=k===l.value;return{isActive:P,key:k,page:i(k),props:{ref:T,ellipsis:!1,icon:!0,disabled:!!e.disabled||+e.length<2,color:P?e.activeColor:e.color,"aria-current":P,"aria-label":o(P?e.currentPageAriaLabel:e.pageAriaLabel,k),onClick:_=>S(_,k)}}}})),V=m(()=>{const k=!!e.disabled||l.value<=g.value,C=!!e.disabled||l.value>=g.value+v.value-1;return{first:e.showFirstLastPage?{icon:r.value?e.lastIcon:e.firstIcon,onClick:T=>S(T,g.value,"first"),disabled:k,"aria-label":o(e.firstAriaLabel),"aria-disabled":k}:void 0,prev:{icon:r.value?e.nextIcon:e.prevIcon,onClick:T=>S(T,l.value-1,"prev"),disabled:k,"aria-label":o(e.previousAriaLabel),"aria-disabled":k},next:{icon:r.value?e.prevIcon:e.nextIcon,onClick:T=>S(T,l.value+1,"next"),disabled:C,"aria-label":o(e.nextAriaLabel),"aria-disabled":C},last:e.showFirstLastPage?{icon:r.value?e.firstIcon:e.lastIcon,onClick:T=>S(T,g.value+v.value-1,"last"),disabled:C,"aria-label":o(e.lastAriaLabel),"aria-disabled":C}:void 0}});function x(){const k=l.value-g.value;w.value[k]?.$el.focus()}function I(k){k.key===ml.left&&!e.disabled&&l.value>+e.start?(l.value=l.value-1,ke(x)):k.key===ml.right&&!e.disabled&&l.value<g.value+v.value-1&&(l.value=l.value+1,ke(x))}return O(()=>s(e.tag,{ref:f,class:["v-pagination",u.value,e.class],style:e.style,role:"navigation","aria-label":o(e.ariaLabel),onKeydown:I,"data-test":"v-pagination-root"},{default:()=>[s("ul",{class:"v-pagination__list"},[e.showFirstLastPage&&s("li",{key:"first",class:"v-pagination__first","data-test":"v-pagination-first"},[n.first?n.first(V.value.first):s(he,F({_as:"VPaginationBtn"},V.value.first),null)]),s("li",{key:"prev",class:"v-pagination__prev","data-test":"v-pagination-prev"},[n.prev?n.prev(V.value.prev):s(he,F({_as:"VPaginationBtn"},V.value.prev),null)]),A.value.map((k,C)=>s("li",{key:k.key,class:["v-pagination__item",{"v-pagination__item--is-active":k.isActive}],"data-test":"v-pagination-item"},[n.item?n.item(k):s(he,F({_as:"VPaginationBtn"},k.props),{default:()=>[k.page]})])),s("li",{key:"next",class:"v-pagination__next","data-test":"v-pagination-next"},[n.next?n.next(V.value.next):s(he,F({_as:"VPaginationBtn"},V.value.next),null)]),e.showFirstLastPage&&s("li",{key:"last",class:"v-pagination__last","data-test":"v-pagination-last"},[n.last?n.last(V.value.last):s(he,F({_as:"VPaginationBtn"},V.value.last),null)])])]})),{}}}),No=L({prevIcon:{type:ie,default:"$prev"},nextIcon:{type:ie,default:"$next"},firstIcon:{type:ie,default:"$first"},lastIcon:{type:ie,default:"$last"},itemsPerPageText:{type:String,default:"$vuetify.dataFooter.itemsPerPageText"},pageText:{type:String,default:"$vuetify.dataFooter.pageText"},firstPageLabel:{type:String,default:"$vuetify.dataFooter.firstPage"},prevPageLabel:{type:String,default:"$vuetify.dataFooter.prevPage"},nextPageLabel:{type:String,default:"$vuetify.dataFooter.nextPage"},lastPageLabel:{type:String,default:"$vuetify.dataFooter.lastPage"},itemsPerPageOptions:{type:Array,default:()=>[{value:10,title:"10"},{value:25,title:"25"},{value:50,title:"50"},{value:100,title:"100"},{value:-1,title:"$vuetify.dataFooter.itemsPerPageAll"}]},showCurrentPage:Boolean},"VDataTableFooter"),qn=M()({name:"VDataTableFooter",props:No(),setup(e,t){let{slots:n}=t;const{t:a}=Ae(),{page:l,pageCount:o,startIndex:i,stopIndex:r,itemsLength:u,itemsPerPage:d,setItemsPerPage:c}=hh(),f=m(()=>e.itemsPerPageOptions.map(v=>typeof v=="number"?{value:v,title:v===-1?a("$vuetify.dataFooter.itemsPerPageAll"):String(v)}:{...v,title:isNaN(Number(v.title))?a(v.title):v.title}));return O(()=>{const v=Nl.filterProps(e);return s("div",{class:"v-data-table-footer"},[n.prepend?.(),s("div",{class:"v-data-table-footer__items-per-page"},[s("span",null,[a(e.itemsPerPageText)]),s(Lo,{items:f.value,modelValue:d.value,"onUpdate:modelValue":g=>c(Number(g)),density:"compact",variant:"outlined","hide-details":!0},null)]),s("div",{class:"v-data-table-footer__info"},[s("div",null,[a(e.pageText,u.value?i.value+1:0,r.value,u.value)])]),s("div",{class:"v-data-table-footer__pagination"},[s(Nl,F({modelValue:l.value,"onUpdate:modelValue":g=>l.value=g,density:"comfortable","first-aria-label":e.firstPageLabel,"last-aria-label":e.lastPageLabel,length:o.value,"next-aria-label":e.nextPageLabel,"previous-aria-label":e.prevPageLabel,rounded:!0,"show-first-last-page":!0,"total-visible":e.showCurrentPage?1:0,variant:"plain"},v),null)])])}),{}}}),wa=md({align:{type:String,default:"start"},fixed:Boolean,fixedOffset:[Number,String],height:[Number,String],lastFixed:Boolean,noPadding:Boolean,tag:String,width:[Number,String],maxWidth:[Number,String],nowrap:Boolean},(e,t)=>{let{slots:n}=t;const a=e.tag??"td";return s(a,{class:["v-data-table__td",{"v-data-table-column--fixed":e.fixed,"v-data-table-column--last-fixed":e.lastFixed,"v-data-table-column--no-padding":e.noPadding,"v-data-table-column--nowrap":e.nowrap},`v-data-table-column--align-${e.align}`],style:{height:K(e.height),width:K(e.width),maxWidth:K(e.maxWidth),left:K(e.fixedOffset||null)}},{default:()=>[n.default?.()]})}),ph=L({headers:Array},"DataTable-header"),_u=Symbol.for("vuetify:data-table-headers"),Au={title:"",sortable:!1},_h={...Au,width:48};function Ah(){const t=(arguments.length>0&&arguments[0]!==void 0?arguments[0]:[]).map(n=>({element:n,priority:0}));return{enqueue:(n,a)=>{let l=!1;for(let o=0;o<t.length;o++)if(t[o].priority>a){t.splice(o,0,{element:n,priority:a}),l=!0;break}l||t.push({element:n,priority:a})},size:()=>t.length,count:()=>{let n=0;if(!t.length)return 0;const a=Math.floor(t[0].priority);for(let l=0;l<t.length;l++)Math.floor(t[l].priority)===a&&(n+=1);return n},dequeue:()=>t.shift()}}function Hl(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[];if(!e.children)t.push(e);else for(const n of e.children)Hl(n,t);return t}function Lu(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:new Set;for(const n of e)n.key&&t.add(n.key),n.children&&Lu(n.children,t);return t}function Lh(e){if(e.key){if(e.key==="data-table-group")return Au;if(["data-table-expand","data-table-select"].includes(e.key))return _h}}function Ho(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return e.children?Math.max(t,...e.children.map(n=>Ho(n,t+1))):t}function Th(e){let t=!1;function n(o){let i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(o)if(i&&(o.fixed=!0),o.fixed)if(o.children)for(let r=o.children.length-1;r>=0;r--)n(o.children[r],!0);else t?isNaN(+o.width)&&(`${o.key}`,void 0):o.lastFixed=!0,t=!0;else if(o.children)for(let r=o.children.length-1;r>=0;r--)n(o.children[r]);else t=!1}for(let o=e.length-1;o>=0;o--)n(e[o]);function a(o){let i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;if(!o)return i;if(o.children){o.fixedOffset=i;for(const r of o.children)i=a(r,i)}else o.fixed&&(o.fixedOffset=i,i+=parseFloat(o.width||"0")||0);return i}let l=0;for(const o of e)l=a(o,l)}function Bh(e,t){const n=[];let a=0;const l=Ah(e);for(;l.size()>0;){let i=l.count();const r=[];let u=1;for(;i>0;){const{element:d,priority:c}=l.dequeue(),f=t-a-Ho(d);if(r.push({...d,rowspan:f??1,colspan:d.children?Hl(d).length:1}),d.children)for(const v of d.children){const g=c%1+u/Math.pow(10,a+2);l.enqueue(v,a+f+g)}u+=1,i-=1}a+=1,n.push(r)}return{columns:e.map(i=>Hl(i)).flat(),headers:n}}function Tu(e){const t=[];for(const n of e){const a={...Lh(n),...n},l=a.key??(typeof a.value=="string"?a.value:null),o=a.value??l??null,i={...a,key:l,value:o,sortable:a.sortable??(a.key!=null||!!a.sort),children:a.children?Tu(a.children):void 0};t.push(i)}return t}function zo(e,t){const n=H([]),a=H([]),l=H({}),o=H({}),i=H({});Te(()=>{const d=(e.headers||Object.keys(e.items[0]??{}).map(b=>({key:b,title:An(b)}))).slice(),c=Lu(d);t?.groupBy?.value.length&&!c.has("data-table-group")&&d.unshift({key:"data-table-group",title:"Group"}),t?.showSelect?.value&&!c.has("data-table-select")&&d.unshift({key:"data-table-select"}),t?.showExpand?.value&&!c.has("data-table-expand")&&d.push({key:"data-table-expand"});const f=Tu(d);Th(f);const v=Math.max(...f.map(b=>Ho(b)))+1,g=Bh(f,v);n.value=g.headers,a.value=g.columns;const h=g.headers.flat(1);for(const b of h)b.key&&(b.sortable&&(b.sort&&(l.value[b.key]=b.sort),b.sortRaw&&(o.value[b.key]=b.sortRaw)),b.filter&&(i.value[b.key]=b.filter))});const r={headers:n,columns:a,sortFunctions:l,sortRawFunctions:o,filterFunctions:i};return we(_u,r),r}function nl(){const e=ge(_u);if(!e)throw new Error("Missing headers!");return e}const Bu=L({color:String,sticky:Boolean,disableSort:Boolean,multiSort:Boolean,sortAscIcon:{type:ie,default:"$sortAsc"},sortDescIcon:{type:ie,default:"$sortDesc"},headerProps:{type:Object},...sn(),...Ha()},"VDataTableHeaders"),tn=M()({name:"VDataTableHeaders",props:Bu(),setup(e,t){let{slots:n}=t;const{t:a}=Ae(),{toggleSort:l,sortBy:o,isSorted:i}=pu(),{someSelected:r,allSelected:u,selectAll:d,showSelectAll:c}=Qa(),{columns:f,headers:v}=nl(),{loaderClasses:g}=na(e);function h(k,C){if(!(!e.sticky&&!k.fixed))return{position:"sticky",left:k.fixed?K(k.fixedOffset):void 0,top:e.sticky?`calc(var(--v-table-header-height) * ${C})`:void 0}}function b(k){const C=o.value.find(T=>T.key===k.key);return C?C.order==="asc"?e.sortAscIcon:e.sortDescIcon:e.sortAscIcon}const{backgroundColorClasses:y,backgroundColorStyles:S}=Ce(e,"color"),{displayClasses:w,mobile:p}=ut(e),A=m(()=>({headers:v.value,columns:f.value,toggleSort:l,isSorted:i,sortBy:o.value,someSelected:r.value,allSelected:u.value,selectAll:d,getSortIcon:b})),V=m(()=>["v-data-table__th",{"v-data-table__th--sticky":e.sticky},w.value,g.value]),x=k=>{let{column:C,x:T,y:P}=k;const _=C.key==="data-table-select"||C.key==="data-table-expand",B=F(e.headerProps??{},C.headerProps??{});return s(wa,F({tag:"th",align:C.align,class:[{"v-data-table__th--sortable":C.sortable&&!e.disableSort,"v-data-table__th--sorted":i(C),"v-data-table__th--fixed":C.fixed},...V.value],style:{width:K(C.width),minWidth:K(C.minWidth),maxWidth:K(C.maxWidth),...h(C,P)},colspan:C.colspan,rowspan:C.rowspan,onClick:C.sortable?()=>l(C):void 0,fixed:C.fixed,nowrap:C.nowrap,lastFixed:C.lastFixed,noPadding:_},B),{default:()=>{const N=`header.${C.key}`,z={column:C,selectAll:d,isSorted:i,toggleSort:l,sortBy:o.value,someSelected:r.value,allSelected:u.value,getSortIcon:b};return n[N]?n[N](z):C.key==="data-table-select"?n["header.data-table-select"]?.(z)??(c.value&&s(Lt,{modelValue:u.value,indeterminate:r.value&&!u.value,"onUpdate:modelValue":d},null)):s("div",{class:"v-data-table-header__content"},[s("span",null,[C.title]),C.sortable&&!e.disableSort&&s(ye,{key:"icon",class:"v-data-table-header__sort-icon",icon:b(C)},null),e.multiSort&&i(C)&&s("div",{key:"badge",class:["v-data-table-header__sort-badge",...y.value],style:S.value},[o.value.findIndex(j=>j.key===C.key)+1])])}})},I=()=>{const k=F(e.headerProps??{}??{}),C=m(()=>f.value.filter(P=>P?.sortable&&!e.disableSort)),T=m(()=>{if(f.value.find(_=>_.key==="data-table-select")!=null)return u.value?"$checkboxOn":r.value?"$checkboxIndeterminate":"$checkboxOff"});return s(wa,F({tag:"th",class:[...V.value],colspan:v.value.length+1},k),{default:()=>[s("div",{class:"v-data-table-header__content"},[s(Lo,{chips:!0,class:"v-data-table__td-sort-select",clearable:!0,density:"default",items:C.value,label:a("$vuetify.dataTable.sortBy"),multiple:e.multiSort,variant:"underlined","onClick:clear":()=>o.value=[],appendIcon:T.value,"onClick:append":()=>d(!u.value)},{...n,chip:P=>s(Dn,{onClick:P.item.raw?.sortable?()=>l(P.item.raw):void 0,onMousedown:_=>{_.preventDefault(),_.stopPropagation()}},{default:()=>[P.item.title,s(ye,{class:["v-data-table__td-sort-icon",i(P.item.raw)&&"v-data-table__td-sort-icon-active"],icon:b(P.item.raw),size:"small"},null)]})})])]})};O(()=>p.value?s("tr",null,[s(I,null,null)]):s(ae,null,[n.headers?n.headers(A.value):v.value.map((k,C)=>s("tr",null,[k.map((T,P)=>s(x,{column:T,x:P,y:C},null))])),e.loading&&s("tr",{class:"v-data-table-progress"},[s("th",{colspan:f.value.length},[s(aa,{name:"v-data-table-progress",absolute:!0,active:!0,color:typeof e.loading=="boolean"?void 0:e.loading,indeterminate:!0},{default:n.loader})])])]))}}),Dh=L({item:{type:Object,required:!0}},"VDataTableGroupHeaderRow"),Mh=M()({name:"VDataTableGroupHeaderRow",props:Dh(),setup(e,t){let{slots:n}=t;const{isGroupOpen:a,toggleGroup:l,extractRows:o}=hu(),{isSelected:i,isSomeSelected:r,select:u}=Qa(),{columns:d}=nl(),c=m(()=>o([e.item]));return()=>s("tr",{class:"v-data-table-group-header-row",style:{"--v-data-table-group-header-row-depth":e.item.depth}},[d.value.map(f=>{if(f.key==="data-table-group"){const v=a(e.item)?"$expand":"$next",g=()=>l(e.item);return n["data-table-group"]?.({item:e.item,count:c.value.length,props:{icon:v,onClick:g}})??s(wa,{class:"v-data-table-group-header-row__column"},{default:()=>[s(he,{size:"small",variant:"text",icon:v,onClick:g},null),s("span",null,[e.item.value]),s("span",null,[Ft("("),c.value.length,Ft(")")])]})}if(f.key==="data-table-select"){const v=i(c.value),g=r(c.value)&&!v,h=b=>u(c.value,b);return n["data-table-select"]?.({props:{modelValue:v,indeterminate:g,"onUpdate:modelValue":h}})??s("td",null,[s(Lt,{modelValue:v,indeterminate:g,"onUpdate:modelValue":h},null)])}return s("td",null,null)})])}}),Eh=L({index:Number,item:Object,cellProps:[Object,Function],onClick:We(),onContextmenu:We(),onDblclick:We(),...sn()},"VDataTableRow"),Wo=M()({name:"VDataTableRow",props:Eh(),setup(e,t){let{slots:n}=t;const{displayClasses:a,mobile:l}=ut(e,"v-data-table__tr"),{isSelected:o,toggleSelect:i,someSelected:r,allSelected:u,selectAll:d}=Qa(),{isExpanded:c,toggleExpand:f}=mu(),{toggleSort:v,sortBy:g,isSorted:h}=pu(),{columns:b}=nl();O(()=>s("tr",{class:["v-data-table__tr",{"v-data-table__tr--clickable":!!(e.onClick||e.onContextmenu||e.onDblclick)},a.value],onClick:e.onClick,onContextmenu:e.onContextmenu,onDblclick:e.onDblclick},[e.item&&b.value.map((y,S)=>{const w=e.item,p=`item.${y.key}`,A=`header.${y.key}`,V={index:e.index,item:w.raw,internalItem:w,value:Xt(w.columns,y.key),column:y,isSelected:o,toggleSelect:i,isExpanded:c,toggleExpand:f},x={column:y,selectAll:d,isSorted:h,toggleSort:v,sortBy:g.value,someSelected:r.value,allSelected:u.value,getSortIcon:()=>""},I=typeof e.cellProps=="function"?e.cellProps({index:V.index,item:V.item,internalItem:V.internalItem,value:V.value,column:y}):e.cellProps,k=typeof y.cellProps=="function"?y.cellProps({index:V.index,item:V.item,internalItem:V.internalItem,value:V.value}):y.cellProps;return s(wa,F({align:y.align,class:{"v-data-table__td--expanded-row":y.key==="data-table-expand","v-data-table__td--select-row":y.key==="data-table-select"},fixed:y.fixed,fixedOffset:y.fixedOffset,lastFixed:y.lastFixed,maxWidth:l.value?void 0:y.maxWidth,noPadding:y.key==="data-table-select"||y.key==="data-table-expand",nowrap:y.nowrap,width:l.value?void 0:y.width},I,k),{default:()=>{if(n[p]&&!l.value)return n[p]?.(V);if(y.key==="data-table-select")return n["item.data-table-select"]?.(V)??s(Lt,{disabled:!w.selectable,modelValue:o([w]),onClick:Xo(()=>i(w),["stop"])},null);if(y.key==="data-table-expand")return n["item.data-table-expand"]?.(V)??s(he,{icon:c(w)?"$collapse":"$expand",size:"small",variant:"text",onClick:Xo(()=>f(w),["stop"])},null);const C=pc(V.value);return l.value?s(ae,null,[s("div",{class:"v-data-table__td-title"},[n[A]?.(x)??y.title]),s("div",{class:"v-data-table__td-value"},[n[p]?.(V)??C])]):C}})})]))}}),Du=L({loading:[Boolean,String],loadingText:{type:String,default:"$vuetify.dataIterator.loadingText"},hideNoData:Boolean,items:{type:Array,default:()=>[]},noDataText:{type:String,default:"$vuetify.noDataText"},rowProps:[Object,Function],cellProps:[Object,Function],...sn()},"VDataTableRows"),nn=M()({name:"VDataTableRows",inheritAttrs:!1,props:Du(),setup(e,t){let{attrs:n,slots:a}=t;const{columns:l}=nl(),{expandOnClick:o,toggleExpand:i,isExpanded:r}=mu(),{isSelected:u,toggleSelect:d}=Qa(),{toggleGroup:c,isGroupOpen:f}=hu(),{t:v}=Ae(),{mobile:g}=ut(e);return O(()=>e.loading&&(!e.items.length||a.loading)?s("tr",{class:"v-data-table-rows-loading",key:"loading"},[s("td",{colspan:l.value.length},[a.loading?.()??v(e.loadingText)])]):!e.loading&&!e.items.length&&!e.hideNoData?s("tr",{class:"v-data-table-rows-no-data",key:"no-data"},[s("td",{colspan:l.value.length},[a["no-data"]?.()??v(e.noDataText)])]):s(ae,null,[e.items.map((h,b)=>{if(h.type==="group"){const w={index:b,item:h,columns:l.value,isExpanded:r,toggleExpand:i,isSelected:u,toggleSelect:d,toggleGroup:c,isGroupOpen:f};return a["group-header"]?a["group-header"](w):s(Mh,F({key:`group-header_${h.id}`,item:h},Si(n,":group-header",()=>w)),a)}const y={index:b,item:h.raw,internalItem:h,columns:l.value,isExpanded:r,toggleExpand:i,isSelected:u,toggleSelect:d},S={...y,props:F({key:`item_${h.key??h.index}`,onClick:o.value?()=>{i(h)}:void 0,index:b,item:h,cellProps:e.cellProps,mobile:g.value},Si(n,":row",()=>y),typeof e.rowProps=="function"?e.rowProps({item:y.item,index:y.index,internalItem:y.internalItem}):e.rowProps)};return s(ae,{key:S.props.key},[a.item?a.item(S):s(Wo,S.props,a),r(h)&&a["expanded-row"]?.(y)])})])),{}}}),Mu=L({fixedHeader:Boolean,fixedFooter:Boolean,height:[Number,String],hover:Boolean,...X(),...ze(),...re(),...me()},"VTable"),an=M()({name:"VTable",props:Mu(),setup(e,t){let{slots:n,emit:a}=t;const{themeClasses:l}=be(e),{densityClasses:o}=et(e);return O(()=>s(e.tag,{class:["v-table",{"v-table--fixed-height":!!e.height,"v-table--fixed-header":e.fixedHeader,"v-table--fixed-footer":e.fixedFooter,"v-table--has-top":!!n.top,"v-table--has-bottom":!!n.bottom,"v-table--hover":e.hover},l.value,o.value,e.class],style:e.style},{default:()=>[n.top?.(),n.default?s("div",{class:"v-table__wrapper",style:{height:K(e.height)}},[s("table",null,[n.default()])]):n.wrapper?.(),n.bottom?.()]})),{}}}),Fh=L({items:{type:Array,default:()=>[]},itemValue:{type:[String,Array,Function],default:"id"},itemSelectable:{type:[String,Array,Function],default:null},rowProps:[Object,Function],cellProps:[Object,Function],returnObject:Boolean},"DataTable-items");function Oh(e,t,n,a){const l=e.returnObject?t:Re(t,e.itemValue),o=Re(t,e.itemSelectable,!0),i=a.reduce((r,u)=>(u.key!=null&&(r[u.key]=Re(t,u.value)),r),{});return{type:"item",key:e.returnObject?Re(t,e.itemValue):l,index:n,value:l,selectable:o,columns:i,raw:t}}function $h(e,t,n){return t.map((a,l)=>Oh(e,a,l,n))}function jo(e,t){return{items:m(()=>$h(e,e.items,t.value))}}const Yo=L({...Du(),hideDefaultBody:Boolean,hideDefaultFooter:Boolean,hideDefaultHeader:Boolean,width:[String,Number],search:String,...vu(),...Mo(),...ph(),...Fh(),...wu(),...Pu(),...Bu(),...Mu()},"DataTable"),Rh=L({...Fo(),...Yo(),...oa(),...No()},"VDataTable"),Nh=M()({name:"VDataTable",props:Rh(),emits:{"update:modelValue":e=>!0,"update:page":e=>!0,"update:itemsPerPage":e=>!0,"update:sortBy":e=>!0,"update:options":e=>!0,"update:groupBy":e=>!0,"update:expanded":e=>!0,"update:currentItems":e=>!0},setup(e,t){let{attrs:n,slots:a}=t;const{groupBy:l}=Eo(e),{sortBy:o,multiSort:i,mustSort:r}=el(e),{page:u,itemsPerPage:d}=Oo(e),{disableSort:c}=ln(e),{columns:f,headers:v,sortFunctions:g,sortRawFunctions:h,filterFunctions:b}=zo(e,{groupBy:l,showSelect:D(e,"showSelect"),showExpand:D(e,"showExpand")}),{items:y}=jo(e,f),S=D(e,"search"),{filteredItems:w}=ia(e,y,S,{transform:se=>se.columns,customKeyFilter:b}),{toggleSort:p}=tl({sortBy:o,multiSort:i,mustSort:r,page:u}),{sortByWithGroups:A,opened:V,extractRows:x,isGroupOpen:I,toggleGroup:k}=qa({groupBy:l,sortBy:o,disableSort:c}),{sortedItems:C}=Ro(e,w,A,{transform:se=>({...se.raw,...se.columns}),sortFunctions:g,sortRawFunctions:h}),{flatItems:T}=Xa(C,l,V),P=m(()=>T.value.length),{startIndex:_,stopIndex:B,pageCount:N,setItemsPerPage:z}=$o({page:u,itemsPerPage:d,itemsLength:P}),{paginatedItems:j}=ku({items:T,startIndex:_,stopIndex:B,itemsPerPage:d}),ee=m(()=>x(j.value)),{isSelected:Z,select:$,selectAll:E,toggleSelect:R,someSelected:q,allSelected:ue}=Ja(e,{allItems:y,currentPage:ee}),{isExpanded:le,toggleExpand:oe}=Ka(e);Za({page:u,itemsPerPage:d,sortBy:o,groupBy:l,search:S}),pe({VDataTableRows:{hideNoData:D(e,"hideNoData"),noDataText:D(e,"noDataText"),loading:D(e,"loading"),loadingText:D(e,"loadingText")}});const W=m(()=>({page:u.value,itemsPerPage:d.value,sortBy:o.value,pageCount:N.value,toggleSort:p,setItemsPerPage:z,someSelected:q.value,allSelected:ue.value,isSelected:Z,select:$,selectAll:E,toggleSelect:R,isExpanded:le,toggleExpand:oe,isGroupOpen:I,toggleGroup:k,items:ee.value.map(se=>se.raw),internalItems:ee.value,groupedItems:j.value,columns:f.value,headers:v.value}));return O(()=>{const se=qn.filterProps(e),ce=tn.filterProps(e),De=nn.filterProps(e),xe=an.filterProps(e);return s(an,F({class:["v-data-table",{"v-data-table--show-select":e.showSelect,"v-data-table--loading":e.loading},e.class],style:e.style},xe),{top:()=>a.top?.(W.value),default:()=>a.default?a.default(W.value):s(ae,null,[a.colgroup?.(W.value),!e.hideDefaultHeader&&s("thead",{key:"thead"},[s(tn,ce,a)]),a.thead?.(W.value),!e.hideDefaultBody&&s("tbody",null,[a["body.prepend"]?.(W.value),a.body?a.body(W.value):s(nn,F(n,De,{items:j.value}),a),a["body.append"]?.(W.value)]),a.tbody?.(W.value),a.tfoot?.(W.value)]),bottom:()=>a.bottom?a.bottom(W.value):!e.hideDefaultFooter&&s(ae,null,[s(Qn,null,null),s(qn,se,{prepend:a["footer.prepend"]})])})}),{}}}),Hh=L({...Yo(),...Mo(),...zr(),...oa()},"VDataTableVirtual"),zh=M()({name:"VDataTableVirtual",props:Hh(),emits:{"update:modelValue":e=>!0,"update:sortBy":e=>!0,"update:options":e=>!0,"update:groupBy":e=>!0,"update:expanded":e=>!0},setup(e,t){let{attrs:n,slots:a}=t;const{groupBy:l}=Eo(e),{sortBy:o,multiSort:i,mustSort:r}=el(e),{disableSort:u}=ln(e),{columns:d,headers:c,filterFunctions:f,sortFunctions:v,sortRawFunctions:g}=zo(e,{groupBy:l,showSelect:D(e,"showSelect"),showExpand:D(e,"showExpand")}),{items:h}=jo(e,d),b=D(e,"search"),{filteredItems:y}=ia(e,h,b,{transform:ce=>ce.columns,customKeyFilter:f}),{toggleSort:S}=tl({sortBy:o,multiSort:i,mustSort:r}),{sortByWithGroups:w,opened:p,extractRows:A,isGroupOpen:V,toggleGroup:x}=qa({groupBy:l,sortBy:o,disableSort:u}),{sortedItems:I}=Ro(e,y,w,{transform:ce=>({...ce.raw,...ce.columns}),sortFunctions:v,sortRawFunctions:g}),{flatItems:k}=Xa(I,l,p),C=m(()=>A(k.value)),{isSelected:T,select:P,selectAll:_,toggleSelect:B,someSelected:N,allSelected:z}=Ja(e,{allItems:C,currentPage:C}),{isExpanded:j,toggleExpand:ee}=Ka(e),{containerRef:Z,markerRef:$,paddingTop:E,paddingBottom:R,computedItems:q,handleItemResize:ue,handleScroll:le,handleScrollend:oe}=Wr(e,k),W=m(()=>q.value.map(ce=>ce.raw));Za({sortBy:o,page:Y(1),itemsPerPage:Y(-1),groupBy:l,search:b}),pe({VDataTableRows:{hideNoData:D(e,"hideNoData"),noDataText:D(e,"noDataText"),loading:D(e,"loading"),loadingText:D(e,"loadingText")}});const se=m(()=>({sortBy:o.value,toggleSort:S,someSelected:N.value,allSelected:z.value,isSelected:T,select:P,selectAll:_,toggleSelect:B,isExpanded:j,toggleExpand:ee,isGroupOpen:V,toggleGroup:x,items:C.value.map(ce=>ce.raw),internalItems:C.value,groupedItems:k.value,columns:d.value,headers:c.value}));O(()=>{const ce=tn.filterProps(e),De=nn.filterProps(e),xe=an.filterProps(e);return s(an,F({class:["v-data-table",{"v-data-table--loading":e.loading},e.class],style:e.style},xe),{top:()=>a.top?.(se.value),wrapper:()=>s("div",{ref:Z,onScrollPassive:le,onScrollend:oe,class:"v-table__wrapper",style:{height:K(e.height)}},[s("table",null,[a.colgroup?.(se.value),!e.hideDefaultHeader&&s("thead",{key:"thead"},[s(tn,F(ce,{sticky:e.fixedHeader}),a)]),!e.hideDefaultBody&&s("tbody",null,[s("tr",{ref:$,style:{height:K(E.value),border:0}},[s("td",{colspan:d.value.length,style:{height:0,border:0}},null)]),a["body.prepend"]?.(se.value),s(nn,F(n,De,{items:W.value}),{...a,item:J=>s(Hr,{key:J.internalItem.index,renderless:!0,"onUpdate:height":G=>ue(J.internalItem.index,G)},{default:G=>{let{itemRef:de}=G;return a.item?.({...J,itemRef:de})??s(Wo,F(J.props,{ref:de,key:J.internalItem.index,index:J.internalItem.index}),a)}})}),a["body.append"]?.(se.value),s("tr",{style:{height:K(R.value),border:0}},[s("td",{colspan:d.value.length,style:{height:0,border:0}},null)])])])]),bottom:()=>a.bottom?.(se.value)})})}}),Wh=L({itemsLength:{type:[Number,String],required:!0},...Fo(),...Yo(),...No()},"VDataTableServer"),jh=M()({name:"VDataTableServer",props:Wh(),emits:{"update:modelValue":e=>!0,"update:page":e=>!0,"update:itemsPerPage":e=>!0,"update:sortBy":e=>!0,"update:options":e=>!0,"update:expanded":e=>!0,"update:groupBy":e=>!0},setup(e,t){let{attrs:n,slots:a}=t;const{groupBy:l}=Eo(e),{sortBy:o,multiSort:i,mustSort:r}=el(e),{page:u,itemsPerPage:d}=Oo(e),{disableSort:c}=ln(e),f=m(()=>parseInt(e.itemsLength,10)),{columns:v,headers:g}=zo(e,{groupBy:l,showSelect:D(e,"showSelect"),showExpand:D(e,"showExpand")}),{items:h}=jo(e,v),{toggleSort:b}=tl({sortBy:o,multiSort:i,mustSort:r,page:u}),{opened:y,isGroupOpen:S,toggleGroup:w,extractRows:p}=qa({groupBy:l,sortBy:o,disableSort:c}),{pageCount:A,setItemsPerPage:V}=$o({page:u,itemsPerPage:d,itemsLength:f}),{flatItems:x}=Xa(h,l,y),{isSelected:I,select:k,selectAll:C,toggleSelect:T,someSelected:P,allSelected:_}=Ja(e,{allItems:h,currentPage:h}),{isExpanded:B,toggleExpand:N}=Ka(e),z=m(()=>p(h.value));Za({page:u,itemsPerPage:d,sortBy:o,groupBy:l,search:D(e,"search")}),we("v-data-table",{toggleSort:b,sortBy:o}),pe({VDataTableRows:{hideNoData:D(e,"hideNoData"),noDataText:D(e,"noDataText"),loading:D(e,"loading"),loadingText:D(e,"loadingText")}});const j=m(()=>({page:u.value,itemsPerPage:d.value,sortBy:o.value,pageCount:A.value,toggleSort:b,setItemsPerPage:V,someSelected:P.value,allSelected:_.value,isSelected:I,select:k,selectAll:C,toggleSelect:T,isExpanded:B,toggleExpand:N,isGroupOpen:S,toggleGroup:w,items:z.value.map(ee=>ee.raw),internalItems:z.value,groupedItems:x.value,columns:v.value,headers:g.value}));O(()=>{const ee=qn.filterProps(e),Z=tn.filterProps(e),$=nn.filterProps(e),E=an.filterProps(e);return s(an,F({class:["v-data-table",{"v-data-table--loading":e.loading},e.class],style:e.style},E),{top:()=>a.top?.(j.value),default:()=>a.default?a.default(j.value):s(ae,null,[a.colgroup?.(j.value),!e.hideDefaultHeader&&s("thead",{key:"thead",class:"v-data-table__thead",role:"rowgroup"},[s(tn,F(Z,{sticky:e.fixedHeader}),a)]),a.thead?.(j.value),!e.hideDefaultBody&&s("tbody",{class:"v-data-table__tbody",role:"rowgroup"},[a["body.prepend"]?.(j.value),a.body?a.body(j.value):s(nn,F(n,$,{items:x.value}),a),a["body.append"]?.(j.value)]),a.tbody?.(j.value),a.tfoot?.(j.value)]),bottom:()=>a.bottom?a.bottom(j.value):!e.hideDefaultFooter&&s(ae,null,[s(Qn,null,null),s(qn,ee,{prepend:a["footer.prepend"]})])})})}}),Yh=L({fluid:{type:Boolean,default:!1},...X(),...Oe(),...re()},"VContainer"),Gh=M()({name:"VContainer",props:Yh(),setup(e,t){let{slots:n}=t;const{rtlClasses:a}=Fe(),{dimensionStyles:l}=$e(e);return O(()=>s(e.tag,{class:["v-container",{"v-container--fluid":e.fluid},a.value,e.class],style:[l.value,e.style]},n)),{}}}),Eu=Aa.reduce((e,t)=>(e[t]={type:[Boolean,String,Number],default:!1},e),{}),Fu=Aa.reduce((e,t)=>{const n="offset"+An(t);return e[n]={type:[String,Number],default:null},e},{}),Ou=Aa.reduce((e,t)=>{const n="order"+An(t);return e[n]={type:[String,Number],default:null},e},{}),ji={col:Object.keys(Eu),offset:Object.keys(Fu),order:Object.keys(Ou)};function Uh(e,t,n){let a=e;if(!(n==null||n===!1)){if(t){const l=t.replace(e,"");a+=`-${l}`}return e==="col"&&(a="v-"+a),e==="col"&&(n===""||n===!0)||(a+=`-${n}`),a.toLowerCase()}}const Kh=["auto","start","end","center","baseline","stretch"],qh=L({cols:{type:[Boolean,String,Number],default:!1},...Eu,offset:{type:[String,Number],default:null},...Fu,order:{type:[String,Number],default:null},...Ou,alignSelf:{type:String,default:null,validator:e=>Kh.includes(e)},...X(),...re()},"VCol"),Xh=M()({name:"VCol",props:qh(),setup(e,t){let{slots:n}=t;const a=m(()=>{const l=[];let o;for(o in ji)ji[o].forEach(r=>{const u=e[r],d=Uh(o,r,u);d&&l.push(d)});const i=l.some(r=>r.startsWith("v-col-"));return l.push({"v-col":!i||!e.cols,[`v-col-${e.cols}`]:e.cols,[`offset-${e.offset}`]:e.offset,[`order-${e.order}`]:e.order,[`align-self-${e.alignSelf}`]:e.alignSelf}),l});return()=>on(e.tag,{class:[a.value,e.class],style:e.style},n.default?.())}}),Go=["start","end","center"],$u=["space-between","space-around","space-evenly"];function Uo(e,t){return Aa.reduce((n,a)=>{const l=e+An(a);return n[l]=t(),n},{})}const Zh=[...Go,"baseline","stretch"],Ru=e=>Zh.includes(e),Nu=Uo("align",()=>({type:String,default:null,validator:Ru})),Jh=[...Go,...$u],Hu=e=>Jh.includes(e),zu=Uo("justify",()=>({type:String,default:null,validator:Hu})),Qh=[...Go,...$u,"stretch"],Wu=e=>Qh.includes(e),ju=Uo("alignContent",()=>({type:String,default:null,validator:Wu})),Yi={align:Object.keys(Nu),justify:Object.keys(zu),alignContent:Object.keys(ju)},ey={align:"align",justify:"justify",alignContent:"align-content"};function ty(e,t,n){let a=ey[e];if(n!=null){if(t){const l=t.replace(e,"");a+=`-${l}`}return a+=`-${n}`,a.toLowerCase()}}const ny=L({dense:Boolean,noGutters:Boolean,align:{type:String,default:null,validator:Ru},...Nu,justify:{type:String,default:null,validator:Hu},...zu,alignContent:{type:String,default:null,validator:Wu},...ju,...X(),...re()},"VRow"),ay=M()({name:"VRow",props:ny(),setup(e,t){let{slots:n}=t;const a=m(()=>{const l=[];let o;for(o in Yi)Yi[o].forEach(i=>{const r=e[i],u=ty(o,i,r);u&&l.push(u)});return l.push({"v-row--no-gutters":e.noGutters,"v-row--dense":e.dense,[`align-${e.align}`]:e.align,[`justify-${e.justify}`]:e.justify,[`align-content-${e.alignContent}`]:e.alignContent}),l});return()=>on(e.tag,{class:["v-row",a.value,e.class],style:e.style},n.default?.())}}),Yu=Pt("v-spacer","div","VSpacer"),Gu=L({active:{type:[String,Array],default:void 0},disabled:{type:[Boolean,String,Array],default:!1},nextIcon:{type:ie,default:"$next"},prevIcon:{type:ie,default:"$prev"},modeIcon:{type:ie,default:"$subgroup"},text:String,viewMode:{type:String,default:"month"}},"VDatePickerControls"),zl=M()({name:"VDatePickerControls",props:Gu(),emits:{"click:year":()=>!0,"click:month":()=>!0,"click:prev":()=>!0,"click:next":()=>!0,"click:text":()=>!0},setup(e,t){let{emit:n}=t;const a=m(()=>Array.isArray(e.disabled)?e.disabled.includes("text"):!!e.disabled),l=m(()=>Array.isArray(e.disabled)?e.disabled.includes("mode"):!!e.disabled),o=m(()=>Array.isArray(e.disabled)?e.disabled.includes("prev"):!!e.disabled),i=m(()=>Array.isArray(e.disabled)?e.disabled.includes("next"):!!e.disabled);function r(){n("click:prev")}function u(){n("click:next")}function d(){n("click:year")}function c(){n("click:month")}return O(()=>s("div",{class:["v-date-picker-controls"]},[s(he,{class:"v-date-picker-controls__month-btn",disabled:a.value,text:e.text,variant:"text",rounded:!0,onClick:c},null),s(he,{key:"mode-btn",class:"v-date-picker-controls__mode-btn",disabled:l.value,density:"comfortable",icon:e.modeIcon,variant:"text",onClick:d},null),s(Yu,{key:"mode-spacer"},null),s("div",{key:"month-buttons",class:"v-date-picker-controls__month"},[s(he,{disabled:o.value,icon:e.prevIcon,variant:"text",onClick:r},null),s(he,{disabled:i.value,icon:e.nextIcon,variant:"text",onClick:u},null)])])),{}}}),ly=L({appendIcon:String,color:String,header:String,transition:String,onClick:We()},"VDatePickerHeader"),Wl=M()({name:"VDatePickerHeader",props:ly(),emits:{click:()=>!0,"click:append":()=>!0},setup(e,t){let{emit:n,slots:a}=t;const{backgroundColorClasses:l,backgroundColorStyles:o}=Ce(e,"color");function i(){n("click")}function r(){n("click:append")}return O(()=>{const u=!!(a.default||e.header),d=!!(a.append||e.appendIcon);return s("div",{class:["v-date-picker-header",{"v-date-picker-header--clickable":!!e.onClick},l.value],style:o.value,onClick:i},[a.prepend&&s("div",{key:"prepend",class:"v-date-picker-header__prepend"},[a.prepend()]),u&&s(Xe,{key:"content",name:e.transition},{default:()=>[s("div",{key:e.header,class:"v-date-picker-header__content"},[a.default?.()??e.header])]}),d&&s("div",{class:"v-date-picker-header__append"},[a.append?s(ve,{key:"append-defaults",disabled:!e.appendIcon,defaults:{VBtn:{icon:e.appendIcon,variant:"text"}}},{default:()=>[a.append?.()]}):s(he,{key:"append-btn",icon:e.appendIcon,variant:"text",onClick:r},null)])])}),{}}}),oy=L({allowedDates:[Array,Function],disabled:Boolean,displayValue:null,modelValue:Array,month:[Number,String],max:null,min:null,showAdjacentMonths:Boolean,year:[Number,String],weekdays:{type:Array,default:()=>[0,1,2,3,4,5,6]},weeksInMonth:{type:String,default:"dynamic"},firstDayOfWeek:[Number,String]},"calendar");function iy(e){const t=Ln(),n=Q(e,"modelValue",[],g=>Pe(g)),a=m(()=>e.displayValue?t.date(e.displayValue):n.value.length>0?t.date(n.value[0]):e.min?t.date(e.min):Array.isArray(e.allowedDates)?t.date(e.allowedDates[0]):t.date()),l=Q(e,"year",void 0,g=>{const h=g!=null?Number(g):t.getYear(a.value);return t.startOfYear(t.setYear(t.date(),h))},g=>t.getYear(g)),o=Q(e,"month",void 0,g=>{const h=g!=null?Number(g):t.getMonth(a.value),b=t.setYear(t.startOfMonth(t.date()),t.getYear(l.value));return t.setMonth(b,h)},g=>t.getMonth(g)),i=m(()=>{const g=Number(e.firstDayOfWeek??0);return e.weekdays.map(h=>(h+g)%7)}),r=m(()=>{const g=t.getWeekArray(o.value,e.firstDayOfWeek),h=g.flat(),b=6*7;if(e.weeksInMonth==="static"&&h.length<b){const y=h[h.length-1];let S=[];for(let w=1;w<=b-h.length;w++)S.push(t.addDays(y,w)),w%7===0&&(g.push(S),S=[])}return g});function u(g,h){return g.filter(b=>i.value.includes(t.toJsDate(b).getDay())).map((b,y)=>{const S=t.toISO(b),w=!t.isSameMonth(b,o.value),p=t.isSameDay(b,t.startOfMonth(o.value)),A=t.isSameDay(b,t.endOfMonth(o.value)),V=t.isSameDay(b,o.value);return{date:b,isoDate:S,formatted:t.format(b,"keyboardDate"),year:t.getYear(b),month:t.getMonth(b),isDisabled:v(b),isWeekStart:y%7===0,isWeekEnd:y%7===6,isToday:t.isSameDay(b,h),isAdjacent:w,isHidden:w&&!e.showAdjacentMonths,isStart:p,isSelected:n.value.some(x=>t.isSameDay(b,x)),isEnd:A,isSame:V,localized:t.format(b,"dayOfMonth")}})}const d=m(()=>{const g=t.startOfWeek(a.value,e.firstDayOfWeek),h=[];for(let y=0;y<=6;y++)h.push(t.addDays(g,y));const b=t.date();return u(h,b)}),c=m(()=>{const g=r.value.flat(),h=t.date();return u(g,h)}),f=m(()=>r.value.map(g=>g.length?vv(t,g[0]):null));function v(g){if(e.disabled)return!0;const h=t.date(g);return e.min&&t.isAfter(t.date(e.min),h)||e.max&&t.isAfter(h,t.date(e.max))?!0:Array.isArray(e.allowedDates)&&e.allowedDates.length>0?!e.allowedDates.some(b=>t.isSameDay(t.date(b),h)):typeof e.allowedDates=="function"?!e.allowedDates(h):!1}return{displayValue:a,daysInMonth:c,daysInWeek:d,genDays:u,model:n,weeksInMonth:r,weekDays:i,weekNumbers:f}}const Uu=L({color:String,hideWeekdays:Boolean,multiple:[Boolean,Number,String],showWeek:Boolean,transition:{type:String,default:"picker-transition"},reverseTransition:{type:String,default:"picker-reverse-transition"},...oy()},"VDatePickerMonth"),jl=M()({name:"VDatePickerMonth",props:Uu(),emits:{"update:modelValue":e=>!0,"update:month":e=>!0,"update:year":e=>!0},setup(e,t){let{emit:n,slots:a}=t;const l=H(),{daysInMonth:o,model:i,weekNumbers:r}=iy(e),u=Ln(),d=Y(),c=Y(),f=Y(!1),v=m(()=>f.value?e.reverseTransition:e.transition);e.multiple==="range"&&i.value.length>0&&(d.value=i.value[0],i.value.length>1&&(c.value=i.value[i.value.length-1]));const g=m(()=>{const S=["number","string"].includes(typeof e.multiple)?Number(e.multiple):1/0;return i.value.length>=S});U(o,(S,w)=>{w&&(f.value=u.isBefore(S[0].date,w[0].date))});function h(S){const w=u.startOfDay(S);if(i.value.length===0?d.value=void 0:i.value.length===1&&(d.value=i.value[0],c.value=void 0),!d.value)d.value=w,i.value=[d.value];else if(c.value)d.value=S,c.value=void 0,i.value=[d.value];else{if(u.isSameDay(w,d.value)){d.value=void 0,i.value=[];return}else u.isBefore(w,d.value)?(c.value=u.endOfDay(d.value),d.value=w):c.value=u.endOfDay(w);const p=u.getDiff(c.value,d.value,"days"),A=[d.value];for(let V=1;V<p;V++){const x=u.addDays(d.value,V);A.push(x)}A.push(c.value),i.value=A}}function b(S){const w=i.value.findIndex(p=>u.isSameDay(p,S));if(w===-1)i.value=[...i.value,S];else{const p=[...i.value];p.splice(w,1),i.value=p}}function y(S){e.multiple==="range"?h(S):e.multiple?b(S):i.value=[S]}return()=>s("div",{class:"v-date-picker-month"},[e.showWeek&&s("div",{key:"weeks",class:"v-date-picker-month__weeks"},[!e.hideWeekdays&&s("div",{key:"hide-week-days",class:"v-date-picker-month__day"},[Ft(" ")]),r.value.map(S=>s("div",{class:["v-date-picker-month__day","v-date-picker-month__day--adjacent"]},[S]))]),s(Xe,{name:v.value},{default:()=>[s("div",{ref:l,key:o.value[0].date?.toString(),class:"v-date-picker-month__days"},[!e.hideWeekdays&&u.getWeekdays(e.firstDayOfWeek).map(S=>s("div",{class:["v-date-picker-month__day","v-date-picker-month__weekday"]},[S])),o.value.map((S,w)=>{const p={props:{onClick:()=>y(S.date)},item:S,i:w};return g.value&&!S.isSelected&&(S.isDisabled=!0),s("div",{class:["v-date-picker-month__day",{"v-date-picker-month__day--adjacent":S.isAdjacent,"v-date-picker-month__day--hide-adjacent":S.isHidden,"v-date-picker-month__day--selected":S.isSelected,"v-date-picker-month__day--week-end":S.isWeekEnd,"v-date-picker-month__day--week-start":S.isWeekStart}],"data-v-date":S.isDisabled?void 0:S.isoDate},[(e.showAdjacentMonths||!S.isAdjacent)&&s(ve,{defaults:{VBtn:{class:"v-date-picker-month__day-btn",color:(S.isSelected||S.isToday)&&!S.isDisabled?e.color:void 0,disabled:S.isDisabled,icon:!0,ripple:!1,text:S.localized,variant:S.isDisabled?S.isToday?"outlined":"text":S.isToday&&!S.isSelected?"outlined":"flat",onClick:()=>y(S.date)}}},{default:()=>[a.day?.(p)??s(he,p.props,null)]})])})])]})])}}),Ku=L({color:String,height:[String,Number],min:null,max:null,modelValue:Number,year:Number},"VDatePickerMonths"),Yl=M()({name:"VDatePickerMonths",props:Ku(),emits:{"update:modelValue":e=>!0},setup(e,t){let{emit:n,slots:a}=t;const l=Ln(),o=Q(e,"modelValue"),i=m(()=>{let r=l.startOfYear(l.date());return e.year&&(r=l.setYear(r,e.year)),Ct(12).map(u=>{const d=l.format(r,"monthShort"),c=!!(e.min&&l.isAfter(l.startOfMonth(l.date(e.min)),r)||e.max&&l.isAfter(r,l.startOfMonth(l.date(e.max))));return r=l.getNextMonth(r),{isDisabled:c,text:d,value:u}})});return Te(()=>{o.value=o.value??l.getMonth(l.date())}),O(()=>s("div",{class:"v-date-picker-months",style:{height:K(e.height)}},[s("div",{class:"v-date-picker-months__content"},[i.value.map((r,u)=>{const d={active:o.value===u,color:o.value===u?e.color:void 0,disabled:r.isDisabled,rounded:!0,text:r.text,variant:o.value===r.value?"flat":"text",onClick:()=>c(u)};function c(f){if(o.value===f){n("update:modelValue",o.value);return}o.value=f}return a.month?.({month:r,i:u,props:d})??s(he,F({key:"month"},d),null)})])])),{}}}),qu=L({color:String,height:[String,Number],min:null,max:null,modelValue:Number},"VDatePickerYears"),Gl=M()({name:"VDatePickerYears",props:qu(),emits:{"update:modelValue":e=>!0},setup(e,t){let{emit:n,slots:a}=t;const l=Ln(),o=Q(e,"modelValue"),i=m(()=>{const u=l.getYear(l.date());let d=u-100,c=u+52;e.min&&(d=l.getYear(l.date(e.min))),e.max&&(c=l.getYear(l.date(e.max)));let f=l.startOfYear(l.date());return f=l.setYear(f,d),Ct(c-d+1,d).map(v=>{const g=l.format(f,"year");return f=l.setYear(f,l.getYear(f)+1),{text:g,value:v}})});Te(()=>{o.value=o.value??l.getYear(l.date())});const r=ha();return Qe(async()=>{await ke(),r.el?.scrollIntoView({block:"center"})}),O(()=>s("div",{class:"v-date-picker-years",style:{height:K(e.height)}},[s("div",{class:"v-date-picker-years__content"},[i.value.map((u,d)=>{const c={ref:o.value===u.value?r:void 0,active:o.value===u.value,color:o.value===u.value?e.color:void 0,rounded:!0,text:u.text,variant:o.value===u.value?"flat":"text",onClick:()=>{if(o.value===u.value){n("update:modelValue",o.value);return}o.value=u.value}};return a.year?.({year:u,i:d,props:c})??s(he,F({key:"month"},c),null)})])])),{}}}),sy=Pt("v-picker-title"),Xu=L({bgColor:String,landscape:Boolean,title:String,hideHeader:Boolean,...Ua()},"VPicker"),Gi=M()({name:"VPicker",props:Xu(),setup(e,t){let{slots:n}=t;const{backgroundColorClasses:a,backgroundColorStyles:l}=Ce(D(e,"color"));return O(()=>{const o=en.filterProps(e),i=!!(e.title||n.title);return s(en,F(o,{color:e.bgColor,class:["v-picker",{"v-picker--landscape":e.landscape,"v-picker--with-actions":!!n.actions},e.class],style:e.style}),{default:()=>[!e.hideHeader&&s("div",{key:"header",class:[a.value],style:[l.value]},[i&&s(sy,{key:"picker-title"},{default:()=>[n.title?.()??e.title]}),n.header&&s("div",{class:"v-picker__header"},[n.header()])]),s("div",{class:"v-picker__body"},[n.default?.()]),n.actions&&s(ve,{defaults:{VBtn:{slim:!0,variant:"text"}}},{default:()=>[s("div",{class:"v-picker__actions"},[n.actions()])]})]})}),{}}}),ry=L({header:{type:String,default:"$vuetify.datePicker.header"},...Gu(),...Uu({weeksInMonth:"static"}),...Ee(Ku(),["modelValue"]),...Ee(qu(),["modelValue"]),...Xu({title:"$vuetify.datePicker.title"}),modelValue:null},"VDatePicker"),uy=M()({name:"VDatePicker",props:ry(),emits:{"update:modelValue":e=>!0,"update:month":e=>!0,"update:year":e=>!0,"update:viewMode":e=>!0},setup(e,t){let{emit:n,slots:a}=t;const l=Ln(),{t:o}=Ae(),i=Q(e,"modelValue",void 0,C=>Pe(C),C=>e.multiple?C:C[0]),r=Q(e,"viewMode"),u=m(()=>{const C=l.date(i.value?.[0]);return C&&l.isValid(C)?C:l.date()}),d=H(Number(e.month??l.getMonth(l.startOfMonth(u.value)))),c=H(Number(e.year??l.getYear(l.startOfYear(l.setMonth(u.value,d.value))))),f=Y(!1),v=m(()=>e.multiple&&i.value.length>1?o("$vuetify.datePicker.itemsSelected",i.value.length):i.value[0]&&l.isValid(i.value[0])?l.format(l.date(i.value[0]),"normalDateWithWeekday"):o(e.header)),g=m(()=>{let C=l.date();return C=l.setDate(C,1),C=l.setMonth(C,d.value),C=l.setYear(C,c.value),l.format(C,"monthAndYear")}),h=m(()=>`date-picker-header${f.value?"-reverse":""}-transition`),b=m(()=>{const C=l.date(e.min);return e.min&&l.isValid(C)?C:null}),y=m(()=>{const C=l.date(e.max);return e.max&&l.isValid(C)?C:null}),S=m(()=>{if(e.disabled)return!0;const C=[];if(r.value!=="month")C.push("prev","next");else{let T=l.date();if(T=l.setYear(T,c.value),T=l.setMonth(T,d.value),b.value){const P=l.addDays(l.startOfMonth(T),-1);l.isAfter(b.value,P)&&C.push("prev")}if(y.value){const P=l.addDays(l.endOfMonth(T),1);l.isAfter(P,y.value)&&C.push("next")}}return C});function w(){d.value<11?d.value++:(c.value++,d.value=0,k(c.value)),I(d.value)}function p(){d.value>0?d.value--:(c.value--,d.value=11,k(c.value)),I(d.value)}function A(){r.value="month"}function V(){r.value=r.value==="months"?"month":"months"}function x(){r.value=r.value==="year"?"month":"year"}function I(C){r.value==="months"&&V(),n("update:month",C)}function k(C){r.value==="year"&&x(),n("update:year",C)}return U(i,(C,T)=>{const P=Pe(T),_=Pe(C);if(!_.length)return;const B=l.date(P[P.length-1]),N=l.date(_[_.length-1]),z=l.getMonth(N),j=l.getYear(N);z!==d.value&&(d.value=z,I(d.value)),j!==c.value&&(c.value=j,k(c.value)),f.value=l.isBefore(B,N)}),O(()=>{const C=Gi.filterProps(e),T=zl.filterProps(e),P=Wl.filterProps(e),_=jl.filterProps(e),B=Ee(Yl.filterProps(e),["modelValue"]),N=Ee(Gl.filterProps(e),["modelValue"]),z={header:v.value,transition:h.value};return s(Gi,F(C,{class:["v-date-picker",`v-date-picker--${r.value}`,{"v-date-picker--show-week":e.showWeek},e.class],style:e.style}),{title:()=>a.title?.()??s("div",{class:"v-date-picker__title"},[o(e.title)]),header:()=>a.header?s(ve,{defaults:{VDatePickerHeader:{...z}}},{default:()=>[a.header?.(z)]}):s(Wl,F({key:"header"},P,z,{onClick:r.value!=="month"?A:void 0}),{...a,default:void 0}),default:()=>s(ae,null,[s(zl,F(T,{disabled:S.value,text:g.value,"onClick:next":w,"onClick:prev":p,"onClick:month":V,"onClick:year":x}),null),s(Wn,{hideOnLeave:!0},{default:()=>[r.value==="months"?s(Yl,F({key:"date-picker-months"},B,{modelValue:d.value,"onUpdate:modelValue":[j=>d.value=j,I],min:b.value,max:y.value,year:c.value}),null):r.value==="year"?s(Gl,F({key:"date-picker-years"},N,{modelValue:c.value,"onUpdate:modelValue":[j=>c.value=j,k],min:b.value,max:y.value}),null):s(jl,F({key:"date-picker-month"},_,{modelValue:i.value,"onUpdate:modelValue":j=>i.value=j,month:d.value,"onUpdate:month":[j=>d.value=j,I],year:c.value,"onUpdate:year":[j=>c.value=j,k],min:b.value,max:y.value}),null)]})]),actions:a.actions})}),{}}}),cy=L({actionText:String,bgColor:String,color:String,icon:ie,image:String,justify:{type:String,default:"center"},headline:String,title:String,text:String,textWidth:{type:[Number,String],default:500},href:String,to:String,...X(),...Oe(),...It({size:void 0}),...me()},"VEmptyState"),dy=M()({name:"VEmptyState",props:cy(),emits:{"click:action":e=>!0},setup(e,t){let{emit:n,slots:a}=t;const{themeClasses:l}=be(e),{backgroundColorClasses:o,backgroundColorStyles:i}=Ce(D(e,"bgColor")),{dimensionStyles:r}=$e(e),{displayClasses:u}=ut();function d(c){n("click:action",c)}return O(()=>{const c=!!(a.actions||e.actionText),f=!!(a.headline||e.headline),v=!!(a.title||e.title),g=!!(a.text||e.text),h=!!(a.media||e.image||e.icon),b=e.size||(e.image?200:96);return s("div",{class:["v-empty-state",{[`v-empty-state--${e.justify}`]:!0},l.value,o.value,u.value,e.class],style:[i.value,r.value,e.style]},[h&&s("div",{key:"media",class:"v-empty-state__media"},[a.media?s(ve,{key:"media-defaults",defaults:{VImg:{src:e.image,height:b},VIcon:{size:b,icon:e.icon}}},{default:()=>[a.media()]}):s(ae,null,[e.image?s(_t,{key:"image",src:e.image,height:b},null):e.icon?s(ye,{key:"icon",color:e.color,size:b,icon:e.icon},null):void 0])]),f&&s("div",{key:"headline",class:"v-empty-state__headline"},[a.headline?.()??e.headline]),v&&s("div",{key:"title",class:"v-empty-state__title"},[a.title?.()??e.title]),g&&s("div",{key:"text",class:"v-empty-state__text",style:{maxWidth:K(e.textWidth)}},[a.text?.()??e.text]),a.default&&s("div",{key:"content",class:"v-empty-state__content"},[a.default()]),c&&s("div",{key:"actions",class:"v-empty-state__actions"},[s(ve,{defaults:{VBtn:{class:"v-empty-state__action-btn",color:e.color??"surface-variant",text:e.actionText}}},{default:()=>[a.actions?.({props:{onClick:d}})??s(he,{onClick:d},null)]})])])}),{}}}),Xn=Symbol.for("vuetify:v-expansion-panel"),Zu=L({...X(),...Po()},"VExpansionPanelText"),Ul=M()({name:"VExpansionPanelText",props:Zu(),setup(e,t){let{slots:n}=t;const a=ge(Xn);if(!a)throw new Error("[Vuetify] v-expansion-panel-text needs to be placed inside v-expansion-panel");const{hasContent:l,onAfterLeave:o}=Io(e,a.isSelected);return O(()=>s(Da,{onAfterLeave:o},{default:()=>[Ie(s("div",{class:["v-expansion-panel-text",e.class],style:e.style},[n.default&&l.value&&s("div",{class:"v-expansion-panel-text__wrapper"},[n.default?.()])]),[[bt,a.isSelected.value]])]})),{}}}),Ju=L({color:String,expandIcon:{type:ie,default:"$expand"},collapseIcon:{type:ie,default:"$collapse"},hideActions:Boolean,focusable:Boolean,static:Boolean,ripple:{type:[Boolean,Object],default:!1},readonly:Boolean,...X(),...Oe()},"VExpansionPanelTitle"),Kl=M()({name:"VExpansionPanelTitle",directives:{Ripple:Nt},props:Ju(),setup(e,t){let{slots:n}=t;const a=ge(Xn);if(!a)throw new Error("[Vuetify] v-expansion-panel-title needs to be placed inside v-expansion-panel");const{backgroundColorClasses:l,backgroundColorStyles:o}=Ce(e,"color"),{dimensionStyles:i}=$e(e),r=m(()=>({collapseIcon:e.collapseIcon,disabled:a.disabled.value,expanded:a.isSelected.value,expandIcon:e.expandIcon,readonly:e.readonly})),u=m(()=>a.isSelected.value?e.collapseIcon:e.expandIcon);return O(()=>Ie(s("button",{class:["v-expansion-panel-title",{"v-expansion-panel-title--active":a.isSelected.value,"v-expansion-panel-title--focusable":e.focusable,"v-expansion-panel-title--static":e.static},l.value,e.class],style:[o.value,i.value,e.style],type:"button",tabindex:a.disabled.value?-1:void 0,disabled:a.disabled.value,"aria-expanded":a.isSelected.value,onClick:e.readonly?void 0:a.toggle},[s("span",{class:"v-expansion-panel-title__overlay"},null),n.default?.(r.value),!e.hideActions&&s(ve,{defaults:{VIcon:{icon:u.value}}},{default:()=>[s("span",{class:"v-expansion-panel-title__icon"},[n.actions?.(r.value)??s(ye,null,null)])]})]),[[st("ripple"),e.ripple]])),{}}}),Qu=L({title:String,text:String,bgColor:String,...Ne(),...fn(),...Ve(),...re(),...Ju(),...Zu()},"VExpansionPanel"),vy=M()({name:"VExpansionPanel",props:Qu(),emits:{"group:selected":e=>!0},setup(e,t){let{slots:n}=t;const a=mn(e,Xn),{backgroundColorClasses:l,backgroundColorStyles:o}=Ce(e,"bgColor"),{elevationClasses:i}=Ye(e),{roundedClasses:r}=Le(e),u=m(()=>a?.disabled.value||e.disabled),d=m(()=>a.group.items.value.reduce((v,g,h)=>(a.group.selected.value.includes(g.id)&&v.push(h),v),[])),c=m(()=>{const v=a.group.items.value.findIndex(g=>g.id===a.id);return!a.isSelected.value&&d.value.some(g=>g-v===1)}),f=m(()=>{const v=a.group.items.value.findIndex(g=>g.id===a.id);return!a.isSelected.value&&d.value.some(g=>g-v===-1)});return we(Xn,a),O(()=>{const v=!!(n.text||e.text),g=!!(n.title||e.title),h=Kl.filterProps(e),b=Ul.filterProps(e);return s(e.tag,{class:["v-expansion-panel",{"v-expansion-panel--active":a.isSelected.value,"v-expansion-panel--before-active":c.value,"v-expansion-panel--after-active":f.value,"v-expansion-panel--disabled":u.value},r.value,l.value,e.class],style:[o.value,e.style]},{default:()=>[s("div",{class:["v-expansion-panel__shadow",...i.value]},null),s(ve,{defaults:{VExpansionPanelTitle:{...h},VExpansionPanelText:{...b}}},{default:()=>[g&&s(Kl,{key:"title"},{default:()=>[n.title?n.title():e.title]}),v&&s(Ul,{key:"text"},{default:()=>[n.text?n.text():e.text]}),n.default?.()]})]})}),{groupItem:a}}}),fy=["default","accordion","inset","popout"],my=L({flat:Boolean,...vn(),...eo(Qu(),["bgColor","collapseIcon","color","eager","elevation","expandIcon","focusable","hideActions","readonly","ripple","rounded","tile","static"]),...me(),...X(),...re(),variant:{type:String,default:"default",validator:e=>fy.includes(e)}},"VExpansionPanels"),gy=M()({name:"VExpansionPanels",props:my(),emits:{"update:modelValue":e=>!0},setup(e,t){let{slots:n}=t;const{next:a,prev:l}=Ht(e,Xn),{themeClasses:o}=be(e),i=m(()=>e.variant&&`v-expansion-panels--variant-${e.variant}`);return pe({VExpansionPanel:{bgColor:D(e,"bgColor"),collapseIcon:D(e,"collapseIcon"),color:D(e,"color"),eager:D(e,"eager"),elevation:D(e,"elevation"),expandIcon:D(e,"expandIcon"),focusable:D(e,"focusable"),hideActions:D(e,"hideActions"),readonly:D(e,"readonly"),ripple:D(e,"ripple"),rounded:D(e,"rounded"),static:D(e,"static")}}),O(()=>s(e.tag,{class:["v-expansion-panels",{"v-expansion-panels--flat":e.flat,"v-expansion-panels--tile":e.tile},o.value,i.value,e.class],style:e.style},{default:()=>[n.default?.({prev:l,next:a})]})),{next:a,prev:l}}}),hy=L({app:Boolean,appear:Boolean,extended:Boolean,layout:Boolean,offset:Boolean,modelValue:{type:Boolean,default:!0},...Ee(ja({active:!0}),["location"]),...rn(),...Dt(),...St({transition:"fab-transition"})},"VFab"),yy=M()({name:"VFab",props:hy(),emits:{"update:modelValue":e=>!0},setup(e,t){let{slots:n}=t;const a=Q(e,"modelValue"),l=Y(56),o=H(),{resizeRef:i}=yt(f=>{f.length&&(l.value=f[0].target.clientHeight)}),r=m(()=>e.app||e.absolute),u=m(()=>r.value?e.location?.split(" ").shift()??"bottom":!1),d=m(()=>r.value?e.location?.split(" ")[1]??"end":!1);nt(()=>e.app,()=>{const f=un({id:e.name,order:m(()=>parseInt(e.order,10)),position:u,layoutSize:m(()=>e.layout?l.value+24:0),elementSize:m(()=>l.value+24),active:m(()=>e.app&&a.value),absolute:D(e,"absolute")});Te(()=>{o.value=f.layoutItemStyles.value})});const c=H();return O(()=>{const f=he.filterProps(e);return s("div",{ref:c,class:["v-fab",{"v-fab--absolute":e.absolute,"v-fab--app":!!e.app,"v-fab--extended":e.extended,"v-fab--offset":e.offset,[`v-fab--${u.value}`]:r.value,[`v-fab--${d.value}`]:r.value},e.class],style:[e.app?{...o.value}:{height:"inherit",width:void 0},e.style]},[s("div",{class:"v-fab__container"},[s(Xe,{appear:e.appear,transition:e.transition},{default:()=>[Ie(s(he,F({ref:i},f,{active:void 0,location:void 0}),n),[[bt,e.active]])]})])])}),{}}}),by=L({chips:Boolean,counter:Boolean,counterSizeString:{type:String,default:"$vuetify.fileInput.counterSize"},counterString:{type:String,default:"$vuetify.fileInput.counter"},hideInput:Boolean,multiple:Boolean,showSize:{type:[Boolean,Number,String],default:!1,validator:e=>typeof e=="boolean"||[1e3,1024].includes(Number(e))},...Bt({prependIcon:"$file"}),modelValue:{type:[Array,Object],default:e=>e.multiple?[]:null,validator:e=>Pe(e).every(t=>t!=null&&typeof t=="object")},...la({clearable:!0})},"VFileInput"),Sy=M()({name:"VFileInput",inheritAttrs:!1,props:by(),emits:{"click:control":e=>!0,"mousedown:control":e=>!0,"update:focused":e=>!0,"update:modelValue":e=>!0},setup(e,t){let{attrs:n,emit:a,slots:l}=t;const{t:o}=Ae(),i=Q(e,"modelValue",e.modelValue,C=>Pe(C),C=>!e.multiple&&Array.isArray(C)?C[0]:C),{isFocused:r,focus:u,blur:d}=Tt(e),c=m(()=>typeof e.showSize!="boolean"?e.showSize:void 0),f=m(()=>(i.value??[]).reduce((C,T)=>{let{size:P=0}=T;return C+P},0)),v=m(()=>li(f.value,c.value)),g=m(()=>(i.value??[]).map(C=>{const{name:T="",size:P=0}=C;return e.showSize?`${T} (${li(P,c.value)})`:T})),h=m(()=>{const C=i.value?.length??0;return e.showSize?o(e.counterSizeString,C,v.value):o(e.counterString,C)}),b=H(),y=H(),S=H(),w=m(()=>r.value||e.active),p=m(()=>["plain","underlined"].includes(e.variant));function A(){S.value!==document.activeElement&&S.value?.focus(),r.value||u()}function V(C){S.value?.click()}function x(C){a("mousedown:control",C)}function I(C){S.value?.click(),a("click:control",C)}function k(C){C.stopPropagation(),A(),ke(()=>{i.value=[],no(e["onClick:clear"],C)})}return U(i,C=>{(!Array.isArray(C)||!C.length)&&S.value&&(S.value.value="")}),O(()=>{const C=!!(l.counter||e.counter),T=!!(C||l.details),[P,_]=$t(n),{modelValue:B,...N}=Ue.filterProps(e),z=po(e);return s(Ue,F({ref:b,modelValue:i.value,"onUpdate:modelValue":j=>i.value=j,class:["v-file-input",{"v-file-input--chips":!!e.chips,"v-file-input--hide":e.hideInput,"v-input--plain-underlined":p.value},e.class],style:e.style,"onClick:prepend":V},P,N,{centerAffix:!p.value,focused:r.value}),{...l,default:j=>{let{id:ee,isDisabled:Z,isDirty:$,isReadonly:E,isValid:R}=j;return s(Mn,F({ref:y,"prepend-icon":e.prependIcon,onMousedown:x,onClick:I,"onClick:clear":k,"onClick:prependInner":e["onClick:prependInner"],"onClick:appendInner":e["onClick:appendInner"]},z,{id:ee.value,active:w.value||$.value,dirty:$.value||e.dirty,disabled:Z.value,focused:r.value,error:R.value===!1}),{...l,default:q=>{let{props:{class:ue,...le}}=q;return s(ae,null,[s("input",F({ref:S,type:"file",readonly:E.value,disabled:Z.value,multiple:e.multiple,name:e.name,onClick:oe=>{oe.stopPropagation(),E.value&&oe.preventDefault(),A()},onChange:oe=>{if(!oe.target)return;const W=oe.target;i.value=[...W.files??[]]},onFocus:A,onBlur:d},le,_),null),s("div",{class:ue},[!!i.value?.length&&!e.hideInput&&(l.selection?l.selection({fileNames:g.value,totalBytes:f.value,totalBytesReadable:v.value}):e.chips?g.value.map(oe=>s(Dn,{key:oe,size:"small",text:oe},null)):g.value.join(", "))])])}})},details:T?j=>s(ae,null,[l.details?.(j),C&&s(ae,null,[s("span",null,null),s($a,{active:!!i.value?.length,value:h.value,disabled:e.disabled},l.counter)])]):void 0})}),gt({},b,y,S)}}),ky=L({app:Boolean,color:String,height:{type:[Number,String],default:"auto"},...lt(),...X(),...Ne(),...rn(),...Ve(),...re({tag:"footer"}),...me()},"VFooter"),Cy=M()({name:"VFooter",props:ky(),setup(e,t){let{slots:n}=t;const a=H(),{themeClasses:l}=be(e),{backgroundColorClasses:o,backgroundColorStyles:i}=Ce(D(e,"color")),{borderClasses:r}=dt(e),{elevationClasses:u}=Ye(e),{roundedClasses:d}=Le(e),c=Y(32),{resizeRef:f}=yt(g=>{g.length&&(c.value=g[0].target.clientHeight)}),v=m(()=>e.height==="auto"?c.value:parseInt(e.height,10));return nt(()=>e.app,()=>{const g=un({id:e.name,order:m(()=>parseInt(e.order,10)),position:m(()=>"bottom"),layoutSize:v,elementSize:m(()=>e.height==="auto"?void 0:v.value),active:m(()=>e.app),absolute:D(e,"absolute")});Te(()=>{a.value=g.layoutItemStyles.value})}),O(()=>s(e.tag,{ref:f,class:["v-footer",l.value,o.value,r.value,u.value,d.value,e.class],style:[i.value,e.app?a.value:{height:K(e.height)},e.style]},n)),{}}}),xy=L({...X(),...Tf()},"VForm"),wy=M()({name:"VForm",props:xy(),emits:{"update:modelValue":e=>!0,submit:e=>!0},setup(e,t){let{slots:n,emit:a}=t;const l=Bf(e),o=H();function i(u){u.preventDefault(),l.reset()}function r(u){const d=u,c=l.validate();d.then=c.then.bind(c),d.catch=c.catch.bind(c),d.finally=c.finally.bind(c),a("submit",d),d.defaultPrevented||c.then(f=>{let{valid:v}=f;v&&o.value?.submit()}),d.preventDefault()}return O(()=>s("form",{ref:o,class:["v-form",e.class],style:e.style,novalidate:!0,onReset:i,onSubmit:r},[n.default?.(l)])),gt(l,o)}}),Vy=L({disabled:Boolean,modelValue:{type:Boolean,default:null},...wo()},"VHover"),Py=M()({name:"VHover",props:Vy(),emits:{"update:modelValue":e=>!0},setup(e,t){let{slots:n}=t;const a=Q(e,"modelValue"),{runOpenDelay:l,runCloseDelay:o}=Vo(e,i=>!e.disabled&&(a.value=i));return()=>n.default?.({isHovering:a.value,props:{onMouseenter:l,onMouseleave:o}})}}),Iy=L({color:String,direction:{type:String,default:"vertical",validator:e=>["vertical","horizontal"].includes(e)},side:{type:String,default:"end",validator:e=>["start","end","both"].includes(e)},mode:{type:String,default:"intersect",validator:e=>["intersect","manual"].includes(e)},margin:[Number,String],loadMoreText:{type:String,default:"$vuetify.infiniteScroll.loadMore"},emptyText:{type:String,default:"$vuetify.infiniteScroll.empty"},...Oe(),...re()},"VInfiniteScroll"),Ui=rt({name:"VInfiniteScrollIntersect",props:{side:{type:String,required:!0},rootMargin:String},emits:{intersect:(e,t)=>!0},setup(e,t){let{emit:n}=t;const{intersectionRef:a,isIntersecting:l}=Ra();return U(l,async o=>{n("intersect",e.side,o)}),O(()=>s("div",{class:"v-infinite-scroll-intersect",style:{"--v-infinite-margin-size":e.rootMargin},ref:a},[Ft(" ")])),{}}}),py=M()({name:"VInfiniteScroll",props:Iy(),emits:{load:e=>!0},setup(e,t){let{slots:n,emit:a}=t;const l=H(),o=Y("ok"),i=Y("ok"),r=m(()=>K(e.margin)),u=Y(!1);function d(V){if(!l.value)return;const x=e.direction==="vertical"?"scrollTop":"scrollLeft";l.value[x]=V}function c(){if(!l.value)return 0;const V=e.direction==="vertical"?"scrollTop":"scrollLeft";return l.value[V]}function f(){if(!l.value)return 0;const V=e.direction==="vertical"?"scrollHeight":"scrollWidth";return l.value[V]}function v(){if(!l.value)return 0;const V=e.direction==="vertical"?"clientHeight":"clientWidth";return l.value[V]}Qe(()=>{l.value&&(e.side==="start"?d(f()):e.side==="both"&&d(f()/2-v()/2))});function g(V,x){V==="start"?o.value=x:V==="end"&&(i.value=x)}function h(V){return V==="start"?o.value:i.value}let b=0;function y(V,x){u.value=x,u.value&&S(V)}function S(V){if(e.mode!=="manual"&&!u.value)return;const x=h(V);if(!l.value||["empty","loading"].includes(x))return;b=f(),g(V,"loading");function I(k){g(V,k),ke(()=>{k==="empty"||k==="error"||(k==="ok"&&V==="start"&&d(f()-b+c()),e.mode!=="manual"&&ke(()=>{window.requestAnimationFrame(()=>{window.requestAnimationFrame(()=>{window.requestAnimationFrame(()=>{S(V)})})})}))})}a("load",{side:V,done:I})}const{t:w}=Ae();function p(V,x){if(e.side!==V&&e.side!=="both")return;const I=()=>S(V),k={side:V,props:{onClick:I,color:e.color}};return x==="error"?n.error?.(k):x==="empty"?n.empty?.(k)??s("div",null,[w(e.emptyText)]):e.mode==="manual"?x==="loading"?n.loading?.(k)??s(_n,{indeterminate:!0,color:e.color},null):n["load-more"]?.(k)??s(he,{variant:"outlined",color:e.color,onClick:I},{default:()=>[w(e.loadMoreText)]}):n.loading?.(k)??s(_n,{indeterminate:!0,color:e.color},null)}const{dimensionStyles:A}=$e(e);O(()=>{const V=e.tag,x=e.side==="start"||e.side==="both",I=e.side==="end"||e.side==="both",k=e.mode==="intersect";return s(V,{ref:l,class:["v-infinite-scroll",`v-infinite-scroll--${e.direction}`,{"v-infinite-scroll--start":x,"v-infinite-scroll--end":I}],style:A.value},{default:()=>[s("div",{class:"v-infinite-scroll__side"},[p("start",o.value)]),x&&k&&s(Ui,{key:"start",side:"start",onIntersect:y,rootMargin:r.value},null),n.default?.(),I&&k&&s(Ui,{key:"end",side:"end",onIntersect:y,rootMargin:r.value},null),s("div",{class:"v-infinite-scroll__side"},[p("end",i.value)])]})})}}),ec=Symbol.for("vuetify:v-item-group"),_y=L({...X(),...vn({selectedClass:"v-item--selected"}),...re(),...me()},"VItemGroup"),Ay=M()({name:"VItemGroup",props:_y(),emits:{"update:modelValue":e=>!0},setup(e,t){let{slots:n}=t;const{themeClasses:a}=be(e),{isSelected:l,select:o,next:i,prev:r,selected:u}=Ht(e,ec);return()=>s(e.tag,{class:["v-item-group",a.value,e.class],style:e.style},{default:()=>[n.default?.({isSelected:l,select:o,next:i,prev:r,selected:u.value})]})}}),Ly=M()({name:"VItem",props:fn(),emits:{"group:selected":e=>!0},setup(e,t){let{slots:n}=t;const{isSelected:a,select:l,toggle:o,selectedClass:i,value:r,disabled:u}=mn(e,ec);return()=>n.default?.({isSelected:a.value,selectedClass:i.value,select:l,toggle:o,value:r.value,disabled:u.value})}}),Ty=Pt("v-kbd"),By=L({...X(),...Oe(),...zs()},"VLayout"),Dy=M()({name:"VLayout",props:By(),setup(e,t){let{slots:n}=t;const{layoutClasses:a,layoutStyles:l,getLayoutItem:o,items:i,layoutRef:r}=Ws(e),{dimensionStyles:u}=$e(e);return O(()=>s("div",{ref:r,class:[a.value,e.class],style:[u.value,l.value,e.style]},[n.default?.()])),{getLayoutItem:o,items:i}}}),My=L({position:{type:String,required:!0},size:{type:[Number,String],default:300},modelValue:Boolean,...X(),...rn()},"VLayoutItem"),Ey=M()({name:"VLayoutItem",props:My(),setup(e,t){let{slots:n}=t;const{layoutItemStyles:a}=un({id:e.name,order:m(()=>parseInt(e.order,10)),position:D(e,"position"),elementSize:D(e,"size"),layoutSize:D(e,"size"),active:D(e,"modelValue"),absolute:D(e,"absolute")});return()=>s("div",{class:["v-layout-item",e.class],style:[a.value,e.style]},[n.default?.()])}}),Fy=L({modelValue:Boolean,options:{type:Object,default:()=>({root:void 0,rootMargin:void 0,threshold:void 0})},...X(),...Oe(),...re(),...St({transition:"fade-transition"})},"VLazy"),Oy=M()({name:"VLazy",directives:{intersect:Ma},props:Fy(),emits:{"update:modelValue":e=>!0},setup(e,t){let{slots:n}=t;const{dimensionStyles:a}=$e(e),l=Q(e,"modelValue");function o(i){l.value||(l.value=i)}return O(()=>Ie(s(e.tag,{class:["v-lazy",e.class],style:[a.value,e.style]},{default:()=>[l.value&&s(Xe,{transition:e.transition,appear:!0},{default:()=>[n.default?.()]})]}),[[st("intersect"),{handler:o,options:e.options},null]])),{}}}),$y=L({locale:String,fallbackLocale:String,messages:Object,rtl:{type:Boolean,default:void 0},...X()},"VLocaleProvider"),Ry=M()({name:"VLocaleProvider",props:$y(),setup(e,t){let{slots:n}=t;const{rtlClasses:a}=wd(e);return O(()=>s("div",{class:["v-locale-provider",a.value,e.class],style:e.style},[n.default?.()])),{}}}),Ny=L({scrollable:Boolean,...X(),...Oe(),...re({tag:"main"})},"VMain"),Hy=M()({name:"VMain",props:Ny(),setup(e,t){let{slots:n}=t;const{dimensionStyles:a}=$e(e),{mainStyles:l}=fo(),{ssrBootStyles:o}=cn();return O(()=>s(e.tag,{class:["v-main",{"v-main--scrollable":e.scrollable},e.class],style:[l.value,o.value,a.value,e.style]},{default:()=>[e.scrollable?s("div",{class:"v-main__scroller"},[n.default?.()]):n.default?.()]})),{}}});function zy(e){let{rootEl:t,isSticky:n,layoutItemStyles:a}=e;const l=Y(!1),o=Y(0),i=m(()=>{const d=typeof l.value=="boolean"?"top":l.value;return[n.value?{top:"auto",bottom:"auto",height:void 0}:void 0,l.value?{[d]:K(o.value)}:{top:a.value.top}]});Qe(()=>{U(n,d=>{d?window.addEventListener("scroll",u,{passive:!0}):window.removeEventListener("scroll",u)},{immediate:!0})}),Je(()=>{window.removeEventListener("scroll",u)});let r=0;function u(){const d=r>window.scrollY?"up":"down",c=t.value.getBoundingClientRect(),f=parseFloat(a.value.top??0),v=window.scrollY-Math.max(0,o.value-f),g=c.height+Math.max(o.value,f)-window.scrollY-window.innerHeight,h=parseFloat(getComputedStyle(t.value).getPropertyValue("--v-body-scroll-y"))||0;c.height<window.innerHeight-f?(l.value="top",o.value=f):d==="up"&&l.value==="bottom"||d==="down"&&l.value==="top"?(o.value=window.scrollY+c.top-h,l.value=!0):d==="down"&&g<=0?(o.value=0,l.value="bottom"):d==="up"&&v<=0&&(h?l.value!=="top"&&(o.value=-v+h+f,l.value="top"):(o.value=c.top+v,l.value="top")),r=window.scrollY}return{isStuck:l,stickyStyles:i}}const Wy=100,jy=20;function Ki(e){return(e<0?-1:1)*Math.sqrt(Math.abs(e))*1.41421356237}function qi(e){if(e.length<2)return 0;if(e.length===2)return e[1].t===e[0].t?0:(e[1].d-e[0].d)/(e[1].t-e[0].t);let t=0;for(let n=e.length-1;n>0;n--){if(e[n].t===e[n-1].t)continue;const a=Ki(t),l=(e[n].d-e[n-1].d)/(e[n].t-e[n-1].t);t+=(l-a)*Math.abs(l),n===e.length-1&&(t*=.5)}return Ki(t)*1e3}function Yy(){const e={};function t(l){Array.from(l.changedTouches).forEach(o=>{(e[o.identifier]??(e[o.identifier]=new Oc(jy))).push([l.timeStamp,o])})}function n(l){Array.from(l.changedTouches).forEach(o=>{delete e[o.identifier]})}function a(l){const o=e[l]?.values().reverse();if(!o)throw new Error(`No samples for touch id ${l}`);const i=o[0],r=[],u=[];for(const d of o){if(i[0]-d[0]>Wy)break;r.push({t:d[0],d:d[1].clientX}),u.push({t:d[0],d:d[1].clientY})}return{x:qi(r),y:qi(u),get direction(){const{x:d,y:c}=this,[f,v]=[Math.abs(d),Math.abs(c)];return f>v&&d>=0?"right":f>v&&d<=0?"left":v>f&&c>=0?"down":v>f&&c<=0?"up":Gy()}}}return{addMovement:t,endTouch:n,getVelocity:a}}function Gy(){throw new Error}function Uy(e){let{el:t,isActive:n,isTemporary:a,width:l,touchless:o,position:i}=e;Qe(()=>{window.addEventListener("touchstart",w,{passive:!0}),window.addEventListener("touchmove",p,{passive:!1}),window.addEventListener("touchend",A,{passive:!0})}),Je(()=>{window.removeEventListener("touchstart",w),window.removeEventListener("touchmove",p),window.removeEventListener("touchend",A)});const r=m(()=>["left","right"].includes(i.value)),{addMovement:u,endTouch:d,getVelocity:c}=Yy();let f=!1;const v=Y(!1),g=Y(0),h=Y(0);let b;function y(x,I){return(i.value==="left"?x:i.value==="right"?document.documentElement.clientWidth-x:i.value==="top"?x:i.value==="bottom"?document.documentElement.clientHeight-x:kn())-(I?l.value:0)}function S(x){let I=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;const k=i.value==="left"?(x-h.value)/l.value:i.value==="right"?(document.documentElement.clientWidth-x-h.value)/l.value:i.value==="top"?(x-h.value)/l.value:i.value==="bottom"?(document.documentElement.clientHeight-x-h.value)/l.value:kn();return I?Math.max(0,Math.min(1,k)):k}function w(x){if(o.value)return;const I=x.changedTouches[0].clientX,k=x.changedTouches[0].clientY,C=25,T=i.value==="left"?I<C:i.value==="right"?I>document.documentElement.clientWidth-C:i.value==="top"?k<C:i.value==="bottom"?k>document.documentElement.clientHeight-C:kn(),P=n.value&&(i.value==="left"?I<l.value:i.value==="right"?I>document.documentElement.clientWidth-l.value:i.value==="top"?k<l.value:i.value==="bottom"?k>document.documentElement.clientHeight-l.value:kn());(T||P||n.value&&a.value)&&(b=[I,k],h.value=y(r.value?I:k,n.value),g.value=S(r.value?I:k),f=h.value>-20&&h.value<80,d(x),u(x))}function p(x){const I=x.changedTouches[0].clientX,k=x.changedTouches[0].clientY;if(f){if(!x.cancelable){f=!1;return}const T=Math.abs(I-b[0]),P=Math.abs(k-b[1]);(r.value?T>P&&T>3:P>T&&P>3)?(v.value=!0,f=!1):(r.value?P:T)>3&&(f=!1)}if(!v.value)return;x.preventDefault(),u(x);const C=S(r.value?I:k,!1);g.value=Math.max(0,Math.min(1,C)),C>1?h.value=y(r.value?I:k,!0):C<0&&(h.value=y(r.value?I:k,!1))}function A(x){if(f=!1,!v.value)return;u(x),v.value=!1;const I=c(x.changedTouches[0].identifier),k=Math.abs(I.x),C=Math.abs(I.y);(r.value?k>C&&k>400:C>k&&C>3)?n.value=I.direction===({left:"right",right:"left",top:"down",bottom:"up"}[i.value]||kn()):n.value=g.value>.5}const V=m(()=>v.value?{transform:i.value==="left"?`translateX(calc(-100% + ${g.value*l.value}px))`:i.value==="right"?`translateX(calc(100% - ${g.value*l.value}px))`:i.value==="top"?`translateY(calc(-100% + ${g.value*l.value}px))`:i.value==="bottom"?`translateY(calc(100% - ${g.value*l.value}px))`:kn(),transition:"none"}:void 0);return nt(v,()=>{const x=t.value?.style.transform??null,I=t.value?.style.transition??null;Te(()=>{t.value?.style.setProperty("transform",V.value?.transform||"none"),t.value?.style.setProperty("transition",V.value?.transition||null)}),Ze(()=>{t.value?.style.setProperty("transform",x),t.value?.style.setProperty("transition",I)})}),{isDragging:v,dragProgress:g,dragStyles:V}}function kn(){throw new Error}const Ky=["start","end","left","right","top","bottom"],qy=L({color:String,disableResizeWatcher:Boolean,disableRouteWatcher:Boolean,expandOnHover:Boolean,floating:Boolean,modelValue:{type:Boolean,default:null},permanent:Boolean,rail:{type:Boolean,default:null},railWidth:{type:[Number,String],default:56},scrim:{type:[Boolean,String],default:!0},image:String,temporary:Boolean,persistent:Boolean,touchless:Boolean,width:{type:[Number,String],default:256},location:{type:String,default:"start",validator:e=>Ky.includes(e)},sticky:Boolean,...lt(),...X(),...wo(),...sn({mobile:null}),...Ne(),...rn(),...Ve(),...re({tag:"nav"}),...me()},"VNavigationDrawer"),Xy=M()({name:"VNavigationDrawer",props:qy(),emits:{"update:modelValue":e=>!0,"update:rail":e=>!0},setup(e,t){let{attrs:n,emit:a,slots:l}=t;const{isRtl:o}=Fe(),{themeClasses:i}=be(e),{borderClasses:r}=dt(e),{backgroundColorClasses:u,backgroundColorStyles:d}=Ce(D(e,"color")),{elevationClasses:c}=Ye(e),{displayClasses:f,mobile:v}=ut(e),{roundedClasses:g}=Le(e),h=ir(),b=Q(e,"modelValue",null,R=>!!R),{ssrBootStyles:y}=cn(),{scopeId:S}=gn(),w=H(),p=Y(!1),{runOpenDelay:A,runCloseDelay:V}=Vo(e,R=>{p.value=R}),x=m(()=>e.rail&&e.expandOnHover&&p.value?Number(e.width):Number(e.rail?e.railWidth:e.width)),I=m(()=>yl(e.location,o.value)),k=m(()=>e.persistent),C=m(()=>!e.permanent&&(v.value||e.temporary)),T=m(()=>e.sticky&&!C.value&&I.value!=="bottom");nt(()=>e.expandOnHover&&e.rail!=null,()=>{U(p,R=>a("update:rail",!R))}),nt(()=>!e.disableResizeWatcher,()=>{U(C,R=>!e.permanent&&ke(()=>b.value=!R))}),nt(()=>!e.disableRouteWatcher&&!!h,()=>{U(h.currentRoute,()=>C.value&&(b.value=!1))}),U(()=>e.permanent,R=>{R&&(b.value=!0)}),e.modelValue==null&&!C.value&&(b.value=e.permanent||!v.value);const{isDragging:P,dragProgress:_}=Uy({el:w,isActive:b,isTemporary:C,width:x,touchless:D(e,"touchless"),position:I}),B=m(()=>{const R=C.value?0:e.rail&&e.expandOnHover?Number(e.railWidth):x.value;return P.value?R*_.value:R}),N=m(()=>["top","bottom"].includes(e.location)?0:x.value),{layoutItemStyles:z,layoutItemScrimStyles:j}=un({id:e.name,order:m(()=>parseInt(e.order,10)),position:I,layoutSize:B,elementSize:N,active:m(()=>b.value||P.value),disableTransitions:m(()=>P.value),absolute:m(()=>e.absolute||T.value&&typeof ee.value!="string")}),{isStuck:ee,stickyStyles:Z}=zy({rootEl:w,isSticky:T,layoutItemStyles:z}),$=Ce(m(()=>typeof e.scrim=="string"?e.scrim:null)),E=m(()=>({...P.value?{opacity:_.value*.2,transition:"none"}:void 0,...j.value}));return pe({VList:{bgColor:"transparent"}}),O(()=>{const R=l.image||e.image;return s(ae,null,[s(e.tag,F({ref:w,onMouseenter:A,onMouseleave:V,class:["v-navigation-drawer",`v-navigation-drawer--${I.value}`,{"v-navigation-drawer--expand-on-hover":e.expandOnHover,"v-navigation-drawer--floating":e.floating,"v-navigation-drawer--is-hovering":p.value,"v-navigation-drawer--rail":e.rail,"v-navigation-drawer--temporary":C.value,"v-navigation-drawer--persistent":k.value,"v-navigation-drawer--active":b.value,"v-navigation-drawer--sticky":T.value},i.value,u.value,r.value,f.value,c.value,g.value,e.class],style:[d.value,z.value,y.value,Z.value,e.style,["top","bottom"].includes(I.value)?{height:"auto"}:{}]},S,n),{default:()=>[R&&s("div",{key:"image",class:"v-navigation-drawer__img"},[l.image?s(ve,{key:"image-defaults",disabled:!e.image,defaults:{VImg:{alt:"",cover:!0,height:"inherit",src:e.image}}},l.image):s(_t,{key:"image-img",alt:"",cover:!0,height:"inherit",src:e.image},null)]),l.prepend&&s("div",{class:"v-navigation-drawer__prepend"},[l.prepend?.()]),s("div",{class:"v-navigation-drawer__content"},[l.default?.()]),l.append&&s("div",{class:"v-navigation-drawer__append"},[l.append?.()])]}),s(Et,{name:"fade-transition"},{default:()=>[C.value&&(P.value||b.value)&&!!e.scrim&&s("div",F({class:["v-navigation-drawer__scrim",$.backgroundColorClasses.value],style:[E.value,$.backgroundColorStyles.value],onClick:()=>{k.value||(b.value=!1)}},S),null)]})])}),{isStuck:ee}}}),Zy=rt({name:"VNoSsr",setup(e,t){let{slots:n}=t;const a=Or();return()=>a.value&&n.default?.()}}),Jy=L({autofocus:Boolean,divider:String,focusAll:Boolean,label:{type:String,default:"$vuetify.input.otp"},length:{type:[Number,String],default:6},modelValue:{type:[Number,String],default:void 0},placeholder:String,type:{type:String,default:"number"},...Oe(),...ea(),...Va(la({variant:"outlined"}),["baseColor","bgColor","class","color","disabled","error","loading","rounded","style","theme","variant"])},"VOtpInput"),Qy=M()({name:"VOtpInput",props:Jy(),emits:{finish:e=>!0,"update:focused":e=>!0,"update:modelValue":e=>!0},setup(e,t){let{attrs:n,emit:a,slots:l}=t;const{dimensionStyles:o}=$e(e),{isFocused:i,focus:r,blur:u}=Tt(e),d=Q(e,"modelValue","",k=>k==null?[]:String(k).split(""),k=>k.join("")),{t:c}=Ae(),f=m(()=>Number(e.length)),v=m(()=>Array(f.value).fill(0)),g=H(-1),h=H(),b=H([]),y=m(()=>b.value[g.value]);function S(){if(I(y.value.value)){y.value.value="";return}const k=d.value.slice(),C=y.value.value;k[g.value]=C;let T=null;g.value>d.value.length?T=d.value.length+1:g.value+1!==f.value&&(T="next"),d.value=k,T&&Ut(h.value,T)}function w(k){const C=d.value.slice(),T=g.value;let P=null;["ArrowLeft","ArrowRight","Backspace","Delete"].includes(k.key)&&(k.preventDefault(),k.key==="ArrowLeft"?P="prev":k.key==="ArrowRight"?P="next":["Backspace","Delete"].includes(k.key)&&(C[g.value]="",d.value=C,g.value>0&&k.key==="Backspace"?P="prev":requestAnimationFrame(()=>{b.value[T]?.select()})),requestAnimationFrame(()=>{P!=null&&Ut(h.value,P)}))}function p(k,C){C.preventDefault(),C.stopPropagation();const T=C?.clipboardData?.getData("Text").slice(0,f.value)??"";I(T)||(d.value=T.split(""),b.value?.[k].blur())}function A(){d.value=[]}function V(k,C){r(),g.value=C}function x(){u(),g.value=-1}function I(k){return e.type==="number"&&/[^0-9]/g.test(k)}return pe({VField:{color:m(()=>e.color),bgColor:m(()=>e.color),baseColor:m(()=>e.baseColor),disabled:m(()=>e.disabled),error:m(()=>e.error),variant:m(()=>e.variant)}},{scoped:!0}),U(d,k=>{k.length===f.value&&a("finish",k.join(""))},{deep:!0}),U(g,k=>{k<0||ke(()=>{b.value[k]?.select()})}),O(()=>{const[k,C]=$t(n);return s("div",F({class:["v-otp-input",{"v-otp-input--divided":!!e.divider},e.class],style:[e.style]},k),[s("div",{ref:h,class:"v-otp-input__content",style:[o.value]},[v.value.map((T,P)=>s(ae,null,[e.divider&&P!==0&&s("span",{class:"v-otp-input__divider"},[e.divider]),s(Mn,{focused:i.value&&e.focusAll||g.value===P,key:P},{...l,loader:void 0,default:()=>s("input",{ref:_=>b.value[P]=_,"aria-label":c(e.label,P+1),autofocus:P===0&&e.autofocus,autocomplete:"one-time-code",class:["v-otp-input__field"],disabled:e.disabled,inputmode:e.type==="number"?"numeric":"text",min:e.type==="number"?0:void 0,maxlength:"1",placeholder:e.placeholder,type:e.type==="number"?"text":e.type,value:d.value[P],onInput:S,onFocus:_=>V(_,P),onBlur:x,onKeydown:w,onPaste:_=>p(P,_)},null)})])),s("input",F({class:"v-otp-input-input",type:"hidden"},C,{value:d.value.join("")}),null),s(xt,{contained:!0,"content-class":"v-otp-input__loader","model-value":!!e.loading,persistent:!0},{default:()=>[l.loader?.()??s(_n,{color:typeof e.loading=="boolean"?void 0:e.loading,indeterminate:!0,size:"24",width:"2"},null)]}),l.default?.()])])}),{blur:()=>{b.value?.some(k=>k.blur())},focus:()=>{b.value?.[0].focus()},reset:A,isFocused:i}}});function eb(e){return Math.floor(Math.abs(e))*Math.sign(e)}const tb=L({scale:{type:[Number,String],default:.5},...X()},"VParallax"),nb=M()({name:"VParallax",props:tb(),setup(e,t){let{slots:n}=t;const{intersectionRef:a,isIntersecting:l}=Ra(),{resizeRef:o,contentRect:i}=yt(),{height:r}=ut(),u=H();Te(()=>{a.value=o.value=u.value?.$el});let d;U(l,g=>{g?(d=so(a.value),d=d===document.scrollingElement?document:d,d.addEventListener("scroll",v,{passive:!0}),v()):d.removeEventListener("scroll",v)}),Je(()=>{d?.removeEventListener("scroll",v)}),U(r,v),U(()=>i.value?.height,v);const c=m(()=>1-Be(+e.scale));let f=-1;function v(){l.value&&(cancelAnimationFrame(f),f=requestAnimationFrame(()=>{const g=(u.value?.$el).querySelector(".v-img__img");if(!g)return;const h=d instanceof Document?document.documentElement.clientHeight:d.clientHeight,b=d instanceof Document?window.scrollY:d.scrollTop,y=a.value.getBoundingClientRect().top+b,S=i.value.height,w=y+(S-h)/2,p=eb((b-w)*c.value),A=Math.max(1,(c.value*(h-S)+S)/S);g.style.setProperty("transform",`translateY(${p}px) scale(${A})`)}))}return O(()=>s(_t,{class:["v-parallax",{"v-parallax--active":l.value},e.class],style:e.style,ref:u,cover:!0,onLoadstart:v,onLoad:v},n)),{}}}),ab=L({...Fa({falseIcon:"$radioOff",trueIcon:"$radioOn"})},"VRadio"),lb=M()({name:"VRadio",props:ab(),setup(e,t){let{slots:n}=t;return O(()=>{const a=Ot.filterProps(e);return s(Ot,F(a,{class:["v-radio",e.class],style:e.style,type:"radio"}),n)}),{}}}),ob=L({height:{type:[Number,String],default:"auto"},...Bt(),...Ee(Co(),["multiple"]),trueIcon:{type:ie,default:"$radioOn"},falseIcon:{type:ie,default:"$radioOff"},type:{type:String,default:"radio"}},"VRadioGroup"),ib=M()({name:"VRadioGroup",inheritAttrs:!1,props:ob(),emits:{"update:modelValue":e=>!0},setup(e,t){let{attrs:n,slots:a}=t;const l=je(),o=m(()=>e.id||`radio-group-${l}`),i=Q(e,"modelValue");return O(()=>{const[r,u]=$t(n),d=Ue.filterProps(e),c=Ot.filterProps(e),f=a.label?a.label({label:e.label,props:{for:o.value}}):e.label;return s(Ue,F({class:["v-radio-group",e.class],style:e.style},r,d,{modelValue:i.value,"onUpdate:modelValue":v=>i.value=v,id:o.value}),{...a,default:v=>{let{id:g,messagesId:h,isDisabled:b,isReadonly:y}=v;return s(ae,null,[f&&s(Bn,{id:g.value},{default:()=>[f]}),s(Cr,F(c,{id:g.value,"aria-describedby":h.value,defaultsTarget:"VRadio",trueIcon:e.trueIcon,falseIcon:e.falseIcon,type:e.type,disabled:b.value,readonly:y.value,"aria-labelledby":f?g.value:void 0,multiple:!1},u,{modelValue:i.value,"onUpdate:modelValue":S=>i.value=S}),a)])}})}),{}}}),sb=L({...ea(),...Bt(),...ru(),strict:Boolean,modelValue:{type:Array,default:()=>[0,0]}},"VRangeSlider"),rb=M()({name:"VRangeSlider",props:sb(),emits:{"update:focused":e=>!0,"update:modelValue":e=>!0,end:e=>!0,start:e=>!0},setup(e,t){let{slots:n,emit:a}=t;const l=H(),o=H(),i=H(),{rtlClasses:r}=Fe();function u(T){if(!l.value||!o.value)return;const P=Ol(T,l.value.$el,e.direction),_=Ol(T,o.value.$el,e.direction),B=Math.abs(P),N=Math.abs(_);return B<N||B===N&&P<0?l.value.$el:o.value.$el}const d=uu(e),c=Q(e,"modelValue",void 0,T=>T?.length?T.map(P=>d.roundValue(P)):[0,0]),{activeThumbRef:f,hasLabels:v,max:g,min:h,mousePressed:b,onSliderMousedown:y,onSliderTouchstart:S,position:w,trackContainerRef:p,readonly:A}=cu({props:e,steps:d,onSliderStart:()=>{a("start",c.value)},onSliderEnd:T=>{let{value:P}=T;const _=f.value===l.value?.$el?[P,c.value[1]]:[c.value[0],P];!e.strict&&_[0]<_[1]&&(c.value=_),a("end",c.value)},onSliderMove:T=>{let{value:P}=T;const[_,B]=c.value;!e.strict&&_===B&&_!==h.value&&(f.value=P>_?o.value?.$el:l.value?.$el,f.value?.focus()),f.value===l.value?.$el?c.value=[Math.min(P,B),B]:c.value=[_,Math.max(_,P)]},getActiveThumb:u}),{isFocused:V,focus:x,blur:I}=Tt(e),k=m(()=>w(c.value[0])),C=m(()=>w(c.value[1]));return O(()=>{const T=Ue.filterProps(e),P=!!(e.label||n.label||n.prepend);return s(Ue,F({class:["v-slider","v-range-slider",{"v-slider--has-labels":!!n["tick-label"]||v.value,"v-slider--focused":V.value,"v-slider--pressed":b.value,"v-slider--disabled":e.disabled},r.value,e.class],style:e.style,ref:i},T,{focused:V.value}),{...n,prepend:P?_=>s(ae,null,[n.label?.(_)??(e.label?s(Bn,{class:"v-slider__label",text:e.label},null):void 0),n.prepend?.(_)]):void 0,default:_=>{let{id:B,messagesId:N}=_;return s("div",{class:"v-slider__container",onMousedown:A.value?void 0:y,onTouchstartPassive:A.value?void 0:S},[s("input",{id:`${B.value}_start`,name:e.name||B.value,disabled:!!e.disabled,readonly:!!e.readonly,tabindex:"-1",value:c.value[0]},null),s("input",{id:`${B.value}_stop`,name:e.name||B.value,disabled:!!e.disabled,readonly:!!e.readonly,tabindex:"-1",value:c.value[1]},null),s(du,{ref:p,start:k.value,stop:C.value},{"tick-label":n["tick-label"]}),s($l,{ref:l,"aria-describedby":N.value,focused:V&&f.value===l.value?.$el,modelValue:c.value[0],"onUpdate:modelValue":z=>c.value=[z,c.value[1]],onFocus:z=>{x(),f.value=l.value?.$el,c.value[0]===c.value[1]&&c.value[1]===h.value&&z.relatedTarget!==o.value?.$el&&(l.value?.$el.blur(),o.value?.$el.focus())},onBlur:()=>{I(),f.value=void 0},min:h.value,max:c.value[1],position:k.value,ripple:e.ripple},{"thumb-label":n["thumb-label"]}),s($l,{ref:o,"aria-describedby":N.value,focused:V&&f.value===o.value?.$el,modelValue:c.value[1],"onUpdate:modelValue":z=>c.value=[c.value[0],z],onFocus:z=>{x(),f.value=o.value?.$el,c.value[0]===c.value[1]&&c.value[0]===g.value&&z.relatedTarget!==l.value?.$el&&(o.value?.$el.blur(),l.value?.$el.focus())},onBlur:()=>{I(),f.value=void 0},min:c.value[0],max:g.value,position:C.value,ripple:e.ripple},{"thumb-label":n["thumb-label"]})])}})}),{}}}),ub=L({name:String,itemAriaLabel:{type:String,default:"$vuetify.rating.ariaLabel.item"},activeColor:String,color:String,clearable:Boolean,disabled:Boolean,emptyIcon:{type:ie,default:"$ratingEmpty"},fullIcon:{type:ie,default:"$ratingFull"},halfIncrements:Boolean,hover:Boolean,length:{type:[Number,String],default:5},readonly:Boolean,modelValue:{type:[Number,String],default:0},itemLabels:Array,itemLabelPosition:{type:String,default:"top",validator:e=>["top","bottom"].includes(e)},ripple:Boolean,...X(),...ze(),...It(),...re(),...me()},"VRating"),cb=M()({name:"VRating",props:ub(),emits:{"update:modelValue":e=>!0},setup(e,t){let{slots:n}=t;const{t:a}=Ae(),{themeClasses:l}=be(e),o=Q(e,"modelValue"),i=m(()=>Be(parseFloat(o.value),0,+e.length)),r=m(()=>Ct(Number(e.length),1)),u=m(()=>r.value.flatMap(b=>e.halfIncrements?[b-.5,b]:[b])),d=Y(-1),c=m(()=>u.value.map(b=>{const y=e.hover&&d.value>-1,S=i.value>=b,w=d.value>=b,A=(y?w:S)?e.fullIcon:e.emptyIcon,V=e.activeColor??e.color,x=S||w?V:e.color;return{isFilled:S,isHovered:w,icon:A,color:x}})),f=m(()=>[0,...u.value].map(b=>{function y(){d.value=b}function S(){d.value=-1}function w(){e.disabled||e.readonly||(o.value=i.value===b&&e.clearable?0:b)}return{onMouseenter:e.hover?y:void 0,onMouseleave:e.hover?S:void 0,onClick:w}})),v=m(()=>e.name??`v-rating-${je()}`);function g(b){let{value:y,index:S,showStar:w=!0}=b;const{onMouseenter:p,onMouseleave:A,onClick:V}=f.value[S+1],x=`${v.value}-${String(y).replace(".","-")}`,I={color:c.value[S]?.color,density:e.density,disabled:e.disabled,icon:c.value[S]?.icon,ripple:e.ripple,size:e.size,variant:"plain"};return s(ae,null,[s("label",{for:x,class:{"v-rating__item--half":e.halfIncrements&&y%1>0,"v-rating__item--full":e.halfIncrements&&y%1===0},onMouseenter:p,onMouseleave:A,onClick:V},[s("span",{class:"v-rating__hidden"},[a(e.itemAriaLabel,y,e.length)]),w?n.item?n.item({...c.value[S],props:I,value:y,index:S,rating:i.value}):s(he,F({"aria-label":a(e.itemAriaLabel,y,e.length)},I),null):void 0]),s("input",{class:"v-rating__hidden",name:v.value,id:x,type:"radio",value:y,checked:i.value===y,tabindex:-1,readonly:e.readonly,disabled:e.disabled},null)])}function h(b){return n["item-label"]?n["item-label"](b):b.label?s("span",null,[b.label]):s("span",null,[Ft(" ")])}return O(()=>{const b=!!e.itemLabels?.length||n["item-label"];return s(e.tag,{class:["v-rating",{"v-rating--hover":e.hover,"v-rating--readonly":e.readonly},l.value,e.class],style:e.style},{default:()=>[s(g,{value:0,index:-1,showStar:!1},null),r.value.map((y,S)=>s("div",{class:"v-rating__wrapper"},[b&&e.itemLabelPosition==="top"?h({value:y,index:S,label:e.itemLabels?.[S]}):void 0,s("div",{class:"v-rating__item"},[e.halfIncrements?s(ae,null,[s(g,{value:y-.5,index:S*2},null),s(g,{value:y,index:S*2+1},null)]):s(g,{value:y,index:S},null)]),b&&e.itemLabelPosition==="bottom"?h({value:y,index:S,label:e.itemLabels?.[S]}):void 0]))]})}),{}}}),db={actions:"button@2",article:"heading, paragraph",avatar:"avatar",button:"button",card:"image, heading","card-avatar":"image, list-item-avatar",chip:"chip","date-picker":"list-item, heading, divider, date-picker-options, date-picker-days, actions","date-picker-options":"text, avatar@2","date-picker-days":"avatar@28",divider:"divider",heading:"heading",image:"image","list-item":"text","list-item-avatar":"avatar, text","list-item-two-line":"sentences","list-item-avatar-two-line":"avatar, sentences","list-item-three-line":"paragraph","list-item-avatar-three-line":"avatar, paragraph",ossein:"ossein",paragraph:"text@3",sentences:"text@2",subtitle:"text",table:"table-heading, table-thead, table-tbody, table-tfoot","table-heading":"chip, text","table-thead":"heading@6","table-tbody":"table-row-divider@6","table-row-divider":"table-row, divider","table-row":"text@6","table-tfoot":"text@2, avatar@2",text:"text"};function vb(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[];return s("div",{class:["v-skeleton-loader__bone",`v-skeleton-loader__${e}`]},[t])}function Xi(e){const[t,n]=e.split("@");return Array.from({length:n}).map(()=>al(t))}function al(e){let t=[];if(!e)return t;const n=db[e];if(e!==n){if(e.includes(","))return Zi(e);if(e.includes("@"))return Xi(e);n.includes(",")?t=Zi(n):n.includes("@")?t=Xi(n):n&&t.push(al(n))}return[vb(e,t)]}function Zi(e){return e.replace(/\s/g,"").split(",").map(al)}const fb=L({boilerplate:Boolean,color:String,loading:Boolean,loadingText:{type:String,default:"$vuetify.loading"},type:{type:[String,Array],default:"ossein"},...Oe(),...Ne(),...me()},"VSkeletonLoader"),mb=M()({name:"VSkeletonLoader",props:fb(),setup(e,t){let{slots:n}=t;const{backgroundColorClasses:a,backgroundColorStyles:l}=Ce(D(e,"color")),{dimensionStyles:o}=$e(e),{elevationClasses:i}=Ye(e),{themeClasses:r}=be(e),{t:u}=Ae(),d=m(()=>al(Pe(e.type).join(",")));return O(()=>{const c=!n.default||e.loading,f=e.boilerplate||!c?{}:{ariaLive:"polite",ariaLabel:u(e.loadingText),role:"alert"};return s("div",F({class:["v-skeleton-loader",{"v-skeleton-loader--boilerplate":e.boilerplate},r.value,a.value,i.value],style:[l.value,c?o.value:{}]},f),[c?d.value:n.default?.()])}),{}}}),gb=M()({name:"VSlideGroupItem",props:fn(),emits:{"group:selected":e=>!0},setup(e,t){let{slots:n}=t;const a=mn(e,Tr);return()=>n.default?.({isSelected:a.isSelected.value,select:a.select,toggle:a.toggle,selectedClass:a.selectedClass.value})}});function hb(e){const t=Y(e());let n=-1;function a(){clearInterval(n)}function l(){a(),ke(()=>t.value=e())}function o(i){const r=i?getComputedStyle(i):{transitionDuration:.2},u=parseFloat(r.transitionDuration)*1e3||200;if(a(),t.value<=0)return;const d=performance.now();n=window.setInterval(()=>{const c=performance.now()-d+u;t.value=Math.max(e()-c,0),t.value<=0&&a()},u)}return Ze(a),{clear:a,time:t,start:o,reset:l}}const yb=L({multiLine:Boolean,text:String,timer:[Boolean,String],timeout:{type:[Number,String],default:5e3},vertical:Boolean,...Dt({location:"bottom"}),...En(),...Ve(),...mt(),...me(),...Ee(ta({transition:"v-snackbar-transition"}),["persistent","noClickAnimation","scrim","scrollStrategy"])},"VSnackbar"),bb=M()({name:"VSnackbar",props:yb(),emits:{"update:modelValue":e=>!0},setup(e,t){let{slots:n}=t;const a=Q(e,"modelValue"),{positionClasses:l}=Fn(e),{scopeId:o}=gn(),{themeClasses:i}=be(e),{colorClasses:r,colorStyles:u,variantClasses:d}=dn(e),{roundedClasses:c}=Le(e),f=hb(()=>Number(e.timeout)),v=H(),g=H(),h=Y(!1),b=Y(0),y=H(),S=ge(zn,void 0);nt(()=>!!S,()=>{const P=fo();Te(()=>{y.value=P.mainStyles.value})}),U(a,p),U(()=>e.timeout,p),Qe(()=>{a.value&&p()});let w=-1;function p(){f.reset(),window.clearTimeout(w);const P=Number(e.timeout);if(!a.value||P===-1)return;const _=Ql(g.value);f.start(_),w=window.setTimeout(()=>{a.value=!1},P)}function A(){f.reset(),window.clearTimeout(w)}function V(){h.value=!0,A()}function x(){h.value=!1,p()}function I(P){b.value=P.touches[0].clientY}function k(P){Math.abs(b.value-P.changedTouches[0].clientY)>50&&(a.value=!1)}function C(){h.value&&x()}const T=m(()=>e.location.split(" ").reduce((P,_)=>(P[`v-snackbar--${_}`]=!0,P),{}));return O(()=>{const P=xt.filterProps(e),_=!!(n.default||n.text||e.text);return s(xt,F({ref:v,class:["v-snackbar",{"v-snackbar--active":a.value,"v-snackbar--multi-line":e.multiLine&&!e.vertical,"v-snackbar--timer":!!e.timer,"v-snackbar--vertical":e.vertical},T.value,l.value,e.class],style:[y.value,e.style]},P,{modelValue:a.value,"onUpdate:modelValue":B=>a.value=B,contentProps:F({class:["v-snackbar__wrapper",i.value,r.value,c.value,d.value],style:[u.value],onPointerenter:V,onPointerleave:x},P.contentProps),persistent:!0,noClickAnimation:!0,scrim:!1,scrollStrategy:"none",_disableGlobalStack:!0,onTouchstartPassive:I,onTouchend:k,onAfterLeave:C},o),{default:()=>[Rt(!1,"v-snackbar"),e.timer&&!h.value&&s("div",{key:"timer",class:"v-snackbar__timer"},[s(Na,{ref:g,color:typeof e.timer=="string"?e.timer:"info",max:e.timeout,"model-value":f.time.value},null)]),_&&s("div",{key:"content",class:"v-snackbar__content",role:"status","aria-live":"polite"},[n.text?.()??e.text,n.default?.()]),n.actions&&s(ve,{defaults:{VBtn:{variant:"text",ripple:!1,slim:!0}}},{default:()=>[s("div",{class:"v-snackbar__actions"},[n.actions({isActive:a})])]})],activator:n.activator})}),gt({},v)}}),tc=L({autoDraw:Boolean,autoDrawDuration:[Number,String],autoDrawEasing:{type:String,default:"ease"},color:String,gradient:{type:Array,default:()=>[]},gradientDirection:{type:String,validator:e=>["top","bottom","left","right"].includes(e),default:"top"},height:{type:[String,Number],default:75},labels:{type:Array,default:()=>[]},labelSize:{type:[Number,String],default:7},lineWidth:{type:[String,Number],default:4},id:String,itemValue:{type:String,default:"value"},modelValue:{type:Array,default:()=>[]},min:[String,Number],max:[String,Number],padding:{type:[String,Number],default:8},showLabels:Boolean,smooth:Boolean,width:{type:[Number,String],default:300}},"Line"),nc=L({autoLineWidth:Boolean,...tc()},"VBarline"),Ji=M()({name:"VBarline",props:nc(),setup(e,t){let{slots:n}=t;const a=je(),l=m(()=>e.id||`barline-${a}`),o=m(()=>Number(e.autoDrawDuration)||500),i=m(()=>!!(e.showLabels||e.labels.length>0||n?.label)),r=m(()=>parseFloat(e.lineWidth)||4),u=m(()=>Math.max(e.modelValue.length*r.value,Number(e.width))),d=m(()=>({minX:0,maxX:u.value,minY:0,maxY:parseInt(e.height,10)})),c=m(()=>e.modelValue.map(b=>Re(b,e.itemValue,b)));function f(b,y){const{minX:S,maxX:w,minY:p,maxY:A}=y,V=b.length;let x=e.max!=null?Number(e.max):Math.max(...b),I=e.min!=null?Number(e.min):Math.min(...b);I>0&&e.min==null&&(I=0),x<0&&e.max==null&&(x=0);const k=w/V,C=(A-p)/(x-I||1),T=A-Math.abs(I*C);return b.map((P,_)=>{const B=Math.abs(C*P);return{x:S+_*k,y:T-B+ +(P<0)*B,height:B,value:P}})}const v=m(()=>{const b=[],y=f(c.value,d.value),S=y.length;for(let w=0;b.length<S;w++){const p=y[w];let A=e.labels[w];A||(A=typeof p=="object"?p.value:p),b.push({x:p.x,value:String(A)})}return b}),g=m(()=>f(c.value,d.value)),h=m(()=>(Math.abs(g.value[0].x-g.value[1].x)-r.value)/2);O(()=>{const b=e.gradient.slice().length?e.gradient.slice().reverse():[""];return s("svg",{display:"block"},[s("defs",null,[s("linearGradient",{id:l.value,gradientUnits:"userSpaceOnUse",x1:e.gradientDirection==="left"?"100%":"0",y1:e.gradientDirection==="top"?"100%":"0",x2:e.gradientDirection==="right"?"100%":"0",y2:e.gradientDirection==="bottom"?"100%":"0"},[b.map((y,S)=>s("stop",{offset:S/Math.max(b.length-1,1),"stop-color":y||"currentColor"},null))])]),s("clipPath",{id:`${l.value}-clip`},[g.value.map(y=>s("rect",{x:y.x+h.value,y:y.y,width:r.value,height:y.height,rx:typeof e.smooth=="number"?e.smooth:e.smooth?2:0,ry:typeof e.smooth=="number"?e.smooth:e.smooth?2:0},[e.autoDraw&&s(ae,null,[s("animate",{attributeName:"y",from:y.y+y.height,to:y.y,dur:`${o.value}ms`,fill:"freeze"},null),s("animate",{attributeName:"height",from:"0",to:y.height,dur:`${o.value}ms`,fill:"freeze"},null)])]))]),i.value&&s("g",{key:"labels",style:{textAnchor:"middle",dominantBaseline:"mathematical",fill:"currentColor"}},[v.value.map((y,S)=>s("text",{x:y.x+h.value+r.value/2,y:parseInt(e.height,10)-2+(parseInt(e.labelSize,10)||7*.75),"font-size":Number(e.labelSize)||7},[n.label?.({index:S,value:y.value})??y.value]))]),s("g",{"clip-path":`url(#${l.value}-clip)`,fill:`url(#${l.value})`},[s("rect",{x:0,y:0,width:Math.max(e.modelValue.length*r.value,Number(e.width)),height:e.height},null)])])})}});function Sb(e,t){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,a=arguments.length>3&&arguments[3]!==void 0?arguments[3]:75;if(e.length===0)return"";const l=e.shift(),o=e[e.length-1];return(n?`M${l.x} ${a-l.x+2} L${l.x} ${l.y}`:`M${l.x} ${l.y}`)+e.map((i,r)=>{const u=e[r+1],d=e[r-1]||l,c=u&&kb(u,i,d);if(!u||c)return`L${i.x} ${i.y}`;const f=Math.min(Qi(d,i),Qi(u,i)),g=f/2<t?f/2:t,h=es(d,i,g),b=es(u,i,g);return`L${h.x} ${h.y}S${i.x} ${i.y} ${b.x} ${b.y}`}).join("")+(n?`L${o.x} ${a-l.x+2} Z`:"")}function va(e){return parseInt(e,10)}function kb(e,t,n){return va(e.x+n.x)===va(2*t.x)&&va(e.y+n.y)===va(2*t.y)}function Qi(e,t){return Math.sqrt(Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2))}function es(e,t,n){const a={x:e.x-t.x,y:e.y-t.y},l=Math.sqrt(a.x*a.x+a.y*a.y),o={x:a.x/l,y:a.y/l};return{x:t.x+o.x*n,y:t.y+o.y*n}}const ac=L({fill:Boolean,...tc()},"VTrendline"),ts=M()({name:"VTrendline",props:ac(),setup(e,t){let{slots:n}=t;const a=je(),l=m(()=>e.id||`trendline-${a}`),o=m(()=>Number(e.autoDrawDuration)||(e.fill?500:2e3)),i=H(0),r=H(null);function u(y,S){const{minX:w,maxX:p,minY:A,maxY:V}=S,x=y.length,I=e.max!=null?Number(e.max):Math.max(...y),k=e.min!=null?Number(e.min):Math.min(...y),C=(p-w)/(x-1),T=(V-A)/(I-k||1);return y.map((P,_)=>({x:w+_*C,y:V-(P-k)*T,value:P}))}const d=m(()=>!!(e.showLabels||e.labels.length>0||n?.label)),c=m(()=>parseFloat(e.lineWidth)||4),f=m(()=>Number(e.width)),v=m(()=>{const y=Number(e.padding);return{minX:y,maxX:f.value-y,minY:y,maxY:parseInt(e.height,10)-y}}),g=m(()=>e.modelValue.map(y=>Re(y,e.itemValue,y))),h=m(()=>{const y=[],S=u(g.value,v.value),w=S.length;for(let p=0;y.length<w;p++){const A=S[p];let V=e.labels[p];V||(V=typeof A=="object"?A.value:A),y.push({x:A.x,value:String(V)})}return y});U(()=>e.modelValue,async()=>{if(await ke(),!e.autoDraw||!r.value)return;const y=r.value,S=y.getTotalLength();e.fill?(y.style.transformOrigin="bottom center",y.style.transition="none",y.style.transform="scaleY(0)",y.getBoundingClientRect(),y.style.transition=`transform ${o.value}ms ${e.autoDrawEasing}`,y.style.transform="scaleY(1)"):(y.style.strokeDasharray=`${S}`,y.style.strokeDashoffset=`${S}`,y.getBoundingClientRect(),y.style.transition=`stroke-dashoffset ${o.value}ms ${e.autoDrawEasing}`,y.style.strokeDashoffset="0"),i.value=S},{immediate:!0});function b(y){return Sb(u(g.value,v.value),e.smooth?8:Number(e.smooth),y,parseInt(e.height,10))}O(()=>{const y=e.gradient.slice().length?e.gradient.slice().reverse():[""];return s("svg",{display:"block","stroke-width":parseFloat(e.lineWidth)??4},[s("defs",null,[s("linearGradient",{id:l.value,gradientUnits:"userSpaceOnUse",x1:e.gradientDirection==="left"?"100%":"0",y1:e.gradientDirection==="top"?"100%":"0",x2:e.gradientDirection==="right"?"100%":"0",y2:e.gradientDirection==="bottom"?"100%":"0"},[y.map((S,w)=>s("stop",{offset:w/Math.max(y.length-1,1),"stop-color":S||"currentColor"},null))])]),d.value&&s("g",{key:"labels",style:{textAnchor:"middle",dominantBaseline:"mathematical",fill:"currentColor"}},[h.value.map((S,w)=>s("text",{x:S.x+c.value/2+c.value/2,y:parseInt(e.height,10)-4+(parseInt(e.labelSize,10)||7*.75),"font-size":Number(e.labelSize)||7},[n.label?.({index:w,value:S.value})??S.value]))]),s("path",{ref:r,d:b(e.fill),fill:e.fill?`url(#${l.value})`:"none",stroke:e.fill?"none":`url(#${l.value})`},null),e.fill&&s("path",{d:b(!1),fill:"none",stroke:e.color??e.gradient?.[0]},null)])})}}),Cb=L({type:{type:String,default:"trend"},...nc(),...ac()},"VSparkline"),xb=M()({name:"VSparkline",props:Cb(),setup(e,t){let{slots:n}=t;const{textColorClasses:a,textColorStyles:l}=Ge(D(e,"color")),o=m(()=>!!(e.showLabels||e.labels.length>0||n?.label)),i=m(()=>{let r=parseInt(e.height,10);return o.value&&(r+=parseInt(e.labelSize,10)*1.5),r});O(()=>{const r=e.type==="trend"?ts:Ji,u=e.type==="trend"?ts.filterProps(e):Ji.filterProps(e);return s(r,F({key:e.type,class:a.value,style:l.value,viewBox:`0 0 ${e.width} ${parseInt(i.value,10)}`},u),n)})}}),wb=L({...X(),...Nr({offset:8,minWidth:0,openDelay:0,closeDelay:100,location:"top center",transition:"scale-transition"})},"VSpeedDial"),Vb=M()({name:"VSpeedDial",props:wb(),emits:{"update:modelValue":e=>!0},setup(e,t){let{slots:n}=t;const a=Q(e,"modelValue"),l=H(),o=m(()=>{const[r,u="center"]=e.location?.split(" ")??[];return`${r} ${u}`}),i=m(()=>({[`v-speed-dial__content--${o.value.replace(" ","-")}`]:!0}));return O(()=>{const r=pn.filterProps(e);return s(pn,F(r,{modelValue:a.value,"onUpdate:modelValue":u=>a.value=u,class:e.class,style:e.style,contentClass:["v-speed-dial__content",i.value,e.contentClass],location:o.value,ref:l,transition:"fade-transition"}),{...n,default:u=>s(ve,{defaults:{VBtn:{size:"small"}}},{default:()=>[s(Xe,{appear:!0,group:!0,transition:e.transition},{default:()=>[n.default?.(u)]})]})})}),{}}}),Ko=Symbol.for("vuetify:v-stepper"),lc=L({color:String,disabled:{type:[Boolean,String],default:!1},prevText:{type:String,default:"$vuetify.stepper.prev"},nextText:{type:String,default:"$vuetify.stepper.next"}},"VStepperActions"),oc=M()({name:"VStepperActions",props:lc(),emits:{"click:prev":()=>!0,"click:next":()=>!0},setup(e,t){let{emit:n,slots:a}=t;const{t:l}=Ae();function o(){n("click:prev")}function i(){n("click:next")}return O(()=>{const r={onClick:o},u={onClick:i};return s("div",{class:"v-stepper-actions"},[s(ve,{defaults:{VBtn:{disabled:["prev",!0].includes(e.disabled),text:l(e.prevText),variant:"text"}}},{default:()=>[a.prev?.({props:r})??s(he,r,null)]}),s(ve,{defaults:{VBtn:{color:e.color,disabled:["next",!0].includes(e.disabled),text:l(e.nextText),variant:"tonal"}}},{default:()=>[a.next?.({props:u})??s(he,u,null)]})])}),{}}}),ic=Pt("v-stepper-header"),Pb=L({color:String,title:String,subtitle:String,complete:Boolean,completeIcon:{type:String,default:"$complete"},editable:Boolean,editIcon:{type:String,default:"$edit"},error:Boolean,errorIcon:{type:String,default:"$error"},icon:String,ripple:{type:[Boolean,Object],default:!0},rules:{type:Array,default:()=>[]}},"StepperItem"),Ib=L({...Pb(),...fn()},"VStepperItem"),sc=M()({name:"VStepperItem",directives:{Ripple:Nt},props:Ib(),emits:{"group:selected":e=>!0},setup(e,t){let{slots:n}=t;const a=mn(e,Ko,!0),l=m(()=>a?.value.value??e.value),o=m(()=>e.rules.every(v=>v()===!0)),i=m(()=>!e.disabled&&e.editable),r=m(()=>!e.disabled&&e.editable),u=m(()=>e.error||!o.value),d=m(()=>e.complete||e.rules.length>0&&o.value),c=m(()=>u.value?e.errorIcon:d.value?e.completeIcon:a.isSelected.value&&e.editable?e.editIcon:e.icon),f=m(()=>({canEdit:r.value,hasError:u.value,hasCompleted:d.value,title:e.title,subtitle:e.subtitle,step:l.value,value:e.value}));return O(()=>{const v=(!a||a.isSelected.value||d.value||r.value)&&!u.value&&!e.disabled,g=!!(e.title!=null||n.title),h=!!(e.subtitle!=null||n.subtitle);function b(){a?.toggle()}return Ie(s("button",{class:["v-stepper-item",{"v-stepper-item--complete":d.value,"v-stepper-item--disabled":e.disabled,"v-stepper-item--error":u.value},a?.selectedClass.value],disabled:!e.editable,onClick:b},[i.value&&Rt(!0,"v-stepper-item"),s(ft,{key:"stepper-avatar",class:"v-stepper-item__avatar",color:v?e.color:void 0,size:24},{default:()=>[n.icon?.(f.value)??(c.value?s(ye,{icon:c.value},null):l.value)]}),s("div",{class:"v-stepper-item__content"},[g&&s("div",{key:"title",class:"v-stepper-item__title"},[n.title?.(f.value)??e.title]),h&&s("div",{key:"subtitle",class:"v-stepper-item__subtitle"},[n.subtitle?.(f.value)??e.subtitle]),n.default?.(f.value)])]),[[st("ripple"),e.ripple&&e.editable,null]])}),{}}}),pb=L({...Ee(Ya(),["continuous","nextIcon","prevIcon","showArrows","touch","mandatory"])},"VStepperWindow"),rc=M()({name:"VStepperWindow",props:pb(),emits:{"update:modelValue":e=>!0},setup(e,t){let{slots:n}=t;const a=ge(Ko,null),l=Q(e,"modelValue"),o=m({get(){return l.value!=null||!a?l.value:a.items.value.find(i=>a.selected.value.includes(i.id))?.value},set(i){l.value=i}});return O(()=>{const i=Jt.filterProps(e);return s(Jt,F({_as:"VStepperWindow"},i,{modelValue:o.value,"onUpdate:modelValue":r=>o.value=r,class:["v-stepper-window",e.class],style:e.style,mandatory:!1,touch:!1}),n)}),{}}}),_b=L({...Ga()},"VStepperWindowItem"),uc=M()({name:"VStepperWindowItem",props:_b(),setup(e,t){let{slots:n}=t;return O(()=>{const a=Qt.filterProps(e);return s(Qt,F({_as:"VStepperWindowItem"},a,{class:["v-stepper-window-item",e.class],style:e.style}),n)}),{}}}),Ab=L({altLabels:Boolean,bgColor:String,completeIcon:String,editIcon:String,editable:Boolean,errorIcon:String,hideActions:Boolean,items:{type:Array,default:()=>[]},itemTitle:{type:String,default:"title"},itemValue:{type:String,default:"value"},nonLinear:Boolean,flat:Boolean,...sn()},"Stepper"),Lb=L({...Ab(),...vn({mandatory:"force",selectedClass:"v-stepper-item--selected"}),...Ua(),...Va(lc(),["prevText","nextText"])},"VStepper"),Tb=M()({name:"VStepper",props:Lb(),emits:{"update:modelValue":e=>!0},setup(e,t){let{slots:n}=t;const{items:a,next:l,prev:o,selected:i}=Ht(e,Ko),{displayClasses:r,mobile:u}=ut(e),{completeIcon:d,editIcon:c,errorIcon:f,color:v,editable:g,prevText:h,nextText:b}=ln(e),y=m(()=>e.items.map((p,A)=>{const V=Re(p,e.itemTitle,p),x=Re(p,e.itemValue,A+1);return{title:V,value:x,raw:p}})),S=m(()=>a.value.findIndex(p=>i.value.includes(p.id))),w=m(()=>e.disabled?e.disabled:S.value===0?"prev":S.value===a.value.length-1?"next":!1);return pe({VStepperItem:{editable:g,errorIcon:f,completeIcon:d,editIcon:c,prevText:h,nextText:b},VStepperActions:{color:v,disabled:w,prevText:h,nextText:b}}),O(()=>{const p=en.filterProps(e),A=!!(n.header||e.items.length),V=e.items.length>0,x=!e.hideActions&&!!(V||n.actions);return s(en,F(p,{color:e.bgColor,class:["v-stepper",{"v-stepper--alt-labels":e.altLabels,"v-stepper--flat":e.flat,"v-stepper--non-linear":e.nonLinear,"v-stepper--mobile":u.value},r.value,e.class],style:e.style}),{default:()=>[A&&s(ic,{key:"stepper-header"},{default:()=>[y.value.map((I,k)=>{let{raw:C,...T}=I;return s(ae,null,[!!k&&s(Qn,null,null),s(sc,T,{default:n[`header-item.${T.value}`]??n.header,icon:n.icon,title:n.title,subtitle:n.subtitle})])})]}),V&&s(rc,{key:"stepper-window"},{default:()=>[y.value.map(I=>s(uc,{value:I.value},{default:()=>n[`item.${I.value}`]?.(I)??n.item?.(I)}))]}),n.default?.({prev:o,next:l}),x&&(n.actions?.({next:l,prev:o})??s(oc,{key:"stepper-actions","onClick:prev":o,"onClick:next":l},n))]})}),{prev:o,next:l}}}),Bb=L({indeterminate:Boolean,inset:Boolean,flat:Boolean,loading:{type:[Boolean,String],default:!1},...Bt(),...Fa()},"VSwitch"),Db=M()({name:"VSwitch",inheritAttrs:!1,props:Bb(),emits:{"update:focused":e=>!0,"update:modelValue":e=>!0,"update:indeterminate":e=>!0},setup(e,t){let{attrs:n,slots:a}=t;const l=Q(e,"indeterminate"),o=Q(e,"modelValue"),{loaderClasses:i}=na(e),{isFocused:r,focus:u,blur:d}=Tt(e),c=H(),f=Se&&window.matchMedia("(forced-colors: active)").matches,v=m(()=>typeof e.loading=="string"&&e.loading!==""?e.loading:e.color),g=je(),h=m(()=>e.id||`switch-${g}`);function b(){l.value&&(l.value=!1)}function y(S){S.stopPropagation(),S.preventDefault(),c.value?.input?.click()}return O(()=>{const[S,w]=$t(n),p=Ue.filterProps(e),A=Ot.filterProps(e);return s(Ue,F({class:["v-switch",{"v-switch--flat":e.flat},{"v-switch--inset":e.inset},{"v-switch--indeterminate":l.value},i.value,e.class]},S,p,{modelValue:o.value,"onUpdate:modelValue":V=>o.value=V,id:h.value,focused:r.value,style:e.style}),{...a,default:V=>{let{id:x,messagesId:I,isDisabled:k,isReadonly:C,isValid:T}=V;const P={model:o,isValid:T};return s(Ot,F({ref:c},A,{modelValue:o.value,"onUpdate:modelValue":[_=>o.value=_,b],id:x.value,"aria-describedby":I.value,type:"checkbox","aria-checked":l.value?"mixed":void 0,disabled:k.value,readonly:C.value,onFocus:u,onBlur:d},w),{...a,default:_=>{let{backgroundColorClasses:B,backgroundColorStyles:N}=_;return s("div",{class:["v-switch__track",f?void 0:B.value],style:N.value,onClick:y},[a["track-true"]&&s("div",{key:"prepend",class:"v-switch__track-true"},[a["track-true"](P)]),a["track-false"]&&s("div",{key:"append",class:"v-switch__track-false"},[a["track-false"](P)])])},input:_=>{let{inputNode:B,icon:N,backgroundColorClasses:z,backgroundColorStyles:j}=_;return s(ae,null,[B,s("div",{class:["v-switch__thumb",{"v-switch__thumb--filled":N||e.loading},e.inset||f?void 0:z.value],style:e.inset?void 0:j.value},[a.thumb?s(ve,{defaults:{VIcon:{icon:N,size:"x-small"}}},{default:()=>[a.thumb({...P,icon:N})]}):s(go,null,{default:()=>[e.loading?s(aa,{name:"v-switch",active:!0,color:T.value===!1?void 0:v.value},{default:ee=>a.loader?a.loader(ee):s(_n,{active:ee.isActive,color:ee.color,indeterminate:!0,size:"16",width:"2"},null)}):N&&s(ye,{key:String(N),icon:N,size:"x-small"},null)]})])])}})}})}),{}}}),Mb=L({color:String,height:[Number,String],window:Boolean,...X(),...Ne(),...rn(),...Ve(),...re(),...me()},"VSystemBar"),Eb=M()({name:"VSystemBar",props:Mb(),setup(e,t){let{slots:n}=t;const{themeClasses:a}=be(e),{backgroundColorClasses:l,backgroundColorStyles:o}=Ce(D(e,"color")),{elevationClasses:i}=Ye(e),{roundedClasses:r}=Le(e),{ssrBootStyles:u}=cn(),d=m(()=>e.height??(e.window?32:24)),{layoutItemStyles:c}=un({id:e.name,order:m(()=>parseInt(e.order,10)),position:Y("top"),layoutSize:d,elementSize:d,active:m(()=>!0),absolute:D(e,"absolute")});return O(()=>s(e.tag,{class:["v-system-bar",{"v-system-bar--window":e.window},a.value,l.value,i.value,r.value,e.class],style:[o.value,c.value,u.value,e.style]},n)),{}}}),qo=Symbol.for("vuetify:v-tabs"),Fb=L({fixed:Boolean,sliderColor:String,hideSlider:Boolean,direction:{type:String,default:"horizontal"},...Ee(ja({selectedClass:"v-tab--selected",variant:"text"}),["active","block","flat","location","position","symbol"])},"VTab"),cc=M()({name:"VTab",props:Fb(),setup(e,t){let{slots:n,attrs:a}=t;const{textColorClasses:l,textColorStyles:o}=Ge(e,"sliderColor"),i=H(),r=H(),u=m(()=>e.direction==="horizontal"),d=m(()=>i.value?.group?.isSelected.value??!1);function c(f){let{value:v}=f;if(v){const g=i.value?.$el.parentElement?.querySelector(".v-tab--selected .v-tab__slider"),h=r.value;if(!g||!h)return;const b=getComputedStyle(g).color,y=g.getBoundingClientRect(),S=h.getBoundingClientRect(),w=u.value?"x":"y",p=u.value?"X":"Y",A=u.value?"right":"bottom",V=u.value?"width":"height",x=y[w],I=S[w],k=x>I?y[A]-S[A]:y[w]-S[w],C=Math.sign(k)>0?u.value?"right":"bottom":Math.sign(k)<0?u.value?"left":"top":"center",P=(Math.abs(k)+(Math.sign(k)<0?y[V]:S[V]))/Math.max(y[V],S[V])||0,_=y[V]/S[V]||0,B=1.5;Yt(h,{backgroundColor:[b,"currentcolor"],transform:[`translate${p}(${k}px) scale${p}(${_})`,`translate${p}(${k/B}px) scale${p}(${(P-1)/B+1})`,"none"],transformOrigin:Array(3).fill(C)},{duration:225,easing:Nn})}}return O(()=>{const f=he.filterProps(e);return s(he,F({symbol:qo,ref:i,class:["v-tab",e.class],style:e.style,tabindex:d.value?0:-1,role:"tab","aria-selected":String(d.value),active:!1},f,a,{block:e.fixed,maxWidth:e.fixed?300:void 0,"onGroup:selected":c}),{...n,default:()=>s(ae,null,[n.default?.()??e.text,!e.hideSlider&&s("div",{ref:r,class:["v-tab__slider",l.value],style:o.value},null)])})}),gt({},i)}}),Ob=L({...Ee(Ya(),["continuous","nextIcon","prevIcon","showArrows","touch","mandatory"])},"VTabsWindow"),dc=M()({name:"VTabsWindow",props:Ob(),emits:{"update:modelValue":e=>!0},setup(e,t){let{slots:n}=t;const a=ge(qo,null),l=Q(e,"modelValue"),o=m({get(){return l.value!=null||!a?l.value:a.items.value.find(i=>a.selected.value.includes(i.id))?.value},set(i){l.value=i}});return O(()=>{const i=Jt.filterProps(e);return s(Jt,F({_as:"VTabsWindow"},i,{modelValue:o.value,"onUpdate:modelValue":r=>o.value=r,class:["v-tabs-window",e.class],style:e.style,mandatory:!1,touch:!1}),n)}),{}}}),$b=L({...Ga()},"VTabsWindowItem"),vc=M()({name:"VTabsWindowItem",props:$b(),setup(e,t){let{slots:n}=t;return O(()=>{const a=Qt.filterProps(e);return s(Qt,F({_as:"VTabsWindowItem"},a,{class:["v-tabs-window-item",e.class],style:e.style}),n)}),{}}});function Rb(e){return e?e.map(t=>ss(t)?t:{text:t,value:t}):[]}const Nb=L({alignTabs:{type:String,default:"start"},color:String,fixedTabs:Boolean,items:{type:Array,default:()=>[]},stacked:Boolean,bgColor:String,grow:Boolean,height:{type:[Number,String],default:void 0},hideSlider:Boolean,sliderColor:String,...xo({mandatory:"force",selectedClass:"v-tab-item--selected"}),...ze(),...re()},"VTabs"),Hb=M()({name:"VTabs",props:Nb(),emits:{"update:modelValue":e=>!0},setup(e,t){let{attrs:n,slots:a}=t;const l=Q(e,"modelValue"),o=m(()=>Rb(e.items)),{densityClasses:i}=et(e),{backgroundColorClasses:r,backgroundColorStyles:u}=Ce(D(e,"bgColor")),{scopeId:d}=gn();return pe({VTab:{color:D(e,"color"),direction:D(e,"direction"),stacked:D(e,"stacked"),fixed:D(e,"fixedTabs"),sliderColor:D(e,"sliderColor"),hideSlider:D(e,"hideSlider")}}),O(()=>{const c=Kn.filterProps(e),f=!!(a.window||e.items.length>0);return s(ae,null,[s(Kn,F(c,{modelValue:l.value,"onUpdate:modelValue":v=>l.value=v,class:["v-tabs",`v-tabs--${e.direction}`,`v-tabs--align-tabs-${e.alignTabs}`,{"v-tabs--fixed-tabs":e.fixedTabs,"v-tabs--grow":e.grow,"v-tabs--stacked":e.stacked},i.value,r.value,e.class],style:[{"--v-tabs-height":K(e.height)},u.value,e.style],role:"tablist",symbol:qo},d,n),{default:()=>[a.default?.()??o.value.map(v=>a.tab?.({item:v})??s(cc,F(v,{key:v.text,value:v.value}),{default:a[`tab.${v.value}`]?()=>a[`tab.${v.value}`]?.({item:v}):void 0}))]}),f&&s(dc,F({modelValue:l.value,"onUpdate:modelValue":v=>l.value=v,key:"tabs-window"},d),{default:()=>[o.value.map(v=>a.item?.({item:v})??s(vc,{value:v.value},{default:()=>a[`item.${v.value}`]?.({item:v})})),a.window?.()]})])}),{}}}),zb=L({autoGrow:Boolean,autofocus:Boolean,counter:[Boolean,Number,String],counterValue:Function,prefix:String,placeholder:String,persistentPlaceholder:Boolean,persistentCounter:Boolean,noResize:Boolean,rows:{type:[Number,String],default:5,validator:e=>!isNaN(parseFloat(e))},maxRows:{type:[Number,String],validator:e=>!isNaN(parseFloat(e))},suffix:String,modelModifiers:Object,...Bt(),...la()},"VTextarea"),Wb=M()({name:"VTextarea",directives:{Intersect:Ma},inheritAttrs:!1,props:zb(),emits:{"click:control":e=>!0,"mousedown:control":e=>!0,"update:focused":e=>!0,"update:modelValue":e=>!0},setup(e,t){let{attrs:n,emit:a,slots:l}=t;const o=Q(e,"modelValue"),{isFocused:i,focus:r,blur:u}=Tt(e),d=m(()=>typeof e.counterValue=="function"?e.counterValue(o.value):(o.value||"").toString().length),c=m(()=>{if(n.maxlength)return n.maxlength;if(!(!e.counter||typeof e.counter!="number"&&typeof e.counter!="string"))return e.counter});function f(P,_){!e.autofocus||!P||_[0].target?.focus?.()}const v=H(),g=H(),h=Y(""),b=H(),y=m(()=>e.persistentPlaceholder||i.value||e.active);function S(){b.value!==document.activeElement&&b.value?.focus(),i.value||r()}function w(P){S(),a("click:control",P)}function p(P){a("mousedown:control",P)}function A(P){P.stopPropagation(),S(),ke(()=>{o.value="",no(e["onClick:clear"],P)})}function V(P){const _=P.target;if(o.value=_.value,e.modelModifiers?.trim){const B=[_.selectionStart,_.selectionEnd];ke(()=>{_.selectionStart=B[0],_.selectionEnd=B[1]})}}const x=H(),I=H(+e.rows),k=m(()=>["plain","underlined"].includes(e.variant));Te(()=>{e.autoGrow||(I.value=+e.rows)});function C(){e.autoGrow&&ke(()=>{if(!x.value||!g.value)return;const P=getComputedStyle(x.value),_=getComputedStyle(g.value.$el),B=parseFloat(P.getPropertyValue("--v-field-padding-top"))+parseFloat(P.getPropertyValue("--v-input-padding-top"))+parseFloat(P.getPropertyValue("--v-field-padding-bottom")),N=x.value.scrollHeight,z=parseFloat(P.lineHeight),j=Math.max(parseFloat(e.rows)*z+B,parseFloat(_.getPropertyValue("--v-input-control-height"))),ee=parseFloat(e.maxRows)*z+B||1/0,Z=Be(N??0,j,ee);I.value=Math.floor((Z-B)/z),h.value=K(Z)})}Qe(C),U(o,C),U(()=>e.rows,C),U(()=>e.maxRows,C),U(()=>e.density,C);let T;return U(x,P=>{P?(T=new ResizeObserver(C),T.observe(x.value)):T?.disconnect()}),Je(()=>{T?.disconnect()}),O(()=>{const P=!!(l.counter||e.counter||e.counterValue),_=!!(P||l.details),[B,N]=$t(n),{modelValue:z,...j}=Ue.filterProps(e),ee=po(e);return s(Ue,F({ref:v,modelValue:o.value,"onUpdate:modelValue":Z=>o.value=Z,class:["v-textarea v-text-field",{"v-textarea--prefixed":e.prefix,"v-textarea--suffixed":e.suffix,"v-text-field--prefixed":e.prefix,"v-text-field--suffixed":e.suffix,"v-textarea--auto-grow":e.autoGrow,"v-textarea--no-resize":e.noResize||e.autoGrow,"v-input--plain-underlined":k.value},e.class],style:e.style},B,j,{centerAffix:I.value===1&&!k.value,focused:i.value}),{...l,default:Z=>{let{id:$,isDisabled:E,isDirty:R,isReadonly:q,isValid:ue}=Z;return s(Mn,F({ref:g,style:{"--v-textarea-control-height":h.value},onClick:w,onMousedown:p,"onClick:clear":A,"onClick:prependInner":e["onClick:prependInner"],"onClick:appendInner":e["onClick:appendInner"]},ee,{id:$.value,active:y.value||R.value,centerAffix:I.value===1&&!k.value,dirty:R.value||e.dirty,disabled:E.value,focused:i.value,error:ue.value===!1}),{...l,default:le=>{let{props:{class:oe,...W}}=le;return s(ae,null,[e.prefix&&s("span",{class:"v-text-field__prefix"},[e.prefix]),Ie(s("textarea",F({ref:b,class:oe,value:o.value,onInput:V,autofocus:e.autofocus,readonly:q.value,disabled:E.value,placeholder:e.placeholder,rows:e.rows,name:e.name,onFocus:S,onBlur:u},W,N),null),[[st("intersect"),{handler:f},null,{once:!0}]]),e.autoGrow&&Ie(s("textarea",{class:[oe,"v-textarea__sizer"],id:`${W.id}-sizer`,"onUpdate:modelValue":se=>o.value=se,ref:x,readonly:!0,"aria-hidden":"true"},null),[[_c,o.value]]),e.suffix&&s("span",{class:"v-text-field__suffix"},[e.suffix])])}})},details:_?Z=>s(ae,null,[l.details?.(Z),P&&s(ae,null,[s("span",null,null),s($a,{active:e.persistentCounter||i.value,value:d.value,max:c.value,disabled:e.disabled},l.counter)])]):void 0})}),gt({},v,g,b)}}),jb=L({withBackground:Boolean,...X(),...me(),...re()},"VThemeProvider"),Yb=M()({name:"VThemeProvider",props:jb(),setup(e,t){let{slots:n}=t;const{themeClasses:a}=be(e);return()=>e.withBackground?s(e.tag,{class:["v-theme-provider",a.value,e.class],style:e.style},{default:()=>[n.default?.()]}):n.default?.()}}),Gb=L({dotColor:String,fillDot:Boolean,hideDot:Boolean,icon:ie,iconColor:String,lineColor:String,...X(),...Ve(),...It(),...Ne()},"VTimelineDivider"),Ub=M()({name:"VTimelineDivider",props:Gb(),setup(e,t){let{slots:n}=t;const{sizeClasses:a,sizeStyles:l}=Tn(e,"v-timeline-divider__dot"),{backgroundColorStyles:o,backgroundColorClasses:i}=Ce(D(e,"dotColor")),{roundedClasses:r}=Le(e,"v-timeline-divider__dot"),{elevationClasses:u}=Ye(e),{backgroundColorClasses:d,backgroundColorStyles:c}=Ce(D(e,"lineColor"));return O(()=>s("div",{class:["v-timeline-divider",{"v-timeline-divider--fill-dot":e.fillDot},e.class],style:e.style},[s("div",{class:["v-timeline-divider__before",d.value],style:c.value},null),!e.hideDot&&s("div",{key:"dot",class:["v-timeline-divider__dot",u.value,r.value,a.value],style:l.value},[s("div",{class:["v-timeline-divider__inner-dot",i.value,r.value],style:o.value},[n.default?s(ve,{key:"icon-defaults",disabled:!e.icon,defaults:{VIcon:{color:e.iconColor,icon:e.icon,size:e.size}}},n.default):s(ye,{key:"icon",color:e.iconColor,icon:e.icon,size:e.size},null)])]),s("div",{class:["v-timeline-divider__after",d.value],style:c.value},null)])),{}}}),fc=L({density:String,dotColor:String,fillDot:Boolean,hideDot:Boolean,hideOpposite:{type:Boolean,default:void 0},icon:ie,iconColor:String,lineInset:[Number,String],...X(),...Oe(),...Ne(),...Ve(),...It(),...re()},"VTimelineItem"),Kb=M()({name:"VTimelineItem",props:fc(),setup(e,t){let{slots:n}=t;const{dimensionStyles:a}=$e(e),l=Y(0),o=H();return U(o,i=>{i&&(l.value=i.$el.querySelector(".v-timeline-divider__dot")?.getBoundingClientRect().width??0)},{flush:"post"}),O(()=>s("div",{class:["v-timeline-item",{"v-timeline-item--fill-dot":e.fillDot},e.class],style:[{"--v-timeline-dot-size":K(l.value),"--v-timeline-line-inset":e.lineInset?`calc(var(--v-timeline-dot-size) / 2 + ${K(e.lineInset)})`:K(0)},e.style]},[s("div",{class:"v-timeline-item__body",style:a.value},[n.default?.()]),s(Ub,{ref:o,hideDot:e.hideDot,icon:e.icon,iconColor:e.iconColor,size:e.size,elevation:e.elevation,dotColor:e.dotColor,fillDot:e.fillDot,rounded:e.rounded},{default:n.icon}),e.density!=="compact"&&s("div",{class:"v-timeline-item__opposite"},[!e.hideOpposite&&n.opposite?.()])])),{}}}),qb=L({align:{type:String,default:"center",validator:e=>["center","start"].includes(e)},direction:{type:String,default:"vertical",validator:e=>["vertical","horizontal"].includes(e)},justify:{type:String,default:"auto",validator:e=>["auto","center"].includes(e)},side:{type:String,validator:e=>e==null||["start","end"].includes(e)},lineThickness:{type:[String,Number],default:2},lineColor:String,truncateLine:{type:String,validator:e=>["start","end","both"].includes(e)},...Va(fc({lineInset:0}),["dotColor","fillDot","hideOpposite","iconColor","lineInset","size"]),...X(),...ze(),...re(),...me()},"VTimeline"),Xb=M()({name:"VTimeline",props:qb(),setup(e,t){let{slots:n}=t;const{themeClasses:a}=be(e),{densityClasses:l}=et(e),{rtlClasses:o}=Fe();pe({VTimelineDivider:{lineColor:D(e,"lineColor")},VTimelineItem:{density:D(e,"density"),dotColor:D(e,"dotColor"),fillDot:D(e,"fillDot"),hideOpposite:D(e,"hideOpposite"),iconColor:D(e,"iconColor"),lineColor:D(e,"lineColor"),lineInset:D(e,"lineInset"),size:D(e,"size")}});const i=m(()=>{const u=e.side?e.side:e.density!=="default"?"end":null;return u&&`v-timeline--side-${u}`}),r=m(()=>{const u=["v-timeline--truncate-line-start","v-timeline--truncate-line-end"];switch(e.truncateLine){case"both":return u;case"start":return u[0];case"end":return u[1];default:return null}});return O(()=>s(e.tag,{class:["v-timeline",`v-timeline--${e.direction}`,`v-timeline--align-${e.align}`,`v-timeline--justify-${e.justify}`,r.value,{"v-timeline--inset-line":!!e.lineInset},a.value,l.value,i.value,o.value,e.class],style:[{"--v-timeline-line-thickness":K(e.lineThickness)},e.style]},n)),{}}}),Zb=L({...X(),...mt({variant:"text"})},"VToolbarItems"),Jb=M()({name:"VToolbarItems",props:Zb(),setup(e,t){let{slots:n}=t;return pe({VBtn:{color:D(e,"color"),height:"inherit",variant:D(e,"variant")}}),O(()=>s("div",{class:["v-toolbar-items",e.class],style:e.style},[n.default?.()])),{}}}),Qb=M()({name:"VValidation",props:Ir(),emits:{"update:modelValue":e=>!0},setup(e,t){let{slots:n}=t;const a=pr(e,"validation");return()=>n.default?.(a)}}),n1=Object.freeze(Object.defineProperty({__proto__:null,VAlert:Ym,VAlertTitle:Jr,VApp:Mm,VAppBar:Rm,VAppBarNavIcon:Hm,VAppBarTitle:zm,VAutocomplete:Xm,VAvatar:ft,VBadge:Jm,VBanner:tg,VBannerActions:Qr,VBannerText:eu,VBottomNavigation:ag,VBottomSheet:og,VBreadcrumbs:ug,VBreadcrumbsDivider:nu,VBreadcrumbsItem:au,VBtn:he,VBtnGroup:Bl,VBtnToggle:xm,VCard:Lm,VCardActions:Yr,VCardItem:Kr,VCardSubtitle:Gr,VCardText:qr,VCardTitle:Ur,VCarousel:bg,VCarouselItem:kg,VCheckbox:Mf,VCheckboxBtn:Lt,VChip:Dn,VChipGroup:Hf,VClassIcon:co,VCode:Cg,VCol:Xh,VColorPicker:uh,VCombobox:vh,VComponentIcon:wl,VConfirmEdit:mh,VContainer:Gh,VCounter:$a,VDataIterator:Vh,VDataTable:Nh,VDataTableFooter:qn,VDataTableHeaders:tn,VDataTableRow:Wo,VDataTableRows:nn,VDataTableServer:jh,VDataTableVirtual:zh,VDatePicker:uy,VDatePickerControls:zl,VDatePickerHeader:Wl,VDatePickerMonth:jl,VDatePickerMonths:Yl,VDatePickerYears:Gl,VDefaultsProvider:ve,VDialog:Ml,VDialogBottomTransition:Bv,VDialogTopTransition:Dv,VDialogTransition:Ba,VDivider:Qn,VEmptyState:dy,VExpandTransition:Da,VExpandXTransition:yo,VExpansionPanel:vy,VExpansionPanelText:Ul,VExpansionPanelTitle:Kl,VExpansionPanels:gy,VFab:yy,VFabTransition:Tv,VFadeTransition:Wn,VField:Mn,VFieldLabel:$n,VFileInput:Sy,VFooter:Cy,VForm:wy,VHover:Py,VIcon:ye,VImg:_t,VInfiniteScroll:py,VInput:Ue,VItem:Ly,VItemGroup:Ay,VKbd:Ty,VLabel:Bn,VLayout:Dy,VLayoutItem:Ey,VLazy:Oy,VLigatureIcon:kv,VList:Ea,VListGroup:Pl,VListImg:xf,VListItem:At,VListItemAction:Vf,VListItemMedia:If,VListItemSubtitle:tr,VListItemTitle:nr,VListSubheader:gr,VLocaleProvider:Ry,VMain:Hy,VMenu:pn,VMessages:Vr,VNavigationDrawer:Xy,VNoSsr:Zy,VOtpInput:Qy,VOverlay:xt,VPagination:Nl,VParallax:nb,VProgressCircular:_n,VProgressLinear:Na,VRadio:lb,VRadioGroup:ib,VRangeSlider:rb,VRating:cb,VResponsive:Il,VRow:ay,VScaleTransition:go,VScrollXReverseTransition:Ev,VScrollXTransition:Mv,VScrollYReverseTransition:Ov,VScrollYTransition:Fv,VSelect:Lo,VSelectionControl:Ot,VSelectionControlGroup:Cr,VSheet:en,VSkeletonLoader:mb,VSlideGroup:Kn,VSlideGroupItem:gb,VSlideXReverseTransition:Rv,VSlideXTransition:$v,VSlideYReverseTransition:Nv,VSlideYTransition:ho,VSlider:Rl,VSnackbar:bb,VSpacer:Yu,VSparkline:xb,VSpeedDial:Vb,VStepper:Tb,VStepperActions:oc,VStepperHeader:ic,VStepperItem:sc,VStepperWindow:rc,VStepperWindowItem:uc,VSvgIcon:Ta,VSwitch:Db,VSystemBar:Eb,VTab:cc,VTable:an,VTabs:Hb,VTabsWindow:dc,VTabsWindowItem:vc,VTextField:Zt,VTextarea:Wb,VThemeProvider:Yb,VTimeline:Xb,VTimelineItem:Kb,VToolbar:Dl,VToolbarItems:Jb,VToolbarTitle:Bo,VTooltip:Bm,VValidation:Qb,VVirtualScroll:Wa,VWindow:Jt,VWindowItem:Qt},Symbol.toStringTag,{value:"Module"})),a1={collapse:"svg:M7.41,15.41L12,10.83L16.59,15.41L18,14L12,8L6,14L7.41,15.41Z",complete:"svg:M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z",cancel:"svg:M12,2C17.53,2 22,6.47 22,12C22,17.53 17.53,22 12,22C6.47,22 2,17.53 2,12C2,6.47 6.47,2 12,2M15.59,7L12,10.59L8.41,7L7,8.41L10.59,12L7,15.59L8.41,17L12,13.41L15.59,17L17,15.59L13.41,12L17,8.41L15.59,7Z",close:"svg:M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z",delete:"svg:M12,2C17.53,2 22,6.47 22,12C22,17.53 17.53,22 12,22C6.47,22 2,17.53 2,12C2,6.47 6.47,2 12,2M15.59,7L12,10.59L8.41,7L7,8.41L10.59,12L7,15.59L8.41,17L12,13.41L15.59,17L17,15.59L13.41,12L17,8.41L15.59,7Z",clear:"svg:M12,2C17.53,2 22,6.47 22,12C22,17.53 17.53,22 12,22C6.47,22 2,17.53 2,12C2,6.47 6.47,2 12,2M15.59,7L12,10.59L8.41,7L7,8.41L10.59,12L7,15.59L8.41,17L12,13.41L15.59,17L17,15.59L13.41,12L17,8.41L15.59,7Z",success:"svg:M12,2C17.52,2 22,6.48 22,12C22,17.52 17.52,22 12,22C6.48,22 2,17.52 2,12C2,6.48 6.48,2 12,2M11,16.5L18,9.5L16.59,8.09L11,13.67L7.91,10.59L6.5,12L11,16.5Z",info:"svg:M13,9H11V7H13M13,17H11V11H13M12,2C6.48,2 2,6.48 2,12C2,17.52 6.48,22 12,22C17.52,22 22,17.52 22,12C22,6.48 17.52,2 12,2Z",warning:"svg:M13,13H11V7H13M13,17H11V15H13M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z",error:"svg:M12,2C17.53,2 22,6.47 22,12C22,17.53 17.53,22 12,22C6.47,22 2,17.53 2,12C2,6.47 6.47,2 12,2M15.59,7L12,10.59L8.41,7L7,8.41L10.59,12L7,15.59L8.41,17L12,13.41L15.59,17L17,15.59L13.41,12L17,8.41L15.59,7Z",prev:"svg:M15.41,16.58L10.83,12L15.41,7.41L14,6L8,12L14,18L15.41,16.58Z",next:"svg:M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z",checkboxOn:"svg:M10,17L5,12L6.41,10.58L10,14.17L17.59,6.58L19,8M19,3H5C3.89,3 3,3.89 3,5V19C3,20.1 3.9,21 5,21H19C20.1,21 21,20.1 21,19V5C21,3.89 20.1,3 19,3Z",checkboxOff:"svg:M19,3H5C3.89,3 3,3.89 3,5V19C3,20.1 3.9,21 5,21H19C20.1,21 21,20.1 21,19V5C21,3.89 20.1,3 19,3M19,5V19H5V5H19Z",checkboxIndeterminate:"svg:M17,13H7V11H17M19,3H5C3.89,3 3,3.89 3,5V19C3,20.1 3.9,21 5,21H19C20.1,21 21,20.1 21,19V5C21,3.89 20.1,3 19,3Z",delimiter:"svg:M12,2C6.48,2 2,6.48 2,12C2,17.52 6.48,22 12,22C17.52,22 22,17.52 22,12C22,6.48 17.52,2 12,2Z",sortAsc:"svg:M13,20H11V8L5.5,13.5L4.08,12.08L12,4.16L19.92,12.08L18.5,13.5L13,8V20Z",sortDesc:"svg:M11,4H13V16L18.5,10.5L19.92,11.92L12,19.84L4.08,11.92L5.5,10.5L11,16V4Z",expand:"svg:M7.41,8.58L12,13.17L16.59,8.58L18,10L12,16L6,10L7.41,8.58Z",menu:"svg:M3,6H21V8H3V6M3,11H21V13H3V11M3,16H21V18H3V16Z",subgroup:"svg:M7,10L12,15L17,10H7Z",dropdown:"svg:M7,10L12,15L17,10H7Z",radioOn:"svg:M12,20C7.58,20 4,16.42 4,12C4,7.58 7.58,4 12,4C16.42,4 20,7.58 20,12C20,16.42 16.42,20 12,20M12,2C6.48,2 2,6.48 2,12C2,17.52 6.48,22 12,22C17.52,22 22,17.52 22,12C22,6.48 17.52,2 12,2M12,7C9.24,7 7,9.24 7,12C7,14.76 9.24,17 12,17C14.76,17 17,14.76 17,12C17,9.24 14.76,7 12,7Z",radioOff:"svg:M12,20C7.58,20 4,16.42 4,12C4,7.58 7.58,4 12,4C16.42,4 20,7.58 20,12C20,16.42 16.42,20 12,20M12,2C6.48,2 2,6.48 2,12C2,17.52 6.48,22 12,22C17.52,22 22,17.52 22,12C22,6.48 17.52,2 12,2Z",edit:"svg:M20.71,7.04C21.1,6.65 21.1,6 20.71,5.63L18.37,3.29C18,2.9 17.35,2.9 16.96,3.29L15.12,5.12L18.87,8.87M3,17.25V21H6.75L17.81,9.93L14.06,6.18L3,17.25Z",ratingEmpty:"svg:M12,15.39L8.24,17.66L9.23,13.38L5.91,10.5L10.29,10.13L12,6.09L13.71,10.13L18.09,10.5L14.77,13.38L15.76,17.66M22,9.24L14.81,8.63L12,2L9.19,8.63L2,9.24L7.45,13.97L5.82,21L12,17.27L18.18,21L16.54,13.97L22,9.24Z",ratingFull:"svg:M12,17.27L18.18,21L16.54,13.97L22,9.24L14.81,8.62L12,2L9.19,8.62L2,9.24L7.45,13.97L5.82,21L12,17.27Z",ratingHalf:"svg:M12,15.4V6.1L13.71,10.13L18.09,10.5L14.77,13.39L15.76,17.67M22,9.24L14.81,8.63L12,2L9.19,8.63L2,9.24L7.45,13.97L5.82,21L12,17.27L18.18,21L16.54,13.97L22,9.24Z",loading:"svg:M19,8L15,12H18C18,15.31 15.31,18 12,18C11,18 10.03,17.75 9.2,17.3L7.74,18.76C8.97,19.54 10.43,20 12,20C16.42,20 20,16.42 20,12H23M6,12C6,8.69 8.69,6 12,6C13,6 13.97,6.25 14.8,6.7L16.26,5.24C15.03,4.46 13.57,4 12,4C7.58,4 4,7.58 4,12H1L5,16L9,12",first:"svg:M18.41,16.59L13.82,12L18.41,7.41L17,6L11,12L17,18L18.41,16.59M6,6H8V18H6V6Z",last:"svg:M5.59,7.41L10.18,12L5.59,16.59L7,18L13,12L7,6L5.59,7.41M16,6H18V18H16V6Z",unfold:"svg:M12,18.17L8.83,15L7.42,16.41L12,21L16.59,16.41L15.17,15M12,5.83L15.17,9L16.58,7.59L12,3L7.41,7.59L8.83,9L12,5.83Z",file:"svg:M16.5,6V17.5C16.5,19.71 14.71,21.5 12.5,21.5C10.29,21.5 8.5,19.71 8.5,17.5V5C8.5,3.62 9.62,2.5 11,2.5C12.38,2.5 13.5,3.62 13.5,5V15.5C13.5,16.05 13.05,16.5 12.5,16.5C11.95,16.5 11.5,16.05 11.5,15.5V6H10V15.5C10,16.88 11.12,18 12.5,18C13.88,18 15,16.88 15,15.5V5C15,2.79 13.21,1 11,1C8.79,1 7,2.79 7,5V17.5C7,20.54 9.46,23 12.5,23C15.54,23 18,20.54 18,17.5V6H16.5Z",plus:"svg:M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z",minus:"svg:M19,13H5V11H19V13Z",calendar:"svg:M19,19H5V8H19M16,1V3H8V1H6V3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3H18V1M17,12H12V17H17V12Z",treeviewCollapse:"svg:M7,10L12,15L17,10H7Z",treeviewExpand:"svg:M10,17L15,12L10,7V17Z",eyeDropper:"svg:M19.35,11.72L17.22,13.85L15.81,12.43L8.1,20.14L3.5,22L2,20.5L3.86,15.9L11.57,8.19L10.15,6.78L12.28,4.65L19.35,11.72M16.76,3C17.93,1.83 19.83,1.83 21,3C22.17,4.17 22.17,6.07 21,7.24L19.08,9.16L14.84,4.92L16.76,3M5.56,17.03L4.5,19.5L6.97,18.44L14.4,11L13,9.6L5.56,17.03Z"},l1={component:Ta};export{Xm as $,Hy as A,Xy as B,ay as C,gy as D,Xh as E,Kl as F,Ul as G,vy as H,Wa as I,ib as J,lb as K,_n as L,Hf as M,en as N,py as O,Nl as P,Yu as Q,Nt as R,an as S,uh as T,Rl as U,Lo as V,xt as W,Mm as X,js as Y,a1 as Z,l1 as _,At as a,Wb as a0,wy as a1,gr as a2,Ur as a3,qr as a4,Yr as a5,Ml as a6,Qt as a7,Jt as a8,Ay as a9,Ly as aa,t1 as ab,n1 as ac,ye as b,he as c,Bm as d,Qn as e,Lm as f,pn as g,_t as h,Zt as i,Mf as j,Ea as k,Dn as l,nr as m,vo as n,cc as o,Hb as p,vc as q,dc as r,mb as s,Rm as t,fo as u,vh as v,xm as w,Na as x,Cy as y,Ym as z};
