// 插入杆塔-绘制结束调用
import { saveOrUpdateEquipmentInfos, getEquipmentState } from "@/api/desginManage/draw.js";
import { voltageLevelData, getLegendTypeKeyByTowerType, getTowerArrangementKeyByLoop, getNoModuleTypeKey } from "@/views/pages/online/commonData.js";
import { ElMessage } from "element-plus";
import { calculateAngle } from "@/views/pages/online/geometricCalculation.js";
import { getPole, getNoModuleId } from "@/api/onlineDesign/matchData.js";
import useProjectInfoStore from "@/store/modules/projectInfo.js";
import { getEquipmentModel1 } from "@/views/pages/online/saveModelInfo.js";

// 全局变量
let inListFlag = '';

export const insertTower = async (dataList, options) => {
  console.log(dataList,'1111')
  const projectInfoStore = useProjectInfoStore();
  const extendData = options;
  const taskId = new URLSearchParams(new URL(window.location.href).search).get("id");
  
  // 参数校验
  if (!taskId) {
    ElMessage.error("任务ID获取失败");
    return;
  }

  // 初始化数据容器
  const paramsSaveAllInfos = new Map();
  const towerAutoModuleInfos = [];
  
  try {
    // 获取地理条件设置
    const geographicCondition = projectInfoStore.getGeographicConditionsSetting(taskId);
    const towerlineSetting = projectInfoStore.getBaseSetting('towerlineSetting');
    for (const item of dataList) {
      if (item.CLASS_NAME !== "PWPolePSR") continue;

      // 基础参数处理
      const Voltagerating = voltageLevelData.find(v => v.id === extendData?.voltageLevel)?.text;
      if (!Voltagerating) {
        console.warn("电压等级数据异常", extendData?.voltageLevel);
        continue;
      }

      // 杆塔拓扑关系初始化
      const towerTopologyrelations = {
        AssociatedIn: [],
        AssociatedOut: [],
        AssociatedParent: [],
        AssociatedChild: [],
        AssociatedLabel: [],
        AssociatedLabelOwner: [],
        AssociatedFile: [],
      };
      //导线拓扑关系
      const lineTopologyrelations = {
        AssociatedIn: [], // 进线拓扑
        AssociatedOut: [], // 出线拓扑
        AssociatedParent: [], //
        AssociatedChild: [], //
        AssociatedLabel: [], // 目前不涉及
        AssociatedLabelOwner: [], // 目前不涉及
        AssociatedFile: [], // 目前不涉及
    }

      // 获取关联运行杆塔
      const tempTowers = dataList.filter(o => o.POLE_PSR_ID === item.DEV_ID);
      if (tempTowers.length === 0) {
        console.warn("未找到关联运行杆塔", item.DEV_ID);
        continue;
      }
console.log(tempTowers,'干他')
      // 导线处理逻辑封装
      const processLine = async (line, isInLine) => {
        try {
          const coordinates = line.SHAPE.match(/(\d+\.\d+)\s+(\d+\.\d+)/g);
           console.log(coordinates,'走避哦')
          if (!coordinates || coordinates.length < 2) {
            console.warn("导线坐标格式异常", line.DEV_ID);
            return;
          }

          // 获取线路状态
          const legendState = await updateLineLegendState(line.DEV_ID, taskId);

          // 构建导线数据
          const lineData = getEquipmentModel1("Line", coordinates, {
            legendTypeKey: Voltagerating === "380V" ? "TY_DY_380DHJKX" 
            : Voltagerating === "220V" ? "TY_DY_220DHJKX" 
            : "TY_DHJKX",
            legendState,
            legendGuidKey: line.DEV_ID,
            engineeringId: taskId,
          })
          lineData.privatepropertys = {
            Voltagerating,
            Span: line.SPAN.toFixed(2),
            TDDJ: line.SPAN.toFixed(2),
            LineNumber: line.Name,
          }
          lineData.topologyrelations = { ...lineTopologyrelations}
          paramsSaveAllInfos.set(line.DEV_ID, lineData);
        } catch (err) {
          console.error("导线处理异常", line.DEV_ID, err);
        }
      };

      // 处理进出导线
      const inLines = [];
      const outLines = [];
      
      for (const tempTower of tempTowers) {
        const inLine = dataList.find(o => o.END_POLE_ID === tempTower.DEV_ID);
        const outLine = dataList.find(o => o.START_POLE_ID === tempTower.DEV_ID);

        if (inLine) {
          inLines.push(inLine);
          console.log(inLines,'前导线')
          lineTopologyrelations.AssociatedOut=[];
          lineTopologyrelations.AssociatedIn=[];
          lineTopologyrelations.AssociatedOut.push(inLine.END_POLE_ID);
          if(inLine.START_POLE_ID===tempTower.DEV_ID){
            lineTopologyrelations.AssociatedIn.push(inLine.START_POLE_ID)
        }
          await processLine(inLine, true);
        }

        if (outLine) {
          outLines.push(outLine);
          console.log(outLines,'后  导线')
          lineTopologyrelations.AssociatedOut=[];
          lineTopologyrelations.AssociatedIn=[];
          lineTopologyrelations.AssociatedIn.push(outLine.START_POLE_ID);
          if(outLine.END_POLE_ID===tempTower.DEV_ID){
            lineTopologyrelations.AssociatedOut.push(outLine.END_POLE_ID)
        }
          await processLine(outLine, false);
        }
        //杆塔拓扑
        if(tempTower.DEV_ID===inLine.END_POLE_ID&&tempTower.DEV_ID===outLine.START_POLE_ID){
            towerTopologyrelations.AssociatedIn.push(inLine.DEV_ID)
            towerTopologyrelations.AssociatedOut.push(outLine.DEV_ID)
          }
      }

      // 构建杆塔数据
      const towerData = getEquipmentModel1("Point", `${tempTowers[0].X} ${tempTowers[0].Y}`, {
        legendTypeKey: getLegendTypeKeyByTowerType(extendData?.ganClass),
        legendState: extendData?.status,
        legendGuidKey: tempTowers[0].POLE_PSR_ID,
        engineeringId: taskId,
      })
      towerData.privatepropertys = {
        Longitude: tempTowers[0].LONGITUDE,
        Latitude: tempTowers[0].LATITUDE,
        GTowerHeght: extendData?.gggHeight,
        cementMaterialID: extendData?.cementModel,
        TowerType: extendData?.ganClass,
        TTowerHeght: extendData?.gggHeight,
        TZ: extendData?.soil,
        DX: extendData?.terrain,
        Voltagerating,
        HDCD: extendData?.crossLength,
        JC: extendData?.foundation,
        Modality: extendData?.gggXs,
        UserNumber: tempTowers[0].Name
      },
      towerData.topologyrelations = { ...towerTopologyrelations }
      paramsSaveAllInfos.set(item.DEV_ID, towerData);

      // 构建自动推导参数
      const isTerminalPole = towerTopologyrelations.AssociatedIn.length === 0 || towerTopologyrelations.AssociatedOut.length === 0;
      const towerAutoModuleInfo = {
        towerGuid: item.DEV_ID,
        params: {
          angle: 0,
          pointID: item.DEV_ID,
          arrayMode: towerlineSetting[getTowerArrangementKeyByLoop("单回")[3]],
          cementMaterialID: extendData?.cementModel,
          gtlb: extendData?.ganClass,
          isTerminalPole,
          loopNumber: getLegendTypeKeyByTowerType(extendData?.ganClass),
          meteorological: geographicCondition.GeographicName,
          spanLength: Math.max(...inLines.map(o => o.SPAN)) || 0,
          strainLength: 0,
          equipType: extendData?.ganClass,
          tddj: Math.max(...inLines.map(o => o.SPAN)) || 0,
          voltage: Voltagerating,
          zxArr: [towerlineSetting[getTowerArrangementKeyByLoop("单回")[0]]],
          zjArr: [towerlineSetting[getTowerArrangementKeyByLoop("单回")[1]]],
          nzArr: [towerlineSetting[getTowerArrangementKeyByLoop("单回")[2]]],
          zdArr: [towerlineSetting[getTowerArrangementKeyByLoop("单回")[3]]]
        }
      };
      towerAutoModuleInfos.push(towerAutoModuleInfo);
    }

    // 关键修改3：型号推导异步处理
    const processModule = async (moduleInfo) => { 
      try {
        // if (!extendData.checkbox) return;
        let moduleId;
        const moduletypekey = getNoModuleTypeKey(
          moduleInfo.params.voltage,
          paramsSaveAllInfos.get(moduleInfo.towerGuid).legendTypeKey
        );
        if(extendData.checkbox){
          const poleRes = await getPole(moduleInfo.params);
          if (poleRes.code !== 200) throw new Error(poleRes.msg);
          if (poleRes.data.moduleDes === '未选型') {
            const noModuleRes = await getNoModuleId({
              voltage: moduleInfo.params.voltage,
              moduletypekey
            });
            moduleId = noModuleRes.data?.[0]?.moduleid || "default_module";
          } else {
            moduleId = poleRes.data.assID;
          }
        }else{
          const noModuleRes = await getNoModuleId({
            voltage: moduleInfo.params.voltage,
            moduletypekey
          });
          moduleId = noModuleRes.data?.[0]?.moduleid || "default_module";
        }
        if (paramsSaveAllInfos.has(moduleInfo.towerGuid)) {
          paramsSaveAllInfos.get(moduleInfo.towerGuid).moduleId = moduleId;
        }
      } catch (err) {
        console.error("型号推导失败", moduleInfo.towerGuid, err);
        paramsSaveAllInfos.get(moduleInfo.towerGuid).moduleId = "error_module";
      }
    };

    // 并行处理所有型号推导
    await Promise.all(towerAutoModuleInfos.map(processModule));
    const finalData = [];
    paramsSaveAllInfos.forEach((value, key) => {
    //   if (!value.moduleId) {
    //     console.warn("缺失moduleId", key);
    //     return;
    //   }
    //   if (!value.legendTypeKey) {
    //     console.warn("缺失legendTypeKey", key);
    //     return;
    //   }
      finalData.push(value);
    });

    // if (finalData.length === 0) {
    //   ElMessage.warning("无有效数据需要保存");
    //   return;
    // }
    // 保存操作
    const res = await saveOrUpdateEquipmentInfos(finalData);
    if (res.code === 200) {
      ElMessage.success("保存成功");
    } else {
      ElMessage.error(res.msg);
    }
  } catch (err) {
    console.error("主流程异常", err);
    ElMessage.error(`操作失败: ${err.message}`);
  }
};

// 优化后的状态获取函数
async function updateLineLegendState(id, taskId) {
  if (inListFlag === id) return "Original";
  
  try {
    const res = await getEquipmentState({ equipmentId: id, taskId });
    inListFlag = id;
    return res.data?.trim() || "Original";
  } catch (err) {
    console.error("状态获取失败", id, err);
    return "ErrorState";
  }
}
