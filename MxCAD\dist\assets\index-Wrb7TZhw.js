import{M as V}from"./index-8X61wlK0.js";import{n as p,M as f,a as g,q as v}from"./index-D95UjFey.js";import{E as t,i as o,b as r,j as u,C as d,J as w,K as _}from"./vuetify-B_xYg4qv.js";import{h as y,a0 as B,_ as C,$ as e,a1 as a,m as l,V as n,Q as D,u as j,B as M}from"./vue-DfH9C9Rx.js";import"./mxcad-CfPpL1Bn.js";import"./mxdraw-C_n_7lEs.js";import"./mapbox-gl-PN3pDJHq.js";import"./xlsx-Ck5MUZvf.js";const k={class:"px-3"},F={class:""},R={class:""},X={class:"mt-1 mb-4",style:{"margin-right":"42px"}},N={class:""},S={class:""},T={class:""},$={class:"w-100 d-flex justify-space-between"},q={class:""},A={class:""},E={class:""},G={class:""},I={class:"d-flex flex-column justify-center align-center"},J={class:"text-center"},Y=y({__name:"index",setup(K){const{isShow:i,showDialog:m}=v,h=[{name:"开始转换",fun:()=>{},primary:!0},{name:"取消",fun:()=>m(!1)}];return(s,c)=>(B(),C(V,{modelValue:j(i),"onUpdate:modelValue":c[0]||(c[0]=b=>M(i)?i.value=b:null),footerBtnList:h,"max-width":"450",title:"CAD转PDF"},{default:e(()=>[a("div",k,[l(d,{class:"mt-1"},{default:e(()=>[l(t,{cols:"8"},{default:e(()=>[l(o,null,{prepend:e(()=>[a("span",F,n(s.t("480"))+":",1)]),append:e(()=>[l(p,null,{default:e(()=>[l(r,{icon:"class:iconfont more"})]),_:1})]),_:1})]),_:1}),l(t,{cols:"4"},{default:e(()=>[l(u,{class:"ml-2",label:s.t("481")},null,8,["label"])]),_:1})]),_:1}),l(d,null,{default:e(()=>[l(t,null,{default:e(()=>[l(o,null,{prepend:e(()=>[a("span",R,n(s.t("482"))+":",1)]),append:e(()=>[l(p,null,{default:e(()=>[l(r,{icon:"class:iconfont more"})]),_:1})]),_:1})]),_:1})]),_:1}),a("div",X,[l(d,null,{default:e(()=>[l(t,null,{default:e(()=>[l(o,null,{prepend:e(()=>[a("span",N,n(s.t("483"))+":",1)]),_:1})]),_:1})]),_:1}),l(d,{justify:"space-between"},{default:e(()=>[l(t,{cols:"5"},{default:e(()=>[l(o,null,{prepend:e(()=>[a("span",S,n(s.t("484"))+":",1)]),_:1})]),_:1}),l(t,{cols:"5"},{default:e(()=>[l(o,null,{prepend:e(()=>[a("span",T,n(s.t("485"))+":",1)]),_:1})]),_:1})]),_:1}),l(d,{justify:"end","no-gutters":""},{default:e(()=>[l(t,{cols:"8"}),l(t,{cols:"4"},{default:e(()=>[l(u,{class:"",label:s.t("486")},null,8,["label"])]),_:1})]),_:1}),l(u,{class:"",label:s.t("487")},null,8,["label"]),l(u,{class:"mt-1",label:s.t("488")},null,8,["label"]),l(f,{title:s.t("489")},{default:e(()=>[l(w,{class:""},{default:e(()=>[a("div",$,[l(_,{label:s.t("490"),value:"1"},null,8,["label"]),l(_,{label:s.t("491"),value:"2"},null,8,["label"]),l(_,{label:s.t("492"),value:"3"},null,8,["label"])])]),_:1}),l(d,null,{default:e(()=>[l(t,{cols:"6"},{default:e(()=>[l(o,null,{prepend:e(()=>[a("span",q,n(s.t("493"))+"X1:",1)]),_:1})]),_:1}),l(t,{cols:"6"},{default:e(()=>[l(o,null,{prepend:e(()=>[a("span",A,n(s.t("493"))+"X1:",1)]),_:1})]),_:1})]),_:1}),l(d,null,{default:e(()=>[l(t,{cols:"6"},{default:e(()=>[l(o,null,{prepend:e(()=>[a("span",E,n(s.t("493"))+"X1:",1)]),_:1})]),_:1}),l(t,{cols:"6"},{default:e(()=>[l(o,null,{prepend:e(()=>[a("span",G,n(s.t("493"))+"X1:",1)]),_:1})]),_:1})]),_:1})]),_:1},8,["title"]),l(f,{title:s.t("489")},{default:e(()=>[a("div",I,[a("p",J,n(s.t("494")),1),l(p,{class:"mt-2"},{default:e(()=>[l(g,{"key-name":"B"},{default:e(()=>[D(n(s.t("495"))+"F",1)]),_:1})]),_:1})])]),_:1},8,["title"])])])]),_:1},8,["modelValue"]))}});export{Y as default};
