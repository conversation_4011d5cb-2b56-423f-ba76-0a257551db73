import {ref, onMounted, onUnmounted} from 'vue';

export function useIframeMessage(targetOrigin = '*') {
    const message = ref(null); // 存储接收到的消息

    // 监听来自 iframe 的消息
    const handleMessage = (event) => {
        if (event.origin !== targetOrigin) {
            return; // 如果消息来自不信任的源，则忽略
        }

        message.value = event.data; // 处理消息
    };

    const clearListening = () => {
        window.removeEventListener('message', handleMessage);
    }

    // 注册消息监听
    onMounted(() => {
        window.addEventListener('message', handleMessage);
    });

    // 清理监听
    onUnmounted(() => {
        clearListening()
    });

    return {
        message,
        clearListening
    };
}
