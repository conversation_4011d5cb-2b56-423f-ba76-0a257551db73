<script setup>

import DataDialog from "@/components/DataDialog/index.vue";

import useAppStore from "@/store/modules/app.js";

const appStore = useAppStore();


const closeDialog = () => {
  appStore.closeMapDialog()
};

const resolvedComponent = shallowRef(null);

const title = computed(() => appStore.currentMxAppMenu.name)
const componentName = computed(() => appStore.currentMxAppMenu.component)

const isMain = computed(() => appStore.currentMxAppMenu.componentType === 'main')
const componentKey = computed(() => appStore.componentKey)

const showDialog = computed(() => appStore.mapIndex && componentName.value);

const componentList = shallowRef({
  ElectricalSchematicDesign: defineAsyncComponent(() => import('../electrical-schematic-design')),
  NumberingRearrangement: defineAsyncComponent(() => import('../ToolsManagement/components/numbering-rearrangement')),
  //   图签编辑
  TitleBlockEdit: defineAsyncComponent(() => import('../ToolsManagement/title-block-edit')),
  // 设备选型
  PlantModelSelection: defineAsyncComponent(() => import('../ToolsManagement/plant-model-selection')),
  EquipmentScreening: defineAsyncComponent(() => import('../ToolsManagement/equipment-screening')),
  // 图纸拆分
  DrawingSplitting: defineAsyncComponent(() => import('../ToolsManagement/drawing-splitting')),

  // 标注设备
  EquipmentAnnotation: defineAsyncComponent(() => import('../annotation/equipment-annotation')),
  CableAnnotation: defineAsyncComponent(() => import('../annotation/cable-annotation')),
  EquipmentAncillaryFacilities: defineAsyncComponent(() => import('../annotation/ancillary-facilities-annotation')),
  ElseAnnotation: defineAsyncComponent(() => import('../annotation/else-annotation')),

})

const closeComponent = () => {
  closeDialog()
}

// 动态加载组件
watch(componentName, async (n) => {
  if (n) {
    resolvedComponent.value = componentList.value[n]
  }
})

</script>

<template>
  <div class="dynamic-dialog">
    <template v-if="showDialog">
    <div v-if="isMain" class="">
      <component :is="resolvedComponent" :key="componentKey" @close="closeComponent"/>
    </div>
    <data-dialog
        v-else
        dataWidth=""
        @close="closeDialog"
    >
      <template #header>
        <h4 style="margin-left: 10px; margin-top: 0px; line-height: 39px">
          {{ title }}
        </h4>
      </template>
      <template #body>
        <component :is="resolvedComponent" @close="closeComponent"/>
      </template>
    </data-dialog>
    </template>
  </div>

</template>

<style lang="scss">
.dynamic-dialog {
  .b-form {
    .b-form-item {
      background-color: var(--el-color-primary-light-6);
      border-bottom: 1px solid #fff;
      margin-bottom: 0;

      .el-form-item__label-wrap {
        padding: 4px;
        background-color: var(--el-color-primary-light-6);
      }

      .el-form-item__content {
        padding: 0 16px;
        background-color: var(--el-color-primary-light-9);
      }
    }
  }

  .b-footer {
    text-align: center;
    padding: 4px 0;
    background-color: var(--el-color-primary-light-9);
  }

  .group-header {
    background: #e4f2f2;
    width: 100%;
    height: 30px;
    display: flex;
    align-items: center;
    margin-bottom: 1px;
    padding: 0 8px;

    .group-title {
      position: relative;
      color: #0e8b8d;
      font-size: 16px;
      font-weight: bold;
      font-family: "Noto Sans SC", sans-serif;
      padding-left: 8px; // 留出左侧空间，以便显示竖杠

      // 添加竖杠
      &::before {
        width: 4px;
        height: 70%;
        //border-radius: 4px;
        content: ''; // 竖杠字符
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%); // 垂直居中
        background-color: var(--el-color-primary); // 竖杠颜色
      }
    }
  }
}
</style>
