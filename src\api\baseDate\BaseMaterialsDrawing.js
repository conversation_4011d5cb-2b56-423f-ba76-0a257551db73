import request from '@/utils/request'

// 查询物料图纸列表
export function listBaseMaterialsDrawing(query) {
  return request({
    url: '/base_materials_drawing/list',
    method: 'get',
    params: query
  })
}

// 查询物料图纸详细
export function getBaseMaterialsDrawing(objId) {
  return request({
    url: '/base_materials_drawing/' + objId,
    method: 'get'
  })
}

// 新增物料图纸
export function addBaseMaterialsDrawing(data) {
  return request({
    url: '/base_materials_drawing',
    method: 'post',
    data: data
  })
}

// 修改物料图纸
export function updateBaseMaterialsDrawing(data) {
  return request({
    url: '/base_materials_drawing/update',
    method: 'post',
    data: data
  })
}

// 删除物料图纸
export function delBaseMaterialsDrawing(objId) {
  return request({
    url: '/base_materials_drawing' + objId,
    method: 'delete'
  })
}
