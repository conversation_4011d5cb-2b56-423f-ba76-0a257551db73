import {MxCpp,McCmColor, McDbAttributeDefinition,McGeVector3d,McGeMatrix3d, McDbPolyline,McDbText,McDb, MxCADUiPrPoint, McDbBlockReference, McGePoint3d, McDbLine, McDbBlockTableRecord, MxCADResbuf, MxCADUiPrEntity, MxCADUiPrKeyWord} from "mxcad";
import {MxFun} from "mxdraw";

async function Draw_BYJG(data) {
    const param = JSON.parse(data.param)
    const {drawType, width, height, text} = param

    let trueColor = new McCmColor(255, 0, 0)
    // if(!param.drawData)
    const mxcad = MxCpp.getCurrentMxCAD();

    let getPoint = new MxCADUiPrPoint();
    getPoint.setMessage("请选择插入点")
    const pt = await getPoint.go();

    let blkWidth = width
    let blkHeight = height
   /* if(drawType === '1'){
        blkWidth = width
        blkHeight = height
    }else{
        blkWidth = height
        blkHeight = width
    }*/
    const blkTable = mxcad.getDatabase().getBlockTable();
    const blkRecId = blkTable.add(new McDbBlockTableRecord());
    const blkRecord = blkRecId.getMcDbBlockTableRecord();

    const squarePoly = new McDbPolyline();

    squarePoly.addVertexAt(new McGePoint3d(-blkWidth/2, blkHeight/2)); // 左上
    squarePoly.addVertexAt(new McGePoint3d(blkWidth/2, blkHeight/2));  // 右上
    squarePoly.addVertexAt(new McGePoint3d(blkWidth/2, -blkHeight/2)); // 右下
    squarePoly.addVertexAt(new McGePoint3d(-blkWidth/2, -blkHeight/2));// 左下
    squarePoly.isClosed = true;
    blkRecord.appendAcDbEntity(squarePoly);

    const textEntity = new McDbText();
    textEntity.textString = text;
    textEntity.position = pt;  // 中心坐标
    textEntity.height = 5;         // 文字高度
    textEntity.horizontalMode = McDb.TextHorzMode.kTextCenter
    textEntity.verticalMode = McDb.TextVertMode.kTextVertMid
    blkRecord.appendAcDbEntity(textEntity);

    console.log('pt', pt)
    blkRecord.origin = new McGePoint3d(0, 0, 0); // 基点保持中心对齐
    const blkRef = new McDbBlockReference();
    blkRef.blockTableRecordId = blkRecId;

    blkRef.position = pt

    if(drawType === '2') {
        console.log('90 * (180 / Math.PI)', 90 * (Math.PI / 180))
        blkRef.rotate(pt, 90 * (Math.PI / 180))
    }
    blkRef.trueColor = trueColor;

    const entId = mxcad.drawEntity(blkRef);

    const ent = entId.getMcDbEntity();
    const handle = ent.getHandle()

    MxFun.postMessageToParentFrame({
        messageId: data.id,
        params: {
            handle,
            position: `${pt.x},${pt.y},${pt.z}`
        },
    });
}



export function init() {
    MxFun.addCommand("Draw_BYJG", Draw_BYJG);
}