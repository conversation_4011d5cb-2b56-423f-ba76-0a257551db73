<template>
  <el-descriptions style="margin-top: 5px;width: 30%" border label-width="15%" :column="2">
    <el-descriptions-item label="图签组">
      <div style="display: flex;justify-content: center">
        <el-select
            style="width: 220px;"
            v-model="baseData.GraphTagGroup"
            placeholder="请选择"
            @change='cadYlFun'
        >
          <el-option
              v-for="item in tqzValueOption"
              :key="item.value"
              :label="item.label"
              :value="item.value"
          />
        </el-select>
      </div>
    </el-descriptions-item>
  </el-descriptions>
  <div style="height: 600px; overflow: hidden;">
    <canvas id="myCanvas"></canvas>
  </div>
</template>
<script setup>
import {McObject} from "mxcad"

const mxcad = new McObject()
onMounted(() => {
  console.log(new URL(`/src/assets/${tqzValue.value}.mxweb`, import.meta.url).href, '1111')
  cadYlFun()
})

const baseData = defineModel('baseData', {type: Object, required: true});

const cadYlFun = () => {
  mxcad.create({
    // canvas元素的id
    canvas: "#myCanvas",
    // 获取加载wasm相关文件(wasm/js/worker.js)路径位置
    locateFile: (fileName) => new URL(`/node_modules/mxcad/dist/wasm/2d/${fileName}`, import.meta.url).href,
    // 需要初始化打开的文件url路径
    fileUrl: new URL(`/src/assets/${tqzValue.value}.mxweb`, import.meta.url).href,
    // 提供加载字体的目录路径
    fontspath: new URL("/node_modules/mxcad/dist/fonts", import.meta.url).href,
  })
}

const tqzValue = ref('业扩标准图签')
const tqzValueOption = ref([
  {label: '业扩标准图签', value: '业扩标准图签'},
  {label: '标准图签', value: '标准图签'},
])
</script>
<style scoped lang="scss">
#mycanvas {
  width: 615px !important;
  height: 268px !important;
}
</style>
