import{M as s,y as n}from"./mxdraw-C_n_7lEs.js";import{U as i}from"./mxcad-CfPpL1Bn.js";import{ak as m}from"./vue-DfH9C9Rx.js";import{ab as r,ac as d}from"./vuetify-B_xYg4qv.js";import{b5 as b,K as u,b6 as p,b7 as x,b8 as f,b9 as w,ba as M,N as C,ah as c,bb as g,b as l,bc as D,bd as y,be as h,aW as T,af as A,bf as E,bg as I,u as v,O as L,b2 as o,bh as U,bi as F,bj as _,bk as N,bl as O,ao as P,bm as S}from"./index-D95UjFey.js";import{M as W}from"./index-8X61wlK0.js";import{M as k}from"./index--PcCuGx1.js";import{T as V}from"./TextEllipsis-Do4nnv80.js";import{a as j}from"./mapbox-gl-PN3pDJHq.js";import"./xlsx-Ck5MUZvf.js";const B={MxBtnList:b,MxColorSelect:u,MxDialog:W,MxMenu:p,MxTabsWindow:k,TextEllipsis:V},G={getApp:x,setCustomDataListLength:f,setOnGetCategoryNamesFun:w,store:{useFocus:M,useTextStyle:C,useLayer:c},getUiConfig:g,utils:{browserCacheRef:l},useTheme:D,useFileName:y,useUserInfo:h,addDrawerComponent(e,a){s.callEvent("__addDrawerComponent",{name:e,componentInfo:a})},components:{...B},addCommand:T,callCommand:A,getMxCADUIImplement:E,useLoading:I,useMessage:v,useDialogIsShow:L,on:o.on.bind(o),off:o.off.bind(o),emit:o.emit.bind(o),once:o.once.bind(o),showDynamicInputMenu:U,hideDynamicInputOrWord:F,registerEditorCustomTextPlugin:_,getMxCADMTextEditor:N,createMxCADMTextEditor:O};window.Mx=n;window.MxCAD=i;window.Vue=m;window.axios=P;window.MxPluginContext=G;window.vuetify=r;window.vuetifyComponents=d;window.mapboxgl=j;s.on("__addDrawerComponent",e=>{const{name:a,componentInfo:t}=e;S(a,t)});
