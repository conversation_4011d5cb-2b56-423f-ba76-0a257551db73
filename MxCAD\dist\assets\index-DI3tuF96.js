var q=Object.defineProperty;var Z=(n,e,t)=>e in n?q(n,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):n[e]=t;var g=(n,e,t)=>Z(n,typeof e!="symbol"?e+"":e,t);import{Q as ee,s as C,Z as te,c as ne,a as oe}from"./index-CzBriCFR.js";import{M as ae}from"./index-itnQ6avM.js";import{r as se,u as re}from"./xlsx-ocdEQCFM.js";import{H as z,r as ie}from"./handsontable-Ch5RdAT_.js";import{h as F,M as le,_ as K,$ as ce,ab as ue,d as O,w as he,z as me,R as de,a3 as fe,a4 as L,u as N,B as pe,a0 as E,Q as H,V as P,m as V}from"./vue-Cj9QYd7Z.js";import{y as ge,M as we,x as W,l as ve,m as be,h as Se}from"./mxcad-DrgW2waE.js";import{M as Q}from"./mxdraw-BQ5HhRCY.js";import"./mapbox-gl-DQAr7S0z.js";import{L as B}from"./vuetify-BqCp6y38.js";function k(n,e){var t=Object.keys(n);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(n);e&&(o=o.filter(function(a){return Object.getOwnPropertyDescriptor(n,a).enumerable})),t.push.apply(t,o)}return t}function Ce(n){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?arguments[e]:{};e%2?k(Object(t),!0).forEach(function(o){Ie(n,o,t[o])}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(t)):k(Object(t)).forEach(function(o){Object.defineProperty(n,o,Object.getOwnPropertyDescriptor(t,o))})}return n}function ye(n,e){if(typeof n!="object"||!n)return n;var t=n[Symbol.toPrimitive];if(t!==void 0){var o=t.call(n,e||"default");if(typeof o!="object")return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(n)}function _e(n){var e=ye(n,"string");return typeof e=="symbol"?e:e+""}function T(n){"@babel/helpers - typeof";return T=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},T(n)}function Ie(n,e,t){return e=_e(e),e in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}var M=Symbol("unassigned"),xe="The Handsontable instance bound to this component was destroyed and cannot be used properly.";function j(n,e){return Object.prototype.hasOwnProperty.call(n,e)}function X(n){var e=z.hooks.getRegistered(),t={};Object.assign(t,z.DefaultSettings);for(var o in t)t[o]={default:M};for(var a=0;a<e.length;a++)t[e[a]]={default:M};return t.settings={default:M},n==="HotTable"&&(t.id={type:String,default:"hot-".concat(Math.random().toString(36).substring(5))}),t}function Y(n){var e={},t=n.settings;if(t!==M)for(var o in t)j(t,o)&&t[o]!==M&&(e[o]=t[o]);for(var a in n)j(n,a)&&a!=="settings"&&n[a]!==M&&(e[a]=n[a]);return e}function G(n,e){var t=Y(n),o=n.settings?n.settings:t,a=n.settings?t:null,s={};for(var r in o)j(o,r)&&o[r]!==void 0&&(!(e&&r!=="data")||!J(e[r],o[r]))&&(s[r]=o[r]);for(var i in a)j(a,i)&&i!=="id"&&i!=="settings"&&a[i]!==void 0&&(!(e&&i!=="data")||!J(e[i],a[i]))&&(s[i]=a[i]);return s}function J(n,e){var t=function(a){var s=function(){var r=new WeakSet;return function(i,l){if(T(l)==="object"&&l!==null){if(r.has(l))return;r.add(l)}return l}}();return JSON.stringify(a,s)};return typeof n=="function"&&typeof e=="function"?n.toString()===e.toString():T(n)!==T(e)?!1:t(n)===t(e)}var Me="14.4.0",$=F({name:"HotTable",props:X("HotTable"),provide:function(){return{columnsCache:this.columnsCache}},watch:{$props:{handler:function(e){var t=G(e,this.hotInstance?this.hotInstance.getSettings():void 0);!this.hotInstance||t===void 0||(t.data&&(this.hotInstance.isColumnModificationAllowed()||!this.hotInstance.isColumnModificationAllowed()&&this.hotInstance.countSourceCols()===this.miscCache.currentSourceColumns)&&(this.matchHotMappersSize(),delete t.data),Object.keys(t).length?this.hotInstance.updateSettings(t):this.hotInstance.render(),this.miscCache.currentSourceColumns=this.hotInstance.countSourceCols())},deep:!0,immediate:!0}},data:function(){return{__hotInstance:null,miscCache:{currentSourceColumns:null},columnSettings:null,columnsCache:new Map,get hotInstance(){return!this.__hotInstance||this.__hotInstance&&!this.__hotInstance.isDestroyed?this.__hotInstance:(console.warn(xe),null)},set hotInstance(e){this.__hotInstance=e}}},methods:{hotInit:function(){var e=G(this.$props);e.columns=this.columnSettings?this.columnSettings:e.columns,this.hotInstance=le(new z.Core(this.$el,e)),this.hotInstance.init(),this.miscCache.currentSourceColumns=this.hotInstance.countSourceCols()},matchHotMappersSize:function(){var e=this;if(this.hotInstance){var t=this.hotInstance.getSourceData(),o=[],a=[],s=this.hotInstance.rowIndexMapper.getNumberOfIndexes(),r=this.hotInstance.isColumnModificationAllowed(),i=0;if(t&&t.length!==s&&t.length<s)for(var l=t.length;l<s;l++)o.push(l);if(r){var f;if(i=this.hotInstance.columnIndexMapper.getNumberOfIndexes(),t&&t[0]&&((f=t[0])===null||f===void 0?void 0:f.length)!==i&&t[0].length<i)for(var u=t[0].length;u<i;u++)a.push(u)}this.hotInstance.batch(function(){o.length>0?e.hotInstance.rowIndexMapper.removeIndexes(o):e.hotInstance.rowIndexMapper.insertIndexes(s-1,t.length-s),r&&t.length!==0&&(a.length>0?e.hotInstance.columnIndexMapper.removeIndexes(a):e.hotInstance.columnIndexMapper.insertIndexes(i-1,t[0].length-i))})}},getColumnSettings:function(){var e=Array.from(this.columnsCache.values());return e.length?e:void 0}},mounted:function(){this.columnSettings=this.getColumnSettings(),this.hotInit()},beforeUnmount:function(){this.hotInstance&&this.hotInstance.destroy()},version:Me}),De=["id"];function He(n,e,t,o,a,s){return K(),ce("div",{id:n.id},[ue(n.$slots,"default")],8,De)}$.render=He;$.__file="src/HotTable.vue";var Pe=F({name:"HotColumn",props:X("HotColumn"),inject:["columnsCache"],methods:{createColumnSettings:function(){var e=Y(this.$props),t=Ce({},e);e.renderer&&(t.renderer=e.renderer),e.editor&&(t.editor=e.editor),this.columnsCache.set(this,t)}},mounted:function(){this.createColumnSettings()},unmounted:function(){this.columnsCache.delete(this)},render:function(){return null}});Pe.__file="src/HotColumn.vue";class Te{constructor(){g(this,"m_aryColumn",[]);g(this,"m_allData",[]);g(this,"m_rowHeights",[]);g(this,"m_dColumnHeight",28);g(this,"m_dRowHeight",25);g(this,"m_mates",[]);g(this,"m_dRowLineWdith",0);g(this,"m_dColumnLineWidth",.5);g(this,"m_dRowTextHeight",13);g(this,"m_dColumnTextHeight",13)}addColumn(e,t){this.m_aryColumn.push({name:e,w:t})}addRow(e,t){this.m_allData.push(e),this.m_rowHeights.push(t)}}function Re(n,e,t){typeof e=="string"&&(e=parseFloat(e));const o=document.createElement("canvas"),a=o.getContext("2d");if(!a)return;a.font=e+"px "+t;let r=a.measureText(n).width,l=e*1.2;return o.remove(),r=Math.ceil(r),l=Math.ceil(l),{width:r,height:l}}class Oe{constructor(){g(this,"data",new Te);g(this,"pt",new ge);g(this,"dScale",1);g(this,"mxcad",we.getCurrentMxCAD());g(this,"isDrawTableHead",!1)}draw(e,t){return this.pt=e,this.dScale=t,this.data.m_aryColumn.length==0||this.data.m_allData.length==0?!1:(this.DrawTableHead(),this.DrawContent(),!0)}DrawTableHead(){if(!this.isDrawTableHead)return;let e=this.data,t=this.pt.clone(),o=this.pt.clone(),a=new W(0,1,0).mult(this.Scale(this.data.m_dColumnHeight)),s=this.Scale(this.data.m_dColumnLineWidth),r=0,i=new W(1,0,0);for(let l=0;l<e.m_aryColumn.length;l++){let f=e.m_aryColumn[l];this.DrawLine(o,o.clone().subvec(a),s),this.DrawMCText(o.clone().addvec(i.clone().mult(this.Scale(f.w)*.5)).subvec(a.clone().mult(.5)),f.name,this.Scale(e.m_dColumnTextHeight),f.w),o.addvec(i.clone().mult(this.Scale(f.w))),r+=this.Scale(f.w)}return this.DrawLine(o,o.clone().subvec(a),s),this.DrawLine(t,t.clone().addvec(i.clone().mult(r)),s),this.DrawLine(t.clone().subvec(a),t.addvec(i.clone().mult(r)).subvec(a),s),!0}DrawContent(){let e=this.data,t=this.pt,o=0;for(let u=0;u<e.m_aryColumn.length;u++)o+=this.Scale(e.m_aryColumn[u].w);let a=this.Scale(this.data.m_dRowLineWdith),s=new W(1,0,0),r=new W(0,1,0),i=t.clone().subvec(r.clone().mult(this.Scale(e.m_dColumnHeight))),l=[],f=[];for(let u=0;u<e.m_allData.length;u++){const R=e.m_rowHeights[u];let w=i.clone(),h=r.clone().mult(this.Scale(R||e.m_dRowHeight));for(let c=0;c<e.m_aryColumn.length;c++){let d=e.m_aryColumn[c],{hidden:v,rowspan:y,colspan:_}=e.m_mates[u][c]||{};if(v||(this.DrawLine(w,w.clone().subvec(h),a),this.DrawMCText(w.clone().addvec(s.clone().mult(this.Scale(d.w*0))).subvec(h.clone().mult(.25)),e.m_allData[u][c],this.Scale(e.m_dRowTextHeight),d.w)),typeof y=="number"&&y>0&&typeof _=="number"&&_>0)for(let m=1;m<y;m++){l.push([u+m,c]);for(let p=0;p<_;p++)f.push([u+m,c+p])}const I=l.some(([m,p])=>u===m&&c===p),S=f.some(([m,p])=>u===m&&c===p);if(I&&this.DrawLine(w,w.clone().subvec(h),a),w.addvec(s.clone().mult(this.Scale(d.w))),S||this.DrawLine(w,w.clone().subvec(s.clone().mult(this.Scale(d.w))),a),u===e.m_allData.length-1){const m=w.clone().subvec(r.clone().mult(this.Scale(R||e.m_dRowHeight)));this.DrawLine(m,m.clone().subvec(s.clone().mult(this.Scale(d.w))),a)}c===e.m_aryColumn.length-1&&this.DrawLine(w,w.clone().subvec(h),a)}i.subvec(h)}return!0}DrawLine(e,t,o){this.mxcad.drawLineWidth=o,this.mxcad.drawLine(e.x,e.y,t.x,t.y)}splitStringEvenly(e,t){if(t<=0||!e)return[];const o=e.length,a=Math.ceil(o/t);let s=[];for(let r=0;r<t;r++){let i=r*a,l=i+a;l=l>o?o:l,s.push(e.substring(i,l))}return s}DrawMCText(e,t,o,a){if(!t)return;const s=new ve,i=(Re(t,Q.docCoordLong2Screen(o)+"px","微软雅黑")?.width||t.length*16)/a;t=this.splitStringEvenly(t,i).join("\\P"),s.textHeight=o,s.contents=t.replaceAll(`
`,"\\P"),s.attachment=be.AttachmentPoint.kTopLeft,s.lineweight=o,s.location=e,this.mxcad.drawEntity(s)}Scale(e){return e*this.dScale}}const Le={class:"d-flex align-center justify-space-between my-2"},Ee={for:"exportTableInputId"},Ve={class:"d-flex"},Ge=F({__name:"index",setup(n){ie();const{isShow:e,showDialog:t}=ee(!1,"Mx_InsertTable"),o=O({width:"auto",height:"300",rowHeaders:!0,colHeaders:!0,autoWrapRow:!0,observeDOMVisibility:!0,viewportRowRenderingOffset:"auto",renderAllRows:!0,autoWrapCol:!0,fixedColumnsStart:0,startRows:10,startCols:10,licenseKey:"non-commercial-and-evaluation",dropdownMenu:!1,comments:!0,manualRowMove:!0,manualColumnFreeze:!0,manualColumnMove:!0,manualRowResize:!0,manualColumnResize:!0,mergeCells:[],contextMenu:{items:{row_above:{name:C("上面插入一行")},row_below:{name:C("下面插入一行")},col_left:{name:C("左侧插入一列")},col_right:{name:C("右侧插入一列")},remove_row:{name:C("移除本行")},remove_col:{name:C("移除本列")},clear_custom:{name:C("清空所有单元格数据"),callback:function(){this.clear()}},mergeCells:{name:C("合并单元格")}}},autoColumnSize:!1,autoRowSize:!1});he(e,()=>{o.value.mergeCells=[]});const a=[{name:"确定",fun:async()=>{t(!1);let h=new Oe;const c=(s.value?.hotInstance).getData(),d=(s.value?.hotInstance).getColHeader();(s.value?.hotInstance).getRowHeader(),d.forEach((S,m)=>{const p=(s.value?.hotInstance).getColWidth(m);h.data.addColumn(S.toString(),p)}),c.map((S,m)=>{h.data.addRow([...S],(s.value?.hotInstance).getRowHeight(m))});const v=[];(s.value?.hotInstance).getCellsMeta().map(S=>{const{hidden:m,row:p,col:A,rowspan:x,colspan:D}=S;v[p]?v[p][A]={hidden:!!m,rowspan:x,colspan:D}:v[p]=[{hidden:!!m,rowspan:x,colspan:D}]}),h.data.m_mates=v;let y=new Se;y.setMessage(`
`+C("指定表格插入点")+":");let _=await y.go();if(!_)return;let I=Q.viewCoordLong2Cad(1);h.draw(_,I)},primary:!0},{name:"关闭",fun:()=>t(!1)}],s=O();let r=!1;const i=h=>{const c=h.target;if(c.files){const d=c.files[0],v=new FileReader;v.onload=function(y){const _=y.target?.result,I=se(_,{type:"binary"}),S=I.Sheets[I.SheetNames[0]],m=re.sheet_to_csv(I.Sheets[I.SheetNames[0]],{blankrows:!0}),p=S["!merges"];p&&(o.value.mergeCells=p.map(b=>({row:b.s.r,col:b.s.c,rowspan:b.e.r-b.s.r+1,colspan:b.e.c-b.s.c+1})));const A=m.split(`
`);let x=0;const D=A.map(function(b){const U=b.split(",");return x=Math.max(x,U.length),U});D.forEach(b=>{for(;b.length<x;)b.push("")}),(s.value?.hotInstance).loadData(D)},v.readAsBinaryString(d)}},l=()=>{r=oe(),window.addEventListener("focus",()=>{setTimeout(()=>{r&&ne("MxFullscreen")},100)},{once:!0})},f=O(4),u=O(4),R=()=>{const h=[];for(let c=0;c<u.value;c++){let d=[];for(let v=0;v<f.value;v++)d.push("");h.push(d)}o.value.mergeCells=[],(s.value?.hotInstance).loadData(h)},w=()=>{s.value?.hotInstance&&setTimeout(()=>{s.value.hotInstance.render(),s.value.hotInstance.refreshDimensions()},100)};return me(()=>{window.addEventListener("resize",w)}),de(()=>{window.removeEventListener("resize",w)}),(h,c)=>(K(),fe(ae,{title:h.t("289"),"max-width":"600",modelValue:N(e),"onUpdate:modelValue":c[2]||(c[2]=d=>pe(e)?e.value=d:null),footerBtnList:a},{default:L(()=>[E("div",Le,[E("label",Ee,[H(P(h.t("647"))+": ",1),E("input",{id:"exportTableInputId",class:"mb-1",style:{width:"150px"},accept:".xls,.xlsx,.xlsm,.csv,.xlsb",onClick:l,type:"file",onChange:i},null,32)]),E("div",Ve,[V(B,{modelValue:u.value,"onUpdate:modelValue":c[0]||(c[0]=d=>u.value=d),type:"number",class:"pa-0 ml-1",style:{"max-width":"80px"}},{prepend:L(()=>[H(P(h.t("648"))+":",1)]),_:1},8,["modelValue"]),V(B,{modelValue:f.value,"onUpdate:modelValue":c[1]||(c[1]=d=>f.value=d),type:"number",class:"pa-0",style:{"max-width":"80px"}},{prepend:L(()=>[H(P(h.t("649"))+":",1)]),_:1},8,["modelValue"])]),V(te,{class:"ml-2",onClick:R},{default:L(()=>[H(P(h.t("650")),1)]),_:1})]),H(" "+P(h.t("651"))+": ",1),V(N($),{settings:o.value,ref_key:"hotTable",ref:s},null,8,["settings"])]),_:1},8,["title","modelValue"]))}});export{Ge as default};
