import { saveAllInfo,saveAllInfos, saveEquipmentInfos, getEquipmentState} from "@/api/desginManage/draw.js";
import { voltageLevelData, getLegendTypeKeyBySymbolId, getFSLXByModuleType, getlegendTypeKey } from "@/views/pages/online/commonData.js";
import { ElMessage } from "element-plus";
import { generateUuid } from "@/utils";
import useProjectInfoStore from "@/store/modules/projectInfo.js";
import { getEquipmentModel, getEquipmentModel1 } from "@/views/pages/online/saveModelInfo.js";
import { getNoModuleIdByVoltageLegendType } from "@/api/onlineDesign/matchData.js";

// 电缆分支箱绘制结束
export const drawEndCableBox = (params, listForm, drawInfo) => {
  if(!params) return
  console.log('drawEndCableBox', params, listForm)
  const taskId = new URLSearchParams(new URL(window.location.href).search).get(
    "id"
  );
  let sdkArr = params.data;
  const sdkList = sdkArr.find(
    (item) => drawInfo.sdkClassName.split('-')[0] === item.CLASS_NAME
  );
  const Intervals = {
    in: [],
    out: []
  }
  for(let item of listForm.lineInIntervals){
    Intervals.in.push({id: generateUuid(), name: item})
  }
  for(let item of listForm.lineOutIntervals){
    Intervals.out.push({id: generateUuid(), name: item})
  }
  const paramsData = getEquipmentModel("Point", `${sdkList.X} ${sdkList.Y}`, { 
    moduleId: listForm.cableBox, // 顶层id
    legendTypeKey: drawInfo.legendtypekey, // 图元类型
    legendState: listForm.status, // 状态
    legendGuidKey: sdkList.DEV_ID, // 设备id
    engineeringId: taskId,
  })
  paramsData.privatepropertys = {
    Voltagerating: listForm.voltageLevel,
    UserNumber: listForm.deviceCode, 
    LineNumber: listForm.lineName,
    Intervals: JSON.stringify(Intervals) 
  }
  console.log("处理完后的数据", paramsData);
  saveAllInfo(paramsData).then((res) => {
    if (res.code === 200) {
      ElMessage.success("保存成功");
    } else {
      ElMessage.error(res.msg);
    }
  });
}

// 工井绘制结束
export const drawEndWell = async (params, listForm, drawInfo) =>{
  if(!params) return
  console.log('drawEndCableBox', params, listForm)
  const taskId = new URLSearchParams(new URL(window.location.href).search).get(
    "id"
  );
  let sdkArr = params.data;
  const sdkWell = sdkArr.find(
    (item) => drawInfo.sdkclassName.split('-')[0] === item.CLASS_NAME
  );
  const wellParam = getEquipmentModel("Point", `${sdkWell.X} ${sdkWell.Y}`, {
    moduleId: listForm.cableModel,
    legendTypeKey: drawInfo.legendtypekey, 
    legendState: listForm.status, 
    legendGuidKey: sdkWell.DEV_ID, 
    engineeringId: taskId}
  )
  wellParam.addFlag = sdkWell.addFlag,
  wellParam.privatepropertys = {
    TZ: listForm.soil,
    LMPSLX: listForm.brokenRoad, 
    PSMJ: listForm.brokenRoadArea,
    UserNumber: listForm.userNumber,
    LineNumber: listForm.lineName
  }
  if(sdkArr.some(o=>o.CLASS_NAME === 'CableChannelSec')){
    //表示工井绘制在通道上
    const paramsDatas = []
    const channels = sdkArr.filter(o=>o.CLASS_NAME === 'CableChannelSec')
    let legendTypeKey = ""
    for(let element of channels){
      // 无法获取到通道状态 查数据库 如果能查到使用数据库里的状态  查不到认为是现状通道
      let legendState = await getEquipmentState({ equipmentId: element.DEV_ID, taskId: taskId })
      legendState = legendState.data || "Original";
      let coordinates = element.SHAPE.match(/(\d+\.\d+)\s+(\d+\.\d+)/g)
      if(element.SUBTYPE_NAME){
        legendTypeKey = getLegendTypeKeyBySymbolId(element.SUBTYPE_NAME)
      }
      const param = getEquipmentModel1("MLine", coordinates, {
        moduleId: "",
        legendTypeKey, 
        legendState, 
        legendGuidKey: element.DEV_ID, 
        engineeringId: taskId
      })
      param.addFlag = element.addFlag ?? "null"
      if(element.HEAD_DEV_ID){
        if(element.HEAD_DEV_ID === wellParam.legendGuidKey){
          wellParam.topologyrelations.AssociatedOut.push(element.DEV_ID)
        }
        param.topologyrelations.AssociatedIn.push(element.HEAD_DEV_ID)
      }
      if(element.TAIL_DEV_ID){
        if(element.TAIL_DEV_ID === wellParam.legendGuidKey){
          wellParam.topologyrelations.AssociatedIn.push(element.DEV_ID)
        }
        param.topologyrelations.AssociatedOut.push(element.TAIL_DEV_ID)
      } 
      paramsDatas.push(param)
    }
    wellParam.equipment_info = wellParam.equipmentInfo
    paramsDatas.push(wellParam)
    //补充之前没有赋值的数据
    paramsDatas.forEach(element=>{
      if(!element.legendTypeKey){
        element.legendTypeKey = legendTypeKey
      }
    })
    saveEquipmentInfos(paramsDatas).then((res) => {
      if (res.code === 200) {
          ElMessage.success("保存成功");
      } else {
          ElMessage.success(res.msg);
      }
    });
  }else{
    //表示工井绘制在空白地方
    console.log("处理完后的数据", wellParam);
    saveAllInfo(wellParam).then((res) => {
      if (res.code === 200) {
        ElMessage.success("保存成功");
      } else {
        ElMessage.error(res.msg);
      }
    });
  }
}

// 电缆绘制结束
export const drawEndCable = async (params, cableListForm, drawInfo) =>{
  if(!params) return
  console.log('drawEndCable', params, cableListForm, drawInfo)
  const taskId = new URLSearchParams(new URL(window.location.href).search).get(
    "id"
  );

  const projectInfoStore = useProjectInfoStore();
  const saveParams = []
  const findCableKeys = [["START_DEV_ID", "START_DEV_CLASS_NAME"], ["END_DEV_ID", "END_DEV_CLASS_NAME"]]
  const sdkArr = params.data;
  const sdkLines = sdkArr.filter(o=> (o.CLASS_NAME === "PWCableSecPSR") && o.addFlag) //绘制的电缆
  for(let itemLine of sdkLines) {
    let coordinates = itemLine.SHAPE.match(/(\d+\.\d+)\s+(\d+\.\d+)/g)
    const lineData = getEquipmentModel("Line", coordinates, {
      legendTypeKey: drawInfo.legendTypeKey, 
      legendGuidKey: itemLine.DEV_ID, 
      engineeringId: taskId, 
      legendState: cableListForm[0].status, 
      moduleId: cableListForm[0].dlxh })
    lineData.privatepropertys = {
      Voltagerating: drawInfo.voltage,// 电压等级
      Span: (itemLine.SPAN ?? itemLine.LENGTH).toFixed(2), 
      TDDJ: (itemLine.SPAN ?? itemLine.LENGTH).toFixed(2),
      LineNumber: cableListForm[0].xlName, //线路名称
      YD: projectInfoStore.getCalculationSetting("CableYuDu"), //裕度
      XBYX: projectInfoStore.getCalculationSetting("XiangBianYuXian"), //箱式站余线（m）
      SBCXYX: projectInfoStore.getCalculationSetting("ChuXianYuXian"), //土建站房余线（m）
      ZJTYX: projectInfoStore.getCalculationSetting("ZhongJianTouYuXian"),  //中间头余线（m）
      DLSGYX: projectInfoStore.getCalculationSetting("ShangGanYuXian"), //上杆余线（m）
      DLJYX: projectInfoStore.getCalculationSetting("DianLanJingYuXian"), //电缆井余线（m）
      LayingState: drawInfo.channelStatus, //敷设状态
      LayingName: drawInfo.LayingName, //敷设方式
      LayingFullName: drawInfo.LayingFullName, //敷设方式
      Laying: drawInfo.Laying, //敷设方式
      FSLB: getFSLXByModuleType(drawInfo.LayingModuletypekey)//敷设类别 
    }
    for(let i = 0; i < findCableKeys.length; i++) {// i===0 首端  i===1 末端
      let startID = itemLine[findCableKeys[i][0]]
      if(itemLine[findCableKeys[i][1]] === "PWPolesitePSR"){// 起始关联的运行杆需要找到对应的物理杆
        startID = sdkArr.find(o=>o.DEV_ID === itemLine[findCableKeys[i][0]]).POLE_PSR_ID
      }
      lineData.topologyrelations[i === 0 ? 'AssociatedIn' : 'AssociatedOut'].push(startID)
      const findStart = saveParams.find(o=>o.legendGuidKey === startID)
      if(findStart){
        findStart.topologyrelations[i === 0 ? 'AssociatedOut' : 'AssociatedIn'].push(itemLine.DEV_ID)
        if(findStart.legendTypeKey === "TY_DLZJT"){
          findStart.topologyrelations.AssociatedParent.push(itemLine.DEV_ID)
        }
      } else {
        const towerSDK = sdkArr.find(o=>o.DEV_ID === startID)
        const legendTypeKey = getlegendTypeKey(towerSDK.CLASS_NAME)
        let legendState = await getEquipmentState({ equipmentId: startID, taskId: taskId })
        legendState = legendState.data || "Original";
        if(legendTypeKey === "TY_DLZJT" && legendState === "Original"){
          //sdk会绘制出电缆中间头
          legendState = "New"
        }
        console.log("towerSDKtowerSDKtowerSDK",towerSDK)
        let towerXY={
          X:towerSDK.SHAPE.split(' ')[1].split('(').join(''),
          Y:towerSDK.SHAPE.split(' ')[2].split(')').join('')
        }
        const tower = getEquipmentModel("Point", `${towerXY.X} ${towerXY.Y}`, {legendTypeKey, legendGuidKey: startID, engineeringId: taskId, legendState })
        let noModuleID = await getNoModuleIdByVoltageLegendType(lineData.privatepropertys.Voltagerating, legendTypeKey)
        tower.moduleId = noModuleID
        tower.topologyrelations[i === 0 ? 'AssociatedOut' : 'AssociatedIn'].push(itemLine.DEV_ID)
        if(legendTypeKey === "TY_DLZJT"){
          tower.topologyrelations.AssociatedParent.push(itemLine.DEV_ID)
        }
        saveParams.push(tower)
      }
    }
    saveParams.push(lineData)
  }
  for(let element of saveParams){
    element.privatepropertys = JSON.stringify(element.privatepropertys)
    element.equipmentInfo = JSON.stringify(element.equipmentInfo)
    element.topologyrelations = JSON.stringify(element.topologyrelations)
  }
  console.log(saveParams, "paramsparamsparamsparams");
  saveAllInfos(saveParams).then((res) => {
    if (res.code === 200) {
      ElMessage.success("保存成功");
    } else {
      ElMessage.error(res.msg);
    }
  });
}
//基于土建路径绘制
export const cableSoil = async (dataList,drawInfo,cableListForm) => {
if(!dataList) return
  const taskId = new URLSearchParams(new URL(window.location.href).search).get(
    "id"
  );

  const projectInfoStore = useProjectInfoStore();
  const saveParams = []
  const sdkSoil = dataList.filter(o=> (o.CLASS_NAME === "PWCableSecPSR") && o.addFlag) 
  for(let itemLine of sdkSoil) {
    let coordinates = itemLine.SHAPE.match(/(\d+\.\d+)\s+(\d+\.\d+)/g)
    const lineData = getEquipmentModel("Line", coordinates, {
      legendTypeKey: drawInfo.legendTypeKey, 
      legendGuidKey: itemLine.DEV_ID, 
      engineeringId: taskId, 
      legendState: cableListForm[0].status, 
      moduleId: cableListForm[0].dlxh })
      lineData.privatepropertys = {
        Voltagerating: drawInfo.voltage,// 电压等级
        Span: (itemLine.SPAN ?? itemLine.LENGTH).toFixed(2),
        TDDJ: (itemLine.SPAN ?? itemLine.LENGTH).toFixed(2),
        LineNumber: cableListForm[0].xlName, //线路名称
        YD: projectInfoStore.getCalculationSetting("CableYuDu"), //裕度
        XBYX: projectInfoStore.getCalculationSetting("XiangBianYuXian"), //箱式站余线（m）
        SBCXYX: projectInfoStore.getCalculationSetting("ChuXianYuXian"), //土建站房余线（m）
        ZJTYX: projectInfoStore.getCalculationSetting("ZhongJianTouYuXian"),  //中间头余线（m）
        DLSGYX: projectInfoStore.getCalculationSetting("ShangGanYuXian"), //上杆余线（m）
        DLJYX: projectInfoStore.getCalculationSetting("DianLanJingYuXian"), //电缆井余线（m）
        LayingState: drawInfo.channelStatus, //敷设状态
        LayingName: drawInfo.LayingName, //敷设方式
        LayingFullName: drawInfo.LayingFullName, //敷设方式
        Laying: drawInfo.Laying, //敷设方式
        FSLB: getFSLXByModuleType(drawInfo.LayingModuletypekey)//敷设类别 
      }
      lineData.topologyrelations.AssociatedParent.push(itemLine.START_DEV_ID)
      lineData.topologyrelations.AssociatedParent.push(itemLine.END_DEV_ID)
      saveParams.push(lineData)
  }
  for(let element of saveParams){
    element.privatepropertys = JSON.stringify(element.privatepropertys)
    element.equipmentInfo = JSON.stringify(element.equipmentInfo)
    element.topologyrelations = JSON.stringify(element.topologyrelations)
  }
  console.log(saveParams, "paramsparamsparamsparams");
  saveAllInfos(saveParams).then((res) => {
    if (res.code === 200) {
      ElMessage.success("保存成功");
    } else {
      ElMessage.error(res.msg);
    }
  });
}