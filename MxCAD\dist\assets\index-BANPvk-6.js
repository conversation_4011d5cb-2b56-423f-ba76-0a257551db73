import{M as g}from"./index-8X61wlK0.js";import{ah as k,I}from"./index-D95UjFey.js";import{u as M}from"./hooks-DBtpDKrj.js";import{i as C,a as B,k as w}from"./vuetify-B_xYg4qv.js";import{h as D,d as c,c as R,a0 as m,_ as d,$ as p,a1 as S,m as v,a3 as F,a4 as K,F as N,u as T,B as U}from"./vue-DfH9C9Rx.js";import"./mxcad-CfPpL1Bn.js";import"./mxdraw-C_n_7lEs.js";import"./mapbox-gl-PN3pDJHq.js";import"./xlsx-Ck5MUZvf.js";const E={class:"d-flex algin-center"},P=D({__name:"index",setup(O){const{dialog:o}=M(),{isShow:i}=o,f=c(!1);o.onReveal(l=>{f.value=!!l.isMultiple});const x=k(),{list:h}=I(x),a=c([]),r=R(()=>h.value.filter(e=>e.name).map(({name:e})=>e)),n=c(""),V=()=>{n.value===""?a.value=[]:a.value=[r.value.findIndex(l=>l.toLocaleLowerCase()===n.value.toLocaleLowerCase())]},y=[{name:"确定",fun:()=>{o.confirm(a.value.map(l=>r.value[l]))},primary:!0},{name:"关闭",fun:()=>o.showDialog(!1)}],L=(l,e)=>{if(f.value){if(l.ctrlKey)a.value.indexOf(e)===-1?a.value=[...a.value,e]:a.value=a.value.filter(s=>s!==e);else if(l.shiftKey){const t=a.value;let s=Math.min(...t),u=Math.max(...t);s>e?s=e:u=e,a.value=r.value.map((W,_)=>_).slice(s,u+1)}}else a.value=[e]};return(l,e)=>(m(),d(g,{maxWidth:"400px",ref:"layerDialog",modelValue:T(i),"onUpdate:modelValue":e[1]||(e[1]=t=>U(i)?i.value=t:null),title:"选择图层",footerBtnList:y},{default:p(()=>[S("div",E,[v(C,{placeholder:l.t("621"),modelValue:n.value,"onUpdate:modelValue":[e[0]||(e[0]=t=>n.value=t),V]},null,8,["placeholder","modelValue"])]),v(w,{density:"compact",color:"primary",style:{height:"300px"}},{default:p(()=>[(m(!0),F(N,null,K(r.value,(t,s)=>(m(),d(B,{class:"ma-0",title:t,ref_for:!0,ref:"refItems",key:s,onClick:u=>L(u,s),active:a.value.includes(s)},null,8,["title","onClick","active"]))),128))]),_:1})]),_:1},8,["modelValue"]))}});export{P as default};
