import { MxCpp } from "mxcad";
import { MxFun } from "mxdraw";

// 获取所有图层
function MxTest_GetAllLayer() {
  // 获取当前mxcad对象
    let mxcad = MxCpp.App.getCurrentMxCAD();
    // 获取图层表
    let layerTable = mxcad.getDatabase().getLayerTable();
    // 获取所有记录id
    let aryId = layerTable.getAllRecordId();
    let ret = [];
    // 遍历图层id
    aryId.forEach((id) => {
      let layerRec = id.getMcDbLayerTableRecord();
      if (layerRec === null) return;
  
      console.log(layerRec);
      console.log("layerRec.color:" + layerRec.color.getColorString());
      console.log("layerRec.name:" + layerRec.name);
      let layer: any = {};
      layer.name = layerRec.name;
  
      layer.color = layerRec.color.getColorString();
      ret.push(layer);
    });
    
    MxFun.postMessageToParentFrame(ret);
    return ret;
  };

  // 调用获取所有图层的方法
  MxTest_GetAllLayer();