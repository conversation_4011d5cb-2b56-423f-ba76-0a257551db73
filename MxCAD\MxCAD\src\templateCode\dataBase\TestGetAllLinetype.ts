import { MxCpp } from "mxcad"

// 获取所有线型样式
function MxTest_GetAllLinetype() {
    // 获取当前mxcad对象
    let mxobj = MxCpp.getCurrentMxCAD();
    // 获取数据库线型样式表
    let linetypeTable = mxobj.getDatabase().getLinetypeTable();
    // 获取所有线型记录的ID数组
    let aryId = linetypeTable.getAllRecordId();
    // 遍历线型记录
    aryId.forEach((id) => {
        let linetypeRec = id.getMcDbLinetypeTableRecord();
        if (linetypeRec === null) return;
        console.log(linetypeRec);
    });
};

// 调用获取所有线型样式
MxTest_GetAllLinetype()