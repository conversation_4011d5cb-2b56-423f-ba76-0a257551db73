import{u as P,V as f,aB as i,aC as a,D as A,Z as _,M as b,C as E,o as Z,_ as O,Q as G,aD as Q,i as q}from"./index-CzBriCFR.js";import{M as J}from"./index-itnQ6avM.js";import{M as I,a as K}from"./mxcad-DrgW2waE.js";import{M as W}from"./mxdraw-BQ5HhRCY.js";import{G as X,H as L,B as S,L as c,$ as B,c as v,z as j,I as Y}from"./vuetify-BqCp6y38.js";import{h as R,d as V,_ as y,$ as h,m as e,a4 as t,a0 as r,u,B as m,V as p,Q as g,F as ee,a3 as D}from"./vue-Cj9QYd7Z.js";import"./handsontable-Ch5RdAT_.js";import"./mapbox-gl-DQAr7S0z.js";const le={class:"d-flex justify-center algin-center"},te={class:"d-flex justify-center algin-center"},ae={class:"d-flex"},oe={class:"d-flex align-center w-50"},se={class:"d-flex align-center w-50"},ne={class:"d-flex"},ie=R({__name:"ElectronSetUp",setup(T,{expose:x}){const k=V(""),U=()=>{b.clearFileCache()},M=()=>{b.debugTools()},w=V(!0),{createColor:C}=P(),s=V(),d=V(),z=V(!1);Z(async()=>{const l=I.getCurrentMxCAD().mxdraw.getViewColor(),o=E(l).toString();s.value=C({color:o,name:o}),z.value=await b.getAppConfig("isStartOpenLastFile")||!1});const H=n=>{s.value=n},N=n=>{const l=E(n.color),o=l.red(),$=l.green(),F=l.blue();I.getCurrentMxCAD().setViewBackgroundColor(o,$,F),W.callEvent("updateBackgroundColor",new K(o,$,F))};return x({determine:()=>{s.value&&N(s.value),b.setAppConfig("isStartOpenLastFile",z.value)}}),(n,l)=>(y(),h(ee,null,[e(f,{title:n.t("627")},{default:t(()=>[e(X,{modelValue:w.value,"onUpdate:modelValue":l[0]||(l[0]=o=>w.value=o)},{default:t(()=>[e(L,{label:n.t("628"),value:!0},null,8,["label"]),e(L,{label:n.t("629"),value:!1},null,8,["label"])]),_:1},8,["modelValue"])]),_:1},8,["title"]),e(j,{class:"mt-2"},{default:t(()=>[e(S,{cols:"6",class:"mb-1"},{default:t(()=>[e(f,{title:n.t("630")},{default:t(()=>[r("div",le,[e(c,{modelValue:u(i),"onUpdate:modelValue":l[1]||(l[1]=o=>m(i)?i.value=o:null),class:"pa-0",style:{"max-width":"36px"}},null,8,["modelValue"]),e(B,{"thumb-size":10,"tick-size":1,max:128,color:"#007AD9",modelValue:u(i),"onUpdate:modelValue":l[4]||(l[4]=o=>m(i)?i.value=o:null),"hide-details":"",step:1},{prepend:t(()=>[e(v,{icon:"class:iconfont minus",size:"x-small",variant:"text",onClick:l[2]||(l[2]=o=>u(i)>0&&i.value--)})]),append:t(()=>[e(v,{icon:"class:iconfont plus",size:"x-small",variant:"text",onClick:l[3]||(l[3]=o=>u(i)<128&&i.value++)})]),_:1},8,["modelValue"])])]),_:1},8,["title"])]),_:1}),e(S,{cols:"6",class:"mb-1","align-self":"start"},{default:t(()=>[e(f,{title:n.t("631")},{default:t(()=>[r("div",te,[e(c,{modelValue:u(a),"onUpdate:modelValue":l[5]||(l[5]=o=>m(a)?a.value=o:null),class:"pa-0",style:{"max-width":"36px"}},null,8,["modelValue"]),e(B,{"thumb-size":10,"tick-size":1,color:"#007AD9",modelValue:u(a),"onUpdate:modelValue":l[8]||(l[8]=o=>m(a)?a.value=o:null),min:0,max:68,"hide-details":"",step:1},{prepend:t(()=>[e(v,{icon:"class:iconfont minus",size:"x-small",variant:"text",onClick:l[6]||(l[6]=o=>u(a)>0&&a.value--)})]),append:t(()=>[e(v,{icon:"class:iconfont plus",size:"x-small",variant:"text",onClick:l[7]||(l[7]=o=>u(a)<68&&a.value++)})]),_:1},8,["modelValue"])])]),_:1},8,["title"])]),_:1})]),_:1}),e(f,{title:n.t("632"),class:"mt-2"},{default:t(()=>[r("div",ae,[r("div",oe,[r("span",null,p(n.t("633"))+":",1),e(A,{"model-value":s.value,"onUpdate:modelValue":H,"menus-props":{maxHeight:"600"}},null,8,["model-value"])]),r("div",se,[r("span",null,p(n.t("634"))+":",1),e(A,{"model-value":d.value,"menus-props":{maxHeight:"600"}},null,8,["model-value"])])])]),_:1},8,["title"]),e(f,{title:n.t("286"),class:"mt-2"},{default:t(()=>[r("div",ne,[e(c,{modelValue:u(a),"onUpdate:modelValue":l[9]||(l[9]=o=>m(a)?a.value=o:null),class:"pa-0"},{prepend:t(()=>[g(p(n.t("635"))+": ",1)]),_:1},8,["modelValue"]),e(c,{modelValue:u(a),"onUpdate:modelValue":l[10]||(l[10]=o=>m(a)?a.value=o:null),class:"pa-0 ml-4"},{prepend:t(()=>[g(p(n.t("636"))+"("+p(n.t("637"))+"): ",1)]),_:1},8,["modelValue"])])]),_:1},8,["title"]),e(c,{"model-value":k.value,readonly:"",class:"mt-2"},{prepend:t(()=>[g(p(n.t("638")),1)]),_:1},8,["model-value"]),e(Y,{label:n.t("639"),modelValue:z.value,"onUpdate:modelValue":l[11]||(l[11]=o=>z.value=o),class:"mt-2"},null,8,["label","modelValue"]),e(_,{class:"mt-2 mr-2",onClick:U},{default:t(()=>[g(p(n.t("640")),1)]),_:1}),e(_,{class:"mt-2",onClick:M},{default:t(()=>[g(p(n.t("641")),1)]),_:1})],64))}}),ue=O(ie,[["__scopeId","data-v-48f8e479"]]),de={class:"px-3"},re={class:"d-flex justify-center algin-center"},me={class:"d-flex justify-center algin-center"},pe=R({__name:"index",setup(T){const{isShow:x,showDialog:k}=G(!1,"Mx_SetAppDialog"),U=V(),w=[{name:"确定",fun:()=>{Q(),U.value?.determine(),k(!1)},primary:!0},{name:"关闭",fun:()=>k(!1)}];return(C,s)=>(y(),D(J,{title:C.t("293"),"max-width":"600",modelValue:u(x),"onUpdate:modelValue":s[8]||(s[8]=d=>m(x)?x.value=d:null),footerBtnList:w},{default:t(()=>[r("div",de,[u(q)()?(y(),D(ue,{key:0,ref_key:"electronSetUp",ref:U},null,512)):(y(),D(j,{key:1},{default:t(()=>[e(S,{cols:"6",class:"mb-1"},{default:t(()=>[e(f,{title:C.t("630")},{default:t(()=>[r("div",re,[e(c,{modelValue:u(i),"onUpdate:modelValue":s[0]||(s[0]=d=>m(i)?i.value=d:null),class:"pa-0",style:{"max-width":"36px"}},null,8,["modelValue"]),e(B,{"thumb-size":10,"tick-size":1,max:128,color:"#007AD9",modelValue:u(i),"onUpdate:modelValue":s[3]||(s[3]=d=>m(i)?i.value=d:null),"hide-details":"",step:1},{prepend:t(()=>[e(v,{icon:"class:iconfont minus",size:"x-small",variant:"text",onClick:s[1]||(s[1]=d=>u(i)>0&&i.value--)})]),append:t(()=>[e(v,{icon:"class:iconfont plus",size:"x-small",variant:"text",onClick:s[2]||(s[2]=d=>u(i)<128&&i.value++)})]),_:1},8,["modelValue"])])]),_:1},8,["title"])]),_:1}),e(S,{cols:"6",class:"mb-1","align-self":"start"},{default:t(()=>[e(f,{title:C.t("631")},{default:t(()=>[r("div",me,[e(c,{modelValue:u(a),"onUpdate:modelValue":s[4]||(s[4]=d=>m(a)?a.value=d:null),class:"pa-0",style:{"max-width":"36px"}},null,8,["modelValue"]),e(B,{"thumb-size":10,"tick-size":1,color:"#007AD9",modelValue:u(a),"onUpdate:modelValue":s[7]||(s[7]=d=>m(a)?a.value=d:null),min:0,max:68,"hide-details":"",step:1},{prepend:t(()=>[e(v,{icon:"class:iconfont minus",size:"x-small",variant:"text",onClick:s[5]||(s[5]=d=>u(a)>0&&a.value--)})]),append:t(()=>[e(v,{icon:"class:iconfont plus",size:"x-small",variant:"text",onClick:s[6]||(s[6]=d=>u(a)<68&&a.value++)})]),_:1},8,["modelValue"])])]),_:1},8,["title"])]),_:1})]),_:1}))])]),_:1},8,["title","modelValue"]))}}),ze=O(pe,[["__scopeId","data-v-a6e01d76"]]);export{ze as default};
