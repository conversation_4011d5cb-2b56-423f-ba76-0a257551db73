import {MxCADUiPrPoint, MxCpp} from "mxcad";

//画图片
async function Mx_drawImg() {
    // 设置图片插入点
    const getPoint = new MxCADUiPrPoint();
    getPoint.setMessage("\n指定插入点:");
    let pt = await getPoint.go();
    if (!pt) return;
   
    const mxcad = MxCpp.getCurrentMxCAD();
    // 设置图片地址
    let imagUrl = "https://cdn.pixabay.com/photo/2022/11/15/12/23/winter-7593872_960_720.jpg";
    mxcad.loadImage(imagUrl,(image)=>{
        if(!image ){ 
        console.log("loadImage failed");
        return;
        }
        // 设置图片宽高
        let width = mxcad.mxdraw.viewCoordLong2Cad(100);
        let height  = (image.height /  image.width) * width;
        mxcad.drawImage((pt as any).x,(pt as any).y,width,height,0,imagUrl,true,image.width,image.height);
        mxcad.updateDisplay();
    });
};

// 调用画图片的方法
Mx_drawImg();