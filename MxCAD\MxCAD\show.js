const fs = require('fs');

// 读取 JSON 文件
fs.readFile('../dist/data.json', 'utf8', (err, data) => {
    if (err) {
        console.error('读取文件出错:', err);
        return;
    }

    // 解析 JSON 数据
    let jsonData = JSON.parse(data);

    // 修改数据
    jsonData.age = 30; // 修改年龄
    jsonData.city = "Los Angeles"; // 修改城市

    // 将修改后的数据写回 JSON 文件
    fs.writeFile('../dist/data.json', JSON.stringify(jsonData, null, 4), 'utf8', (err) => {
        if (err) {
            console.error('写入文件出错:', err);
            return;
        }
        console.log('JSON 文件已成功更新!');
    });
});
