<template>
  <router-view />
</template>

<script setup>
import useSettingsStore from "@/store/modules/settings";
import { handleThemeStyle } from "@/utils/theme";
import { setToken } from "@/utils/auth.js";

// 获取 token
const urlParams = new URLSearchParams(window.location.search);
console.log("🚀 ~ urlParams:", urlParams)
const token1 = urlParams.get("token");
console.log("🚀 ~ token1:", token1)
let token
if(token1)  token=decodeURIComponent(token1.replace(/ /g,'+'))
console.log("🚀 ~ token:", token)
if (token) {
  setToken(token);
}
onMounted(() => {
  nextTick(() => {
    // 初始化主题样式
    handleThemeStyle(useSettingsStore().theme);
  });
});
</script>