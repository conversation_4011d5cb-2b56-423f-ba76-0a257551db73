//绘制电缆中间接头
import {
    getEquipmentState,
    saveEquipmentInfos,
  } from "@/api/desginManage/draw.js";
import { ElMessage } from "element-plus";
import { getEquipmentModel1 } from "@/views/pages/online/saveModelInfo.js";

//中间接头的数据下标
export const cableHead = async (arr,cableForm,zjtList,index) => {
let sdkIndex = arr.findIndex(
    (item) => item.CLASS_NAME === "PWCableJointPSR"
  );
  let legendGuidKey = arr[sdkIndex].DEV_ID;
  const taskId = new URLSearchParams(new URL(window.location.href).search).get(
    "id"
  );
  let inList = [];
  let outList = [];
  //in中间接头前一段数据
  let inIndex = arr.findIndex(
    (item) =>
      item.START_DEV_ID === legendGuidKey &&
      item.CLASS_NAME === "PWCableSecPSR"
  );
  inList.push(arr[inIndex].DEV_ID);
  //out中间接头后一段数据
  let outIndex = arr.findIndex(
    (item) =>
      item.END_DEV_ID === legendGuidKey &&
      item.CLASS_NAME === "PWCableSecPSR"
  );
  outList.push(arr[outIndex].DEV_ID);
  //中间接头前一段坐标
  let coordinates=arr[inIndex].SHAPE.match(/(\d+\.\d+)\s+(\d+\.\d+)/g)
  //中间接头后一段坐标
  let outCoordinates=arr[outIndex].SHAPE.match(/(\d+\.\d+)\s+(\d+\.\d+)/g)
  // 获取状态
  let legendState = "";
  getEquipmentState({ equipmentId: outList[0], taskId: taskId }).then(
    (res) => {
      console.log(res, res.data, "返回");
      if (res.data !== "") {
        legendState = res.data;
      } else {
        legendState = "Original";
      }
      console.log(legendState, "state");
      const params = [];
      const param1 = getEquipmentModel1("Point", `${arr[sdkIndex].X} ${arr[sdkIndex].Y}`, {
        moduleId: cableForm.cable1, // 顶层id
        legendTypeKey: zjtList[index].legendTypeKey, // 图元类型
        legendState: legendState, // 状态
        legendGuidKey, // 设备id
        engineeringId: taskId,
      })
      param1.addFlag = arr[sdkIndex].addFlag?arr[sdkIndex].addFlag:"null"
      param1.topologyrelations.AssociatedIn = inList;
      param1.topologyrelations.AssociatedOut = outList;
      param1.topologyrelations.AssociatedParent = [inList[0],outList[0]]
      params.push(param1);
      const param2 = getEquipmentModel1("Line", coordinates, {
        legendState, // 状态
        legendGuidKey: inList[0], // 设备id
        engineeringId: taskId,
      })
      param2.addFlag = arr[inIndex].addFlag?arr[inIndex].addFlag:"null"
      param2.topologyrelations.AssociatedOut = [legendGuidKey];
      param2.privatepropertys = {
        Span: arr[inIndex].LENGTH.toFixed(2),
        TDDJ: arr[inIndex].LENGTH.toFixed(2),
      }
      params.push(param2);
      const param3 = getEquipmentModel1("Line", outCoordinates, {
        legendState, // 状态
        legendGuidKey: outList[0], // 设备id
        engineeringId: taskId,
      })
      param3.addFlag = arr[outIndex].addFlag?arr[outIndex].addFlag:"null"
      param3.topologyrelations.AssociatedIn = [legendGuidKey];
      param3.privatepropertys = {
        Span: arr[outIndex].LENGTH.toFixed(2),
        TDDJ: arr[outIndex].LENGTH.toFixed(2),
      }
      params.push(param3);
      saveEquipmentInfos(params).then((res) => {
        if (res.code === 200) {
            ElMessage.success("保存成功");
        } else {
            ElMessage.success(res.msg);
        }
      });
    }
  );
}