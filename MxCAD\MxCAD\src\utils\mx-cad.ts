
import {McGePoint3d} from "mxcad";



/* 根据起点坐标、距离和角度计算新坐标
 * @param {McGePoint3d} pt - 起点坐标
 * @param {number} d - 距离
 * @param {number} radian - 角度（以弧度为单位）
 * @returns {McGePoint3d} - 新坐标
 */
export function calculateNewCoordinate(pt: McGePoint3d, d: number, radian: number) {
    // 将角度转换为弧度
    const point: McGePoint3d = pt.clone();

    // 使用三角公式计算新坐标
    const newX: number = point.x + d * Math.cos(radian);
    const newY: number = point.y + d * Math.sin(radian);
    point.x = newX;
    point.y = newY;

    return point;
}

// 角度转弧度
export function convertDegreesToRadians(degrees: number) {
    return degrees * Math.PI / 180;
}

/**
 * 将16进制颜色转换成rgb
 * @param hexStr 杆塔类型的颜色
 */
export function colorHexToRgb (hexStr: string) {
    if(!hexStr || hexStr === 'ByBlock') return undefined
    //rgb颜色值的正则表达式
    const reg = /^(rgba|rgb|RGBA|RGB)\([\s]*[0-9]+[\s]*,[\s]*[0-9]+[\s]*,[\s]*[0-9]+[\s]*(,[\s]*[0-9.]+[\s]*)*\)$/;
    let letcolorNew;
    let conststr;
    let colorNew;
    if (reg.test(hexStr)) {
        return hexStr;
    } else {
        hexStr = hexStr.toLowerCase()
        if (hexStr.length === 4) {
            letcolorNew = "#";
            for (let i = 1; i < 4; i += 1) {
                conststr = hexStr.slice(i, i + 1);
                let str;
                colorNew += str + str;
            }
            hexStr = colorNew;
        }
        const rgbArray = [];
        for (let i = 1; i < hexStr.length; i += 2) {
            if (i < 7) {
                rgbArray.push(parseInt("0x" + hexStr.slice(i, i + 2)));
            }
            if (i >= 7) {
                conststr = hexStr.slice(i, i + 2);
                let str;
                rgbArray.push(/^[a-f0-9]{2}$/.test(str) ? parseInt(`0x${str}`) / 255 : (Number(str) / 100))
            }
        }
        return rgbArray
    }
}