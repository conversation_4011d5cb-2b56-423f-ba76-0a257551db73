<template>
  <data-dialog
      @close="closeDialog"
      dataWidth="830px"
      v-if="appStore.mapIndex == '线夹匹配'"
  >
    <template #header>
      <h4 style="margin-left: 10px; margin-top: 0px; line-height: 39px">
        线夹匹配
      </h4>
    </template>
    <template #body>
      <div class="photo-box">
        <div class="photo-right">
          <el-tabs
              v-model="activeName"
              type="card"
              class="demo-tabs"
              @tab-click="handleClick"
          >
            <el-tab-pane label="耐张线夹" name="first">
              <div style="background-color:#fff;border-radius: 5px;padding: 0 10px;color: #515a6e;font-size: 14px;display: flex;align-items: center">
                <el-checkbox v-model="checkedNz">
                  <template #default>
                    <span style="color: #515a6e">匹配时不改变线夹类别</span>
                  </template>
                </el-checkbox>
                <div style="margin-left: 15px;cursor: pointer">保存</div>
                <div style="margin-left: 15px;cursor: pointer">自动匹配线夹</div>
              </div>
              <el-table
                  :data="tableDataNz"
                  :span-method="objectSpanMethodNz"
                  border
                  height="360px"
                  style="width: 100%;"
              >
                <el-table-column prop="wirecliptype" label="线夹类别" width="150px"/>
                <el-table-column prop="materialsprojectid" label="规格型号">
                  <template #default="scope">
                    <el-select
                        v-model="scope.row.materialsprojectid"
                        placeholder=""
                        size="small"
                        style="width: 300px"
                    >
                      <el-option
                          v-for="item in scope.row.option"
                          :key="item.materialsprojectid"
                          :label="item.spec"
                          :value="item.materialsprojectid"
                      />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column prop="suitablesection" label="适用导线截面" width="110px" />
              </el-table>
            </el-tab-pane>
            <el-tab-pane label="并沟线夹" name="second">
              <div style="background-color:#fff;border-radius: 5px;padding: 0 10px;color: #515a6e;font-size: 14px;display: flex;align-items: center">
                <el-checkbox v-model="checkedNz">
                  <template #default>
                    <span style="color: #515a6e">匹配时不改变线夹类别</span>
                  </template>
                </el-checkbox>
                <div style="margin-left: 15px;cursor: pointer">保存</div>
                <div style="margin-left: 15px;cursor: pointer">自动匹配线夹</div>
              </div>
              <el-table
                  :data="tableDataBg"
                  :span-method="objectSpanMethodBg"
                  border
                  height="360px"
                  style="width: 100%;"
              >
                <el-table-column prop="wirecliptype" label="线夹类别" width="150px">
                  <template #default="scope">
                    <el-checkbox v-model="scope.row.checked">
                      <template #default>
                        <span style="color: #515a6e">{{ scope.row.wirecliptype }}</span>
                      </template>
                    </el-checkbox>
                  </template>
                </el-table-column>
                <el-table-column prop="materialsprojectid" label="规格型号">
                  <template #default="scope">
                    <el-select
                        v-model="scope.row.materialsprojectid"
                        placeholder=""
                        size="small"
                        style="width: 200px"
                    >
                      <el-option
                          v-for="item in scope.row.option"
                          :key="item.materialsprojectid"
                          :label="item.spec"
                          :value="item.materialsprojectid"
                      />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column prop="mainsection" label="主导线" width="110px" />
                <el-table-column prop="subsection" label="支导线" width="110px" />
              </el-table>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </template>
  </data-dialog>
</template>
<script setup>
import DataDialog from "@/components/DataDialog/index.vue";
import useAppStore from "@/store/modules/app.js";
import {
  GetDataBingGou,
  GetDataNaiZhang,
  getWireClipMaterialsProject,
  materialswireclipSelectList
} from "@/api/desginManage/ToolManagement.js";
const appStore = useAppStore();
const closeDialog = () => {
  appStore.mapIndex = "";
};
const activeName = ref('first')
const handleClick = () => {

}
const checkedNz = ref(true)
const options = ref({
  optionsNz: [],
  optionsBg: [],
})
const tableDataNz = ref([
  { name: '耐张线夹-螺栓型', type: '', num: '25-50'},
  { name: '耐张线夹-螺栓型', type: '', num: '70-95'},
  { name: '耐张线夹-螺栓型', type: '', num: '120-150'},
  { name: '耐张线夹-螺栓型', type: '', num: '185-240'},
  { name: '耐张线夹-楔形绝缘（NXL）', type: '', num: '25-50'},
  { name: '耐张线夹-楔形绝缘（NXL）', type: '', num: '70-95'},
  { name: '耐张线夹-楔形绝缘（NXL）', type: '', num: '120-150'},
  { name: '耐张线夹-楔形绝缘（NXL）', type: '', num: '185-240'},
  { name: '耐张线夹-楔形绝缘（NXJG）', type: '', num: '25-50'},
  { name: '耐张线夹-楔形绝缘（NXJG）', type: '', num: '70-95'},
  { name: '耐张线夹-楔形绝缘（NXJG）', type: '', num: '120-150'},
  { name: '耐张线夹-楔形绝缘（NXJG）', type: '', num: '185-240'},
])
const tableDataBg = ref([
  { checked: true, name: '楔形并沟线夹', type: '', num1: '35-50', num2: '35-50'},
  { checked: true, name: '楔形并沟线夹', type: '', num1: '70-95', num2: '35-50'},
  { checked: true, name: '楔形并沟线夹', type: '', num1: '70-95', num2: '70-95'},
  { checked: true, name: '楔形并沟线夹', type: '', num1: '120-150', num2: '35-50'},
  { checked: true, name: '楔形并沟线夹', type: '', num1: '120-150', num2: '70-95'},
  { checked: true, name: '楔形并沟线夹', type: '', num1: '185-240', num2: '35-50'},
  { checked: true, name: '楔形并沟线夹', type: '', num1: '185-240', num2: '70-95'},
  { checked: true, name: '楔形并沟线夹', type: '', num1: '185-240', num2: '120-150'},
  { checked: true, name: '楔形并沟线夹', type: '', num1: '185-240', num2: '185-240'},
  { checked: false, name: '异形并沟线夹', type: '', num1: '', num2: ''},
  { checked: false, name: '异形并沟线夹', type: '', num1: '', num2: ''},
  { checked: false, name: '异形并沟线夹', type: '', num1: '', num2: ''},
  { checked: false, name: '异形并沟线夹', type: '', num1: '', num2: ''},
])
// 合并单元格
const objectSpanMethodBg = ({row, column, rowIndex, columnIndex,}) => {
  if (column.property === 'wirecliptype') {
    if (rowIndex > 0) {
      const prevRow = tableDataBg.value[rowIndex - 1];
      if (prevRow[column.property] === row[column.property]) {
        return { rowspan: 0, colspan: 0 };
      }
    }
    let rowspan = 1;
    for (let i = rowIndex + 1; i < tableDataBg.value.length; i++) {
      if (tableDataBg.value[i][column.property] === row[column.property]) {
        rowspan++;
      } else {
        break;
      }
    }
    return { rowspan, colspan: 1 };
  }
}
const objectSpanMethodNz = ({row, column, rowIndex, columnIndex,}) => {
  if (column.property === 'wirecliptype') {
    if (rowIndex > 0) {
      const prevRow = tableDataNz.value[rowIndex - 1];
      if (prevRow[column.property] === row[column.property]) {
        return { rowspan: 0, colspan: 0 };
      }
    }
    let rowspan = 1;
    for (let i = rowIndex + 1; i < tableDataNz.value.length; i++) {
      if (tableDataNz.value[i][column.property] === row[column.property]) {
        rowspan++;
      } else {
        break;
      }
    }
    return { rowspan, colspan: 1 };
  }
}
const dataList = async () => {
  // 并行获取两个数据
  const [naiZhangData, bingGouData] = await Promise.all([GetDataNaiZhang(), GetDataBingGou()]);
  // 处理 naiZhang 数据
  const wireclipTypeKeys = [...new Set(naiZhangData.data.map(item => item.wirecliptypekey))];
  // 获取相关材料信息
  const wireClipProjectRes = await getWireClipMaterialsProject(wireclipTypeKeys);
  tableDataNz.value = naiZhangData.data.map(item => {
    const matchingOptions = wireClipProjectRes.data
        .map(itemmap => itemmap[item.wirecliptypekey])
        .filter(Boolean); // 过滤掉 null 和 undefined
    return {
      ...item,
      option: matchingOptions.length > 0 ? matchingOptions[0] : []  // 如果没有匹配项，返回空数组
    };
  });

  const wireclipTypeKeysbingGou = [...new Set(bingGouData.data.map(item => item.wirecliptypekey))];
  // 获取相关材料信息
  const wireClipProjectResbingGou = await getWireClipMaterialsProject(wireclipTypeKeysbingGou);
  // 处理 bingGou 数据
  tableDataBg.value = bingGouData.data.map(item => {
    const matchingOptions = wireClipProjectResbingGou.data
        .map(itemmap => itemmap[item.wirecliptypekey])
        .filter(Boolean); // 过滤掉 null 和 undefined
    return {
      ...item,
      option: matchingOptions.length > 0 ? matchingOptions[0] : []  // 如果没有匹配项，返回空数组
    };
  });
  console.log(tableDataNz.value, tableDataBg.value)
}
watch(
    () => appStore.mapIndex,
    (newInfo, oldInfo) => {
      console.log(newInfo, oldInfo);
      if (newInfo == "线夹匹配") {
        dataList()
      }
    }
);
</script>
<style scoped lang="scss">
@use '../../index' as *;
::v-deep .el-tabs__header {
  margin: 0;
}
</style>