import { MxCpp, McGePoint3d } from "mxcad";

//画图片
function drawImg() {
  const mxcad = MxCpp.getCurrentMxCAD();
  // 创建新画布
  mxcad.newFile();

  //图片插入点
  const pt = new McGePoint3d(0, 0, 0)
  //图片地址
  let imagUrl = "https://cdn.pixabay.com/photo/2022/11/15/12/23/winter-7593872_960_720.jpg";
  mxcad.loadImage(imagUrl, (image) => {
    if (!image) {
      console.log("loadImage failed");
      return;
    }
    let width = mxcad.mxdraw.viewCoordLong2Cad(100);
    let height = (image.height / image.width) * width;
    mxcad.drawImage((pt as any).x, (pt as any).y, width, height, 0, imagUrl,true,image.width,image.height);
    mxcad.updateDisplay();
  });
};

// 调用绘图片方法
drawImg();