<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true">
         <el-form-item label="工程名称" prop="projectName">
            <el-input
               v-model="queryParams.projectName"
               placeholder="请输入工程名称"
               clearable
               style="width: 200px"
            />
         </el-form-item>
         <el-form-item label="工程编号" prop="projectCode">
            <el-input
               v-model="queryParams.projectCode"
               placeholder="请输入工程编号"
               clearable
               style="width: 200px"
            />
         </el-form-item>
         <el-form-item label="地市公司" prop="cityOrganisationName">
            <el-input
               v-model="queryParams.cityOrganisationCode"
               placeholder="请输入地市公司"
               clearable
               style="width: 200px"
            />
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
         </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
         <el-col :span="1.5">
            <el-button
               type="primary"
               plain
               icon="Plus"
               @click="handleAdd"
            >新增</el-button>
            <el-button
               type="primary"
               plain
               icon="Plus"
               @click="handleChildAdd"
            >新增子项目</el-button>
         </el-col>
      </el-row>
      <el-table v-loading="loading" stripe highlight-current-row :data="versionList" @current-change="handleCurrentChange">
        <el-table-column type="expand">
          <template #default="props">
            <el-table :data="props.row.children" stripe highlight-current-row>
              <el-table-column label="工程编号" align="center" width="180" prop="projectCode" />
              <el-table-column label="工程名称" show-overflow-tooltip align="center" prop="projectName" />
              <el-table-column label="市公司" align="center" width="180" prop="cityOrganisationName" />
              <el-table-column label="县公司" align="center" width="180" prop="countyOrganisationName" />
              <el-table-column label="电压等级" align="center" width="100" prop="voltageLevel" />
              <el-table-column label="建项年度" align="center" width="100" prop="createYear" />
              <el-table-column label="项目阶段" align="center" width="100" prop="stage" />
              <el-table-column label="操作" width="240" align="center" class-name="small-padding fixed-width">
                <template #default="scope">
                  <el-button link type="primary" icon="List" @click="handleRelease(scope.row)" :disabled = "!(scope.row.state != '进行中')">发布任务</el-button>
                  <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:post:edit']">修改</el-button>
                  <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:post:remove']">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </template>
        </el-table-column>
        <!-- <el-table-column type="index" width="55" align="center" /> -->
        <el-table-column label="项目编号" align="center" width="180" prop="projectCode" />
        <el-table-column label="项目名称" show-overflow-tooltip align="center" prop="projectName" />
        <el-table-column label="市公司" align="center" width="180" prop="cityOrganisationName" />
        <el-table-column label="县公司" align="center" width="180" prop="countyOrganisationName" />
        <el-table-column label="电压等级" align="center" width="100" prop="voltageLevel" />
        <el-table-column label="建项年度" align="center" width="100" prop="createYear" />
        <el-table-column label="项目阶段" align="center" width="100" prop="stage" />
        <el-table-column label="操作" width="180" align="center" class-name="small-padding fixed-width">
          <template #default="scope">           
            <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:post:edit']">修改</el-button>
            <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:post:remove']">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
         v-show="total > 0"
         :total="total"
         v-model:page="queryParams.pageNum"
         v-model:limit="queryParams.pageSize"
         @pagination="getList"
      />
      <!--新增或修改对话框-->
      <el-dialog :title="title" v-model="open" width="55%" append-to-body>
         <el-form :model="form" :rules="rules" ref="versionRef" label-width="120px">
          <el-row>
            <el-col :span="8">
              <el-form-item label="工程编码" prop="projectCode">
                <el-input v-model="form.projectCode" placeholder="请输入工程编码" maxlength="30" />
              </el-form-item>
            </el-col>
            <el-col :span="16">
              <el-form-item label="工程名称" prop="projectName">
                <el-input v-model="form.projectName" placeholder="请输入工程名称" maxlength="30" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="所属市" prop="cityOrganisationCode">
                <el-select v-model="form.cityOrganisationCode" @change="provinceChange" placeholder="请选择">
                  <el-option v-for="dict in provinceOptions" :key="dict.deptId" :label="dict.deptName" :value="dict.deptId"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="建项年度" prop="createYear">
                <el-date-picker
                  v-model="form.createYear"
                  type="year"
                  value-format="YYYY"
                  format="YYYY"
                  placeholder="请选择建项年度"
                />
              </el-form-item>
              <el-form-item label="项目类型" prop="projectType">
                <el-select v-model="form.projectType" placeholder="请选择">
                  <el-option v-for="dict in ProjectTypeOptions" :key="dict.projectTypeId" :label="dict.projectTypeName" :value="dict.projectTypeId"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="所属县" prop="countyOrganisationCode">
                <el-select v-model="form.countyOrganisationCode" @change="cityChange" placeholder="请选择">
                  <el-option v-for="dict in CityCompanyOptions" :key="dict.deptId" :label="dict.deptName" :value="dict.deptId"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="深度规范类型" prop="projectDepthType">
                <el-select v-model="form.projectDepthType" multiple clearable placeholder="请选择">
                  <el-option v-for="dict in ProjectDepthTypeOptions" :key="dict.projectDepthTypeId" :label="dict.projectDepthTypeName" :value="dict.projectDepthTypeId"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="是否非典设工程">
                <el-select v-model="form.isStandard" placeholder="请选择">
                  <el-option key="是" label="是" value="是"></el-option>
                  <el-option key="否" label="否" value="否"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="供电所" prop="powersupplyStationOrganisationName">
                <el-select v-model="form.powersupplyStationOrganisationName" placeholder="请选择">
                  <el-option v-for="dict in substationOptions" :key="dict.deptId" :label="dict.deptName" :value="dict.deptId"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="电压等级" prop="voltageLevel">
                <el-select v-model="form.voltageLevel" placeholder="请选择">
                  <el-option key="0.4kV" label="0.4kV" value="0.4kV"></el-option>
                  <el-option key="10kV" label="10kV" value="10kV"></el-option>
                  <el-option key="20kV" label="20kV" value="20kV"></el-option>
                  <el-option key="35kV" label="35kV" value="35kV"></el-option>
                  <el-option key="无" label="无" value="无"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="资产性质" prop="assetNature">
                <el-select v-model="form.assetNature" placeholder="请选择">
                  <el-option key="业主投资" label="业主投资" value="业主投资"></el-option>
                  <el-option key="用户投资" label="用户投资" value="用户投资"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
         </el-form>
         <template #footer>
         <div class="dialog-footer">
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
         </div>
         </template>
      </el-dialog>
  </div>
</template>  
<script setup name="dataVersion">
const { proxy } = getCurrentInstance();
import { getList as getListByEngineer, addOrUpdate, delEngineering } from "@/api/taskManage/taskProjectInfo";
// import { addManage } from "@/api/taskManage/taskManage";
import { addOrUpdate as addManage } from "@/api/desginManage/GccsDialog";
import { listDept } from "@/api/system/dept"
import { ref } from 'vue';
import useUserStore from '@/store/modules/user'

const userStore = useUserStore()
const loading = ref(false)
const total = ref(0)
const versionList = ref([])
const title = ref("");
const open = ref(false)
const provinceOptions = ref([])
const CityCompanyOptions = ref([])
const substationOptions = ref([])
const ProjectTypeOptions = ref([
  {projectTypeId:'01',projectTypeName:'变电站出线配套10KV（20KV）工程项目'},
  {projectTypeId:'02',projectTypeName:'配电自动改造'},
  {projectTypeId:'03',projectTypeName:'业扩接入电网配套改造'},
  {projectTypeId:'04',projectTypeName:'其他'},
  {projectTypeId:'05',projectTypeName:'网架优化完善'},
  {projectTypeId:'06',projectTypeName:'供电区域优化'},
  {projectTypeId:'07',projectTypeName:'配电增容布点'},
  {projectTypeId:'08',projectTypeName:'老旧或缺陷设备改造'},
  {projectTypeId:'09',projectTypeName:'架空线路绝缘化改造'},
])
const ProjectDepthTypeOptions = ref([
  {projectDepthTypeId:'架空',projectDepthTypeName:'架空'},
  {projectDepthTypeId:'电缆',projectDepthTypeName:'电缆'},
  {projectDepthTypeId:'配电站房',projectDepthTypeName:'配电站房'},
])
const data = reactive({
  form: {
    objId:'',
    projectCode: undefined,
    projectName: undefined,
    projectCodeParent: undefined,
    compilingOrganisationName: undefined,
    compilerName: undefined,
    compilerNumber: undefined,
    isStandard: undefined,
    cityOrganisationName: undefined,
    countyOrganisationName: undefined,
    cityOrganisationCode: undefined,
    countyOrganisationCode: undefined,
    createYear: undefined,
    voltageLevel: undefined,
    assetNature: undefined,
    projectType: undefined,
    powersupplyStationOrganisationName: undefined,
    projectDepthType: undefined,
    state: undefined,
    rollback: undefined,
    stage: undefined
  },
  queryParams: {
   pageNum: 1,
   pageSize: 10,
   projectName: undefined,
   projectCode: undefined,
   cityOrganisationCode: undefined
  },
  companyInfo: {
   companyId: undefined,
   companyName: undefined,
  },
  rules: {
    projectCode: [{ required: true, message: "工程编码不能为空", trigger: "blur" }],
    projectName: [{ required: true, message: "工程名称不能为空", trigger: "blur" }],
    cityOrganisationCode: [{ required: true, message: "请选择所属市", trigger: "blur" }],
    countyOrganisationCode: [{ required: true, message: "请选择所属县", trigger: "blur" }],
    createYear: [{ required: true, message: "建项年份不能为空", trigger: "blur" }],
    voltageLevel: [{ required: true, message: "请选择电压等级", trigger: "blur" }],
    assetNature: [{ required: true, message: "请选择资产性质", trigger: "blur" }],
    projectType: [{ required: true, message: "请选择项目类型", trigger: "blur" }],
    powersupplyStationOrganisationName: [{ required: true, message: "请选择供电所", trigger: "blur" }],
    projectDepthType: [{ required: true, message: "请选择深度规范类型", trigger: "blur" }]
  },
});
const { queryParams,form,companyInfo,rules } = toRefs(data);

/** 查询省公司下拉树结构 */
function getProvinceTree() {
   const queryParams = {
    parentId: 100
   }
   listDept(queryParams).then(response => {
    provinceOptions.value = response.data;      
  });
};

/**查询事件 */
function handleQuery() {
   queryParams.value.pageNum = 1
   getList()
}

/**重置查询参数 */
function resetQuery() {
   queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    companyId: undefined,
    versionName: undefined
  }
  getList()
}

/**发布任务 */
function handleRelease(row) {
  let _objId = row.objId
  row.objId = ''
  // 暂时处理调用两个接口
  addOrUpdate(row).then(() => {
    addManage(row).then(() => {
      row.objId = _objId
      row.state = '1'
      proxy.$modal.msgSuccess("发布成功");
    })    
  })
}

/**新增工程 */
function handleAdd() {
  getProvinceTree()
  open.value = true
  form.value.projectCodeParent = ""
  title.value = '新增工程信息'
}

/**选中列表 */
function handleCurrentChange(row) {
  if (row) {
    form.value.projectCodeParent = row.objId
  }
}

/**新增子工程 */
function handleChildAdd() {
  getProvinceTree()
  open.value = true
  title.value = '新增子工程信息'
}

/**修改版本 */
function handleUpdate(row) {
  getProvinceTree()
  open.value = true
  title.value = '修改工程信息'
  form.value = row
  console.log(row)
  if (form.value.cityOrganisationCode) {
    provinceChange(form.value.cityOrganisationCode)
  }
  form.value.cityOrganisationCode = parseInt(form.value.cityOrganisationCode)
  if (form.value.countyOrganisationCode) {
    cityChange(form.value.countyOrganisationCode)
  }
  form.value.countyOrganisationCode = parseInt(form.value.countyOrganisationCode)
  form.value.powersupplyStationOrganisationName = parseInt(form.value.powersupplyStationOrganisationName)
  if (form.value.projectDepthType.length > 0) {
    form.value.projectDepthType = form.value.projectDepthType.split(',')
  }
}

/**删除版本 */
function handleDelete(row){
   proxy.$modal.confirm('是否确认删除项目编号为"' + row.projectCode + '"的数据项?').then(function() {
    const param = {
      objId: row.objId
    }
    return delEngineering(param);
  }).then((req) => {
    console.log(req)
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch((err) => {
    console.log(req)
  });
}

/**获取工程信息数据 */
function getList() {
  loading.value = true;
  getListByEngineer(queryParams.value).then(response => {
    versionList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  }).catch(() => {
    loading.value = false;
  })
}

/**保存新版本 */
function submitForm() {
   proxy.$refs["versionRef"].validate(valid => {
      if (valid) {
         const provinceOption = provinceOptions.value.find(option => option.deptId == form.value.cityOrganisationCode)
         form.value.cityOrganisationName = provinceOption.deptName
         const cityCompanyOption = CityCompanyOptions.value.find(option => option.deptId == form.value.countyOrganisationCode)
         form.value.countyOrganisationName = cityCompanyOption.deptName
         let _str = '';
         form.value.projectDepthType.forEach(element => {
          _str += element + ','
         });
         form.value.projectDepthType = _str.substring(0,_str.lastIndexOf(','))
         addOrUpdate(form.value).then(res => {
          getList()
          proxy.$modal.msgSuccess("保存成功");
          cancel()
        })        
      }
   })
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
};

/** 重置操作表单 */
function reset() {
  form.value = {
    objId:'',
    projectCode: undefined,
    projectName: undefined,
    projectCodeParent: undefined,
    compilingOrganisationName: undefined,
    compilerName: undefined,
    compilerNumber: undefined,
    isStandard: undefined,
    cityOrganisationName: undefined,
    countyOrganisationName: undefined,
    cityOrganisationCode: undefined,
    countyOrganisationCode: undefined,
    createYear: undefined,
    voltageLevel: undefined,
    assetNature: undefined,
    projectType: undefined,
    powersupplyStationOrganisationName: undefined,
    projectDepthType: undefined,
    state: undefined,
    rollback: undefined,
    stage: undefined
  };
  proxy.resetForm("versionRef");
};

/** 选中市公司事件 */
function provinceChange(row) {
   const queryParams = {
      parentId: row
   }
   listDept(queryParams).then(res => {
      CityCompanyOptions.value = res.data
   })
}

/** 选中县公司事件 */
function cityChange(row) {
  const queryParams = {
    parentId: row
   }
   listDept(queryParams).then(res => {
    substationOptions.value = res.data
   })
}
getList()
</script>