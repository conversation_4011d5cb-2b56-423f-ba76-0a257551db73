import request from '@/utils/request'

//交叉跨域
export function GetSpanCategory() {
    return request({
        url: '/jsObjDrawOverlappingAcross/GetSpanCategory',
        method: 'get',
    })
}

//柱上设备绘制
export function GetPageData() {
    return request({
        url: '/jsObjIncreaseRodEquipment/GetPageData',
        method: 'get',
    })
}

//柱上设备绘制 --select
export function GetRodEquipmentNumber(data) {
    return request({
        url: '/jsObjIncreaseRodEquipment/GetRodEquipmentNumber',
        method: 'get',
        params: data
    })
}

//增加附属设施类型

export function getFacilitiesType() {
    return request({
        url: '/jsObjIncreaseRodEquipment/getFacilitiesType',
        method: 'get',
    })
}

//增加附属设施设备型号

export function getModuleByParentKey(data) {
    return request({
        url: '/jsObjIncreaseRodEquipment/getModuleByParentKey',
        method: 'get',
        params: data
    })
}

//接地绘制
// 接地型号列表
export function getGroundingList(data) {
    return request({
        url: '/jsObjIncreaseRodEquipment/getGroundingList',
        method: 'get',
        params: data
    })
}

//绘制电缆
export function getCableTypeName(data) {
    return request({
        url: '/jsObjDrawCable/getCableTypeName',
        method: 'post',
        data: data
    })
}

//获取通道类型数据--select

export function getChannelValue(data) {
    return request({
        url: '/jsObjDrawCable/getChannelValue',
        method: 'get',
        params: data
    })
}

/// 获取电缆型号
export function getCableTypeFull(data) {
    return request({
        url: '/jsObjDrawCable/getCableTypeFull',
        method: 'get',
        params: data
    })
}

//绘制电缆头 电缆头绘制类型-select
export function getCableHeadType(data) {
    return request({
        url: '/jsObjInsertCableHead/getCableHeadType',
        method: 'get',
        params: data
    })
}

//绘制电缆分支箱 获取 电缆分支箱类别接口-select
export function getModuleTypeKeyList(data) {
    return request({
        url: '/jsobjCableBranchBoxEvent/getModuleTypeKeyList',
        method: 'get',
        params: data
    })
}

//绘制电缆分支箱 获取 电缆分支箱接口-select

export function getCableBranchBoxList(data) {
    return request({
        url: '/jsobjCableBranchBoxEvent/getCableBranchBoxList',
        method: 'get',
        params: data
    })
}

//绘制电缆井 --获取类型--select

export function getCableWellType() {
    return request({
        url: '/jsobjCableBranchBoxEvent/GetCableWellType',
        method: 'get',
    })
}

//绘制电缆井 --获取电缆井型号 --select
export function getCableWellTypeNumber(data) {
    return request({
        url: '/jsobjCableBranchBoxEvent/getCableWellData',
        method: 'get',
        params: data
    })
}

// //绘制电缆井 --获取土质类型 --select
// export function getSoilType() {
//   return request({
//     url: '/jsObjInsertCableWell/getSoilType',
//     method: 'get',
//   })
// }
// //绘制电缆井 --获取破碎路面--select
// export function getBrokenPavementType() {
//   return request({
//     url: '/jsObjInsertCableWell/getBrokenPavementType',
//     method: 'get',
//   })
// }
//绘制电缆通道  敷设方式 -select

export function moduleTypes() {
    return request({
        url: '/jsObjChannel/moduleTypes',
        method: 'get',
    })
}


//绘制电缆通道  通道型号 -select
export function getModulesByModuleTypeKey(data) {
    return request({
        url: '/jsObjChannel/getModulesByModuleTypeKey',
        method: 'get',
        params: data
    })
}

//绘制电缆通道  管材类型 -select
export function pipeTypes() {
    return request({
        url: '/jsObjChannel/pipeTypes',
        method: 'get',
    })
}

// 绘制电缆通道  查询管材型号
export function pipeModels(data) {
    return request({
        url: '/jsObjChannel/pipeModels',
        method: 'get',
        params: data
    })
}

//标注管理

export function tagsetList(data) {
    return request({
        url: '/tagsetting/list',
        method: 'get',
        params: data
    })
}

//标注管理 更新
export function tagsetUpdate(data) {
    return request({
        url: '/tagsetting/update',
        method: 'post',
        data: data
    })
}

//配电站房设计 绘制路径图元
export function getSchemeType(data) {
    return request({
        url: '/jsObjDrawRouteElement/getSchemeType',
        method: 'get',
        params: data
    })
}

//得到站房模块进出线数
export function getIntervalNumData(data){
    return request({
        url: '/rodLineJointDraw/getIntervalNumData',
        method: 'get',
        params: data
    })
}


// 配电站房设计 获取方案编号
export function getSchemeModuleCode(data) {
    return request({
        url: '/jsObjDrawRouteElement/getSchemeModuleCode',
        method: 'get',
        params: data
    })
}

// 配电站房设计 物料信息
export function GetIntervalSplicingInfo(data) {
    return request({
        url: '/jsObjDrawRouteElement/GetIntervalSplicingInfo',
        method: 'get',
        params: data
    })
}

// 配电站房设计 方案名称
export function GetSchemeModuleName(data) {
    return request({
        url: '​/jsObjDrawRouteElement​/GetSchemeModuleName',
        method: 'get',
        params: data
    })
}


// 杆塔
export function getDLSGModuleByTowerZj(data) {
    return request({
        url: '/rodLineJointDraw/getDLSGModuleByTowerZJ',
        method: 'get',
        // params: data
    })
}

//得到电缆分支箱进出线个数
export function getCableBoxInOutData(data) {
    return request({
        url: '/jsobjCableBranchBoxEvent/getIntervalNumData',
        method: 'get',
        params: data
    })
}
