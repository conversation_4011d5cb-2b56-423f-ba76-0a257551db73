import { MxCpp, McDbDictionary, McDbXrecord } from "mxcad";

// 字典数据遍历
function MxTest_DictionaryData(dict) {
    let aryName = dict.getAllObjectName();
    aryName.forEach((name) => {
        console.log(name);
        let id = dict.getAt(name);
        let obj = id.getMcDbObject();
        if (obj instanceof McDbDictionary) {
            let dict = obj as McDbDictionary;
            console.log(dict);
            MxTest_DictionaryData(dict);
        }
        else if (obj instanceof McDbXrecord) {
            let xrec = obj as McDbXrecord;
            let data = xrec.getData()
            data.PrintData();
        }
    })
}

// 得到命名字典
async function MxTest_GetNamedObjectsDictionary() {
    let mxcad = MxCpp.getCurrentMxCAD();
    let dict = mxcad.getDatabase().getNamedObjectsDictionary();
    let aryName = dict.getAllObjectName();
    aryName.forEach((name) => {
        console.log(name);
        let id = dict.getAt(name);
        let obj = id.getMcDbObject();
        if (obj instanceof McDbDictionary) {
            let dict = obj as McDbDictionary;
            console.log(dict);
            MxTest_DictionaryData(dict);
        }
    })
}

// 调用得到命名字典方法
MxTest_GetNamedObjectsDictionary()