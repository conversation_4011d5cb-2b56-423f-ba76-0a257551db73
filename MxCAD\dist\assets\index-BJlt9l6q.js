import{h as W,d as v,w as P,a0 as R,_ as U,$ as t,a1 as X,m as e,Q as n,V as y,n as Y,H as Z,I as ee,u as A,a5 as $,B as le}from"./vue-DfH9C9Rx.js";import{M as ae}from"./index-8X61wlK0.js";import{s as B,I as H,ah as te,J as se,a as V,c as ue,K as oe,M as ne,ai as re,aj as ie,ak as E,al as me,_ as de}from"./index-D95UjFey.js";import{M as fe,a as J,i as ve}from"./mxcad-CfPpL1Bn.js";import{E as o,V as x,C as b,k as ce,a as pe,J as ye,K,j as Ve}from"./vuetify-B_xYg4qv.js";import"./mxdraw-C_n_7lEs.js";import"./mapbox-gl-PN3pDJHq.js";import"./xlsx-Ck5MUZvf.js";const Ce={class:"px-2 mt-5"},ke=W({__name:"index",setup(ge){const{isShow:M,showDialog:j}=re;let c={};const I=v([]),g=v("所有图元");P(M,s=>{s&&ie(()=>{c={};const a=new ve;a.allSelect(),a.forEach(l=>{const u=l.getObjectName();c[u]?c[u].push(l):c[u]=[l]}),I.value=[{title:"所有图元",value:"all"},...Object.keys(c).map(l=>({title:me[l]||l,value:l}))],g.value=I.value[0].value})});const O=[{title:"Color",value:1},{title:"Layer",value:2},{title:"LineType",value:3}],i=v(O[0].value),G=[{title:"= "+B("等于"),value:"="},{title:"!= "+B("不等于"),value:"!="},{title:"> "+B("大于"),value:">"},{title:B("全部选择"),value:"all"}],m=v(G[0].value),_=v(!0),{list:L}=H(te()),C=v(L.value[0]?.name),{lineTypeList:S}=H(se()),k=v(S.value[0]?.name),h=v(!0);P(i,()=>{C.value||(C.value=L.value[0].name),k.value||(k.value=S.value[0].name)});const w=v(),F=[{name:"确定",fun:()=>{j(!1);const s=fe.getCurrentMxCAD(),a=l=>{let u;if(i.value===1&&(u="trueColor"),i.value===2&&(u="layer"),i.value===3&&(u="linetype"),!u)return;const Q=p=>{if(p instanceof J&&u==="trueColor"){const r=new J,d=w.value?.color?.method;if(d!==E.kByColor&&(d===E.kByLayer&&p.method===d||d===E.kByBlock&&p.method===d||d===E.kByACI&&p.colorIndex===w.value?.color?.index))return!0;const{r:f,g:q,b:z}=new THREE.Color(w.value?.color?.color);r.setRGB(f*255,q*255,z*255);const N=Number(r.getColorValue()),T=Number(p.getColorValue());return m.value==="="?N===T:m.value==="!="?N!==T:m.value===">"?N<T:!1}return!1};h.value||s.mxdraw.clearMxCurrentSelect();const D=[];c[l]?.forEach(p=>{let r=!1;const d=p.getMcDbEntity();if(d){if(m.value==="all")r=!0;else{const f=d[u];Q(f)&&(r=!0),m.value==="="?(u==="layer"&&f===C.value||u==="linetype"&&f===k.value)&&(r=!0):m.value==="!="?(u==="layer"&&f!==C.value||u==="linetype"&&f!==k.value)&&(r=!0):m.value===">"&&(u==="layer"&&f>C.value||u==="linetype"&&f>k.value)&&(r=!0),_.value||(r=!r)}r&&D.push(p)}}),s.addCurrentSelect(D,D.length<30)};g.value==="all"?Object.keys(c).forEach(l=>{a(l)}):_.value?a(g.value):Object.keys(c).filter(l=>l!==g.value).forEach(l=>{a(l)}),s.updateDisplay()},primary:!0},{name:"关闭",fun:()=>j(!1)}];return(s,a)=>(R(),U(ae,{title:s.t("292"),"max-width":"320",modelValue:A(M),"onUpdate:modelValue":a[6]||(a[6]=l=>le(M)?M.value=l:null),footerBtnList:F},{default:t(()=>[X("div",Ce,[e(b,null,{default:t(()=>[e(o,{cols:"4",class:"text-right"},{default:t(()=>[e(V,{"key-name":"V"},{default:t(()=>[n(y(s.t("612")),1)]),_:1}),a[7]||(a[7]=n(": "))]),_:1}),e(o,{cols:"6"},{default:t(()=>[e(x,{class:"mx-1",items:[s.t("613")],"model-value":s.t("613")},null,8,["items","model-value"])]),_:1}),e(o,{cols:"2"},{default:t(()=>[e(ue,{disabled:""})]),_:1})]),_:1}),e(b,null,{default:t(()=>[e(o,{cols:"4",class:"text-right"},{default:t(()=>[e(V,{"key-name":"B"},{default:t(()=>[n(y(s.t("614")),1)]),_:1}),a[8]||(a[8]=n(": "))]),_:1}),e(o,{cols:"6"},{default:t(()=>[e(x,{class:"mx-1",items:I.value,modelValue:g.value,"onUpdate:modelValue":a[0]||(a[0]=l=>g.value=l)},null,8,["items","modelValue"])]),_:1}),e(o,{cols:"2"})]),_:1}),e(b,null,{default:t(()=>[e(o,{cols:"4",class:"text-right","align-self":"start"},{default:t(()=>[e(V,{"key-name":"P"},{default:t(()=>[n(y(s.t("615")),1)]),_:1}),a[9]||(a[9]=n(": "))]),_:1}),e(o,{cols:"6"},{default:t(()=>[e(ce,{items:O,border:"",height:"160",density:"compact",variant:"text"},{item:t(({props:l})=>[e(pe,Y(l,{onClick:u=>i.value=l.value,class:i.value===l.value?"bg-light-blue-darken-2":""}),null,16,["onClick","class"])]),_:1})]),_:1}),e(o,{cols:"2"})]),_:1}),e(b,null,{default:t(()=>[e(o,{cols:"4",class:"text-right"},{default:t(()=>[e(V,{"key-name":"O"},{default:t(()=>[n(y(s.t("616")),1)]),_:1}),a[10]||(a[10]=n(": "))]),_:1}),e(o,{cols:"6"},{default:t(()=>[e(x,{class:"mx-1",items:G,modelValue:m.value,"onUpdate:modelValue":a[1]||(a[1]=l=>m.value=l)},null,8,["modelValue"])]),_:1}),e(o,{cols:"2"})]),_:1}),e(b,null,{default:t(()=>[e(o,{cols:"4",class:"text-right"},{default:t(()=>[e(V,{"key-name":"V"},{default:t(()=>[n(y(s.t("617")),1)]),_:1}),a[11]||(a[11]=n(": "))]),_:1}),e(o,{cols:"6"},{default:t(()=>[Z(e(oe,{ref_key:"selectColor",ref:w},null,512),[[ee,i.value===1]]),i.value===2?(R(),U(x,{key:0,modelValue:C.value,"onUpdate:modelValue":a[2]||(a[2]=l=>C.value=l),items:A(L),"item-title":"name","item-value":"name"},null,8,["modelValue","items"])):$("",!0),i.value===3?(R(),U(x,{key:1,modelValue:k.value,"onUpdate:modelValue":a[3]||(a[3]=l=>k.value=l),items:A(S),"item-title":"name","item-value":"name"},null,8,["modelValue","items"])):$("",!0)]),_:1}),e(o,{cols:"2"})]),_:1}),e(ne,{title:s.t("612")},{default:t(()=>[e(ye,{class:"",inline:!1,modelValue:_.value,"onUpdate:modelValue":a[4]||(a[4]=l=>_.value=l)},{default:t(()=>[e(K,{value:!0},{label:t(()=>[e(V,{"key-name":"I"},{default:t(()=>[n(y(s.t("618")),1)]),_:1})]),_:1}),e(K,{value:!1,class:"mt-1"},{label:t(()=>[e(V,{"key-name":"E"},{default:t(()=>[n(y(s.t("619")),1)]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1},8,["title"]),e(Ve,{class:"mt-2",modelValue:h.value,"onUpdate:modelValue":a[5]||(a[5]=l=>h.value=l)},{label:t(()=>[e(V,{"key-name":"A"},{default:t(()=>[n(y(s.t("620")),1)]),_:1})]),_:1},8,["modelValue"])])]),_:1},8,["title","modelValue"]))}}),Se=de(ke,[["__scopeId","data-v-b3cff0e5"]]);export{Se as default};
