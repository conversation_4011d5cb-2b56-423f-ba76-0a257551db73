import { McDbBlockTableRecord,MxCADUiPrPoint, McGePoint3d, McDbPolyline, MxCpp,MxTools, McDbText, McDb, McCmColor, McDbBlockReference, McDbAttributeDefinition, McDbMText } from "mxcad";
import {MxFun} from "mxdraw";
function textLineSf(text) {
    console.log(text.length,'文本长度')
    let  result = 1;//文本宽度缩放等级
    let num =0; //文本位置
    if( text.length>=20&&text.length<=28){
        result = 0.6 
        num=1
    }else if(text.length>28){
        result = 0.4
        num = 0.7
    }
    return {result,num};
}
export async function TzFile(value) {
   
    let mxcad = MxCpp.App.getCurrentMxCAD();
    mxcad.newFile();
    let blkTable =  mxcad.getDatabase().getBlockTable();
    let blkRecId = blkTable.add(new McDbBlockTableRecord());
    // 根据ObjectId再次得到刚刚添加的图块记录
    let blkTableRecord:McDbBlockTableRecord = blkRecId.getMcDbBlockTableRecord()
    // 处理偏移量
    const offsetX = 0; // 默认值为 0

    let blkFilePathNr =  value.openUrl ? URL.createObjectURL(value.openUrl) : ''
    let blkrecIdNr = await mxcad.insertBlock(blkFilePathNr, value.tzmc);
    if (!blkrecIdNr.isValid()) {
        return
    }
    let dwgBTR:McDbBlockTableRecord = blkrecIdNr.getMcDbBlockTableRecord() as any;
     const boundboxPoints = dwgBTR.getBoundingBox()
     const blockWidth = boundboxPoints.maxPt.x - boundboxPoints.minPt.x;
    const blockHeight = boundboxPoints.maxPt.y - boundboxPoints.minPt.y;
    const blockCenterX = boundboxPoints.minPt.x + blockWidth / 2;
    const blockCenterY = boundboxPoints.minPt.y + blockHeight / 2;

    //  dwgBTR.origin = boundboxPoints.minPt;
    let blkRefNr = new McDbBlockReference();
    blkRefNr.blockTableRecordId = blkrecIdNr;
    blkRefNr.position = new McGePoint3d(0, 0, 0)
    blkTableRecord.appendAcDbEntity(blkRefNr)

    let blkFilePath = value.openUrlTk ? URL.createObjectURL(value.openUrlTk) : ''
    // 插入图块文件
    let blkrecId = await mxcad.insertBlock(blkFilePath, '图框');
    if (!blkrecId.isValid()) {
        return
    }
    let dwgBTR1:McDbBlockTableRecord = blkrecId.getMcDbBlockTableRecord() as any;
    const boundboxPoints2 = dwgBTR1.getBoundingBox()
    const blockWidth1 = boundboxPoints2.maxPt.x - boundboxPoints2.minPt.x;
   const blockHeight1 = boundboxPoints2.maxPt.y - boundboxPoints2.minPt.y;
   const blockCenterX1 = boundboxPoints2.minPt.x + blockWidth1 / 2;
   const blockCenterY1 = boundboxPoints2.minPt.y + blockHeight1 / 2;
    let blkRef = new McDbBlockReference();
    blkRef.blockTableRecordId = blkrecId;
    blkRef.position = new McGePoint3d(blockCenterX-blockCenterX1, blockCenterY-blockCenterY1, 0);
    // mxcad.drawEntity(blkRef);
    blkTableRecord.appendAcDbEntity(blkRef)
    let blkRecord: any = blkrecId.getMcDbBlockTableRecord();
    let leftHeight = 0// 左边框宽
    let rightHeight = 0 // 右边框宽
    let bottomWidth = 0// 下边框宽
    let borderWidth = 0 // 外框宽
    let TqBorderWidth = 0// 图签宽
    let ids = blkRecord.getAllEntityId();
    ids.forEach((id: any, index: any) => {
        if (!id.isKindOf("McDbAttributeDefinition")) return;
        let attribDef = id.getMcDbEntity() as McDbAttributeDefinition;
        if (attribDef.tag === '左边框宽') {
            leftHeight = Number(attribDef.textString)
        } else if (attribDef.tag === '右边框宽') {
            rightHeight = Number(attribDef.textString)
        } else if (attribDef.tag === '外框宽') {
            borderWidth = Number(attribDef.textString)
        } else if (attribDef.tag === '下边框高') {
            bottomWidth = Number(attribDef.textString)
        }
    })
    let blkFilePathT =  value.openUrlTq ? URL.createObjectURL(value.openUrlTq) : ''
    let blkrecIdT = await mxcad.insertBlock(blkFilePathT, '图签');
    if (!blkrecIdT.isValid()) {
        return
    }
    let blkRefT = new McDbBlockReference();
    blkRefT.blockTableRecordId = blkrecIdT;
    let blkRecordT: any = blkrecIdT.getMcDbBlockTableRecord();
    const boundboxPoints1 = blkRecordT.getBoundingBox();
     TqBorderWidth = boundboxPoints1.maxPt.x - boundboxPoints1.minPt.x;
    // let idsT = blkRecordT.getAllEntityId();
    // idsT.forEach((idT: any, index: any) => {
    //     if (!idT.isKindOf("McDbAttributeDefinition")) return;
    //     let attribDef = idT.getMcDbEntity() as McDbAttributeDefinition;
    //     console.log("🚀 ~ idsT.forEach ~ attribDef:", attribDef)
    //     if (attribDef.tag === '图签宽') {
    //         TqBorderWidth = Number(attribDef.textString)
    //     }
    // })
    let aWidth=blockCenterX-blockCenterX1+borderWidth;
    let aHeight=blockCenterY-blockCenterY1;
    blkRefT.position = new McGePoint3d(aWidth - TqBorderWidth - rightHeight + offsetX,aHeight+bottomWidth,0)
    blkTableRecord.appendAcDbEntity(blkRefT)

    // 供电公司
    const textG = new McDbText();
    const positionG = new McGePoint3d(aWidth - TqBorderWidth - rightHeight + 17 + offsetX,aHeight+bottomWidth + 16,0)
    textG.textString = value.sjdw ? value.sjdw : ''; // 使用传入的文本参数
    textG.position = positionG;
    textG.alignmentPoint = positionG;
    textG.height = 2; // 文本高度
    textG.widthFactor = textLineSf(value.sjdw ? value.sjdw : '').result; // 文本宽度缩放
    textG.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textG.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textG.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    // mxcad.drawEntity(textG);
    blkTableRecord.appendAcDbEntity(textG)

    // 项目名称
    const textPrject = new McDbText();
    const positionPrject = new McGePoint3d(aWidth - TqBorderWidth - rightHeight + 58 + offsetX,aHeight+bottomWidth + 16+textLineSf(value.gcmc ? value.gcmc : '').num,0)
    textPrject.textString = value.gcmc ? value.gcmc : ''; // 使用传入的文本参数
    textPrject.position = positionPrject;
    textPrject.alignmentPoint = positionPrject;
    textPrject.height = 2; // 文本高度
    textPrject.widthFactor = textLineSf(value.gcmc ? value.gcmc : '').result; // 文本宽度缩放
    textPrject.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textPrject.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textPrject.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    blkTableRecord.appendAcDbEntity(textPrject)

    // 阶段
    const textJd = new McDbText();
    const positionJd = new McGePoint3d(aWidth - TqBorderWidth - rightHeight + 80 + offsetX,aHeight+bottomWidth + 16,0)
    const textString = value.sjjd ? value.sjjd : ''; // 使用传入的文本参数
    textJd.textString = textString; // 使用传入的文本参数
    textJd.position = positionJd;
    textJd.alignmentPoint = positionJd;
    textJd.height = 2; // 文本高度
    textJd.widthFactor = 1; // 文本宽度缩放
    textJd.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textJd.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textJd.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    blkTableRecord.appendAcDbEntity(textJd)

    // 时间
    const textTime = new McDbText();
    const positionTime = new McGePoint3d(aWidth - TqBorderWidth - rightHeight + 40 + offsetX,aHeight+bottomWidth + 1.5,0)
    const textStringTime = value.time ? value.time : '';
    textTime.textString = textStringTime; // 使用传入的文本参数
    textTime.position = positionTime;
    textTime.alignmentPoint = positionTime;
    textTime.height = 1; // 文本高度
    textTime.widthFactor = 1; // 文本宽度缩放
    textTime.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textTime.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textTime.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    blkTableRecord.appendAcDbEntity(textTime)

    // 比例
    const textBl = new McDbText();
    const positionBl = new McGePoint3d(aWidth - TqBorderWidth - rightHeight + 40 + offsetX,aHeight+bottomWidth + 5.5,0)
    const textStringBl = value.tzbl ? value.tzbl : '';
    textBl.textString = textStringBl; // 使用传入的文本参数
    textBl.position = positionBl;
    textBl.alignmentPoint = positionBl;
    textBl.height = 1; // 文本高度
    textBl.widthFactor = 1; // 文本宽度缩放
    textBl.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textBl.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textBl.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    blkTableRecord.appendAcDbEntity(textBl)

    // 图号：项目编号+n
    const textCode = new McDbText();
    const positionCode = new McGePoint3d(aWidth - TqBorderWidth - rightHeight + 60 + offsetX,aHeight+bottomWidth + 1.5,0)
    const textStringCode = value.th ? value.th : ''; // 使用传入的文本参数
    textCode.textString = textStringCode; // 使用传入的文本参数
    textCode.position = positionCode;
    textCode.alignmentPoint = positionCode;
    textCode.height = 1; // 文本高度
    textCode.widthFactor = 1; // 文本宽度缩放
    textCode.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textCode.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textCode.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    blkTableRecord.appendAcDbEntity(textCode)

    // 图框名称：主要设备清单+当前页1,2,3
    const textK = new McDbMText();
    const positionK = new McGePoint3d(aWidth - TqBorderWidth - rightHeight + 48 + offsetX,aHeight+bottomWidth + 14,0)
    const textStringK = value.tzmc ? value.tzmc : ''; // 使用传入的文本参数
    textK.attachment = McDb.AttachmentPoint.kTopLeft
    textK.contents = addNewlineAfterEvery20Chars(textStringK)
    textK.location = positionK
    textK.trueColor = new McCmColor(0, 0, 0)
    textK.textHeight = 2
    // const textK = new McDbText();
    // textK.textString = textStringK; // 使用传入的文本参数
    // textK.position = positionK;
    // textK.alignmentPoint = positionK;
    // textK.height = 2; // 文本高度
    // textK.widthFactor = 1; // 文本宽度缩放
    // textK.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    // textK.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    // textK.trueColor = new McCmColor(0, 0, 0)
    // 绘制文本
    // mxcad.drawEntity(textK);
    blkTableRecord.appendAcDbEntity(textK)

    // 实例化块参照 这里需要设置我们刚刚添加图块记录得到的ObjectId
    let blkRefObj = new McDbBlockReference();
    blkRefObj.blockTableRecordId = blkRecId;
    // 最后设置位置 渲染图块
    let newBlkRefId = mxcad.drawEntity(blkRefObj);
    mxcad.updateDisplay()
    mxcad.regen();
    // 获取该对象的实体
    const ent = newBlkRefId.getMcDbEntity();
    if(!ent) return { id: null, handle: null };
    // 获取对象ID
    const entId = ent.getObjectID();
    // 获取对象句柄
    const sHandle = ent.getHandle();
    // 返回一个 Promise
    return new Promise((resolve, reject) => {
        MxCpp.getCurrentMxCAD().saveFile(void 0, (data) => {
            let blob: Blob;
            const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
            if (isSafari) {
                blob = new Blob([data.buffer], { type: "application/octet-stream" });
            } else {
                blob = new Blob([data.buffer], { type: "application/octet-binary" });
            }
            const file = new File([blob], value.tzmc + '.mxweb', { type: isSafari ? "application/octet-stream" : "application/octet-binary" });
            resolve({file: file, sHandle}); // 返回文件对象
        }, false, true);
    });
}

function addNewlineAfterEvery20Chars(text) {
    let result = "";
    for (let i = 0; i < text.length; i += 20) {
        result += text.slice(i, i + 20) + "\\P";
    }
    return result;
}