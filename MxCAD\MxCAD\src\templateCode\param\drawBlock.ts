import { MxCpp, McDbBlockTableRecord, McDbLine, McDbCircle, McCmColor, McDbBlockReference, McGePoint3d } from "mxcad";

// 画图块
function drawBlock() {
  const mxcad = MxCpp.getCurrentMxCAD();
  // 创建新画布
  mxcad.newFile();

  // 先从数据库中得到图块表
  let blkTable = mxcad.getDatabase().getBlockTable();
  // 将一个新的图块记录添加到图块表中
  let blkRecId = blkTable.add(new McDbBlockTableRecord());

  // 根据ObjectId再次得到刚刚添加的图块记录
  let blkTableRecord = blkRecId.getMcDbBlockTableRecord()

  // 添加两条线段再图块记录中 这里每条线段的具体属性比如开始点和结束点自行赋值
  const line = new McDbLine(50, 50, 0, -50, -50, 0)
  line.trueColor = new McCmColor(0, 255, 0)
  const line1 = new McDbLine(-50, 50, 0, 50, -50, 0)
  const circle = new McDbCircle(0, 0, 0, 50)
  circle.trueColor = new McCmColor(255, 255, 0)
  blkTableRecord.appendAcDbEntity(line);
  blkTableRecord.appendAcDbEntity(line1);
  blkTableRecord.appendAcDbEntity(circle);

  // 设置图块的基点 一般是包围盒内的点， 可以任意指定
  blkTableRecord.origin = new McGePoint3d(0, 0, 0);

  // 实例化块参照 这里需要设置我们刚刚添加图块记录得到的ObjectId
  let blkRef = new McDbBlockReference();
  blkRef.blockTableRecordId = blkRecId;
  // 最后设置位置 渲染图块
  blkRef.position = new McGePoint3d(0, 0, 0);

  mxcad.drawEntity(blkRef);

  mxcad.zoomAll()
  mxcad.zoomScale(0.8)
};

// 调用画图块方法
drawBlock();