<template>
  <el-tree
  style="treeD"
    :data="treeData"
    :props="defaultProps"
    accordion
    default-expand-all
    node-key="id"
    :render-content="renderContent"
    :height="height"
    @node-click="handleNodeClick"
    @node-contextmenu="handleNodeContextMenu"
  ></el-tree>
</template>

<script setup>
import { ref } from "vue";
const emit = defineEmits(["handleNodeContextMenu"]);
const props = defineProps({
  treeData: {
    type: Array,
    default: () => [],
  },
  defaultProps: {
    type: Object,
    default: () => ({}),
  },
  height: {
    type: [Number, String],
    default: "auto",
  },
});

const renderContent = (h, { node }) => {
  return h("span", { class: "custom-node" }, node.label);
};
const handleNodeClick = (event, node, item) => {
  emit("nodeClick", node, item);
};
const handleNodeContextMenu = (event, data, node, tree) => {
  event.preventDefault(); // 阻止浏览器默认右键菜单
  emit("handleNodeContextMenu", event, data, node, tree);
}
</script>

<style scoped>
.el-tree{
    background:#00706b ;
    color: white;
}
.custom-node {
  position: relative;
  padding-left: 20px;
  font-size: 14px;
}

.el-tree-node__content {
  position: relative;
}

.el-tree-node__children {
  padding-left: 20px;
}

/* .el-tree-node__content::before {
  content: "";
  position: absolute;
  left: -10px;
  top: 50%;
  width: 10px;
  height: 1px;
  border-top: 1px dashed #ccc;
  transform: translateY(-50%);
}

.el-tree-node__children::before {
  content: "";
  position: absolute;
  left: -10px;
  top: 0;
  width: 1px;
  height: 100%;
  border-left: 1px dashed #ccc;
}

.el-tree-node__children > .el-tree-node__content::before {
  left: -10px;
  top: -5px;
  width: 10px;
  height: 1px;
  border-top: 1px dashed #ccc;
} */
/* .el-tree :hover{
  background-color: rgba(0, 0, 0, 0.06)!important;
} */
::v-deep .el-tree-node:focus > .el-tree-node__content {
          background: rgba(80, 169, 170, 0.4);
        }
        ::v-deep .el-tree-node :hover {
          background: rgba(80, 169, 170, 0.1) !important;
        }
</style>
