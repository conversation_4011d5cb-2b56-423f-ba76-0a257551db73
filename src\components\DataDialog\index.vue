<template>
  <div style="pointer-events: none;width: 100%;height: auto">
    <el-dialog v-model="appStore.dataFlag" draggable overflow
    :width="isFullscreen ? '100vw' : props.dataWidth" 
    :close-on-click-modal=false
    :show-close=false
    :modal=false
    style="float: right;margin-right: 100px;"
    :style="{padding: '0px', height:isFullscreen ? '100vh' : props.dataHeight,top:isFullscreen ? '-6vh' :'0'}"
   >
   <!-- @opened="handleDialogOpen" -->
      <template #header >
        <div :style="{width: isFullscreen ? '100vw' : `${props.dataWidth}px`}"  class="header">
          <slot name="header">
            <!-- <h3 style="margin-left: 10px; line-height: 39px"></h3> -->
          </slot>
          <div class="fullscreen" @click="toggleFullscreen" v-if="props.isFullscreenshow"><img src="@/assets/icons/svg/fangda.svg" alt="" style="height: 18px;"></div>
          <div class="close" @click="dialogClose">X</div>
        </div>
      </template>
      <div class="body">
        <slot name="body">
          <div></div>
        </slot>
      </div>
  </el-dialog>
  </div>
  
</template>

<script setup>
import useAppStore from "@/store/modules/app";
import { ElLoading } from "element-plus";

const appStore = useAppStore();
const emit = defineEmits();
const props = defineProps({
  dataWidth: {
    default: 378,
    type: [Number, String],
  },
  dataHeight: {
    default: 'auto',
    type: [String],
  },
  isFullscreenshow:{
    default: false,
    type: Boolean,
  }
});
const isFullscreen = ref(false);
const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value;
}
const dialogClose = () => {
  emit('close');
}
const handleDialogOpen = async () => {
  try {
    await nextTick()
    // const loading = ElLoading.service({
    //   lock: true,
    //   text: "数据加载中...",
    //   background: "rgba(255, 255, 255, 0.6)"
    // });
    // await dialogOpen()
    setTimeout(() => {
      
      // loading.close()
    }, 500)
  } catch (error) {
    // loading.close()
  }
}
const dialogOpen = () => {
  return new Promise((resolve) => {
    const elDialog = document.getElementsByClassName('el-dialog')[0]
    const header = document.getElementsByClassName('el-dialog__header')[0]
    const body = document.getElementsByClassName('el-dialog__body')[0]
    if (body) body.style.padding = '0px'
    if (header) header.style.padding = '0px'
    if(elDialog) {
      elDialog.style["pointer-events"] = 'auto'
      elDialog.parentElement.parentElement.style["pointer-events"] = 'none'
    }
    resolve()
  })
}

</script>

<style lang="scss" scoped>

.header {
  // width: 378px;
  height: 39px;
  background: #0e8b8d;
  border-radius: 5px 5px 0px 0px;
  line-height: 39px;
  font-weight: bold;
  font-size: 16px;
  color: #ffffff;
  position: relative;
  pointer-events: auto;

  .fullscreen {
    cursor: pointer;
    position: absolute;
    right: 40px;
    top: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
  }
  .close {
    cursor: pointer;
    position: absolute;
    right: 10px;
    top: 2px;
  }
}

.body {
  margin-top: -16px;
  border-radius: 0px 0px 5px 5px;
  max-height: 700px;
  overflow: auto;
  background: white;
  pointer-events: auto;
}
  
</style>
<style scoped>

::v-deep .el-overlay-dialog{
  pointer-events: none;
}
</style>