<script setup>
import {onUnmounted} from "vue";
import SingleEquipmentSelection from "@/views/pages/online/sidebar/property/single-equipment-selection"
import {useIframeCommunication} from "@/hooks/useIframeCommunication.js";
import {ElMessage} from "element-plus";
import useAppStore from "@/store/modules/app";
import {useRoute} from "vue-router";
import { getModuleByZFLB } from "@/api/insertSag/index.js";
const { proxy } = getCurrentInstance();
const appStore = useAppStore();
const {sendMessage, cleanup} = useIframeCommunication()
const route = useRoute()
const cadAppRef = inject('cadAppRef');

const visible = ref(false)
const projectID= route.query.id
const currentId = ref(null)

const sendCommand = () => {
  console.log("🚀 ~ sendCommand ~  appStore.property:",  appStore.property)
  // visible.value=true
  // currentId.value = '4663960'
  
  const params = {
    name: '设备选型',
    content: '|Mx_ModelSelection',
    type: "sdkcad",
  }
  sendMessage(cadAppRef.value, params, (res) => {
    console.log('sendCommandres', res)
    console.log("设备选型打开前projectID",projectID,appStore.sdkClassName.blockId)
    getModuleByZFLB(projectID,appStore.sdkClassName.blockId).then(result=>{
      if(result.code==200){
      if(result.data=='土建站'){
        proxy.$modal.msgWarning(`土建站房不可设备选型!`);
    }else{
      if(res.id) {
      currentId.value = res.id
      visible.value = true
    }
    if(res.type=='error'){
      if(appStore.property&&appStore.property.length===1){
    visible.value=true
    currentId.value=appStore.property[0]
  }else{
    ElMessage.info('请选择设备')
  }
    }
    }
      }
    })
   
  })
}

onMounted(() => {
  sendCommand()
})

onUnmounted(() => {
  console.log('onUnmounted设备选型')
})
// watch(
//   () => appStore.property,
//   (newInfo, oldInfo) => {
//     console.log("🚀 ~ newInfo:", newInfo)
//   })
</script>

<template>
  <!--    设备选型-->
  <single-equipment-selection
      v-if="visible"
      :id="currentId"
      v-model:visible="visible"
  />
</template>

<style lang="scss" scoped>

</style>
