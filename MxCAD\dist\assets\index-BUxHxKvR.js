import{u as W,$ as Z,C as G,a0 as te,a1 as O,a2 as z,U as D,_ as ee,Y as T,Q as se,a3 as ne}from"./index-CzBriCFR.js";import{M as ae}from"./index-itnQ6avM.js";import{M as ue}from"./index-D2jCiJ2B.js";import"./mxcad-DrgW2waE.js";import"./mapbox-gl-DQAr7S0z.js";import{h as Y,c as R,d as f,z as re,_ as g,$ as V,a0 as a,V as k,F as j,a5 as E,ac as H,a1 as w,H as q,I as J,m as n,a4 as u,Q as L,u as A,w as de,k as K,a3 as ie,B as X}from"./vue-Cj9QYd7Z.js";import{B as F,L as N,z as oe,a0 as ce,$ as me}from"./vuetify-BqCp6y38.js";import{M as ve}from"./mxdraw-BQ5HhRCY.js";import"./handsontable-Ch5RdAT_.js";const fe={class:"pa-1"},pe={class:"ml-2 mt-1"},xe={class:"px-2 pt-1 pb-4"},Ce={class:"colors d-flex flex-column flex-wrap mt-1 mirrorRotateVertical",style:{height:"100px"}},ge=["onMousemove","onClick"],be={class:"colors d-flex flex-column flex-wrap mt-3",style:{height:"100px"}},ye=["onMousemove","onClick"],Ve={style:{height:"24px"}},ke={class:"d-flex justify-space-between mt-1"},$e={class:"index-colors d-flex flex-wrap"},he=["onMousemove","onClick"],_e={class:"index-colors d-flex flex-wrap"},Me=["onMousemove","onClick"],Ue={class:"superimposed-layer align-self-end mt-2"},P=9,Q=7,we=Y({__name:"IndexColor",props:{color:{},method:{}},emits:["update:color"],setup(r,{emit:b}){const $=b,x=t=>{$("update:color",t)},h=W(),{colorIndexList:c}=Z(h),m=R(()=>c.value.slice(0,P)),I=R(()=>c.value.slice(c.value.length-Q,c.value.length-1)),S=R(()=>c.value.slice(P,c.value.length-Q).filter((t,d)=>d%2!==0)),_=R(()=>c.value.slice(P,c.value.length-Q-1).filter((t,d)=>d%2===0)),v=f(-1),p=f(-1),y=f(r.color),i=f(""),l=f(!1),e=r.color?.toString();re(()=>{const t=G(r.color),d=t.red(),s=t.green(),M=t.blue(),U=te(d,s,M,!1);p.value=U,v.value=U,i.value=O[U]||U.toString(),r.method!==z.kByACI&&(i.value=`${d},${s},${M}`)});const o=t=>{v.value=t.index,i.value=O[t.index]||t.index.toString(),x(t.color)},C=t=>{i.value=t;const d=O[t];typeof d=="number"?v.value=d:v.value=Number(t)},B=t=>{y.value=t.color,p.value=t.index},le=R(()=>{const t=G(y.value);return`${t.red()}, ${t.green()}, ${t.blue()}, ${t.alpha()*255}`});return(t,d)=>(g(),V("div",fe,[a("p",pe,"MxCAD"+k(t.t("501"))+"(ACI):",1),a("div",xe,[a("div",{onMouseout:d[0]||(d[0]=s=>l.value=!1),onMousemove:d[1]||(d[1]=s=>l.value=!0)},[a("div",Ce,[(g(!0),V(j,null,E(_.value,(s,M)=>(g(),V("div",{class:H(["color-box",v.value===s.index?"active-color-box":""]),style:w({backgroundColor:s.color}),onMousemove:U=>B(s),onClick:U=>o(s)},null,46,ge))),256))]),a("div",be,[(g(!0),V(j,null,E(S.value,(s,M)=>(g(),V("div",{class:H(["color-box",v.value===s.index?"active-color-box":""]),style:w({backgroundColor:s.color}),onMousemove:U=>B(s),onClick:U=>o(s)},null,46,ye))),256))])],32),a("div",Ve,[a("div",ke,[q(a("span",null,k(t.t("10")+":"+p.value),513),[[J,l.value]]),q(a("span",null,"RGB: "+k(le.value),513),[[J,l.value]])])]),n(oe,null,{default:u(()=>[n(F,{cols:"6"},{default:u(()=>[a("div",{onMouseout:d[2]||(d[2]=s=>l.value=!1),onMousemove:d[3]||(d[3]=s=>l.value=!0)},[a("div",$e,[(g(!0),V(j,null,E(m.value,s=>(g(),V("div",{class:H(["color-box",v.value===s.index?"active-color-box":""]),style:w({backgroundColor:s.color}),onMousemove:M=>B(s),onClick:M=>o(s)},null,46,he))),256))]),a("div",_e,[(g(!0),V(j,null,E(I.value,s=>(g(),V("div",{class:H(["color-box",v.value===s.index?"active-color-box":""]),style:w({backgroundColor:s.color}),onMousemove:M=>B(s),onClick:M=>o(s)},null,46,Me))),256))])],32),n(N,{class:"mt-2","model-value":i.value,"onUpdate:modelValue":C},{prepend:u(()=>[n(D,{"key-name":"C"},{default:u(()=>[L(k(t.t("226")),1)]),_:1})]),_:1},8,["model-value"])]),_:1}),n(F,{cols:"6",class:"d-flex justify-end"},{default:u(()=>[a("div",Ue,[a("div",{class:"box1",style:w({backgroundColor:A(e)})},null,4),a("div",{class:"box2",style:w({backgroundColor:t.color})},null,4)])]),_:1})]),_:1})])]))}}),Ie=ee(we,[["__scopeId","data-v-5cd1e990"]]),Se={class:"pa-1"},Be={class:"d-flex mr-12"},De={class:""},Le={class:"ml-5"},Ne={class:"d-flex flex-column justify-center align-center"},Te={class:"d-flex flex-column"},Re={class:"superimposed-layer align-self-end m-t mb-2"},ze=Y({__name:"TrueColor",props:{color:{},method:{}},emits:["update:color"],setup(r,{emit:b}){const $=b,x=l=>l.startsWith("#")&&l.length>9?l.slice(0,-2):l,h=l=>{l=x(l),y(l),$("update:color",l)},c=r.color?.toString(),m=f(0),I=f(0),S=f(0),_=f(0),v=f(0),p=f(0),y=l=>{l=x(l);const e=G(l);m.value=T(e.red(),0),I.value=T(e.blue(),0),S.value=T(e.green(),0);const o=2;_.value=T(e.hue(),o),v.value=T(e.saturationl(),o),p.value=T(e.lightness(),o)};de(()=>r.color,l=>{if(l)try{y(l)}catch{}}),r.color&&y(r.color);const i=(l,e)=>{typeof e=="string"&&(e=x(e));const o=G(r.color);h(o[l](Number(e)).toString())};return(l,e)=>(g(),V("div",Se,[n(oe,null,{default:u(()=>[n(F,{cols:"7","align-self":"start"},{default:u(()=>[a("div",Be,[a("div",De,[n(D,{"key-name":"E"},{default:u(()=>[L(k(l.t("502")),1)]),_:1}),n(N,{class:"",type:"number",modelValue:_.value,"onUpdate:modelValue":[e[0]||(e[0]=o=>_.value=o),e[1]||(e[1]=o=>i("hue",o))]},null,8,["modelValue"])]),a("div",Le,[n(D,{"key-name":"S"},{default:u(()=>[L(k(l.t("503")),1)]),_:1}),n(N,{class:"",type:"number",modelValue:v.value,"onUpdate:modelValue":[e[2]||(e[2]=o=>v.value=o),e[3]||(e[3]=o=>i("saturationl",o))]},null,8,["modelValue"])])]),n(ce,{class:"mt-4 mx-color-picker","model-value":l.color,"onUpdate:modelValue":h,elevation:"0",rounded:"0",width:"300"},null,8,["model-value"])]),_:1}),n(F,{cols:"2","align-self":"start"},{default:u(()=>[a("div",Ne,[a("div",Te,[n(D,{"key-name":"L"},{default:u(()=>[L(k(l.t("504")),1)]),_:1}),n(N,{class:"",type:"number",modelValue:p.value,"onUpdate:modelValue":[e[4]||(e[4]=o=>p.value=o),e[5]||(e[5]=o=>i("lightness",o))]},null,8,["modelValue"]),n(me,{class:"mx-color-slider",direction:"vertical",modelValue:p.value,"onUpdate:modelValue":[e[6]||(e[6]=o=>p.value=o),e[7]||(e[7]=o=>i("lightness",o))]},null,8,["modelValue"])])])]),_:1}),n(F,{cols:"3","align-self":"end"},{default:u(()=>[n(N,{class:"mt-1",type:"number",modelValue:m.value,"onUpdate:modelValue":[e[8]||(e[8]=o=>m.value=o),e[9]||(e[9]=o=>i("red",o))]},{prepend:u(()=>[n(D,{"key-name":"R"},{default:u(()=>[L(k(l.t("505")),1)]),_:1})]),_:1},8,["modelValue"]),n(N,{class:"mt-1",type:"number",modelValue:I.value,"onUpdate:modelValue":[e[10]||(e[10]=o=>I.value=o),e[11]||(e[11]=o=>i("blue",o))]},{prepend:u(()=>[n(D,{"key-name":"G"},{default:u(()=>[L(k(l.t("506")),1)]),_:1})]),_:1},8,["modelValue"]),n(N,{class:"mt-1",type:"number",modelValue:S.value,"onUpdate:modelValue":[e[12]||(e[12]=o=>S.value=o),e[13]||(e[13]=o=>i("green",o))]},{prepend:u(()=>[n(D,{"key-name":"B"},{default:u(()=>[L(k(l.t("507")),1)]),_:1})]),_:1},8,["modelValue"]),a("div",Re,[a("div",{class:"box1",style:w(`background:${A(c)};`)},null,4),a("div",{class:"box2",style:w(`background:${l.color};`)},null,4)])]),_:1})]),_:1})]))}}),Ae=ee(ze,[["__scopeId","data-v-3fcfa457"]]),Fe=()=>{const r=f(),b=f(z.kByACI),$=f(0),x=[{tab:"索引颜色",component:()=>K(Ie,{color:r.value,method:b.value,"onUpdate:color":m=>{r.value=m,c(z.kByACI)}})},{tab:"真彩色",component:()=>K(Ae,{color:r.value,method:b.value,"onUpdate:color":m=>{r.value=m,c(z.kByColor)}})}],h=m=>{r.value=m},c=m=>{b.value=m,$.value=m===z.kByColor?1:0};return{items:x,color:r,setColor:h,setMethod:c,tab:$,method:b}},je={class:"px-1"},Je=Y({__name:"index",setup(r){const{items:b,color:$,tab:x,method:h,setColor:c,setMethod:m}=Fe(),{createColor:I}=W(),{colorSelectList:S,currentSelectColor:_}=Z(W());let v;const p=(e={call:(o,C)=>{const B=I({...ne(o,C)});S.value.unshift(B),_.value=B},color:_.value})=>{const{call:o,color:C}=e;i(!0),v=o,C&&(c(C.color),m(C.method))},{isShow:y,showDialog:i}=se(!1,"Mx_Color",p);ve.on("openCustomColorDiallog",p);const l=[{name:"确定",fun:()=>{v&&v($.value,h.value),i(!1)},primary:!0},{name:"取消",fun:()=>i(!1)}];return(e,o)=>(g(),ie(ae,{modelValue:A(y),"onUpdate:modelValue":o[1]||(o[1]=C=>X(y)?y.value=C:null),footerBtnList:l,"max-width":"580",title:e.t("选择颜色")},{default:u(()=>[a("div",je,[n(ue,{modelValue:A(x),"onUpdate:modelValue":o[0]||(o[0]=C=>X(x)?x.value=C:null),items:A(b),"tabs-props":{grow:!0},height:386},null,8,["modelValue","items"])])]),_:1},8,["modelValue","title"]))}});export{Je as default};
