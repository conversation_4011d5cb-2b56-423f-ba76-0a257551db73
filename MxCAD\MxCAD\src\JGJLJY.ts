import {
  MxCADSelectionSet,
  MxCADUiPrPoint,
  MxCADResbuf,
  MxCADUiPrEntity,
  MxCADUiPrKeyWord,
  McGeVector3d,
  IMcDbDwgFiler,
  McDb,
  McDbCustomEntity,
  McDbEntity,
  McDbLine,
  McDbMText,
  McGePoint3d,
  McGePoint3dArray,
  MxCADWorldDraw,
  MxCpp,
} from "mxcad";
import { MxFun } from "mxdraw";
import { MxMeasure } from "./MeasureDistance";

async function JGJLJY() {
  let mxcad = MxCpp.getCurrentMxCAD();
  let getEnt = new MxCADUiPrEntity();

  const config = {
    // 单列
    "1": {
      minDistance: 800,
    },
    // 面对面
    "2": {
      minDistance: 800,
    },
    // 背靠背
    "3": {
      minDistance: 1000,
    },
  };
  const filter = new MxCADResbuf();
  filter.AddMcDbEntityTypes("INSERT");
  getEnt.setFilter(filter);
  getEnt.setMessage("选择目标");
  let id = await getEnt.go();
  console.log(id);
  if (!id) return;
  const pt = getEnt.pickPoint();

  const ent = id.getMcDbEntity();
  console.log("JGJLJY", ent, pt);

  const blkDrawType = ent.getxDataString("drawType");
  if (!blkDrawType.ret) return;

  const getKey = new MxCADUiPrKeyWord();
  getKey.setMessage("请选择校验方式");
  getKey.setKeyWords("[柜前维护通道(1)/柜后维护通道(2)]");
  const keyVal: string = await getKey.go();
  console.log("keyVal", keyVal, blkDrawType.val);

  const minDistance = config[blkDrawType.val].minDistance;

  const mDist = new MxMeasure();
  mDist.setStartPoint(pt);
  mDist.onUpdate = (text) => {
    console.log("JGJLJYonUpdate", text);

    MxFun.formatString(text);
    const processedText = text.replace(/M$/, "");
    const currentValue = parseFloat(processedText);
    if (currentValue < minDistance) {
      console.log(
        `按1:100绘图比例测量校验距离为：${processedText}，小于典设安全距离${minDistance}，请注意调整`
      );
    }
  };
  mDist.DoDimensionMeasurement();
}

export function init() {
  MxFun.addCommand("JGJLJY", JGJLJY);
}
