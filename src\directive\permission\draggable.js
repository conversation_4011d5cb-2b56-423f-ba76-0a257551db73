export default {
    beforeMount(el) {
        // 记录拖拽的起始位置
        let offsetX = 0
        let offsetY = 0
        let isDragging = false
        // 拖拽开始
        const startDrag = (event) => {
            event.preventDefault()
            offsetX = event.offsetX
            offsetY = event.offsetY
            isDragging = true
            document.addEventListener('mousemove', dragMove)
            document.addEventListener('mouseup', stopDrag)
        }
        // 拖拽过程中
        const dragMove = (event) => {
            if (!isDragging) return
            const newX = document.documentElement.clientWidth - event.clientX - offsetX
            const newY = event.clientY - offsetY
            el.style.right = `${newX}px`
            el.style.top = `${newY}px`
        }
        // 停止拖拽
        const stopDrag = () => {
            isDragging = false
            document.removeEventListener('mousemove', dragMove)
            document.removeEventListener('mouseup', stopDrag)
        }
        el.addEventListener('mousedown', startDrag)
        el._cleanup = () => {
            el.removeEventListener('mousedown', startDrag)
        }
    },
    unmounted(el) {
        if (el._cleanup) {
            el._cleanup()  
        }
    }
}