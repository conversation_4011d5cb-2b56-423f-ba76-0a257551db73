import{h as W,d as i,c as C,w as h,a3 as L,a4 as c,u as k,B as Q,K as j,_ as y,m as u,Q as M,V as I,a0 as q,a6 as G,H,$ as P,a5 as Z,F as z}from"./vue-Cj9QYd7Z.js";import{Q as X,q as Y,$ as ee,aK as ae,aL as _,a4 as le,s as w,Z as B,W as te}from"./index-CzBriCFR.js";import{M as se}from"./index-itnQ6avM.js";import{e as oe,i as re}from"./mxcad-DrgW2waE.js";import"./mapbox-gl-DQAr7S0z.js";import{I as E,a7 as ne,L as ue,h as ie,a as ce}from"./vuetify-BqCp6y38.js";import"./mxdraw-BQ5HhRCY.js";import"./handsontable-Ch5RdAT_.js";const de={class:"d-flex algin-center"},Le=W({__name:"index",setup(fe){const f=X(!1,"showWalkThroughLayers"),{isShow:v}=f,b=Y(),{setValue:N,stringifyJSON:D,remove:A,setLayerList:K}=b,{list:p}=ee(b),r=i(!1),n=C(()=>p.value.filter(e=>e.name).map(({name:e})=>e)),t=i([...n.value]);let x;const V=i(!0);h(v,l=>{l?x=ae():x&&V.value&&_(x)}),h(t,le(l=>{p.value.forEach((e,s)=>{N("visible",l.includes(e.name),s)}),_(D())},100));const d=i(""),g=i(!1),$=async()=>{f.showDialog(!1);const l=await oe.userSelect(w("选择对象")),e=new Set;l.forEach(s=>{const a=s.getMcDbEntity();a&&e.add(a.layer)}),t.value=Array.from(e),f.showDialog(!0)};h(d,l=>{l===""&&(g.value=!1,r.value=!1)});const F=()=>{if(d.value===""){t.value=[];return}g.value=!0,r.value=!0,t.value=n.value.filter(l=>l.toLocaleLowerCase()===d.value.toLocaleLowerCase())},S=i([]),T=l=>{const{map:e}=l(S.value.map(a=>a.$el),(a,o)=>o);if(e.length===0)return;const s=r.value?t.value:n.value;t.value=e.map(a=>s[a])},U=(l,e,s)=>{if(l.ctrlKey)t.value.indexOf(e)===-1?t.value=[...t.value,e]:t.value=t.value.filter(o=>o!==e);else if(l.shiftKey){const a=t.value.map(R=>n.value.indexOf(R));let o=Math.min(...a),m=Math.max(...a);o>s?o=s:m=s,t.value=n.value.slice(o,m+1)}else t.value=[e]},J=C(()=>`${w("图层漫游")} - ${w("图层数")}:`+n.value.length),O=()=>{const l=new re,e=new Set;l.allSelect(),l.forEach(a=>{let o=a.getMcDbEntity();o&&e.add(o.layer)});const s=Array.from(e);A(p.value.filter(({name:a})=>!s.includes(a)),"this"),K(D())};return(l,e)=>{const s=j("box-selection");return y(),L(se,{maxWidth:"400px",ref:"layerDialog",modelValue:k(v),"onUpdate:modelValue":e[4]||(e[4]=a=>Q(v)?v.value=a:null),title:J.value},{actions:c(()=>[u(ne,{class:"mx-1 mt-0 mb-1 py-0 px-2 bg-dialog-card-text d-flex justify-end"},{default:c(()=>[u(E,{class:"mr-5",label:l.t("676"),modelValue:V.value,"onUpdate:modelValue":e[2]||(e[2]=a=>V.value=a)},null,8,["label","modelValue"]),u(B,{onClick:O},{default:c(()=>[M(I(l.t("677")),1)]),_:1}),u(B,{onClick:e[3]||(e[3]=a=>k(f).showDialog(!1))},{default:c(()=>[M(I(l.t("230")),1)]),_:1})]),_:1})]),default:c(()=>[q("div",de,[u(te,{onClick:$}),u(ue,{modelValue:d.value,"onUpdate:modelValue":e[0]||(e[0]=a=>d.value=a),onKeydown:G(F,["enter"])},null,8,["modelValue"]),u(E,{label:l.t("675"),modelValue:r.value,"onUpdate:modelValue":e[1]||(e[1]=a=>r.value=a),disabled:!g.value},null,8,["label","modelValue","disabled"])]),H((y(),L(ie,{density:"compact",color:"primary",style:{height:"300px"}},{default:c(()=>[(y(!0),P(z,null,Z(r.value?[...t.value]:n.value,(a,o)=>(y(),L(ce,{class:"ma-0",title:a,ref_for:!0,ref_key:"refItems",ref:S,key:o,onClick:m=>U(m,a,o),active:r.value?r.value:t.value.includes(a)},null,8,["title","onClick","active"]))),128))]),_:1})),[[s,T]])]),_:1},8,["modelValue","title"])}}});export{Le as default};
