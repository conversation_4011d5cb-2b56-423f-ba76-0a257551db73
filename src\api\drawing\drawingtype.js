import request from '@/utils/request'

// 查询图纸类型列表
export function listDrawingtype(query) {
  return request({
    url: '/drawingType/list',
    method: 'post',
    params: query
  })
}
export function listTree() {
  return request({
    url: '/drawingType/listTree',
    method: 'post'
  })
}

// 查询图纸类型详细
export function getDrawingtype(id) {
  return request({
    url: '/drawingType/getInfo/' + id,
    method: 'post'
  })
}

// 新增图纸类型
export function addDrawingtype(data) {
  return request({
    url: '/drawingType/add',
    method: 'post',
    data: data
  })
}

// 修改图纸类型
export function updateDrawingtype(data) {
  return request({
    url: '/drawingType/update',
    method: 'post',
    data: data
  })
}

// 删除图纸类型
export function delDrawingtype(id) {
  return request({
    url: '/drawingType/delete/' + id,
    method: 'post'
  })
}
