import{M as _e}from"./index-itnQ6avM.js";import{a4 as ke,s as v,e as H,Q as Se,_ as se,a5 as Ae}from"./index-CzBriCFR.js";import{d as $,s as J,c as C,w as U,A as P,h as le,z as Ie,R as Te,_ as b,$ as E,a0 as x,ac as z,F as Fe,a5 as Be,a3 as W,a4 as V,V as O,U as G,a9 as K,H as $e,I as Ve,m as B,Q as N,u as g,B as ae,n as X,a7 as be}from"./vue-Cj9QYd7Z.js";import{i as Oe,c as Y,b as j,L as ne,J as Re,T as Ue,d as te,F as De}from"./vuetify-BqCp6y38.js";import"./mxcad-DrgW2waE.js";import"./mxdraw-BQ5HhRCY.js";import"./handsontable-Ch5RdAT_.js";import"./mapbox-gl-DQAr7S0z.js";const Ee=m=>{const n=$({searchText:"",sortBy:"command",sortOrder:"asc"}),l=J([]),a=J([]),h=(_,M)=>{if(!M)return _;const e=M.toLowerCase().split(/\s+/);return _.map(t=>{const c=t.command.toLowerCase(),A=t.aliases.join(" ").toLowerCase();let F=0;return e.forEach(s=>{c===s?F+=5:c.startsWith(s)?F+=4:c.includes(s)?F+=3:t.aliases.some(k=>k.toLowerCase()===s)?F+=2:A.includes(s)&&(F+=1)}),{item:t,score:F}}).filter(({score:t})=>t>0).sort((t,c)=>{const A=c.score-t.score;return A!==0?A:t.item.command.localeCompare(c.item.command)}).map(({item:t})=>t)},d=_=>[..._].sort((M,e)=>{let t=0;if(n.value.sortBy==="command")t=M.command.localeCompare(e.command,void 0,{numeric:!0});else{const c=M.aliases[0]||"",A=e.aliases[0]||"";M.aliases.length===0&&e.aliases.length>0?t=1:M.aliases.length>0&&e.aliases.length===0?t=-1:t=c.localeCompare(A,void 0,{numeric:!0})}return n.value.sortOrder==="asc"?t:-t}),y=ke(()=>{l.value=h(m.value,n.value.searchText),a.value=d(l.value)},200),I=C(()=>(y(),a.value)),T=C({get:()=>n.value.searchText,set:_=>{n.value.searchText=_,y()}}),r=C(()=>n.value.sortBy),f=C(()=>n.value.sortOrder),o=_=>{n.value.sortBy===_?n.value.sortOrder=n.value.sortOrder==="asc"?"desc":"asc":(n.value.sortBy=_,n.value.sortOrder="asc"),a.value=d(l.value)},i=()=>{n.value={searchText:"",sortBy:"command",sortOrder:"asc"},l.value=[],a.value=d(m.value)};return l.value=m.value,a.value=d(m.value),{searchText:T,filteredAliasList:I,sortBy:r,sortOrder:f,toggleSort:o,resetAll:i}},We=(m=50,n)=>{const l=J([]),a=J([]),h=o=>({aliasList:JSON.parse(JSON.stringify(o)),timestamp:Date.now()}),d=o=>{const i=h(o);l.value=[...l.value,i].slice(-m),a.value=[]},y=()=>{if(l.value.length===0)return;const o=h(n.value);a.value=[...a.value,o];const i=l.value.pop();i&&(n.value=JSON.parse(JSON.stringify(i.aliasList)))},I=()=>{if(a.value.length===0)return;const o=h(n.value);l.value=[...l.value,o];const i=a.value.pop();i&&(n.value=JSON.parse(JSON.stringify(i.aliasList)))},T=()=>{l.value=[],a.value=[]},r=C(()=>l.value.length>0),f=C(()=>a.value.length>0);return{addHistory:d,handleUndo:y,handleRedo:I,clearHistory:T,canUndo:r,canRedo:f}},Le=(m,n,l,a)=>{const h=H(),d=r=>r.length<1?{valid:!1,message:v("737")}:r.length>50?{valid:!1,message:v("738")+r+v("739")}:/[<>:"\/\\|?*\x00-\x1F]/.test(r)?{valid:!1,message:v("738")+r+v("740")}:{valid:!0},y=(r,f)=>{const o=m.value.find(e=>e.id===r);if(!o)return{success:!1,message:v("741")};const i=f.map(e=>e.trim()).filter(Boolean).map(e=>({alias:e,validation:d(e)})),_=i.filter(e=>!e.validation.valid).map(e=>e.validation.message);if(_.length>0)return{success:!1,message:_.join(`
`),type:"validation"};const M=i.map(e=>e.alias);for(const e of M)if(a(e,o.command)){for(const t of m.value)if(t.id!==r&&(t.command.toLowerCase()===e.toLowerCase()||t.aliases.some(c=>c.toLowerCase()===e.toLowerCase())))return{success:!1,message:`${v("738")}"${e}"${v("742")}"${t.command}"${v("743")}`,type:"conflict"}}try{return l(m.value),n(r,M),{success:!0}}catch(e){return console.error("更新别名失败:",e),{success:!1,message:v("744"),type:"error"}}};return{handleUpdateAlias:y,handleRemoveAlias:(r,f)=>{const o=m.value.find(i=>i.id===r);if(!o)return h.error(v("741")),!1;try{return l(m.value),n(r,o.aliases.filter(i=>i!==f)),!0}catch(i){return console.error("删除别名失败:",i),h.error(v("745")),!1}},handleBatchUpdate:r=>{const f={success:[],failed:[]};for(const o of r){const i=y(o.id,o.aliases);i.success?f.success.push(o.id):f.failed.push({id:o.id,message:i.message||v("746")})}return f},validateAlias:d}},ze=(m,n,l)=>{const a=$({searchInputMap:{},menuModeMap:{},expandedMap:{},aliasFilterMap:{},focusedId:null}),h=()=>{const e=new Set(m.value.map(c=>c.id)),t={...a.value};for(const c of Object.keys(t.searchInputMap))e.has(c)||delete t.searchInputMap[c];for(const c of Object.keys(t.menuModeMap))e.has(c)||delete t.menuModeMap[c];for(const c of Object.keys(t.expandedMap))e.has(c)||delete t.expandedMap[c];for(const c of Object.keys(t.aliasFilterMap))e.has(c)||delete t.aliasFilterMap[c];a.value=t};U(()=>m.value,()=>{P(h)},{deep:!0});const d=e=>{a.value.focusedId=e,n(e)&&(a.value.menuModeMap[e]=!0)},y=e=>{setTimeout(()=>{a.value.focusedId===e&&(a.value.focusedId=null),a.value.searchInputMap[e]||(a.value.menuModeMap[e]=!1)},200)},I=e=>{if(l.value){a.value.expandedMap[e]=!a.value.expandedMap[e];return}const t=Object.entries(a.value.expandedMap).find(([c,A])=>A)?.[0];if(a.value.expandedMap[e]){a.value.expandedMap[e]=!1;return}t&&(a.value.expandedMap[t]=!1),a.value.expandedMap[e]=!0},T=(e,t)=>{a.value.aliasFilterMap[e]=t,t&&(a.value.expandedMap[e]=!0)},r=e=>{a.value.searchInputMap[e]="",a.value.menuModeMap[e]=!1,a.value.aliasFilterMap[e]=""},f=C({get:()=>a.value.searchInputMap,set:e=>a.value.searchInputMap=e}),o=C({get:()=>a.value.menuModeMap,set:e=>a.value.menuModeMap=e}),i=C({get:()=>{const e={...a.value.expandedMap};return m.value.forEach(t=>{typeof e[t.id]!="boolean"&&(e[t.id]=!1)}),e},set:e=>a.value.expandedMap=e}),_=C({get:()=>{const e={...a.value.aliasFilterMap};return m.value.forEach(t=>{e[t.id]||(e[t.id]="")}),e},set:e=>a.value.aliasFilterMap=e});return{searchInputMap:f,menuModeMap:o,expandedMap:i,aliasFilterMap:_,handleClear:r,handleFocus:d,handleBlur:y,toggleExpand:I,setAliasFilter:T,resetState:()=>{a.value={searchInputMap:{},menuModeMap:{},expandedMap:{},aliasFilterMap:{},focusedId:null}}}},Ne=()=>{const{isShow:m,showDialog:n}=Se(!1,"MxCAD_CommandAlias"),l=$(),a=$(!1),h=H(),d=async r=>{a.value=!0;try{await r()}catch(f){console.error("操作失败:",f),h.error(v("732"))}finally{a.value=!1}},y=()=>l.value?.click();return{isShow:m,showDialog:n,fileInput:l,loading:a,handleImport:y,handleFileSelect:async(r,f)=>{const o=r.target.files;o?.[0]&&(await d(async()=>{await f(o[0])}),l.value&&(l.value.value=""))},createFooterBtnList:(r,f,o,i)=>[{name:v("733"),fun:async()=>{await d(async()=>{await r(),i.value=!i.value,n(!1)})},primary:!0},{name:v("734"),fun:()=>{f()}},{name:v("735"),fun:o},{name:v("736"),fun:y},{name:v("517"),fun:()=>{n(!1)}}],withLoading:d}},je={class:"alias-cell"},He={class:"alias-container"},Je={class:"chips-container"},Pe=["title"],Ke={key:0,class:"more-hint"},Qe={class:"expanded-wrapper"},qe={class:"expanded-content"},Ge={class:"alias-input"},Xe=["title"],Ye=le({__name:"AliasCell",props:{item:{},expanded:{type:Boolean},filterText:{}},emits:["update:aliases","remove","toggle-expand","update:filterText","show-error"],setup(m,{emit:n}){const l=m,a=n,h=C(()=>Math.floor(552/100)),d=$(""),y=$(!1),I=$(!1),T=C(()=>l.filterText?l.item.aliases.filter(s=>s.toLowerCase().includes(l.filterText?.toLowerCase()||"")):l.item.aliases),r=C(()=>{const s=l.filterText?T.value:l.item.aliases;return l.expanded||s.length<=h.value?s:s.slice(0,h.value)}),f=C(()=>T.value.length>h.value),o=C(()=>{const s=T.value.length,k=l.expanded?s:Math.min(s,h.value);return s-k}),i=C(()=>!!(y.value||l.filterText||d.value.trim()));U(i,s=>{s&&!l.expanded&&a("toggle-expand")});const _=s=>{s.target.closest(".v-chip__close, .add-btn, .v-field__input")||window.getSelection()?.toString()||a("toggle-expand")},M=()=>{const s=d.value.trim();if(!s)return;const k=s.split(/[\s,]+/).map(S=>S.trim()).filter(S=>S&&!l.item.aliases.includes(S));if(k.length===0){a("show-error",v("795"));return}a("update:aliases",[...l.item.aliases,...k]),d.value="",P(()=>{A.value?.focus()})};C(()=>d.value.trim()?v("796"):v("797"));const e=s=>{s.key==="Enter"?(s.preventDefault(),M()):s.key==="Escape"?(s.preventDefault(),d.value="",I.value=!1,s.target.blur()):s.key==="Tab"?d.value.trim()||(s.preventDefault(),a("toggle-expand")):s.key===","&&(s.preventDefault(),M())},t=()=>{y.value=!0,I.value=!0},c=()=>{y.value=!1,setTimeout(()=>{I.value=!1},200)},A=$();U(()=>l.expanded,s=>{s&&P(()=>{A.value?.focus()})});const F=s=>{if(l.expanded){const k=s.target,S=k.closest(".alias-cell");k.closest(".v-dialog")&&!S&&a("toggle-expand")}};return Ie(()=>{document.addEventListener("click",F)}),Te(()=>{document.removeEventListener("click",F)}),U(d,s=>{s.trim()&&(I.value=!0)},{immediate:!0}),C(()=>Math.floor(768/150)),(s,k)=>(b(),E("div",je,[x("div",{class:z(["alias-content",{"is-expanded":s.expanded}]),onClick:G(_,["stop"])},[x("div",He,[x("div",{class:z(["chips-wrapper",{"has-focus":y.value,"has-many":s.item.aliases.length>h.value,clickable:!s.expanded&&s.item.aliases.length>0}])},[x("div",Je,[(b(!0),E(Fe,null,Be(r.value,S=>(b(),W(Oe,{key:S,size:"x-small",closable:s.expanded,class:z(["ma-1",{highlight:d.value===S}]),"onClick:close":G(D=>a("remove",S),["stop"])},{default:V(()=>[x("span",{class:"alias-text",title:S},O(S),9,Pe)]),_:2},1032,["closable","onClick:close","class"]))),128))]),!s.expanded&&f.value?(b(),E("span",Ke," +"+O(o.value),1)):K("",!0)],2)]),$e(x("div",Qe,[x("div",qe,[x("div",Ge,[B(ne,{ref_key:"inputRef",ref:A,modelValue:d.value,"onUpdate:modelValue":k[0]||(k[0]=S=>d.value=S),placeholder:g(v)("797"),onKeydown:e,onFocus:t,onBlur:c},{append:V(()=>[B(Y,{color:"primary",variant:"text",size:"small",onClick:G(M,["stop"]),disabled:!d.value.trim(),class:"add-btn"},{default:V(()=>[B(j,{start:"",size:"small",icon:"$mdi-plus"}),N(" "+O(g(v)("607")),1)]),_:1},8,["disabled"])]),_:1},8,["modelValue","placeholder"])]),!s.expanded&&o.value>0?(b(),E("span",{key:0,class:"more-hint",title:g(v)("798",[o.value])}," +"+O(o.value),9,Xe)):K("",!0)])],512),[[Ve,s.expanded]])],2)]))}}),Ze=se(Ye,[["__scopeId","data-v-18525a06"]]),ea={class:"alias-dialog"},aa={class:"search-bar mb-2"},ta={class:"d-flex align-center gap-2 ml-4"},sa={class:"grid-container"},la={class:"grid-header"},na={class:"grid-body custom-scrollbar"},oa={key:0,class:"empty-state"},ra={class:"mt-2"},ia=["onClick"],ca={class:"command-cell"},da={class:"alias-cell"},ua=le({__name:"index",setup(m){const{aliasList:n,initData:l,updateAlias:a,saveConfig:h,resetToDefault:d,exportConfig:y,importConfig:I,checkConflict:T}=Ae(),{isShow:r,showDialog:f,fileInput:o,handleFileSelect:i,createFooterBtnList:_}=Ne(),M=$(!1),e=_(h,d,y,M),{searchText:t,filteredAliasList:c,sortBy:A,sortOrder:F,toggleSort:s,resetAll:k}=Ee(n),{addHistory:S,handleUndo:D,handleRedo:Z,clearHistory:pa,canUndo:oe,canRedo:re}=We(50,n),{handleUpdateAlias:ie,handleRemoveAlias:ce}=Le(n,a,S,T),de={"ctrl + z":D,"ctrl + y":Z,"ctrl + s":async()=>{try{await h(),M.value=!M.value,f(!1)}catch(u){console.error("保存失败:",u)}}},ue=C(()=>({height:400,itemHeight:32,items:c.value})),Q=$(null),pe=u=>{Q.value=u.id},ve=u=>{const w=n.value.find(p=>p.id===u);return w?w.aliases.length>3:!1};U(()=>n.value,()=>{P(()=>{he.value={}})},{deep:!0});const L=$(!1),fe=async()=>{L.value=!0;try{await l(),k(),Q.value=null,q.success(v("加载成功"))}catch(u){console.error("初始化失败:",u),q.error(v("加载失败"))}finally{L.value=!1}};U(r,async u=>{u&&await fe()});const q=H(),me=u=>{i(u,I)},{searchInputMap:va,menuModeMap:he,handleClear:fa,handleFocus:ma,handleBlur:ha,expandedMap:ge,aliasFilterMap:xe,toggleExpand:ye,setAliasFilter:Me}=ze(n,ve,t),we=u=>({"table-row":!0,"is-highlighted":u.id===Q.value,"has-aliases":u.aliases.length>0}),ee=u=>{(u==="command"||u==="aliases")&&s(u)},Ce=(u,w)=>{const p=ie(u,w);!p.success&&p.message&&H().error(p.message)};return(u,w)=>(b(),W(_e,{title:u.t("781"),modelValue:g(r),"onUpdate:modelValue":w[4]||(w[4]=p=>ae(r)?r.value=p:null),maxWidth:"800",footerBtnList:g(e),keys:de},{default:V(()=>[x("div",ea,[B(Ue,{modelValue:L.value,"onUpdate:modelValue":w[0]||(w[0]=p=>L.value=p),class:"align-center justify-center"},{default:V(()=>[B(Re,{indeterminate:""})]),_:1},8,["modelValue"]),x("div",aa,[B(ne,{modelValue:g(t),"onUpdate:modelValue":w[1]||(w[1]=p=>ae(t)?t.value=p:null),placeholder:u.t("782"),"prepend-inner-icon":"$mdi-magnify",class:"flex-grow-1",clearable:""},null,8,["modelValue","placeholder"]),x("div",ta,[B(te,{text:u.t("783"),location:"top"},{activator:V(({props:p})=>[B(Y,X(p,{icon:"houtui",disabled:!g(oe),onClick:g(D),variant:"text",size:"32"}),null,16,["disabled","onClick"])]),_:1},8,["text"]),B(te,{text:u.t("718"),location:"top"},{activator:V(({props:p})=>[B(Y,X(p,{icon:"qianjin",disabled:!g(re),onClick:g(Z),variant:"text",size:"32"}),{append:V(()=>w[5]||(w[5]=[])),_:2},1040,["disabled","onClick"])]),_:1},8,["text"])])]),x("div",sa,[x("div",la,[x("div",{class:"command-header text-center",onClick:w[2]||(w[2]=p=>ee("command"))},[N(O(u.t("395"))+" ",1),g(A)==="command"?(b(),W(j,{key:0,size:"20",icon:g(F)==="asc"?"$mdi-arrow-up":"$mdi-arrow-down",color:"primary",class:"ml-1"},null,8,["icon"])):K("",!0)]),x("div",{class:"alias-header text-center",onClick:w[3]||(w[3]=p=>ee("aliases"))},[N(O(u.t("738"))+" ",1),g(A)==="aliases"?(b(),W(j,{key:0,size:"20",icon:g(F)==="asc"?"$mdi-arrow-up":"$mdi-arrow-down",color:"primary",class:"ml-1"},null,8,["icon"])):K("",!0)])]),x("div",na,[g(c).length===0?(b(),E("div",oa,[B(j,{size:"48",color:"grey"},{default:V(()=>w[6]||(w[6]=[N("$mdi-magnify")])),_:1}),x("p",ra,O(u.t("784")),1)])):(b(),W(De,be(X({key:1},ue.value)),{default:V(({item:p})=>[x("div",{class:z(["grid-row",we(p)]),onClick:R=>pe(p)},[x("div",ca,O(p.command),1),x("div",da,[B(Ze,{item:p,expanded:!!g(ge)[p.id],filterText:g(xe)[p.id],"onUpdate:filterText":R=>g(Me)(p.id,R),"onUpdate:aliases":R=>Ce(p.id,R),onRemove:R=>g(ce)(p.id,R),onToggleExpand:R=>g(ye)(p.id),onShowError:g(q).error},null,8,["item","expanded","filterText","onUpdate:filterText","onUpdate:aliases","onRemove","onToggleExpand","onShowError"])])],10,ia)]),_:1},16))])])]),x("input",{type:"file",ref_key:"fileInput",ref:o,accept:".json",style:{display:"none"},onChange:me},null,544)]),_:1},8,["title","modelValue","footerBtnList"]))}}),Sa=se(ua,[["__scopeId","data-v-a0552719"]]);export{Sa as default};
