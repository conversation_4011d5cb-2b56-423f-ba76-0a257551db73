import {<PERSON><PERSON><PERSON><PERSON>, McDb<PERSON>ext, McDbMText, McDb, McGePoint3d, McCmColor, MxCpp} from "mxcad"
import {McEdGetPointWorldDrawObject, MrxDbgUiPrPoint, MxDbLeadComment, MxFun} from "mxdraw";


export function useInsertText() {


    const initInsertText = async (params) => {
        const mxcad = MxCpp.getCurrentMxCAD()
        const getPoint = new MrxDbgUiPrPoint();

        const text = params.text;
           getPoint.setMessage("点击完成绘制")
           const point = await getPoint.go()
           console.log('point', point)
           mxcad.drawText(point.x, point.y, text, 5, 0, 0, 1);

    }

    const saveFiles = async (event) => {
        getFiles().then((res) => {
            const params = {
                file: res
            }
            event.source.postMessage({messageId: event.data?.id, params}, '*')

        })
    }
    const getFiles = async () => {
        return new Promise((resolve, reject) => {
            MxCpp.getCurrentMxCAD().saveFile(void 0, (data) => {
                let blob;
                const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
                if (isSafari) {
                    blob = new Blob([data.buffer], { type: "application/octet-stream" });
                } else {
                    blob = new Blob([data.buffer], { type: "application/octet-binary" });
                }
                const file = new File([blob], 'zazong' + '.mxweb', { type: isSafari ? "application/octet-stream" : "application/octet-binary" });
                resolve(file); // 返回文件对象
            }, false, true);
        });
    }

    return {
        initInsertText,
        saveFiles
    }
}
