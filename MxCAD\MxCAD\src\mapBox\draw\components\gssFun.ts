import {
    McDbAttribute,
    McDbAttributeDefinition,
    McDbBlockReference,
    McGePoint3d,
    MxCADSelectionSet,
    MxCADUiPrEntity,
    MxCpp
} from "mxcad";

let entIdMb = null
let newIds =  []

export function Mx_FormatPainterBoss (value) {
    const idsBoss = []
    if (value.arr.length > 0) {
        value.arr.forEach(item => {
            const ss = new MxCADSelectionSet();
            //选择所有图形元素
            ss.allSelect();
            ss.forEach((id)=> {
                if (id.id == item) {
                    let ent = id.getMcDbEntity();
                    let BlkRef = entIdMb.getMcDbEntity().clone()
                    BlkRef.position = new McGePoint3d(ent.position.x,ent.position.y,0); // 偏移量
                    const newBlkRefId = MxCpp.getCurrentMxCAD().drawEntity(BlkRef);
                    BlkRef = newBlkRefId.getMcDbEntity() as McDbBlockReference;
                    BlkRef.disableDisplay(true);
                    let blkRecord: any = BlkRef.blockTableRecordId.getMcDbBlockTableRecord();
                    let ids = blkRecord.getAllEntityId();
                    ids.forEach((id: any, index: any) => {
                        if (!id.isKindOf("McDbAttributeDefinition")) return;
                        let attribDef = id.getMcDbEntity() as McDbAttributeDefinition;
                        let tag = attribDef.tag;
                        let attrib = new McDbAttribute();
                        attrib.tag = tag;
                        attrib.position = attribDef.position;
                        attrib.alignmentPoint = attribDef.alignmentPoint
                        attrib.height = attribDef.height
                        attrib.trueColor = attribDef.trueColor
                        attrib.widthFactor = attribDef.widthFactor
                        if (tag === '设备编号') {
                            attrib.textString = value.code
                        }
                        attrib.isInvisible = false;
                        attrib.transformBy(BlkRef.blockTransform);
                        attrib = BlkRef.appendAttribute(attrib).getMcDbEntity() as McDbAttribute;
                        attrib.textStyle = attribDef.textStyle
                        attrib.layer = attribDef.layer
                    })
                    BlkRef.disableDisplay(false);
                    const idNew = BlkRef.getObjectID();// 获取对象ID
                    const sHandleNew = BlkRef.getHandle();// 获取对象句柄
                    const idOld = ent.getObjectID();// 获取对象ID
                    const sHandleOld = ent.getHandle();// 获取对象句柄
                    idsBoss.push({newId: idNew.id, oldId: idOld.id, templateId: entIdMb.id, handler: sHandleNew })
                    ent.erase();
                    MxCpp.getCurrentMxCAD().updateDisplay()
                    MxCpp.getCurrentMxCAD().regen();
                }
            })
        })
    }
    window.parent.postMessage(
        {
            type: 'parentCad',
            params: {
                content: '格式刷图元最终数据',
                formData: {
                    arr: idsBoss
                }
            }
        },
        '*'
    );
}
// 格式刷-框选内得设备进行跟模板一样
export async function Mx_FormatPainterGss(value) {
    const ss = new MxCADSelectionSet();
    ss.isWhileSelect = true
    ss.isSelectHighlight = true
    ss.userSelect("选择成功, 请点选或框选要进行刷新得图元：").then((is) => {
        if (is) {
            // 得到框选的两个选择点
            const { pt1, pt2 } = ss.getSelectPoint()
            ss.getIds()
            ss.forEach((id) => {
                let ent = id.getMcDbEntity();
                if (!ent) return;
                const entId = ent.getObjectID();// 获取对象ID
                const sHandle = ent.getHandle();// 获取对象句柄
                newIds.push({id: entId.id, sHandle: sHandle})
            })
            window.parent.postMessage(
                {
                    type: 'parentCad',
                    params: {
                        content: '格式刷图元校验哪些可以刷',
                        formData: {
                            arr: newIds,
                            MbId: entIdMb
                        }
                    }
                },
                '*'
            );
        }
    })
}
// 格式刷-选择模板进行校验
export async function Mx_FormatPainterOne () {
    const mxcad = MxCpp.getCurrentMxCAD();
    // 实例化一个mxcad提供的通过鼠标点击获得图形对象ID的类
    let getEnt = new MxCADUiPrEntity();
    getEnt.setMessage("请选择作为模板得设备:")
    let id = await getEnt.go();
    // 通过ID对象得到图形数据对象
    let ent = id.getMcDbEntity();
    if(!ent) return;
    entIdMb = ent.getObjectID();// 获取对象ID
    const sHandle = ent.getHandle();// 获取对象句柄
    window.parent.postMessage(
        {
            type: 'parentCad',
            params: {
                content: '格式刷图元模板校验',
                formData: {
                    id: entIdMb, // 发送所有生成的文件
                    ent: ent
                }
            }
        },
        '*'
    );
}