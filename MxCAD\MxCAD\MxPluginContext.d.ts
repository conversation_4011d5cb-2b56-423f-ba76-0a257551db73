// Generated by dts-bundle-generator v8.1.2

declare const MxPluginContext: {
	/** 设置自定义数据属性栏的限制的个数 */
	setCustomDataListLength: (length: number) => void;
	/** pinia 全局数据 */
	store: {
		/** pinia Model 聚焦控制
		 * @template
		 * ```ts
		 * const { setCommandFocus,  } = MxPluginContext.useFocus()
		 * setCommandFocus(false) // 关闭命令行输入框用户键盘输入自动聚焦 true 则开启自动聚焦
		 * filterKeydownNotFocusCodes.add("ArrowUp") // 添加按下不允许自动聚焦命令行输入框的Code(按键事件对应的e.code的值)
		 * ```
		 **/
		useFocus: import("pinia").StoreDefinition<"focus", import("pinia")._UnwrapAll<Pick<{
			isCommandFocus: import("vue").Ref<boolean>;
			setCommandFocus: (is: boolean) => void;
			setInputEl: (el: HTMLElement) => void;
			destroyInputAutoFocus: () => void;
			filterKeydownNotFocusCodes: Set<string>;
		}, "isCommandFocus" | "filterKeydownNotFocusCodes">>, Pick<{
			isCommandFocus: import("vue").Ref<boolean>;
			setCommandFocus: (is: boolean) => void;
			setInputEl: (el: HTMLElement) => void;
			destroyInputAutoFocus: () => void;
			filterKeydownNotFocusCodes: Set<string>;
		}, never>, Pick<{
			isCommandFocus: import("vue").Ref<boolean>;
			setCommandFocus: (is: boolean) => void;
			setInputEl: (el: HTMLElement) => void;
			destroyInputAutoFocus: () => void;
			filterKeydownNotFocusCodes: Set<string>;
		}, "setCommandFocus" | "setInputEl" | "destroyInputAutoFocus">>;
	};
	/** 提供的组件列表 */
	components: {};
};

export {
	MxPluginContext as default,
};

export {};
