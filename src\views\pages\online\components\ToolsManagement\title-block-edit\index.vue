<script setup>
import {
  downloadTuQian,
  getTuQianFileBy<PERSON><PERSON>,
  getTuQian<PERSON>ist,
  getVarSelectOption,
  saveTuQian,
  uploadTuQianFile
} from "@/api/onlineDesign/index.js";
import {ElMessage} from "element-plus";
import {useIframeCommunication} from '@/hooks/useIframeCommunication.js'
import {useCAdApp} from '@/hooks/useCAdApp.js'
const { proxy } = getCurrentInstance();
const emit = defineEmits(['close'])

const cadIframeRef = ref()

const {sendMessage} = useIframeCommunication()

const {cadAppSrc} = useCAdApp()

const loading = ref(false)

const upload = ref()
const taskId = new URLSearchParams(new URL(window.location.href).search).get('id')
const uploadChange = (e) => {
  uploadTitleBlockSubmit(e.file)
}

const uploadTitleBlockSubmit = (file) => {
  const formData = new FormData()
  formData.append('file', file)
  uploadTuQianFile(formData).then(res => {
   if(res.code === 200){
    proxy.$message.success("导入成功");
    getLegendList()
   }
  }).finally(() => {
    upload.value.clearFiles()
  })
}

const cadIframeSrc = cadAppSrc()


const getDwgFile = () => {
  console.log('getDwgFile')
  const data = {
    filePath: currentLegend.value.path
  }
  getTuQianFileByPath(data).then(res => {

    loading.value = true
    nextTick(() => {
      setTimeout(() => {
        const params = {
          type: "cadPreview",
          content: "Mx_cadPreview",
          formData: {
            openUrl: res,
          }
        }
        sendMessage(cadIframeRef.value, params, (res => {
        }))
        loading.value = false
      }, 2000)
    })

  }).finally(() => {
  })
}



const legendList = ref([])

const getLegendList = () => {
  getTuQianList().then(res => {
    if (res.code === 200) {
      legendList.value = res.data.map(item => {
        return {
          ...item,
          variable: ''
        }
      })
    }
  })
}

const varOptions = ref([])

const getVarOptions = () => {
  getVarSelectOption().then(res => {
    if (res.code === 200) {
      varOptions.value = res.data
    }
  })
}

const varTableData = computed(() => {
  return legendList.value.filter(item => item.id === currentLegend.value?.id)
})

const currentLegend = ref(null)
const legendClickHandle = (e, column) => {
  currentLegend.value = e
  getDwgFile()
}

const multipleSelection = ref([])

const beforeUpload=(res)=>{
console.log("🚀 ~ beforeUpload ~ res:", res)

}

const handleSelectionChange = (val) => {
  multipleSelection.value = val
}

const exportHandle = () => {
  multipleSelection.value.forEach((item) => {
    item.taskId = taskId
  })
  downloadTuQian(multipleSelection.value).then(res => {
    const blob = new Blob([res])
    saveAs(blob, '图签.zip')
  })
}

const placeHandle = (e) => {
  const text = e.variable
  if(!text) return ElMessage.error('请选择变量！')
  nextTick(() => {
    const params = {
      type: "cadPreview",
      content: "Mx_InsertText",
      formData: {
        text: e.variable,
      }
    }
    sendMessage(cadIframeRef.value, params, (res => {
    }))
  })
}

const saveHandle = () => {
  loading.value = true
  const params = {
    type: "cadPreview",
    content: "Mx_SaveFiles",
    formData: {
    }
  }
  sendMessage(cadIframeRef.value, params, (res => {
    const formData = new FormData()

    formData.append('files', res.file)
    formData.append('fileIds', currentLegend.value.fileId)
    formData.append('originPathList', currentLegend.value.path)

    saveTuQian(formData).then(res => {
      if(res.code === 200) {
        ElMessage.success('保存成功！')
        getLegendList()
      } else {
        ElMessage.error(res.msg)
      }
    }).finally(() => {
      loading.value = false
    })
  }))
}

const closeHandle = () => {
  emit('close')
}

onMounted(() => {
  getVarOptions()
  getLegendList()
})
</script>

<template>
  <!-- 图签编辑 -->
  <div class="title-block-edit" v-loading="loading">
    <div class="material">
      <div
          style="
              display: flex;
              justify-content: flex-end;
              padding: 8px;
              background: #e4f2f2;
            "
      >
        <el-upload
            ref="upload"
            style="margin-right: 10px"
            class="upload-demo"
            accept=".dwg"
            action="#"
            :show-file-list="false"
            :http-request="uploadChange"
            :before-upload="beforeUpload"
        >
          <template #trigger>
            <el-button type="primary">导入图签</el-button>
          </template>
        </el-upload>
        <el-button :disabled="!multipleSelection.length" @click="exportHandle">导出图签</el-button>
      </div>
      <div
          style="
              padding: 0px 10px 10px 10px;
              display: flex;
              justify-content: flex-start;
            "
      >
        <el-table
            size="small"
            border
            height="300px"
            style="width:160px"
            :data="legendList"
            highlight-current-row
            @row-click="legendClickHandle"
            @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="50" align="center"/>
          <el-table-column align="center" prop="name" label="图例名称">
          </el-table-column>
        </el-table>
        <div class="right-box">
          <el-table
              style="width:100%"
              size="small"
              border
              :data="varTableData"
          >
            <el-table-column align="center" label="变量插入">
              <template #default="{row}">
                <div
                    style="
                      display: flex;
                      justify-content: space-between;
                      align-items: center;
                    "
                >
                  <el-select size="small" v-model="row.variable">
                    <el-option v-for="item in varOptions" :label="item" :value="item" :key="item"></el-option>
                  </el-select>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作"  align="center">
              <template #default="{row}">
                <el-button size="small" type="primary" text @click="placeHandle(row)">放置</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="iframe-box">
            <iframe
                id="myiframeYl"
                v-if="currentLegend"
                ref="cadIframeRef"
                style="width: 100%;height: 100%"
                :src="cadIframeSrc"
            />
          </div>
        </div>
      </div>
    </div>
    <div class="b-footer">
      <el-button type="primary" @click="saveHandle">保存</el-button>
      <el-button @click="closeHandle">关闭</el-button>
    </div>
  </div>
</template>

<style scoped lang="scss">
.title-block-edit {
  // width: 500px;
 width: 100%;
  .right-box {
    flex: 1;
    height: 300px;
    display: flex;
    flex-direction: column;

    .iframe-box {
      flex: 1;

      iframe {
        height: 100%;
      }
    }
  }
}
</style>
