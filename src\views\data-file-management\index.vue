<template>
  <div class="app-container">
    <el-button type="primary" @click="uploadDialogVisible = true"
      >上传文件</el-button
    >

    <el-dialog
      v-model="uploadDialogVisible"
      title="文件上传"
      width="30%"
      @closed="handleClose"
    >
      <el-form
        ref="formRef"
        :model="uploadData"
        :rules="formRules"
        label-width="100"
      >
        <el-form-item label="文件类型" prop="type">
          <el-select v-model="uploadData.type" clearable placeholder="请选择">
            <el-option
              v-for="item in typeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="文件模块" prop="module">
          <el-select v-model="uploadData.module" clearable placeholder="请选择">
            <el-option
              v-for="item in moduleOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="文件">
          <el-upload
            ref="uploadRef"
            :accept="acceptTypes"
            :auto-upload="false"
            :http-request="handleHttpRequest"
            :limit="1"
            :on-success="handleSuccess"
            drag
            multiple
            @change="handleFileChange"
          >
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">
              将文件放到这里或 <em>点击上传</em>
            </div>
            <template #tip> </template>
          </el-upload>
          <img
            v-if="uploadData.module === 1 && coverPreview"
            :src="coverPreview"
            alt="封面预览"
            class="cover-preview"
            style="margin-top: 10px; max-width: 200px; border-radius: 4px"
          />
        </el-form-item>
        <el-alert v-if="uploadData.module" type="warning">{{
          uploadAlert
        }}</el-alert>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="uploadDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="handleConfirm">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <el-table v-loading="loading" :data="fileList" style="width: 100%">
      <el-table-column label="文件名称" prop="fileName" width="" />
      <el-table-column label="文件类型" prop="fileType" width="120">
        <template #default="scope">
          {{ typeFormat(scope.row.fileType) }}
        </template>
      </el-table-column>
      <el-table-column label="文件模块" prop="fileModule" width="120">
        <template #default="scope">
          <el-tag
            :type="scope.row.fileModule === '1' ? 'primary' : 'success'"
            >{{ moduleFormat(scope.row.fileModule) }}</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column label="上传时间" prop="insertTime" width="250" />
      <el-table-column label="操作" width="120">
        <template #default="scope">
          <el-button link type="danger" @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import {
  getFileList,
  uploadFiles,
  deleteFile,
} from "@/api/data-file-management/index.js";

const { proxy } = getCurrentInstance();
const coverPreview = ref("");

// 文件变更处理
const handleFileChange = (file) => {
  console.log("file", file);
  if (uploadData.module === 1) {
    generateVideoThumbnail(file.raw).then((blob) => {
      coverPreview.value = URL.createObjectURL(blob);
    });
  }
};
const uploadRef = ref();
const formRules = reactive({
  type: [{ required: true, message: "请选择文件类型", trigger: "change" }],
  module: [{ required: true, message: "请选择文件模块", trigger: "change" }],
});

const loading = ref(false);
const fileList = ref([]);
const uploadDialogVisible = ref(false);

const uploadAlert = computed(() => {
  const module = uploadData.module;
  if (module) {
    return `${moduleFormat(module)}支持上传${moduleFileTypes[module].join(
      ","
    )}`;
  }
  return "";
});

// 模块对应文件类型映射
const moduleFileTypes = {
  1: ["mp4"],
  2: ["doc", "docx", "xls", "xlsx", "ppt", "pptx", "dwg", "jpeg", "jpg", "pdf"],
};

// 动态计算accept类型
const acceptTypes = computed(() => {
  if (uploadData.module === 1) {
    return ".mp4";
  } else if (uploadData.module === 2) {
    return ".doc,.docx,.xls,.xlsx,.ppt,.pptx,.dwg,.jpeg,.jpg,.pdf";
  }
  return "";
});

// 视频封面生成函数
const generateVideoThumbnail = async (file) => {
  return new Promise((resolve) => {
    const video = document.createElement("video");
    const canvas = document.createElement("canvas");
    video.src = URL.createObjectURL(file);

    video.addEventListener("loadedmetadata", () => {
      video.currentTime = 0;
    });

    video.addEventListener("seeked", () => {
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      canvas.getContext("2d").drawImage(video, 0, 0);
      canvas.toBlob((blob) => {
        resolve(blob);
        URL.revokeObjectURL(video.src);
      }, "image/jpeg");
    });
  });
};

// 自定义上传处理
async function handleHttpRequest(options) {
  console.log("options", options);

  // 获取文件后缀
  const getFileExtension = (filename) => {
    return filename.split(".").pop().toLowerCase();
  };

  // 校验文件类型
  const validateFileType = (file, module) => {
    const allowedTypes = moduleFileTypes[module] || [];
    const ext = getFileExtension(file.name);
    return allowedTypes.includes(ext);
  };

  // 执行文件类型校验
  if (!validateFileType(options.file, uploadData.module)) {
    proxy.$modal.msgError("文件类型不符合当前模块要求");
    options.onError(new Error("Invalid file type"));
    return Promise.reject(new Error("Invalid file type"));
  }

  const formData = new FormData();
  formData.append("type", uploadData.type);
  formData.append("module", uploadData.module);

  formData.append("files", options.file);
  // 添加封面（仅视频文件）
  if (uploadData.module === 1) {
    const thumbnail = await generateVideoThumbnail(options.file);
    console.log('thumbnail', thumbnail)
    formData.append("files", thumbnail, "cover.jpg");
  } else {
    // formData.append("img", '');
  }

  return uploadFiles(formData)
    .then((res) => {})
    .catch((err) => {
      options.onError(err);
      throw err;
    });
}
const handleSuccess = () => {
  uploadDialogVisible.value = false;
  getList();
  proxy.$modal.msgSuccess("上传成功");
};

const formRef = ref();
const uploadData = reactive({
  type: null,
  module: null,
});

const typeOptions = [
  { value: 1, label: "架空设计" },
  { value: 2, label: "电缆设计" },
  { value: 3, label: "配电" },
  { value: 4, label: "通用工具" },
  { value: 5, label: "报表输出" },
  { value: 6, label: "其他" },
];

const moduleOptions = [
  { value: 1, label: "培训资料" },
  { value: 2, label: "用户手册" },
];

// 获取文件列表
function getList() {
  loading.value = true;
  getFileList()
    .then((response) => {
      fileList.value = response.data;
    })
    .finally(() => {
      loading.value = false;
    });
}

// 上传成功回调
function handleConfirm() {
  formRef.value.validate(async (valid) => {
    if (!valid) return;

    try {
      await proxy.$refs.uploadRef?.submit();
    } catch (e) {
      console.error("上传失败:", e);
    }
  });
}

// 删除操作
function handleDelete(row) {
  proxy.$modal
    .confirm("确认删除该文件？")
    .then(() => {
      const params = {
        fileId: row.id,
      };
      return deleteFile(params);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    });
}

// 类型格式化
function typeFormat(type) {
  return typeOptions.find((item) => item.value == type)?.label || "未知";
}

// 模块格式化
function moduleFormat(module) {
  return moduleOptions.find((item) => item.value == module)?.label || "未知";
}

// 弹窗关闭事件处理
function handleClose() {
  formRef.value.resetFields();
  uploadRef.value?.clearFiles();
}

onMounted(() => {
  getList();
});
</script>

<style scoped>
.app-container {
  padding: 20px;
}
.upload-demo {
  display: inline-block;
  margin-left: 10px;
}
</style>
