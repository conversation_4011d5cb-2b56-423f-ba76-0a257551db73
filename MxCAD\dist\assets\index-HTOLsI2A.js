import{M as Ho}from"./index-itnQ6avM.js";import{u as Wo,I as <PERSON>,$ as jo,as as Ko,a3 as qo,at as Uo,C as _o,t as Go,D as Yo,V as Xo,_ as Zo}from"./index-CzBriCFR.js";import{s as Qo,z as ts,q as ns,r as rs,M as el,h as is,d as Ue,a as tl,k as An,O as nl,Z as rl,A as il,u as U,g as sl,c as ss,a3 as $r,a4 as Je,B as jt,_ as Dn,a0 as Hr,m as je,Q as In,V as Rn,$ as ol,a5 as ll,F as al,a1 as cl}from"./vue-Cj9QYd7Z.js";import{a as ls,C as Ie}from"./mxcad-DrgW2waE.js";import"./mapbox-gl-DQAr7S0z.js";import{V as Wr,g as ul,c as dl,b as fl}from"./vuetify-BqCp6y38.js";import"./mxdraw-BQ5HhRCY.js";import"./handsontable-Ch5RdAT_.js";function _(n){this.content=n}_.prototype={constructor:_,find:function(n){for(var e=0;e<this.content.length;e+=2)if(this.content[e]===n)return e;return-1},get:function(n){var e=this.find(n);return e==-1?void 0:this.content[e+1]},update:function(n,e,t){var r=t&&t!=n?this.remove(t):this,i=r.find(n),s=r.content.slice();return i==-1?s.push(t||n,e):(s[i+1]=e,t&&(s[i]=t)),new _(s)},remove:function(n){var e=this.find(n);if(e==-1)return this;var t=this.content.slice();return t.splice(e,2),new _(t)},addToStart:function(n,e){return new _([n,e].concat(this.remove(n).content))},addToEnd:function(n,e){var t=this.remove(n).content.slice();return t.push(n,e),new _(t)},addBefore:function(n,e,t){var r=this.remove(e),i=r.content.slice(),s=r.find(n);return i.splice(s==-1?i.length:s,0,e,t),new _(i)},forEach:function(n){for(var e=0;e<this.content.length;e+=2)n(this.content[e],this.content[e+1])},prepend:function(n){return n=_.from(n),n.size?new _(n.content.concat(this.subtract(n).content)):this},append:function(n){return n=_.from(n),n.size?new _(this.subtract(n).content.concat(n.content)):this},subtract:function(n){var e=this;n=_.from(n);for(var t=0;t<n.content.length;t+=2)e=e.remove(n.content[t]);return e},toObject:function(){var n={};return this.forEach(function(e,t){n[e]=t}),n},get size(){return this.content.length>>1}};_.from=function(n){if(n instanceof _)return n;var e=[];if(n)for(var t in n)e.push(t,n[t]);return new _(e)};function as(n,e,t){for(let r=0;;r++){if(r==n.childCount||r==e.childCount)return n.childCount==e.childCount?null:t;let i=n.child(r),s=e.child(r);if(i==s){t+=i.nodeSize;continue}if(!i.sameMarkup(s))return t;if(i.isText&&i.text!=s.text){for(let o=0;i.text[o]==s.text[o];o++)t++;return t}if(i.content.size||s.content.size){let o=as(i.content,s.content,t+1);if(o!=null)return o}t+=i.nodeSize}}function cs(n,e,t,r){for(let i=n.childCount,s=e.childCount;;){if(i==0||s==0)return i==s?null:{a:t,b:r};let o=n.child(--i),l=e.child(--s),a=o.nodeSize;if(o==l){t-=a,r-=a;continue}if(!o.sameMarkup(l))return{a:t,b:r};if(o.isText&&o.text!=l.text){let c=0,u=Math.min(o.text.length,l.text.length);for(;c<u&&o.text[o.text.length-c-1]==l.text[l.text.length-c-1];)c++,t--,r--;return{a:t,b:r}}if(o.content.size||l.content.size){let c=cs(o.content,l.content,t-1,r-1);if(c)return c}t-=a,r-=a}}class k{constructor(e,t){if(this.content=e,this.size=t||0,t==null)for(let r=0;r<e.length;r++)this.size+=e[r].nodeSize}nodesBetween(e,t,r,i=0,s){for(let o=0,l=0;l<t;o++){let a=this.content[o],c=l+a.nodeSize;if(c>e&&r(a,i+l,s||null,o)!==!1&&a.content.size){let u=l+1;a.nodesBetween(Math.max(0,e-u),Math.min(a.content.size,t-u),r,i+u)}l=c}}descendants(e){this.nodesBetween(0,this.size,e)}textBetween(e,t,r,i){let s="",o=!0;return this.nodesBetween(e,t,(l,a)=>{let c=l.isText?l.text.slice(Math.max(e,a)-a,t-a):l.isLeaf?i?typeof i=="function"?i(l):i:l.type.spec.leafText?l.type.spec.leafText(l):"":"";l.isBlock&&(l.isLeaf&&c||l.isTextblock)&&r&&(o?o=!1:s+=r),s+=c},0),s}append(e){if(!e.size)return this;if(!this.size)return e;let t=this.lastChild,r=e.firstChild,i=this.content.slice(),s=0;for(t.isText&&t.sameMarkup(r)&&(i[i.length-1]=t.withText(t.text+r.text),s=1);s<e.content.length;s++)i.push(e.content[s]);return new k(i,this.size+e.size)}cut(e,t=this.size){if(e==0&&t==this.size)return this;let r=[],i=0;if(t>e)for(let s=0,o=0;o<t;s++){let l=this.content[s],a=o+l.nodeSize;a>e&&((o<e||a>t)&&(l.isText?l=l.cut(Math.max(0,e-o),Math.min(l.text.length,t-o)):l=l.cut(Math.max(0,e-o-1),Math.min(l.content.size,t-o-1))),r.push(l),i+=l.nodeSize),o=a}return new k(r,i)}cutByIndex(e,t){return e==t?k.empty:e==0&&t==this.content.length?this:new k(this.content.slice(e,t))}replaceChild(e,t){let r=this.content[e];if(r==t)return this;let i=this.content.slice(),s=this.size+t.nodeSize-r.nodeSize;return i[e]=t,new k(i,s)}addToStart(e){return new k([e].concat(this.content),this.size+e.nodeSize)}addToEnd(e){return new k(this.content.concat(e),this.size+e.nodeSize)}eq(e){if(this.content.length!=e.content.length)return!1;for(let t=0;t<this.content.length;t++)if(!this.content[t].eq(e.content[t]))return!1;return!0}get firstChild(){return this.content.length?this.content[0]:null}get lastChild(){return this.content.length?this.content[this.content.length-1]:null}get childCount(){return this.content.length}child(e){let t=this.content[e];if(!t)throw new RangeError("Index "+e+" out of range for "+this);return t}maybeChild(e){return this.content[e]||null}forEach(e){for(let t=0,r=0;t<this.content.length;t++){let i=this.content[t];e(i,r,t),r+=i.nodeSize}}findDiffStart(e,t=0){return as(this,e,t)}findDiffEnd(e,t=this.size,r=e.size){return cs(this,e,t,r)}findIndex(e,t=-1){if(e==0)return Kt(0,e);if(e==this.size)return Kt(this.content.length,e);if(e>this.size||e<0)throw new RangeError(`Position ${e} outside of fragment (${this})`);for(let r=0,i=0;;r++){let s=this.child(r),o=i+s.nodeSize;if(o>=e)return o==e||t>0?Kt(r+1,o):Kt(r,i);i=o}}toString(){return"<"+this.toStringInner()+">"}toStringInner(){return this.content.join(", ")}toJSON(){return this.content.length?this.content.map(e=>e.toJSON()):null}static fromJSON(e,t){if(!t)return k.empty;if(!Array.isArray(t))throw new RangeError("Invalid input for Fragment.fromJSON");return new k(t.map(e.nodeFromJSON))}static fromArray(e){if(!e.length)return k.empty;let t,r=0;for(let i=0;i<e.length;i++){let s=e[i];r+=s.nodeSize,i&&s.isText&&e[i-1].sameMarkup(s)?(t||(t=e.slice(0,i)),t[t.length-1]=s.withText(t[t.length-1].text+s.text)):t&&t.push(s)}return new k(t||e,r)}static from(e){if(!e)return k.empty;if(e instanceof k)return e;if(Array.isArray(e))return this.fromArray(e);if(e.attrs)return new k([e],e.nodeSize);throw new RangeError("Can not convert "+e+" to a Fragment"+(e.nodesBetween?" (looks like multiple versions of prosemirror-model were loaded)":""))}}k.empty=new k([],0);const Pn={index:0,offset:0};function Kt(n,e){return Pn.index=n,Pn.offset=e,Pn}function Qt(n,e){if(n===e)return!0;if(!(n&&typeof n=="object")||!(e&&typeof e=="object"))return!1;let t=Array.isArray(n);if(Array.isArray(e)!=t)return!1;if(t){if(n.length!=e.length)return!1;for(let r=0;r<n.length;r++)if(!Qt(n[r],e[r]))return!1}else{for(let r in n)if(!(r in e)||!Qt(n[r],e[r]))return!1;for(let r in e)if(!(r in n))return!1}return!0}let B=class nr{constructor(e,t){this.type=e,this.attrs=t}addToSet(e){let t,r=!1;for(let i=0;i<e.length;i++){let s=e[i];if(this.eq(s))return e;if(this.type.excludes(s.type))t||(t=e.slice(0,i));else{if(s.type.excludes(this.type))return e;!r&&s.type.rank>this.type.rank&&(t||(t=e.slice(0,i)),t.push(this),r=!0),t&&t.push(s)}}return t||(t=e.slice()),r||t.push(this),t}removeFromSet(e){for(let t=0;t<e.length;t++)if(this.eq(e[t]))return e.slice(0,t).concat(e.slice(t+1));return e}isInSet(e){for(let t=0;t<e.length;t++)if(this.eq(e[t]))return!0;return!1}eq(e){return this==e||this.type==e.type&&Qt(this.attrs,e.attrs)}toJSON(){let e={type:this.type.name};for(let t in this.attrs){e.attrs=this.attrs;break}return e}static fromJSON(e,t){if(!t)throw new RangeError("Invalid input for Mark.fromJSON");let r=e.marks[t.type];if(!r)throw new RangeError(`There is no mark type ${t.type} in this schema`);return r.create(t.attrs)}static sameSet(e,t){if(e==t)return!0;if(e.length!=t.length)return!1;for(let r=0;r<e.length;r++)if(!e[r].eq(t[r]))return!1;return!0}static setFrom(e){if(!e||Array.isArray(e)&&e.length==0)return nr.none;if(e instanceof nr)return[e];let t=e.slice();return t.sort((r,i)=>r.type.rank-i.type.rank),t}};B.none=[];class en extends Error{}class x{constructor(e,t,r){this.content=e,this.openStart=t,this.openEnd=r}get size(){return this.content.size-this.openStart-this.openEnd}insertAt(e,t){let r=ds(this.content,e+this.openStart,t);return r&&new x(r,this.openStart,this.openEnd)}removeBetween(e,t){return new x(us(this.content,e+this.openStart,t+this.openStart),this.openStart,this.openEnd)}eq(e){return this.content.eq(e.content)&&this.openStart==e.openStart&&this.openEnd==e.openEnd}toString(){return this.content+"("+this.openStart+","+this.openEnd+")"}toJSON(){if(!this.content.size)return null;let e={content:this.content.toJSON()};return this.openStart>0&&(e.openStart=this.openStart),this.openEnd>0&&(e.openEnd=this.openEnd),e}static fromJSON(e,t){if(!t)return x.empty;let r=t.openStart||0,i=t.openEnd||0;if(typeof r!="number"||typeof i!="number")throw new RangeError("Invalid input for Slice.fromJSON");return new x(k.fromJSON(e,t.content),r,i)}static maxOpen(e,t=!0){let r=0,i=0;for(let s=e.firstChild;s&&!s.isLeaf&&(t||!s.type.spec.isolating);s=s.firstChild)r++;for(let s=e.lastChild;s&&!s.isLeaf&&(t||!s.type.spec.isolating);s=s.lastChild)i++;return new x(e,r,i)}}x.empty=new x(k.empty,0,0);function us(n,e,t){let{index:r,offset:i}=n.findIndex(e),s=n.maybeChild(r),{index:o,offset:l}=n.findIndex(t);if(i==e||s.isText){if(l!=t&&!n.child(o).isText)throw new RangeError("Removing non-flat range");return n.cut(0,e).append(n.cut(t))}if(r!=o)throw new RangeError("Removing non-flat range");return n.replaceChild(r,s.copy(us(s.content,e-i-1,t-i-1)))}function ds(n,e,t,r){let{index:i,offset:s}=n.findIndex(e),o=n.maybeChild(i);if(s==e||o.isText)return n.cut(0,e).append(t).append(n.cut(e));let l=ds(o.content,e-s-1,t);return l&&n.replaceChild(i,o.copy(l))}function hl(n,e,t){if(t.openStart>n.depth)throw new en("Inserted content deeper than insertion position");if(n.depth-t.openStart!=e.depth-t.openEnd)throw new en("Inconsistent open depths");return fs(n,e,t,0)}function fs(n,e,t,r){let i=n.index(r),s=n.node(r);if(i==e.index(r)&&r<n.depth-t.openStart){let o=fs(n,e,t,r+1);return s.copy(s.content.replaceChild(i,o))}else if(t.content.size)if(!t.openStart&&!t.openEnd&&n.depth==r&&e.depth==r){let o=n.parent,l=o.content;return Ze(o,l.cut(0,n.parentOffset).append(t.content).append(l.cut(e.parentOffset)))}else{let{start:o,end:l}=pl(t,n);return Ze(s,ps(n,o,l,e,r))}else return Ze(s,tn(n,e,r))}function hs(n,e){if(!e.type.compatibleContent(n.type))throw new en("Cannot join "+e.type.name+" onto "+n.type.name)}function rr(n,e,t){let r=n.node(t);return hs(r,e.node(t)),r}function Xe(n,e){let t=e.length-1;t>=0&&n.isText&&n.sameMarkup(e[t])?e[t]=n.withText(e[t].text+n.text):e.push(n)}function Nt(n,e,t,r){let i=(e||n).node(t),s=0,o=e?e.index(t):i.childCount;n&&(s=n.index(t),n.depth>t?s++:n.textOffset&&(Xe(n.nodeAfter,r),s++));for(let l=s;l<o;l++)Xe(i.child(l),r);e&&e.depth==t&&e.textOffset&&Xe(e.nodeBefore,r)}function Ze(n,e){return n.type.checkContent(e),n.copy(e)}function ps(n,e,t,r,i){let s=n.depth>i&&rr(n,e,i+1),o=r.depth>i&&rr(t,r,i+1),l=[];return Nt(null,n,i,l),s&&o&&e.index(i)==t.index(i)?(hs(s,o),Xe(Ze(s,ps(n,e,t,r,i+1)),l)):(s&&Xe(Ze(s,tn(n,e,i+1)),l),Nt(e,t,i,l),o&&Xe(Ze(o,tn(t,r,i+1)),l)),Nt(r,null,i,l),new k(l)}function tn(n,e,t){let r=[];if(Nt(null,n,t,r),n.depth>t){let i=rr(n,e,t+1);Xe(Ze(i,tn(n,e,t+1)),r)}return Nt(e,null,t,r),new k(r)}function pl(n,e){let t=e.depth-n.openStart,i=e.node(t).copy(n.content);for(let s=t-1;s>=0;s--)i=e.node(s).copy(k.from(i));return{start:i.resolveNoCache(n.openStart+t),end:i.resolveNoCache(i.content.size-n.openEnd-t)}}class It{constructor(e,t,r){this.pos=e,this.path=t,this.parentOffset=r,this.depth=t.length/3-1}resolveDepth(e){return e==null?this.depth:e<0?this.depth+e:e}get parent(){return this.node(this.depth)}get doc(){return this.node(0)}node(e){return this.path[this.resolveDepth(e)*3]}index(e){return this.path[this.resolveDepth(e)*3+1]}indexAfter(e){return e=this.resolveDepth(e),this.index(e)+(e==this.depth&&!this.textOffset?0:1)}start(e){return e=this.resolveDepth(e),e==0?0:this.path[e*3-1]+1}end(e){return e=this.resolveDepth(e),this.start(e)+this.node(e).content.size}before(e){if(e=this.resolveDepth(e),!e)throw new RangeError("There is no position before the top-level node");return e==this.depth+1?this.pos:this.path[e*3-1]}after(e){if(e=this.resolveDepth(e),!e)throw new RangeError("There is no position after the top-level node");return e==this.depth+1?this.pos:this.path[e*3-1]+this.path[e*3].nodeSize}get textOffset(){return this.pos-this.path[this.path.length-1]}get nodeAfter(){let e=this.parent,t=this.index(this.depth);if(t==e.childCount)return null;let r=this.pos-this.path[this.path.length-1],i=e.child(t);return r?e.child(t).cut(r):i}get nodeBefore(){let e=this.index(this.depth),t=this.pos-this.path[this.path.length-1];return t?this.parent.child(e).cut(0,t):e==0?null:this.parent.child(e-1)}posAtIndex(e,t){t=this.resolveDepth(t);let r=this.path[t*3],i=t==0?0:this.path[t*3-1]+1;for(let s=0;s<e;s++)i+=r.child(s).nodeSize;return i}marks(){let e=this.parent,t=this.index();if(e.content.size==0)return B.none;if(this.textOffset)return e.child(t).marks;let r=e.maybeChild(t-1),i=e.maybeChild(t);if(!r){let l=r;r=i,i=l}let s=r.marks;for(var o=0;o<s.length;o++)s[o].type.spec.inclusive===!1&&(!i||!s[o].isInSet(i.marks))&&(s=s[o--].removeFromSet(s));return s}marksAcross(e){let t=this.parent.maybeChild(this.index());if(!t||!t.isInline)return null;let r=t.marks,i=e.parent.maybeChild(e.index());for(var s=0;s<r.length;s++)r[s].type.spec.inclusive===!1&&(!i||!r[s].isInSet(i.marks))&&(r=r[s--].removeFromSet(r));return r}sharedDepth(e){for(let t=this.depth;t>0;t--)if(this.start(t)<=e&&this.end(t)>=e)return t;return 0}blockRange(e=this,t){if(e.pos<this.pos)return e.blockRange(this);for(let r=this.depth-(this.parent.inlineContent||this.pos==e.pos?1:0);r>=0;r--)if(e.pos<=this.end(r)&&(!t||t(this.node(r))))return new nn(this,e,r);return null}sameParent(e){return this.pos-this.parentOffset==e.pos-e.parentOffset}max(e){return e.pos>this.pos?e:this}min(e){return e.pos<this.pos?e:this}toString(){let e="";for(let t=1;t<=this.depth;t++)e+=(e?"/":"")+this.node(t).type.name+"_"+this.index(t-1);return e+":"+this.parentOffset}static resolve(e,t){if(!(t>=0&&t<=e.content.size))throw new RangeError("Position "+t+" out of range");let r=[],i=0,s=t;for(let o=e;;){let{index:l,offset:a}=o.content.findIndex(s),c=s-a;if(r.push(o,l,i+a),!c||(o=o.child(l),o.isText))break;s=c-1,i+=a+1}return new It(t,r,s)}static resolveCached(e,t){for(let i=0;i<Bn.length;i++){let s=Bn[i];if(s.pos==t&&s.doc==e)return s}let r=Bn[zn]=It.resolve(e,t);return zn=(zn+1)%ml,r}}let Bn=[],zn=0,ml=12;class nn{constructor(e,t,r){this.$from=e,this.$to=t,this.depth=r}get start(){return this.$from.before(this.depth+1)}get end(){return this.$to.after(this.depth+1)}get parent(){return this.$from.node(this.depth)}get startIndex(){return this.$from.index(this.depth)}get endIndex(){return this.$to.indexAfter(this.depth)}}const gl=Object.create(null);let Qe=class ir{constructor(e,t,r,i=B.none){this.type=e,this.attrs=t,this.marks=i,this.content=r||k.empty}get nodeSize(){return this.isLeaf?1:2+this.content.size}get childCount(){return this.content.childCount}child(e){return this.content.child(e)}maybeChild(e){return this.content.maybeChild(e)}forEach(e){this.content.forEach(e)}nodesBetween(e,t,r,i=0){this.content.nodesBetween(e,t,r,i,this)}descendants(e){this.nodesBetween(0,this.content.size,e)}get textContent(){return this.isLeaf&&this.type.spec.leafText?this.type.spec.leafText(this):this.textBetween(0,this.content.size,"")}textBetween(e,t,r,i){return this.content.textBetween(e,t,r,i)}get firstChild(){return this.content.firstChild}get lastChild(){return this.content.lastChild}eq(e){return this==e||this.sameMarkup(e)&&this.content.eq(e.content)}sameMarkup(e){return this.hasMarkup(e.type,e.attrs,e.marks)}hasMarkup(e,t,r){return this.type==e&&Qt(this.attrs,t||e.defaultAttrs||gl)&&B.sameSet(this.marks,r||B.none)}copy(e=null){return e==this.content?this:new ir(this.type,this.attrs,e,this.marks)}mark(e){return e==this.marks?this:new ir(this.type,this.attrs,this.content,e)}cut(e,t=this.content.size){return e==0&&t==this.content.size?this:this.copy(this.content.cut(e,t))}slice(e,t=this.content.size,r=!1){if(e==t)return x.empty;let i=this.resolve(e),s=this.resolve(t),o=r?0:i.sharedDepth(t),l=i.start(o),c=i.node(o).content.cut(i.pos-l,s.pos-l);return new x(c,i.depth-o,s.depth-o)}replace(e,t,r){return hl(this.resolve(e),this.resolve(t),r)}nodeAt(e){for(let t=this;;){let{index:r,offset:i}=t.content.findIndex(e);if(t=t.maybeChild(r),!t)return null;if(i==e||t.isText)return t;e-=i+1}}childAfter(e){let{index:t,offset:r}=this.content.findIndex(e);return{node:this.content.maybeChild(t),index:t,offset:r}}childBefore(e){if(e==0)return{node:null,index:0,offset:0};let{index:t,offset:r}=this.content.findIndex(e);if(r<e)return{node:this.content.child(t),index:t,offset:r};let i=this.content.child(t-1);return{node:i,index:t-1,offset:r-i.nodeSize}}resolve(e){return It.resolveCached(this,e)}resolveNoCache(e){return It.resolve(this,e)}rangeHasMark(e,t,r){let i=!1;return t>e&&this.nodesBetween(e,t,s=>(r.isInSet(s.marks)&&(i=!0),!i)),i}get isBlock(){return this.type.isBlock}get isTextblock(){return this.type.isTextblock}get inlineContent(){return this.type.inlineContent}get isInline(){return this.type.isInline}get isText(){return this.type.isText}get isLeaf(){return this.type.isLeaf}get isAtom(){return this.type.isAtom}toString(){if(this.type.spec.toDebugString)return this.type.spec.toDebugString(this);let e=this.type.name;return this.content.size&&(e+="("+this.content.toStringInner()+")"),ms(this.marks,e)}contentMatchAt(e){let t=this.type.contentMatch.matchFragment(this.content,0,e);if(!t)throw new Error("Called contentMatchAt on a node with invalid content");return t}canReplace(e,t,r=k.empty,i=0,s=r.childCount){let o=this.contentMatchAt(e).matchFragment(r,i,s),l=o&&o.matchFragment(this.content,t);if(!l||!l.validEnd)return!1;for(let a=i;a<s;a++)if(!this.type.allowsMarks(r.child(a).marks))return!1;return!0}canReplaceWith(e,t,r,i){if(i&&!this.type.allowsMarks(i))return!1;let s=this.contentMatchAt(e).matchType(r),o=s&&s.matchFragment(this.content,t);return o?o.validEnd:!1}canAppend(e){return e.content.size?this.canReplace(this.childCount,this.childCount,e.content):this.type.compatibleContent(e.type)}check(){this.type.checkContent(this.content);let e=B.none;for(let t=0;t<this.marks.length;t++)e=this.marks[t].addToSet(e);if(!B.sameSet(e,this.marks))throw new RangeError(`Invalid collection of marks for node ${this.type.name}: ${this.marks.map(t=>t.type.name)}`);this.content.forEach(t=>t.check())}toJSON(){let e={type:this.type.name};for(let t in this.attrs){e.attrs=this.attrs;break}return this.content.size&&(e.content=this.content.toJSON()),this.marks.length&&(e.marks=this.marks.map(t=>t.toJSON())),e}static fromJSON(e,t){if(!t)throw new RangeError("Invalid input for Node.fromJSON");let r=null;if(t.marks){if(!Array.isArray(t.marks))throw new RangeError("Invalid mark data for Node.fromJSON");r=t.marks.map(e.markFromJSON)}if(t.type=="text"){if(typeof t.text!="string")throw new RangeError("Invalid text node in JSON");return e.text(t.text,r)}let i=k.fromJSON(e,t.content);return e.nodeType(t.type).create(t.attrs,i,r)}};Qe.prototype.text=void 0;class rn extends Qe{constructor(e,t,r,i){if(super(e,t,null,i),!r)throw new RangeError("Empty text nodes are not allowed");this.text=r}toString(){return this.type.spec.toDebugString?this.type.spec.toDebugString(this):ms(this.marks,JSON.stringify(this.text))}get textContent(){return this.text}textBetween(e,t){return this.text.slice(e,t)}get nodeSize(){return this.text.length}mark(e){return e==this.marks?this:new rn(this.type,this.attrs,this.text,e)}withText(e){return e==this.text?this:new rn(this.type,this.attrs,e,this.marks)}cut(e=0,t=this.text.length){return e==0&&t==this.text.length?this:this.withText(this.text.slice(e,t))}eq(e){return this.sameMarkup(e)&&this.text==e.text}toJSON(){let e=super.toJSON();return e.text=this.text,e}}function ms(n,e){for(let t=n.length-1;t>=0;t--)e=n[t].type.name+"("+e+")";return e}class rt{constructor(e){this.validEnd=e,this.next=[],this.wrapCache=[]}static parse(e,t){let r=new yl(e,t);if(r.next==null)return rt.empty;let i=gs(r);r.next&&r.err("Unexpected trailing text");let s=wl(Cl(i));return Tl(s,r),s}matchType(e){for(let t=0;t<this.next.length;t++)if(this.next[t].type==e)return this.next[t].next;return null}matchFragment(e,t=0,r=e.childCount){let i=this;for(let s=t;i&&s<r;s++)i=i.matchType(e.child(s).type);return i}get inlineContent(){return this.next.length!=0&&this.next[0].type.isInline}get defaultType(){for(let e=0;e<this.next.length;e++){let{type:t}=this.next[e];if(!(t.isText||t.hasRequiredAttrs()))return t}return null}compatible(e){for(let t=0;t<this.next.length;t++)for(let r=0;r<e.next.length;r++)if(this.next[t].type==e.next[r].type)return!0;return!1}fillBefore(e,t=!1,r=0){let i=[this];function s(o,l){let a=o.matchFragment(e,r);if(a&&(!t||a.validEnd))return k.from(l.map(c=>c.createAndFill()));for(let c=0;c<o.next.length;c++){let{type:u,next:d}=o.next[c];if(!(u.isText||u.hasRequiredAttrs())&&i.indexOf(d)==-1){i.push(d);let f=s(d,l.concat(u));if(f)return f}}return null}return s(this,[])}findWrapping(e){for(let r=0;r<this.wrapCache.length;r+=2)if(this.wrapCache[r]==e)return this.wrapCache[r+1];let t=this.computeWrapping(e);return this.wrapCache.push(e,t),t}computeWrapping(e){let t=Object.create(null),r=[{match:this,type:null,via:null}];for(;r.length;){let i=r.shift(),s=i.match;if(s.matchType(e)){let o=[];for(let l=i;l.type;l=l.via)o.push(l.type);return o.reverse()}for(let o=0;o<s.next.length;o++){let{type:l,next:a}=s.next[o];!l.isLeaf&&!l.hasRequiredAttrs()&&!(l.name in t)&&(!i.type||a.validEnd)&&(r.push({match:l.contentMatch,type:l,via:i}),t[l.name]=!0)}}return null}get edgeCount(){return this.next.length}edge(e){if(e>=this.next.length)throw new RangeError(`There's no ${e}th edge in this content match`);return this.next[e]}toString(){let e=[];function t(r){e.push(r);for(let i=0;i<r.next.length;i++)e.indexOf(r.next[i].next)==-1&&t(r.next[i].next)}return t(this),e.map((r,i)=>{let s=i+(r.validEnd?"*":" ")+" ";for(let o=0;o<r.next.length;o++)s+=(o?", ":"")+r.next[o].type.name+"->"+e.indexOf(r.next[o].next);return s}).join(`
`)}}rt.empty=new rt(!0);class yl{constructor(e,t){this.string=e,this.nodeTypes=t,this.inline=null,this.pos=0,this.tokens=e.split(/\s*(?=\b|\W|$)/),this.tokens[this.tokens.length-1]==""&&this.tokens.pop(),this.tokens[0]==""&&this.tokens.shift()}get next(){return this.tokens[this.pos]}eat(e){return this.next==e&&(this.pos++||!0)}err(e){throw new SyntaxError(e+" (in content expression '"+this.string+"')")}}function gs(n){let e=[];do e.push(kl(n));while(n.eat("|"));return e.length==1?e[0]:{type:"choice",exprs:e}}function kl(n){let e=[];do e.push(bl(n));while(n.next&&n.next!=")"&&n.next!="|");return e.length==1?e[0]:{type:"seq",exprs:e}}function bl(n){let e=Ml(n);for(;;)if(n.eat("+"))e={type:"plus",expr:e};else if(n.eat("*"))e={type:"star",expr:e};else if(n.eat("?"))e={type:"opt",expr:e};else if(n.eat("{"))e=xl(n,e);else break;return e}function Jr(n){/\D/.test(n.next)&&n.err("Expected number, got '"+n.next+"'");let e=Number(n.next);return n.pos++,e}function xl(n,e){let t=Jr(n),r=t;return n.eat(",")&&(n.next!="}"?r=Jr(n):r=-1),n.eat("}")||n.err("Unclosed braced range"),{type:"range",min:t,max:r,expr:e}}function Sl(n,e){let t=n.nodeTypes,r=t[e];if(r)return[r];let i=[];for(let s in t){let o=t[s];o.groups.indexOf(e)>-1&&i.push(o)}return i.length==0&&n.err("No node type or group '"+e+"' found"),i}function Ml(n){if(n.eat("(")){let e=gs(n);return n.eat(")")||n.err("Missing closing paren"),e}else if(/\W/.test(n.next))n.err("Unexpected token '"+n.next+"'");else{let e=Sl(n,n.next).map(t=>(n.inline==null?n.inline=t.isInline:n.inline!=t.isInline&&n.err("Mixing inline and block content"),{type:"name",value:t}));return n.pos++,e.length==1?e[0]:{type:"choice",exprs:e}}}function Cl(n){let e=[[]];return i(s(n,0),t()),e;function t(){return e.push([])-1}function r(o,l,a){let c={term:a,to:l};return e[o].push(c),c}function i(o,l){o.forEach(a=>a.to=l)}function s(o,l){if(o.type=="choice")return o.exprs.reduce((a,c)=>a.concat(s(c,l)),[]);if(o.type=="seq")for(let a=0;;a++){let c=s(o.exprs[a],l);if(a==o.exprs.length-1)return c;i(c,l=t())}else if(o.type=="star"){let a=t();return r(l,a),i(s(o.expr,a),a),[r(a)]}else if(o.type=="plus"){let a=t();return i(s(o.expr,l),a),i(s(o.expr,a),a),[r(a)]}else{if(o.type=="opt")return[r(l)].concat(s(o.expr,l));if(o.type=="range"){let a=l;for(let c=0;c<o.min;c++){let u=t();i(s(o.expr,a),u),a=u}if(o.max==-1)i(s(o.expr,a),a);else for(let c=o.min;c<o.max;c++){let u=t();r(a,u),i(s(o.expr,a),u),a=u}return[r(a)]}else{if(o.type=="name")return[r(l,void 0,o.value)];throw new Error("Unknown expr type")}}}}function ys(n,e){return e-n}function jr(n,e){let t=[];return r(e),t.sort(ys);function r(i){let s=n[i];if(s.length==1&&!s[0].term)return r(s[0].to);t.push(i);for(let o=0;o<s.length;o++){let{term:l,to:a}=s[o];!l&&t.indexOf(a)==-1&&r(a)}}}function wl(n){let e=Object.create(null);return t(jr(n,0));function t(r){let i=[];r.forEach(o=>{n[o].forEach(({term:l,to:a})=>{if(!l)return;let c;for(let u=0;u<i.length;u++)i[u][0]==l&&(c=i[u][1]);jr(n,a).forEach(u=>{c||i.push([l,c=[]]),c.indexOf(u)==-1&&c.push(u)})})});let s=e[r.join(",")]=new rt(r.indexOf(n.length-1)>-1);for(let o=0;o<i.length;o++){let l=i[o][1].sort(ys);s.next.push({type:i[o][0],next:e[l.join(",")]||t(l)})}return s}}function Tl(n,e){for(let t=0,r=[n];t<r.length;t++){let i=r[t],s=!i.validEnd,o=[];for(let l=0;l<i.next.length;l++){let{type:a,next:c}=i.next[l];o.push(a.name),s&&!(a.isText||a.hasRequiredAttrs())&&(s=!1),r.indexOf(c)==-1&&r.push(c)}s&&e.err("Only non-generatable nodes ("+o.join(", ")+") in a required position (see https://prosemirror.net/docs/guide/#generatable)")}}function ks(n){let e=Object.create(null);for(let t in n){let r=n[t];if(!r.hasDefault)return null;e[t]=r.default}return e}function bs(n,e){let t=Object.create(null);for(let r in n){let i=e&&e[r];if(i===void 0){let s=n[r];if(s.hasDefault)i=s.default;else throw new RangeError("No value supplied for attribute "+r)}t[r]=i}return t}function xs(n){let e=Object.create(null);if(n)for(let t in n)e[t]=new Ol(n[t]);return e}let Kr=class Ss{constructor(e,t,r){this.name=e,this.schema=t,this.spec=r,this.markSet=null,this.groups=r.group?r.group.split(" "):[],this.attrs=xs(r.attrs),this.defaultAttrs=ks(this.attrs),this.contentMatch=null,this.inlineContent=null,this.isBlock=!(r.inline||e=="text"),this.isText=e=="text"}get isInline(){return!this.isBlock}get isTextblock(){return this.isBlock&&this.inlineContent}get isLeaf(){return this.contentMatch==rt.empty}get isAtom(){return this.isLeaf||!!this.spec.atom}get whitespace(){return this.spec.whitespace||(this.spec.code?"pre":"normal")}hasRequiredAttrs(){for(let e in this.attrs)if(this.attrs[e].isRequired)return!0;return!1}compatibleContent(e){return this==e||this.contentMatch.compatible(e.contentMatch)}computeAttrs(e){return!e&&this.defaultAttrs?this.defaultAttrs:bs(this.attrs,e)}create(e=null,t,r){if(this.isText)throw new Error("NodeType.create can't construct text nodes");return new Qe(this,this.computeAttrs(e),k.from(t),B.setFrom(r))}createChecked(e=null,t,r){return t=k.from(t),this.checkContent(t),new Qe(this,this.computeAttrs(e),t,B.setFrom(r))}createAndFill(e=null,t,r){if(e=this.computeAttrs(e),t=k.from(t),t.size){let o=this.contentMatch.fillBefore(t);if(!o)return null;t=o.append(t)}let i=this.contentMatch.matchFragment(t),s=i&&i.fillBefore(k.empty,!0);return s?new Qe(this,e,t.append(s),B.setFrom(r)):null}validContent(e){let t=this.contentMatch.matchFragment(e);if(!t||!t.validEnd)return!1;for(let r=0;r<e.childCount;r++)if(!this.allowsMarks(e.child(r).marks))return!1;return!0}checkContent(e){if(!this.validContent(e))throw new RangeError(`Invalid content for node ${this.name}: ${e.toString().slice(0,50)}`)}allowsMarkType(e){return this.markSet==null||this.markSet.indexOf(e)>-1}allowsMarks(e){if(this.markSet==null)return!0;for(let t=0;t<e.length;t++)if(!this.allowsMarkType(e[t].type))return!1;return!0}allowedMarks(e){if(this.markSet==null)return e;let t;for(let r=0;r<e.length;r++)this.allowsMarkType(e[r].type)?t&&t.push(e[r]):t||(t=e.slice(0,r));return t?t.length?t:B.none:e}static compile(e,t){let r=Object.create(null);e.forEach((s,o)=>r[s]=new Ss(s,t,o));let i=t.spec.topNode||"doc";if(!r[i])throw new RangeError("Schema is missing its top node type ('"+i+"')");if(!r.text)throw new RangeError("Every schema needs a 'text' type");for(let s in r.text.attrs)throw new RangeError("The text node type should not have attributes");return r}};class Ol{constructor(e){this.hasDefault=Object.prototype.hasOwnProperty.call(e,"default"),this.default=e.default}get isRequired(){return!this.hasDefault}}class yn{constructor(e,t,r,i){this.name=e,this.rank=t,this.schema=r,this.spec=i,this.attrs=xs(i.attrs),this.excluded=null;let s=ks(this.attrs);this.instance=s?new B(this,s):null}create(e=null){return!e&&this.instance?this.instance:new B(this,bs(this.attrs,e))}static compile(e,t){let r=Object.create(null),i=0;return e.forEach((s,o)=>r[s]=new yn(s,i++,t,o)),r}removeFromSet(e){for(var t=0;t<e.length;t++)e[t].type==this&&(e=e.slice(0,t).concat(e.slice(t+1)),t--);return e}isInSet(e){for(let t=0;t<e.length;t++)if(e[t].type==this)return e[t]}excludes(e){return this.excluded.indexOf(e)>-1}}class vl{constructor(e){this.linebreakReplacement=null,this.cached=Object.create(null);let t=this.spec={};for(let i in e)t[i]=e[i];t.nodes=_.from(e.nodes),t.marks=_.from(e.marks||{}),this.nodes=Kr.compile(this.spec.nodes,this),this.marks=yn.compile(this.spec.marks,this);let r=Object.create(null);for(let i in this.nodes){if(i in this.marks)throw new RangeError(i+" can not be both a node and a mark");let s=this.nodes[i],o=s.spec.content||"",l=s.spec.marks;if(s.contentMatch=r[o]||(r[o]=rt.parse(o,this.nodes)),s.inlineContent=s.contentMatch.inlineContent,s.spec.linebreakReplacement){if(this.linebreakReplacement)throw new RangeError("Multiple linebreak nodes defined");if(!s.isInline||!s.isLeaf)throw new RangeError("Linebreak replacement nodes must be inline leaf nodes");this.linebreakReplacement=s}s.markSet=l=="_"?null:l?qr(this,l.split(" ")):l==""||!s.inlineContent?[]:null}for(let i in this.marks){let s=this.marks[i],o=s.spec.excludes;s.excluded=o==null?[s]:o==""?[]:qr(this,o.split(" "))}this.nodeFromJSON=this.nodeFromJSON.bind(this),this.markFromJSON=this.markFromJSON.bind(this),this.topNodeType=this.nodes[this.spec.topNode||"doc"],this.cached.wrappings=Object.create(null)}node(e,t=null,r,i){if(typeof e=="string")e=this.nodeType(e);else if(e instanceof Kr){if(e.schema!=this)throw new RangeError("Node type from different schema used ("+e.name+")")}else throw new RangeError("Invalid node type: "+e);return e.createChecked(t,r,i)}text(e,t){let r=this.nodes.text;return new rn(r,r.defaultAttrs,e,B.setFrom(t))}mark(e,t){return typeof e=="string"&&(e=this.marks[e]),e.create(t)}nodeFromJSON(e){return Qe.fromJSON(this,e)}markFromJSON(e){return B.fromJSON(this,e)}nodeType(e){let t=this.nodes[e];if(!t)throw new RangeError("Unknown node type: "+e);return t}}function qr(n,e){let t=[];for(let r=0;r<e.length;r++){let i=e[r],s=n.marks[i],o=s;if(s)t.push(s);else for(let l in n.marks){let a=n.marks[l];(i=="_"||a.spec.group&&a.spec.group.split(" ").indexOf(i)>-1)&&t.push(o=a)}if(!o)throw new SyntaxError("Unknown mark type: '"+e[r]+"'")}return t}function Nl(n){return n.tag!=null}function El(n){return n.style!=null}class gt{constructor(e,t){this.schema=e,this.rules=t,this.tags=[],this.styles=[],t.forEach(r=>{Nl(r)?this.tags.push(r):El(r)&&this.styles.push(r)}),this.normalizeLists=!this.tags.some(r=>{if(!/^(ul|ol)\b/.test(r.tag)||!r.node)return!1;let i=e.nodes[r.node];return i.contentMatch.matchType(i)})}parse(e,t={}){let r=new _r(this,t,!1);return r.addAll(e,t.from,t.to),r.finish()}parseSlice(e,t={}){let r=new _r(this,t,!0);return r.addAll(e,t.from,t.to),x.maxOpen(r.finish())}matchTag(e,t,r){for(let i=r?this.tags.indexOf(r)+1:0;i<this.tags.length;i++){let s=this.tags[i];if(Il(e,s.tag)&&(s.namespace===void 0||e.namespaceURI==s.namespace)&&(!s.context||t.matchesContext(s.context))){if(s.getAttrs){let o=s.getAttrs(e);if(o===!1)continue;s.attrs=o||void 0}return s}}}matchStyle(e,t,r,i){for(let s=i?this.styles.indexOf(i)+1:0;s<this.styles.length;s++){let o=this.styles[s],l=o.style;if(!(l.indexOf(e)!=0||o.context&&!r.matchesContext(o.context)||l.length>e.length&&(l.charCodeAt(e.length)!=61||l.slice(e.length+1)!=t))){if(o.getAttrs){let a=o.getAttrs(t);if(a===!1)continue;o.attrs=a||void 0}return o}}}static schemaRules(e){let t=[];function r(i){let s=i.priority==null?50:i.priority,o=0;for(;o<t.length;o++){let l=t[o];if((l.priority==null?50:l.priority)<s)break}t.splice(o,0,i)}for(let i in e.marks){let s=e.marks[i].spec.parseDOM;s&&s.forEach(o=>{r(o=Gr(o)),o.mark||o.ignore||o.clearMark||(o.mark=i)})}for(let i in e.nodes){let s=e.nodes[i].spec.parseDOM;s&&s.forEach(o=>{r(o=Gr(o)),o.node||o.ignore||o.mark||(o.node=i)})}return t}static fromSchema(e){return e.cached.domParser||(e.cached.domParser=new gt(e,gt.schemaRules(e)))}}const Ms={address:!0,article:!0,aside:!0,blockquote:!0,canvas:!0,dd:!0,div:!0,dl:!0,fieldset:!0,figcaption:!0,figure:!0,footer:!0,form:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,header:!0,hgroup:!0,hr:!0,li:!0,noscript:!0,ol:!0,output:!0,p:!0,pre:!0,section:!0,table:!0,tfoot:!0,ul:!0},Al={head:!0,noscript:!0,object:!0,script:!0,style:!0,title:!0},Cs={ol:!0,ul:!0},sn=1,on=2,Et=4;function Ur(n,e,t){return e!=null?(e?sn:0)|(e==="full"?on:0):n&&n.whitespace=="pre"?sn|on:t&~Et}class qt{constructor(e,t,r,i,s,o,l){this.type=e,this.attrs=t,this.marks=r,this.pendingMarks=i,this.solid=s,this.options=l,this.content=[],this.activeMarks=B.none,this.stashMarks=[],this.match=o||(l&Et?null:e.contentMatch)}findWrapping(e){if(!this.match){if(!this.type)return[];let t=this.type.contentMatch.fillBefore(k.from(e));if(t)this.match=this.type.contentMatch.matchFragment(t);else{let r=this.type.contentMatch,i;return(i=r.findWrapping(e.type))?(this.match=r,i):null}}return this.match.findWrapping(e.type)}finish(e){if(!(this.options&sn)){let r=this.content[this.content.length-1],i;if(r&&r.isText&&(i=/[ \t\r\n\u000c]+$/.exec(r.text))){let s=r;r.text.length==i[0].length?this.content.pop():this.content[this.content.length-1]=s.withText(s.text.slice(0,s.text.length-i[0].length))}}let t=k.from(this.content);return!e&&this.match&&(t=t.append(this.match.fillBefore(k.empty,!0))),this.type?this.type.create(this.attrs,t,this.marks):t}popFromStashMark(e){for(let t=this.stashMarks.length-1;t>=0;t--)if(e.eq(this.stashMarks[t]))return this.stashMarks.splice(t,1)[0]}applyPending(e){for(let t=0,r=this.pendingMarks;t<r.length;t++){let i=r[t];(this.type?this.type.allowsMarkType(i.type):Rl(i.type,e))&&!i.isInSet(this.activeMarks)&&(this.activeMarks=i.addToSet(this.activeMarks),this.pendingMarks=i.removeFromSet(this.pendingMarks))}}inlineContext(e){return this.type?this.type.inlineContent:this.content.length?this.content[0].isInline:e.parentNode&&!Ms.hasOwnProperty(e.parentNode.nodeName.toLowerCase())}}class _r{constructor(e,t,r){this.parser=e,this.options=t,this.isOpen=r,this.open=0;let i=t.topNode,s,o=Ur(null,t.preserveWhitespace,0)|(r?Et:0);i?s=new qt(i.type,i.attrs,B.none,B.none,!0,t.topMatch||i.type.contentMatch,o):r?s=new qt(null,null,B.none,B.none,!0,null,o):s=new qt(e.schema.topNodeType,null,B.none,B.none,!0,null,o),this.nodes=[s],this.find=t.findPositions,this.needsBlock=!1}get top(){return this.nodes[this.open]}addDOM(e){e.nodeType==3?this.addTextNode(e):e.nodeType==1&&this.addElement(e)}withStyleRules(e,t){let r=e.style;if(!r||!r.length)return t();let i=this.readStyles(e.style);if(!i)return;let[s,o]=i,l=this.top;for(let a=0;a<o.length;a++)this.removePendingMark(o[a],l);for(let a=0;a<s.length;a++)this.addPendingMark(s[a]);t();for(let a=0;a<s.length;a++)this.removePendingMark(s[a],l);for(let a=0;a<o.length;a++)this.addPendingMark(o[a])}addTextNode(e){let t=e.nodeValue,r=this.top;if(r.options&on||r.inlineContext(e)||/[^ \t\r\n\u000c]/.test(t)){if(r.options&sn)r.options&on?t=t.replace(/\r\n?/g,`
`):t=t.replace(/\r?\n|\r/g," ");else if(t=t.replace(/[ \t\r\n\u000c]+/g," "),/^[ \t\r\n\u000c]/.test(t)&&this.open==this.nodes.length-1){let i=r.content[r.content.length-1],s=e.previousSibling;(!i||s&&s.nodeName=="BR"||i.isText&&/[ \t\r\n\u000c]$/.test(i.text))&&(t=t.slice(1))}t&&this.insertNode(this.parser.schema.text(t)),this.findInText(e)}else this.findInside(e)}addElement(e,t){let r=e.nodeName.toLowerCase(),i;Cs.hasOwnProperty(r)&&this.parser.normalizeLists&&Dl(e);let s=this.options.ruleFromNode&&this.options.ruleFromNode(e)||(i=this.parser.matchTag(e,this,t));if(s?s.ignore:Al.hasOwnProperty(r))this.findInside(e),this.ignoreFallback(e);else if(!s||s.skip||s.closeParent){s&&s.closeParent?this.open=Math.max(0,this.open-1):s&&s.skip.nodeType&&(e=s.skip);let o,l=this.top,a=this.needsBlock;if(Ms.hasOwnProperty(r))l.content.length&&l.content[0].isInline&&this.open&&(this.open--,l=this.top),o=!0,l.type||(this.needsBlock=!0);else if(!e.firstChild){this.leafFallback(e);return}s&&s.skip?this.addAll(e):this.withStyleRules(e,()=>this.addAll(e)),o&&this.sync(l),this.needsBlock=a}else this.withStyleRules(e,()=>{this.addElementByRule(e,s,s.consuming===!1?i:void 0)})}leafFallback(e){e.nodeName=="BR"&&this.top.type&&this.top.type.inlineContent&&this.addTextNode(e.ownerDocument.createTextNode(`
`))}ignoreFallback(e){e.nodeName=="BR"&&(!this.top.type||!this.top.type.inlineContent)&&this.findPlace(this.parser.schema.text("-"))}readStyles(e){let t=B.none,r=B.none;for(let i=0,s=e.length;i<s;i++){let o=e.item(i);for(let l=void 0;;){let a=this.parser.matchStyle(o,e.getPropertyValue(o),this,l);if(!a)break;if(a.ignore)return null;if(a.clearMark?this.top.pendingMarks.concat(this.top.activeMarks).forEach(c=>{a.clearMark(c)&&(r=c.addToSet(r))}):t=this.parser.schema.marks[a.mark].create(a.attrs).addToSet(t),a.consuming===!1)l=a;else break}}return[t,r]}addElementByRule(e,t,r){let i,s,o;t.node?(s=this.parser.schema.nodes[t.node],s.isLeaf?this.insertNode(s.create(t.attrs))||this.leafFallback(e):i=this.enter(s,t.attrs||null,t.preserveWhitespace)):(o=this.parser.schema.marks[t.mark].create(t.attrs),this.addPendingMark(o));let l=this.top;if(s&&s.isLeaf)this.findInside(e);else if(r)this.addElement(e,r);else if(t.getContent)this.findInside(e),t.getContent(e,this.parser.schema).forEach(a=>this.insertNode(a));else{let a=e;typeof t.contentElement=="string"?a=e.querySelector(t.contentElement):typeof t.contentElement=="function"?a=t.contentElement(e):t.contentElement&&(a=t.contentElement),this.findAround(e,a,!0),this.addAll(a)}i&&this.sync(l)&&this.open--,o&&this.removePendingMark(o,l)}addAll(e,t,r){let i=t||0;for(let s=t?e.childNodes[t]:e.firstChild,o=r==null?null:e.childNodes[r];s!=o;s=s.nextSibling,++i)this.findAtPoint(e,i),this.addDOM(s);this.findAtPoint(e,i)}findPlace(e){let t,r;for(let i=this.open;i>=0;i--){let s=this.nodes[i],o=s.findWrapping(e);if(o&&(!t||t.length>o.length)&&(t=o,r=s,!o.length)||s.solid)break}if(!t)return!1;this.sync(r);for(let i=0;i<t.length;i++)this.enterInner(t[i],null,!1);return!0}insertNode(e){if(e.isInline&&this.needsBlock&&!this.top.type){let t=this.textblockFromContext();t&&this.enterInner(t)}if(this.findPlace(e)){this.closeExtra();let t=this.top;t.applyPending(e.type),t.match&&(t.match=t.match.matchType(e.type));let r=t.activeMarks;for(let i=0;i<e.marks.length;i++)(!t.type||t.type.allowsMarkType(e.marks[i].type))&&(r=e.marks[i].addToSet(r));return t.content.push(e.mark(r)),!0}return!1}enter(e,t,r){let i=this.findPlace(e.create(t));return i&&this.enterInner(e,t,!0,r),i}enterInner(e,t=null,r=!1,i){this.closeExtra();let s=this.top;s.applyPending(e),s.match=s.match&&s.match.matchType(e);let o=Ur(e,i,s.options);s.options&Et&&s.content.length==0&&(o|=Et),this.nodes.push(new qt(e,t,s.activeMarks,s.pendingMarks,r,null,o)),this.open++}closeExtra(e=!1){let t=this.nodes.length-1;if(t>this.open){for(;t>this.open;t--)this.nodes[t-1].content.push(this.nodes[t].finish(e));this.nodes.length=this.open+1}}finish(){return this.open=0,this.closeExtra(this.isOpen),this.nodes[0].finish(this.isOpen||this.options.topOpen)}sync(e){for(let t=this.open;t>=0;t--)if(this.nodes[t]==e)return this.open=t,!0;return!1}get currentPos(){this.closeExtra();let e=0;for(let t=this.open;t>=0;t--){let r=this.nodes[t].content;for(let i=r.length-1;i>=0;i--)e+=r[i].nodeSize;t&&e++}return e}findAtPoint(e,t){if(this.find)for(let r=0;r<this.find.length;r++)this.find[r].node==e&&this.find[r].offset==t&&(this.find[r].pos=this.currentPos)}findInside(e){if(this.find)for(let t=0;t<this.find.length;t++)this.find[t].pos==null&&e.nodeType==1&&e.contains(this.find[t].node)&&(this.find[t].pos=this.currentPos)}findAround(e,t,r){if(e!=t&&this.find)for(let i=0;i<this.find.length;i++)this.find[i].pos==null&&e.nodeType==1&&e.contains(this.find[i].node)&&t.compareDocumentPosition(this.find[i].node)&(r?2:4)&&(this.find[i].pos=this.currentPos)}findInText(e){if(this.find)for(let t=0;t<this.find.length;t++)this.find[t].node==e&&(this.find[t].pos=this.currentPos-(e.nodeValue.length-this.find[t].offset))}matchesContext(e){if(e.indexOf("|")>-1)return e.split(/\s*\|\s*/).some(this.matchesContext,this);let t=e.split("/"),r=this.options.context,i=!this.isOpen&&(!r||r.parent.type==this.nodes[0].type),s=-(r?r.depth+1:0)+(i?0:1),o=(l,a)=>{for(;l>=0;l--){let c=t[l];if(c==""){if(l==t.length-1||l==0)continue;for(;a>=s;a--)if(o(l-1,a))return!0;return!1}else{let u=a>0||a==0&&i?this.nodes[a].type:r&&a>=s?r.node(a-s).type:null;if(!u||u.name!=c&&u.groups.indexOf(c)==-1)return!1;a--}}return!0};return o(t.length-1,this.open)}textblockFromContext(){let e=this.options.context;if(e)for(let t=e.depth;t>=0;t--){let r=e.node(t).contentMatchAt(e.indexAfter(t)).defaultType;if(r&&r.isTextblock&&r.defaultAttrs)return r}for(let t in this.parser.schema.nodes){let r=this.parser.schema.nodes[t];if(r.isTextblock&&r.defaultAttrs)return r}}addPendingMark(e){let t=Pl(e,this.top.pendingMarks);t&&this.top.stashMarks.push(t),this.top.pendingMarks=e.addToSet(this.top.pendingMarks)}removePendingMark(e,t){for(let r=this.open;r>=0;r--){let i=this.nodes[r];if(i.pendingMarks.lastIndexOf(e)>-1)i.pendingMarks=e.removeFromSet(i.pendingMarks);else{i.activeMarks=e.removeFromSet(i.activeMarks);let o=i.popFromStashMark(e);o&&i.type&&i.type.allowsMarkType(o.type)&&(i.activeMarks=o.addToSet(i.activeMarks))}if(i==t)break}}}function Dl(n){for(let e=n.firstChild,t=null;e;e=e.nextSibling){let r=e.nodeType==1?e.nodeName.toLowerCase():null;r&&Cs.hasOwnProperty(r)&&t?(t.appendChild(e),e=t):r=="li"?t=e:r&&(t=null)}}function Il(n,e){return(n.matches||n.msMatchesSelector||n.webkitMatchesSelector||n.mozMatchesSelector).call(n,e)}function Gr(n){let e={};for(let t in n)e[t]=n[t];return e}function Rl(n,e){let t=e.schema.nodes;for(let r in t){let i=t[r];if(!i.allowsMarkType(n))continue;let s=[],o=l=>{s.push(l);for(let a=0;a<l.edgeCount;a++){let{type:c,next:u}=l.edge(a);if(c==e||s.indexOf(u)<0&&o(u))return!0}};if(o(i.contentMatch))return!0}}function Pl(n,e){for(let t=0;t<e.length;t++)if(n.eq(e[t]))return e[t]}class ye{constructor(e,t){this.nodes=e,this.marks=t}serializeFragment(e,t={},r){r||(r=Ln(t).createDocumentFragment());let i=r,s=[];return e.forEach(o=>{if(s.length||o.marks.length){let l=0,a=0;for(;l<s.length&&a<o.marks.length;){let c=o.marks[a];if(!this.marks[c.type.name]){a++;continue}if(!c.eq(s[l][0])||c.type.spec.spanning===!1)break;l++,a++}for(;l<s.length;)i=s.pop()[1];for(;a<o.marks.length;){let c=o.marks[a++],u=this.serializeMark(c,o.isInline,t);u&&(s.push([c,i]),i.appendChild(u.dom),i=u.contentDOM||u.dom)}}i.appendChild(this.serializeNodeInner(o,t))}),r}serializeNodeInner(e,t){let{dom:r,contentDOM:i}=ye.renderSpec(Ln(t),this.nodes[e.type.name](e));if(i){if(e.isLeaf)throw new RangeError("Content hole not allowed in a leaf node spec");this.serializeFragment(e.content,t,i)}return r}serializeNode(e,t={}){let r=this.serializeNodeInner(e,t);for(let i=e.marks.length-1;i>=0;i--){let s=this.serializeMark(e.marks[i],e.isInline,t);s&&((s.contentDOM||s.dom).appendChild(r),r=s.dom)}return r}serializeMark(e,t,r={}){let i=this.marks[e.type.name];return i&&ye.renderSpec(Ln(r),i(e,t))}static renderSpec(e,t,r=null){if(typeof t=="string")return{dom:e.createTextNode(t)};if(t.nodeType!=null)return{dom:t};if(t.dom&&t.dom.nodeType!=null)return t;let i=t[0],s=i.indexOf(" ");s>0&&(r=i.slice(0,s),i=i.slice(s+1));let o,l=r?e.createElementNS(r,i):e.createElement(i),a=t[1],c=1;if(a&&typeof a=="object"&&a.nodeType==null&&!Array.isArray(a)){c=2;for(let u in a)if(a[u]!=null){let d=u.indexOf(" ");d>0?l.setAttributeNS(u.slice(0,d),u.slice(d+1),a[u]):l.setAttribute(u,a[u])}}for(let u=c;u<t.length;u++){let d=t[u];if(d===0){if(u<t.length-1||u>c)throw new RangeError("Content hole must be the only child of its parent node");return{dom:l,contentDOM:l}}else{let{dom:f,contentDOM:h}=ye.renderSpec(e,d,r);if(l.appendChild(f),h){if(o)throw new RangeError("Multiple content holes");o=h}}}return{dom:l,contentDOM:o}}static fromSchema(e){return e.cached.domSerializer||(e.cached.domSerializer=new ye(this.nodesFromSchema(e),this.marksFromSchema(e)))}static nodesFromSchema(e){let t=Yr(e.nodes);return t.text||(t.text=r=>r.text),t}static marksFromSchema(e){return Yr(e.marks)}}function Yr(n){let e={};for(let t in n){let r=n[t].spec.toDOM;r&&(e[t]=r)}return e}function Ln(n){return n.document||window.document}const ws=65535,Ts=Math.pow(2,16);function Bl(n,e){return n+e*Ts}function Xr(n){return n&ws}function zl(n){return(n-(n&ws))/Ts}const Os=1,vs=2,Yt=4,Ns=8;class sr{constructor(e,t,r){this.pos=e,this.delInfo=t,this.recover=r}get deleted(){return(this.delInfo&Ns)>0}get deletedBefore(){return(this.delInfo&(Os|Yt))>0}get deletedAfter(){return(this.delInfo&(vs|Yt))>0}get deletedAcross(){return(this.delInfo&Yt)>0}}class se{constructor(e,t=!1){if(this.ranges=e,this.inverted=t,!e.length&&se.empty)return se.empty}recover(e){let t=0,r=Xr(e);if(!this.inverted)for(let i=0;i<r;i++)t+=this.ranges[i*3+2]-this.ranges[i*3+1];return this.ranges[r*3]+t+zl(e)}mapResult(e,t=1){return this._map(e,t,!1)}map(e,t=1){return this._map(e,t,!0)}_map(e,t,r){let i=0,s=this.inverted?2:1,o=this.inverted?1:2;for(let l=0;l<this.ranges.length;l+=3){let a=this.ranges[l]-(this.inverted?i:0);if(a>e)break;let c=this.ranges[l+s],u=this.ranges[l+o],d=a+c;if(e<=d){let f=c?e==a?-1:e==d?1:t:t,h=a+i+(f<0?0:u);if(r)return h;let p=e==(t<0?a:d)?null:Bl(l/3,e-a),m=e==a?vs:e==d?Os:Yt;return(t<0?e!=a:e!=d)&&(m|=Ns),new sr(h,m,p)}i+=u-c}return r?e+i:new sr(e+i,0,null)}touches(e,t){let r=0,i=Xr(t),s=this.inverted?2:1,o=this.inverted?1:2;for(let l=0;l<this.ranges.length;l+=3){let a=this.ranges[l]-(this.inverted?r:0);if(a>e)break;let c=this.ranges[l+s],u=a+c;if(e<=u&&l==i*3)return!0;r+=this.ranges[l+o]-c}return!1}forEach(e){let t=this.inverted?2:1,r=this.inverted?1:2;for(let i=0,s=0;i<this.ranges.length;i+=3){let o=this.ranges[i],l=o-(this.inverted?s:0),a=o+(this.inverted?0:s),c=this.ranges[i+t],u=this.ranges[i+r];e(l,l+c,a,a+u),s+=u-c}}invert(){return new se(this.ranges,!this.inverted)}toString(){return(this.inverted?"-":"")+JSON.stringify(this.ranges)}static offset(e){return e==0?se.empty:new se(e<0?[0,-e,0]:[0,0,e])}}se.empty=new se([]);class ft{constructor(e=[],t,r=0,i=e.length){this.maps=e,this.mirror=t,this.from=r,this.to=i}slice(e=0,t=this.maps.length){return new ft(this.maps,this.mirror,e,t)}copy(){return new ft(this.maps.slice(),this.mirror&&this.mirror.slice(),this.from,this.to)}appendMap(e,t){this.to=this.maps.push(e),t!=null&&this.setMirror(this.maps.length-1,t)}appendMapping(e){for(let t=0,r=this.maps.length;t<e.maps.length;t++){let i=e.getMirror(t);this.appendMap(e.maps[t],i!=null&&i<t?r+i:void 0)}}getMirror(e){if(this.mirror){for(let t=0;t<this.mirror.length;t++)if(this.mirror[t]==e)return this.mirror[t+(t%2?-1:1)]}}setMirror(e,t){this.mirror||(this.mirror=[]),this.mirror.push(e,t)}appendMappingInverted(e){for(let t=e.maps.length-1,r=this.maps.length+e.maps.length;t>=0;t--){let i=e.getMirror(t);this.appendMap(e.maps[t].invert(),i!=null&&i>t?r-i-1:void 0)}}invert(){let e=new ft;return e.appendMappingInverted(this),e}map(e,t=1){if(this.mirror)return this._map(e,t,!0);for(let r=this.from;r<this.to;r++)e=this.maps[r].map(e,t);return e}mapResult(e,t=1){return this._map(e,t,!1)}_map(e,t,r){let i=0;for(let s=this.from;s<this.to;s++){let o=this.maps[s],l=o.mapResult(e,t);if(l.recover!=null){let a=this.getMirror(s);if(a!=null&&a>s&&a<this.to){s=a,e=this.maps[a].recover(l.recover);continue}}i|=l.delInfo,e=l.pos}return r?e:new sr(e,i,null)}}const Fn=Object.create(null);class Q{getMap(){return se.empty}merge(e){return null}static fromJSON(e,t){if(!t||!t.stepType)throw new RangeError("Invalid input for Step.fromJSON");let r=Fn[t.stepType];if(!r)throw new RangeError(`No step type ${t.stepType} defined`);return r.fromJSON(e,t)}static jsonID(e,t){if(e in Fn)throw new RangeError("Duplicate use of step JSON ID "+e);return Fn[e]=t,t.prototype.jsonID=e,t}}class ${constructor(e,t){this.doc=e,this.failed=t}static ok(e){return new $(e,null)}static fail(e){return new $(null,e)}static fromReplace(e,t,r,i){try{return $.ok(e.replace(t,r,i))}catch(s){if(s instanceof en)return $.fail(s.message);throw s}}}function Mr(n,e,t){let r=[];for(let i=0;i<n.childCount;i++){let s=n.child(i);s.content.size&&(s=s.copy(Mr(s.content,e,s))),s.isInline&&(s=e(s,t,i)),r.push(s)}return k.fromArray(r)}class Re extends Q{constructor(e,t,r){super(),this.from=e,this.to=t,this.mark=r}apply(e){let t=e.slice(this.from,this.to),r=e.resolve(this.from),i=r.node(r.sharedDepth(this.to)),s=new x(Mr(t.content,(o,l)=>!o.isAtom||!l.type.allowsMarkType(this.mark.type)?o:o.mark(this.mark.addToSet(o.marks)),i),t.openStart,t.openEnd);return $.fromReplace(e,this.from,this.to,s)}invert(){return new ke(this.from,this.to,this.mark)}map(e){let t=e.mapResult(this.from,1),r=e.mapResult(this.to,-1);return t.deleted&&r.deleted||t.pos>=r.pos?null:new Re(t.pos,r.pos,this.mark)}merge(e){return e instanceof Re&&e.mark.eq(this.mark)&&this.from<=e.to&&this.to>=e.from?new Re(Math.min(this.from,e.from),Math.max(this.to,e.to),this.mark):null}toJSON(){return{stepType:"addMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(e,t){if(typeof t.from!="number"||typeof t.to!="number")throw new RangeError("Invalid input for AddMarkStep.fromJSON");return new Re(t.from,t.to,e.markFromJSON(t.mark))}}Q.jsonID("addMark",Re);class ke extends Q{constructor(e,t,r){super(),this.from=e,this.to=t,this.mark=r}apply(e){let t=e.slice(this.from,this.to),r=new x(Mr(t.content,i=>i.mark(this.mark.removeFromSet(i.marks)),e),t.openStart,t.openEnd);return $.fromReplace(e,this.from,this.to,r)}invert(){return new Re(this.from,this.to,this.mark)}map(e){let t=e.mapResult(this.from,1),r=e.mapResult(this.to,-1);return t.deleted&&r.deleted||t.pos>=r.pos?null:new ke(t.pos,r.pos,this.mark)}merge(e){return e instanceof ke&&e.mark.eq(this.mark)&&this.from<=e.to&&this.to>=e.from?new ke(Math.min(this.from,e.from),Math.max(this.to,e.to),this.mark):null}toJSON(){return{stepType:"removeMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(e,t){if(typeof t.from!="number"||typeof t.to!="number")throw new RangeError("Invalid input for RemoveMarkStep.fromJSON");return new ke(t.from,t.to,e.markFromJSON(t.mark))}}Q.jsonID("removeMark",ke);class Pe extends Q{constructor(e,t){super(),this.pos=e,this.mark=t}apply(e){let t=e.nodeAt(this.pos);if(!t)return $.fail("No node at mark step's position");let r=t.type.create(t.attrs,null,this.mark.addToSet(t.marks));return $.fromReplace(e,this.pos,this.pos+1,new x(k.from(r),0,t.isLeaf?0:1))}invert(e){let t=e.nodeAt(this.pos);if(t){let r=this.mark.addToSet(t.marks);if(r.length==t.marks.length){for(let i=0;i<t.marks.length;i++)if(!t.marks[i].isInSet(r))return new Pe(this.pos,t.marks[i]);return new Pe(this.pos,this.mark)}}return new yt(this.pos,this.mark)}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new Pe(t.pos,this.mark)}toJSON(){return{stepType:"addNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(e,t){if(typeof t.pos!="number")throw new RangeError("Invalid input for AddNodeMarkStep.fromJSON");return new Pe(t.pos,e.markFromJSON(t.mark))}}Q.jsonID("addNodeMark",Pe);class yt extends Q{constructor(e,t){super(),this.pos=e,this.mark=t}apply(e){let t=e.nodeAt(this.pos);if(!t)return $.fail("No node at mark step's position");let r=t.type.create(t.attrs,null,this.mark.removeFromSet(t.marks));return $.fromReplace(e,this.pos,this.pos+1,new x(k.from(r),0,t.isLeaf?0:1))}invert(e){let t=e.nodeAt(this.pos);return!t||!this.mark.isInSet(t.marks)?this:new Pe(this.pos,this.mark)}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new yt(t.pos,this.mark)}toJSON(){return{stepType:"removeNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(e,t){if(typeof t.pos!="number")throw new RangeError("Invalid input for RemoveNodeMarkStep.fromJSON");return new yt(t.pos,e.markFromJSON(t.mark))}}Q.jsonID("removeNodeMark",yt);class J extends Q{constructor(e,t,r,i=!1){super(),this.from=e,this.to=t,this.slice=r,this.structure=i}apply(e){return this.structure&&or(e,this.from,this.to)?$.fail("Structure replace would overwrite content"):$.fromReplace(e,this.from,this.to,this.slice)}getMap(){return new se([this.from,this.to-this.from,this.slice.size])}invert(e){return new J(this.from,this.from+this.slice.size,e.slice(this.from,this.to))}map(e){let t=e.mapResult(this.from,1),r=e.mapResult(this.to,-1);return t.deletedAcross&&r.deletedAcross?null:new J(t.pos,Math.max(t.pos,r.pos),this.slice)}merge(e){if(!(e instanceof J)||e.structure||this.structure)return null;if(this.from+this.slice.size==e.from&&!this.slice.openEnd&&!e.slice.openStart){let t=this.slice.size+e.slice.size==0?x.empty:new x(this.slice.content.append(e.slice.content),this.slice.openStart,e.slice.openEnd);return new J(this.from,this.to+(e.to-e.from),t,this.structure)}else if(e.to==this.from&&!this.slice.openStart&&!e.slice.openEnd){let t=this.slice.size+e.slice.size==0?x.empty:new x(e.slice.content.append(this.slice.content),e.slice.openStart,this.slice.openEnd);return new J(e.from,this.to,t,this.structure)}else return null}toJSON(){let e={stepType:"replace",from:this.from,to:this.to};return this.slice.size&&(e.slice=this.slice.toJSON()),this.structure&&(e.structure=!0),e}static fromJSON(e,t){if(typeof t.from!="number"||typeof t.to!="number")throw new RangeError("Invalid input for ReplaceStep.fromJSON");return new J(t.from,t.to,x.fromJSON(e,t.slice),!!t.structure)}}Q.jsonID("replace",J);class j extends Q{constructor(e,t,r,i,s,o,l=!1){super(),this.from=e,this.to=t,this.gapFrom=r,this.gapTo=i,this.slice=s,this.insert=o,this.structure=l}apply(e){if(this.structure&&(or(e,this.from,this.gapFrom)||or(e,this.gapTo,this.to)))return $.fail("Structure gap-replace would overwrite content");let t=e.slice(this.gapFrom,this.gapTo);if(t.openStart||t.openEnd)return $.fail("Gap is not a flat range");let r=this.slice.insertAt(this.insert,t.content);return r?$.fromReplace(e,this.from,this.to,r):$.fail("Content does not fit in gap")}getMap(){return new se([this.from,this.gapFrom-this.from,this.insert,this.gapTo,this.to-this.gapTo,this.slice.size-this.insert])}invert(e){let t=this.gapTo-this.gapFrom;return new j(this.from,this.from+this.slice.size+t,this.from+this.insert,this.from+this.insert+t,e.slice(this.from,this.to).removeBetween(this.gapFrom-this.from,this.gapTo-this.from),this.gapFrom-this.from,this.structure)}map(e){let t=e.mapResult(this.from,1),r=e.mapResult(this.to,-1),i=this.from==this.gapFrom?t.pos:e.map(this.gapFrom,-1),s=this.to==this.gapTo?r.pos:e.map(this.gapTo,1);return t.deletedAcross&&r.deletedAcross||i<t.pos||s>r.pos?null:new j(t.pos,r.pos,i,s,this.slice,this.insert,this.structure)}toJSON(){let e={stepType:"replaceAround",from:this.from,to:this.to,gapFrom:this.gapFrom,gapTo:this.gapTo,insert:this.insert};return this.slice.size&&(e.slice=this.slice.toJSON()),this.structure&&(e.structure=!0),e}static fromJSON(e,t){if(typeof t.from!="number"||typeof t.to!="number"||typeof t.gapFrom!="number"||typeof t.gapTo!="number"||typeof t.insert!="number")throw new RangeError("Invalid input for ReplaceAroundStep.fromJSON");return new j(t.from,t.to,t.gapFrom,t.gapTo,x.fromJSON(e,t.slice),t.insert,!!t.structure)}}Q.jsonID("replaceAround",j);function or(n,e,t){let r=n.resolve(e),i=t-e,s=r.depth;for(;i>0&&s>0&&r.indexAfter(s)==r.node(s).childCount;)s--,i--;if(i>0){let o=r.node(s).maybeChild(r.indexAfter(s));for(;i>0;){if(!o||o.isLeaf)return!0;o=o.firstChild,i--}}return!1}function Ll(n,e,t,r){let i=[],s=[],o,l;n.doc.nodesBetween(e,t,(a,c,u)=>{if(!a.isInline)return;let d=a.marks;if(!r.isInSet(d)&&u.type.allowsMarkType(r.type)){let f=Math.max(c,e),h=Math.min(c+a.nodeSize,t),p=r.addToSet(d);for(let m=0;m<d.length;m++)d[m].isInSet(p)||(o&&o.to==f&&o.mark.eq(d[m])?o.to=h:i.push(o=new ke(f,h,d[m])));l&&l.to==f?l.to=h:s.push(l=new Re(f,h,r))}}),i.forEach(a=>n.step(a)),s.forEach(a=>n.step(a))}function Fl(n,e,t,r){let i=[],s=0;n.doc.nodesBetween(e,t,(o,l)=>{if(!o.isInline)return;s++;let a=null;if(r instanceof yn){let c=o.marks,u;for(;u=r.isInSet(c);)(a||(a=[])).push(u),c=u.removeFromSet(c)}else r?r.isInSet(o.marks)&&(a=[r]):a=o.marks;if(a&&a.length){let c=Math.min(l+o.nodeSize,t);for(let u=0;u<a.length;u++){let d=a[u],f;for(let h=0;h<i.length;h++){let p=i[h];p.step==s-1&&d.eq(i[h].style)&&(f=p)}f?(f.to=c,f.step=s):i.push({style:d,from:Math.max(l,e),to:c,step:s})}}}),i.forEach(o=>n.step(new ke(o.from,o.to,o.style)))}function Es(n,e,t,r=t.contentMatch,i=!0){let s=n.doc.nodeAt(e),o=[],l=e+1;for(let a=0;a<s.childCount;a++){let c=s.child(a),u=l+c.nodeSize,d=r.matchType(c.type);if(!d)o.push(new J(l,u,x.empty));else{r=d;for(let f=0;f<c.marks.length;f++)t.allowsMarkType(c.marks[f].type)||n.step(new ke(l,u,c.marks[f]));if(i&&c.isText&&t.whitespace!="pre"){let f,h=/\r?\n|\r/g,p;for(;f=h.exec(c.text);)p||(p=new x(k.from(t.schema.text(" ",t.allowedMarks(c.marks))),0,0)),o.push(new J(l+f.index,l+f.index+f[0].length,p))}}l=u}if(!r.validEnd){let a=r.fillBefore(k.empty,!0);n.replace(l,l,new x(a,0,0))}for(let a=o.length-1;a>=0;a--)n.step(o[a])}function Vl(n,e,t){return(e==0||n.canReplace(e,n.childCount))&&(t==n.childCount||n.canReplace(0,t))}function Ct(n){let t=n.parent.content.cutByIndex(n.startIndex,n.endIndex);for(let r=n.depth;;--r){let i=n.$from.node(r),s=n.$from.index(r),o=n.$to.indexAfter(r);if(r<n.depth&&i.canReplace(s,o,t))return r;if(r==0||i.type.spec.isolating||!Vl(i,s,o))break}return null}function $l(n,e,t){let{$from:r,$to:i,depth:s}=e,o=r.before(s+1),l=i.after(s+1),a=o,c=l,u=k.empty,d=0;for(let p=s,m=!1;p>t;p--)m||r.index(p)>0?(m=!0,u=k.from(r.node(p).copy(u)),d++):a--;let f=k.empty,h=0;for(let p=s,m=!1;p>t;p--)m||i.after(p+1)<i.end(p)?(m=!0,f=k.from(i.node(p).copy(f)),h++):c++;n.step(new j(a,c,o,l,new x(u.append(f),d,h),u.size-d,!0))}function Cr(n,e,t=null,r=n){let i=Hl(n,e),s=i&&Wl(r,e);return s?i.map(Zr).concat({type:e,attrs:t}).concat(s.map(Zr)):null}function Zr(n){return{type:n,attrs:null}}function Hl(n,e){let{parent:t,startIndex:r,endIndex:i}=n,s=t.contentMatchAt(r).findWrapping(e);if(!s)return null;let o=s.length?s[0]:e;return t.canReplaceWith(r,i,o)?s:null}function Wl(n,e){let{parent:t,startIndex:r,endIndex:i}=n,s=t.child(r),o=e.contentMatch.findWrapping(s.type);if(!o)return null;let a=(o.length?o[o.length-1]:e).contentMatch;for(let c=r;a&&c<i;c++)a=a.matchType(t.child(c).type);return!a||!a.validEnd?null:o}function Jl(n,e,t){let r=k.empty;for(let o=t.length-1;o>=0;o--){if(r.size){let l=t[o].type.contentMatch.matchFragment(r);if(!l||!l.validEnd)throw new RangeError("Wrapper type given to Transform.wrap does not form valid content of its parent wrapper")}r=k.from(t[o].type.create(t[o].attrs,r))}let i=e.start,s=e.end;n.step(new j(i,s,i,s,new x(r,0,0),t.length,!0))}function jl(n,e,t,r,i){if(!r.isTextblock)throw new RangeError("Type given to setBlockType should be a textblock");let s=n.steps.length;n.doc.nodesBetween(e,t,(o,l)=>{if(o.isTextblock&&!o.hasMarkup(r,i)&&Ul(n.doc,n.mapping.slice(s).map(l),r)){let a=null;if(r.schema.linebreakReplacement){let f=r.whitespace=="pre",h=!!r.contentMatch.matchType(r.schema.linebreakReplacement);f&&!h?a=!1:!f&&h&&(a=!0)}a===!1&&ql(n,o,l,s),Es(n,n.mapping.slice(s).map(l,1),r,void 0,a===null);let c=n.mapping.slice(s),u=c.map(l,1),d=c.map(l+o.nodeSize,1);return n.step(new j(u,d,u+1,d-1,new x(k.from(r.create(i,null,o.marks)),0,0),1,!0)),a===!0&&Kl(n,o,l,s),!1}})}function Kl(n,e,t,r){e.forEach((i,s)=>{if(i.isText){let o,l=/\r?\n|\r/g;for(;o=l.exec(i.text);){let a=n.mapping.slice(r).map(t+1+s+o.index);n.replaceWith(a,a+1,e.type.schema.linebreakReplacement.create())}}})}function ql(n,e,t,r){e.forEach((i,s)=>{if(i.type==i.type.schema.linebreakReplacement){let o=n.mapping.slice(r).map(t+1+s);n.replaceWith(o,o+1,e.type.schema.text(`
`))}})}function Ul(n,e,t){let r=n.resolve(e),i=r.index();return r.parent.canReplaceWith(i,i+1,t)}function _l(n,e,t,r,i){let s=n.doc.nodeAt(e);if(!s)throw new RangeError("No node at given position");t||(t=s.type);let o=t.create(r,null,i||s.marks);if(s.isLeaf)return n.replaceWith(e,e+s.nodeSize,o);if(!t.validContent(s.content))throw new RangeError("Invalid content for node type "+t.name);n.step(new j(e,e+s.nodeSize,e+1,e+s.nodeSize-1,new x(k.from(o),0,0),1,!0))}function ht(n,e,t=1,r){let i=n.resolve(e),s=i.depth-t,o=r&&r[r.length-1]||i.parent;if(s<0||i.parent.type.spec.isolating||!i.parent.canReplace(i.index(),i.parent.childCount)||!o.type.validContent(i.parent.content.cutByIndex(i.index(),i.parent.childCount)))return!1;for(let c=i.depth-1,u=t-2;c>s;c--,u--){let d=i.node(c),f=i.index(c);if(d.type.spec.isolating)return!1;let h=d.content.cutByIndex(f,d.childCount),p=r&&r[u+1];p&&(h=h.replaceChild(0,p.type.create(p.attrs)));let m=r&&r[u]||d;if(!d.canReplace(f+1,d.childCount)||!m.type.validContent(h))return!1}let l=i.indexAfter(s),a=r&&r[0];return i.node(s).canReplaceWith(l,l,a?a.type:i.node(s+1).type)}function Gl(n,e,t=1,r){let i=n.doc.resolve(e),s=k.empty,o=k.empty;for(let l=i.depth,a=i.depth-t,c=t-1;l>a;l--,c--){s=k.from(i.node(l).copy(s));let u=r&&r[c];o=k.from(u?u.type.create(u.attrs,o):i.node(l).copy(o))}n.step(new J(e,e,new x(s.append(o),t,t),!0))}function $e(n,e){let t=n.resolve(e),r=t.index();return As(t.nodeBefore,t.nodeAfter)&&t.parent.canReplace(r,r+1)}function As(n,e){return!!(n&&e&&!n.isLeaf&&n.canAppend(e))}function kn(n,e,t=-1){let r=n.resolve(e);for(let i=r.depth;;i--){let s,o,l=r.index(i);if(i==r.depth?(s=r.nodeBefore,o=r.nodeAfter):t>0?(s=r.node(i+1),l++,o=r.node(i).maybeChild(l)):(s=r.node(i).maybeChild(l-1),o=r.node(i+1)),s&&!s.isTextblock&&As(s,o)&&r.node(i).canReplace(l,l+1))return e;if(i==0)break;e=t<0?r.before(i):r.after(i)}}function Yl(n,e,t){let r=new J(e-t,e+t,x.empty,!0);n.step(r)}function Xl(n,e,t){let r=n.resolve(e);if(r.parent.canReplaceWith(r.index(),r.index(),t))return e;if(r.parentOffset==0)for(let i=r.depth-1;i>=0;i--){let s=r.index(i);if(r.node(i).canReplaceWith(s,s,t))return r.before(i+1);if(s>0)return null}if(r.parentOffset==r.parent.content.size)for(let i=r.depth-1;i>=0;i--){let s=r.indexAfter(i);if(r.node(i).canReplaceWith(s,s,t))return r.after(i+1);if(s<r.node(i).childCount)return null}return null}function Ds(n,e,t){let r=n.resolve(e);if(!t.content.size)return e;let i=t.content;for(let s=0;s<t.openStart;s++)i=i.firstChild.content;for(let s=1;s<=(t.openStart==0&&t.size?2:1);s++)for(let o=r.depth;o>=0;o--){let l=o==r.depth?0:r.pos<=(r.start(o+1)+r.end(o+1))/2?-1:1,a=r.index(o)+(l>0?1:0),c=r.node(o),u=!1;if(s==1)u=c.canReplace(a,a,i);else{let d=c.contentMatchAt(a).findWrapping(i.firstChild.type);u=d&&c.canReplaceWith(a,a,d[0])}if(u)return l==0?r.pos:l<0?r.before(o+1):r.after(o+1)}return null}function bn(n,e,t=e,r=x.empty){if(e==t&&!r.size)return null;let i=n.resolve(e),s=n.resolve(t);return Is(i,s,r)?new J(e,t,r):new Zl(i,s,r).fit()}function Is(n,e,t){return!t.openStart&&!t.openEnd&&n.start()==e.start()&&n.parent.canReplace(n.index(),e.index(),t.content)}class Zl{constructor(e,t,r){this.$from=e,this.$to=t,this.unplaced=r,this.frontier=[],this.placed=k.empty;for(let i=0;i<=e.depth;i++){let s=e.node(i);this.frontier.push({type:s.type,match:s.contentMatchAt(e.indexAfter(i))})}for(let i=e.depth;i>0;i--)this.placed=k.from(e.node(i).copy(this.placed))}get depth(){return this.frontier.length-1}fit(){for(;this.unplaced.size;){let c=this.findFittable();c?this.placeNodes(c):this.openMore()||this.dropNode()}let e=this.mustMoveInline(),t=this.placed.size-this.depth-this.$from.depth,r=this.$from,i=this.close(e<0?this.$to:r.doc.resolve(e));if(!i)return null;let s=this.placed,o=r.depth,l=i.depth;for(;o&&l&&s.childCount==1;)s=s.firstChild.content,o--,l--;let a=new x(s,o,l);return e>-1?new j(r.pos,e,this.$to.pos,this.$to.end(),a,t):a.size||r.pos!=this.$to.pos?new J(r.pos,i.pos,a):null}findFittable(){let e=this.unplaced.openStart;for(let t=this.unplaced.content,r=0,i=this.unplaced.openEnd;r<e;r++){let s=t.firstChild;if(t.childCount>1&&(i=0),s.type.spec.isolating&&i<=r){e=r;break}t=s.content}for(let t=1;t<=2;t++)for(let r=t==1?e:this.unplaced.openStart;r>=0;r--){let i,s=null;r?(s=Vn(this.unplaced.content,r-1).firstChild,i=s.content):i=this.unplaced.content;let o=i.firstChild;for(let l=this.depth;l>=0;l--){let{type:a,match:c}=this.frontier[l],u,d=null;if(t==1&&(o?c.matchType(o.type)||(d=c.fillBefore(k.from(o),!1)):s&&a.compatibleContent(s.type)))return{sliceDepth:r,frontierDepth:l,parent:s,inject:d};if(t==2&&o&&(u=c.findWrapping(o.type)))return{sliceDepth:r,frontierDepth:l,parent:s,wrap:u};if(s&&c.matchType(s.type))break}}}openMore(){let{content:e,openStart:t,openEnd:r}=this.unplaced,i=Vn(e,t);return!i.childCount||i.firstChild.isLeaf?!1:(this.unplaced=new x(e,t+1,Math.max(r,i.size+t>=e.size-r?t+1:0)),!0)}dropNode(){let{content:e,openStart:t,openEnd:r}=this.unplaced,i=Vn(e,t);if(i.childCount<=1&&t>0){let s=e.size-t<=t+i.size;this.unplaced=new x(Tt(e,t-1,1),t-1,s?t-1:r)}else this.unplaced=new x(Tt(e,t,1),t,r)}placeNodes({sliceDepth:e,frontierDepth:t,parent:r,inject:i,wrap:s}){for(;this.depth>t;)this.closeFrontierNode();if(s)for(let m=0;m<s.length;m++)this.openFrontierNode(s[m]);let o=this.unplaced,l=r?r.content:o.content,a=o.openStart-e,c=0,u=[],{match:d,type:f}=this.frontier[t];if(i){for(let m=0;m<i.childCount;m++)u.push(i.child(m));d=d.matchFragment(i)}let h=l.size+e-(o.content.size-o.openEnd);for(;c<l.childCount;){let m=l.child(c),g=d.matchType(m.type);if(!g)break;c++,(c>1||a==0||m.content.size)&&(d=g,u.push(Rs(m.mark(f.allowedMarks(m.marks)),c==1?a:0,c==l.childCount?h:-1)))}let p=c==l.childCount;p||(h=-1),this.placed=Ot(this.placed,t,k.from(u)),this.frontier[t].match=d,p&&h<0&&r&&r.type==this.frontier[this.depth].type&&this.frontier.length>1&&this.closeFrontierNode();for(let m=0,g=l;m<h;m++){let y=g.lastChild;this.frontier.push({type:y.type,match:y.contentMatchAt(y.childCount)}),g=y.content}this.unplaced=p?e==0?x.empty:new x(Tt(o.content,e-1,1),e-1,h<0?o.openEnd:e-1):new x(Tt(o.content,e,c),o.openStart,o.openEnd)}mustMoveInline(){if(!this.$to.parent.isTextblock)return-1;let e=this.frontier[this.depth],t;if(!e.type.isTextblock||!$n(this.$to,this.$to.depth,e.type,e.match,!1)||this.$to.depth==this.depth&&(t=this.findCloseLevel(this.$to))&&t.depth==this.depth)return-1;let{depth:r}=this.$to,i=this.$to.after(r);for(;r>1&&i==this.$to.end(--r);)++i;return i}findCloseLevel(e){e:for(let t=Math.min(this.depth,e.depth);t>=0;t--){let{match:r,type:i}=this.frontier[t],s=t<e.depth&&e.end(t+1)==e.pos+(e.depth-(t+1)),o=$n(e,t,i,r,s);if(o){for(let l=t-1;l>=0;l--){let{match:a,type:c}=this.frontier[l],u=$n(e,l,c,a,!0);if(!u||u.childCount)continue e}return{depth:t,fit:o,move:s?e.doc.resolve(e.after(t+1)):e}}}}close(e){let t=this.findCloseLevel(e);if(!t)return null;for(;this.depth>t.depth;)this.closeFrontierNode();t.fit.childCount&&(this.placed=Ot(this.placed,t.depth,t.fit)),e=t.move;for(let r=t.depth+1;r<=e.depth;r++){let i=e.node(r),s=i.type.contentMatch.fillBefore(i.content,!0,e.index(r));this.openFrontierNode(i.type,i.attrs,s)}return e}openFrontierNode(e,t=null,r){let i=this.frontier[this.depth];i.match=i.match.matchType(e),this.placed=Ot(this.placed,this.depth,k.from(e.create(t,r))),this.frontier.push({type:e,match:e.contentMatch})}closeFrontierNode(){let t=this.frontier.pop().match.fillBefore(k.empty,!0);t.childCount&&(this.placed=Ot(this.placed,this.frontier.length,t))}}function Tt(n,e,t){return e==0?n.cutByIndex(t,n.childCount):n.replaceChild(0,n.firstChild.copy(Tt(n.firstChild.content,e-1,t)))}function Ot(n,e,t){return e==0?n.append(t):n.replaceChild(n.childCount-1,n.lastChild.copy(Ot(n.lastChild.content,e-1,t)))}function Vn(n,e){for(let t=0;t<e;t++)n=n.firstChild.content;return n}function Rs(n,e,t){if(e<=0)return n;let r=n.content;return e>1&&(r=r.replaceChild(0,Rs(r.firstChild,e-1,r.childCount==1?t-1:0))),e>0&&(r=n.type.contentMatch.fillBefore(r).append(r),t<=0&&(r=r.append(n.type.contentMatch.matchFragment(r).fillBefore(k.empty,!0)))),n.copy(r)}function $n(n,e,t,r,i){let s=n.node(e),o=i?n.indexAfter(e):n.index(e);if(o==s.childCount&&!t.compatibleContent(s.type))return null;let l=r.fillBefore(s.content,!0,o);return l&&!Ql(t,s.content,o)?l:null}function Ql(n,e,t){for(let r=t;r<e.childCount;r++)if(!n.allowsMarks(e.child(r).marks))return!0;return!1}function ea(n){return n.spec.defining||n.spec.definingForContent}function ta(n,e,t,r){if(!r.size)return n.deleteRange(e,t);let i=n.doc.resolve(e),s=n.doc.resolve(t);if(Is(i,s,r))return n.step(new J(e,t,r));let o=Bs(i,n.doc.resolve(t));o[o.length-1]==0&&o.pop();let l=-(i.depth+1);o.unshift(l);for(let f=i.depth,h=i.pos-1;f>0;f--,h--){let p=i.node(f).type.spec;if(p.defining||p.definingAsContext||p.isolating)break;o.indexOf(f)>-1?l=f:i.before(f)==h&&o.splice(1,0,-f)}let a=o.indexOf(l),c=[],u=r.openStart;for(let f=r.content,h=0;;h++){let p=f.firstChild;if(c.push(p),h==r.openStart)break;f=p.content}for(let f=u-1;f>=0;f--){let h=c[f],p=ea(h.type);if(p&&!h.sameMarkup(i.node(Math.abs(l)-1)))u=f;else if(p||!h.type.isTextblock)break}for(let f=r.openStart;f>=0;f--){let h=(f+u+1)%(r.openStart+1),p=c[h];if(p)for(let m=0;m<o.length;m++){let g=o[(m+a)%o.length],y=!0;g<0&&(y=!1,g=-g);let b=i.node(g-1),M=i.index(g-1);if(b.canReplaceWith(M,M,p.type,p.marks))return n.replace(i.before(g),y?s.after(g):t,new x(Ps(r.content,0,r.openStart,h),h,r.openEnd))}}let d=n.steps.length;for(let f=o.length-1;f>=0&&(n.replace(e,t,r),!(n.steps.length>d));f--){let h=o[f];h<0||(e=i.before(h),t=s.after(h))}}function Ps(n,e,t,r,i){if(e<t){let s=n.firstChild;n=n.replaceChild(0,s.copy(Ps(s.content,e+1,t,r,s)))}if(e>r){let s=i.contentMatchAt(0),o=s.fillBefore(n).append(n);n=o.append(s.matchFragment(o).fillBefore(k.empty,!0))}return n}function na(n,e,t,r){if(!r.isInline&&e==t&&n.doc.resolve(e).parent.content.size){let i=Xl(n.doc,e,r.type);i!=null&&(e=t=i)}n.replaceRange(e,t,new x(k.from(r),0,0))}function ra(n,e,t){let r=n.doc.resolve(e),i=n.doc.resolve(t),s=Bs(r,i);for(let o=0;o<s.length;o++){let l=s[o],a=o==s.length-1;if(a&&l==0||r.node(l).type.contentMatch.validEnd)return n.delete(r.start(l),i.end(l));if(l>0&&(a||r.node(l-1).canReplace(r.index(l-1),i.indexAfter(l-1))))return n.delete(r.before(l),i.after(l))}for(let o=1;o<=r.depth&&o<=i.depth;o++)if(e-r.start(o)==r.depth-o&&t>r.end(o)&&i.end(o)-t!=i.depth-o)return n.delete(r.before(o),t);n.delete(e,t)}function Bs(n,e){let t=[],r=Math.min(n.depth,e.depth);for(let i=r;i>=0;i--){let s=n.start(i);if(s<n.pos-(n.depth-i)||e.end(i)>e.pos+(e.depth-i)||n.node(i).type.spec.isolating||e.node(i).type.spec.isolating)break;(s==e.start(i)||i==n.depth&&i==e.depth&&n.parent.inlineContent&&e.parent.inlineContent&&i&&e.start(i-1)==s-1)&&t.push(i)}return t}class pt extends Q{constructor(e,t,r){super(),this.pos=e,this.attr=t,this.value=r}apply(e){let t=e.nodeAt(this.pos);if(!t)return $.fail("No node at attribute step's position");let r=Object.create(null);for(let s in t.attrs)r[s]=t.attrs[s];r[this.attr]=this.value;let i=t.type.create(r,null,t.marks);return $.fromReplace(e,this.pos,this.pos+1,new x(k.from(i),0,t.isLeaf?0:1))}getMap(){return se.empty}invert(e){return new pt(this.pos,this.attr,e.nodeAt(this.pos).attrs[this.attr])}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new pt(t.pos,this.attr,this.value)}toJSON(){return{stepType:"attr",pos:this.pos,attr:this.attr,value:this.value}}static fromJSON(e,t){if(typeof t.pos!="number"||typeof t.attr!="string")throw new RangeError("Invalid input for AttrStep.fromJSON");return new pt(t.pos,t.attr,t.value)}}Q.jsonID("attr",pt);class Rt extends Q{constructor(e,t){super(),this.attr=e,this.value=t}apply(e){let t=Object.create(null);for(let i in e.attrs)t[i]=e.attrs[i];t[this.attr]=this.value;let r=e.type.create(t,e.content,e.marks);return $.ok(r)}getMap(){return se.empty}invert(e){return new Rt(this.attr,e.attrs[this.attr])}map(e){return this}toJSON(){return{stepType:"docAttr",attr:this.attr,value:this.value}}static fromJSON(e,t){if(typeof t.attr!="string")throw new RangeError("Invalid input for DocAttrStep.fromJSON");return new Rt(t.attr,t.value)}}Q.jsonID("docAttr",Rt);let kt=class extends Error{};kt=function n(e){let t=Error.call(this,e);return t.__proto__=n.prototype,t};kt.prototype=Object.create(Error.prototype);kt.prototype.constructor=kt;kt.prototype.name="TransformError";class ia{constructor(e){this.doc=e,this.steps=[],this.docs=[],this.mapping=new ft}get before(){return this.docs.length?this.docs[0]:this.doc}step(e){let t=this.maybeStep(e);if(t.failed)throw new kt(t.failed);return this}maybeStep(e){let t=e.apply(this.doc);return t.failed||this.addStep(e,t.doc),t}get docChanged(){return this.steps.length>0}addStep(e,t){this.docs.push(this.doc),this.steps.push(e),this.mapping.appendMap(e.getMap()),this.doc=t}replace(e,t=e,r=x.empty){let i=bn(this.doc,e,t,r);return i&&this.step(i),this}replaceWith(e,t,r){return this.replace(e,t,new x(k.from(r),0,0))}delete(e,t){return this.replace(e,t,x.empty)}insert(e,t){return this.replaceWith(e,e,t)}replaceRange(e,t,r){return ta(this,e,t,r),this}replaceRangeWith(e,t,r){return na(this,e,t,r),this}deleteRange(e,t){return ra(this,e,t),this}lift(e,t){return $l(this,e,t),this}join(e,t=1){return Yl(this,e,t),this}wrap(e,t){return Jl(this,e,t),this}setBlockType(e,t=e,r,i=null){return jl(this,e,t,r,i),this}setNodeMarkup(e,t,r=null,i){return _l(this,e,t,r,i),this}setNodeAttribute(e,t,r){return this.step(new pt(e,t,r)),this}setDocAttribute(e,t){return this.step(new Rt(e,t)),this}addNodeMark(e,t){return this.step(new Pe(e,t)),this}removeNodeMark(e,t){if(!(t instanceof B)){let r=this.doc.nodeAt(e);if(!r)throw new RangeError("No node at position "+e);if(t=t.isInSet(r.marks),!t)return this}return this.step(new yt(e,t)),this}split(e,t=1,r){return Gl(this,e,t,r),this}addMark(e,t,r){return Ll(this,e,t,r),this}removeMark(e,t,r){return Fl(this,e,t,r),this}clearIncompatible(e,t,r){return Es(this,e,t,r),this}}const Hn=Object.create(null);class E{constructor(e,t,r){this.$anchor=e,this.$head=t,this.ranges=r||[new sa(e.min(t),e.max(t))]}get anchor(){return this.$anchor.pos}get head(){return this.$head.pos}get from(){return this.$from.pos}get to(){return this.$to.pos}get $from(){return this.ranges[0].$from}get $to(){return this.ranges[0].$to}get empty(){let e=this.ranges;for(let t=0;t<e.length;t++)if(e[t].$from.pos!=e[t].$to.pos)return!1;return!0}content(){return this.$from.doc.slice(this.from,this.to,!0)}replace(e,t=x.empty){let r=t.content.lastChild,i=null;for(let l=0;l<t.openEnd;l++)i=r,r=r.lastChild;let s=e.steps.length,o=this.ranges;for(let l=0;l<o.length;l++){let{$from:a,$to:c}=o[l],u=e.mapping.slice(s);e.replaceRange(u.map(a.pos),u.map(c.pos),l?x.empty:t),l==0&&ti(e,s,(r?r.isInline:i&&i.isTextblock)?-1:1)}}replaceWith(e,t){let r=e.steps.length,i=this.ranges;for(let s=0;s<i.length;s++){let{$from:o,$to:l}=i[s],a=e.mapping.slice(r),c=a.map(o.pos),u=a.map(l.pos);s?e.deleteRange(c,u):(e.replaceRangeWith(c,u,t),ti(e,r,t.isInline?-1:1))}}static findFrom(e,t,r=!1){let i=e.parent.inlineContent?new O(e):at(e.node(0),e.parent,e.pos,e.index(),t,r);if(i)return i;for(let s=e.depth-1;s>=0;s--){let o=t<0?at(e.node(0),e.node(s),e.before(s+1),e.index(s),t,r):at(e.node(0),e.node(s),e.after(s+1),e.index(s)+1,t,r);if(o)return o}return null}static near(e,t=1){return this.findFrom(e,t)||this.findFrom(e,-t)||new he(e.node(0))}static atStart(e){return at(e,e,0,0,1)||new he(e)}static atEnd(e){return at(e,e,e.content.size,e.childCount,-1)||new he(e)}static fromJSON(e,t){if(!t||!t.type)throw new RangeError("Invalid input for Selection.fromJSON");let r=Hn[t.type];if(!r)throw new RangeError(`No selection type ${t.type} defined`);return r.fromJSON(e,t)}static jsonID(e,t){if(e in Hn)throw new RangeError("Duplicate use of selection JSON ID "+e);return Hn[e]=t,t.prototype.jsonID=e,t}getBookmark(){return O.between(this.$anchor,this.$head).getBookmark()}}E.prototype.visible=!0;class sa{constructor(e,t){this.$from=e,this.$to=t}}let Qr=!1;function ei(n){!Qr&&!n.parent.inlineContent&&(Qr=!0,console.warn("TextSelection endpoint not pointing into a node with inline content ("+n.parent.type.name+")"))}class O extends E{constructor(e,t=e){ei(e),ei(t),super(e,t)}get $cursor(){return this.$anchor.pos==this.$head.pos?this.$head:null}map(e,t){let r=e.resolve(t.map(this.head));if(!r.parent.inlineContent)return E.near(r);let i=e.resolve(t.map(this.anchor));return new O(i.parent.inlineContent?i:r,r)}replace(e,t=x.empty){if(super.replace(e,t),t==x.empty){let r=this.$from.marksAcross(this.$to);r&&e.ensureMarks(r)}}eq(e){return e instanceof O&&e.anchor==this.anchor&&e.head==this.head}getBookmark(){return new xn(this.anchor,this.head)}toJSON(){return{type:"text",anchor:this.anchor,head:this.head}}static fromJSON(e,t){if(typeof t.anchor!="number"||typeof t.head!="number")throw new RangeError("Invalid input for TextSelection.fromJSON");return new O(e.resolve(t.anchor),e.resolve(t.head))}static create(e,t,r=t){let i=e.resolve(t);return new this(i,r==t?i:e.resolve(r))}static between(e,t,r){let i=e.pos-t.pos;if((!r||i)&&(r=i>=0?1:-1),!t.parent.inlineContent){let s=E.findFrom(t,r,!0)||E.findFrom(t,-r,!0);if(s)t=s.$head;else return E.near(t,r)}return e.parent.inlineContent||(i==0?e=t:(e=(E.findFrom(e,-r,!0)||E.findFrom(e,r,!0)).$anchor,e.pos<t.pos!=i<0&&(e=t))),new O(e,t)}}E.jsonID("text",O);class xn{constructor(e,t){this.anchor=e,this.head=t}map(e){return new xn(e.map(this.anchor),e.map(this.head))}resolve(e){return O.between(e.resolve(this.anchor),e.resolve(this.head))}}class w extends E{constructor(e){let t=e.nodeAfter,r=e.node(0).resolve(e.pos+t.nodeSize);super(e,r),this.node=t}map(e,t){let{deleted:r,pos:i}=t.mapResult(this.anchor),s=e.resolve(i);return r?E.near(s):new w(s)}content(){return new x(k.from(this.node),0,0)}eq(e){return e instanceof w&&e.anchor==this.anchor}toJSON(){return{type:"node",anchor:this.anchor}}getBookmark(){return new wr(this.anchor)}static fromJSON(e,t){if(typeof t.anchor!="number")throw new RangeError("Invalid input for NodeSelection.fromJSON");return new w(e.resolve(t.anchor))}static create(e,t){return new w(e.resolve(t))}static isSelectable(e){return!e.isText&&e.type.spec.selectable!==!1}}w.prototype.visible=!1;E.jsonID("node",w);class wr{constructor(e){this.anchor=e}map(e){let{deleted:t,pos:r}=e.mapResult(this.anchor);return t?new xn(r,r):new wr(r)}resolve(e){let t=e.resolve(this.anchor),r=t.nodeAfter;return r&&w.isSelectable(r)?new w(t):E.near(t)}}class he extends E{constructor(e){super(e.resolve(0),e.resolve(e.content.size))}replace(e,t=x.empty){if(t==x.empty){e.delete(0,e.doc.content.size);let r=E.atStart(e.doc);r.eq(e.selection)||e.setSelection(r)}else super.replace(e,t)}toJSON(){return{type:"all"}}static fromJSON(e){return new he(e)}map(e){return new he(e)}eq(e){return e instanceof he}getBookmark(){return oa}}E.jsonID("all",he);const oa={map(){return this},resolve(n){return new he(n)}};function at(n,e,t,r,i,s=!1){if(e.inlineContent)return O.create(n,t);for(let o=r-(i>0?0:1);i>0?o<e.childCount:o>=0;o+=i){let l=e.child(o);if(l.isAtom){if(!s&&w.isSelectable(l))return w.create(n,t-(i<0?l.nodeSize:0))}else{let a=at(n,l,t+i,i<0?l.childCount:0,i,s);if(a)return a}t+=l.nodeSize*i}return null}function ti(n,e,t){let r=n.steps.length-1;if(r<e)return;let i=n.steps[r];if(!(i instanceof J||i instanceof j))return;let s=n.mapping.maps[r],o;s.forEach((l,a,c,u)=>{o==null&&(o=u)}),n.setSelection(E.near(n.doc.resolve(o),t))}const ni=1,Ut=2,ri=4;class la extends ia{constructor(e){super(e.doc),this.curSelectionFor=0,this.updated=0,this.meta=Object.create(null),this.time=Date.now(),this.curSelection=e.selection,this.storedMarks=e.storedMarks}get selection(){return this.curSelectionFor<this.steps.length&&(this.curSelection=this.curSelection.map(this.doc,this.mapping.slice(this.curSelectionFor)),this.curSelectionFor=this.steps.length),this.curSelection}setSelection(e){if(e.$from.doc!=this.doc)throw new RangeError("Selection passed to setSelection must point at the current document");return this.curSelection=e,this.curSelectionFor=this.steps.length,this.updated=(this.updated|ni)&~Ut,this.storedMarks=null,this}get selectionSet(){return(this.updated&ni)>0}setStoredMarks(e){return this.storedMarks=e,this.updated|=Ut,this}ensureMarks(e){return B.sameSet(this.storedMarks||this.selection.$from.marks(),e)||this.setStoredMarks(e),this}addStoredMark(e){return this.ensureMarks(e.addToSet(this.storedMarks||this.selection.$head.marks()))}removeStoredMark(e){return this.ensureMarks(e.removeFromSet(this.storedMarks||this.selection.$head.marks()))}get storedMarksSet(){return(this.updated&Ut)>0}addStep(e,t){super.addStep(e,t),this.updated=this.updated&~Ut,this.storedMarks=null}setTime(e){return this.time=e,this}replaceSelection(e){return this.selection.replace(this,e),this}replaceSelectionWith(e,t=!0){let r=this.selection;return t&&(e=e.mark(this.storedMarks||(r.empty?r.$from.marks():r.$from.marksAcross(r.$to)||B.none))),r.replaceWith(this,e),this}deleteSelection(){return this.selection.replace(this),this}insertText(e,t,r){let i=this.doc.type.schema;if(t==null)return e?this.replaceSelectionWith(i.text(e),!0):this.deleteSelection();{if(r==null&&(r=t),r=r??t,!e)return this.deleteRange(t,r);let s=this.storedMarks;if(!s){let o=this.doc.resolve(t);s=r==t?o.marks():o.marksAcross(this.doc.resolve(r))}return this.replaceRangeWith(t,r,i.text(e,s)),this.selection.empty||this.setSelection(E.near(this.selection.$to)),this}}setMeta(e,t){return this.meta[typeof e=="string"?e:e.key]=t,this}getMeta(e){return this.meta[typeof e=="string"?e:e.key]}get isGeneric(){for(let e in this.meta)return!1;return!0}scrollIntoView(){return this.updated|=ri,this}get scrolledIntoView(){return(this.updated&ri)>0}}function ii(n,e){return!e||!n?n:n.bind(e)}class vt{constructor(e,t,r){this.name=e,this.init=ii(t.init,r),this.apply=ii(t.apply,r)}}const aa=[new vt("doc",{init(n){return n.doc||n.schema.topNodeType.createAndFill()},apply(n){return n.doc}}),new vt("selection",{init(n,e){return n.selection||E.atStart(e.doc)},apply(n){return n.selection}}),new vt("storedMarks",{init(n){return n.storedMarks||null},apply(n,e,t,r){return r.selection.$cursor?n.storedMarks:null}}),new vt("scrollToSelection",{init(){return 0},apply(n,e){return n.scrolledIntoView?e+1:e}})];class Wn{constructor(e,t){this.schema=e,this.plugins=[],this.pluginsByKey=Object.create(null),this.fields=aa.slice(),t&&t.forEach(r=>{if(this.pluginsByKey[r.key])throw new RangeError("Adding different instances of a keyed plugin ("+r.key+")");this.plugins.push(r),this.pluginsByKey[r.key]=r,r.spec.state&&this.fields.push(new vt(r.key,r.spec.state,r))})}}class ut{constructor(e){this.config=e}get schema(){return this.config.schema}get plugins(){return this.config.plugins}apply(e){return this.applyTransaction(e).state}filterTransaction(e,t=-1){for(let r=0;r<this.config.plugins.length;r++)if(r!=t){let i=this.config.plugins[r];if(i.spec.filterTransaction&&!i.spec.filterTransaction.call(i,e,this))return!1}return!0}applyTransaction(e){if(!this.filterTransaction(e))return{state:this,transactions:[]};let t=[e],r=this.applyInner(e),i=null;for(;;){let s=!1;for(let o=0;o<this.config.plugins.length;o++){let l=this.config.plugins[o];if(l.spec.appendTransaction){let a=i?i[o].n:0,c=i?i[o].state:this,u=a<t.length&&l.spec.appendTransaction.call(l,a?t.slice(a):t,c,r);if(u&&r.filterTransaction(u,o)){if(u.setMeta("appendedTransaction",e),!i){i=[];for(let d=0;d<this.config.plugins.length;d++)i.push(d<o?{state:r,n:t.length}:{state:this,n:0})}t.push(u),r=r.applyInner(u),s=!0}i&&(i[o]={state:r,n:t.length})}}if(!s)return{state:r,transactions:t}}}applyInner(e){if(!e.before.eq(this.doc))throw new RangeError("Applying a mismatched transaction");let t=new ut(this.config),r=this.config.fields;for(let i=0;i<r.length;i++){let s=r[i];t[s.name]=s.apply(e,this[s.name],this,t)}return t}get tr(){return new la(this)}static create(e){let t=new Wn(e.doc?e.doc.type.schema:e.schema,e.plugins),r=new ut(t);for(let i=0;i<t.fields.length;i++)r[t.fields[i].name]=t.fields[i].init(e,r);return r}reconfigure(e){let t=new Wn(this.schema,e.plugins),r=t.fields,i=new ut(t);for(let s=0;s<r.length;s++){let o=r[s].name;i[o]=this.hasOwnProperty(o)?this[o]:r[s].init(e,i)}return i}toJSON(e){let t={doc:this.doc.toJSON(),selection:this.selection.toJSON()};if(this.storedMarks&&(t.storedMarks=this.storedMarks.map(r=>r.toJSON())),e&&typeof e=="object")for(let r in e){if(r=="doc"||r=="selection")throw new RangeError("The JSON fields `doc` and `selection` are reserved");let i=e[r],s=i.spec.state;s&&s.toJSON&&(t[r]=s.toJSON.call(i,this[i.key]))}return t}static fromJSON(e,t,r){if(!t)throw new RangeError("Invalid input for EditorState.fromJSON");if(!e.schema)throw new RangeError("Required config field 'schema' missing");let i=new Wn(e.schema,e.plugins),s=new ut(i);return i.fields.forEach(o=>{if(o.name=="doc")s.doc=Qe.fromJSON(e.schema,t.doc);else if(o.name=="selection")s.selection=E.fromJSON(s.doc,t.selection);else if(o.name=="storedMarks")t.storedMarks&&(s.storedMarks=t.storedMarks.map(e.schema.markFromJSON));else{if(r)for(let l in r){let a=r[l],c=a.spec.state;if(a.key==o.name&&c&&c.fromJSON&&Object.prototype.hasOwnProperty.call(t,l)){s[o.name]=c.fromJSON.call(a,e,t[l],s);return}}s[o.name]=o.init(e,s)}}),s}}function zs(n,e,t){for(let r in n){let i=n[r];i instanceof Function?i=i.bind(e):r=="handleDOMEvents"&&(i=zs(i,e,{})),t[r]=i}return t}class le{constructor(e){this.spec=e,this.props={},e.props&&zs(e.props,this,this.props),this.key=e.key?e.key.key:Ls("plugin")}getState(e){return e[this.key]}}const Jn=Object.create(null);function Ls(n){return n in Jn?n+"$"+ ++Jn[n]:(Jn[n]=0,n+"$")}class Oe{constructor(e="key"){this.key=Ls(e)}get(e){return e.config.pluginsByKey[this.key]}getState(e){return e[this.key]}}const G=function(n){for(var e=0;;e++)if(n=n.previousSibling,!n)return e},Pt=function(n){let e=n.assignedSlot||n.parentNode;return e&&e.nodeType==11?e.host:e};let lr=null;const Me=function(n,e,t){let r=lr||(lr=document.createRange());return r.setEnd(n,t??n.nodeValue.length),r.setStart(n,e||0),r},ca=function(){lr=null},it=function(n,e,t,r){return t&&(si(n,e,t,r,-1)||si(n,e,t,r,1))},ua=/^(img|br|input|textarea|hr)$/i;function si(n,e,t,r,i){for(;;){if(n==t&&e==r)return!0;if(e==(i<0?0:ge(n))){let s=n.parentNode;if(!s||s.nodeType!=1||$t(n)||ua.test(n.nodeName)||n.contentEditable=="false")return!1;e=G(n)+(i<0?0:1),n=s}else if(n.nodeType==1){if(n=n.childNodes[e+(i<0?-1:0)],n.contentEditable=="false")return!1;e=i<0?ge(n):0}else return!1}}function ge(n){return n.nodeType==3?n.nodeValue.length:n.childNodes.length}function da(n,e){for(;;){if(n.nodeType==3&&e)return n;if(n.nodeType==1&&e>0){if(n.contentEditable=="false")return null;n=n.childNodes[e-1],e=ge(n)}else if(n.parentNode&&!$t(n))e=G(n),n=n.parentNode;else return null}}function fa(n,e){for(;;){if(n.nodeType==3&&e<n.nodeValue.length)return n;if(n.nodeType==1&&e<n.childNodes.length){if(n.contentEditable=="false")return null;n=n.childNodes[e],e=0}else if(n.parentNode&&!$t(n))e=G(n)+1,n=n.parentNode;else return null}}function ha(n,e,t){for(let r=e==0,i=e==ge(n);r||i;){if(n==t)return!0;let s=G(n);if(n=n.parentNode,!n)return!1;r=r&&s==0,i=i&&s==ge(n)}}function $t(n){let e;for(let t=n;t&&!(e=t.pmViewDesc);t=t.parentNode);return e&&e.node&&e.node.isBlock&&(e.dom==n||e.contentDOM==n)}const Sn=function(n){return n.focusNode&&it(n.focusNode,n.focusOffset,n.anchorNode,n.anchorOffset)};function Ke(n,e){let t=document.createEvent("Event");return t.initEvent("keydown",!0,!0),t.keyCode=n,t.key=t.code=e,t}function pa(n){let e=n.activeElement;for(;e&&e.shadowRoot;)e=e.shadowRoot.activeElement;return e}function ma(n,e,t){if(n.caretPositionFromPoint)try{let r=n.caretPositionFromPoint(e,t);if(r)return{node:r.offsetNode,offset:r.offset}}catch{}if(n.caretRangeFromPoint){let r=n.caretRangeFromPoint(e,t);if(r)return{node:r.startContainer,offset:r.startOffset}}}const be=typeof navigator<"u"?navigator:null,oi=typeof document<"u"?document:null,He=be&&be.userAgent||"",ar=/Edge\/(\d+)/.exec(He),Fs=/MSIE \d/.exec(He),cr=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(He),ie=!!(Fs||cr||ar),ze=Fs?document.documentMode:cr?+cr[1]:ar?+ar[1]:0,pe=!ie&&/gecko\/(\d+)/i.test(He);pe&&+(/Firefox\/(\d+)/.exec(He)||[0,0])[1];const ur=!ie&&/Chrome\/(\d+)/.exec(He),ee=!!ur,ga=ur?+ur[1]:0,te=!ie&&!!be&&/Apple Computer/.test(be.vendor),bt=te&&(/Mobile\/\w+/.test(He)||!!be&&be.maxTouchPoints>2),ae=bt||(be?/Mac/.test(be.platform):!1),ya=be?/Win/.test(be.platform):!1,de=/Android \d/.test(He),Ht=!!oi&&"webkitFontSmoothing"in oi.documentElement.style,ka=Ht?+(/\bAppleWebKit\/(\d+)/.exec(navigator.userAgent)||[0,0])[1]:0;function ba(n){let e=n.defaultView&&n.defaultView.visualViewport;return e?{left:0,right:e.width,top:0,bottom:e.height}:{left:0,right:n.documentElement.clientWidth,top:0,bottom:n.documentElement.clientHeight}}function Se(n,e){return typeof n=="number"?n:n[e]}function xa(n){let e=n.getBoundingClientRect(),t=e.width/n.offsetWidth||1,r=e.height/n.offsetHeight||1;return{left:e.left,right:e.left+n.clientWidth*t,top:e.top,bottom:e.top+n.clientHeight*r}}function li(n,e,t){let r=n.someProp("scrollThreshold")||0,i=n.someProp("scrollMargin")||5,s=n.dom.ownerDocument;for(let o=t||n.dom;o;o=Pt(o)){if(o.nodeType!=1)continue;let l=o,a=l==s.body,c=a?ba(s):xa(l),u=0,d=0;if(e.top<c.top+Se(r,"top")?d=-(c.top-e.top+Se(i,"top")):e.bottom>c.bottom-Se(r,"bottom")&&(d=e.bottom-e.top>c.bottom-c.top?e.top+Se(i,"top")-c.top:e.bottom-c.bottom+Se(i,"bottom")),e.left<c.left+Se(r,"left")?u=-(c.left-e.left+Se(i,"left")):e.right>c.right-Se(r,"right")&&(u=e.right-c.right+Se(i,"right")),u||d)if(a)s.defaultView.scrollBy(u,d);else{let f=l.scrollLeft,h=l.scrollTop;d&&(l.scrollTop+=d),u&&(l.scrollLeft+=u);let p=l.scrollLeft-f,m=l.scrollTop-h;e={left:e.left-p,top:e.top-m,right:e.right-p,bottom:e.bottom-m}}if(a||/^(fixed|sticky)$/.test(getComputedStyle(o).position))break}}function Sa(n){let e=n.dom.getBoundingClientRect(),t=Math.max(0,e.top),r,i;for(let s=(e.left+e.right)/2,o=t+1;o<Math.min(innerHeight,e.bottom);o+=5){let l=n.root.elementFromPoint(s,o);if(!l||l==n.dom||!n.dom.contains(l))continue;let a=l.getBoundingClientRect();if(a.top>=t-20){r=l,i=a.top;break}}return{refDOM:r,refTop:i,stack:Vs(n.dom)}}function Vs(n){let e=[],t=n.ownerDocument;for(let r=n;r&&(e.push({dom:r,top:r.scrollTop,left:r.scrollLeft}),n!=t);r=Pt(r));return e}function Ma({refDOM:n,refTop:e,stack:t}){let r=n?n.getBoundingClientRect().top:0;$s(t,r==0?0:r-e)}function $s(n,e){for(let t=0;t<n.length;t++){let{dom:r,top:i,left:s}=n[t];r.scrollTop!=i+e&&(r.scrollTop=i+e),r.scrollLeft!=s&&(r.scrollLeft=s)}}let ot=null;function Ca(n){if(n.setActive)return n.setActive();if(ot)return n.focus(ot);let e=Vs(n);n.focus(ot==null?{get preventScroll(){return ot={preventScroll:!0},!0}}:void 0),ot||(ot=!1,$s(e,0))}function Hs(n,e){let t,r=2e8,i,s=0,o=e.top,l=e.top,a,c;for(let u=n.firstChild,d=0;u;u=u.nextSibling,d++){let f;if(u.nodeType==1)f=u.getClientRects();else if(u.nodeType==3)f=Me(u).getClientRects();else continue;for(let h=0;h<f.length;h++){let p=f[h];if(p.top<=o&&p.bottom>=l){o=Math.max(p.bottom,o),l=Math.min(p.top,l);let m=p.left>e.left?p.left-e.left:p.right<e.left?e.left-p.right:0;if(m<r){t=u,r=m,i=m&&t.nodeType==3?{left:p.right<e.left?p.right:p.left,top:e.top}:e,u.nodeType==1&&m&&(s=d+(e.left>=(p.left+p.right)/2?1:0));continue}}else p.top>e.top&&!a&&p.left<=e.left&&p.right>=e.left&&(a=u,c={left:Math.max(p.left,Math.min(p.right,e.left)),top:p.top});!t&&(e.left>=p.right&&e.top>=p.top||e.left>=p.left&&e.top>=p.bottom)&&(s=d+1)}}return!t&&a&&(t=a,i=c,r=0),t&&t.nodeType==3?wa(t,i):!t||r&&t.nodeType==1?{node:n,offset:s}:Hs(t,i)}function wa(n,e){let t=n.nodeValue.length,r=document.createRange();for(let i=0;i<t;i++){r.setEnd(n,i+1),r.setStart(n,i);let s=ve(r,1);if(s.top!=s.bottom&&Tr(e,s))return{node:n,offset:i+(e.left>=(s.left+s.right)/2?1:0)}}return{node:n,offset:0}}function Tr(n,e){return n.left>=e.left-1&&n.left<=e.right+1&&n.top>=e.top-1&&n.top<=e.bottom+1}function Ta(n,e){let t=n.parentNode;return t&&/^li$/i.test(t.nodeName)&&e.left<n.getBoundingClientRect().left?t:n}function Oa(n,e,t){let{node:r,offset:i}=Hs(e,t),s=-1;if(r.nodeType==1&&!r.firstChild){let o=r.getBoundingClientRect();s=o.left!=o.right&&t.left>(o.left+o.right)/2?1:-1}return n.docView.posFromDOM(r,i,s)}function va(n,e,t,r){let i=-1;for(let s=e,o=!1;s!=n.dom;){let l=n.docView.nearestDesc(s,!0);if(!l)return null;if(l.dom.nodeType==1&&(l.node.isBlock&&l.parent||!l.contentDOM)){let a=l.dom.getBoundingClientRect();if(l.node.isBlock&&l.parent&&(!o&&a.left>r.left||a.top>r.top?i=l.posBefore:(!o&&a.right<r.left||a.bottom<r.top)&&(i=l.posAfter),o=!0),!l.contentDOM&&i<0&&!l.node.isText)return(l.node.isBlock?r.top<(a.top+a.bottom)/2:r.left<(a.left+a.right)/2)?l.posBefore:l.posAfter}s=l.dom.parentNode}return i>-1?i:n.docView.posFromDOM(e,t,-1)}function Ws(n,e,t){let r=n.childNodes.length;if(r&&t.top<t.bottom)for(let i=Math.max(0,Math.min(r-1,Math.floor(r*(e.top-t.top)/(t.bottom-t.top))-2)),s=i;;){let o=n.childNodes[s];if(o.nodeType==1){let l=o.getClientRects();for(let a=0;a<l.length;a++){let c=l[a];if(Tr(e,c))return Ws(o,e,c)}}if((s=(s+1)%r)==i)break}return n}function Na(n,e){let t=n.dom.ownerDocument,r,i=0,s=ma(t,e.left,e.top);s&&({node:r,offset:i}=s);let o=(n.root.elementFromPoint?n.root:t).elementFromPoint(e.left,e.top),l;if(!o||!n.dom.contains(o.nodeType!=1?o.parentNode:o)){let c=n.dom.getBoundingClientRect();if(!Tr(e,c)||(o=Ws(n.dom,e,c),!o))return null}if(te)for(let c=o;r&&c;c=Pt(c))c.draggable&&(r=void 0);if(o=Ta(o,e),r){if(pe&&r.nodeType==1&&(i=Math.min(i,r.childNodes.length),i<r.childNodes.length)){let u=r.childNodes[i],d;u.nodeName=="IMG"&&(d=u.getBoundingClientRect()).right<=e.left&&d.bottom>e.top&&i++}let c;Ht&&i&&r.nodeType==1&&(c=r.childNodes[i-1]).nodeType==1&&c.contentEditable=="false"&&c.getBoundingClientRect().top>=e.top&&i--,r==n.dom&&i==r.childNodes.length-1&&r.lastChild.nodeType==1&&e.top>r.lastChild.getBoundingClientRect().bottom?l=n.state.doc.content.size:(i==0||r.nodeType!=1||r.childNodes[i-1].nodeName!="BR")&&(l=va(n,r,i,e))}l==null&&(l=Oa(n,o,e));let a=n.docView.nearestDesc(o,!0);return{pos:l,inside:a?a.posAtStart-a.border:-1}}function ai(n){return n.top<n.bottom||n.left<n.right}function ve(n,e){let t=n.getClientRects();if(t.length){let r=t[e<0?0:t.length-1];if(ai(r))return r}return Array.prototype.find.call(t,ai)||n.getBoundingClientRect()}const Ea=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac]/;function Js(n,e,t){let{node:r,offset:i,atom:s}=n.docView.domFromPos(e,t<0?-1:1),o=Ht||pe;if(r.nodeType==3)if(o&&(Ea.test(r.nodeValue)||(t<0?!i:i==r.nodeValue.length))){let a=ve(Me(r,i,i),t);if(pe&&i&&/\s/.test(r.nodeValue[i-1])&&i<r.nodeValue.length){let c=ve(Me(r,i-1,i-1),-1);if(c.top==a.top){let u=ve(Me(r,i,i+1),-1);if(u.top!=a.top)return wt(u,u.left<c.left)}}return a}else{let a=i,c=i,u=t<0?1:-1;return t<0&&!i?(c++,u=-1):t>=0&&i==r.nodeValue.length?(a--,u=1):t<0?a--:c++,wt(ve(Me(r,a,c),u),u<0)}if(!n.state.doc.resolve(e-(s||0)).parent.inlineContent){if(s==null&&i&&(t<0||i==ge(r))){let a=r.childNodes[i-1];if(a.nodeType==1)return jn(a.getBoundingClientRect(),!1)}if(s==null&&i<ge(r)){let a=r.childNodes[i];if(a.nodeType==1)return jn(a.getBoundingClientRect(),!0)}return jn(r.getBoundingClientRect(),t>=0)}if(s==null&&i&&(t<0||i==ge(r))){let a=r.childNodes[i-1],c=a.nodeType==3?Me(a,ge(a)-(o?0:1)):a.nodeType==1&&(a.nodeName!="BR"||!a.nextSibling)?a:null;if(c)return wt(ve(c,1),!1)}if(s==null&&i<ge(r)){let a=r.childNodes[i];for(;a.pmViewDesc&&a.pmViewDesc.ignoreForCoords;)a=a.nextSibling;let c=a?a.nodeType==3?Me(a,0,o?0:1):a.nodeType==1?a:null:null;if(c)return wt(ve(c,-1),!0)}return wt(ve(r.nodeType==3?Me(r):r,-t),t>=0)}function wt(n,e){if(n.width==0)return n;let t=e?n.left:n.right;return{top:n.top,bottom:n.bottom,left:t,right:t}}function jn(n,e){if(n.height==0)return n;let t=e?n.top:n.bottom;return{top:t,bottom:t,left:n.left,right:n.right}}function js(n,e,t){let r=n.state,i=n.root.activeElement;r!=e&&n.updateState(e),i!=n.dom&&n.focus();try{return t()}finally{r!=e&&n.updateState(r),i!=n.dom&&i&&i.focus()}}function Aa(n,e,t){let r=e.selection,i=t=="up"?r.$from:r.$to;return js(n,e,()=>{let{node:s}=n.docView.domFromPos(i.pos,t=="up"?-1:1);for(;;){let l=n.docView.nearestDesc(s,!0);if(!l)break;if(l.node.isBlock){s=l.contentDOM||l.dom;break}s=l.dom.parentNode}let o=Js(n,i.pos,1);for(let l=s.firstChild;l;l=l.nextSibling){let a;if(l.nodeType==1)a=l.getClientRects();else if(l.nodeType==3)a=Me(l,0,l.nodeValue.length).getClientRects();else continue;for(let c=0;c<a.length;c++){let u=a[c];if(u.bottom>u.top+1&&(t=="up"?o.top-u.top>(u.bottom-o.top)*2:u.bottom-o.bottom>(o.bottom-u.top)*2))return!1}}return!0})}const Da=/[\u0590-\u08ac]/;function Ia(n,e,t){let{$head:r}=e.selection;if(!r.parent.isTextblock)return!1;let i=r.parentOffset,s=!i,o=i==r.parent.content.size,l=n.domSelection();return!Da.test(r.parent.textContent)||!l.modify?t=="left"||t=="backward"?s:o:js(n,e,()=>{let{focusNode:a,focusOffset:c,anchorNode:u,anchorOffset:d}=n.domSelectionRange(),f=l.caretBidiLevel;l.modify("move",t,"character");let h=r.depth?n.docView.domAfterPos(r.before()):n.dom,{focusNode:p,focusOffset:m}=n.domSelectionRange(),g=p&&!h.contains(p.nodeType==1?p:p.parentNode)||a==p&&c==m;try{l.collapse(u,d),a&&(a!=u||c!=d)&&l.extend&&l.extend(a,c)}catch{}return f!=null&&(l.caretBidiLevel=f),g})}let ci=null,ui=null,di=!1;function Ra(n,e,t){return ci==e&&ui==t?di:(ci=e,ui=t,di=t=="up"||t=="down"?Aa(n,e,t):Ia(n,e,t))}const ce=0,fi=1,_e=2,xe=3;class Wt{constructor(e,t,r,i){this.parent=e,this.children=t,this.dom=r,this.contentDOM=i,this.dirty=ce,r.pmViewDesc=this}matchesWidget(e){return!1}matchesMark(e){return!1}matchesNode(e,t,r){return!1}matchesHack(e){return!1}parseRule(){return null}stopEvent(e){return!1}get size(){let e=0;for(let t=0;t<this.children.length;t++)e+=this.children[t].size;return e}get border(){return 0}destroy(){this.parent=void 0,this.dom.pmViewDesc==this&&(this.dom.pmViewDesc=void 0);for(let e=0;e<this.children.length;e++)this.children[e].destroy()}posBeforeChild(e){for(let t=0,r=this.posAtStart;;t++){let i=this.children[t];if(i==e)return r;r+=i.size}}get posBefore(){return this.parent.posBeforeChild(this)}get posAtStart(){return this.parent?this.parent.posBeforeChild(this)+this.border:0}get posAfter(){return this.posBefore+this.size}get posAtEnd(){return this.posAtStart+this.size-2*this.border}localPosFromDOM(e,t,r){if(this.contentDOM&&this.contentDOM.contains(e.nodeType==1?e:e.parentNode))if(r<0){let s,o;if(e==this.contentDOM)s=e.childNodes[t-1];else{for(;e.parentNode!=this.contentDOM;)e=e.parentNode;s=e.previousSibling}for(;s&&!((o=s.pmViewDesc)&&o.parent==this);)s=s.previousSibling;return s?this.posBeforeChild(o)+o.size:this.posAtStart}else{let s,o;if(e==this.contentDOM)s=e.childNodes[t];else{for(;e.parentNode!=this.contentDOM;)e=e.parentNode;s=e.nextSibling}for(;s&&!((o=s.pmViewDesc)&&o.parent==this);)s=s.nextSibling;return s?this.posBeforeChild(o):this.posAtEnd}let i;if(e==this.dom&&this.contentDOM)i=t>G(this.contentDOM);else if(this.contentDOM&&this.contentDOM!=this.dom&&this.dom.contains(this.contentDOM))i=e.compareDocumentPosition(this.contentDOM)&2;else if(this.dom.firstChild){if(t==0)for(let s=e;;s=s.parentNode){if(s==this.dom){i=!1;break}if(s.previousSibling)break}if(i==null&&t==e.childNodes.length)for(let s=e;;s=s.parentNode){if(s==this.dom){i=!0;break}if(s.nextSibling)break}}return i??r>0?this.posAtEnd:this.posAtStart}nearestDesc(e,t=!1){for(let r=!0,i=e;i;i=i.parentNode){let s=this.getDesc(i),o;if(s&&(!t||s.node))if(r&&(o=s.nodeDOM)&&!(o.nodeType==1?o.contains(e.nodeType==1?e:e.parentNode):o==e))r=!1;else return s}}getDesc(e){let t=e.pmViewDesc;for(let r=t;r;r=r.parent)if(r==this)return t}posFromDOM(e,t,r){for(let i=e;i;i=i.parentNode){let s=this.getDesc(i);if(s)return s.localPosFromDOM(e,t,r)}return-1}descAt(e){for(let t=0,r=0;t<this.children.length;t++){let i=this.children[t],s=r+i.size;if(r==e&&s!=r){for(;!i.border&&i.children.length;)i=i.children[0];return i}if(e<s)return i.descAt(e-r-i.border);r=s}}domFromPos(e,t){if(!this.contentDOM)return{node:this.dom,offset:0,atom:e+1};let r=0,i=0;for(let s=0;r<this.children.length;r++){let o=this.children[r],l=s+o.size;if(l>e||o instanceof qs){i=e-s;break}s=l}if(i)return this.children[r].domFromPos(i-this.children[r].border,t);for(let s;r&&!(s=this.children[r-1]).size&&s instanceof Ks&&s.side>=0;r--);if(t<=0){let s,o=!0;for(;s=r?this.children[r-1]:null,!(!s||s.dom.parentNode==this.contentDOM);r--,o=!1);return s&&t&&o&&!s.border&&!s.domAtom?s.domFromPos(s.size,t):{node:this.contentDOM,offset:s?G(s.dom)+1:0}}else{let s,o=!0;for(;s=r<this.children.length?this.children[r]:null,!(!s||s.dom.parentNode==this.contentDOM);r++,o=!1);return s&&o&&!s.border&&!s.domAtom?s.domFromPos(0,t):{node:this.contentDOM,offset:s?G(s.dom):this.contentDOM.childNodes.length}}}parseRange(e,t,r=0){if(this.children.length==0)return{node:this.contentDOM,from:e,to:t,fromOffset:0,toOffset:this.contentDOM.childNodes.length};let i=-1,s=-1;for(let o=r,l=0;;l++){let a=this.children[l],c=o+a.size;if(i==-1&&e<=c){let u=o+a.border;if(e>=u&&t<=c-a.border&&a.node&&a.contentDOM&&this.contentDOM.contains(a.contentDOM))return a.parseRange(e,t,u);e=o;for(let d=l;d>0;d--){let f=this.children[d-1];if(f.size&&f.dom.parentNode==this.contentDOM&&!f.emptyChildAt(1)){i=G(f.dom)+1;break}e-=f.size}i==-1&&(i=0)}if(i>-1&&(c>t||l==this.children.length-1)){t=c;for(let u=l+1;u<this.children.length;u++){let d=this.children[u];if(d.size&&d.dom.parentNode==this.contentDOM&&!d.emptyChildAt(-1)){s=G(d.dom);break}t+=d.size}s==-1&&(s=this.contentDOM.childNodes.length);break}o=c}return{node:this.contentDOM,from:e,to:t,fromOffset:i,toOffset:s}}emptyChildAt(e){if(this.border||!this.contentDOM||!this.children.length)return!1;let t=this.children[e<0?0:this.children.length-1];return t.size==0||t.emptyChildAt(e)}domAfterPos(e){let{node:t,offset:r}=this.domFromPos(e,0);if(t.nodeType!=1||r==t.childNodes.length)throw new RangeError("No node after pos "+e);return t.childNodes[r]}setSelection(e,t,r,i=!1){let s=Math.min(e,t),o=Math.max(e,t);for(let f=0,h=0;f<this.children.length;f++){let p=this.children[f],m=h+p.size;if(s>h&&o<m)return p.setSelection(e-h-p.border,t-h-p.border,r,i);h=m}let l=this.domFromPos(e,e?-1:1),a=t==e?l:this.domFromPos(t,t?-1:1),c=r.getSelection(),u=!1;if((pe||te)&&e==t){let{node:f,offset:h}=l;if(f.nodeType==3){if(u=!!(h&&f.nodeValue[h-1]==`
`),u&&h==f.nodeValue.length)for(let p=f,m;p;p=p.parentNode){if(m=p.nextSibling){m.nodeName=="BR"&&(l=a={node:m.parentNode,offset:G(m)+1});break}let g=p.pmViewDesc;if(g&&g.node&&g.node.isBlock)break}}else{let p=f.childNodes[h-1];u=p&&(p.nodeName=="BR"||p.contentEditable=="false")}}if(pe&&c.focusNode&&c.focusNode!=a.node&&c.focusNode.nodeType==1){let f=c.focusNode.childNodes[c.focusOffset];f&&f.contentEditable=="false"&&(i=!0)}if(!(i||u&&te)&&it(l.node,l.offset,c.anchorNode,c.anchorOffset)&&it(a.node,a.offset,c.focusNode,c.focusOffset))return;let d=!1;if((c.extend||e==t)&&!u){c.collapse(l.node,l.offset);try{e!=t&&c.extend(a.node,a.offset),d=!0}catch{}}if(!d){if(e>t){let h=l;l=a,a=h}let f=document.createRange();f.setEnd(a.node,a.offset),f.setStart(l.node,l.offset),c.removeAllRanges(),c.addRange(f)}}ignoreMutation(e){return!this.contentDOM&&e.type!="selection"}get contentLost(){return this.contentDOM&&this.contentDOM!=this.dom&&!this.dom.contains(this.contentDOM)}markDirty(e,t){for(let r=0,i=0;i<this.children.length;i++){let s=this.children[i],o=r+s.size;if(r==o?e<=o&&t>=r:e<o&&t>r){let l=r+s.border,a=o-s.border;if(e>=l&&t<=a){this.dirty=e==r||t==o?_e:fi,e==l&&t==a&&(s.contentLost||s.dom.parentNode!=this.contentDOM)?s.dirty=xe:s.markDirty(e-l,t-l);return}else s.dirty=s.dom==s.contentDOM&&s.dom.parentNode==this.contentDOM&&!s.children.length?_e:xe}r=o}this.dirty=_e}markParentsDirty(){let e=1;for(let t=this.parent;t;t=t.parent,e++){let r=e==1?_e:fi;t.dirty<r&&(t.dirty=r)}}get domAtom(){return!1}get ignoreForCoords(){return!1}isText(e){return!1}}class Ks extends Wt{constructor(e,t,r,i){let s,o=t.type.toDOM;if(typeof o=="function"&&(o=o(r,()=>{if(!s)return i;if(s.parent)return s.parent.posBeforeChild(s)})),!t.type.spec.raw){if(o.nodeType!=1){let l=document.createElement("span");l.appendChild(o),o=l}o.contentEditable="false",o.classList.add("ProseMirror-widget")}super(e,[],o,null),this.widget=t,this.widget=t,s=this}matchesWidget(e){return this.dirty==ce&&e.type.eq(this.widget.type)}parseRule(){return{ignore:!0}}stopEvent(e){let t=this.widget.spec.stopEvent;return t?t(e):!1}ignoreMutation(e){return e.type!="selection"||this.widget.spec.ignoreSelection}destroy(){this.widget.type.destroy(this.dom),super.destroy()}get domAtom(){return!0}get side(){return this.widget.type.side}}class Pa extends Wt{constructor(e,t,r,i){super(e,[],t,null),this.textDOM=r,this.text=i}get size(){return this.text.length}localPosFromDOM(e,t){return e!=this.textDOM?this.posAtStart+(t?this.size:0):this.posAtStart+t}domFromPos(e){return{node:this.textDOM,offset:e}}ignoreMutation(e){return e.type==="characterData"&&e.target.nodeValue==e.oldValue}}class st extends Wt{constructor(e,t,r,i){super(e,[],r,i),this.mark=t}static create(e,t,r,i){let s=i.nodeViews[t.type.name],o=s&&s(t,i,r);return(!o||!o.dom)&&(o=ye.renderSpec(document,t.type.spec.toDOM(t,r))),new st(e,t,o.dom,o.contentDOM||o.dom)}parseRule(){return this.dirty&xe||this.mark.type.spec.reparseInView?null:{mark:this.mark.type.name,attrs:this.mark.attrs,contentElement:this.contentDOM}}matchesMark(e){return this.dirty!=xe&&this.mark.eq(e)}markDirty(e,t){if(super.markDirty(e,t),this.dirty!=ce){let r=this.parent;for(;!r.node;)r=r.parent;r.dirty<this.dirty&&(r.dirty=this.dirty),this.dirty=ce}}slice(e,t,r){let i=st.create(this.parent,this.mark,!0,r),s=this.children,o=this.size;t<o&&(s=hr(s,t,o,r)),e>0&&(s=hr(s,0,e,r));for(let l=0;l<s.length;l++)s[l].parent=i;return i.children=s,i}}class Le extends Wt{constructor(e,t,r,i,s,o,l,a,c){super(e,[],s,o),this.node=t,this.outerDeco=r,this.innerDeco=i,this.nodeDOM=l}static create(e,t,r,i,s,o){let l=s.nodeViews[t.type.name],a,c=l&&l(t,s,()=>{if(!a)return o;if(a.parent)return a.parent.posBeforeChild(a)},r,i),u=c&&c.dom,d=c&&c.contentDOM;if(t.isText){if(!u)u=document.createTextNode(t.text);else if(u.nodeType!=3)throw new RangeError("Text must be rendered as a DOM text node")}else u||({dom:u,contentDOM:d}=ye.renderSpec(document,t.type.spec.toDOM(t)));!d&&!t.isText&&u.nodeName!="BR"&&(u.hasAttribute("contenteditable")||(u.contentEditable="false"),t.type.spec.draggable&&(u.draggable=!0));let f=u;return u=Gs(u,r,t),c?a=new Ba(e,t,r,i,u,d||null,f,c,s,o+1):t.isText?new Mn(e,t,r,i,u,f,s):new Le(e,t,r,i,u,d||null,f,s,o+1)}parseRule(){if(this.node.type.spec.reparseInView)return null;let e={node:this.node.type.name,attrs:this.node.attrs};if(this.node.type.whitespace=="pre"&&(e.preserveWhitespace="full"),!this.contentDOM)e.getContent=()=>this.node.content;else if(!this.contentLost)e.contentElement=this.contentDOM;else{for(let t=this.children.length-1;t>=0;t--){let r=this.children[t];if(this.dom.contains(r.dom.parentNode)){e.contentElement=r.dom.parentNode;break}}e.contentElement||(e.getContent=()=>k.empty)}return e}matchesNode(e,t,r){return this.dirty==ce&&e.eq(this.node)&&fr(t,this.outerDeco)&&r.eq(this.innerDeco)}get size(){return this.node.nodeSize}get border(){return this.node.isLeaf?0:1}updateChildren(e,t){let r=this.node.inlineContent,i=t,s=e.composing?this.localCompositionInfo(e,t):null,o=s&&s.pos>-1?s:null,l=s&&s.pos<0,a=new La(this,o&&o.node,e);$a(this.node,this.innerDeco,(c,u,d)=>{c.spec.marks?a.syncToMarks(c.spec.marks,r,e):c.type.side>=0&&!d&&a.syncToMarks(u==this.node.childCount?B.none:this.node.child(u).marks,r,e),a.placeWidget(c,e,i)},(c,u,d,f)=>{a.syncToMarks(c.marks,r,e);let h;a.findNodeMatch(c,u,d,f)||l&&e.state.selection.from>i&&e.state.selection.to<i+c.nodeSize&&(h=a.findIndexWithChild(s.node))>-1&&a.updateNodeAt(c,u,d,h,e)||a.updateNextNode(c,u,d,e,f,i)||a.addNode(c,u,d,e,i),i+=c.nodeSize}),a.syncToMarks([],r,e),this.node.isTextblock&&a.addTextblockHacks(),a.destroyRest(),(a.changed||this.dirty==_e)&&(o&&this.protectLocalComposition(e,o),Us(this.contentDOM,this.children,e),bt&&Ha(this.dom))}localCompositionInfo(e,t){let{from:r,to:i}=e.state.selection;if(!(e.state.selection instanceof O)||r<t||i>t+this.node.content.size)return null;let s=e.input.compositionNode;if(!s||!this.dom.contains(s.parentNode))return null;if(this.node.inlineContent){let o=s.nodeValue,l=Wa(this.node.content,o,r-t,i-t);return l<0?null:{node:s,pos:l,text:o}}else return{node:s,pos:-1,text:""}}protectLocalComposition(e,{node:t,pos:r,text:i}){if(this.getDesc(t))return;let s=t;for(;s.parentNode!=this.contentDOM;s=s.parentNode){for(;s.previousSibling;)s.parentNode.removeChild(s.previousSibling);for(;s.nextSibling;)s.parentNode.removeChild(s.nextSibling);s.pmViewDesc&&(s.pmViewDesc=void 0)}let o=new Pa(this,s,t,i);e.input.compositionNodes.push(o),this.children=hr(this.children,r,r+i.length,e,o)}update(e,t,r,i){return this.dirty==xe||!e.sameMarkup(this.node)?!1:(this.updateInner(e,t,r,i),!0)}updateInner(e,t,r,i){this.updateOuterDeco(t),this.node=e,this.innerDeco=r,this.contentDOM&&this.updateChildren(i,this.posAtStart),this.dirty=ce}updateOuterDeco(e){if(fr(e,this.outerDeco))return;let t=this.nodeDOM.nodeType!=1,r=this.dom;this.dom=_s(this.dom,this.nodeDOM,dr(this.outerDeco,this.node,t),dr(e,this.node,t)),this.dom!=r&&(r.pmViewDesc=void 0,this.dom.pmViewDesc=this),this.outerDeco=e}selectNode(){this.nodeDOM.nodeType==1&&this.nodeDOM.classList.add("ProseMirror-selectednode"),(this.contentDOM||!this.node.type.spec.draggable)&&(this.dom.draggable=!0)}deselectNode(){this.nodeDOM.nodeType==1&&(this.nodeDOM.classList.remove("ProseMirror-selectednode"),(this.contentDOM||!this.node.type.spec.draggable)&&this.dom.removeAttribute("draggable"))}get domAtom(){return this.node.isAtom}}function hi(n,e,t,r,i){Gs(r,e,n);let s=new Le(void 0,n,e,t,r,r,r,i,0);return s.contentDOM&&s.updateChildren(i,0),s}class Mn extends Le{constructor(e,t,r,i,s,o,l){super(e,t,r,i,s,null,o,l,0)}parseRule(){let e=this.nodeDOM.parentNode;for(;e&&e!=this.dom&&!e.pmIsDeco;)e=e.parentNode;return{skip:e||!0}}update(e,t,r,i){return this.dirty==xe||this.dirty!=ce&&!this.inParent()||!e.sameMarkup(this.node)?!1:(this.updateOuterDeco(t),(this.dirty!=ce||e.text!=this.node.text)&&e.text!=this.nodeDOM.nodeValue&&(this.nodeDOM.nodeValue=e.text,i.trackWrites==this.nodeDOM&&(i.trackWrites=null)),this.node=e,this.dirty=ce,!0)}inParent(){let e=this.parent.contentDOM;for(let t=this.nodeDOM;t;t=t.parentNode)if(t==e)return!0;return!1}domFromPos(e){return{node:this.nodeDOM,offset:e}}localPosFromDOM(e,t,r){return e==this.nodeDOM?this.posAtStart+Math.min(t,this.node.text.length):super.localPosFromDOM(e,t,r)}ignoreMutation(e){return e.type!="characterData"&&e.type!="selection"}slice(e,t,r){let i=this.node.cut(e,t),s=document.createTextNode(i.text);return new Mn(this.parent,i,this.outerDeco,this.innerDeco,s,s,r)}markDirty(e,t){super.markDirty(e,t),this.dom!=this.nodeDOM&&(e==0||t==this.nodeDOM.nodeValue.length)&&(this.dirty=xe)}get domAtom(){return!1}isText(e){return this.node.text==e}}class qs extends Wt{parseRule(){return{ignore:!0}}matchesHack(e){return this.dirty==ce&&this.dom.nodeName==e}get domAtom(){return!0}get ignoreForCoords(){return this.dom.nodeName=="IMG"}}class Ba extends Le{constructor(e,t,r,i,s,o,l,a,c,u){super(e,t,r,i,s,o,l,c,u),this.spec=a}update(e,t,r,i){if(this.dirty==xe)return!1;if(this.spec.update){let s=this.spec.update(e,t,r);return s&&this.updateInner(e,t,r,i),s}else return!this.contentDOM&&!e.isLeaf?!1:super.update(e,t,r,i)}selectNode(){this.spec.selectNode?this.spec.selectNode():super.selectNode()}deselectNode(){this.spec.deselectNode?this.spec.deselectNode():super.deselectNode()}setSelection(e,t,r,i){this.spec.setSelection?this.spec.setSelection(e,t,r):super.setSelection(e,t,r,i)}destroy(){this.spec.destroy&&this.spec.destroy(),super.destroy()}stopEvent(e){return this.spec.stopEvent?this.spec.stopEvent(e):!1}ignoreMutation(e){return this.spec.ignoreMutation?this.spec.ignoreMutation(e):super.ignoreMutation(e)}}function Us(n,e,t){let r=n.firstChild,i=!1;for(let s=0;s<e.length;s++){let o=e[s],l=o.dom;if(l.parentNode==n){for(;l!=r;)r=pi(r),i=!0;r=r.nextSibling}else i=!0,n.insertBefore(l,r);if(o instanceof st){let a=r?r.previousSibling:n.lastChild;Us(o.contentDOM,o.children,t),r=a?a.nextSibling:n.firstChild}}for(;r;)r=pi(r),i=!0;i&&t.trackWrites==n&&(t.trackWrites=null)}const At=function(n){n&&(this.nodeName=n)};At.prototype=Object.create(null);const Ge=[new At];function dr(n,e,t){if(n.length==0)return Ge;let r=t?Ge[0]:new At,i=[r];for(let s=0;s<n.length;s++){let o=n[s].type.attrs;if(o){o.nodeName&&i.push(r=new At(o.nodeName));for(let l in o){let a=o[l];a!=null&&(t&&i.length==1&&i.push(r=new At(e.isInline?"span":"div")),l=="class"?r.class=(r.class?r.class+" ":"")+a:l=="style"?r.style=(r.style?r.style+";":"")+a:l!="nodeName"&&(r[l]=a))}}}return i}function _s(n,e,t,r){if(t==Ge&&r==Ge)return e;let i=e;for(let s=0;s<r.length;s++){let o=r[s],l=t[s];if(s){let a;l&&l.nodeName==o.nodeName&&i!=n&&(a=i.parentNode)&&a.nodeName.toLowerCase()==o.nodeName||(a=document.createElement(o.nodeName),a.pmIsDeco=!0,a.appendChild(i),l=Ge[0]),i=a}za(i,l||Ge[0],o)}return i}function za(n,e,t){for(let r in e)r!="class"&&r!="style"&&r!="nodeName"&&!(r in t)&&n.removeAttribute(r);for(let r in t)r!="class"&&r!="style"&&r!="nodeName"&&t[r]!=e[r]&&n.setAttribute(r,t[r]);if(e.class!=t.class){let r=e.class?e.class.split(" ").filter(Boolean):[],i=t.class?t.class.split(" ").filter(Boolean):[];for(let s=0;s<r.length;s++)i.indexOf(r[s])==-1&&n.classList.remove(r[s]);for(let s=0;s<i.length;s++)r.indexOf(i[s])==-1&&n.classList.add(i[s]);n.classList.length==0&&n.removeAttribute("class")}if(e.style!=t.style){if(e.style){let r=/\s*([\w\-\xa1-\uffff]+)\s*:(?:"(?:\\.|[^"])*"|'(?:\\.|[^'])*'|\(.*?\)|[^;])*/g,i;for(;i=r.exec(e.style);)n.style.removeProperty(i[1])}t.style&&(n.style.cssText+=t.style)}}function Gs(n,e,t){return _s(n,n,Ge,dr(e,t,n.nodeType!=1))}function fr(n,e){if(n.length!=e.length)return!1;for(let t=0;t<n.length;t++)if(!n[t].type.eq(e[t].type))return!1;return!0}function pi(n){let e=n.nextSibling;return n.parentNode.removeChild(n),e}class La{constructor(e,t,r){this.lock=t,this.view=r,this.index=0,this.stack=[],this.changed=!1,this.top=e,this.preMatch=Fa(e.node.content,e)}destroyBetween(e,t){if(e!=t){for(let r=e;r<t;r++)this.top.children[r].destroy();this.top.children.splice(e,t-e),this.changed=!0}}destroyRest(){this.destroyBetween(this.index,this.top.children.length)}syncToMarks(e,t,r){let i=0,s=this.stack.length>>1,o=Math.min(s,e.length);for(;i<o&&(i==s-1?this.top:this.stack[i+1<<1]).matchesMark(e[i])&&e[i].type.spec.spanning!==!1;)i++;for(;i<s;)this.destroyRest(),this.top.dirty=ce,this.index=this.stack.pop(),this.top=this.stack.pop(),s--;for(;s<e.length;){this.stack.push(this.top,this.index+1);let l=-1;for(let a=this.index;a<Math.min(this.index+3,this.top.children.length);a++){let c=this.top.children[a];if(c.matchesMark(e[s])&&!this.isLocked(c.dom)){l=a;break}}if(l>-1)l>this.index&&(this.changed=!0,this.destroyBetween(this.index,l)),this.top=this.top.children[this.index];else{let a=st.create(this.top,e[s],t,r);this.top.children.splice(this.index,0,a),this.top=a,this.changed=!0}this.index=0,s++}}findNodeMatch(e,t,r,i){let s=-1,o;if(i>=this.preMatch.index&&(o=this.preMatch.matches[i-this.preMatch.index]).parent==this.top&&o.matchesNode(e,t,r))s=this.top.children.indexOf(o,this.index);else for(let l=this.index,a=Math.min(this.top.children.length,l+5);l<a;l++){let c=this.top.children[l];if(c.matchesNode(e,t,r)&&!this.preMatch.matched.has(c)){s=l;break}}return s<0?!1:(this.destroyBetween(this.index,s),this.index++,!0)}updateNodeAt(e,t,r,i,s){let o=this.top.children[i];return o.dirty==xe&&o.dom==o.contentDOM&&(o.dirty=_e),o.update(e,t,r,s)?(this.destroyBetween(this.index,i),this.index++,!0):!1}findIndexWithChild(e){for(;;){let t=e.parentNode;if(!t)return-1;if(t==this.top.contentDOM){let r=e.pmViewDesc;if(r){for(let i=this.index;i<this.top.children.length;i++)if(this.top.children[i]==r)return i}return-1}e=t}}updateNextNode(e,t,r,i,s,o){for(let l=this.index;l<this.top.children.length;l++){let a=this.top.children[l];if(a instanceof Le){let c=this.preMatch.matched.get(a);if(c!=null&&c!=s)return!1;let u=a.dom,d,f=this.isLocked(u)&&!(e.isText&&a.node&&a.node.isText&&a.nodeDOM.nodeValue==e.text&&a.dirty!=xe&&fr(t,a.outerDeco));if(!f&&a.update(e,t,r,i))return this.destroyBetween(this.index,l),a.dom!=u&&(this.changed=!0),this.index++,!0;if(!f&&(d=this.recreateWrapper(a,e,t,r,i,o)))return this.top.children[this.index]=d,d.contentDOM&&(d.dirty=_e,d.updateChildren(i,o+1),d.dirty=ce),this.changed=!0,this.index++,!0;break}}return!1}recreateWrapper(e,t,r,i,s,o){if(e.dirty||t.isAtom||!e.children.length||!e.node.content.eq(t.content))return null;let l=Le.create(this.top,t,r,i,s,o);if(l.contentDOM){l.children=e.children,e.children=[];for(let a of l.children)a.parent=l}return e.destroy(),l}addNode(e,t,r,i,s){let o=Le.create(this.top,e,t,r,i,s);o.contentDOM&&o.updateChildren(i,s+1),this.top.children.splice(this.index++,0,o),this.changed=!0}placeWidget(e,t,r){let i=this.index<this.top.children.length?this.top.children[this.index]:null;if(i&&i.matchesWidget(e)&&(e==i.widget||!i.widget.type.toDOM.parentNode))this.index++;else{let s=new Ks(this.top,e,t,r);this.top.children.splice(this.index++,0,s),this.changed=!0}}addTextblockHacks(){let e=this.top.children[this.index-1],t=this.top;for(;e instanceof st;)t=e,e=t.children[t.children.length-1];(!e||!(e instanceof Mn)||/\n$/.test(e.node.text)||this.view.requiresGeckoHackNode&&/\s$/.test(e.node.text))&&((te||ee)&&e&&e.dom.contentEditable=="false"&&this.addHackNode("IMG",t),this.addHackNode("BR",this.top))}addHackNode(e,t){if(t==this.top&&this.index<t.children.length&&t.children[this.index].matchesHack(e))this.index++;else{let r=document.createElement(e);e=="IMG"&&(r.className="ProseMirror-separator",r.alt=""),e=="BR"&&(r.className="ProseMirror-trailingBreak");let i=new qs(this.top,[],r,null);t!=this.top?t.children.push(i):t.children.splice(this.index++,0,i),this.changed=!0}}isLocked(e){return this.lock&&(e==this.lock||e.nodeType==1&&e.contains(this.lock.parentNode))}}function Fa(n,e){let t=e,r=t.children.length,i=n.childCount,s=new Map,o=[];e:for(;i>0;){let l;for(;;)if(r){let c=t.children[r-1];if(c instanceof st)t=c,r=c.children.length;else{l=c,r--;break}}else{if(t==e)break e;r=t.parent.children.indexOf(t),t=t.parent}let a=l.node;if(a){if(a!=n.child(i-1))break;--i,s.set(l,i),o.push(l)}}return{index:i,matched:s,matches:o.reverse()}}function Va(n,e){return n.type.side-e.type.side}function $a(n,e,t,r){let i=e.locals(n),s=0;if(i.length==0){for(let c=0;c<n.childCount;c++){let u=n.child(c);r(u,i,e.forChild(s,u),c),s+=u.nodeSize}return}let o=0,l=[],a=null;for(let c=0;;){let u,d;for(;o<i.length&&i[o].to==s;){let g=i[o++];g.widget&&(u?(d||(d=[u])).push(g):u=g)}if(u)if(d){d.sort(Va);for(let g=0;g<d.length;g++)t(d[g],c,!!a)}else t(u,c,!!a);let f,h;if(a)h=-1,f=a,a=null;else if(c<n.childCount)h=c,f=n.child(c++);else break;for(let g=0;g<l.length;g++)l[g].to<=s&&l.splice(g--,1);for(;o<i.length&&i[o].from<=s&&i[o].to>s;)l.push(i[o++]);let p=s+f.nodeSize;if(f.isText){let g=p;o<i.length&&i[o].from<g&&(g=i[o].from);for(let y=0;y<l.length;y++)l[y].to<g&&(g=l[y].to);g<p&&(a=f.cut(g-s),f=f.cut(0,g-s),p=g,h=-1)}else for(;o<i.length&&i[o].to<p;)o++;let m=f.isInline&&!f.isLeaf?l.filter(g=>!g.inline):l.slice();r(f,m,e.forChild(s,f),h),s=p}}function Ha(n){if(n.nodeName=="UL"||n.nodeName=="OL"){let e=n.style.cssText;n.style.cssText=e+"; list-style: square !important",window.getComputedStyle(n).listStyle,n.style.cssText=e}}function Wa(n,e,t,r){for(let i=0,s=0;i<n.childCount&&s<=r;){let o=n.child(i++),l=s;if(s+=o.nodeSize,!o.isText)continue;let a=o.text;for(;i<n.childCount;){let c=n.child(i++);if(s+=c.nodeSize,!c.isText)break;a+=c.text}if(s>=t){if(s>=r&&a.slice(r-e.length-l,r-l)==e)return r-e.length;let c=l<r?a.lastIndexOf(e,r-l-1):-1;if(c>=0&&c+e.length+l>=t)return l+c;if(t==r&&a.length>=r+e.length-l&&a.slice(r-l,r-l+e.length)==e)return r}}return-1}function hr(n,e,t,r,i){let s=[];for(let o=0,l=0;o<n.length;o++){let a=n[o],c=l,u=l+=a.size;c>=t||u<=e?s.push(a):(c<e&&s.push(a.slice(0,e-c,r)),i&&(s.push(i),i=void 0),u>t&&s.push(a.slice(t-c,a.size,r)))}return s}function Or(n,e=null){let t=n.domSelectionRange(),r=n.state.doc;if(!t.focusNode)return null;let i=n.docView.nearestDesc(t.focusNode),s=i&&i.size==0,o=n.docView.posFromDOM(t.focusNode,t.focusOffset,1);if(o<0)return null;let l=r.resolve(o),a,c;if(Sn(t)){for(a=l;i&&!i.node;)i=i.parent;let u=i.node;if(i&&u.isAtom&&w.isSelectable(u)&&i.parent&&!(u.isInline&&ha(t.focusNode,t.focusOffset,i.dom))){let d=i.posBefore;c=new w(o==d?l:r.resolve(d))}}else{let u=n.docView.posFromDOM(t.anchorNode,t.anchorOffset,1);if(u<0)return null;a=r.resolve(u)}if(!c){let u=e=="pointer"||n.state.selection.head<l.pos&&!s?1:-1;c=vr(n,a,l,u)}return c}function Ys(n){return n.editable?n.hasFocus():Zs(n)&&document.activeElement&&document.activeElement.contains(n.dom)}function Te(n,e=!1){let t=n.state.selection;if(Xs(n,t),!!Ys(n)){if(!e&&n.input.mouseDown&&n.input.mouseDown.allowDefault&&ee){let r=n.domSelectionRange(),i=n.domObserver.currentSelection;if(r.anchorNode&&i.anchorNode&&it(r.anchorNode,r.anchorOffset,i.anchorNode,i.anchorOffset)){n.input.mouseDown.delayedSelectionSync=!0,n.domObserver.setCurSelection();return}}if(n.domObserver.disconnectSelection(),n.cursorWrapper)ja(n);else{let{anchor:r,head:i}=t,s,o;mi&&!(t instanceof O)&&(t.$from.parent.inlineContent||(s=gi(n,t.from)),!t.empty&&!t.$from.parent.inlineContent&&(o=gi(n,t.to))),n.docView.setSelection(r,i,n.root,e),mi&&(s&&yi(s),o&&yi(o)),t.visible?n.dom.classList.remove("ProseMirror-hideselection"):(n.dom.classList.add("ProseMirror-hideselection"),"onselectionchange"in document&&Ja(n))}n.domObserver.setCurSelection(),n.domObserver.connectSelection()}}const mi=te||ee&&ga<63;function gi(n,e){let{node:t,offset:r}=n.docView.domFromPos(e,0),i=r<t.childNodes.length?t.childNodes[r]:null,s=r?t.childNodes[r-1]:null;if(te&&i&&i.contentEditable=="false")return Kn(i);if((!i||i.contentEditable=="false")&&(!s||s.contentEditable=="false")){if(i)return Kn(i);if(s)return Kn(s)}}function Kn(n){return n.contentEditable="true",te&&n.draggable&&(n.draggable=!1,n.wasDraggable=!0),n}function yi(n){n.contentEditable="false",n.wasDraggable&&(n.draggable=!0,n.wasDraggable=null)}function Ja(n){let e=n.dom.ownerDocument;e.removeEventListener("selectionchange",n.input.hideSelectionGuard);let t=n.domSelectionRange(),r=t.anchorNode,i=t.anchorOffset;e.addEventListener("selectionchange",n.input.hideSelectionGuard=()=>{(t.anchorNode!=r||t.anchorOffset!=i)&&(e.removeEventListener("selectionchange",n.input.hideSelectionGuard),setTimeout(()=>{(!Ys(n)||n.state.selection.visible)&&n.dom.classList.remove("ProseMirror-hideselection")},20))})}function ja(n){let e=n.domSelection(),t=document.createRange(),r=n.cursorWrapper.dom,i=r.nodeName=="IMG";i?t.setEnd(r.parentNode,G(r)+1):t.setEnd(r,0),t.collapse(!1),e.removeAllRanges(),e.addRange(t),!i&&!n.state.selection.visible&&ie&&ze<=11&&(r.disabled=!0,r.disabled=!1)}function Xs(n,e){if(e instanceof w){let t=n.docView.descAt(e.from);t!=n.lastSelectedViewDesc&&(ki(n),t&&t.selectNode(),n.lastSelectedViewDesc=t)}else ki(n)}function ki(n){n.lastSelectedViewDesc&&(n.lastSelectedViewDesc.parent&&n.lastSelectedViewDesc.deselectNode(),n.lastSelectedViewDesc=void 0)}function vr(n,e,t,r){return n.someProp("createSelectionBetween",i=>i(n,e,t))||O.between(e,t,r)}function bi(n){return n.editable&&!n.hasFocus()?!1:Zs(n)}function Zs(n){let e=n.domSelectionRange();if(!e.anchorNode)return!1;try{return n.dom.contains(e.anchorNode.nodeType==3?e.anchorNode.parentNode:e.anchorNode)&&(n.editable||n.dom.contains(e.focusNode.nodeType==3?e.focusNode.parentNode:e.focusNode))}catch{return!1}}function Ka(n){let e=n.docView.domFromPos(n.state.selection.anchor,0),t=n.domSelectionRange();return it(e.node,e.offset,t.anchorNode,t.anchorOffset)}function pr(n,e){let{$anchor:t,$head:r}=n.selection,i=e>0?t.max(r):t.min(r),s=i.parent.inlineContent?i.depth?n.doc.resolve(e>0?i.after():i.before()):null:i;return s&&E.findFrom(s,e)}function Ee(n,e){return n.dispatch(n.state.tr.setSelection(e).scrollIntoView()),!0}function xi(n,e,t){let r=n.state.selection;if(r instanceof O)if(t.indexOf("s")>-1){let{$head:i}=r,s=i.textOffset?null:e<0?i.nodeBefore:i.nodeAfter;if(!s||s.isText||!s.isLeaf)return!1;let o=n.state.doc.resolve(i.pos+s.nodeSize*(e<0?-1:1));return Ee(n,new O(r.$anchor,o))}else if(r.empty){if(n.endOfTextblock(e>0?"forward":"backward")){let i=pr(n.state,e);return i&&i instanceof w?Ee(n,i):!1}else if(!(ae&&t.indexOf("m")>-1)){let i=r.$head,s=i.textOffset?null:e<0?i.nodeBefore:i.nodeAfter,o;if(!s||s.isText)return!1;let l=e<0?i.pos-s.nodeSize:i.pos;return s.isAtom||(o=n.docView.descAt(l))&&!o.contentDOM?w.isSelectable(s)?Ee(n,new w(e<0?n.state.doc.resolve(i.pos-s.nodeSize):i)):Ht?Ee(n,new O(n.state.doc.resolve(e<0?l:l+s.nodeSize))):!1:!1}}else return!1;else{if(r instanceof w&&r.node.isInline)return Ee(n,new O(e>0?r.$to:r.$from));{let i=pr(n.state,e);return i?Ee(n,i):!1}}}function ln(n){return n.nodeType==3?n.nodeValue.length:n.childNodes.length}function Dt(n,e){let t=n.pmViewDesc;return t&&t.size==0&&(e<0||n.nextSibling||n.nodeName!="BR")}function lt(n,e){return e<0?qa(n):Ua(n)}function qa(n){let e=n.domSelectionRange(),t=e.focusNode,r=e.focusOffset;if(!t)return;let i,s,o=!1;for(pe&&t.nodeType==1&&r<ln(t)&&Dt(t.childNodes[r],-1)&&(o=!0);;)if(r>0){if(t.nodeType!=1)break;{let l=t.childNodes[r-1];if(Dt(l,-1))i=t,s=--r;else if(l.nodeType==3)t=l,r=t.nodeValue.length;else break}}else{if(Qs(t))break;{let l=t.previousSibling;for(;l&&Dt(l,-1);)i=t.parentNode,s=G(l),l=l.previousSibling;if(l)t=l,r=ln(t);else{if(t=t.parentNode,t==n.dom)break;r=0}}}o?mr(n,t,r):i&&mr(n,i,s)}function Ua(n){let e=n.domSelectionRange(),t=e.focusNode,r=e.focusOffset;if(!t)return;let i=ln(t),s,o;for(;;)if(r<i){if(t.nodeType!=1)break;let l=t.childNodes[r];if(Dt(l,1))s=t,o=++r;else break}else{if(Qs(t))break;{let l=t.nextSibling;for(;l&&Dt(l,1);)s=l.parentNode,o=G(l)+1,l=l.nextSibling;if(l)t=l,r=0,i=ln(t);else{if(t=t.parentNode,t==n.dom)break;r=i=0}}}s&&mr(n,s,o)}function Qs(n){let e=n.pmViewDesc;return e&&e.node&&e.node.isBlock}function _a(n,e){for(;n&&e==n.childNodes.length&&!$t(n);)e=G(n)+1,n=n.parentNode;for(;n&&e<n.childNodes.length;){let t=n.childNodes[e];if(t.nodeType==3)return t;if(t.nodeType==1&&t.contentEditable=="false")break;n=t,e=0}}function Ga(n,e){for(;n&&!e&&!$t(n);)e=G(n),n=n.parentNode;for(;n&&e;){let t=n.childNodes[e-1];if(t.nodeType==3)return t;if(t.nodeType==1&&t.contentEditable=="false")break;n=t,e=n.childNodes.length}}function mr(n,e,t){if(e.nodeType!=3){let s,o;(o=_a(e,t))?(e=o,t=0):(s=Ga(e,t))&&(e=s,t=s.nodeValue.length)}let r=n.domSelection();if(Sn(r)){let s=document.createRange();s.setEnd(e,t),s.setStart(e,t),r.removeAllRanges(),r.addRange(s)}else r.extend&&r.extend(e,t);n.domObserver.setCurSelection();let{state:i}=n;setTimeout(()=>{n.state==i&&Te(n)},50)}function Si(n,e){let t=n.state.doc.resolve(e);if(!(ee||ya)&&t.parent.inlineContent){let i=n.coordsAtPos(e);if(e>t.start()){let s=n.coordsAtPos(e-1),o=(s.top+s.bottom)/2;if(o>i.top&&o<i.bottom&&Math.abs(s.left-i.left)>1)return s.left<i.left?"ltr":"rtl"}if(e<t.end()){let s=n.coordsAtPos(e+1),o=(s.top+s.bottom)/2;if(o>i.top&&o<i.bottom&&Math.abs(s.left-i.left)>1)return s.left>i.left?"ltr":"rtl"}}return getComputedStyle(n.dom).direction=="rtl"?"rtl":"ltr"}function Mi(n,e,t){let r=n.state.selection;if(r instanceof O&&!r.empty||t.indexOf("s")>-1||ae&&t.indexOf("m")>-1)return!1;let{$from:i,$to:s}=r;if(!i.parent.inlineContent||n.endOfTextblock(e<0?"up":"down")){let o=pr(n.state,e);if(o&&o instanceof w)return Ee(n,o)}if(!i.parent.inlineContent){let o=e<0?i:s,l=r instanceof he?E.near(o,e):E.findFrom(o,e);return l?Ee(n,l):!1}return!1}function Ci(n,e){if(!(n.state.selection instanceof O))return!0;let{$head:t,$anchor:r,empty:i}=n.state.selection;if(!t.sameParent(r))return!0;if(!i)return!1;if(n.endOfTextblock(e>0?"forward":"backward"))return!0;let s=!t.textOffset&&(e<0?t.nodeBefore:t.nodeAfter);if(s&&!s.isText){let o=n.state.tr;return e<0?o.delete(t.pos-s.nodeSize,t.pos):o.delete(t.pos,t.pos+s.nodeSize),n.dispatch(o),!0}return!1}function wi(n,e,t){n.domObserver.stop(),e.contentEditable=t,n.domObserver.start()}function Ya(n){if(!te||n.state.selection.$head.parentOffset>0)return!1;let{focusNode:e,focusOffset:t}=n.domSelectionRange();if(e&&e.nodeType==1&&t==0&&e.firstChild&&e.firstChild.contentEditable=="false"){let r=e.firstChild;wi(n,r,"true"),setTimeout(()=>wi(n,r,"false"),20)}return!1}function Xa(n){let e="";return n.ctrlKey&&(e+="c"),n.metaKey&&(e+="m"),n.altKey&&(e+="a"),n.shiftKey&&(e+="s"),e}function Za(n,e){let t=e.keyCode,r=Xa(e);if(t==8||ae&&t==72&&r=="c")return Ci(n,-1)||lt(n,-1);if(t==46&&!e.shiftKey||ae&&t==68&&r=="c")return Ci(n,1)||lt(n,1);if(t==13||t==27)return!0;if(t==37||ae&&t==66&&r=="c"){let i=t==37?Si(n,n.state.selection.from)=="ltr"?-1:1:-1;return xi(n,i,r)||lt(n,i)}else if(t==39||ae&&t==70&&r=="c"){let i=t==39?Si(n,n.state.selection.from)=="ltr"?1:-1:1;return xi(n,i,r)||lt(n,i)}else{if(t==38||ae&&t==80&&r=="c")return Mi(n,-1,r)||lt(n,-1);if(t==40||ae&&t==78&&r=="c")return Ya(n)||Mi(n,1,r)||lt(n,1);if(r==(ae?"m":"c")&&(t==66||t==73||t==89||t==90))return!0}return!1}function eo(n,e){n.someProp("transformCopied",h=>{e=h(e,n)});let t=[],{content:r,openStart:i,openEnd:s}=e;for(;i>1&&s>1&&r.childCount==1&&r.firstChild.childCount==1;){i--,s--;let h=r.firstChild;t.push(h.type.name,h.attrs!=h.type.defaultAttrs?h.attrs:null),r=h.content}let o=n.someProp("clipboardSerializer")||ye.fromSchema(n.state.schema),l=oo(),a=l.createElement("div");a.appendChild(o.serializeFragment(r,{document:l}));let c=a.firstChild,u,d=0;for(;c&&c.nodeType==1&&(u=so[c.nodeName.toLowerCase()]);){for(let h=u.length-1;h>=0;h--){let p=l.createElement(u[h]);for(;a.firstChild;)p.appendChild(a.firstChild);a.appendChild(p),d++}c=a.firstChild}c&&c.nodeType==1&&c.setAttribute("data-pm-slice",`${i} ${s}${d?` -${d}`:""} ${JSON.stringify(t)}`);let f=n.someProp("clipboardTextSerializer",h=>h(e,n))||e.content.textBetween(0,e.content.size,`

`);return{dom:a,text:f,slice:e}}function to(n,e,t,r,i){let s=i.parent.type.spec.code,o,l;if(!t&&!e)return null;let a=e&&(r||s||!t);if(a){if(n.someProp("transformPastedText",f=>{e=f(e,s||r,n)}),s)return e?new x(k.from(n.state.schema.text(e.replace(/\r\n?/g,`
`))),0,0):x.empty;let d=n.someProp("clipboardTextParser",f=>f(e,i,r,n));if(d)l=d;else{let f=i.marks(),{schema:h}=n.state,p=ye.fromSchema(h);o=document.createElement("div"),e.split(/(?:\r\n?|\n)+/).forEach(m=>{let g=o.appendChild(document.createElement("p"));m&&g.appendChild(p.serializeNode(h.text(m,f)))})}}else n.someProp("transformPastedHTML",d=>{t=d(t,n)}),o=tc(t),Ht&&nc(o);let c=o&&o.querySelector("[data-pm-slice]"),u=c&&/^(\d+) (\d+)(?: -(\d+))? (.*)/.exec(c.getAttribute("data-pm-slice")||"");if(u&&u[3])for(let d=+u[3];d>0;d--){let f=o.firstChild;for(;f&&f.nodeType!=1;)f=f.nextSibling;if(!f)break;o=f}if(l||(l=(n.someProp("clipboardParser")||n.someProp("domParser")||gt.fromSchema(n.state.schema)).parseSlice(o,{preserveWhitespace:!!(a||u),context:i,ruleFromNode(f){return f.nodeName=="BR"&&!f.nextSibling&&f.parentNode&&!Qa.test(f.parentNode.nodeName)?{ignore:!0}:null}})),u)l=rc(Ti(l,+u[1],+u[2]),u[4]);else if(l=x.maxOpen(ec(l.content,i),!0),l.openStart||l.openEnd){let d=0,f=0;for(let h=l.content.firstChild;d<l.openStart&&!h.type.spec.isolating;d++,h=h.firstChild);for(let h=l.content.lastChild;f<l.openEnd&&!h.type.spec.isolating;f++,h=h.lastChild);l=Ti(l,d,f)}return n.someProp("transformPasted",d=>{l=d(l,n)}),l}const Qa=/^(a|abbr|acronym|b|cite|code|del|em|i|ins|kbd|label|output|q|ruby|s|samp|span|strong|sub|sup|time|u|tt|var)$/i;function ec(n,e){if(n.childCount<2)return n;for(let t=e.depth;t>=0;t--){let i=e.node(t).contentMatchAt(e.index(t)),s,o=[];if(n.forEach(l=>{if(!o)return;let a=i.findWrapping(l.type),c;if(!a)return o=null;if(c=o.length&&s.length&&ro(a,s,l,o[o.length-1],0))o[o.length-1]=c;else{o.length&&(o[o.length-1]=io(o[o.length-1],s.length));let u=no(l,a);o.push(u),i=i.matchType(u.type),s=a}}),o)return k.from(o)}return n}function no(n,e,t=0){for(let r=e.length-1;r>=t;r--)n=e[r].create(null,k.from(n));return n}function ro(n,e,t,r,i){if(i<n.length&&i<e.length&&n[i]==e[i]){let s=ro(n,e,t,r.lastChild,i+1);if(s)return r.copy(r.content.replaceChild(r.childCount-1,s));if(r.contentMatchAt(r.childCount).matchType(i==n.length-1?t.type:n[i+1]))return r.copy(r.content.append(k.from(no(t,n,i+1))))}}function io(n,e){if(e==0)return n;let t=n.content.replaceChild(n.childCount-1,io(n.lastChild,e-1)),r=n.contentMatchAt(n.childCount).fillBefore(k.empty,!0);return n.copy(t.append(r))}function gr(n,e,t,r,i,s){let o=e<0?n.firstChild:n.lastChild,l=o.content;return n.childCount>1&&(s=0),i<r-1&&(l=gr(l,e,t,r,i+1,s)),i>=t&&(l=e<0?o.contentMatchAt(0).fillBefore(l,s<=i).append(l):l.append(o.contentMatchAt(o.childCount).fillBefore(k.empty,!0))),n.replaceChild(e<0?0:n.childCount-1,o.copy(l))}function Ti(n,e,t){return e<n.openStart&&(n=new x(gr(n.content,-1,e,n.openStart,0,n.openEnd),e,n.openEnd)),t<n.openEnd&&(n=new x(gr(n.content,1,t,n.openEnd,0,0),n.openStart,t)),n}const so={thead:["table"],tbody:["table"],tfoot:["table"],caption:["table"],colgroup:["table"],col:["table","colgroup"],tr:["table","tbody"],td:["table","tbody","tr"],th:["table","tbody","tr"]};let Oi=null;function oo(){return Oi||(Oi=document.implementation.createHTMLDocument("title"))}function tc(n){let e=/^(\s*<meta [^>]*>)*/.exec(n);e&&(n=n.slice(e[0].length));let t=oo().createElement("div"),r=/<([a-z][^>\s]+)/i.exec(n),i;if((i=r&&so[r[1].toLowerCase()])&&(n=i.map(s=>"<"+s+">").join("")+n+i.map(s=>"</"+s+">").reverse().join("")),t.innerHTML=n,i)for(let s=0;s<i.length;s++)t=t.querySelector(i[s])||t;return t}function nc(n){let e=n.querySelectorAll(ee?"span:not([class]):not([style])":"span.Apple-converted-space");for(let t=0;t<e.length;t++){let r=e[t];r.childNodes.length==1&&r.textContent==" "&&r.parentNode&&r.parentNode.replaceChild(n.ownerDocument.createTextNode(" "),r)}}function rc(n,e){if(!n.size)return n;let t=n.content.firstChild.type.schema,r;try{r=JSON.parse(e)}catch{return n}let{content:i,openStart:s,openEnd:o}=n;for(let l=r.length-2;l>=0;l-=2){let a=t.nodes[r[l]];if(!a||a.hasRequiredAttrs())break;i=k.from(a.create(r[l+1],i)),s++,o++}return new x(i,s,o)}const ne={},re={},ic={touchstart:!0,touchmove:!0};class sc{constructor(){this.shiftKey=!1,this.mouseDown=null,this.lastKeyCode=null,this.lastKeyCodeTime=0,this.lastClick={time:0,x:0,y:0,type:""},this.lastSelectionOrigin=null,this.lastSelectionTime=0,this.lastIOSEnter=0,this.lastIOSEnterFallbackTimeout=-1,this.lastFocus=0,this.lastTouch=0,this.lastAndroidDelete=0,this.composing=!1,this.compositionNode=null,this.composingTimeout=-1,this.compositionNodes=[],this.compositionEndedAt=-2e8,this.compositionID=1,this.compositionPendingChanges=0,this.domChangeCount=0,this.eventHandlers=Object.create(null),this.hideSelectionGuard=null}}function oc(n){for(let e in ne){let t=ne[e];n.dom.addEventListener(e,n.input.eventHandlers[e]=r=>{ac(n,r)&&!Nr(n,r)&&(n.editable||!(r.type in re))&&t(n,r)},ic[e]?{passive:!0}:void 0)}te&&n.dom.addEventListener("input",()=>null),yr(n)}function Be(n,e){n.input.lastSelectionOrigin=e,n.input.lastSelectionTime=Date.now()}function lc(n){n.domObserver.stop();for(let e in n.input.eventHandlers)n.dom.removeEventListener(e,n.input.eventHandlers[e]);clearTimeout(n.input.composingTimeout),clearTimeout(n.input.lastIOSEnterFallbackTimeout)}function yr(n){n.someProp("handleDOMEvents",e=>{for(let t in e)n.input.eventHandlers[t]||n.dom.addEventListener(t,n.input.eventHandlers[t]=r=>Nr(n,r))})}function Nr(n,e){return n.someProp("handleDOMEvents",t=>{let r=t[e.type];return r?r(n,e)||e.defaultPrevented:!1})}function ac(n,e){if(!e.bubbles)return!0;if(e.defaultPrevented)return!1;for(let t=e.target;t!=n.dom;t=t.parentNode)if(!t||t.nodeType==11||t.pmViewDesc&&t.pmViewDesc.stopEvent(e))return!1;return!0}function cc(n,e){!Nr(n,e)&&ne[e.type]&&(n.editable||!(e.type in re))&&ne[e.type](n,e)}re.keydown=(n,e)=>{let t=e;if(n.input.shiftKey=t.keyCode==16||t.shiftKey,!ao(n,t)&&(n.input.lastKeyCode=t.keyCode,n.input.lastKeyCodeTime=Date.now(),!(de&&ee&&t.keyCode==13)))if(t.keyCode!=229&&n.domObserver.forceFlush(),bt&&t.keyCode==13&&!t.ctrlKey&&!t.altKey&&!t.metaKey){let r=Date.now();n.input.lastIOSEnter=r,n.input.lastIOSEnterFallbackTimeout=setTimeout(()=>{n.input.lastIOSEnter==r&&(n.someProp("handleKeyDown",i=>i(n,Ke(13,"Enter"))),n.input.lastIOSEnter=0)},200)}else n.someProp("handleKeyDown",r=>r(n,t))||Za(n,t)?t.preventDefault():Be(n,"key")};re.keyup=(n,e)=>{e.keyCode==16&&(n.input.shiftKey=!1)};re.keypress=(n,e)=>{let t=e;if(ao(n,t)||!t.charCode||t.ctrlKey&&!t.altKey||ae&&t.metaKey)return;if(n.someProp("handleKeyPress",i=>i(n,t))){t.preventDefault();return}let r=n.state.selection;if(!(r instanceof O)||!r.$from.sameParent(r.$to)){let i=String.fromCharCode(t.charCode);!/[\r\n]/.test(i)&&!n.someProp("handleTextInput",s=>s(n,r.$from.pos,r.$to.pos,i))&&n.dispatch(n.state.tr.insertText(i).scrollIntoView()),t.preventDefault()}};function Cn(n){return{left:n.clientX,top:n.clientY}}function uc(n,e){let t=e.x-n.clientX,r=e.y-n.clientY;return t*t+r*r<100}function Er(n,e,t,r,i){if(r==-1)return!1;let s=n.state.doc.resolve(r);for(let o=s.depth+1;o>0;o--)if(n.someProp(e,l=>o>s.depth?l(n,t,s.nodeAfter,s.before(o),i,!0):l(n,t,s.node(o),s.before(o),i,!1)))return!0;return!1}function mt(n,e,t){n.focused||n.focus();let r=n.state.tr.setSelection(e);r.setMeta("pointer",!0),n.dispatch(r)}function dc(n,e){if(e==-1)return!1;let t=n.state.doc.resolve(e),r=t.nodeAfter;return r&&r.isAtom&&w.isSelectable(r)?(mt(n,new w(t)),!0):!1}function fc(n,e){if(e==-1)return!1;let t=n.state.selection,r,i;t instanceof w&&(r=t.node);let s=n.state.doc.resolve(e);for(let o=s.depth+1;o>0;o--){let l=o>s.depth?s.nodeAfter:s.node(o);if(w.isSelectable(l)){r&&t.$from.depth>0&&o>=t.$from.depth&&s.before(t.$from.depth+1)==t.$from.pos?i=s.before(t.$from.depth):i=s.before(o);break}}return i!=null?(mt(n,w.create(n.state.doc,i)),!0):!1}function hc(n,e,t,r,i){return Er(n,"handleClickOn",e,t,r)||n.someProp("handleClick",s=>s(n,e,r))||(i?fc(n,t):dc(n,t))}function pc(n,e,t,r){return Er(n,"handleDoubleClickOn",e,t,r)||n.someProp("handleDoubleClick",i=>i(n,e,r))}function mc(n,e,t,r){return Er(n,"handleTripleClickOn",e,t,r)||n.someProp("handleTripleClick",i=>i(n,e,r))||gc(n,t,r)}function gc(n,e,t){if(t.button!=0)return!1;let r=n.state.doc;if(e==-1)return r.inlineContent?(mt(n,O.create(r,0,r.content.size)),!0):!1;let i=r.resolve(e);for(let s=i.depth+1;s>0;s--){let o=s>i.depth?i.nodeAfter:i.node(s),l=i.before(s);if(o.inlineContent)mt(n,O.create(r,l+1,l+1+o.content.size));else if(w.isSelectable(o))mt(n,w.create(r,l));else continue;return!0}}function Ar(n){return an(n)}const lo=ae?"metaKey":"ctrlKey";ne.mousedown=(n,e)=>{let t=e;n.input.shiftKey=t.shiftKey;let r=Ar(n),i=Date.now(),s="singleClick";i-n.input.lastClick.time<500&&uc(t,n.input.lastClick)&&!t[lo]&&(n.input.lastClick.type=="singleClick"?s="doubleClick":n.input.lastClick.type=="doubleClick"&&(s="tripleClick")),n.input.lastClick={time:i,x:t.clientX,y:t.clientY,type:s};let o=n.posAtCoords(Cn(t));o&&(s=="singleClick"?(n.input.mouseDown&&n.input.mouseDown.done(),n.input.mouseDown=new yc(n,o,t,!!r)):(s=="doubleClick"?pc:mc)(n,o.pos,o.inside,t)?t.preventDefault():Be(n,"pointer"))};class yc{constructor(e,t,r,i){this.view=e,this.pos=t,this.event=r,this.flushed=i,this.delayedSelectionSync=!1,this.mightDrag=null,this.startDoc=e.state.doc,this.selectNode=!!r[lo],this.allowDefault=r.shiftKey;let s,o;if(t.inside>-1)s=e.state.doc.nodeAt(t.inside),o=t.inside;else{let u=e.state.doc.resolve(t.pos);s=u.parent,o=u.depth?u.before():0}const l=i?null:r.target,a=l?e.docView.nearestDesc(l,!0):null;this.target=a&&a.dom.nodeType==1?a.dom:null;let{selection:c}=e.state;(r.button==0&&s.type.spec.draggable&&s.type.spec.selectable!==!1||c instanceof w&&c.from<=o&&c.to>o)&&(this.mightDrag={node:s,pos:o,addAttr:!!(this.target&&!this.target.draggable),setUneditable:!!(this.target&&pe&&!this.target.hasAttribute("contentEditable"))}),this.target&&this.mightDrag&&(this.mightDrag.addAttr||this.mightDrag.setUneditable)&&(this.view.domObserver.stop(),this.mightDrag.addAttr&&(this.target.draggable=!0),this.mightDrag.setUneditable&&setTimeout(()=>{this.view.input.mouseDown==this&&this.target.setAttribute("contentEditable","false")},20),this.view.domObserver.start()),e.root.addEventListener("mouseup",this.up=this.up.bind(this)),e.root.addEventListener("mousemove",this.move=this.move.bind(this)),Be(e,"pointer")}done(){this.view.root.removeEventListener("mouseup",this.up),this.view.root.removeEventListener("mousemove",this.move),this.mightDrag&&this.target&&(this.view.domObserver.stop(),this.mightDrag.addAttr&&this.target.removeAttribute("draggable"),this.mightDrag.setUneditable&&this.target.removeAttribute("contentEditable"),this.view.domObserver.start()),this.delayedSelectionSync&&setTimeout(()=>Te(this.view)),this.view.input.mouseDown=null}up(e){if(this.done(),!this.view.dom.contains(e.target))return;let t=this.pos;this.view.state.doc!=this.startDoc&&(t=this.view.posAtCoords(Cn(e))),this.updateAllowDefault(e),this.allowDefault||!t?Be(this.view,"pointer"):hc(this.view,t.pos,t.inside,e,this.selectNode)?e.preventDefault():e.button==0&&(this.flushed||te&&this.mightDrag&&!this.mightDrag.node.isAtom||ee&&!this.view.state.selection.visible&&Math.min(Math.abs(t.pos-this.view.state.selection.from),Math.abs(t.pos-this.view.state.selection.to))<=2)?(mt(this.view,E.near(this.view.state.doc.resolve(t.pos))),e.preventDefault()):Be(this.view,"pointer")}move(e){this.updateAllowDefault(e),Be(this.view,"pointer"),e.buttons==0&&this.done()}updateAllowDefault(e){!this.allowDefault&&(Math.abs(this.event.x-e.clientX)>4||Math.abs(this.event.y-e.clientY)>4)&&(this.allowDefault=!0)}}ne.touchstart=n=>{n.input.lastTouch=Date.now(),Ar(n),Be(n,"pointer")};ne.touchmove=n=>{n.input.lastTouch=Date.now(),Be(n,"pointer")};ne.contextmenu=n=>Ar(n);function ao(n,e){return n.composing?!0:te&&Math.abs(e.timeStamp-n.input.compositionEndedAt)<500?(n.input.compositionEndedAt=-2e8,!0):!1}const kc=de?5e3:-1;re.compositionstart=re.compositionupdate=n=>{if(!n.composing){n.domObserver.flush();let{state:e}=n,t=e.selection.$from;if(e.selection.empty&&(e.storedMarks||!t.textOffset&&t.parentOffset&&t.nodeBefore.marks.some(r=>r.type.spec.inclusive===!1)))n.markCursor=n.state.storedMarks||t.marks(),an(n,!0),n.markCursor=null;else if(an(n),pe&&e.selection.empty&&t.parentOffset&&!t.textOffset&&t.nodeBefore.marks.length){let r=n.domSelectionRange();for(let i=r.focusNode,s=r.focusOffset;i&&i.nodeType==1&&s!=0;){let o=s<0?i.lastChild:i.childNodes[s-1];if(!o)break;if(o.nodeType==3){n.domSelection().collapse(o,o.nodeValue.length);break}else i=o,s=-1}}n.input.composing=!0}co(n,kc)};re.compositionend=(n,e)=>{n.composing&&(n.input.composing=!1,n.input.compositionEndedAt=e.timeStamp,n.input.compositionPendingChanges=n.domObserver.pendingRecords().length?n.input.compositionID:0,n.input.compositionNode=null,n.input.compositionPendingChanges&&Promise.resolve().then(()=>n.domObserver.flush()),n.input.compositionID++,co(n,20))};function co(n,e){clearTimeout(n.input.composingTimeout),e>-1&&(n.input.composingTimeout=setTimeout(()=>an(n),e))}function uo(n){for(n.composing&&(n.input.composing=!1,n.input.compositionEndedAt=xc());n.input.compositionNodes.length>0;)n.input.compositionNodes.pop().markParentsDirty()}function bc(n){let e=n.domSelectionRange();if(!e.focusNode)return null;let t=da(e.focusNode,e.focusOffset),r=fa(e.focusNode,e.focusOffset);if(t&&r&&t!=r){let i=r.pmViewDesc,s=n.domObserver.lastChangedTextNode;if(t==s||r==s)return s;if(!i||!i.isText(r.nodeValue))return r;if(n.input.compositionNode==r){let o=t.pmViewDesc;if(!(!o||!o.isText(t.nodeValue)))return r}}return t||r}function xc(){let n=document.createEvent("Event");return n.initEvent("event",!0,!0),n.timeStamp}function an(n,e=!1){if(!(de&&n.domObserver.flushingSoon>=0)){if(n.domObserver.forceFlush(),uo(n),e||n.docView&&n.docView.dirty){let t=Or(n);return t&&!t.eq(n.state.selection)?n.dispatch(n.state.tr.setSelection(t)):n.updateState(n.state),!0}return!1}}function Sc(n,e){if(!n.dom.parentNode)return;let t=n.dom.parentNode.appendChild(document.createElement("div"));t.appendChild(e),t.style.cssText="position: fixed; left: -10000px; top: 10px";let r=getSelection(),i=document.createRange();i.selectNodeContents(e),n.dom.blur(),r.removeAllRanges(),r.addRange(i),setTimeout(()=>{t.parentNode&&t.parentNode.removeChild(t),n.focus()},50)}const Bt=ie&&ze<15||bt&&ka<604;ne.copy=re.cut=(n,e)=>{let t=e,r=n.state.selection,i=t.type=="cut";if(r.empty)return;let s=Bt?null:t.clipboardData,o=r.content(),{dom:l,text:a}=eo(n,o);s?(t.preventDefault(),s.clearData(),s.setData("text/html",l.innerHTML),s.setData("text/plain",a)):Sc(n,l),i&&n.dispatch(n.state.tr.deleteSelection().scrollIntoView().setMeta("uiEvent","cut"))};function Mc(n){return n.openStart==0&&n.openEnd==0&&n.content.childCount==1?n.content.firstChild:null}function Cc(n,e){if(!n.dom.parentNode)return;let t=n.input.shiftKey||n.state.selection.$from.parent.type.spec.code,r=n.dom.parentNode.appendChild(document.createElement(t?"textarea":"div"));t||(r.contentEditable="true"),r.style.cssText="position: fixed; left: -10000px; top: 10px",r.focus();let i=n.input.shiftKey&&n.input.lastKeyCode!=45;setTimeout(()=>{n.focus(),r.parentNode&&r.parentNode.removeChild(r),t?zt(n,r.value,null,i,e):zt(n,r.textContent,r.innerHTML,i,e)},50)}function zt(n,e,t,r,i){let s=to(n,e,t,r,n.state.selection.$from);if(n.someProp("handlePaste",a=>a(n,i,s||x.empty)))return!0;if(!s)return!1;let o=Mc(s),l=o?n.state.tr.replaceSelectionWith(o,r):n.state.tr.replaceSelection(s);return n.dispatch(l.scrollIntoView().setMeta("paste",!0).setMeta("uiEvent","paste")),!0}function fo(n){let e=n.getData("text/plain")||n.getData("Text");if(e)return e;let t=n.getData("text/uri-list");return t?t.replace(/\r?\n/g," "):""}re.paste=(n,e)=>{let t=e;if(n.composing&&!de)return;let r=Bt?null:t.clipboardData,i=n.input.shiftKey&&n.input.lastKeyCode!=45;r&&zt(n,fo(r),r.getData("text/html"),i,t)?t.preventDefault():Cc(n,t)};class ho{constructor(e,t,r){this.slice=e,this.move=t,this.node=r}}const po=ae?"altKey":"ctrlKey";ne.dragstart=(n,e)=>{let t=e,r=n.input.mouseDown;if(r&&r.done(),!t.dataTransfer)return;let i=n.state.selection,s=i.empty?null:n.posAtCoords(Cn(t)),o;if(!(s&&s.pos>=i.from&&s.pos<=(i instanceof w?i.to-1:i.to))){if(r&&r.mightDrag)o=w.create(n.state.doc,r.mightDrag.pos);else if(t.target&&t.target.nodeType==1){let d=n.docView.nearestDesc(t.target,!0);d&&d.node.type.spec.draggable&&d!=n.docView&&(o=w.create(n.state.doc,d.posBefore))}}let l=(o||n.state.selection).content(),{dom:a,text:c,slice:u}=eo(n,l);t.dataTransfer.clearData(),t.dataTransfer.setData(Bt?"Text":"text/html",a.innerHTML),t.dataTransfer.effectAllowed="copyMove",Bt||t.dataTransfer.setData("text/plain",c),n.dragging=new ho(u,!t[po],o)};ne.dragend=n=>{let e=n.dragging;window.setTimeout(()=>{n.dragging==e&&(n.dragging=null)},50)};re.dragover=re.dragenter=(n,e)=>e.preventDefault();re.drop=(n,e)=>{let t=e,r=n.dragging;if(n.dragging=null,!t.dataTransfer)return;let i=n.posAtCoords(Cn(t));if(!i)return;let s=n.state.doc.resolve(i.pos),o=r&&r.slice;o?n.someProp("transformPasted",p=>{o=p(o,n)}):o=to(n,fo(t.dataTransfer),Bt?null:t.dataTransfer.getData("text/html"),!1,s);let l=!!(r&&!t[po]);if(n.someProp("handleDrop",p=>p(n,t,o||x.empty,l))){t.preventDefault();return}if(!o)return;t.preventDefault();let a=o?Ds(n.state.doc,s.pos,o):s.pos;a==null&&(a=s.pos);let c=n.state.tr;if(l){let{node:p}=r;p?p.replace(c):c.deleteSelection()}let u=c.mapping.map(a),d=o.openStart==0&&o.openEnd==0&&o.content.childCount==1,f=c.doc;if(d?c.replaceRangeWith(u,u,o.content.firstChild):c.replaceRange(u,u,o),c.doc.eq(f))return;let h=c.doc.resolve(u);if(d&&w.isSelectable(o.content.firstChild)&&h.nodeAfter&&h.nodeAfter.sameMarkup(o.content.firstChild))c.setSelection(new w(h));else{let p=c.mapping.map(a);c.mapping.maps[c.mapping.maps.length-1].forEach((m,g,y,b)=>p=b),c.setSelection(vr(n,h,c.doc.resolve(p)))}n.focus(),n.dispatch(c.setMeta("uiEvent","drop"))};ne.focus=n=>{n.input.lastFocus=Date.now(),n.focused||(n.domObserver.stop(),n.dom.classList.add("ProseMirror-focused"),n.domObserver.start(),n.focused=!0,setTimeout(()=>{n.docView&&n.hasFocus()&&!n.domObserver.currentSelection.eq(n.domSelectionRange())&&Te(n)},20))};ne.blur=(n,e)=>{let t=e;n.focused&&(n.domObserver.stop(),n.dom.classList.remove("ProseMirror-focused"),n.domObserver.start(),t.relatedTarget&&n.dom.contains(t.relatedTarget)&&n.domObserver.currentSelection.clear(),n.focused=!1)};ne.beforeinput=(n,e)=>{if(ee&&de&&e.inputType=="deleteContentBackward"){n.domObserver.flushSoon();let{domChangeCount:r}=n.input;setTimeout(()=>{if(n.input.domChangeCount!=r||(n.dom.blur(),n.focus(),n.someProp("handleKeyDown",s=>s(n,Ke(8,"Backspace")))))return;let{$cursor:i}=n.state.selection;i&&i.pos>0&&n.dispatch(n.state.tr.delete(i.pos-1,i.pos).scrollIntoView())},50)}};for(let n in re)ne[n]=re[n];function Lt(n,e){if(n==e)return!0;for(let t in n)if(n[t]!==e[t])return!1;for(let t in e)if(!(t in n))return!1;return!0}class cn{constructor(e,t){this.toDOM=e,this.spec=t||et,this.side=this.spec.side||0}map(e,t,r,i){let{pos:s,deleted:o}=e.mapResult(t.from+i,this.side<0?-1:1);return o?null:new oe(s-r,s-r,this)}valid(){return!0}eq(e){return this==e||e instanceof cn&&(this.spec.key&&this.spec.key==e.spec.key||this.toDOM==e.toDOM&&Lt(this.spec,e.spec))}destroy(e){this.spec.destroy&&this.spec.destroy(e)}}class Fe{constructor(e,t){this.attrs=e,this.spec=t||et}map(e,t,r,i){let s=e.map(t.from+i,this.spec.inclusiveStart?-1:1)-r,o=e.map(t.to+i,this.spec.inclusiveEnd?1:-1)-r;return s>=o?null:new oe(s,o,this)}valid(e,t){return t.from<t.to}eq(e){return this==e||e instanceof Fe&&Lt(this.attrs,e.attrs)&&Lt(this.spec,e.spec)}static is(e){return e.type instanceof Fe}destroy(){}}class Dr{constructor(e,t){this.attrs=e,this.spec=t||et}map(e,t,r,i){let s=e.mapResult(t.from+i,1);if(s.deleted)return null;let o=e.mapResult(t.to+i,-1);return o.deleted||o.pos<=s.pos?null:new oe(s.pos-r,o.pos-r,this)}valid(e,t){let{index:r,offset:i}=e.content.findIndex(t.from),s;return i==t.from&&!(s=e.child(r)).isText&&i+s.nodeSize==t.to}eq(e){return this==e||e instanceof Dr&&Lt(this.attrs,e.attrs)&&Lt(this.spec,e.spec)}destroy(){}}class oe{constructor(e,t,r){this.from=e,this.to=t,this.type=r}copy(e,t){return new oe(e,t,this.type)}eq(e,t=0){return this.type.eq(e.type)&&this.from+t==e.from&&this.to+t==e.to}map(e,t,r){return this.type.map(e,this,t,r)}static widget(e,t,r){return new oe(e,e,new cn(t,r))}static inline(e,t,r,i){return new oe(e,t,new Fe(r,i))}static node(e,t,r,i){return new oe(e,t,new Dr(r,i))}get spec(){return this.type.spec}get inline(){return this.type instanceof Fe}get widget(){return this.type instanceof cn}}const ct=[],et={};class V{constructor(e,t){this.local=e.length?e:ct,this.children=t.length?t:ct}static create(e,t){return t.length?un(t,e,0,et):X}find(e,t,r){let i=[];return this.findInner(e??0,t??1e9,i,0,r),i}findInner(e,t,r,i,s){for(let o=0;o<this.local.length;o++){let l=this.local[o];l.from<=t&&l.to>=e&&(!s||s(l.spec))&&r.push(l.copy(l.from+i,l.to+i))}for(let o=0;o<this.children.length;o+=3)if(this.children[o]<t&&this.children[o+1]>e){let l=this.children[o]+1;this.children[o+2].findInner(e-l,t-l,r,i+l,s)}}map(e,t,r){return this==X||e.maps.length==0?this:this.mapInner(e,t,0,0,r||et)}mapInner(e,t,r,i,s){let o;for(let l=0;l<this.local.length;l++){let a=this.local[l].map(e,r,i);a&&a.type.valid(t,a)?(o||(o=[])).push(a):s.onRemove&&s.onRemove(this.local[l].spec)}return this.children.length?wc(this.children,o||[],e,t,r,i,s):o?new V(o.sort(tt),ct):X}add(e,t){return t.length?this==X?V.create(e,t):this.addInner(e,t,0):this}addInner(e,t,r){let i,s=0;e.forEach((l,a)=>{let c=a+r,u;if(u=go(t,l,c)){for(i||(i=this.children.slice());s<i.length&&i[s]<a;)s+=3;i[s]==a?i[s+2]=i[s+2].addInner(l,u,c+1):i.splice(s,0,a,a+l.nodeSize,un(u,l,c+1,et)),s+=3}});let o=mo(s?yo(t):t,-r);for(let l=0;l<o.length;l++)o[l].type.valid(e,o[l])||o.splice(l--,1);return new V(o.length?this.local.concat(o).sort(tt):this.local,i||this.children)}remove(e){return e.length==0||this==X?this:this.removeInner(e,0)}removeInner(e,t){let r=this.children,i=this.local;for(let s=0;s<r.length;s+=3){let o,l=r[s]+t,a=r[s+1]+t;for(let u=0,d;u<e.length;u++)(d=e[u])&&d.from>l&&d.to<a&&(e[u]=null,(o||(o=[])).push(d));if(!o)continue;r==this.children&&(r=this.children.slice());let c=r[s+2].removeInner(o,l+1);c!=X?r[s+2]=c:(r.splice(s,3),s-=3)}if(i.length){for(let s=0,o;s<e.length;s++)if(o=e[s])for(let l=0;l<i.length;l++)i[l].eq(o,t)&&(i==this.local&&(i=this.local.slice()),i.splice(l--,1))}return r==this.children&&i==this.local?this:i.length||r.length?new V(i,r):X}forChild(e,t){if(this==X)return this;if(t.isLeaf)return V.empty;let r,i;for(let l=0;l<this.children.length;l+=3)if(this.children[l]>=e){this.children[l]==e&&(r=this.children[l+2]);break}let s=e+1,o=s+t.content.size;for(let l=0;l<this.local.length;l++){let a=this.local[l];if(a.from<o&&a.to>s&&a.type instanceof Fe){let c=Math.max(s,a.from)-s,u=Math.min(o,a.to)-s;c<u&&(i||(i=[])).push(a.copy(c,u))}}if(i){let l=new V(i.sort(tt),ct);return r?new De([l,r]):l}return r||X}eq(e){if(this==e)return!0;if(!(e instanceof V)||this.local.length!=e.local.length||this.children.length!=e.children.length)return!1;for(let t=0;t<this.local.length;t++)if(!this.local[t].eq(e.local[t]))return!1;for(let t=0;t<this.children.length;t+=3)if(this.children[t]!=e.children[t]||this.children[t+1]!=e.children[t+1]||!this.children[t+2].eq(e.children[t+2]))return!1;return!0}locals(e){return Ir(this.localsInner(e))}localsInner(e){if(this==X)return ct;if(e.inlineContent||!this.local.some(Fe.is))return this.local;let t=[];for(let r=0;r<this.local.length;r++)this.local[r].type instanceof Fe||t.push(this.local[r]);return t}}V.empty=new V([],[]);V.removeOverlap=Ir;const X=V.empty;class De{constructor(e){this.members=e}map(e,t){const r=this.members.map(i=>i.map(e,t,et));return De.from(r)}forChild(e,t){if(t.isLeaf)return V.empty;let r=[];for(let i=0;i<this.members.length;i++){let s=this.members[i].forChild(e,t);s!=X&&(s instanceof De?r=r.concat(s.members):r.push(s))}return De.from(r)}eq(e){if(!(e instanceof De)||e.members.length!=this.members.length)return!1;for(let t=0;t<this.members.length;t++)if(!this.members[t].eq(e.members[t]))return!1;return!0}locals(e){let t,r=!0;for(let i=0;i<this.members.length;i++){let s=this.members[i].localsInner(e);if(s.length)if(!t)t=s;else{r&&(t=t.slice(),r=!1);for(let o=0;o<s.length;o++)t.push(s[o])}}return t?Ir(r?t:t.sort(tt)):ct}static from(e){switch(e.length){case 0:return X;case 1:return e[0];default:return new De(e.every(t=>t instanceof V)?e:e.reduce((t,r)=>t.concat(r instanceof V?r:r.members),[]))}}}function wc(n,e,t,r,i,s,o){let l=n.slice();for(let c=0,u=s;c<t.maps.length;c++){let d=0;t.maps[c].forEach((f,h,p,m)=>{let g=m-p-(h-f);for(let y=0;y<l.length;y+=3){let b=l[y+1];if(b<0||f>b+u-d)continue;let M=l[y]+u-d;h>=M?l[y+1]=f<=M?-2:-1:f>=u&&g&&(l[y]+=g,l[y+1]+=g)}d+=g}),u=t.maps[c].map(u,-1)}let a=!1;for(let c=0;c<l.length;c+=3)if(l[c+1]<0){if(l[c+1]==-2){a=!0,l[c+1]=-1;continue}let u=t.map(n[c]+s),d=u-i;if(d<0||d>=r.content.size){a=!0;continue}let f=t.map(n[c+1]+s,-1),h=f-i,{index:p,offset:m}=r.content.findIndex(d),g=r.maybeChild(p);if(g&&m==d&&m+g.nodeSize==h){let y=l[c+2].mapInner(t,g,u+1,n[c]+s+1,o);y!=X?(l[c]=d,l[c+1]=h,l[c+2]=y):(l[c+1]=-2,a=!0)}else a=!0}if(a){let c=Tc(l,n,e,t,i,s,o),u=un(c,r,0,o);e=u.local;for(let d=0;d<l.length;d+=3)l[d+1]<0&&(l.splice(d,3),d-=3);for(let d=0,f=0;d<u.children.length;d+=3){let h=u.children[d];for(;f<l.length&&l[f]<h;)f+=3;l.splice(f,0,u.children[d],u.children[d+1],u.children[d+2])}}return new V(e.sort(tt),l)}function mo(n,e){if(!e||!n.length)return n;let t=[];for(let r=0;r<n.length;r++){let i=n[r];t.push(new oe(i.from+e,i.to+e,i.type))}return t}function Tc(n,e,t,r,i,s,o){function l(a,c){for(let u=0;u<a.local.length;u++){let d=a.local[u].map(r,i,c);d?t.push(d):o.onRemove&&o.onRemove(a.local[u].spec)}for(let u=0;u<a.children.length;u+=3)l(a.children[u+2],a.children[u]+c+1)}for(let a=0;a<n.length;a+=3)n[a+1]==-1&&l(n[a+2],e[a]+s+1);return t}function go(n,e,t){if(e.isLeaf)return null;let r=t+e.nodeSize,i=null;for(let s=0,o;s<n.length;s++)(o=n[s])&&o.from>t&&o.to<r&&((i||(i=[])).push(o),n[s]=null);return i}function yo(n){let e=[];for(let t=0;t<n.length;t++)n[t]!=null&&e.push(n[t]);return e}function un(n,e,t,r){let i=[],s=!1;e.forEach((l,a)=>{let c=go(n,l,a+t);if(c){s=!0;let u=un(c,l,t+a+1,r);u!=X&&i.push(a,a+l.nodeSize,u)}});let o=mo(s?yo(n):n,-t).sort(tt);for(let l=0;l<o.length;l++)o[l].type.valid(e,o[l])||(r.onRemove&&r.onRemove(o[l].spec),o.splice(l--,1));return o.length||i.length?new V(o,i):X}function tt(n,e){return n.from-e.from||n.to-e.to}function Ir(n){let e=n;for(let t=0;t<e.length-1;t++){let r=e[t];if(r.from!=r.to)for(let i=t+1;i<e.length;i++){let s=e[i];if(s.from==r.from){s.to!=r.to&&(e==n&&(e=n.slice()),e[i]=s.copy(s.from,r.to),vi(e,i+1,s.copy(r.to,s.to)));continue}else{s.from<r.to&&(e==n&&(e=n.slice()),e[t]=r.copy(r.from,s.from),vi(e,i,r.copy(s.from,r.to)));break}}}return e}function vi(n,e,t){for(;e<n.length&&tt(t,n[e])>0;)e++;n.splice(e,0,t)}function qn(n){let e=[];return n.someProp("decorations",t=>{let r=t(n.state);r&&r!=X&&e.push(r)}),n.cursorWrapper&&e.push(V.create(n.state.doc,[n.cursorWrapper.deco])),De.from(e)}const Oc={childList:!0,characterData:!0,characterDataOldValue:!0,attributes:!0,attributeOldValue:!0,subtree:!0},vc=ie&&ze<=11;class Nc{constructor(){this.anchorNode=null,this.anchorOffset=0,this.focusNode=null,this.focusOffset=0}set(e){this.anchorNode=e.anchorNode,this.anchorOffset=e.anchorOffset,this.focusNode=e.focusNode,this.focusOffset=e.focusOffset}clear(){this.anchorNode=this.focusNode=null}eq(e){return e.anchorNode==this.anchorNode&&e.anchorOffset==this.anchorOffset&&e.focusNode==this.focusNode&&e.focusOffset==this.focusOffset}}class Ec{constructor(e,t){this.view=e,this.handleDOMChange=t,this.queue=[],this.flushingSoon=-1,this.observer=null,this.currentSelection=new Nc,this.onCharData=null,this.suppressingSelectionUpdates=!1,this.lastChangedTextNode=null,this.observer=window.MutationObserver&&new window.MutationObserver(r=>{for(let i=0;i<r.length;i++)this.queue.push(r[i]);ie&&ze<=11&&r.some(i=>i.type=="childList"&&i.removedNodes.length||i.type=="characterData"&&i.oldValue.length>i.target.nodeValue.length)?this.flushSoon():this.flush()}),vc&&(this.onCharData=r=>{this.queue.push({target:r.target,type:"characterData",oldValue:r.prevValue}),this.flushSoon()}),this.onSelectionChange=this.onSelectionChange.bind(this)}flushSoon(){this.flushingSoon<0&&(this.flushingSoon=window.setTimeout(()=>{this.flushingSoon=-1,this.flush()},20))}forceFlush(){this.flushingSoon>-1&&(window.clearTimeout(this.flushingSoon),this.flushingSoon=-1,this.flush())}start(){this.observer&&(this.observer.takeRecords(),this.observer.observe(this.view.dom,Oc)),this.onCharData&&this.view.dom.addEventListener("DOMCharacterDataModified",this.onCharData),this.connectSelection()}stop(){if(this.observer){let e=this.observer.takeRecords();if(e.length){for(let t=0;t<e.length;t++)this.queue.push(e[t]);window.setTimeout(()=>this.flush(),20)}this.observer.disconnect()}this.onCharData&&this.view.dom.removeEventListener("DOMCharacterDataModified",this.onCharData),this.disconnectSelection()}connectSelection(){this.view.dom.ownerDocument.addEventListener("selectionchange",this.onSelectionChange)}disconnectSelection(){this.view.dom.ownerDocument.removeEventListener("selectionchange",this.onSelectionChange)}suppressSelectionUpdates(){this.suppressingSelectionUpdates=!0,setTimeout(()=>this.suppressingSelectionUpdates=!1,50)}onSelectionChange(){if(bi(this.view)){if(this.suppressingSelectionUpdates)return Te(this.view);if(ie&&ze<=11&&!this.view.state.selection.empty){let e=this.view.domSelectionRange();if(e.focusNode&&it(e.focusNode,e.focusOffset,e.anchorNode,e.anchorOffset))return this.flushSoon()}this.flush()}}setCurSelection(){this.currentSelection.set(this.view.domSelectionRange())}ignoreSelectionChange(e){if(!e.focusNode)return!0;let t=new Set,r;for(let s=e.focusNode;s;s=Pt(s))t.add(s);for(let s=e.anchorNode;s;s=Pt(s))if(t.has(s)){r=s;break}let i=r&&this.view.docView.nearestDesc(r);if(i&&i.ignoreMutation({type:"selection",target:r.nodeType==3?r.parentNode:r}))return this.setCurSelection(),!0}pendingRecords(){if(this.observer)for(let e of this.observer.takeRecords())this.queue.push(e);return this.queue}flush(){let{view:e}=this;if(!e.docView||this.flushingSoon>-1)return;let t=this.pendingRecords();t.length&&(this.queue=[]);let r=e.domSelectionRange(),i=!this.suppressingSelectionUpdates&&!this.currentSelection.eq(r)&&bi(e)&&!this.ignoreSelectionChange(r),s=-1,o=-1,l=!1,a=[];if(e.editable)for(let u=0;u<t.length;u++){let d=this.registerMutation(t[u],a);d&&(s=s<0?d.from:Math.min(d.from,s),o=o<0?d.to:Math.max(d.to,o),d.typeOver&&(l=!0))}if(pe&&a.length){let u=a.filter(d=>d.nodeName=="BR");if(u.length==2){let[d,f]=u;d.parentNode&&d.parentNode.parentNode==f.parentNode?f.remove():d.remove()}else{let{focusNode:d}=this.currentSelection;for(let f of u){let h=f.parentNode;h&&h.nodeName=="LI"&&(!d||Ic(e,d)!=h)&&f.remove()}}}let c=null;s<0&&i&&e.input.lastFocus>Date.now()-200&&Math.max(e.input.lastTouch,e.input.lastClick.time)<Date.now()-300&&Sn(r)&&(c=Or(e))&&c.eq(E.near(e.state.doc.resolve(0),1))?(e.input.lastFocus=0,Te(e),this.currentSelection.set(r),e.scrollToSelection()):(s>-1||i)&&(s>-1&&(e.docView.markDirty(s,o),Ac(e)),this.handleDOMChange(s,o,l,a),e.docView&&e.docView.dirty?e.updateState(e.state):this.currentSelection.eq(r)||Te(e),this.currentSelection.set(r))}registerMutation(e,t){if(t.indexOf(e.target)>-1)return null;let r=this.view.docView.nearestDesc(e.target);if(e.type=="attributes"&&(r==this.view.docView||e.attributeName=="contenteditable"||e.attributeName=="style"&&!e.oldValue&&!e.target.getAttribute("style"))||!r||r.ignoreMutation(e))return null;if(e.type=="childList"){for(let u=0;u<e.addedNodes.length;u++){let d=e.addedNodes[u];t.push(d),d.nodeType==3&&(this.lastChangedTextNode=d)}if(r.contentDOM&&r.contentDOM!=r.dom&&!r.contentDOM.contains(e.target))return{from:r.posBefore,to:r.posAfter};let i=e.previousSibling,s=e.nextSibling;if(ie&&ze<=11&&e.addedNodes.length)for(let u=0;u<e.addedNodes.length;u++){let{previousSibling:d,nextSibling:f}=e.addedNodes[u];(!d||Array.prototype.indexOf.call(e.addedNodes,d)<0)&&(i=d),(!f||Array.prototype.indexOf.call(e.addedNodes,f)<0)&&(s=f)}let o=i&&i.parentNode==e.target?G(i)+1:0,l=r.localPosFromDOM(e.target,o,-1),a=s&&s.parentNode==e.target?G(s):e.target.childNodes.length,c=r.localPosFromDOM(e.target,a,1);return{from:l,to:c}}else return e.type=="attributes"?{from:r.posAtStart-r.border,to:r.posAtEnd+r.border}:(this.lastChangedTextNode=e.target,{from:r.posAtStart,to:r.posAtEnd,typeOver:e.target.nodeValue==e.oldValue})}}let Ni=new WeakMap,Ei=!1;function Ac(n){if(!Ni.has(n)&&(Ni.set(n,null),["normal","nowrap","pre-line"].indexOf(getComputedStyle(n.dom).whiteSpace)!==-1)){if(n.requiresGeckoHackNode=pe,Ei)return;console.warn("ProseMirror expects the CSS white-space property to be set, preferably to 'pre-wrap'. It is recommended to load style/prosemirror.css from the prosemirror-view package."),Ei=!0}}function Ai(n,e){let t=e.startContainer,r=e.startOffset,i=e.endContainer,s=e.endOffset,o=n.domAtPos(n.state.selection.anchor);return it(o.node,o.offset,i,s)&&([t,r,i,s]=[i,s,t,r]),{anchorNode:t,anchorOffset:r,focusNode:i,focusOffset:s}}function Dc(n,e){if(e.getComposedRanges){let i=e.getComposedRanges(n.root)[0];if(i)return Ai(n,i)}let t;function r(i){i.preventDefault(),i.stopImmediatePropagation(),t=i.getTargetRanges()[0]}return n.dom.addEventListener("beforeinput",r,!0),document.execCommand("indent"),n.dom.removeEventListener("beforeinput",r,!0),t?Ai(n,t):null}function Ic(n,e){for(let t=e.parentNode;t&&t!=n.dom;t=t.parentNode){let r=n.docView.nearestDesc(t,!0);if(r&&r.node.isBlock)return t}return null}function Rc(n,e,t){let{node:r,fromOffset:i,toOffset:s,from:o,to:l}=n.docView.parseRange(e,t),a=n.domSelectionRange(),c,u=a.anchorNode;if(u&&n.dom.contains(u.nodeType==1?u:u.parentNode)&&(c=[{node:u,offset:a.anchorOffset}],Sn(a)||c.push({node:a.focusNode,offset:a.focusOffset})),ee&&n.input.lastKeyCode===8)for(let g=s;g>i;g--){let y=r.childNodes[g-1],b=y.pmViewDesc;if(y.nodeName=="BR"&&!b){s=g;break}if(!b||b.size)break}let d=n.state.doc,f=n.someProp("domParser")||gt.fromSchema(n.state.schema),h=d.resolve(o),p=null,m=f.parse(r,{topNode:h.parent,topMatch:h.parent.contentMatchAt(h.index()),topOpen:!0,from:i,to:s,preserveWhitespace:h.parent.type.whitespace=="pre"?"full":!0,findPositions:c,ruleFromNode:Pc,context:h});if(c&&c[0].pos!=null){let g=c[0].pos,y=c[1]&&c[1].pos;y==null&&(y=g),p={anchor:g+o,head:y+o}}return{doc:m,sel:p,from:o,to:l}}function Pc(n){let e=n.pmViewDesc;if(e)return e.parseRule();if(n.nodeName=="BR"&&n.parentNode){if(te&&/^(ul|ol)$/i.test(n.parentNode.nodeName)){let t=document.createElement("div");return t.appendChild(document.createElement("li")),{skip:t}}else if(n.parentNode.lastChild==n||te&&/^(tr|table)$/i.test(n.parentNode.nodeName))return{ignore:!0}}else if(n.nodeName=="IMG"&&n.getAttribute("mark-placeholder"))return{ignore:!0};return null}const Bc=/^(a|abbr|acronym|b|bd[io]|big|br|button|cite|code|data(list)?|del|dfn|em|i|ins|kbd|label|map|mark|meter|output|q|ruby|s|samp|small|span|strong|su[bp]|time|u|tt|var)$/i;function zc(n,e,t,r,i){let s=n.input.compositionPendingChanges||(n.composing?n.input.compositionID:0);if(n.input.compositionPendingChanges=0,e<0){let T=n.input.lastSelectionTime>Date.now()-50?n.input.lastSelectionOrigin:null,F=Or(n,T);if(F&&!n.state.selection.eq(F)){if(ee&&de&&n.input.lastKeyCode===13&&Date.now()-100<n.input.lastKeyCodeTime&&n.someProp("handleKeyDown",D=>D(n,Ke(13,"Enter"))))return;let P=n.state.tr.setSelection(F);T=="pointer"?P.setMeta("pointer",!0):T=="key"&&P.scrollIntoView(),s&&P.setMeta("composition",s),n.dispatch(P)}return}let o=n.state.doc.resolve(e),l=o.sharedDepth(t);e=o.before(l+1),t=n.state.doc.resolve(t).after(l+1);let a=n.state.selection,c=Rc(n,e,t),u=n.state.doc,d=u.slice(c.from,c.to),f,h;n.input.lastKeyCode===8&&Date.now()-100<n.input.lastKeyCodeTime?(f=n.state.selection.to,h="end"):(f=n.state.selection.from,h="start"),n.input.lastKeyCode=null;let p=Vc(d.content,c.doc.content,c.from,f,h);if((bt&&n.input.lastIOSEnter>Date.now()-225||de)&&i.some(T=>T.nodeType==1&&!Bc.test(T.nodeName))&&(!p||p.endA>=p.endB)&&n.someProp("handleKeyDown",T=>T(n,Ke(13,"Enter")))){n.input.lastIOSEnter=0;return}if(!p)if(r&&a instanceof O&&!a.empty&&a.$head.sameParent(a.$anchor)&&!n.composing&&!(c.sel&&c.sel.anchor!=c.sel.head))p={start:a.from,endA:a.to,endB:a.to};else{if(c.sel){let T=Di(n,n.state.doc,c.sel);if(T&&!T.eq(n.state.selection)){let F=n.state.tr.setSelection(T);s&&F.setMeta("composition",s),n.dispatch(F)}}return}n.input.domChangeCount++,n.state.selection.from<n.state.selection.to&&p.start==p.endB&&n.state.selection instanceof O&&(p.start>n.state.selection.from&&p.start<=n.state.selection.from+2&&n.state.selection.from>=c.from?p.start=n.state.selection.from:p.endA<n.state.selection.to&&p.endA>=n.state.selection.to-2&&n.state.selection.to<=c.to&&(p.endB+=n.state.selection.to-p.endA,p.endA=n.state.selection.to)),ie&&ze<=11&&p.endB==p.start+1&&p.endA==p.start&&p.start>c.from&&c.doc.textBetween(p.start-c.from-1,p.start-c.from+1)=="  "&&(p.start--,p.endA--,p.endB--);let m=c.doc.resolveNoCache(p.start-c.from),g=c.doc.resolveNoCache(p.endB-c.from),y=u.resolve(p.start),b=m.sameParent(g)&&m.parent.inlineContent&&y.end()>=p.endA,M;if((bt&&n.input.lastIOSEnter>Date.now()-225&&(!b||i.some(T=>T.nodeName=="DIV"||T.nodeName=="P"))||!b&&m.pos<c.doc.content.size&&!m.sameParent(g)&&(M=E.findFrom(c.doc.resolve(m.pos+1),1,!0))&&M.head==g.pos)&&n.someProp("handleKeyDown",T=>T(n,Ke(13,"Enter")))){n.input.lastIOSEnter=0;return}if(n.state.selection.anchor>p.start&&Fc(u,p.start,p.endA,m,g)&&n.someProp("handleKeyDown",T=>T(n,Ke(8,"Backspace")))){de&&ee&&n.domObserver.suppressSelectionUpdates();return}ee&&de&&p.endB==p.start&&(n.input.lastAndroidDelete=Date.now()),de&&!b&&m.start()!=g.start()&&g.parentOffset==0&&m.depth==g.depth&&c.sel&&c.sel.anchor==c.sel.head&&c.sel.head==p.endA&&(p.endB-=2,g=c.doc.resolveNoCache(p.endB-c.from),setTimeout(()=>{n.someProp("handleKeyDown",function(T){return T(n,Ke(13,"Enter"))})},20));let C=p.start,A=p.endA,v,R,z;if(b){if(m.pos==g.pos)ie&&ze<=11&&m.parentOffset==0&&(n.domObserver.suppressSelectionUpdates(),setTimeout(()=>Te(n),20)),v=n.state.tr.delete(C,A),R=u.resolve(p.start).marksAcross(u.resolve(p.endA));else if(p.endA==p.endB&&(z=Lc(m.parent.content.cut(m.parentOffset,g.parentOffset),y.parent.content.cut(y.parentOffset,p.endA-y.start()))))v=n.state.tr,z.type=="add"?v.addMark(C,A,z.mark):v.removeMark(C,A,z.mark);else if(m.parent.child(m.index()).isText&&m.index()==g.index()-(g.textOffset?0:1)){let T=m.parent.textBetween(m.parentOffset,g.parentOffset);if(n.someProp("handleTextInput",F=>F(n,C,A,T)))return;v=n.state.tr.insertText(T,C,A)}}if(v||(v=n.state.tr.replace(C,A,c.doc.slice(p.start-c.from,p.endB-c.from))),c.sel){let T=Di(n,v.doc,c.sel);T&&!(ee&&de&&n.composing&&T.empty&&(p.start!=p.endB||n.input.lastAndroidDelete<Date.now()-100)&&(T.head==C||T.head==v.mapping.map(A)-1)||ie&&T.empty&&T.head==C)&&v.setSelection(T)}R&&v.ensureMarks(R),s&&v.setMeta("composition",s),n.dispatch(v.scrollIntoView())}function Di(n,e,t){return Math.max(t.anchor,t.head)>e.content.size?null:vr(n,e.resolve(t.anchor),e.resolve(t.head))}function Lc(n,e){let t=n.firstChild.marks,r=e.firstChild.marks,i=t,s=r,o,l,a;for(let u=0;u<r.length;u++)i=r[u].removeFromSet(i);for(let u=0;u<t.length;u++)s=t[u].removeFromSet(s);if(i.length==1&&s.length==0)l=i[0],o="add",a=u=>u.mark(l.addToSet(u.marks));else if(i.length==0&&s.length==1)l=s[0],o="remove",a=u=>u.mark(l.removeFromSet(u.marks));else return null;let c=[];for(let u=0;u<e.childCount;u++)c.push(a(e.child(u)));if(k.from(c).eq(n))return{mark:l,type:o}}function Fc(n,e,t,r,i){if(t-e<=i.pos-r.pos||Un(r,!0,!1)<i.pos)return!1;let s=n.resolve(e);if(!r.parent.isTextblock){let l=s.nodeAfter;return l!=null&&t==e+l.nodeSize}if(s.parentOffset<s.parent.content.size||!s.parent.isTextblock)return!1;let o=n.resolve(Un(s,!0,!0));return!o.parent.isTextblock||o.pos>t||Un(o,!0,!1)<t?!1:r.parent.content.cut(r.parentOffset).eq(o.parent.content)}function Un(n,e,t){let r=n.depth,i=e?n.end():n.pos;for(;r>0&&(e||n.indexAfter(r)==n.node(r).childCount);)r--,i++,e=!1;if(t){let s=n.node(r).maybeChild(n.indexAfter(r));for(;s&&!s.isLeaf;)s=s.firstChild,i++}return i}function Vc(n,e,t,r,i){let s=n.findDiffStart(e,t);if(s==null)return null;let{a:o,b:l}=n.findDiffEnd(e,t+n.size,t+e.size);if(i=="end"){let a=Math.max(0,s-Math.min(o,l));r-=o+a-s}if(o<s&&n.size<e.size){let a=r<=s&&r>=o?s-r:0;s-=a,s&&s<e.size&&Ii(e.textBetween(s-1,s+1))&&(s+=a?1:-1),l=s+(l-o),o=s}else if(l<s){let a=r<=s&&r>=l?s-r:0;s-=a,s&&s<n.size&&Ii(n.textBetween(s-1,s+1))&&(s+=a?1:-1),o=s+(o-l),l=s}return{start:s,endA:o,endB:l}}function Ii(n){if(n.length!=2)return!1;let e=n.charCodeAt(0),t=n.charCodeAt(1);return e>=56320&&e<=57343&&t>=55296&&t<=56319}class $c{constructor(e,t){this._root=null,this.focused=!1,this.trackWrites=null,this.mounted=!1,this.markCursor=null,this.cursorWrapper=null,this.lastSelectedViewDesc=void 0,this.input=new sc,this.prevDirectPlugins=[],this.pluginViews=[],this.requiresGeckoHackNode=!1,this.dragging=null,this._props=t,this.state=t.state,this.directPlugins=t.plugins||[],this.directPlugins.forEach(Li),this.dispatch=this.dispatch.bind(this),this.dom=e&&e.mount||document.createElement("div"),e&&(e.appendChild?e.appendChild(this.dom):typeof e=="function"?e(this.dom):e.mount&&(this.mounted=!0)),this.editable=Bi(this),Pi(this),this.nodeViews=zi(this),this.docView=hi(this.state.doc,Ri(this),qn(this),this.dom,this),this.domObserver=new Ec(this,(r,i,s,o)=>zc(this,r,i,s,o)),this.domObserver.start(),oc(this),this.updatePluginViews()}get composing(){return this.input.composing}get props(){if(this._props.state!=this.state){let e=this._props;this._props={};for(let t in e)this._props[t]=e[t];this._props.state=this.state}return this._props}update(e){e.handleDOMEvents!=this._props.handleDOMEvents&&yr(this);let t=this._props;this._props=e,e.plugins&&(e.plugins.forEach(Li),this.directPlugins=e.plugins),this.updateStateInner(e.state,t)}setProps(e){let t={};for(let r in this._props)t[r]=this._props[r];t.state=this.state;for(let r in e)t[r]=e[r];this.update(t)}updateState(e){this.updateStateInner(e,this._props)}updateStateInner(e,t){var r;let i=this.state,s=!1,o=!1;e.storedMarks&&this.composing&&(uo(this),o=!0),this.state=e;let l=i.plugins!=e.plugins||this._props.plugins!=t.plugins;if(l||this._props.plugins!=t.plugins||this._props.nodeViews!=t.nodeViews){let h=zi(this);Wc(h,this.nodeViews)&&(this.nodeViews=h,s=!0)}(l||t.handleDOMEvents!=this._props.handleDOMEvents)&&yr(this),this.editable=Bi(this),Pi(this);let a=qn(this),c=Ri(this),u=i.plugins!=e.plugins&&!i.doc.eq(e.doc)?"reset":e.scrollToSelection>i.scrollToSelection?"to selection":"preserve",d=s||!this.docView.matchesNode(e.doc,c,a);(d||!e.selection.eq(i.selection))&&(o=!0);let f=u=="preserve"&&o&&this.dom.style.overflowAnchor==null&&Sa(this);if(o){this.domObserver.stop();let h=d&&(ie||ee)&&!this.composing&&!i.selection.empty&&!e.selection.empty&&Hc(i.selection,e.selection);if(d){let p=ee?this.trackWrites=this.domSelectionRange().focusNode:null;this.composing&&(this.input.compositionNode=bc(this)),(s||!this.docView.update(e.doc,c,a,this))&&(this.docView.updateOuterDeco(c),this.docView.destroy(),this.docView=hi(e.doc,c,a,this.dom,this)),p&&!this.trackWrites&&(h=!0)}h||!(this.input.mouseDown&&this.domObserver.currentSelection.eq(this.domSelectionRange())&&Ka(this))?Te(this,h):(Xs(this,e.selection),this.domObserver.setCurSelection()),this.domObserver.start()}this.updatePluginViews(i),!((r=this.dragging)===null||r===void 0)&&r.node&&!i.doc.eq(e.doc)&&this.updateDraggedNode(this.dragging,i),u=="reset"?this.dom.scrollTop=0:u=="to selection"?this.scrollToSelection():f&&Ma(f)}scrollToSelection(){let e=this.domSelectionRange().focusNode;if(!this.someProp("handleScrollToSelection",t=>t(this)))if(this.state.selection instanceof w){let t=this.docView.domAfterPos(this.state.selection.from);t.nodeType==1&&li(this,t.getBoundingClientRect(),e)}else li(this,this.coordsAtPos(this.state.selection.head,1),e)}destroyPluginViews(){let e;for(;e=this.pluginViews.pop();)e.destroy&&e.destroy()}updatePluginViews(e){if(!e||e.plugins!=this.state.plugins||this.directPlugins!=this.prevDirectPlugins){this.prevDirectPlugins=this.directPlugins,this.destroyPluginViews();for(let t=0;t<this.directPlugins.length;t++){let r=this.directPlugins[t];r.spec.view&&this.pluginViews.push(r.spec.view(this))}for(let t=0;t<this.state.plugins.length;t++){let r=this.state.plugins[t];r.spec.view&&this.pluginViews.push(r.spec.view(this))}}else for(let t=0;t<this.pluginViews.length;t++){let r=this.pluginViews[t];r.update&&r.update(this,e)}}updateDraggedNode(e,t){let r=e.node,i=-1;if(this.state.doc.nodeAt(r.from)==r.node)i=r.from;else{let s=r.from+(this.state.doc.content.size-t.doc.content.size);(s>0&&this.state.doc.nodeAt(s))==r.node&&(i=s)}this.dragging=new ho(e.slice,e.move,i<0?void 0:w.create(this.state.doc,i))}someProp(e,t){let r=this._props&&this._props[e],i;if(r!=null&&(i=t?t(r):r))return i;for(let o=0;o<this.directPlugins.length;o++){let l=this.directPlugins[o].props[e];if(l!=null&&(i=t?t(l):l))return i}let s=this.state.plugins;if(s)for(let o=0;o<s.length;o++){let l=s[o].props[e];if(l!=null&&(i=t?t(l):l))return i}}hasFocus(){if(ie){let e=this.root.activeElement;if(e==this.dom)return!0;if(!e||!this.dom.contains(e))return!1;for(;e&&this.dom!=e&&this.dom.contains(e);){if(e.contentEditable=="false")return!1;e=e.parentElement}return!0}return this.root.activeElement==this.dom}focus(){this.domObserver.stop(),this.editable&&Ca(this.dom),Te(this),this.domObserver.start()}get root(){let e=this._root;if(e==null){for(let t=this.dom.parentNode;t;t=t.parentNode)if(t.nodeType==9||t.nodeType==11&&t.host)return t.getSelection||(Object.getPrototypeOf(t).getSelection=()=>t.ownerDocument.getSelection()),this._root=t}return e||document}updateRoot(){this._root=null}posAtCoords(e){return Na(this,e)}coordsAtPos(e,t=1){return Js(this,e,t)}domAtPos(e,t=0){return this.docView.domFromPos(e,t)}nodeDOM(e){let t=this.docView.descAt(e);return t?t.nodeDOM:null}posAtDOM(e,t,r=-1){let i=this.docView.posFromDOM(e,t,r);if(i==null)throw new RangeError("DOM position not inside the editor");return i}endOfTextblock(e,t){return Ra(this,t||this.state,e)}pasteHTML(e,t){return zt(this,"",e,!1,t||new ClipboardEvent("paste"))}pasteText(e,t){return zt(this,e,null,!0,t||new ClipboardEvent("paste"))}destroy(){this.docView&&(lc(this),this.destroyPluginViews(),this.mounted?(this.docView.update(this.state.doc,[],qn(this),this),this.dom.textContent=""):this.dom.parentNode&&this.dom.parentNode.removeChild(this.dom),this.docView.destroy(),this.docView=null,ca())}get isDestroyed(){return this.docView==null}dispatchEvent(e){return cc(this,e)}dispatch(e){let t=this._props.dispatchTransaction;t?t.call(this,e):this.updateState(this.state.apply(e))}domSelectionRange(){let e=this.domSelection();return te&&this.root.nodeType===11&&pa(this.dom.ownerDocument)==this.dom&&Dc(this,e)||e}domSelection(){return this.root.getSelection()}}function Ri(n){let e=Object.create(null);return e.class="ProseMirror",e.contenteditable=String(n.editable),n.someProp("attributes",t=>{if(typeof t=="function"&&(t=t(n.state)),t)for(let r in t)r=="class"?e.class+=" "+t[r]:r=="style"?e.style=(e.style?e.style+";":"")+t[r]:!e[r]&&r!="contenteditable"&&r!="nodeName"&&(e[r]=String(t[r]))}),e.translate||(e.translate="no"),[oe.node(0,n.state.doc.content.size,e)]}function Pi(n){if(n.markCursor){let e=document.createElement("img");e.className="ProseMirror-separator",e.setAttribute("mark-placeholder","true"),e.setAttribute("alt",""),n.cursorWrapper={dom:e,deco:oe.widget(n.state.selection.head,e,{raw:!0,marks:n.markCursor})}}else n.cursorWrapper=null}function Bi(n){return!n.someProp("editable",e=>e(n.state)===!1)}function Hc(n,e){let t=Math.min(n.$anchor.sharedDepth(n.head),e.$anchor.sharedDepth(e.head));return n.$anchor.start(t)!=e.$anchor.start(t)}function zi(n){let e=Object.create(null);function t(r){for(let i in r)Object.prototype.hasOwnProperty.call(e,i)||(e[i]=r[i])}return n.someProp("nodeViews",t),n.someProp("markViews",t),e}function Wc(n,e){let t=0,r=0;for(let i in n){if(n[i]!=e[i])return!0;t++}for(let i in e)r++;return t!=r}function Li(n){if(n.spec.state||n.spec.filterTransaction||n.spec.appendTransaction)throw new RangeError("Plugins passed directly to the view must not have a state component")}var Ve={8:"Backspace",9:"Tab",10:"Enter",12:"NumLock",13:"Enter",16:"Shift",17:"Control",18:"Alt",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",44:"PrintScreen",45:"Insert",46:"Delete",59:";",61:"=",91:"Meta",92:"Meta",106:"*",107:"+",108:",",109:"-",110:".",111:"/",144:"NumLock",145:"ScrollLock",160:"Shift",161:"Shift",162:"Control",163:"Control",164:"Alt",165:"Alt",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},dn={48:")",49:"!",50:"@",51:"#",52:"$",53:"%",54:"^",55:"&",56:"*",57:"(",59:":",61:"+",173:"_",186:":",187:"+",188:"<",189:"_",190:">",191:"?",192:"~",219:"{",220:"|",221:"}",222:'"'},Jc=typeof navigator<"u"&&/Mac/.test(navigator.platform),jc=typeof navigator<"u"&&/MSIE \d|Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(navigator.userAgent);for(var Y=0;Y<10;Y++)Ve[48+Y]=Ve[96+Y]=String(Y);for(var Y=1;Y<=24;Y++)Ve[Y+111]="F"+Y;for(var Y=65;Y<=90;Y++)Ve[Y]=String.fromCharCode(Y+32),dn[Y]=String.fromCharCode(Y);for(var _n in Ve)dn.hasOwnProperty(_n)||(dn[_n]=Ve[_n]);function Kc(n){var e=Jc&&n.metaKey&&n.shiftKey&&!n.ctrlKey&&!n.altKey||jc&&n.shiftKey&&n.key&&n.key.length==1||n.key=="Unidentified",t=!e&&n.key||(n.shiftKey?dn:Ve)[n.keyCode]||n.key||"Unidentified";return t=="Esc"&&(t="Escape"),t=="Del"&&(t="Delete"),t=="Left"&&(t="ArrowLeft"),t=="Up"&&(t="ArrowUp"),t=="Right"&&(t="ArrowRight"),t=="Down"&&(t="ArrowDown"),t}const qc=typeof navigator<"u"?/Mac|iP(hone|[oa]d)/.test(navigator.platform):!1;function Uc(n){let e=n.split(/-(?!$)/),t=e[e.length-1];t=="Space"&&(t=" ");let r,i,s,o;for(let l=0;l<e.length-1;l++){let a=e[l];if(/^(cmd|meta|m)$/i.test(a))o=!0;else if(/^a(lt)?$/i.test(a))r=!0;else if(/^(c|ctrl|control)$/i.test(a))i=!0;else if(/^s(hift)?$/i.test(a))s=!0;else if(/^mod$/i.test(a))qc?o=!0:i=!0;else throw new Error("Unrecognized modifier name: "+a)}return r&&(t="Alt-"+t),i&&(t="Ctrl-"+t),o&&(t="Meta-"+t),s&&(t="Shift-"+t),t}function _c(n){let e=Object.create(null);for(let t in n)e[Uc(t)]=n[t];return e}function Gn(n,e,t=!0){return e.altKey&&(n="Alt-"+n),e.ctrlKey&&(n="Ctrl-"+n),e.metaKey&&(n="Meta-"+n),t&&e.shiftKey&&(n="Shift-"+n),n}function Gc(n){return new le({props:{handleKeyDown:ko(n)}})}function ko(n){let e=_c(n);return function(t,r){let i=Kc(r),s,o=e[Gn(i,r)];if(o&&o(t.state,t.dispatch,t))return!0;if(i.length==1&&i!=" "){if(r.shiftKey){let l=e[Gn(i,r,!1)];if(l&&l(t.state,t.dispatch,t))return!0}if((r.shiftKey||r.altKey||r.metaKey||i.charCodeAt(0)>127)&&(s=Ve[r.keyCode])&&s!=i){let l=e[Gn(s,r)];if(l&&l(t.state,t.dispatch,t))return!0}}return!1}}const Yc=(n,e)=>n.selection.empty?!1:(e&&e(n.tr.deleteSelection().scrollIntoView()),!0);function bo(n,e){let{$cursor:t}=n.selection;return!t||(e?!e.endOfTextblock("backward",n):t.parentOffset>0)?null:t}const Xc=(n,e,t)=>{let r=bo(n,t);if(!r)return!1;let i=Rr(r);if(!i){let o=r.blockRange(),l=o&&Ct(o);return l==null?!1:(e&&e(n.tr.lift(o,l).scrollIntoView()),!0)}let s=i.nodeBefore;if(!s.type.spec.isolating&&Co(n,i,e))return!0;if(r.parent.content.size==0&&(xt(s,"end")||w.isSelectable(s))){let o=bn(n.doc,r.before(),r.after(),x.empty);if(o&&o.slice.size<o.to-o.from){if(e){let l=n.tr.step(o);l.setSelection(xt(s,"end")?E.findFrom(l.doc.resolve(l.mapping.map(i.pos,-1)),-1):w.create(l.doc,i.pos-s.nodeSize)),e(l.scrollIntoView())}return!0}}return s.isAtom&&i.depth==r.depth-1?(e&&e(n.tr.delete(i.pos-s.nodeSize,i.pos).scrollIntoView()),!0):!1},Zc=(n,e,t)=>{let r=bo(n,t);if(!r)return!1;let i=Rr(r);return i?xo(n,i,e):!1},Qc=(n,e,t)=>{let r=So(n,t);if(!r)return!1;let i=Pr(r);return i?xo(n,i,e):!1};function xo(n,e,t){let r=e.nodeBefore,i=r,s=e.pos-1;for(;!i.isTextblock;s--){if(i.type.spec.isolating)return!1;let u=i.lastChild;if(!u)return!1;i=u}let o=e.nodeAfter,l=o,a=e.pos+1;for(;!l.isTextblock;a++){if(l.type.spec.isolating)return!1;let u=l.firstChild;if(!u)return!1;l=u}let c=bn(n.doc,s,a,x.empty);if(!c||c.from!=s||c instanceof J&&c.slice.size>=a-s)return!1;if(t){let u=n.tr.step(c);u.setSelection(O.create(u.doc,s)),t(u.scrollIntoView())}return!0}function xt(n,e,t=!1){for(let r=n;r;r=e=="start"?r.firstChild:r.lastChild){if(r.isTextblock)return!0;if(t&&r.childCount!=1)return!1}return!1}const eu=(n,e,t)=>{let{$head:r,empty:i}=n.selection,s=r;if(!i)return!1;if(r.parent.isTextblock){if(t?!t.endOfTextblock("backward",n):r.parentOffset>0)return!1;s=Rr(r)}let o=s&&s.nodeBefore;return!o||!w.isSelectable(o)?!1:(e&&e(n.tr.setSelection(w.create(n.doc,s.pos-o.nodeSize)).scrollIntoView()),!0)};function Rr(n){if(!n.parent.type.spec.isolating)for(let e=n.depth-1;e>=0;e--){if(n.index(e)>0)return n.doc.resolve(n.before(e+1));if(n.node(e).type.spec.isolating)break}return null}function So(n,e){let{$cursor:t}=n.selection;return!t||(e?!e.endOfTextblock("forward",n):t.parentOffset<t.parent.content.size)?null:t}const tu=(n,e,t)=>{let r=So(n,t);if(!r)return!1;let i=Pr(r);if(!i)return!1;let s=i.nodeAfter;if(Co(n,i,e))return!0;if(r.parent.content.size==0&&(xt(s,"start")||w.isSelectable(s))){let o=bn(n.doc,r.before(),r.after(),x.empty);if(o&&o.slice.size<o.to-o.from){if(e){let l=n.tr.step(o);l.setSelection(xt(s,"start")?E.findFrom(l.doc.resolve(l.mapping.map(i.pos)),1):w.create(l.doc,l.mapping.map(i.pos))),e(l.scrollIntoView())}return!0}}return s.isAtom&&i.depth==r.depth-1?(e&&e(n.tr.delete(i.pos,i.pos+s.nodeSize).scrollIntoView()),!0):!1},nu=(n,e,t)=>{let{$head:r,empty:i}=n.selection,s=r;if(!i)return!1;if(r.parent.isTextblock){if(t?!t.endOfTextblock("forward",n):r.parentOffset<r.parent.content.size)return!1;s=Pr(r)}let o=s&&s.nodeAfter;return!o||!w.isSelectable(o)?!1:(e&&e(n.tr.setSelection(w.create(n.doc,s.pos)).scrollIntoView()),!0)};function Pr(n){if(!n.parent.type.spec.isolating)for(let e=n.depth-1;e>=0;e--){let t=n.node(e);if(n.index(e)+1<t.childCount)return n.doc.resolve(n.after(e+1));if(t.type.spec.isolating)break}return null}const ru=(n,e)=>{let t=n.selection,r=t instanceof w,i;if(r){if(t.node.isTextblock||!$e(n.doc,t.from))return!1;i=t.from}else if(i=kn(n.doc,t.from,-1),i==null)return!1;if(e){let s=n.tr.join(i);r&&s.setSelection(w.create(s.doc,i-n.doc.resolve(i).nodeBefore.nodeSize)),e(s.scrollIntoView())}return!0},iu=(n,e)=>{let t=n.selection,r;if(t instanceof w){if(t.node.isTextblock||!$e(n.doc,t.to))return!1;r=t.to}else if(r=kn(n.doc,t.to,1),r==null)return!1;return e&&e(n.tr.join(r).scrollIntoView()),!0},su=(n,e)=>{let{$from:t,$to:r}=n.selection,i=t.blockRange(r),s=i&&Ct(i);return s==null?!1:(e&&e(n.tr.lift(i,s).scrollIntoView()),!0)},ou=(n,e)=>{let{$head:t,$anchor:r}=n.selection;return!t.parent.type.spec.code||!t.sameParent(r)?!1:(e&&e(n.tr.insertText(`
`).scrollIntoView()),!0)};function Mo(n){for(let e=0;e<n.edgeCount;e++){let{type:t}=n.edge(e);if(t.isTextblock&&!t.hasRequiredAttrs())return t}return null}const lu=(n,e)=>{let{$head:t,$anchor:r}=n.selection;if(!t.parent.type.spec.code||!t.sameParent(r))return!1;let i=t.node(-1),s=t.indexAfter(-1),o=Mo(i.contentMatchAt(s));if(!o||!i.canReplaceWith(s,s,o))return!1;if(e){let l=t.after(),a=n.tr.replaceWith(l,l,o.createAndFill());a.setSelection(E.near(a.doc.resolve(l),1)),e(a.scrollIntoView())}return!0},au=(n,e)=>{let t=n.selection,{$from:r,$to:i}=t;if(t instanceof he||r.parent.inlineContent||i.parent.inlineContent)return!1;let s=Mo(i.parent.contentMatchAt(i.indexAfter()));if(!s||!s.isTextblock)return!1;if(e){let o=(!r.parentOffset&&i.index()<i.parent.childCount?r:i).pos,l=n.tr.insert(o,s.createAndFill());l.setSelection(O.create(l.doc,o+1)),e(l.scrollIntoView())}return!0},cu=(n,e)=>{let{$cursor:t}=n.selection;if(!t||t.parent.content.size)return!1;if(t.depth>1&&t.after()!=t.end(-1)){let s=t.before();if(ht(n.doc,s))return e&&e(n.tr.split(s).scrollIntoView()),!0}let r=t.blockRange(),i=r&&Ct(r);return i==null?!1:(e&&e(n.tr.lift(r,i).scrollIntoView()),!0)},uu=(n,e)=>{let{$from:t,to:r}=n.selection,i,s=t.sharedDepth(r);return s==0?!1:(i=t.before(s),e&&e(n.tr.setSelection(w.create(n.doc,i))),!0)};function du(n,e,t){let r=e.nodeBefore,i=e.nodeAfter,s=e.index();return!r||!i||!r.type.compatibleContent(i.type)?!1:!r.content.size&&e.parent.canReplace(s-1,s)?(t&&t(n.tr.delete(e.pos-r.nodeSize,e.pos).scrollIntoView()),!0):!e.parent.canReplace(s,s+1)||!(i.isTextblock||$e(n.doc,e.pos))?!1:(t&&t(n.tr.clearIncompatible(e.pos,r.type,r.contentMatchAt(r.childCount)).join(e.pos).scrollIntoView()),!0)}function Co(n,e,t){let r=e.nodeBefore,i=e.nodeAfter,s,o;if(r.type.spec.isolating||i.type.spec.isolating)return!1;if(du(n,e,t))return!0;let l=e.parent.canReplace(e.index(),e.index()+1);if(l&&(s=(o=r.contentMatchAt(r.childCount)).findWrapping(i.type))&&o.matchType(s[0]||i.type).validEnd){if(t){let d=e.pos+i.nodeSize,f=k.empty;for(let m=s.length-1;m>=0;m--)f=k.from(s[m].create(null,f));f=k.from(r.copy(f));let h=n.tr.step(new j(e.pos-1,d,e.pos,d,new x(f,1,0),s.length,!0)),p=d+2*s.length;$e(h.doc,p)&&h.join(p),t(h.scrollIntoView())}return!0}let a=E.findFrom(e,1),c=a&&a.$from.blockRange(a.$to),u=c&&Ct(c);if(u!=null&&u>=e.depth)return t&&t(n.tr.lift(c,u).scrollIntoView()),!0;if(l&&xt(i,"start",!0)&&xt(r,"end")){let d=r,f=[];for(;f.push(d),!d.isTextblock;)d=d.lastChild;let h=i,p=1;for(;!h.isTextblock;h=h.firstChild)p++;if(d.canReplace(d.childCount,d.childCount,h.content)){if(t){let m=k.empty;for(let y=f.length-1;y>=0;y--)m=k.from(f[y].copy(m));let g=n.tr.step(new j(e.pos-f.length,e.pos+i.nodeSize,e.pos+p,e.pos+i.nodeSize-p,new x(m,f.length,0),0,!0));t(g.scrollIntoView())}return!0}}return!1}function wo(n){return function(e,t){let r=e.selection,i=n<0?r.$from:r.$to,s=i.depth;for(;i.node(s).isInline;){if(!s)return!1;s--}return i.node(s).isTextblock?(t&&t(e.tr.setSelection(O.create(e.doc,n<0?i.start(s):i.end(s)))),!0):!1}}const fu=wo(-1),hu=wo(1);function pu(n,e=null){return function(t,r){let{$from:i,$to:s}=t.selection,o=i.blockRange(s),l=o&&Cr(o,n,e);return l?(r&&r(t.tr.wrap(o,l).scrollIntoView()),!0):!1}}function Fi(n,e=null){return function(t,r){let i=!1;for(let s=0;s<t.selection.ranges.length&&!i;s++){let{$from:{pos:o},$to:{pos:l}}=t.selection.ranges[s];t.doc.nodesBetween(o,l,(a,c)=>{if(i)return!1;if(!(!a.isTextblock||a.hasMarkup(n,e)))if(a.type==n)i=!0;else{let u=t.doc.resolve(c),d=u.index();i=u.parent.canReplaceWith(d,d+1,n)}})}if(!i)return!1;if(r){let s=t.tr;for(let o=0;o<t.selection.ranges.length;o++){let{$from:{pos:l},$to:{pos:a}}=t.selection.ranges[o];s.setBlockType(l,a,n,e)}r(s.scrollIntoView())}return!0}}typeof navigator<"u"?/Mac|iP(hone|[oa]d)/.test(navigator.platform):typeof os<"u"&&os.platform&&os.platform()=="darwin";function mu(n,e=null){return function(t,r){let{$from:i,$to:s}=t.selection,o=i.blockRange(s),l=!1,a=o;if(!o)return!1;if(o.depth>=2&&i.node(o.depth-1).type.compatibleContent(n)&&o.startIndex==0){if(i.index(o.depth-1)==0)return!1;let u=t.doc.resolve(o.start-2);a=new nn(u,u,o.depth),o.endIndex<o.parent.childCount&&(o=new nn(i,t.doc.resolve(s.end(o.depth)),o.depth)),l=!0}let c=Cr(a,n,e,o);return c?(r&&r(gu(t.tr,o,c,l,n).scrollIntoView()),!0):!1}}function gu(n,e,t,r,i){let s=k.empty;for(let u=t.length-1;u>=0;u--)s=k.from(t[u].type.create(t[u].attrs,s));n.step(new j(e.start-(r?2:0),e.end,e.start,e.end,new x(s,0,0),t.length,!0));let o=0;for(let u=0;u<t.length;u++)t[u].type==i&&(o=u+1);let l=t.length-o,a=e.start+t.length-(r?2:0),c=e.parent;for(let u=e.startIndex,d=e.endIndex,f=!0;u<d;u++,f=!1)!f&&ht(n.doc,a,l)&&(n.split(a,l),a+=2*l),a+=c.child(u).nodeSize;return n}function yu(n){return function(e,t){let{$from:r,$to:i}=e.selection,s=r.blockRange(i,o=>o.childCount>0&&o.firstChild.type==n);return s?t?r.node(s.depth-1).type==n?ku(e,t,n,s):bu(e,t,s):!0:!1}}function ku(n,e,t,r){let i=n.tr,s=r.end,o=r.$to.end(r.depth);s<o&&(i.step(new j(s-1,o,s,o,new x(k.from(t.create(null,r.parent.copy())),1,0),1,!0)),r=new nn(i.doc.resolve(r.$from.pos),i.doc.resolve(o),r.depth));const l=Ct(r);if(l==null)return!1;i.lift(r,l);let a=i.mapping.map(s,-1)-1;return $e(i.doc,a)&&i.join(a),e(i.scrollIntoView()),!0}function bu(n,e,t){let r=n.tr,i=t.parent;for(let h=t.end,p=t.endIndex-1,m=t.startIndex;p>m;p--)h-=i.child(p).nodeSize,r.delete(h-1,h+1);let s=r.doc.resolve(t.start),o=s.nodeAfter;if(r.mapping.map(t.end)!=t.start+s.nodeAfter.nodeSize)return!1;let l=t.startIndex==0,a=t.endIndex==i.childCount,c=s.node(-1),u=s.index(-1);if(!c.canReplace(u+(l?0:1),u+1,o.content.append(a?k.empty:k.from(i))))return!1;let d=s.pos,f=d+o.nodeSize;return r.step(new j(d-(l?1:0),f+(a?1:0),d+1,f-1,new x((l?k.empty:k.from(i.copy(k.empty))).append(a?k.empty:k.from(i.copy(k.empty))),l?0:1,a?0:1),l?0:1)),e(r.scrollIntoView()),!0}function xu(n){return function(e,t){let{$from:r,$to:i}=e.selection,s=r.blockRange(i,c=>c.childCount>0&&c.firstChild.type==n);if(!s)return!1;let o=s.startIndex;if(o==0)return!1;let l=s.parent,a=l.child(o-1);if(a.type!=n)return!1;if(t){let c=a.lastChild&&a.lastChild.type==l.type,u=k.from(c?n.create():null),d=new x(k.from(n.create(null,k.from(l.type.create(null,u)))),c?3:1,0),f=s.start,h=s.end;t(e.tr.step(new j(f-(c?3:1),h,f,h,d,1,!0)).scrollIntoView())}return!0}}function wn(n){const{state:e,transaction:t}=n;let{selection:r}=t,{doc:i}=t,{storedMarks:s}=t;return{...e,apply:e.apply.bind(e),applyTransaction:e.applyTransaction.bind(e),plugins:e.plugins,schema:e.schema,reconfigure:e.reconfigure.bind(e),toJSON:e.toJSON.bind(e),get storedMarks(){return s},get selection(){return r},get doc(){return i},get tr(){return r=t.selection,i=t.doc,s=t.storedMarks,t}}}class Tn{constructor(e){this.editor=e.editor,this.rawCommands=this.editor.extensionManager.commands,this.customState=e.state}get hasCustomState(){return!!this.customState}get state(){return this.customState||this.editor.state}get commands(){const{rawCommands:e,editor:t,state:r}=this,{view:i}=t,{tr:s}=r,o=this.buildProps(s);return Object.fromEntries(Object.entries(e).map(([l,a])=>[l,(...u)=>{const d=a(...u)(o);return!s.getMeta("preventDispatch")&&!this.hasCustomState&&i.dispatch(s),d}]))}get chain(){return()=>this.createChain()}get can(){return()=>this.createCan()}createChain(e,t=!0){const{rawCommands:r,editor:i,state:s}=this,{view:o}=i,l=[],a=!!e,c=e||s.tr,u=()=>(!a&&t&&!c.getMeta("preventDispatch")&&!this.hasCustomState&&o.dispatch(c),l.every(f=>f===!0)),d={...Object.fromEntries(Object.entries(r).map(([f,h])=>[f,(...m)=>{const g=this.buildProps(c,t),y=h(...m)(g);return l.push(y),d}])),run:u};return d}createCan(e){const{rawCommands:t,state:r}=this,i=!1,s=e||r.tr,o=this.buildProps(s,i);return{...Object.fromEntries(Object.entries(t).map(([a,c])=>[a,(...u)=>c(...u)({...o,dispatch:void 0})])),chain:()=>this.createChain(s,i)}}buildProps(e,t=!0){const{rawCommands:r,editor:i,state:s}=this,{view:o}=i,l={tr:e,editor:i,view:o,state:wn({state:s,transaction:e}),dispatch:t?()=>{}:void 0,chain:()=>this.createChain(e,t),can:()=>this.createCan(e),get commands(){return Object.fromEntries(Object.entries(r).map(([a,c])=>[a,(...u)=>c(...u)(l)]))}};return l}}class Su{constructor(){this.callbacks={}}on(e,t){return this.callbacks[e]||(this.callbacks[e]=[]),this.callbacks[e].push(t),this}emit(e,...t){const r=this.callbacks[e];return r&&r.forEach(i=>i.apply(this,t)),this}off(e,t){const r=this.callbacks[e];return r&&(t?this.callbacks[e]=r.filter(i=>i!==t):delete this.callbacks[e]),this}removeAllListeners(){this.callbacks={}}}function S(n,e,t){return n.config[e]===void 0&&n.parent?S(n.parent,e,t):typeof n.config[e]=="function"?n.config[e].bind({...t,parent:n.parent?S(n.parent,e,t):null}):n.config[e]}function On(n){const e=n.filter(i=>i.type==="extension"),t=n.filter(i=>i.type==="node"),r=n.filter(i=>i.type==="mark");return{baseExtensions:e,nodeExtensions:t,markExtensions:r}}function To(n){const e=[],{nodeExtensions:t,markExtensions:r}=On(n),i=[...t,...r],s={default:null,rendered:!0,renderHTML:null,parseHTML:null,keepOnSplit:!0,isRequired:!1};return n.forEach(o=>{const l={name:o.name,options:o.options,storage:o.storage},a=S(o,"addGlobalAttributes",l);if(!a)return;a().forEach(u=>{u.types.forEach(d=>{Object.entries(u.attributes).forEach(([f,h])=>{e.push({type:d,name:f,attribute:{...s,...h}})})})})}),i.forEach(o=>{const l={name:o.name,options:o.options,storage:o.storage},a=S(o,"addAttributes",l);if(!a)return;const c=a();Object.entries(c).forEach(([u,d])=>{const f={...s,...d};typeof f?.default=="function"&&(f.default=f.default()),f?.isRequired&&f?.default===void 0&&delete f.default,e.push({type:o.name,name:u,attribute:f})})}),e}function q(n,e){if(typeof n=="string"){if(!e.nodes[n])throw Error(`There is no node type named '${n}'. Maybe you forgot to add the extension?`);return e.nodes[n]}return n}function H(...n){return n.filter(e=>!!e).reduce((e,t)=>{const r={...e};return Object.entries(t).forEach(([i,s])=>{if(!r[i]){r[i]=s;return}if(i==="class"){const l=s?s.split(" "):[],a=r[i]?r[i].split(" "):[],c=l.filter(u=>!a.includes(u));r[i]=[...a,...c].join(" ")}else i==="style"?r[i]=[r[i],s].join("; "):r[i]=s}),r},{})}function kr(n,e){return e.filter(t=>t.attribute.rendered).map(t=>t.attribute.renderHTML?t.attribute.renderHTML(n.attrs)||{}:{[t.name]:n.attrs[t.name]}).reduce((t,r)=>H(t,r),{})}function Oo(n){return typeof n=="function"}function N(n,e=void 0,...t){return Oo(n)?e?n.bind(e)(...t):n(...t):n}function Mu(n={}){return Object.keys(n).length===0&&n.constructor===Object}function Cu(n){return typeof n!="string"?n:n.match(/^[+-]?(?:\d*\.)?\d+$/)?Number(n):n==="true"?!0:n==="false"?!1:n}function Vi(n,e){return n.style?n:{...n,getAttrs:t=>{const r=n.getAttrs?n.getAttrs(t):n.attrs;if(r===!1)return!1;const i=e.reduce((s,o)=>{const l=o.attribute.parseHTML?o.attribute.parseHTML(t):Cu(t.getAttribute(o.name));return l==null?s:{...s,[o.name]:l}},{});return{...r,...i}}}}function $i(n){return Object.fromEntries(Object.entries(n).filter(([e,t])=>e==="attrs"&&Mu(t)?!1:t!=null))}function wu(n,e){var t;const r=To(n),{nodeExtensions:i,markExtensions:s}=On(n),o=(t=i.find(c=>S(c,"topNode")))===null||t===void 0?void 0:t.name,l=Object.fromEntries(i.map(c=>{const u=r.filter(y=>y.type===c.name),d={name:c.name,options:c.options,storage:c.storage,editor:e},f=n.reduce((y,b)=>{const M=S(b,"extendNodeSchema",d);return{...y,...M?M(c):{}}},{}),h=$i({...f,content:N(S(c,"content",d)),marks:N(S(c,"marks",d)),group:N(S(c,"group",d)),inline:N(S(c,"inline",d)),atom:N(S(c,"atom",d)),selectable:N(S(c,"selectable",d)),draggable:N(S(c,"draggable",d)),code:N(S(c,"code",d)),defining:N(S(c,"defining",d)),isolating:N(S(c,"isolating",d)),attrs:Object.fromEntries(u.map(y=>{var b;return[y.name,{default:(b=y?.attribute)===null||b===void 0?void 0:b.default}]}))}),p=N(S(c,"parseHTML",d));p&&(h.parseDOM=p.map(y=>Vi(y,u)));const m=S(c,"renderHTML",d);m&&(h.toDOM=y=>m({node:y,HTMLAttributes:kr(y,u)}));const g=S(c,"renderText",d);return g&&(h.toText=g),[c.name,h]})),a=Object.fromEntries(s.map(c=>{const u=r.filter(g=>g.type===c.name),d={name:c.name,options:c.options,storage:c.storage,editor:e},f=n.reduce((g,y)=>{const b=S(y,"extendMarkSchema",d);return{...g,...b?b(c):{}}},{}),h=$i({...f,inclusive:N(S(c,"inclusive",d)),excludes:N(S(c,"excludes",d)),group:N(S(c,"group",d)),spanning:N(S(c,"spanning",d)),code:N(S(c,"code",d)),attrs:Object.fromEntries(u.map(g=>{var y;return[g.name,{default:(y=g?.attribute)===null||y===void 0?void 0:y.default}]}))}),p=N(S(c,"parseHTML",d));p&&(h.parseDOM=p.map(g=>Vi(g,u)));const m=S(c,"renderHTML",d);return m&&(h.toDOM=g=>m({mark:g,HTMLAttributes:kr(g,u)})),[c.name,h]}));return new vl({topNode:o,nodes:l,marks:a})}function Yn(n,e){return e.nodes[n]||e.marks[n]||null}function Hi(n,e){return Array.isArray(e)?e.some(t=>(typeof t=="string"?t:t.name)===n.name):e}const Tu=(n,e=500)=>{let t="";const r=n.parentOffset;return n.parent.nodesBetween(Math.max(0,r-e),r,(i,s,o,l)=>{var a,c;const u=((c=(a=i.type.spec).toText)===null||c===void 0?void 0:c.call(a,{node:i,pos:s,parent:o,index:l}))||i.textContent||"%leaf%";t+=u.slice(0,Math.max(0,r-s))}),t};function Br(n){return Object.prototype.toString.call(n)==="[object RegExp]"}class vn{constructor(e){this.find=e.find,this.handler=e.handler}}const Ou=(n,e)=>{if(Br(e))return e.exec(n);const t=e(n);if(!t)return null;const r=[t.text];return r.index=t.index,r.input=n,r.data=t.data,t.replaceWith&&(t.text.includes(t.replaceWith)||console.warn('[tiptap warn]: "inputRuleMatch.replaceWith" must be part of "inputRuleMatch.text".'),r.push(t.replaceWith)),r};function _t(n){var e;const{editor:t,from:r,to:i,text:s,rules:o,plugin:l}=n,{view:a}=t;if(a.composing)return!1;const c=a.state.doc.resolve(r);if(c.parent.type.spec.code||!((e=c.nodeBefore||c.nodeAfter)===null||e===void 0)&&e.marks.find(f=>f.type.spec.code))return!1;let u=!1;const d=Tu(c)+s;return o.forEach(f=>{if(u)return;const h=Ou(d,f.find);if(!h)return;const p=a.state.tr,m=wn({state:a.state,transaction:p}),g={from:r-(h[0].length-s.length),to:i},{commands:y,chain:b,can:M}=new Tn({editor:t,state:m});f.handler({state:m,range:g,match:h,commands:y,chain:b,can:M})===null||!p.steps.length||(p.setMeta(l,{transform:p,from:r,to:i,text:s}),a.dispatch(p),u=!0)}),u}function vu(n){const{editor:e,rules:t}=n,r=new le({state:{init(){return null},apply(i,s){const o=i.getMeta(r);if(o)return o;const l=i.getMeta("applyInputRules");return!!l&&setTimeout(()=>{const{from:c,text:u}=l,d=c+u.length;_t({editor:e,from:c,to:d,text:u,rules:t,plugin:r})}),i.selectionSet||i.docChanged?null:s}},props:{handleTextInput(i,s,o,l){return _t({editor:e,from:s,to:o,text:l,rules:t,plugin:r})},handleDOMEvents:{compositionend:i=>(setTimeout(()=>{const{$cursor:s}=i.state.selection;s&&_t({editor:e,from:s.pos,to:s.pos,text:"",rules:t,plugin:r})}),!1)},handleKeyDown(i,s){if(s.key!=="Enter")return!1;const{$cursor:o}=i.state.selection;return o?_t({editor:e,from:o.pos,to:o.pos,text:`
`,rules:t,plugin:r}):!1}},isInputRules:!0});return r}function Nu(n){return typeof n=="number"}class Eu{constructor(e){this.find=e.find,this.handler=e.handler}}const Au=(n,e,t)=>{if(Br(e))return[...n.matchAll(e)];const r=e(n,t);return r?r.map(i=>{const s=[i.text];return s.index=i.index,s.input=n,s.data=i.data,i.replaceWith&&(i.text.includes(i.replaceWith)||console.warn('[tiptap warn]: "pasteRuleMatch.replaceWith" must be part of "pasteRuleMatch.text".'),s.push(i.replaceWith)),s}):[]};function Du(n){const{editor:e,state:t,from:r,to:i,rule:s,pasteEvent:o,dropEvent:l}=n,{commands:a,chain:c,can:u}=new Tn({editor:e,state:t}),d=[];return t.doc.nodesBetween(r,i,(h,p)=>{if(!h.isTextblock||h.type.spec.code)return;const m=Math.max(r,p),g=Math.min(i,p+h.content.size),y=h.textBetween(m-p,g-p,void 0,"￼");Au(y,s.find,o).forEach(M=>{if(M.index===void 0)return;const C=m+M.index+1,A=C+M[0].length,v={from:t.tr.mapping.map(C),to:t.tr.mapping.map(A)},R=s.handler({state:t,range:v,match:M,commands:a,chain:c,can:u,pasteEvent:o,dropEvent:l});d.push(R)})}),d.every(h=>h!==null)}const Iu=n=>{var e;const t=new ClipboardEvent("paste",{clipboardData:new DataTransfer});return(e=t.clipboardData)===null||e===void 0||e.setData("text/html",n),t};function Ru(n){const{editor:e,rules:t}=n;let r=null,i=!1,s=!1,o=typeof ClipboardEvent<"u"?new ClipboardEvent("paste"):null,l=typeof DragEvent<"u"?new DragEvent("drop"):null;const a=({state:u,from:d,to:f,rule:h,pasteEvt:p})=>{const m=u.tr,g=wn({state:u,transaction:m});if(!(!Du({editor:e,state:g,from:Math.max(d-1,0),to:f.b-1,rule:h,pasteEvent:p,dropEvent:l})||!m.steps.length))return l=typeof DragEvent<"u"?new DragEvent("drop"):null,o=typeof ClipboardEvent<"u"?new ClipboardEvent("paste"):null,m};return t.map(u=>new le({view(d){const f=h=>{var p;r=!((p=d.dom.parentElement)===null||p===void 0)&&p.contains(h.target)?d.dom.parentElement:null};return window.addEventListener("dragstart",f),{destroy(){window.removeEventListener("dragstart",f)}}},props:{handleDOMEvents:{drop:(d,f)=>(s=r===d.dom.parentElement,l=f,!1),paste:(d,f)=>{var h;const p=(h=f.clipboardData)===null||h===void 0?void 0:h.getData("text/html");return o=f,i=!!p?.includes("data-pm-slice"),!1}}},appendTransaction:(d,f,h)=>{const p=d[0],m=p.getMeta("uiEvent")==="paste"&&!i,g=p.getMeta("uiEvent")==="drop"&&!s,y=p.getMeta("applyPasteRules"),b=!!y;if(!m&&!g&&!b)return;if(b){const{from:A,text:v}=y,R=A+v.length,z=Iu(v);return a({rule:u,state:h,from:A,to:{b:R},pasteEvt:z})}const M=f.doc.content.findDiffStart(h.doc.content),C=f.doc.content.findDiffEnd(h.doc.content);if(!(!Nu(M)||!C||M===C.b))return a({rule:u,state:h,from:M,to:C,pasteEvt:o})}}))}function Pu(n){const e=n.filter((t,r)=>n.indexOf(t)!==r);return[...new Set(e)]}class dt{constructor(e,t){this.splittableMarks=[],this.editor=t,this.extensions=dt.resolve(e),this.schema=wu(this.extensions,t),this.setupExtensions()}static resolve(e){const t=dt.sort(dt.flatten(e)),r=Pu(t.map(i=>i.name));return r.length&&console.warn(`[tiptap warn]: Duplicate extension names found: [${r.map(i=>`'${i}'`).join(", ")}]. This can lead to issues.`),t}static flatten(e){return e.map(t=>{const r={name:t.name,options:t.options,storage:t.storage},i=S(t,"addExtensions",r);return i?[t,...this.flatten(i())]:t}).flat(10)}static sort(e){return e.sort((r,i)=>{const s=S(r,"priority")||100,o=S(i,"priority")||100;return s>o?-1:s<o?1:0})}get commands(){return this.extensions.reduce((e,t)=>{const r={name:t.name,options:t.options,storage:t.storage,editor:this.editor,type:Yn(t.name,this.schema)},i=S(t,"addCommands",r);return i?{...e,...i()}:e},{})}get plugins(){const{editor:e}=this,t=dt.sort([...this.extensions].reverse()),r=[],i=[],s=t.map(o=>{const l={name:o.name,options:o.options,storage:o.storage,editor:e,type:Yn(o.name,this.schema)},a=[],c=S(o,"addKeyboardShortcuts",l);let u={};if(o.type==="mark"&&o.config.exitable&&(u.ArrowRight=()=>ue.handleExit({editor:e,mark:o})),c){const m=Object.fromEntries(Object.entries(c()).map(([g,y])=>[g,()=>y({editor:e})]));u={...u,...m}}const d=Gc(u);a.push(d);const f=S(o,"addInputRules",l);Hi(o,e.options.enableInputRules)&&f&&r.push(...f());const h=S(o,"addPasteRules",l);Hi(o,e.options.enablePasteRules)&&h&&i.push(...h());const p=S(o,"addProseMirrorPlugins",l);if(p){const m=p();a.push(...m)}return a}).flat();return[vu({editor:e,rules:r}),...Ru({editor:e,rules:i}),...s]}get attributes(){return To(this.extensions)}get nodeViews(){const{editor:e}=this,{nodeExtensions:t}=On(this.extensions);return Object.fromEntries(t.filter(r=>!!S(r,"addNodeView")).map(r=>{const i=this.attributes.filter(a=>a.type===r.name),s={name:r.name,options:r.options,storage:r.storage,editor:e,type:q(r.name,this.schema)},o=S(r,"addNodeView",s);if(!o)return[];const l=(a,c,u,d)=>{const f=kr(a,i);return o()({editor:e,node:a,getPos:u,decorations:d,HTMLAttributes:f,extension:r})};return[r.name,l]}))}setupExtensions(){this.extensions.forEach(e=>{var t;this.editor.extensionStorage[e.name]=e.storage;const r={name:e.name,options:e.options,storage:e.storage,editor:this.editor,type:Yn(e.name,this.schema)};e.type==="mark"&&(!((t=N(S(e,"keepOnSplit",r)))!==null&&t!==void 0)||t)&&this.splittableMarks.push(e.name);const i=S(e,"onBeforeCreate",r),s=S(e,"onCreate",r),o=S(e,"onUpdate",r),l=S(e,"onSelectionUpdate",r),a=S(e,"onTransaction",r),c=S(e,"onFocus",r),u=S(e,"onBlur",r),d=S(e,"onDestroy",r);i&&this.editor.on("beforeCreate",i),s&&this.editor.on("create",s),o&&this.editor.on("update",o),l&&this.editor.on("selectionUpdate",l),a&&this.editor.on("transaction",a),c&&this.editor.on("focus",c),u&&this.editor.on("blur",u),d&&this.editor.on("destroy",d)})}}function Bu(n){return Object.prototype.toString.call(n).slice(8,-1)}function Xn(n){return Bu(n)!=="Object"?!1:n.constructor===Object&&Object.getPrototypeOf(n)===Object.prototype}function Nn(n,e){const t={...n};return Xn(n)&&Xn(e)&&Object.keys(e).forEach(r=>{Xn(e[r])?r in n?t[r]=Nn(n[r],e[r]):Object.assign(t,{[r]:e[r]}):Object.assign(t,{[r]:e[r]})}),t}class W{constructor(e={}){this.type="extension",this.name="extension",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...e},this.name=this.config.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`),this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=N(S(this,"addOptions",{name:this.name}))),this.storage=N(S(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(e={}){return new W(e)}configure(e={}){const t=this.extend();return t.parent=this.parent,t.options=Nn(this.options,e),t.storage=N(S(t,"addStorage",{name:t.name,options:t.options})),t}extend(e={}){const t=new W({...this.config,...e});return t.parent=this,this.child=t,t.name=e.name?e.name:t.parent.name,e.defaultOptions&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${t.name}".`),t.options=N(S(t,"addOptions",{name:t.name})),t.storage=N(S(t,"addStorage",{name:t.name,options:t.options})),t}}function vo(n,e,t){const{from:r,to:i}=e,{blockSeparator:s=`

`,textSerializers:o={}}=t||{};let l="";return n.nodesBetween(r,i,(a,c,u,d)=>{var f;a.isBlock&&c>r&&(l+=s);const h=o?.[a.type.name];if(h)return u&&(l+=h({node:a,pos:c,parent:u,index:d,range:e})),!1;a.isText&&(l+=(f=a?.text)===null||f===void 0?void 0:f.slice(Math.max(r,c)-c,i-c))}),l}function No(n){return Object.fromEntries(Object.entries(n.nodes).filter(([,e])=>e.spec.toText).map(([e,t])=>[e,t.spec.toText]))}const zu=W.create({name:"clipboardTextSerializer",addOptions(){return{blockSeparator:void 0}},addProseMirrorPlugins(){return[new le({key:new Oe("clipboardTextSerializer"),props:{clipboardTextSerializer:()=>{const{editor:n}=this,{state:e,schema:t}=n,{doc:r,selection:i}=e,{ranges:s}=i,o=Math.min(...s.map(u=>u.$from.pos)),l=Math.max(...s.map(u=>u.$to.pos)),a=No(t);return vo(r,{from:o,to:l},{...this.options.blockSeparator!==void 0?{blockSeparator:this.options.blockSeparator}:{},textSerializers:a})}}})]}}),Lu=()=>({editor:n,view:e})=>(requestAnimationFrame(()=>{var t;n.isDestroyed||(e.dom.blur(),(t=window?.getSelection())===null||t===void 0||t.removeAllRanges())}),!0),Fu=(n=!1)=>({commands:e})=>e.setContent("",n),Vu=()=>({state:n,tr:e,dispatch:t})=>{const{selection:r}=e,{ranges:i}=r;return t&&i.forEach(({$from:s,$to:o})=>{n.doc.nodesBetween(s.pos,o.pos,(l,a)=>{if(l.type.isText)return;const{doc:c,mapping:u}=e,d=c.resolve(u.map(a)),f=c.resolve(u.map(a+l.nodeSize)),h=d.blockRange(f);if(!h)return;const p=Ct(h);if(l.type.isTextblock){const{defaultType:m}=d.parent.contentMatchAt(d.index());e.setNodeMarkup(h.start,m)}(p||p===0)&&e.lift(h,p)})}),!0},$u=n=>e=>n(e),Hu=()=>({state:n,dispatch:e})=>au(n,e),Wu=(n,e)=>({editor:t,tr:r})=>{const{state:i}=t,s=i.doc.slice(n.from,n.to);r.deleteRange(n.from,n.to);const o=r.mapping.map(e);return r.insert(o,s.content),r.setSelection(new O(r.doc.resolve(o-1))),!0},Ju=()=>({tr:n,dispatch:e})=>{const{selection:t}=n,r=t.$anchor.node();if(r.content.size>0)return!1;const i=n.selection.$anchor;for(let s=i.depth;s>0;s-=1)if(i.node(s).type===r.type){if(e){const l=i.before(s),a=i.after(s);n.delete(l,a).scrollIntoView()}return!0}return!1},ju=n=>({tr:e,state:t,dispatch:r})=>{const i=q(n,t.schema),s=e.selection.$anchor;for(let o=s.depth;o>0;o-=1)if(s.node(o).type===i){if(r){const a=s.before(o),c=s.after(o);e.delete(a,c).scrollIntoView()}return!0}return!1},Ku=n=>({tr:e,dispatch:t})=>{const{from:r,to:i}=n;return t&&e.delete(r,i),!0},qu=()=>({state:n,dispatch:e})=>Yc(n,e),Uu=()=>({commands:n})=>n.keyboardShortcut("Enter"),_u=()=>({state:n,dispatch:e})=>lu(n,e);function fn(n,e,t={strict:!0}){const r=Object.keys(e);return r.length?r.every(i=>t.strict?e[i]===n[i]:Br(e[i])?e[i].test(n[i]):e[i]===n[i]):!0}function br(n,e,t={}){return n.find(r=>r.type===e&&fn(r.attrs,t))}function Gu(n,e,t={}){return!!br(n,e,t)}function zr(n,e,t={}){if(!n||!e)return;let r=n.parent.childAfter(n.parentOffset);if(n.parentOffset===r.offset&&r.offset!==0&&(r=n.parent.childBefore(n.parentOffset)),!r.node)return;const i=br([...r.node.marks],e,t);if(!i)return;let s=r.index,o=n.start()+r.offset,l=s+1,a=o+r.node.nodeSize;for(br([...r.node.marks],e,t);s>0&&i.isInSet(n.parent.child(s-1).marks);)s-=1,o-=n.parent.child(s).nodeSize;for(;l<n.parent.childCount&&Gu([...n.parent.child(l).marks],e,t);)a+=n.parent.child(l).nodeSize,l+=1;return{from:o,to:a}}function We(n,e){if(typeof n=="string"){if(!e.marks[n])throw Error(`There is no mark type named '${n}'. Maybe you forgot to add the extension?`);return e.marks[n]}return n}const Yu=(n,e={})=>({tr:t,state:r,dispatch:i})=>{const s=We(n,r.schema),{doc:o,selection:l}=t,{$from:a,from:c,to:u}=l;if(i){const d=zr(a,s,e);if(d&&d.from<=c&&d.to>=u){const f=O.create(o,d.from,d.to);t.setSelection(f)}}return!0},Xu=n=>e=>{const t=typeof n=="function"?n(e):n;for(let r=0;r<t.length;r+=1)if(t[r](e))return!0;return!1};function Eo(n){return n instanceof O}function Ye(n=0,e=0,t=0){return Math.min(Math.max(n,e),t)}function Ao(n,e=null){if(!e)return null;const t=E.atStart(n),r=E.atEnd(n);if(e==="start"||e===!0)return t;if(e==="end")return r;const i=t.from,s=r.to;return e==="all"?O.create(n,Ye(0,i,s),Ye(n.content.size,i,s)):O.create(n,Ye(e,i,s),Ye(e,i,s))}function Lr(){return["iPad Simulator","iPhone Simulator","iPod Simulator","iPad","iPhone","iPod"].includes(navigator.platform)||navigator.userAgent.includes("Mac")&&"ontouchend"in document}const Zu=(n=null,e={})=>({editor:t,view:r,tr:i,dispatch:s})=>{e={scrollIntoView:!0,...e};const o=()=>{Lr()&&r.dom.focus(),requestAnimationFrame(()=>{t.isDestroyed||(r.focus(),e?.scrollIntoView&&t.commands.scrollIntoView())})};if(r.hasFocus()&&n===null||n===!1)return!0;if(s&&n===null&&!Eo(t.state.selection))return o(),!0;const l=Ao(i.doc,n)||t.state.selection,a=t.state.selection.eq(l);return s&&(a||i.setSelection(l),a&&i.storedMarks&&i.setStoredMarks(i.storedMarks),o()),!0},Qu=(n,e)=>t=>n.every((r,i)=>e(r,{...t,index:i})),ed=(n,e)=>({tr:t,commands:r})=>r.insertContentAt({from:t.selection.from,to:t.selection.to},n,e),Do=n=>{const e=n.childNodes;for(let t=e.length-1;t>=0;t-=1){const r=e[t];r.nodeType===3&&r.nodeValue&&/^(\n\s\s|\n)$/.test(r.nodeValue)?n.removeChild(r):r.nodeType===1&&Do(r)}return n};function Wi(n){const e=`<body>${n}</body>`,t=new window.DOMParser().parseFromString(e,"text/html").body;return Do(t)}function hn(n,e,t){t={slice:!0,parseOptions:{},...t};const r=typeof n=="object"&&n!==null,i=typeof n=="string";if(r)try{return Array.isArray(n)&&n.length>0?k.fromArray(n.map(o=>e.nodeFromJSON(o))):e.nodeFromJSON(n)}catch(s){return console.warn("[tiptap warn]: Invalid content.","Passed value:",n,"Error:",s),hn("",e,t)}if(i){const s=gt.fromSchema(e);return t.slice?s.parseSlice(Wi(n),t.parseOptions).content:s.parse(Wi(n),t.parseOptions)}return hn("",e,t)}function td(n,e,t){const r=n.steps.length-1;if(r<e)return;const i=n.steps[r];if(!(i instanceof J||i instanceof j))return;const s=n.mapping.maps[r];let o=0;s.forEach((l,a,c,u)=>{o===0&&(o=u)}),n.setSelection(E.near(n.doc.resolve(o),t))}const nd=n=>n.toString().startsWith("<"),rd=(n,e,t)=>({tr:r,dispatch:i,editor:s})=>{if(i){t={parseOptions:{},updateSelection:!0,applyInputRules:!1,applyPasteRules:!1,...t};const o=hn(e,s.schema,{parseOptions:{preserveWhitespace:"full",...t.parseOptions}});if(o.toString()==="<>")return!0;let{from:l,to:a}=typeof n=="number"?{from:n,to:n}:{from:n.from,to:n.to},c=!0,u=!0;if((nd(o)?o:[o]).forEach(h=>{h.check(),c=c?h.isText&&h.marks.length===0:!1,u=u?h.isBlock:!1}),l===a&&u){const{parent:h}=r.doc.resolve(l);h.isTextblock&&!h.type.spec.code&&!h.childCount&&(l-=1,a+=1)}let f;c?(Array.isArray(e)?f=e.map(h=>h.text||"").join(""):typeof e=="object"&&e&&e.text?f=e.text:f=e,r.insertText(f,l,a)):(f=o,r.replaceWith(l,a,f)),t.updateSelection&&td(r,r.steps.length-1,-1),t.applyInputRules&&r.setMeta("applyInputRules",{from:l,text:f}),t.applyPasteRules&&r.setMeta("applyPasteRules",{from:l,text:f})}return!0},id=()=>({state:n,dispatch:e})=>ru(n,e),sd=()=>({state:n,dispatch:e})=>iu(n,e),od=()=>({state:n,dispatch:e})=>Xc(n,e),ld=()=>({state:n,dispatch:e})=>tu(n,e),ad=()=>({tr:n,state:e,dispatch:t})=>{try{const r=kn(e.doc,e.selection.$from.pos,-1);return r==null?!1:(n.join(r,2),t&&t(n),!0)}catch{return!1}},cd=()=>({state:n,dispatch:e,tr:t})=>{try{const r=kn(n.doc,n.selection.$from.pos,1);return r==null?!1:(t.join(r,2),e&&e(t),!0)}catch{return!1}},ud=()=>({state:n,dispatch:e})=>Zc(n,e),dd=()=>({state:n,dispatch:e})=>Qc(n,e);function Io(){return typeof navigator<"u"?/Mac/.test(navigator.platform):!1}function fd(n){const e=n.split(/-(?!$)/);let t=e[e.length-1];t==="Space"&&(t=" ");let r,i,s,o;for(let l=0;l<e.length-1;l+=1){const a=e[l];if(/^(cmd|meta|m)$/i.test(a))o=!0;else if(/^a(lt)?$/i.test(a))r=!0;else if(/^(c|ctrl|control)$/i.test(a))i=!0;else if(/^s(hift)?$/i.test(a))s=!0;else if(/^mod$/i.test(a))Lr()||Io()?o=!0:i=!0;else throw new Error(`Unrecognized modifier name: ${a}`)}return r&&(t=`Alt-${t}`),i&&(t=`Ctrl-${t}`),o&&(t=`Meta-${t}`),s&&(t=`Shift-${t}`),t}const hd=n=>({editor:e,view:t,tr:r,dispatch:i})=>{const s=fd(n).split(/-(?!$)/),o=s.find(c=>!["Alt","Ctrl","Meta","Shift"].includes(c)),l=new KeyboardEvent("keydown",{key:o==="Space"?" ":o,altKey:s.includes("Alt"),ctrlKey:s.includes("Ctrl"),metaKey:s.includes("Meta"),shiftKey:s.includes("Shift"),bubbles:!0,cancelable:!0}),a=e.captureTransaction(()=>{t.someProp("handleKeyDown",c=>c(t,l))});return a?.steps.forEach(c=>{const u=c.map(r.mapping);u&&i&&r.maybeStep(u)}),!0};function Ft(n,e,t={}){const{from:r,to:i,empty:s}=n.selection,o=e?q(e,n.schema):null,l=[];n.doc.nodesBetween(r,i,(d,f)=>{if(d.isText)return;const h=Math.max(r,f),p=Math.min(i,f+d.nodeSize);l.push({node:d,from:h,to:p})});const a=i-r,c=l.filter(d=>o?o.name===d.node.type.name:!0).filter(d=>fn(d.node.attrs,t,{strict:!1}));return s?!!c.length:c.reduce((d,f)=>d+f.to-f.from,0)>=a}const pd=(n,e={})=>({state:t,dispatch:r})=>{const i=q(n,t.schema);return Ft(t,i,e)?su(t,r):!1},md=()=>({state:n,dispatch:e})=>cu(n,e),gd=n=>({state:e,dispatch:t})=>{const r=q(n,e.schema);return yu(r)(e,t)},yd=()=>({state:n,dispatch:e})=>ou(n,e);function En(n,e){return e.nodes[n]?"node":e.marks[n]?"mark":null}function Ji(n,e){const t=typeof e=="string"?[e]:e;return Object.keys(n).reduce((r,i)=>(t.includes(i)||(r[i]=n[i]),r),{})}const kd=(n,e)=>({tr:t,state:r,dispatch:i})=>{let s=null,o=null;const l=En(typeof n=="string"?n:n.name,r.schema);return l?(l==="node"&&(s=q(n,r.schema)),l==="mark"&&(o=We(n,r.schema)),i&&t.selection.ranges.forEach(a=>{r.doc.nodesBetween(a.$from.pos,a.$to.pos,(c,u)=>{s&&s===c.type&&t.setNodeMarkup(u,void 0,Ji(c.attrs,e)),o&&c.marks.length&&c.marks.forEach(d=>{o===d.type&&t.addMark(u,u+c.nodeSize,o.create(Ji(d.attrs,e)))})})}),!0):!1},bd=()=>({tr:n,dispatch:e})=>(e&&n.scrollIntoView(),!0),xd=()=>({tr:n,commands:e})=>e.setTextSelection({from:0,to:n.doc.content.size}),Sd=()=>({state:n,dispatch:e})=>eu(n,e),Md=()=>({state:n,dispatch:e})=>nu(n,e),Cd=()=>({state:n,dispatch:e})=>uu(n,e),wd=()=>({state:n,dispatch:e})=>hu(n,e),Td=()=>({state:n,dispatch:e})=>fu(n,e);function Ro(n,e,t={}){return hn(n,e,{slice:!1,parseOptions:t})}const Od=(n,e=!1,t={})=>({tr:r,editor:i,dispatch:s})=>{const{doc:o}=r,l=Ro(n,i.schema,t);return s&&r.replaceWith(0,o.content.size,l).setMeta("preventUpdate",!e),!0};function Jt(n,e){const t=We(e,n.schema),{from:r,to:i,empty:s}=n.selection,o=[];s?(n.storedMarks&&o.push(...n.storedMarks),o.push(...n.selection.$head.marks())):n.doc.nodesBetween(r,i,a=>{o.push(...a.marks)});const l=o.find(a=>a.type.name===t.name);return l?{...l.attrs}:{}}function vd(n){for(let e=0;e<n.edgeCount;e+=1){const{type:t}=n.edge(e);if(t.isTextblock&&!t.hasRequiredAttrs())return t}return null}function Nd(n,e){for(let t=n.depth;t>0;t-=1){const r=n.node(t);if(e(r))return{pos:t>0?n.before(t):0,start:n.start(t),depth:t,node:r}}}function Fr(n){return e=>Nd(e.$from,n)}function Ed(n,e){const t=ye.fromSchema(e).serializeFragment(n),i=document.implementation.createHTMLDocument().createElement("div");return i.appendChild(t),i.innerHTML}function Ad(n,e){const t={from:0,to:n.content.size};return vo(n,t,e)}function Dd(n,e){const t=q(e,n.schema),{from:r,to:i}=n.selection,s=[];n.doc.nodesBetween(r,i,l=>{s.push(l)});const o=s.reverse().find(l=>l.type.name===t.name);return o?{...o.attrs}:{}}function Id(n,e){const t=En(typeof e=="string"?e:e.name,n.schema);return t==="node"?Dd(n,e):t==="mark"?Jt(n,e):{}}function Po(n,e,t){const r=[];return n===e?t.resolve(n).marks().forEach(i=>{const s=t.resolve(n-1),o=zr(s,i.type);o&&r.push({mark:i,...o})}):t.nodesBetween(n,e,(i,s)=>{!i||i?.nodeSize===void 0||r.push(...i.marks.map(o=>({from:s,to:s+i.nodeSize,mark:o})))}),r}function Xt(n,e,t){return Object.fromEntries(Object.entries(t).filter(([r])=>{const i=n.find(s=>s.type===e&&s.name===r);return i?i.attribute.keepOnSplit:!1}))}function xr(n,e,t={}){const{empty:r,ranges:i}=n.selection,s=e?We(e,n.schema):null;if(r)return!!(n.storedMarks||n.selection.$from.marks()).filter(d=>s?s.name===d.type.name:!0).find(d=>fn(d.attrs,t,{strict:!1}));let o=0;const l=[];if(i.forEach(({$from:d,$to:f})=>{const h=d.pos,p=f.pos;n.doc.nodesBetween(h,p,(m,g)=>{if(!m.isText&&!m.marks.length)return;const y=Math.max(h,g),b=Math.min(p,g+m.nodeSize),M=b-y;o+=M,l.push(...m.marks.map(C=>({mark:C,from:y,to:b})))})}),o===0)return!1;const a=l.filter(d=>s?s.name===d.mark.type.name:!0).filter(d=>fn(d.mark.attrs,t,{strict:!1})).reduce((d,f)=>d+f.to-f.from,0),c=l.filter(d=>s?d.mark.type!==s&&d.mark.type.excludes(s):!0).reduce((d,f)=>d+f.to-f.from,0);return(a>0?a+c:a)>=o}function Rd(n,e,t={}){if(!e)return Ft(n,null,t)||xr(n,null,t);const r=En(e,n.schema);return r==="node"?Ft(n,e,t):r==="mark"?xr(n,e,t):!1}function ji(n,e){const{nodeExtensions:t}=On(e),r=t.find(o=>o.name===n);if(!r)return!1;const i={name:r.name,options:r.options,storage:r.storage},s=N(S(r,"group",i));return typeof s!="string"?!1:s.split(" ").includes("list")}function Pd(n){var e;const t=(e=n.type.createAndFill())===null||e===void 0?void 0:e.toJSON(),r=n.toJSON();return JSON.stringify(t)===JSON.stringify(r)}function Bd(n,e,t){var r;const{selection:i}=e;let s=null;if(Eo(i)&&(s=i.$cursor),s){const l=(r=n.storedMarks)!==null&&r!==void 0?r:s.marks();return!!t.isInSet(l)||!l.some(a=>a.type.excludes(t))}const{ranges:o}=i;return o.some(({$from:l,$to:a})=>{let c=l.depth===0?n.doc.inlineContent&&n.doc.type.allowsMarkType(t):!1;return n.doc.nodesBetween(l.pos,a.pos,(u,d,f)=>{if(c)return!1;if(u.isInline){const h=!f||f.type.allowsMarkType(t),p=!!t.isInSet(u.marks)||!u.marks.some(m=>m.type.excludes(t));c=h&&p}return!c}),c})}const zd=(n,e={})=>({tr:t,state:r,dispatch:i})=>{const{selection:s}=t,{empty:o,ranges:l}=s,a=We(n,r.schema);if(i)if(o){const c=Jt(r,a);t.addStoredMark(a.create({...c,...e}))}else l.forEach(c=>{const u=c.$from.pos,d=c.$to.pos;r.doc.nodesBetween(u,d,(f,h)=>{const p=Math.max(h,u),m=Math.min(h+f.nodeSize,d);f.marks.find(y=>y.type===a)?f.marks.forEach(y=>{a===y.type&&t.addMark(p,m,a.create({...y.attrs,...e}))}):t.addMark(p,m,a.create(e))})});return Bd(r,t,a)},Ld=(n,e)=>({tr:t})=>(t.setMeta(n,e),!0),Fd=(n,e={})=>({state:t,dispatch:r,chain:i})=>{const s=q(n,t.schema);return s.isTextblock?i().command(({commands:o})=>Fi(s,e)(t)?!0:o.clearNodes()).command(({state:o})=>Fi(s,e)(o,r)).run():(console.warn('[tiptap warn]: Currently "setNode()" only supports text block nodes.'),!1)},Vd=n=>({tr:e,dispatch:t})=>{if(t){const{doc:r}=e,i=Ye(n,0,r.content.size),s=w.create(r,i);e.setSelection(s)}return!0},$d=n=>({tr:e,dispatch:t})=>{if(t){const{doc:r}=e,{from:i,to:s}=typeof n=="number"?{from:n,to:n}:n,o=O.atStart(r).from,l=O.atEnd(r).to,a=Ye(i,o,l),c=Ye(s,o,l),u=O.create(r,a,c);e.setSelection(u)}return!0},Hd=n=>({state:e,dispatch:t})=>{const r=q(n,e.schema);return xu(r)(e,t)};function Ki(n,e){const t=n.storedMarks||n.selection.$to.parentOffset&&n.selection.$from.marks();if(t){const r=t.filter(i=>e?.includes(i.type.name));n.tr.ensureMarks(r)}}const Wd=({keepMarks:n=!0}={})=>({tr:e,state:t,dispatch:r,editor:i})=>{const{selection:s,doc:o}=e,{$from:l,$to:a}=s,c=i.extensionManager.attributes,u=Xt(c,l.node().type.name,l.node().attrs);if(s instanceof w&&s.node.isBlock)return!l.parentOffset||!ht(o,l.pos)?!1:(r&&(n&&Ki(t,i.extensionManager.splittableMarks),e.split(l.pos).scrollIntoView()),!0);if(!l.parent.isBlock)return!1;if(r){const d=a.parentOffset===a.parent.content.size;s instanceof O&&e.deleteSelection();const f=l.depth===0?void 0:vd(l.node(-1).contentMatchAt(l.indexAfter(-1)));let h=d&&f?[{type:f,attrs:u}]:void 0,p=ht(e.doc,e.mapping.map(l.pos),1,h);if(!h&&!p&&ht(e.doc,e.mapping.map(l.pos),1,f?[{type:f}]:void 0)&&(p=!0,h=f?[{type:f,attrs:u}]:void 0),p&&(e.split(e.mapping.map(l.pos),1,h),f&&!d&&!l.parentOffset&&l.parent.type!==f)){const m=e.mapping.map(l.before()),g=e.doc.resolve(m);l.node(-1).canReplaceWith(g.index(),g.index()+1,f)&&e.setNodeMarkup(e.mapping.map(l.before()),f)}n&&Ki(t,i.extensionManager.splittableMarks),e.scrollIntoView()}return!0},Jd=n=>({tr:e,state:t,dispatch:r,editor:i})=>{var s;const o=q(n,t.schema),{$from:l,$to:a}=t.selection,c=t.selection.node;if(c&&c.isBlock||l.depth<2||!l.sameParent(a))return!1;const u=l.node(-1);if(u.type!==o)return!1;const d=i.extensionManager.attributes;if(l.parent.content.size===0&&l.node(-1).childCount===l.indexAfter(-1)){if(l.depth===2||l.node(-3).type!==o||l.index(-2)!==l.node(-2).childCount-1)return!1;if(r){let g=k.empty;const y=l.index(-1)?1:l.index(-2)?2:3;for(let R=l.depth-y;R>=l.depth-3;R-=1)g=k.from(l.node(R).copy(g));const b=l.indexAfter(-1)<l.node(-2).childCount?1:l.indexAfter(-2)<l.node(-3).childCount?2:3,M=Xt(d,l.node().type.name,l.node().attrs),C=((s=o.contentMatch.defaultType)===null||s===void 0?void 0:s.createAndFill(M))||void 0;g=g.append(k.from(o.createAndFill(null,C)||void 0));const A=l.before(l.depth-(y-1));e.replace(A,l.after(-b),new x(g,4-y,0));let v=-1;e.doc.nodesBetween(A,e.doc.content.size,(R,z)=>{if(v>-1)return!1;R.isTextblock&&R.content.size===0&&(v=z+1)}),v>-1&&e.setSelection(O.near(e.doc.resolve(v))),e.scrollIntoView()}return!0}const f=a.pos===l.end()?u.contentMatchAt(0).defaultType:null,h=Xt(d,u.type.name,u.attrs),p=Xt(d,l.node().type.name,l.node().attrs);e.delete(l.pos,a.pos);const m=f?[{type:o,attrs:h},{type:f,attrs:p}]:[{type:o,attrs:h}];if(!ht(e.doc,l.pos,2))return!1;if(r){const{selection:g,storedMarks:y}=t,{splittableMarks:b}=i.extensionManager,M=y||g.$to.parentOffset&&g.$from.marks();if(e.split(l.pos,2,m).scrollIntoView(),!M||!r)return!0;const C=M.filter(A=>b.includes(A.type.name));e.ensureMarks(C)}return!0},Zn=(n,e)=>{const t=Fr(o=>o.type===e)(n.selection);if(!t)return!0;const r=n.doc.resolve(Math.max(0,t.pos-1)).before(t.depth);if(r===void 0)return!0;const i=n.doc.nodeAt(r);return t.node.type===i?.type&&$e(n.doc,t.pos)&&n.join(t.pos),!0},Qn=(n,e)=>{const t=Fr(o=>o.type===e)(n.selection);if(!t)return!0;const r=n.doc.resolve(t.start).after(t.depth);if(r===void 0)return!0;const i=n.doc.nodeAt(r);return t.node.type===i?.type&&$e(n.doc,r)&&n.join(r),!0},jd=(n,e,t,r={})=>({editor:i,tr:s,state:o,dispatch:l,chain:a,commands:c,can:u})=>{const{extensions:d,splittableMarks:f}=i.extensionManager,h=q(n,o.schema),p=q(e,o.schema),{selection:m,storedMarks:g}=o,{$from:y,$to:b}=m,M=y.blockRange(b),C=g||m.$to.parentOffset&&m.$from.marks();if(!M)return!1;const A=Fr(v=>ji(v.type.name,d))(m);if(M.depth>=1&&A&&M.depth-A.depth<=1){if(A.node.type===h)return c.liftListItem(p);if(ji(A.node.type.name,d)&&h.validContent(A.node.content)&&l)return a().command(()=>(s.setNodeMarkup(A.pos,h),!0)).command(()=>Zn(s,h)).command(()=>Qn(s,h)).run()}return!t||!C||!l?a().command(()=>u().wrapInList(h,r)?!0:c.clearNodes()).wrapInList(h,r).command(()=>Zn(s,h)).command(()=>Qn(s,h)).run():a().command(()=>{const v=u().wrapInList(h,r),R=C.filter(z=>f.includes(z.type.name));return s.ensureMarks(R),v?!0:c.clearNodes()}).wrapInList(h,r).command(()=>Zn(s,h)).command(()=>Qn(s,h)).run()},Kd=(n,e={},t={})=>({state:r,commands:i})=>{const{extendEmptyMarkRange:s=!1}=t,o=We(n,r.schema);return xr(r,o,e)?i.unsetMark(o,{extendEmptyMarkRange:s}):i.setMark(o,e)},qd=(n,e,t={})=>({state:r,commands:i})=>{const s=q(n,r.schema),o=q(e,r.schema);return Ft(r,s,t)?i.setNode(o):i.setNode(s,t)},Ud=(n,e={})=>({state:t,commands:r})=>{const i=q(n,t.schema);return Ft(t,i,e)?r.lift(i):r.wrapIn(i,e)},_d=()=>({state:n,dispatch:e})=>{const t=n.plugins;for(let r=0;r<t.length;r+=1){const i=t[r];let s;if(i.spec.isInputRules&&(s=i.getState(n))){if(e){const o=n.tr,l=s.transform;for(let a=l.steps.length-1;a>=0;a-=1)o.step(l.steps[a].invert(l.docs[a]));if(s.text){const a=o.doc.resolve(s.from).marks();o.replaceWith(s.from,s.to,n.schema.text(s.text,a))}else o.delete(s.from,s.to)}return!0}}return!1},Gd=()=>({tr:n,dispatch:e})=>{const{selection:t}=n,{empty:r,ranges:i}=t;return r||e&&i.forEach(s=>{n.removeMark(s.$from.pos,s.$to.pos)}),!0},Yd=(n,e={})=>({tr:t,state:r,dispatch:i})=>{var s;const{extendEmptyMarkRange:o=!1}=e,{selection:l}=t,a=We(n,r.schema),{$from:c,empty:u,ranges:d}=l;if(!i)return!0;if(u&&o){let{from:f,to:h}=l;const p=(s=c.marks().find(g=>g.type===a))===null||s===void 0?void 0:s.attrs,m=zr(c,a,p);m&&(f=m.from,h=m.to),t.removeMark(f,h,a)}else d.forEach(f=>{t.removeMark(f.$from.pos,f.$to.pos,a)});return t.removeStoredMark(a),!0},Xd=(n,e={})=>({tr:t,state:r,dispatch:i})=>{let s=null,o=null;const l=En(typeof n=="string"?n:n.name,r.schema);return l?(l==="node"&&(s=q(n,r.schema)),l==="mark"&&(o=We(n,r.schema)),i&&t.selection.ranges.forEach(a=>{const c=a.$from.pos,u=a.$to.pos;r.doc.nodesBetween(c,u,(d,f)=>{s&&s===d.type&&t.setNodeMarkup(f,void 0,{...d.attrs,...e}),o&&d.marks.length&&d.marks.forEach(h=>{if(o===h.type){const p=Math.max(f,c),m=Math.min(f+d.nodeSize,u);t.addMark(p,m,o.create({...h.attrs,...e}))}})})}),!0):!1},Zd=(n,e={})=>({state:t,dispatch:r})=>{const i=q(n,t.schema);return pu(i,e)(t,r)},Qd=(n,e={})=>({state:t,dispatch:r})=>{const i=q(n,t.schema);return mu(i,e)(t,r)};var ef=Object.freeze({__proto__:null,blur:Lu,clearContent:Fu,clearNodes:Vu,command:$u,createParagraphNear:Hu,cut:Wu,deleteCurrentNode:Ju,deleteNode:ju,deleteRange:Ku,deleteSelection:qu,enter:Uu,exitCode:_u,extendMarkRange:Yu,first:Xu,focus:Zu,forEach:Qu,insertContent:ed,insertContentAt:rd,joinUp:id,joinDown:sd,joinBackward:od,joinForward:ld,joinItemBackward:ad,joinItemForward:cd,joinTextblockBackward:ud,joinTextblockForward:dd,keyboardShortcut:hd,lift:pd,liftEmptyBlock:md,liftListItem:gd,newlineInCode:yd,resetAttributes:kd,scrollIntoView:bd,selectAll:xd,selectNodeBackward:Sd,selectNodeForward:Md,selectParentNode:Cd,selectTextblockEnd:wd,selectTextblockStart:Td,setContent:Od,setMark:zd,setMeta:Ld,setNode:Fd,setNodeSelection:Vd,setTextSelection:$d,sinkListItem:Hd,splitBlock:Wd,splitListItem:Jd,toggleList:jd,toggleMark:Kd,toggleNode:qd,toggleWrap:Ud,undoInputRule:_d,unsetAllMarks:Gd,unsetMark:Yd,updateAttributes:Xd,wrapIn:Zd,wrapInList:Qd});const tf=W.create({name:"commands",addCommands(){return{...ef}}}),nf=W.create({name:"editable",addProseMirrorPlugins(){return[new le({key:new Oe("editable"),props:{editable:()=>this.editor.options.editable}})]}}),rf=W.create({name:"focusEvents",addProseMirrorPlugins(){const{editor:n}=this;return[new le({key:new Oe("focusEvents"),props:{handleDOMEvents:{focus:(e,t)=>{n.isFocused=!0;const r=n.state.tr.setMeta("focus",{event:t}).setMeta("addToHistory",!1);return e.dispatch(r),!1},blur:(e,t)=>{n.isFocused=!1;const r=n.state.tr.setMeta("blur",{event:t}).setMeta("addToHistory",!1);return e.dispatch(r),!1}}}})]}}),sf=W.create({name:"keymap",addKeyboardShortcuts(){const n=()=>this.editor.commands.first(({commands:o})=>[()=>o.undoInputRule(),()=>o.command(({tr:l})=>{const{selection:a,doc:c}=l,{empty:u,$anchor:d}=a,{pos:f,parent:h}=d,p=d.parent.isTextblock&&f>0?l.doc.resolve(f-1):d,m=p.parent.type.spec.isolating,g=d.pos-d.parentOffset,y=m&&p.parent.childCount===1?g===d.pos:E.atStart(c).from===f;return!u||!h.type.isTextblock||h.textContent.length||!y||y&&d.parent.type.name==="paragraph"?!1:o.clearNodes()}),()=>o.deleteSelection(),()=>o.joinBackward(),()=>o.selectNodeBackward()]),e=()=>this.editor.commands.first(({commands:o})=>[()=>o.deleteSelection(),()=>o.deleteCurrentNode(),()=>o.joinForward(),()=>o.selectNodeForward()]),r={Enter:()=>this.editor.commands.first(({commands:o})=>[()=>o.newlineInCode(),()=>o.createParagraphNear(),()=>o.liftEmptyBlock(),()=>o.splitBlock()]),"Mod-Enter":()=>this.editor.commands.exitCode(),Backspace:n,"Mod-Backspace":n,"Shift-Backspace":n,Delete:e,"Mod-Delete":e,"Mod-a":()=>this.editor.commands.selectAll()},i={...r},s={...r,"Ctrl-h":n,"Alt-Backspace":n,"Ctrl-d":e,"Ctrl-Alt-Backspace":e,"Alt-Delete":e,"Alt-d":e,"Ctrl-a":()=>this.editor.commands.selectTextblockStart(),"Ctrl-e":()=>this.editor.commands.selectTextblockEnd()};return Lr()||Io()?s:i},addProseMirrorPlugins(){return[new le({key:new Oe("clearDocument"),appendTransaction:(n,e,t)=>{if(!(n.some(p=>p.docChanged)&&!e.doc.eq(t.doc)))return;const{empty:i,from:s,to:o}=e.selection,l=E.atStart(e.doc).from,a=E.atEnd(e.doc).to;if(i||!(s===l&&o===a)||!(t.doc.textBetween(0,t.doc.content.size," "," ").length===0))return;const d=t.tr,f=wn({state:t,transaction:d}),{commands:h}=new Tn({editor:this.editor,state:f});if(h.clearNodes(),!!d.steps.length)return d}})]}}),of=W.create({name:"tabindex",addProseMirrorPlugins(){return[new le({key:new Oe("tabindex"),props:{attributes:this.editor.isEditable?{tabindex:"0"}:{}}})]}});class qe{constructor(e,t,r=!1,i=null){this.currentNode=null,this.actualDepth=null,this.isBlock=r,this.resolvedPos=e,this.editor=t,this.currentNode=i}get name(){return this.node.type.name}get node(){return this.currentNode||this.resolvedPos.node()}get element(){return this.editor.view.domAtPos(this.pos).node}get depth(){var e;return(e=this.actualDepth)!==null&&e!==void 0?e:this.resolvedPos.depth}get pos(){return this.resolvedPos.pos}get content(){return this.node.content}set content(e){let t=this.from,r=this.to;if(this.isBlock){if(this.content.size===0){console.error(`You can’t set content on a block node. Tried to set content on ${this.name} at ${this.pos}`);return}t=this.from+1,r=this.to-1}this.editor.commands.insertContentAt({from:t,to:r},e)}get attributes(){return this.node.attrs}get textContent(){return this.node.textContent}get size(){return this.node.nodeSize}get from(){return this.isBlock?this.pos:this.resolvedPos.start(this.resolvedPos.depth)}get range(){return{from:this.from,to:this.to}}get to(){return this.isBlock?this.pos+this.size:this.resolvedPos.end(this.resolvedPos.depth)+(this.node.isText?0:1)}get parent(){if(this.depth===0)return null;const e=this.resolvedPos.start(this.resolvedPos.depth-1),t=this.resolvedPos.doc.resolve(e);return new qe(t,this.editor)}get before(){let e=this.resolvedPos.doc.resolve(this.from-(this.isBlock?1:2));return e.depth!==this.depth&&(e=this.resolvedPos.doc.resolve(this.from-3)),new qe(e,this.editor)}get after(){let e=this.resolvedPos.doc.resolve(this.to+(this.isBlock?2:1));return e.depth!==this.depth&&(e=this.resolvedPos.doc.resolve(this.to+3)),new qe(e,this.editor)}get children(){const e=[];return this.node.content.forEach((t,r)=>{const i=t.isBlock&&!t.isTextblock,s=this.pos+r+1,o=this.resolvedPos.doc.resolve(s);if(!i&&o.depth<=this.depth)return;const l=new qe(o,this.editor,i,i?t:null);i&&(l.actualDepth=this.depth+1),e.push(new qe(o,this.editor,i,i?t:null))}),e}get firstChild(){return this.children[0]||null}get lastChild(){const e=this.children;return e[e.length-1]||null}closest(e,t={}){let r=null,i=this.parent;for(;i&&!r;){if(i.node.type.name===e)if(Object.keys(t).length>0){const s=i.node.attrs,o=Object.keys(t);for(let l=0;l<o.length;l+=1){const a=o[l];if(s[a]!==t[a])break}}else r=i;i=i.parent}return r}querySelector(e,t={}){return this.querySelectorAll(e,t,!0)[0]||null}querySelectorAll(e,t={},r=!1){let i=[];if(!this.children||this.children.length===0)return i;const s=Object.keys(t);return this.children.forEach(o=>{r&&i.length>0||(o.node.type.name===e&&s.every(a=>t[a]===o.node.attrs[a])&&i.push(o),!(r&&i.length>0)&&(i=i.concat(o.querySelectorAll(e,t,r))))}),i}setAttribute(e){const t=this.editor.state.selection;this.editor.chain().setTextSelection(this.from).updateAttributes(this.node.type.name,e).setTextSelection(t.from).run()}}const lf=`.ProseMirror {
  position: relative;
}

.ProseMirror {
  word-wrap: break-word;
  white-space: pre-wrap;
  white-space: break-spaces;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  font-feature-settings: "liga" 0; /* the above doesn't seem to work in Edge */
}

.ProseMirror [contenteditable="false"] {
  white-space: normal;
}

.ProseMirror [contenteditable="false"] [contenteditable="true"] {
  white-space: pre-wrap;
}

.ProseMirror pre {
  white-space: pre-wrap;
}

img.ProseMirror-separator {
  display: inline !important;
  border: none !important;
  margin: 0 !important;
  width: 1px !important;
  height: 1px !important;
}

.ProseMirror-gapcursor {
  display: none;
  pointer-events: none;
  position: absolute;
  margin: 0;
}

.ProseMirror-gapcursor:after {
  content: "";
  display: block;
  position: absolute;
  top: -2px;
  width: 20px;
  border-top: 1px solid black;
  animation: ProseMirror-cursor-blink 1.1s steps(2, start) infinite;
}

@keyframes ProseMirror-cursor-blink {
  to {
    visibility: hidden;
  }
}

.ProseMirror-hideselection *::selection {
  background: transparent;
}

.ProseMirror-hideselection *::-moz-selection {
  background: transparent;
}

.ProseMirror-hideselection * {
  caret-color: transparent;
}

.ProseMirror-focused .ProseMirror-gapcursor {
  display: block;
}

.tippy-box[data-animation=fade][data-state=hidden] {
  opacity: 0
}`;function af(n,e,t){const r=document.querySelector("style[data-tiptap-style]");if(r!==null)return r;const i=document.createElement("style");return e&&i.setAttribute("nonce",e),i.setAttribute("data-tiptap-style",""),i.innerHTML=n,document.getElementsByTagName("head")[0].appendChild(i),i}let cf=class extends Su{constructor(e={}){super(),this.isFocused=!1,this.extensionStorage={},this.options={element:document.createElement("div"),content:"",injectCSS:!0,injectNonce:void 0,extensions:[],autofocus:!1,editable:!0,editorProps:{},parseOptions:{},coreExtensionOptions:{},enableInputRules:!0,enablePasteRules:!0,enableCoreExtensions:!0,onBeforeCreate:()=>null,onCreate:()=>null,onUpdate:()=>null,onSelectionUpdate:()=>null,onTransaction:()=>null,onFocus:()=>null,onBlur:()=>null,onDestroy:()=>null},this.isCapturingTransaction=!1,this.capturedTransaction=null,this.setOptions(e),this.createExtensionManager(),this.createCommandManager(),this.createSchema(),this.on("beforeCreate",this.options.onBeforeCreate),this.emit("beforeCreate",{editor:this}),this.createView(),this.injectCSS(),this.on("create",this.options.onCreate),this.on("update",this.options.onUpdate),this.on("selectionUpdate",this.options.onSelectionUpdate),this.on("transaction",this.options.onTransaction),this.on("focus",this.options.onFocus),this.on("blur",this.options.onBlur),this.on("destroy",this.options.onDestroy),window.setTimeout(()=>{this.isDestroyed||(this.commands.focus(this.options.autofocus),this.emit("create",{editor:this}))},0)}get storage(){return this.extensionStorage}get commands(){return this.commandManager.commands}chain(){return this.commandManager.chain()}can(){return this.commandManager.can()}injectCSS(){this.options.injectCSS&&document&&(this.css=af(lf,this.options.injectNonce))}setOptions(e={}){this.options={...this.options,...e},!(!this.view||!this.state||this.isDestroyed)&&(this.options.editorProps&&this.view.setProps(this.options.editorProps),this.view.updateState(this.state))}setEditable(e,t=!0){this.setOptions({editable:e}),t&&this.emit("update",{editor:this,transaction:this.state.tr})}get isEditable(){return this.options.editable&&this.view&&this.view.editable}get state(){return this.view.state}registerPlugin(e,t){const r=Oo(t)?t(e,[...this.state.plugins]):[...this.state.plugins,e],i=this.state.reconfigure({plugins:r});this.view.updateState(i)}unregisterPlugin(e){if(this.isDestroyed)return;const t=typeof e=="string"?`${e}$`:e.key,r=this.state.reconfigure({plugins:this.state.plugins.filter(i=>!i.key.startsWith(t))});this.view.updateState(r)}createExtensionManager(){var e,t;const i=[...this.options.enableCoreExtensions?[nf,zu.configure({blockSeparator:(t=(e=this.options.coreExtensionOptions)===null||e===void 0?void 0:e.clipboardTextSerializer)===null||t===void 0?void 0:t.blockSeparator}),tf,rf,sf,of]:[],...this.options.extensions].filter(s=>["extension","node","mark"].includes(s?.type));this.extensionManager=new dt(i,this)}createCommandManager(){this.commandManager=new Tn({editor:this})}createSchema(){this.schema=this.extensionManager.schema}createView(){const e=Ro(this.options.content,this.schema,this.options.parseOptions),t=Ao(e,this.options.autofocus);this.view=new $c(this.options.element,{...this.options.editorProps,dispatchTransaction:this.dispatchTransaction.bind(this),state:ut.create({doc:e,selection:t||void 0})});const r=this.state.reconfigure({plugins:this.extensionManager.plugins});this.view.updateState(r),this.createNodeViews(),this.prependClass();const i=this.view.dom;i.editor=this}createNodeViews(){this.view.setProps({nodeViews:this.extensionManager.nodeViews})}prependClass(){this.view.dom.className=`tiptap ${this.view.dom.className}`}captureTransaction(e){this.isCapturingTransaction=!0,e(),this.isCapturingTransaction=!1;const t=this.capturedTransaction;return this.capturedTransaction=null,t}dispatchTransaction(e){if(this.view.isDestroyed)return;if(this.isCapturingTransaction){if(!this.capturedTransaction){this.capturedTransaction=e;return}e.steps.forEach(o=>{var l;return(l=this.capturedTransaction)===null||l===void 0?void 0:l.step(o)});return}const t=this.state.apply(e),r=!this.state.selection.eq(t.selection);this.view.updateState(t),this.emit("transaction",{editor:this,transaction:e}),r&&this.emit("selectionUpdate",{editor:this,transaction:e});const i=e.getMeta("focus"),s=e.getMeta("blur");i&&this.emit("focus",{editor:this,event:i.event,transaction:e}),s&&this.emit("blur",{editor:this,event:s.event,transaction:e}),!(!e.docChanged||e.getMeta("preventUpdate"))&&this.emit("update",{editor:this,transaction:e})}getAttributes(e){return Id(this.state,e)}isActive(e,t){const r=typeof e=="string"?e:null,i=typeof e=="string"?t:e;return Rd(this.state,r,i)}getJSON(){return this.state.doc.toJSON()}getHTML(){return Ed(this.state.doc.content,this.schema)}getText(e){const{blockSeparator:t=`

`,textSerializers:r={}}=e||{};return Ad(this.state.doc,{blockSeparator:t,textSerializers:{...No(this.schema),...r}})}get isEmpty(){return Pd(this.state.doc)}getCharacterCount(){return console.warn('[tiptap warn]: "editor.getCharacterCount()" is deprecated. Please use "editor.storage.characterCount.characters()" instead.'),this.state.doc.content.size-2}destroy(){this.emit("destroy"),this.view&&this.view.destroy(),this.removeAllListeners()}get isDestroyed(){var e;return!(!((e=this.view)===null||e===void 0)&&e.docView)}$node(e,t){var r;return((r=this.$doc)===null||r===void 0?void 0:r.querySelector(e,t))||null}$nodes(e,t){var r;return((r=this.$doc)===null||r===void 0?void 0:r.querySelectorAll(e,t))||null}$pos(e){const t=this.state.doc.resolve(e);return new qe(t,this)}get $doc(){return this.$pos(0)}};function St(n){return new vn({find:n.find,handler:({state:e,range:t,match:r})=>{const i=N(n.getAttributes,void 0,r);if(i===!1||i===null)return null;const{tr:s}=e,o=r[r.length-1],l=r[0];if(o){const a=l.search(/\S/),c=t.from+l.indexOf(o),u=c+o.length;if(Po(t.from,t.to,e.doc).filter(h=>h.mark.type.excluded.find(m=>m===n.type&&m!==h.mark.type)).filter(h=>h.to>c).length)return null;u<t.to&&s.delete(u,t.to),c>t.from&&s.delete(t.from+a,c);const f=t.from+a+o.length;s.addMark(t.from+a,f,n.type.create(i||{})),s.removeStoredMark(n.type)}}})}function uf(n){return new vn({find:n.find,handler:({state:e,range:t,match:r})=>{const i=N(n.getAttributes,void 0,r)||{},{tr:s}=e,o=t.from;let l=t.to;const a=n.type.create(i);if(r[1]){const c=r[0].lastIndexOf(r[1]);let u=o+c;u>l?u=l:l=u+r[1].length;const d=r[0][r[0].length-1];s.insertText(d,o+r[0].length-1),s.replaceWith(u,l,a)}else r[0]&&s.insert(o-1,n.type.create(i)).delete(s.mapping.map(o),s.mapping.map(l));s.scrollIntoView()}})}function Sr(n){return new vn({find:n.find,handler:({state:e,range:t,match:r})=>{const i=e.doc.resolve(t.from),s=N(n.getAttributes,void 0,r)||{};if(!i.node(-1).canReplaceWith(i.index(-1),i.indexAfter(-1),n.type))return null;e.tr.delete(t.from,t.to).setBlockType(t.from,t.from,n.type,s)}})}function Vt(n){return new vn({find:n.find,handler:({state:e,range:t,match:r,chain:i})=>{const s=N(n.getAttributes,void 0,r)||{},o=e.tr.delete(t.from,t.to),a=o.doc.resolve(t.from).blockRange(),c=a&&Cr(a,n.type,s);if(!c)return null;if(o.wrap(a,c),n.keepMarks&&n.editor){const{selection:d,storedMarks:f}=e,{splittableMarks:h}=n.editor.extensionManager,p=f||d.$to.parentOffset&&d.$from.marks();if(p){const m=p.filter(g=>h.includes(g.type.name));o.ensureMarks(m)}}if(n.keepAttributes){const d=n.type.name==="bulletList"||n.type.name==="orderedList"?"listItem":"taskList";i().updateAttributes(d,s).run()}const u=o.doc.resolve(t.from-1).nodeBefore;u&&u.type===n.type&&$e(o.doc,t.from-1)&&(!n.joinPredicate||n.joinPredicate(r,u))&&o.join(t.from-1)}})}class ue{constructor(e={}){this.type="mark",this.name="mark",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...e},this.name=this.config.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`),this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=N(S(this,"addOptions",{name:this.name}))),this.storage=N(S(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(e={}){return new ue(e)}configure(e={}){const t=this.extend();return t.options=Nn(this.options,e),t.storage=N(S(t,"addStorage",{name:t.name,options:t.options})),t}extend(e={}){const t=new ue({...this.config,...e});return t.parent=this,this.child=t,t.name=e.name?e.name:t.parent.name,e.defaultOptions&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${t.name}".`),t.options=N(S(t,"addOptions",{name:t.name})),t.storage=N(S(t,"addStorage",{name:t.name,options:t.options})),t}static handleExit({editor:e,mark:t}){const{tr:r}=e.state,i=e.state.selection.$from;if(i.pos===i.end()){const o=i.marks();if(!!!o.find(c=>c?.type.name===t.name))return!1;const a=o.find(c=>c?.type.name===t.name);return a&&r.removeStoredMark(a),r.insertText(" ",i.pos),e.view.dispatch(r),!0}return!1}}class Z{constructor(e={}){this.type="node",this.name="node",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...e},this.name=this.config.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`),this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=N(S(this,"addOptions",{name:this.name}))),this.storage=N(S(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(e={}){return new Z(e)}configure(e={}){const t=this.extend();return t.options=Nn(this.options,e),t.storage=N(S(t,"addStorage",{name:t.name,options:t.options})),t}extend(e={}){const t=new Z({...this.config,...e});return t.parent=this,this.child=t,t.name=e.name?e.name:t.parent.name,e.defaultOptions&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${t.name}".`),t.options=N(S(t,"addOptions",{name:t.name})),t.storage=N(S(t,"addStorage",{name:t.name,options:t.options})),t}}function Mt(n){return new Eu({find:n.find,handler:({state:e,range:t,match:r,pasteEvent:i})=>{const s=N(n.getAttributes,void 0,r,i);if(s===!1||s===null)return null;const{tr:o}=e,l=r[r.length-1],a=r[0];let c=t.to;if(l){const u=a.search(/\S/),d=t.from+a.indexOf(l),f=d+l.length;if(Po(t.from,t.to,e.doc).filter(p=>p.mark.type.excluded.find(g=>g===n.type&&g!==p.mark.type)).filter(p=>p.to>d).length)return null;f<t.to&&o.delete(f,t.to),d>t.from&&o.delete(t.from+u,d),c=t.from+u+l.length,o.addMark(t.from+u,c,n.type.create(s||{})),o.removeStoredMark(n.type)}}})}function qi(n){return rl((e,t)=>({get(){return e(),n},set(r){n=r,requestAnimationFrame(()=>{requestAnimationFrame(()=>{t()})})}}))}class df extends cf{constructor(e={}){return super(e),this.vueRenderers=rs(new Map),this.contentComponent=null,this.reactiveState=qi(this.view.state),this.reactiveExtensionStorage=qi(this.extensionStorage),this.on("transaction",()=>{this.reactiveState.value=this.view.state,this.reactiveExtensionStorage.value=this.extensionStorage}),el(this)}get state(){return this.reactiveState?this.reactiveState.value:this.view.state}get storage(){return this.reactiveExtensionStorage?this.reactiveExtensionStorage.value:super.storage}registerPlugin(e,t){super.registerPlugin(e,t),this.reactiveState.value=this.view.state}unregisterPlugin(e){super.unregisterPlugin(e),this.reactiveState.value=this.view.state}}const ff=is({name:"EditorContent",props:{editor:{default:null,type:Object}},setup(n){const e=Ue(),t=sl();return tl(()=>{const r=n.editor;r&&r.options.element&&e.value&&il(()=>{if(!e.value||!r.options.element.firstChild)return;const i=U(e.value);e.value.append(...r.options.element.childNodes),r.contentComponent=t.ctx._,r.setOptions({element:i}),r.createNodeViews()})}),ns(()=>{const r=n.editor;if(!r||(r.isDestroyed||r.view.setProps({nodeViews:{}}),r.contentComponent=null,!r.options.element.firstChild))return;const i=document.createElement("div");i.append(...r.options.element.childNodes),r.setOptions({element:i})}),{rootEl:e}},render(){const n=[];return this.editor&&this.editor.vueRenderers.forEach(e=>{const t=An(nl,{to:e.teleportElement,key:e.id},An(e.component,{ref:e.id,...e.props}));n.push(t)}),An("div",{ref:e=>{this.rootEl=e}},...n)}}),hf=(n={})=>{const e=Qo();return ts(()=>{e.value=new df(n)}),ns(()=>{var t;(t=e.value)===null||t===void 0||t.destroy()}),e},pf=/^\s*>\s$/,mf=Z.create({name:"blockquote",addOptions(){return{HTMLAttributes:{}}},content:"block+",group:"block",defining:!0,parseHTML(){return[{tag:"blockquote"}]},renderHTML({HTMLAttributes:n}){return["blockquote",H(this.options.HTMLAttributes,n),0]},addCommands(){return{setBlockquote:()=>({commands:n})=>n.wrapIn(this.name),toggleBlockquote:()=>({commands:n})=>n.toggleWrap(this.name),unsetBlockquote:()=>({commands:n})=>n.lift(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-b":()=>this.editor.commands.toggleBlockquote()}},addInputRules(){return[Vt({find:pf,type:this.type})]}}),gf=/(?:^|\s)(\*\*(?!\s+\*\*)((?:[^*]+))\*\*(?!\s+\*\*))$/,yf=/(?:^|\s)(\*\*(?!\s+\*\*)((?:[^*]+))\*\*(?!\s+\*\*))/g,kf=/(?:^|\s)(__(?!\s+__)((?:[^_]+))__(?!\s+__))$/,bf=/(?:^|\s)(__(?!\s+__)((?:[^_]+))__(?!\s+__))/g,xf=ue.create({name:"bold",addOptions(){return{HTMLAttributes:{}}},parseHTML(){return[{tag:"strong"},{tag:"b",getAttrs:n=>n.style.fontWeight!=="normal"&&null},{style:"font-weight",getAttrs:n=>/^(bold(er)?|[5-9]\d{2,})$/.test(n)&&null}]},renderHTML({HTMLAttributes:n}){return["strong",H(this.options.HTMLAttributes,n),0]},addCommands(){return{setBold:()=>({commands:n})=>n.setMark(this.name),toggleBold:()=>({commands:n})=>n.toggleMark(this.name),unsetBold:()=>({commands:n})=>n.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-b":()=>this.editor.commands.toggleBold(),"Mod-B":()=>this.editor.commands.toggleBold()}},addInputRules(){return[St({find:gf,type:this.type}),St({find:kf,type:this.type})]},addPasteRules(){return[Mt({find:yf,type:this.type}),Mt({find:bf,type:this.type})]}}),Sf=Z.create({name:"listItem",addOptions(){return{HTMLAttributes:{},bulletListTypeName:"bulletList",orderedListTypeName:"orderedList"}},content:"paragraph block*",defining:!0,parseHTML(){return[{tag:"li"}]},renderHTML({HTMLAttributes:n}){return["li",H(this.options.HTMLAttributes,n),0]},addKeyboardShortcuts(){return{Enter:()=>this.editor.commands.splitListItem(this.name),Tab:()=>this.editor.commands.sinkListItem(this.name),"Shift-Tab":()=>this.editor.commands.liftListItem(this.name)}}}),Ui=ue.create({name:"textStyle",addOptions(){return{HTMLAttributes:{}}},parseHTML(){return[{tag:"span",getAttrs:n=>n.hasAttribute("style")?{}:!1}]},renderHTML({HTMLAttributes:n}){return["span",H(this.options.HTMLAttributes,n),0]},addCommands(){return{removeEmptyTextStyle:()=>({state:n,commands:e})=>{const t=Jt(n,this.type);return Object.entries(t).some(([,i])=>!!i)?!0:e.unsetMark(this.name)}}}}),_i=/^\s*([-+*])\s$/,Mf=Z.create({name:"bulletList",addOptions(){return{itemTypeName:"listItem",HTMLAttributes:{},keepMarks:!1,keepAttributes:!1}},group:"block list",content(){return`${this.options.itemTypeName}+`},parseHTML(){return[{tag:"ul"}]},renderHTML({HTMLAttributes:n}){return["ul",H(this.options.HTMLAttributes,n),0]},addCommands(){return{toggleBulletList:()=>({commands:n,chain:e})=>this.options.keepAttributes?e().toggleList(this.name,this.options.itemTypeName,this.options.keepMarks).updateAttributes(Sf.name,this.editor.getAttributes(Ui.name)).run():n.toggleList(this.name,this.options.itemTypeName,this.options.keepMarks)}},addKeyboardShortcuts(){return{"Mod-Shift-8":()=>this.editor.commands.toggleBulletList()}},addInputRules(){let n=Vt({find:_i,type:this.type});return(this.options.keepMarks||this.options.keepAttributes)&&(n=Vt({find:_i,type:this.type,keepMarks:this.options.keepMarks,keepAttributes:this.options.keepAttributes,getAttributes:()=>this.editor.getAttributes(Ui.name),editor:this.editor})),[n]}}),Cf=/(?:^|\s)(`(?!\s+`)((?:[^`]+))`(?!\s+`))$/,wf=/(?:^|\s)(`(?!\s+`)((?:[^`]+))`(?!\s+`))/g,Tf=ue.create({name:"code",addOptions(){return{HTMLAttributes:{}}},excludes:"_",code:!0,exitable:!0,parseHTML(){return[{tag:"code"}]},renderHTML({HTMLAttributes:n}){return["code",H(this.options.HTMLAttributes,n),0]},addCommands(){return{setCode:()=>({commands:n})=>n.setMark(this.name),toggleCode:()=>({commands:n})=>n.toggleMark(this.name),unsetCode:()=>({commands:n})=>n.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-e":()=>this.editor.commands.toggleCode()}},addInputRules(){return[St({find:Cf,type:this.type})]},addPasteRules(){return[Mt({find:wf,type:this.type})]}}),Of=/^```([a-z]+)?[\s\n]$/,vf=/^~~~([a-z]+)?[\s\n]$/,Nf=Z.create({name:"codeBlock",addOptions(){return{languageClassPrefix:"language-",exitOnTripleEnter:!0,exitOnArrowDown:!0,HTMLAttributes:{}}},content:"text*",marks:"",group:"block",code:!0,defining:!0,addAttributes(){return{language:{default:null,parseHTML:n=>{var e;const{languageClassPrefix:t}=this.options,s=[...((e=n.firstElementChild)===null||e===void 0?void 0:e.classList)||[]].filter(o=>o.startsWith(t)).map(o=>o.replace(t,""))[0];return s||null},rendered:!1}}},parseHTML(){return[{tag:"pre",preserveWhitespace:"full"}]},renderHTML({node:n,HTMLAttributes:e}){return["pre",H(this.options.HTMLAttributes,e),["code",{class:n.attrs.language?this.options.languageClassPrefix+n.attrs.language:null},0]]},addCommands(){return{setCodeBlock:n=>({commands:e})=>e.setNode(this.name,n),toggleCodeBlock:n=>({commands:e})=>e.toggleNode(this.name,"paragraph",n)}},addKeyboardShortcuts(){return{"Mod-Alt-c":()=>this.editor.commands.toggleCodeBlock(),Backspace:()=>{const{empty:n,$anchor:e}=this.editor.state.selection,t=e.pos===1;return!n||e.parent.type.name!==this.name?!1:t||!e.parent.textContent.length?this.editor.commands.clearNodes():!1},Enter:({editor:n})=>{if(!this.options.exitOnTripleEnter)return!1;const{state:e}=n,{selection:t}=e,{$from:r,empty:i}=t;if(!i||r.parent.type!==this.type)return!1;const s=r.parentOffset===r.parent.nodeSize-2,o=r.parent.textContent.endsWith(`

`);return!s||!o?!1:n.chain().command(({tr:l})=>(l.delete(r.pos-2,r.pos),!0)).exitCode().run()},ArrowDown:({editor:n})=>{if(!this.options.exitOnArrowDown)return!1;const{state:e}=n,{selection:t,doc:r}=e,{$from:i,empty:s}=t;if(!s||i.parent.type!==this.type||!(i.parentOffset===i.parent.nodeSize-2))return!1;const l=i.after();return l===void 0||r.nodeAt(l)?!1:n.commands.exitCode()}}},addInputRules(){return[Sr({find:Of,type:this.type,getAttributes:n=>({language:n[1]})}),Sr({find:vf,type:this.type,getAttributes:n=>({language:n[1]})})]},addProseMirrorPlugins(){return[new le({key:new Oe("codeBlockVSCodeHandler"),props:{handlePaste:(n,e)=>{if(!e.clipboardData||this.editor.isActive(this.type.name))return!1;const t=e.clipboardData.getData("text/plain"),r=e.clipboardData.getData("vscode-editor-data"),i=r?JSON.parse(r):void 0,s=i?.mode;if(!t||!s)return!1;const{tr:o}=n.state;return n.state.selection.from===n.state.doc.nodeSize-(1+n.state.selection.$to.depth*2)?o.insert(n.state.selection.from-1,this.type.create({language:s})):o.replaceSelectionWith(this.type.create({language:s})),o.setSelection(O.near(o.doc.resolve(Math.max(0,o.selection.from-2)))),o.insertText(t.replace(/\r\n?/g,`
`)),o.setMeta("paste",!0),n.dispatch(o),!0}}})]}}),Ef=Z.create({name:"doc",topNode:!0,content:"block+"});function Af(n={}){return new le({view(e){return new Df(e,n)}})}class Df{constructor(e,t){var r;this.editorView=e,this.cursorPos=null,this.element=null,this.timeout=-1,this.width=(r=t.width)!==null&&r!==void 0?r:1,this.color=t.color===!1?void 0:t.color||"black",this.class=t.class,this.handlers=["dragover","dragend","drop","dragleave"].map(i=>{let s=o=>{this[i](o)};return e.dom.addEventListener(i,s),{name:i,handler:s}})}destroy(){this.handlers.forEach(({name:e,handler:t})=>this.editorView.dom.removeEventListener(e,t))}update(e,t){this.cursorPos!=null&&t.doc!=e.state.doc&&(this.cursorPos>e.state.doc.content.size?this.setCursor(null):this.updateOverlay())}setCursor(e){e!=this.cursorPos&&(this.cursorPos=e,e==null?(this.element.parentNode.removeChild(this.element),this.element=null):this.updateOverlay())}updateOverlay(){let e=this.editorView.state.doc.resolve(this.cursorPos),t=!e.parent.inlineContent,r;if(t){let l=e.nodeBefore,a=e.nodeAfter;if(l||a){let c=this.editorView.nodeDOM(this.cursorPos-(l?l.nodeSize:0));if(c){let u=c.getBoundingClientRect(),d=l?u.bottom:u.top;l&&a&&(d=(d+this.editorView.nodeDOM(this.cursorPos).getBoundingClientRect().top)/2),r={left:u.left,right:u.right,top:d-this.width/2,bottom:d+this.width/2}}}}if(!r){let l=this.editorView.coordsAtPos(this.cursorPos);r={left:l.left-this.width/2,right:l.left+this.width/2,top:l.top,bottom:l.bottom}}let i=this.editorView.dom.offsetParent;this.element||(this.element=i.appendChild(document.createElement("div")),this.class&&(this.element.className=this.class),this.element.style.cssText="position: absolute; z-index: 50; pointer-events: none;",this.color&&(this.element.style.backgroundColor=this.color)),this.element.classList.toggle("prosemirror-dropcursor-block",t),this.element.classList.toggle("prosemirror-dropcursor-inline",!t);let s,o;if(!i||i==document.body&&getComputedStyle(i).position=="static")s=-pageXOffset,o=-pageYOffset;else{let l=i.getBoundingClientRect();s=l.left-i.scrollLeft,o=l.top-i.scrollTop}this.element.style.left=r.left-s+"px",this.element.style.top=r.top-o+"px",this.element.style.width=r.right-r.left+"px",this.element.style.height=r.bottom-r.top+"px"}scheduleRemoval(e){clearTimeout(this.timeout),this.timeout=setTimeout(()=>this.setCursor(null),e)}dragover(e){if(!this.editorView.editable)return;let t=this.editorView.posAtCoords({left:e.clientX,top:e.clientY}),r=t&&t.inside>=0&&this.editorView.state.doc.nodeAt(t.inside),i=r&&r.type.spec.disableDropCursor,s=typeof i=="function"?i(this.editorView,t,e):i;if(t&&!s){let o=t.pos;if(this.editorView.dragging&&this.editorView.dragging.slice){let l=Ds(this.editorView.state.doc,o,this.editorView.dragging.slice);l!=null&&(o=l)}this.setCursor(o),this.scheduleRemoval(5e3)}}dragend(){this.scheduleRemoval(20)}drop(){this.scheduleRemoval(20)}dragleave(e){(e.target==this.editorView.dom||!this.editorView.dom.contains(e.relatedTarget))&&this.setCursor(null)}}const If=W.create({name:"dropCursor",addOptions(){return{color:"currentColor",width:1,class:void 0}},addProseMirrorPlugins(){return[Af(this.options)]}});class L extends E{constructor(e){super(e,e)}map(e,t){let r=e.resolve(t.map(this.head));return L.valid(r)?new L(r):E.near(r)}content(){return x.empty}eq(e){return e instanceof L&&e.head==this.head}toJSON(){return{type:"gapcursor",pos:this.head}}static fromJSON(e,t){if(typeof t.pos!="number")throw new RangeError("Invalid input for GapCursor.fromJSON");return new L(e.resolve(t.pos))}getBookmark(){return new Vr(this.anchor)}static valid(e){let t=e.parent;if(t.isTextblock||!Rf(e)||!Pf(e))return!1;let r=t.type.spec.allowGapCursor;if(r!=null)return r;let i=t.contentMatchAt(e.index()).defaultType;return i&&i.isTextblock}static findGapCursorFrom(e,t,r=!1){e:for(;;){if(!r&&L.valid(e))return e;let i=e.pos,s=null;for(let o=e.depth;;o--){let l=e.node(o);if(t>0?e.indexAfter(o)<l.childCount:e.index(o)>0){s=l.child(t>0?e.indexAfter(o):e.index(o)-1);break}else if(o==0)return null;i+=t;let a=e.doc.resolve(i);if(L.valid(a))return a}for(;;){let o=t>0?s.firstChild:s.lastChild;if(!o){if(s.isAtom&&!s.isText&&!w.isSelectable(s)){e=e.doc.resolve(i+s.nodeSize*t),r=!1;continue e}break}s=o,i+=t;let l=e.doc.resolve(i);if(L.valid(l))return l}return null}}}L.prototype.visible=!1;L.findFrom=L.findGapCursorFrom;E.jsonID("gapcursor",L);class Vr{constructor(e){this.pos=e}map(e){return new Vr(e.map(this.pos))}resolve(e){let t=e.resolve(this.pos);return L.valid(t)?new L(t):E.near(t)}}function Rf(n){for(let e=n.depth;e>=0;e--){let t=n.index(e),r=n.node(e);if(t==0){if(r.type.spec.isolating)return!0;continue}for(let i=r.child(t-1);;i=i.lastChild){if(i.childCount==0&&!i.inlineContent||i.isAtom||i.type.spec.isolating)return!0;if(i.inlineContent)return!1}}return!0}function Pf(n){for(let e=n.depth;e>=0;e--){let t=n.indexAfter(e),r=n.node(e);if(t==r.childCount){if(r.type.spec.isolating)return!0;continue}for(let i=r.child(t);;i=i.firstChild){if(i.childCount==0&&!i.inlineContent||i.isAtom||i.type.spec.isolating)return!0;if(i.inlineContent)return!1}}return!0}function Bf(){return new le({props:{decorations:Vf,createSelectionBetween(n,e,t){return e.pos==t.pos&&L.valid(t)?new L(t):null},handleClick:Lf,handleKeyDown:zf,handleDOMEvents:{beforeinput:Ff}}})}const zf=ko({ArrowLeft:Gt("horiz",-1),ArrowRight:Gt("horiz",1),ArrowUp:Gt("vert",-1),ArrowDown:Gt("vert",1)});function Gt(n,e){const t=n=="vert"?e>0?"down":"up":e>0?"right":"left";return function(r,i,s){let o=r.selection,l=e>0?o.$to:o.$from,a=o.empty;if(o instanceof O){if(!s.endOfTextblock(t)||l.depth==0)return!1;a=!1,l=r.doc.resolve(e>0?l.after():l.before())}let c=L.findGapCursorFrom(l,e,a);return c?(i&&i(r.tr.setSelection(new L(c))),!0):!1}}function Lf(n,e,t){if(!n||!n.editable)return!1;let r=n.state.doc.resolve(e);if(!L.valid(r))return!1;let i=n.posAtCoords({left:t.clientX,top:t.clientY});return i&&i.inside>-1&&w.isSelectable(n.state.doc.nodeAt(i.inside))?!1:(n.dispatch(n.state.tr.setSelection(new L(r))),!0)}function Ff(n,e){if(e.inputType!="insertCompositionText"||!(n.state.selection instanceof L))return!1;let{$from:t}=n.state.selection,r=t.parent.contentMatchAt(t.index()).findWrapping(n.state.schema.nodes.text);if(!r)return!1;let i=k.empty;for(let o=r.length-1;o>=0;o--)i=k.from(r[o].createAndFill(null,i));let s=n.state.tr.replace(t.pos,t.pos,new x(i,0,0));return s.setSelection(O.near(s.doc.resolve(t.pos+1))),n.dispatch(s),!1}function Vf(n){if(!(n.selection instanceof L))return null;let e=document.createElement("div");return e.className="ProseMirror-gapcursor",V.create(n.doc,[oe.widget(n.selection.head,e,{key:"gapcursor"})])}const $f=W.create({name:"gapCursor",addProseMirrorPlugins(){return[Bf()]},extendNodeSchema(n){var e;const t={name:n.name,options:n.options,storage:n.storage};return{allowGapCursor:(e=N(S(n,"allowGapCursor",t)))!==null&&e!==void 0?e:null}}}),Hf=Z.create({name:"hardBreak",addOptions(){return{keepMarks:!0,HTMLAttributes:{}}},inline:!0,group:"inline",selectable:!1,parseHTML(){return[{tag:"br"}]},renderHTML({HTMLAttributes:n}){return["br",H(this.options.HTMLAttributes,n)]},renderText(){return`
`},addCommands(){return{setHardBreak:()=>({commands:n,chain:e,state:t,editor:r})=>n.first([()=>n.exitCode(),()=>n.command(()=>{const{selection:i,storedMarks:s}=t;if(i.$from.parent.type.spec.isolating)return!1;const{keepMarks:o}=this.options,{splittableMarks:l}=r.extensionManager,a=s||i.$to.parentOffset&&i.$from.marks();return e().insertContent({type:this.name}).command(({tr:c,dispatch:u})=>{if(u&&a&&o){const d=a.filter(f=>l.includes(f.type.name));c.ensureMarks(d)}return!0}).run()})])}},addKeyboardShortcuts(){return{"Mod-Enter":()=>this.editor.commands.setHardBreak(),"Shift-Enter":()=>this.editor.commands.setHardBreak()}}}),Wf=Z.create({name:"heading",addOptions(){return{levels:[1,2,3,4,5,6],HTMLAttributes:{}}},content:"inline*",group:"block",defining:!0,addAttributes(){return{level:{default:1,rendered:!1}}},parseHTML(){return this.options.levels.map(n=>({tag:`h${n}`,attrs:{level:n}}))},renderHTML({node:n,HTMLAttributes:e}){return[`h${this.options.levels.includes(n.attrs.level)?n.attrs.level:this.options.levels[0]}`,H(this.options.HTMLAttributes,e),0]},addCommands(){return{setHeading:n=>({commands:e})=>this.options.levels.includes(n.level)?e.setNode(this.name,n):!1,toggleHeading:n=>({commands:e})=>this.options.levels.includes(n.level)?e.toggleNode(this.name,"paragraph",n):!1}},addKeyboardShortcuts(){return this.options.levels.reduce((n,e)=>({...n,[`Mod-Alt-${e}`]:()=>this.editor.commands.toggleHeading({level:e})}),{})},addInputRules(){return this.options.levels.map(n=>Sr({find:new RegExp(`^(#{1,${n}})\\s$`),type:this.type,getAttributes:{level:n}}))}});var pn=200,K=function(){};K.prototype.append=function(e){return e.length?(e=K.from(e),!this.length&&e||e.length<pn&&this.leafAppend(e)||this.length<pn&&e.leafPrepend(this)||this.appendInner(e)):this};K.prototype.prepend=function(e){return e.length?K.from(e).append(this):this};K.prototype.appendInner=function(e){return new Jf(this,e)};K.prototype.slice=function(e,t){return e===void 0&&(e=0),t===void 0&&(t=this.length),e>=t?K.empty:this.sliceInner(Math.max(0,e),Math.min(this.length,t))};K.prototype.get=function(e){if(!(e<0||e>=this.length))return this.getInner(e)};K.prototype.forEach=function(e,t,r){t===void 0&&(t=0),r===void 0&&(r=this.length),t<=r?this.forEachInner(e,t,r,0):this.forEachInvertedInner(e,t,r,0)};K.prototype.map=function(e,t,r){t===void 0&&(t=0),r===void 0&&(r=this.length);var i=[];return this.forEach(function(s,o){return i.push(e(s,o))},t,r),i};K.from=function(e){return e instanceof K?e:e&&e.length?new Bo(e):K.empty};var Bo=function(n){function e(r){n.call(this),this.values=r}n&&(e.__proto__=n),e.prototype=Object.create(n&&n.prototype),e.prototype.constructor=e;var t={length:{configurable:!0},depth:{configurable:!0}};return e.prototype.flatten=function(){return this.values},e.prototype.sliceInner=function(i,s){return i==0&&s==this.length?this:new e(this.values.slice(i,s))},e.prototype.getInner=function(i){return this.values[i]},e.prototype.forEachInner=function(i,s,o,l){for(var a=s;a<o;a++)if(i(this.values[a],l+a)===!1)return!1},e.prototype.forEachInvertedInner=function(i,s,o,l){for(var a=s-1;a>=o;a--)if(i(this.values[a],l+a)===!1)return!1},e.prototype.leafAppend=function(i){if(this.length+i.length<=pn)return new e(this.values.concat(i.flatten()))},e.prototype.leafPrepend=function(i){if(this.length+i.length<=pn)return new e(i.flatten().concat(this.values))},t.length.get=function(){return this.values.length},t.depth.get=function(){return 0},Object.defineProperties(e.prototype,t),e}(K);K.empty=new Bo([]);var Jf=function(n){function e(t,r){n.call(this),this.left=t,this.right=r,this.length=t.length+r.length,this.depth=Math.max(t.depth,r.depth)+1}return n&&(e.__proto__=n),e.prototype=Object.create(n&&n.prototype),e.prototype.constructor=e,e.prototype.flatten=function(){return this.left.flatten().concat(this.right.flatten())},e.prototype.getInner=function(r){return r<this.left.length?this.left.get(r):this.right.get(r-this.left.length)},e.prototype.forEachInner=function(r,i,s,o){var l=this.left.length;if(i<l&&this.left.forEachInner(r,i,Math.min(s,l),o)===!1||s>l&&this.right.forEachInner(r,Math.max(i-l,0),Math.min(this.length,s)-l,o+l)===!1)return!1},e.prototype.forEachInvertedInner=function(r,i,s,o){var l=this.left.length;if(i>l&&this.right.forEachInvertedInner(r,i-l,Math.max(s,l)-l,o+l)===!1||s<l&&this.left.forEachInvertedInner(r,Math.min(i,l),s,o)===!1)return!1},e.prototype.sliceInner=function(r,i){if(r==0&&i==this.length)return this;var s=this.left.length;return i<=s?this.left.slice(r,i):r>=s?this.right.slice(r-s,i-s):this.left.slice(r,s).append(this.right.slice(0,i-s))},e.prototype.leafAppend=function(r){var i=this.right.leafAppend(r);if(i)return new e(this.left,i)},e.prototype.leafPrepend=function(r){var i=this.left.leafPrepend(r);if(i)return new e(i,this.right)},e.prototype.appendInner=function(r){return this.left.depth>=Math.max(this.right.depth,r.depth)+1?new e(this.left,new e(this.right,r)):new e(this,r)},e}(K);const jf=500;class fe{constructor(e,t){this.items=e,this.eventCount=t}popEvent(e,t){if(this.eventCount==0)return null;let r=this.items.length;for(;;r--)if(this.items.get(r-1).selection){--r;break}let i,s;t&&(i=this.remapping(r,this.items.length),s=i.maps.length);let o=e.tr,l,a,c=[],u=[];return this.items.forEach((d,f)=>{if(!d.step){i||(i=this.remapping(r,f+1),s=i.maps.length),s--,u.push(d);return}if(i){u.push(new me(d.map));let h=d.step.map(i.slice(s)),p;h&&o.maybeStep(h).doc&&(p=o.mapping.maps[o.mapping.maps.length-1],c.push(new me(p,void 0,void 0,c.length+u.length))),s--,p&&i.appendMap(p,s)}else o.maybeStep(d.step);if(d.selection)return l=i?d.selection.map(i.slice(s)):d.selection,a=new fe(this.items.slice(0,r).append(u.reverse().concat(c)),this.eventCount-1),!1},this.items.length,0),{remaining:a,transform:o,selection:l}}addTransform(e,t,r,i){let s=[],o=this.eventCount,l=this.items,a=!i&&l.length?l.get(l.length-1):null;for(let u=0;u<e.steps.length;u++){let d=e.steps[u].invert(e.docs[u]),f=new me(e.mapping.maps[u],d,t),h;(h=a&&a.merge(f))&&(f=h,u?s.pop():l=l.slice(0,l.length-1)),s.push(f),t&&(o++,t=void 0),i||(a=f)}let c=o-r.depth;return c>qf&&(l=Kf(l,c),o-=c),new fe(l.append(s),o)}remapping(e,t){let r=new ft;return this.items.forEach((i,s)=>{let o=i.mirrorOffset!=null&&s-i.mirrorOffset>=e?r.maps.length-i.mirrorOffset:void 0;r.appendMap(i.map,o)},e,t),r}addMaps(e){return this.eventCount==0?this:new fe(this.items.append(e.map(t=>new me(t))),this.eventCount)}rebased(e,t){if(!this.eventCount)return this;let r=[],i=Math.max(0,this.items.length-t),s=e.mapping,o=e.steps.length,l=this.eventCount;this.items.forEach(f=>{f.selection&&l--},i);let a=t;this.items.forEach(f=>{let h=s.getMirror(--a);if(h==null)return;o=Math.min(o,h);let p=s.maps[h];if(f.step){let m=e.steps[h].invert(e.docs[h]),g=f.selection&&f.selection.map(s.slice(a+1,h));g&&l++,r.push(new me(p,m,g))}else r.push(new me(p))},i);let c=[];for(let f=t;f<o;f++)c.push(new me(s.maps[f]));let u=this.items.slice(0,i).append(c).append(r),d=new fe(u,l);return d.emptyItemCount()>jf&&(d=d.compress(this.items.length-r.length)),d}emptyItemCount(){let e=0;return this.items.forEach(t=>{t.step||e++}),e}compress(e=this.items.length){let t=this.remapping(0,e),r=t.maps.length,i=[],s=0;return this.items.forEach((o,l)=>{if(l>=e)i.push(o),o.selection&&s++;else if(o.step){let a=o.step.map(t.slice(r)),c=a&&a.getMap();if(r--,c&&t.appendMap(c,r),a){let u=o.selection&&o.selection.map(t.slice(r));u&&s++;let d=new me(c.invert(),a,u),f,h=i.length-1;(f=i.length&&i[h].merge(d))?i[h]=f:i.push(d)}}else o.map&&r--},this.items.length,0),new fe(K.from(i.reverse()),s)}}fe.empty=new fe(K.empty,0);function Kf(n,e){let t;return n.forEach((r,i)=>{if(r.selection&&e--==0)return t=i,!1}),n.slice(t)}class me{constructor(e,t,r,i){this.map=e,this.step=t,this.selection=r,this.mirrorOffset=i}merge(e){if(this.step&&e.step&&!e.selection){let t=e.step.merge(this.step);if(t)return new me(t.getMap().invert(),t,this.selection)}}}class Ae{constructor(e,t,r,i,s){this.done=e,this.undone=t,this.prevRanges=r,this.prevTime=i,this.prevComposition=s}}const qf=20;function Uf(n,e,t,r){let i=t.getMeta(nt),s;if(i)return i.historyState;t.getMeta(Yf)&&(n=new Ae(n.done,n.undone,null,0,-1));let o=t.getMeta("appendedTransaction");if(t.steps.length==0)return n;if(o&&o.getMeta(nt))return o.getMeta(nt).redo?new Ae(n.done.addTransform(t,void 0,r,Zt(e)),n.undone,Gi(t.mapping.maps[t.steps.length-1]),n.prevTime,n.prevComposition):new Ae(n.done,n.undone.addTransform(t,void 0,r,Zt(e)),null,n.prevTime,n.prevComposition);if(t.getMeta("addToHistory")!==!1&&!(o&&o.getMeta("addToHistory")===!1)){let l=t.getMeta("composition"),a=n.prevTime==0||!o&&n.prevComposition!=l&&(n.prevTime<(t.time||0)-r.newGroupDelay||!_f(t,n.prevRanges)),c=o?er(n.prevRanges,t.mapping):Gi(t.mapping.maps[t.steps.length-1]);return new Ae(n.done.addTransform(t,a?e.selection.getBookmark():void 0,r,Zt(e)),fe.empty,c,t.time,l??n.prevComposition)}else return(s=t.getMeta("rebased"))?new Ae(n.done.rebased(t,s),n.undone.rebased(t,s),er(n.prevRanges,t.mapping),n.prevTime,n.prevComposition):new Ae(n.done.addMaps(t.mapping.maps),n.undone.addMaps(t.mapping.maps),er(n.prevRanges,t.mapping),n.prevTime,n.prevComposition)}function _f(n,e){if(!e)return!1;if(!n.docChanged)return!0;let t=!1;return n.mapping.maps[0].forEach((r,i)=>{for(let s=0;s<e.length;s+=2)r<=e[s+1]&&i>=e[s]&&(t=!0)}),t}function Gi(n){let e=[];return n.forEach((t,r,i,s)=>e.push(i,s)),e}function er(n,e){if(!n)return null;let t=[];for(let r=0;r<n.length;r+=2){let i=e.map(n[r],1),s=e.map(n[r+1],-1);i<=s&&t.push(i,s)}return t}function Gf(n,e,t){let r=Zt(e),i=nt.get(e).spec.config,s=(t?n.undone:n.done).popEvent(e,r);if(!s)return null;let o=s.selection.resolve(s.transform.doc),l=(t?n.done:n.undone).addTransform(s.transform,e.selection.getBookmark(),i,r),a=new Ae(t?l:s.remaining,t?s.remaining:l,null,0,-1);return s.transform.setSelection(o).setMeta(nt,{redo:t,historyState:a})}let tr=!1,Yi=null;function Zt(n){let e=n.plugins;if(Yi!=e){tr=!1,Yi=e;for(let t=0;t<e.length;t++)if(e[t].spec.historyPreserveItems){tr=!0;break}}return tr}const nt=new Oe("history"),Yf=new Oe("closeHistory");function Xf(n={}){return n={depth:n.depth||100,newGroupDelay:n.newGroupDelay||500},new le({key:nt,state:{init(){return new Ae(fe.empty,fe.empty,null,0,-1)},apply(e,t,r){return Uf(t,r,e,n)}},config:n,props:{handleDOMEvents:{beforeinput(e,t){let r=t.inputType,i=r=="historyUndo"?Lo:r=="historyRedo"?Fo:null;return i?(t.preventDefault(),i(e.state,e.dispatch)):!1}}}})}function zo(n,e){return(t,r)=>{let i=nt.getState(t);if(!i||(n?i.undone:i.done).eventCount==0)return!1;if(r){let s=Gf(i,t,n);s&&r(e?s.scrollIntoView():s)}return!0}}const Lo=zo(!1,!0),Fo=zo(!0,!0),Zf=W.create({name:"history",addOptions(){return{depth:100,newGroupDelay:500}},addCommands(){return{undo:()=>({state:n,dispatch:e})=>Lo(n,e),redo:()=>({state:n,dispatch:e})=>Fo(n,e)}},addProseMirrorPlugins(){return[Xf(this.options)]},addKeyboardShortcuts(){return{"Mod-z":()=>this.editor.commands.undo(),"Shift-Mod-z":()=>this.editor.commands.redo(),"Mod-y":()=>this.editor.commands.redo(),"Mod-я":()=>this.editor.commands.undo(),"Shift-Mod-я":()=>this.editor.commands.redo()}}}),Qf=Z.create({name:"horizontalRule",addOptions(){return{HTMLAttributes:{}}},group:"block",parseHTML(){return[{tag:"hr"}]},renderHTML({HTMLAttributes:n}){return["hr",H(this.options.HTMLAttributes,n)]},addCommands(){return{setHorizontalRule:()=>({chain:n,state:e})=>{const{$to:t}=e.selection,r=n();return t.parentOffset===0?r.insertContentAt(Math.max(t.pos-2,0),{type:this.name}):r.insertContent({type:this.name}),r.command(({tr:i,dispatch:s})=>{var o;if(s){const{$to:l}=i.selection,a=l.end();if(l.nodeAfter)l.nodeAfter.isTextblock?i.setSelection(O.create(i.doc,l.pos+1)):l.nodeAfter.isBlock?i.setSelection(w.create(i.doc,l.pos)):i.setSelection(O.create(i.doc,l.pos));else{const c=(o=l.parent.type.contentMatch.defaultType)===null||o===void 0?void 0:o.create();c&&(i.insert(a,c),i.setSelection(O.create(i.doc,a+1)))}i.scrollIntoView()}return!0}).run()}}},addInputRules(){return[uf({find:/^(?:---|—-|___\s|\*\*\*\s)$/,type:this.type})]}}),eh=/(?:^|\s)(\*(?!\s+\*)((?:[^*]+))\*(?!\s+\*))$/,th=/(?:^|\s)(\*(?!\s+\*)((?:[^*]+))\*(?!\s+\*))/g,nh=/(?:^|\s)(_(?!\s+_)((?:[^_]+))_(?!\s+_))$/,rh=/(?:^|\s)(_(?!\s+_)((?:[^_]+))_(?!\s+_))/g,ih=ue.create({name:"italic",addOptions(){return{HTMLAttributes:{}}},parseHTML(){return[{tag:"em"},{tag:"i",getAttrs:n=>n.style.fontStyle!=="normal"&&null},{style:"font-style=italic"}]},renderHTML({HTMLAttributes:n}){return["em",H(this.options.HTMLAttributes,n),0]},addCommands(){return{setItalic:()=>({commands:n})=>n.setMark(this.name),toggleItalic:()=>({commands:n})=>n.toggleMark(this.name),unsetItalic:()=>({commands:n})=>n.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-i":()=>this.editor.commands.toggleItalic(),"Mod-I":()=>this.editor.commands.toggleItalic()}},addInputRules(){return[St({find:eh,type:this.type}),St({find:nh,type:this.type})]},addPasteRules(){return[Mt({find:th,type:this.type}),Mt({find:rh,type:this.type})]}}),sh=Z.create({name:"listItem",addOptions(){return{HTMLAttributes:{},bulletListTypeName:"bulletList",orderedListTypeName:"orderedList"}},content:"paragraph block*",defining:!0,parseHTML(){return[{tag:"li"}]},renderHTML({HTMLAttributes:n}){return["li",H(this.options.HTMLAttributes,n),0]},addKeyboardShortcuts(){return{Enter:()=>this.editor.commands.splitListItem(this.name),Tab:()=>this.editor.commands.sinkListItem(this.name),"Shift-Tab":()=>this.editor.commands.liftListItem(this.name)}}}),oh=Z.create({name:"listItem",addOptions(){return{HTMLAttributes:{},bulletListTypeName:"bulletList",orderedListTypeName:"orderedList"}},content:"paragraph block*",defining:!0,parseHTML(){return[{tag:"li"}]},renderHTML({HTMLAttributes:n}){return["li",H(this.options.HTMLAttributes,n),0]},addKeyboardShortcuts(){return{Enter:()=>this.editor.commands.splitListItem(this.name),Tab:()=>this.editor.commands.sinkListItem(this.name),"Shift-Tab":()=>this.editor.commands.liftListItem(this.name)}}}),Xi=ue.create({name:"textStyle",addOptions(){return{HTMLAttributes:{}}},parseHTML(){return[{tag:"span",getAttrs:n=>n.hasAttribute("style")?{}:!1}]},renderHTML({HTMLAttributes:n}){return["span",H(this.options.HTMLAttributes,n),0]},addCommands(){return{removeEmptyTextStyle:()=>({state:n,commands:e})=>{const t=Jt(n,this.type);return Object.entries(t).some(([,i])=>!!i)?!0:e.unsetMark(this.name)}}}}),Zi=/^(\d+)\.\s$/,lh=Z.create({name:"orderedList",addOptions(){return{itemTypeName:"listItem",HTMLAttributes:{},keepMarks:!1,keepAttributes:!1}},group:"block list",content(){return`${this.options.itemTypeName}+`},addAttributes(){return{start:{default:1,parseHTML:n=>n.hasAttribute("start")?parseInt(n.getAttribute("start")||"",10):1}}},parseHTML(){return[{tag:"ol"}]},renderHTML({HTMLAttributes:n}){const{start:e,...t}=n;return e===1?["ol",H(this.options.HTMLAttributes,t),0]:["ol",H(this.options.HTMLAttributes,n),0]},addCommands(){return{toggleOrderedList:()=>({commands:n,chain:e})=>this.options.keepAttributes?e().toggleList(this.name,this.options.itemTypeName,this.options.keepMarks).updateAttributes(oh.name,this.editor.getAttributes(Xi.name)).run():n.toggleList(this.name,this.options.itemTypeName,this.options.keepMarks)}},addKeyboardShortcuts(){return{"Mod-Shift-7":()=>this.editor.commands.toggleOrderedList()}},addInputRules(){let n=Vt({find:Zi,type:this.type,getAttributes:e=>({start:+e[1]}),joinPredicate:(e,t)=>t.childCount+t.attrs.start===+e[1]});return(this.options.keepMarks||this.options.keepAttributes)&&(n=Vt({find:Zi,type:this.type,keepMarks:this.options.keepMarks,keepAttributes:this.options.keepAttributes,getAttributes:e=>({start:+e[1],...this.editor.getAttributes(Xi.name)}),joinPredicate:(e,t)=>t.childCount+t.attrs.start===+e[1],editor:this.editor})),[n]}}),ah=Z.create({name:"paragraph",priority:1e3,addOptions(){return{HTMLAttributes:{}}},group:"block",content:"inline*",parseHTML(){return[{tag:"p"}]},renderHTML({HTMLAttributes:n}){return["p",H(this.options.HTMLAttributes,n),0]},addCommands(){return{setParagraph:()=>({commands:n})=>n.setNode(this.name)}},addKeyboardShortcuts(){return{"Mod-Alt-0":()=>this.editor.commands.setParagraph()}}}),ch=/(?:^|\s)(~~(?!\s+~~)((?:[^~]+))~~(?!\s+~~))$/,uh=/(?:^|\s)(~~(?!\s+~~)((?:[^~]+))~~(?!\s+~~))/g,dh=ue.create({name:"strike",addOptions(){return{HTMLAttributes:{}}},parseHTML(){return[{tag:"s"},{tag:"del"},{tag:"strike"},{style:"text-decoration",consuming:!1,getAttrs:n=>n.includes("line-through")?{}:!1}]},renderHTML({HTMLAttributes:n}){return["s",H(this.options.HTMLAttributes,n),0]},addCommands(){return{setStrike:()=>({commands:n})=>n.setMark(this.name),toggleStrike:()=>({commands:n})=>n.toggleMark(this.name),unsetStrike:()=>({commands:n})=>n.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-s":()=>this.editor.commands.toggleStrike()}},addInputRules(){return[St({find:ch,type:this.type})]},addPasteRules(){return[Mt({find:uh,type:this.type})]}}),fh=Z.create({name:"text",group:"inline"}),hh=W.create({name:"starterKit",addExtensions(){var n,e,t,r,i,s,o,l,a,c,u,d,f,h,p,m,g,y;const b=[];return this.options.blockquote!==!1&&b.push(mf.configure((n=this.options)===null||n===void 0?void 0:n.blockquote)),this.options.bold!==!1&&b.push(xf.configure((e=this.options)===null||e===void 0?void 0:e.bold)),this.options.bulletList!==!1&&b.push(Mf.configure((t=this.options)===null||t===void 0?void 0:t.bulletList)),this.options.code!==!1&&b.push(Tf.configure((r=this.options)===null||r===void 0?void 0:r.code)),this.options.codeBlock!==!1&&b.push(Nf.configure((i=this.options)===null||i===void 0?void 0:i.codeBlock)),this.options.document!==!1&&b.push(Ef.configure((s=this.options)===null||s===void 0?void 0:s.document)),this.options.dropcursor!==!1&&b.push(If.configure((o=this.options)===null||o===void 0?void 0:o.dropcursor)),this.options.gapcursor!==!1&&b.push($f.configure((l=this.options)===null||l===void 0?void 0:l.gapcursor)),this.options.hardBreak!==!1&&b.push(Hf.configure((a=this.options)===null||a===void 0?void 0:a.hardBreak)),this.options.heading!==!1&&b.push(Wf.configure((c=this.options)===null||c===void 0?void 0:c.heading)),this.options.history!==!1&&b.push(Zf.configure((u=this.options)===null||u===void 0?void 0:u.history)),this.options.horizontalRule!==!1&&b.push(Qf.configure((d=this.options)===null||d===void 0?void 0:d.horizontalRule)),this.options.italic!==!1&&b.push(ih.configure((f=this.options)===null||f===void 0?void 0:f.italic)),this.options.listItem!==!1&&b.push(sh.configure((h=this.options)===null||h===void 0?void 0:h.listItem)),this.options.orderedList!==!1&&b.push(lh.configure((p=this.options)===null||p===void 0?void 0:p.orderedList)),this.options.paragraph!==!1&&b.push(ah.configure((m=this.options)===null||m===void 0?void 0:m.paragraph)),this.options.strike!==!1&&b.push(dh.configure((g=this.options)===null||g===void 0?void 0:g.strike)),this.options.text!==!1&&b.push(fh.configure((y=this.options)===null||y===void 0?void 0:y.text)),b}}),ph=ue.create({name:"textStyle",addOptions(){return{HTMLAttributes:{}}},parseHTML(){return[{tag:"span",getAttrs:n=>n.hasAttribute("style")?{}:!1}]},renderHTML({HTMLAttributes:n}){return["span",H(this.options.HTMLAttributes,n),0]},addCommands(){return{removeEmptyTextStyle:()=>({state:n,commands:e})=>{const t=Jt(n,this.type);return Object.entries(t).some(([,i])=>!!i)?!0:e.unsetMark(this.name)}}}}),mh=W.create({name:"color",addOptions(){return{types:["textStyle"]}},addGlobalAttributes(){return[{types:this.options.types,attributes:{color:{default:null,parseHTML:n=>{var e;return(e=n.style.color)===null||e===void 0?void 0:e.replace(/['"]+/g,"")},renderHTML:n=>n.color?{style:`color: ${n.color}`}:{}}}}]},addCommands(){return{setColor:n=>({chain:e})=>e().setMark("textStyle",{color:n}).run(),unsetColor:()=>({chain:n})=>n().setMark("textStyle",{color:null}).removeEmptyTextStyle().run()}}}),gh=W.create({name:"fontFamily",addOptions(){return{types:["textStyle"]}},addGlobalAttributes(){return[{types:this.options.types,attributes:{fontFamily:{default:null,parseHTML:n=>{var e;return(e=n.style.fontFamily)===null||e===void 0?void 0:e.replace(/['"]+/g,"")},renderHTML:n=>n.fontFamily?{style:`font-family: ${n.fontFamily.split(",").map(e=>CSS.escape(e.trim())).join(", ")}`}:{}}}}]},addCommands(){return{setFontFamily:n=>({chain:e})=>e().setMark("textStyle",{fontFamily:n}).run(),unsetFontFamily:()=>({chain:n})=>n().setMark("textStyle",{fontFamily:null}).removeEmptyTextStyle().run()}}}),yh=W.create({name:"fontSize",addOptions(){return{types:["textStyle"],getStyle:n=>`font-size: ${n}`}},addGlobalAttributes(){return[{types:this.options.types,attributes:{fontSize:{default:null,parseHTML:n=>n.style.fontSize.replace(/['"]+/g,""),renderHTML:n=>n.fontSize?{style:this.options.getStyle(n.fontSize)}:{}}}}]},addCommands(){return{setFontSize:n=>({chain:e})=>e().setMark("textStyle",{fontSize:n}).run(),unsetFontSize:()=>({chain:n})=>n().setMark("textStyle",{fontSize:null}).removeEmptyTextStyle().run()}}});W.create({name:"textDecoration",addOptions(){return{types:["textStyle"],getStyle:n=>`text-decoration: ${n}`}},addGlobalAttributes(){return[{types:this.options.types,attributes:{textDecoration:{default:null,parseHTML:n=>n.style.textDecoration.replace(/['"]+/g,""),renderHTML:n=>n.textDecoration?{style:this.options.getStyle(n.textDecoration)}:{}}}}]},addCommands(){return{setTextDecoration:n=>({chain:e})=>e().setMark("textStyle",{textDecoration:n}).run(),unsetTextDecoration:()=>({chain:n})=>n().setMark("textStyle",{textDecoration:null}).removeEmptyTextStyle().run()}}});const kh=W.create({name:"overline",addOptions(){return{types:["textStyle"]}},addGlobalAttributes(){return[{types:this.options.types,attributes:{overline:{default:!1,parseHTML:n=>n.style.borderTop.replace(/['"]+/g,""),renderHTML:n=>n.overline?{style:"border-top: 1px solid currentColor;"}:{}}}}]},addCommands(){return{toggleOverline:()=>({chain:n,editor:e})=>e.isActive("textStyle",{overline:!0})?n().unsetOverline().run():n().setOverline().run(),setOverline:()=>({chain:n})=>n().setMark("textStyle",{overline:!0}).run(),unsetOverline:()=>({chain:n})=>n().setMark("textStyle",{overline:!1}).removeEmptyTextStyle().run()}},addKeyboardShortcuts(){return{"Mod-o":()=>this.editor.commands.toggleOverline(),"Mod-O":()=>this.editor.commands.toggleOverline()}}}),bh=W.create({name:"underline",addOptions(){return{types:["textStyle"]}},addGlobalAttributes(){return[{types:this.options.types,attributes:{underline:{default:!1,parseHTML:n=>n.style.borderBottom.replace(/['"]+/g,""),renderHTML:n=>n.underline?{style:"border-bottom: 1px solid currentColor;"}:{}}}}]},addCommands(){return{toggleUnderline:()=>({chain:n,editor:e})=>e.isActive("textStyle",{underline:!0})?n().unsetUnderline().run():n().setUnderline().run(),setUnderline:()=>({chain:n})=>n().setMark("textStyle",{underline:!0}).run(),unsetUnderline:()=>({chain:n})=>n().setMark("textStyle",{underline:!1}).removeEmptyTextStyle().run()}},addKeyboardShortcuts(){return{"Mod-u":()=>this.editor.commands.toggleUnderline(),"Mod-U":()=>this.editor.commands.toggleUnderline()}}}),xh=W.create({name:"Strike",addOptions(){return{types:["textStyle"]}},addGlobalAttributes(){return[{types:this.options.types,attributes:{strike:{default:!1,parseHTML:n=>n.style.textDecoration.replace(/['"]+/g,""),renderHTML:n=>n.strike?{style:"text-decoration: 1px line-through currentColor;"}:{}}}}]},addCommands(){return{toggleStrike:()=>({chain:n,editor:e})=>e.isActive("textStyle",{strike:!0})?n().unsetStrike().run():n().setStrike().run(),setStrike:()=>({chain:n})=>n().setMark("textStyle",{strike:!0}).run(),unsetStrike:()=>({chain:n})=>n().setMark("textStyle",{strike:!1}).removeEmptyTextStyle().run()}}}),Qi="background-color: #00349e; color: inherit; user-select: none; pointer-events: none;",Sh=ue.create({name:"selectedText",addOptions(){return{HTMLAttributes:{style:Qi}}},parseHTML(){return[{tag:"span[data-selected]",getAttrs:n=>typeof n=="string"?{}:{style:n.getAttribute("style")}}]},renderHTML({HTMLAttributes:n}){return["span",{"data-selected":"true",style:this.options.HTMLAttributes.style,...n},0]},addCommands(){return{setSelectedText:(n,e)=>({tr:t,dispatch:r})=>(r&&t.addMark(n,e,this.type.create()),!0),unsetSelectedText:(n,e)=>({tr:t,dispatch:r})=>(r&&t.removeMark(n,e,this.type),!0)}},addProseMirrorPlugins(){const n={from:0,to:0},e=(i,s,o)=>oe.inline(o+i,o+s,{style:Qi,class:"selected-text"}),t=(i,s,o,l)=>{if(!i.isText)return null;const a=Math.max(o-s,0),c=Math.min(l-s,i.nodeSize);return a<c?e(a,c,s):null},r=document.createElement("style");return r.textContent=`
      .ProseMirror ::selection {
        background-color: transparent;
        color: inherit;
      }
    `,document.head.appendChild(r),[new le({key:new Oe("selectedText"),props:{decorations(i){const{doc:s,selection:o}=i,{empty:l,from:a,to:c}=o,u=[];return l||(Object.assign(n,{from:a,to:c}),s.nodesBetween(a,c,(d,f)=>{const h=t(d,f,a,c);return h&&u.push(h),!0})),V.create(s,u)},handleDOMEvents:{focus:i=>(n.from!==n.to&&i.dispatch(i.state.tr.removeMark(n.from,n.to,i.state.schema.marks.selectedText)),!1),blur:()=>!1}}})]}}),Mh=n=>(n||(n={}),hf({content:"",extensions:[hh.configure({strike:!1}),ph,mh,gh,yh,kh,bh,xh,Sh.configure({HTMLAttributes:{style:"background-color: #00349e; color: inherit;display: inline-block; line-height: 1.5;"}})],autofocus:!0,...n})),mn=12,we=Ue(mn),Ch=ss(()=>(Ne.value,gn(we.value,Array.from(Ce),we.value)));let Ce=new Set;const es=()=>{Ce.clear()};function gn(n,e,t=12,r=1,i=40){e.includes(t)||e.push(t);const s=Math.min(...e),o=Math.max(...e),l=Math.max(s,r),a=n/l,c=mn*r*a;if(c<i&&o<i)return o<i&&r*a>mn?r*a:c;{let u;return n<=s?u=0:n>=o?u=1:u=(n-s)/(o-s),r+u*(i-r)}}const wh=(n,e,t)=>n+e*256+t*256*256,Th=n=>{const e=n>>16,t=n>>8&255,r=n&255;return new ls(r,t,e)},Oh=n=>{if(!n||n.length===0)return"";let e="",t={};return n.forEach(({type:r,attrs:i})=>{if(r==="bold"&&(t.bold=!0),r==="italic"&&(t.italic=!0),r==="textStyle"&&i){const{color:s,fontFamily:o,fontSize:l,overline:a,strike:c,underline:u}=i;if(s)if(s.method==Ie.kByColor){const d=_o(s.color);e+="\\c"+wh(d.red(),d.green(),d.blue())+";"}else s.method===Ie.kByACI?e+="\\C"+s.index+";":(s.method===Ie.kByBlock&&(e+="\\C0"),s.method===Ie.kByLayer&&(e+="\\C256"));o&&(t.family=o),l&&(e+="\\H"+Number(l)/we.value+"x;"),a&&(e+="\\O"),c&&(e+="\\K"),u&&(e+="\\L")}}),Object.keys(t).length!==0&&(e=`\\f${t.family??""}|${t.bold?"b1":"b0"}|${t.italic?"i1":"i0"}|c134|p49;`+e),e},Vo=n=>{if(!n)return"";let e="";if(n.type==="text"){const t=Oh(n.marks);t&&(e+="{",e+=t),e+=n.text,t&&(e+="}")}return n.content&&(e+=n.content.map(t=>Vo(t)).join("")),n.type==="paragraph"&&(e+="\\P"),n.type==="doc"&&e.endsWith("\\P")&&(e=e.replace(/\\P$/,"")),e},vh=n=>{let e=[],t={type:"doc",content:[{type:"paragraph",content:e}]},r=0,i=!1,s="",o=["c","C","F","f","H"],l=!1,a="",c=[],u={type:"textStyle",attrs:{}},d={};const{createColor:f}=Wo(),h=()=>{if(a){let p={};if(l){const{bold:m,italic:g,...y}=d;p=y;const b=c?.some(({type:C})=>C==="bold"),M=c?.some(({type:C})=>C==="italic");m&&m!==b&&c?.push({type:"bold"}),g&&g!==M&&c?.push({type:"italic"})}u.attrs=Object.assign(p,u.attrs),c&&c.push(u),e.push({type:"text",text:a,marks:c}),a="",u={type:"textStyle",attrs:{}},c=[]}};for(;r<n.length;){const p=n[r];if(r++,i){if(s&&p===";"){const m=s.startsWith("c"),g=s.startsWith("C");if(m||g){let y;if(g){const b=Number(s.slice(1,s.length)),M=new ls;M.setColorIndex(b),y=f({...M,name:Ko(b)||M.getColorString(),index:b,color:`rgba(${M.red},${M.green},${M.blue},${M.n})`}),y.method=Ie.kByACI,b===256&&(M.method=Ie.kByLayer),b===0&&(M.method=Ie.kByBlock)}if(m){const b=s.slice(1,s.length);y=f({...qo(Uo(Th(Number(b))),Ie.kByColor)})}if(!y){i=!1;continue}y.toString=()=>y.color,l&&(typeof d.color<"u"?d.color?.name!==y.name&&(h(),d.color=y):d.color=y),u.attrs.color=y}if(s.startsWith("f")||s.startsWith("F")){const y=s.slice(1,s.length),[b,M,C]=y.split("|");if(l){const A=typeof d.fontFamily<"u",v=typeof d.bold<"u",R=typeof d.italic<"u",z=b!==d.fontFamily,T=M==="b1"!==d.bold,F=C==="i1"!==d.italic;(A&&z||v&&T||R&&F)&&h(),(!A||z)&&(d.fontFamily=b),(!v||T)&&(d.bold=M==="b1"),(!R||F)&&(d.italic=C==="i1")}u.attrs.fontFamily=b,M==="b1"&&c.push({type:"bold"}),C==="i1"&&c.push({type:"italic"}),i=!1,s="";continue}if(s.startsWith("H")){const y=s.slice(1,s.length),b=parseFloat(y)*we.value,M=new Number(b);Ce.add(b),M.toString=()=>gn(b,Array.from(Ce),we.value)+"px",l&&(typeof d.fontSize>"u"?d.fontSize=M:Number(d.fontSize)!==Number(M)&&(h(),d.fontSize=M)),u.attrs.fontSize=M}i=!1,s="";continue}if(s===""){if(p==="P"){h(),e=[],t.content?.push({type:"paragraph",content:e}),i=!1;continue}if(p==="O"){l&&(typeof d.overline>"u"?d.overline=!0:d.overline||(h(),d.overline=!0)),u.attrs.overline=!0,i=!1;continue}if(p==="o"){h(),l&&(d.overline=!1),i=!1;continue}if(p==="K"){l&&(typeof d.strike>"u"?d.strike=!0:d.strike||(h(),d.strike=!0)),u.attrs.strike=!0,i=!1;continue}if(p==="k"){h(),l&&(d.strike=!1),i=!1;continue}if(p==="L"){l&&(typeof d.underline>"u"?d.underline=!0:d.underline||(h(),d.underline=!0)),u.attrs.underline=!0,i=!1;continue}if(p==="l"){h(),l&&(d.underline=!1),i=!1;continue}if(o.includes(p)&&!s){s=p;continue}}s+=p;continue}if(p==="\\"&&n[r]!=="\\"){i=!0,s="";continue}if(p==="{"){h(),d={},l=!0;continue}if(p==="}"&&l){h(),d={};continue}a+=p,r===n.length&&h()}return t},Ne=Ue(1),Nh=n=>{const{editor:e}=n,t=rs([{icon:"class:iconfont bold",title:"Bold",action:()=>e.value?.chain().focus().toggleBold().run(),isActive:()=>e.value?.isActive("bold")},{icon:"class:iconfont italic",title:"Italic",action:()=>e.value?.chain().focus().toggleItalic().run(),isActive:()=>e.value?.isActive("italic")},{icon:"class:iconfont strikethrough",title:"strike",action:()=>e.value?.chain().focus().toggleStrike().run(),isActive:()=>e.value?.isActive("textStyle",{strike:!0})},{icon:"class:iconfont overline",title:"overline",action:()=>e.value?.chain().focus().toggleOverline().run(),isActive:()=>e.value?.isActive("textStyle",{overline:!0})},{icon:"class:iconfont underline",title:"underline",action:()=>e.value?.chain().focus().toggleUnderline().run(),isActive:()=>e.value?.isActive("textStyle",{underline:!0})},{icon:"class:iconfont arrow-u-left-top-bold",title:"Undo",action:()=>e.value?.chain().focus().undo().run()},{icon:"class:iconfont arrow-u-right-top-bold",title:"Redo",action:()=>e.value?.chain().focus().redo().run()},...n.items||[]]),r=Ue([]),i=Ue(),s=Ue(),o=Jo(),{textStyles:l,txtFontNames:a,trueTypeFontNames:c,bigFontNames:u,current:d}=jo(o),f=Ue(d.value.name),h=ss(()=>[...a.value,...c.value,...u.value]),p=()=>{const C=e.value?.getAttributes("textStyle");if(C){const A=C.color;s.value=A;const v=C.fontFamily;v&&(i.value=v);const R=C.fontSize;R?Ne.value=Number(R):Ne.value=we.value}};ts(()=>{e.value?.on("selectionUpdate",p)});let m=null;const g=C=>{if(!m)return;const{editor:A}=C;A.state.tr.doc.textContent.length>m.textContent.length&&(m=null,A.off("transaction",g),b())},y=()=>{if(!e.value||Ne.value<=0)return;const C=new Number(Ne.value);Ce.add(Ne.value),C.toString=()=>gn(C,Array.from(Ce),we.value)+"px",e.value?.chain().setFontSize(C).run(),e.value?.commands;const{from:A,to:v}=e.value.state.selection;if(A===v){if(!e.value.state)return;m=e.value.state.tr.before,e.value.off("transaction",g),e.value.on("transaction",g)}else b()},b=()=>{if(!e.value)return;const C=e.value?.state;if(!C)return;const A=C.tr,v=C.schema;es();const R=[];C.doc.descendants((z,T)=>{if(z.isText){const P=z.marks.find(D=>D.type.name==="textStyle");if(P){if(!z.text)return;const D=P.attrs.fontSize;if(!D)return;const I=D;Ce.add(Number(I)),R.push({originalFontSize:D,pos:T,length:z.text.length,markAttrs:{...P.attrs}})}}}),R.forEach(z=>{const T=z.originalFontSize,F=new Number(T);Ce.add(T),F.toString=()=>gn(T,Array.from(Ce),we.value)+"px",z.markAttrs.fontSize=F;const P=v.marks.textStyle.create(z.markAttrs);A.addMark(z.pos,z.pos+z.length,P)}),e.value.view.dispatch(A)};return{items:t,setSize:y,updateSizes:b,sizes:r,size:Ne,addSize:()=>{r.value=Array.from(new Set([...r.value,Ne.value]))},font:i,fonts:h,styleColor:s,textStyles:l,textStyle:f,baseCadSize:we,selectionUpdate:p,emptyCadSizes:es,defaultFontSize:Ch,defaultSize:mn}},Eh={class:"px-3"},Ah={class:"w-100 d-flex mt-2 flex-wrap"},Dh=is({__name:"index",setup(n){const e=Mh(),{dialog:t}=Go(),{isShow:r,showDialog:i,onReveal:s,confirm:o,cancel:l}=t,{items:a,size:c,sizes:u,updateSizes:d,font:f,styleColor:h,setSize:p,addSize:m,textStyles:g,textStyle:y,fonts:b,baseCadSize:M,selectionUpdate:C,emptyCadSizes:A,defaultFontSize:v,defaultSize:R}=Nh({editor:e});s(P=>{if(A(),M.value=P?.textHeight||R,P?.textHeight&&(c.value=P.textHeight),P?.textStyle){y.value=P.textStyle;const I=P.textStyleId.getMcDbTextStyleTableRecord();f.value=I?.fileName||I?.bigFontFileName}else f.value=b.value[0];const D=vh(P?.contents||"");D&&e.value?.commands.setContent(D,!1),e.value?.commands.focus(),C(),d()});const z=[{name:"确定",fun:()=>{const P=e.value?.getJSON(),D=Vo(P);o({text:D,size:M.value})},primary:!0},{name:"关闭",fun:()=>{l(),i(!1)}}],T=()=>{const P=window.getSelection();P&&P.rangeCount>0&&e.value?.commands.setSelectedText(e.value?.state.selection.from,e.value?.state.selection.to)},F=()=>{e.value?.commands.unsetSelectedText(e.value?.state.selection.from,e.value?.state.selection.to)};return(P,D)=>(Dn(),$r(Ho,{title:P.t("597"),modelValue:U(r),"onUpdate:modelValue":D[9]||(D[9]=I=>jt(r)?r.value=I:null),footerBtnList:z,"max-width":"850"},{default:Je(()=>[Hr("div",Eh,[Hr("div",Ah,[je(Wr,{class:"mr-1 selectBox",items:U(g),modelValue:U(y),"onUpdate:modelValue":D[0]||(D[0]=I=>jt(y)?y.value=I:null),"onUpdate:menu":D[1]||(D[1]=I=>I?T():F())},{prepend:Je(()=>[In(Rn(P.t("362"))+": ",1)]),_:1},8,["items","modelValue"]),je(Wr,{class:"mr-1 selectBox",items:U(b),modelValue:U(f),"onUpdate:modelValue":[D[2]||(D[2]=I=>jt(f)?f.value=I:null),D[4]||(D[4]=I=>U(e)?.chain().setFontFamily(I).run())],"onUpdate:menu":D[3]||(D[3]=I=>I?T():F())},{prepend:Je(()=>[In(Rn(P.t("598"))+": ",1)]),_:1},8,["items","modelValue"]),je(ul,{class:"mr-1 selectBox",type:"number",items:U(u),modelValue:U(c),"onUpdate:modelValue":[D[5]||(D[5]=I=>jt(c)?c.value=I:null),U(p)],modelModifiers:{lazy:!0},"onUpdate:focused":D[6]||(D[6]=I=>I?T():F()),onChange:U(m)},{prepend:Je(()=>[In(Rn(P.t("482"))+": ",1)]),clear:Je(I=>D[10]||(D[10]=[])),_:1},8,["items","modelValue","onUpdate:modelValue","onChange"]),(Dn(!0),ol(al,null,ll(U(a),(I,$o)=>(Dn(),$r(dl,{icon:"",key:$o,onClick:I.action,active:I.isActive&&I.isActive(),title:I.title,size:"24px",variant:"text"},{default:Je(()=>[je(fl,{size:"large",icon:I.icon},null,8,["icon"])]),_:2},1032,["onClick","active","title"]))),128)),je(Yo,{class:"ml-1 selectBox","model-value":U(h),"onUpdate:menu":D[7]||(D[7]=I=>I?T():F()),onChange:D[8]||(D[8]=I=>{I.toString=()=>I.color,U(e)?.chain().focus().setColor(I).run()})},null,8,["model-value"])]),je(Xo,{title:P.t("599")},{default:Je(()=>[je(U(ff),{class:"multi_line_text_edit overflow-x-auto",style:cl({fontSize:U(v)+"px"}),editor:U(e)},null,8,["style","editor"])]),_:1},8,["title"])])]),_:1},8,["title","modelValue"]))}}),Hh=Zo(Dh,[["__scopeId","data-v-e16cbea0"]]);export{Hh as default};
