import {MxFun} from "mxdraw";
import {MxCADUiPrKeyWord, MxCADUtility, McDb,  MxCpp, McDbText, MxCADUiPrPoint, MxCADSelectionSet, MxCADResbuf, McDbBlockReference, McDbAttributeDefinition, McDbAttribute, McGePoint3d, McCmColor} from 'mxcad'
async function ancillaryFacilitiesAnnotation(data) {
    console.log('ancillaryFacilitiesAnnotation', data)
    const getKey = new MxCADUiPrKeyWord
    getKey.setMessage("输入绘制类型")
    getKey.setKeyWords('[显示水泥制品标注(1)]')
    const keyVal: string = await getKey.go()
    console.log('keyVal',keyVal)
     const filter = new MxCADResbuf();
      // 添加对象类型，选择集只选择文字类型的对象
      //     filter.AddMcDbEntityTypes("TEXT,MTEXT")
      let ids = await MxCADUtility.userSelect("框选需要的对象", filter);
      console.log(ids);
      if (!ids.length) return;
      console.log('12123123')
      let idList = ids.map((id) => {
        const ent = id.getMcDbEntity();
        const equipmentId = ent.getxDataString("equipmentId");
        const legendtypekey = ent.getxDataString("legendtypekey");
        console.log("legendtypekey", equipmentId,legendtypekey);
       
        if (equipmentId.ret||legendtypekey.ret) return {equipmentId:equipmentId.val,legendtypekey:legendtypekey.val};
        
      });
      idList=idList.filter(item=>item.legendtypekey=='TY_SNG'||item.legendtypekey=='TY_ZSBYQ')
      const params = {
        idList,
        keyVal,
      };
     
    MxFun.postMessageToParentFrame({messageId: data.id, params,type:"SNG_BZ"});

}

export function init() {
    MxFun.addCommand("Ancillary_Facilities_Annotation", ancillaryFacilitiesAnnotation);
}
