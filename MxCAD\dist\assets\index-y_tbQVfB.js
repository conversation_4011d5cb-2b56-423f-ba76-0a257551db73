import{M as x}from"./index-itnQ6avM.js";import{a7 as k,$ as _,Z as f}from"./index-CzBriCFR.js";import{h as g,z as T,a3 as n,a4 as s,u as t,B as V,_ as a,$ as m,Q as r,V as c,a9 as u,a0 as B,F as C,L as D}from"./vue-Cj9QYd7Z.js";import"./vuetify-BqCp6y38.js";import"./mxcad-DrgW2waE.js";import"./mxdraw-BQ5HhRCY.js";import"./handsontable-Ch5RdAT_.js";import"./mapbox-gl-DQAr7S0z.js";const w={class:"d-flex justify-center algin-center py-7"},M={key:0,class:"d-flex justify-space-evenly py-3"},L=g({__name:"index",setup($){const p=k(),{options:e,isShow:i}=_(p),{showDialog:d}=p,y=()=>{e.value.define&&e.value.define(),d(!1)},v=()=>{e.value.cancel&&e.value.cancel(),d(!1)};return T(()=>{e.value.mounted&&e.value.mounted()}),(N,o)=>(a(),n(x,{title:t(e).title,modelValue:t(i),"onUpdate:modelValue":o[2]||(o[2]=l=>V(i)?i.value=l:null),"max-width":"250"},{actions:s(()=>[t(e).defineTitle||t(e).cancelTitle?(a(),m("div",M,[typeof t(e).defineTitle=="string"?(a(),n(f,{key:0,isAction:"",primary:"",onClick:o[0]||(o[0]=l=>y())},{default:s(()=>[r(c(t(e).defineTitle),1)]),_:1})):u("",!0),typeof t(e).cancelTitle=="string"?(a(),n(f,{key:1,isAction:"",onClick:o[1]||(o[1]=l=>v())},{default:s(()=>[r(c(t(e).cancelTitle),1)]),_:1})):u("",!0)])):u("",!0)]),default:s(()=>[B("div",w,[typeof t(e).text=="string"?(a(),m(C,{key:0},[r(c(t(e).text),1)],64)):(a(),n(D(t(e).text),{key:1}))])]),_:1},8,["title","modelValue"]))}});export{L as default};
