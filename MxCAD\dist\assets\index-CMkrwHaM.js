import{M as K}from"./index-8X61wlK0.js";import{G as Z,M as f,ap as i,aq as a,K as A,n as E,ar as b,H as _,A as q,_ as R,i as J,as as B,at as j,au as Q}from"./index-D95UjFey.js";import{M as O,a as W}from"./mxcad-CfPpL1Bn.js";import{M as X}from"./mxdraw-C_n_7lEs.js";import{J as Y,K as L,E as z,i as c,U as y,c as v,C as T,j as h}from"./vuetify-B_xYg4qv.js";import{h as H,d as V,a0 as w,a3 as ee,m as e,$ as t,a1 as r,u,B as m,V as p,Q as g,F as le,_ as M}from"./vue-DfH9C9Rx.js";import"./mapbox-gl-PN3pDJHq.js";import"./xlsx-Ck5MUZvf.js";const te={class:"d-flex justify-center algin-center"},ae={class:"d-flex justify-center algin-center"},oe={class:"d-flex"},se={class:"d-flex align-center w-50"},ne={class:"d-flex align-center w-50"},ie={class:"d-flex"},ue=H({__name:"ElectronSetUp",setup(I,{expose:k}){const $=V(""),S=()=>{b.clearFileCache()},x=()=>{b.debugTools()},s=V(!0),{createColor:d}=Z(),C=V(),N=V(),U=V(!1);q(async()=>{const l=O.getCurrentMxCAD().mxdraw.getViewColor(),o=_(l).toString();C.value=d({color:o,name:o}),U.value=await b.getAppConfig("isStartOpenLastFile")||!1});const P=n=>{C.value=n},G=n=>{const l=_(n.color),o=l.red(),D=l.green(),F=l.blue();O.getCurrentMxCAD().setViewBackgroundColor(o,D,F),X.callEvent("updateBackgroundColor",new W(o,D,F))};return k({determine:()=>{C.value&&G(C.value),b.setAppConfig("isStartOpenLastFile",U.value)}}),(n,l)=>(w(),ee(le,null,[e(f,{title:n.t("622")},{default:t(()=>[e(Y,{modelValue:s.value,"onUpdate:modelValue":l[0]||(l[0]=o=>s.value=o)},{default:t(()=>[e(L,{label:n.t("623"),value:!0},null,8,["label"]),e(L,{label:n.t("624"),value:!1},null,8,["label"])]),_:1},8,["modelValue"])]),_:1},8,["title"]),e(T,{class:"mt-2"},{default:t(()=>[e(z,{cols:"6",class:"mb-1"},{default:t(()=>[e(f,{title:n.t("625")},{default:t(()=>[r("div",te,[e(c,{modelValue:u(i),"onUpdate:modelValue":l[1]||(l[1]=o=>m(i)?i.value=o:null),class:"pa-0",style:{"max-width":"36px"}},null,8,["modelValue"]),e(y,{"thumb-size":10,"tick-size":1,max:128,color:"#007AD9",modelValue:u(i),"onUpdate:modelValue":l[4]||(l[4]=o=>m(i)?i.value=o:null),"hide-details":"",step:1},{prepend:t(()=>[e(v,{icon:"class:iconfont minus",size:"x-small",variant:"text",onClick:l[2]||(l[2]=o=>u(i)>0&&i.value--)})]),append:t(()=>[e(v,{icon:"class:iconfont plus",size:"x-small",variant:"text",onClick:l[3]||(l[3]=o=>u(i)<128&&i.value++)})]),_:1},8,["modelValue"])])]),_:1},8,["title"])]),_:1}),e(z,{cols:"6",class:"mb-1","align-self":"start"},{default:t(()=>[e(f,{title:n.t("626")},{default:t(()=>[r("div",ae,[e(c,{modelValue:u(a),"onUpdate:modelValue":l[5]||(l[5]=o=>m(a)?a.value=o:null),class:"pa-0",style:{"max-width":"36px"}},null,8,["modelValue"]),e(y,{"thumb-size":10,"tick-size":1,color:"#007AD9",modelValue:u(a),"onUpdate:modelValue":l[8]||(l[8]=o=>m(a)?a.value=o:null),min:0,max:68,"hide-details":"",step:1},{prepend:t(()=>[e(v,{icon:"class:iconfont minus",size:"x-small",variant:"text",onClick:l[6]||(l[6]=o=>u(a)>0&&a.value--)})]),append:t(()=>[e(v,{icon:"class:iconfont plus",size:"x-small",variant:"text",onClick:l[7]||(l[7]=o=>u(a)<68&&a.value++)})]),_:1},8,["modelValue"])])]),_:1},8,["title"])]),_:1})]),_:1}),e(f,{title:n.t("627"),class:"mt-2"},{default:t(()=>[r("div",oe,[r("div",se,[r("span",null,p(n.t("628"))+":",1),e(A,{"model-value":C.value,"onUpdate:modelValue":P,"menus-props":{maxHeight:"600"}},null,8,["model-value"])]),r("div",ne,[r("span",null,p(n.t("629"))+":",1),e(A,{"model-value":N.value,"menus-props":{maxHeight:"600"}},null,8,["model-value"])])])]),_:1},8,["title"]),e(f,{title:n.t("286"),class:"mt-2"},{default:t(()=>[r("div",ie,[e(c,{modelValue:u(a),"onUpdate:modelValue":l[9]||(l[9]=o=>m(a)?a.value=o:null),class:"pa-0"},{prepend:t(()=>[g(p(n.t("630"))+": ",1)]),_:1},8,["modelValue"]),e(c,{modelValue:u(a),"onUpdate:modelValue":l[10]||(l[10]=o=>m(a)?a.value=o:null),class:"pa-0 ml-4"},{prepend:t(()=>[g(p(n.t("631"))+"("+p(n.t("632"))+"): ",1)]),_:1},8,["modelValue"])])]),_:1},8,["title"]),e(c,{"model-value":$.value,readonly:"",class:"mt-2"},{prepend:t(()=>[g(p(n.t("633")),1)]),_:1},8,["model-value"]),e(h,{label:n.t("634"),modelValue:U.value,"onUpdate:modelValue":l[11]||(l[11]=o=>U.value=o),class:"mt-2"},null,8,["label","modelValue"]),e(E,{class:"mt-2 mr-2",onClick:S},{default:t(()=>[g(p(n.t("635")),1)]),_:1}),e(E,{class:"mt-2",onClick:x},{default:t(()=>[g(p(n.t("636")),1)]),_:1})],64))}}),de=R(ue,[["__scopeId","data-v-7b4cca32"]]),re={class:"px-3"},me={class:"d-flex justify-center algin-center"},pe={class:"d-flex justify-center algin-center"},ve=H({__name:"index",setup(I){const k=V(),S=[{name:"确定",fun:()=>{Q(),k.value?.determine(),j(!1)},primary:!0},{name:"关闭",fun:()=>j(!1)}];return(x,s)=>(w(),M(K,{title:x.t("293"),"max-width":"600",modelValue:u(B),"onUpdate:modelValue":s[8]||(s[8]=d=>m(B)?B.value=d:null),footerBtnList:S},{default:t(()=>[r("div",re,[u(J)()?(w(),M(de,{key:0,ref_key:"electronSetUp",ref:k},null,512)):(w(),M(T,{key:1},{default:t(()=>[e(z,{cols:"6",class:"mb-1"},{default:t(()=>[e(f,{title:x.t("625")},{default:t(()=>[r("div",me,[e(c,{modelValue:u(i),"onUpdate:modelValue":s[0]||(s[0]=d=>m(i)?i.value=d:null),class:"pa-0",style:{"max-width":"36px"}},null,8,["modelValue"]),e(y,{"thumb-size":10,"tick-size":1,max:128,color:"#007AD9",modelValue:u(i),"onUpdate:modelValue":s[3]||(s[3]=d=>m(i)?i.value=d:null),"hide-details":"",step:1},{prepend:t(()=>[e(v,{icon:"class:iconfont minus",size:"x-small",variant:"text",onClick:s[1]||(s[1]=d=>u(i)>0&&i.value--)})]),append:t(()=>[e(v,{icon:"class:iconfont plus",size:"x-small",variant:"text",onClick:s[2]||(s[2]=d=>u(i)<128&&i.value++)})]),_:1},8,["modelValue"])])]),_:1},8,["title"])]),_:1}),e(z,{cols:"6",class:"mb-1","align-self":"start"},{default:t(()=>[e(f,{title:x.t("626")},{default:t(()=>[r("div",pe,[e(c,{modelValue:u(a),"onUpdate:modelValue":s[4]||(s[4]=d=>m(a)?a.value=d:null),class:"pa-0",style:{"max-width":"36px"}},null,8,["modelValue"]),e(y,{"thumb-size":10,"tick-size":1,color:"#007AD9",modelValue:u(a),"onUpdate:modelValue":s[7]||(s[7]=d=>m(a)?a.value=d:null),min:0,max:68,"hide-details":"",step:1},{prepend:t(()=>[e(v,{icon:"class:iconfont minus",size:"x-small",variant:"text",onClick:s[5]||(s[5]=d=>u(a)>0&&a.value--)})]),append:t(()=>[e(v,{icon:"class:iconfont plus",size:"x-small",variant:"text",onClick:s[6]||(s[6]=d=>u(a)<68&&a.value++)})]),_:1},8,["modelValue"])])]),_:1},8,["title"])]),_:1})]),_:1}))])]),_:1},8,["title","modelValue"]))}}),ze=R(ve,[["__scopeId","data-v-77de434f"]]);export{ze as default};
