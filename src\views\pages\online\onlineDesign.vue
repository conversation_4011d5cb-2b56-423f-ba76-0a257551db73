<template>
  <div class="online-design-container">
    <!--    <div class="top-menu">
          <tags-view/>
        </div>-->
    <div class="home">
      <!-- 成果上报 -->
       <resultReport></resultReport>
      <!-- 架空线路设计 -->
      <OverheadModule></OverheadModule>
      <!-- 电缆线路设计 -->
      <CableDesign></CableDesign>
      <!-- 配电站房设计 -->
      <DistributionStation></DistributionStation>
      <!-- 标注管理 -->
      <AnnotationManagement></AnnotationManagement>
      <!-- 成果输出管理 -->
      <ResultsManagement></ResultsManagement>
      <!-- 工具管理 -->
      <ToolsManagement></ToolsManagement>


      <div id="myiframe"></div>  
      <!-- 画布 专门用来 生成图纸 不影响主图纸 -->
      <iframe id="myiframehide" v-show="false" ref="cadIframeRef" :src="cadIframeSrc" />
    </div>

    <!-- 动态弹窗组件 -->
    <dynamic-dialog/>

    <!--    设备选型-->
<!--    <single-equipment-selection
        v-if="singleEquipmentSelectionVisible"
        v-model:visible="singleEquipmentSelectionVisible"
    />-->
  </div>
</template>

<script name="Index" setup>
import {undoEquipmentInfoByFlag} from "@/api/desginManage/draw.js"
import OverheadModule from "./components/OverheadModule.vue"
import CableDesign from "./components/CableDesign.vue"
import DistributionStation from "./components/DistributionStation.vue"
import AnnotationManagement from "./components/AnnotationManagement.vue";
import resultReport from "./components/resultReport.vue";
import ResultsManagement from "./components/ResultsManagement/ResultsManagement.vue";
import ToolsManagement from "./components/ToolsManagement/ToolsManagement.vue";
import DynamicDialog from "./components/dynamic-dialog";
import SingleEquipmentSelection from "./sidebar/property/single-equipment-selection"
import useAppStore from "@/store/modules/app";
import {tansParams} from "@/utils/ruoyi.js";
import {useRoute} from "vue-router"
import {getToken} from "@/utils/auth.js";
import {changeEngineeringVersion,getHzgctuByTaskId} from "../../../api/desginManage/GccsDialog";
import {useIframeCommunication} from "@/hooks/useIframeCommunication";
import {ElMessage, ElMessageBox} from "element-plus";
import { sdkUndo, sdkRedo, sdkRemoveDevice } from "./sdkDrawBackData/baseSDK"
import {getBaseSettingList} from "@/api/taskBaseData/index.js";
import { saveDwgInfo, generatorDltdtjReport, generatorGtReport,getAnnotateAuxiliaryEquipment } from "@/api/insertSag/index.js"
import {useCAdApp} from "@/hooks/useCAdApp.js";
import { ElLoading } from 'element-plus'
import { getCurrentInstance } from "vue";
const {sendMessage, cleanup} = useIframeCommunication()
const appStore = useAppStore();
const callChildC = inject('callChildC');
const {proxy}=getCurrentInstance()
const cadIframeRef = ref(null)
const {cadAppSrc} = useCAdApp()
const cadIframeSrc = cadAppSrc({
  isPreview: '1'
})

const closeDialog = () => {
  appStore.closeMapDialog()
};

const singleEquipmentSelectionVisible = computed({
  get() {
    return appStore.mapIndex === '设备选型'
  },
  set() {
    closeDialog()
  }
})
const urlRoute = new useRoute()

// const iframe = ref(null);
const iframe = defineModel('cadAppRef')

const taskId = new URLSearchParams(new URL(window.location.href).search).get('id')

const baseSettingData = ref([])
const getBaseSetting = async () => {
  const res = await getBaseSettingList({taskId: taskId})
  baseSettingData.value = res.data
}
getBaseSetting()
const drawSettingList = computed(()=> {
  const {drawSetting} = baseSettingData.value
  if (!drawSetting) return []
  const drawData = JSON.parse(drawSetting)
  const list = []
  drawData.forEach(item => {
    list.push(
        ...item.SettingList,
    )
  })
  return list
})
const initHzgctu = (data) => {
  const list = data.map(item => {
    const matchedSetting = drawSettingList.value.find(setting => setting.LegendTypeKey === item.legendtypekey && setting.LegendState === item.state);
    return {
      ...item,
      drawStyle: matchedSetting ? matchedSetting : null,
      content:'cad'
    };
  })
  setTimeout(()=>{
    sendMessage(iframe.value,{type:'Huzhi',content:list})
  },1000)
}
// 在组件挂载时初始化 iframe
onMounted(() => {
  let params = tansParams({
    ...urlRoute.query,
    token: getToken() || urlRoute.query?.token,
  })
  console.log('params', params, getToken())
  // const src = params.length !== 0
  //     ? `${import.meta.env.VITE_APP_MX_APP_SRC}?${params.endsWith('&') ? params.substring(0, params.length - 1) : params}`
  //     : `${import.meta.env.VITE_APP_MX_APP_SRC}`
      const src =`${import.meta.env.VITE_APP_MX_APP_SRC}?${params.endsWith('&') ? params.substring(0, params.length - 1) : params}`
      console.log("🚀 ~ onMounted ~ src:", src)
  const iframeElement = document.createElement("iframe");
  iframe.value = iframeElement;
  const myiframe = document.getElementById("myiframe");
  nextTick(() => {
    iframeElement.src = src;
    iframeElement.id = "MXCAD";
    iframeElement.style.width = "100%";
    iframeElement.style.height = "100%";
    myiframe.append(iframeElement);
    console.log('iframe.value', iframe.value.contentWindow)
    appStore.setIframeInfo(iframe.value)
    appStore.setIframeHideInfo(cadIframeRef.value)
  });
  // iframeElement.onload = () => {
  //   getHzgctuByTaskId(taskId).then((res)=>{
  //     console.log(res,'获取沿布数据');
  //     console.log(iframeElement,'iframeElement');
  //     initHzgctu(res.data)
  //     /*setTimeout(()=>{
  //       sendMessage(iframeElement,{type:'greeting',content:res.data})
  //     },1000)*/
  //   })
   
  // }
  
});
// 监听来自子页面的消息
window.addEventListener('message', (event) => { 
  console.log('onlineDesign接收', event)
  if(event.data.params?.type&&event.data.params?.type=="Hzgctu"){
    getHzgctuByTaskId(taskId).then((res)=>{
      initHzgctu(res.data)
    })
  }
  if (import.meta.env.VITE_APP_MX_APP_SRC.indexOf(event.origin) < 0) return

  const {type, params, cmd} = event.data?.params
  console.log(event.data?.params,'dddd')
  if(event.data.type==='click'){
    console.log(event.data.params.selectObjs,'走这个')
    let propertyList=event.data.params.selectObjs
    appStore.setPropertyInfo(propertyList)
    appStore.setSdkClassName(event.data.params)
  } else if (event.data.type === "parentCad"){
    if(event.data.params.content === "GXYL" || event.data.params.content === "JCYL"){
      const loading = ElLoading.service({
        lock: true,
        text: '报表正在生成中，请稍等...',
        background: 'rgba(0, 0, 0, 0.7)',
      })
      //保存报表文件到成果目录
      const files = event.data.params.formData.files
      console.log(files, 'filesfiles')
      const fileFormData = new FormData()
      files.forEach(item => {
        fileFormData.append('files', item)// multipartFile
      })
      fileFormData.append("name", event.data.params.content === "GXYL" ? "杆塔型式一览图" : "基础型式一览图");
      fileFormData.append('taskId', event.data.params.formData.proId)
      fileFormData.append('stage', event.data.params.formData.stage)
      saveDwgInfo(fileFormData).then(res => {
        if (res.code === 200) {
          ElMessage.success("保存成功");
          loading.close()
          if (callChildC) {
            callChildC();
          }
        } else {
          ElMessage.error(res.msg);
          loading.close()
        }
      })
    } else if(event.data.params.content === "DLTDTJ"){
      const loading = ElLoading.service({
        lock: true,
        text: '报表正在生成中，请稍等...',
        background: 'rgba(0, 0, 0, 0.7)',
      })
      const files = event.data.params.formData.files
      console.log(files, 'filesfiles')
      const tableData = JSON.parse(event.data.params.formData.tableData)
      const tableDataNew = tableData.map((item,index) => {
        return {
         xh:index+1,
         lx:item.moduleName,
         gg:item.moduleType,
         mk:item.moduleCode,
         dw:item.unit,
         sl:item.quantity,
         zt:item.state,
         bz:''
        }
      })
      const fileFormData = new FormData()
      files.forEach(item => {
        fileFormData.append('multipartFile', item)
      })
      fileFormData.append("dltdtjList", JSON.stringify(tableDataNew));
      fileFormData.append('prjTaskInfoId', event.data.params.formData.proId)
      fileFormData.append('stage', event.data.params.formData.stage)
      fileFormData.append('equipmentInfos', JSON.stringify({ th: `${route.query.proCode}-1` }))
      generatorDltdtjReport(fileFormData).then(res => {
        if (res.code === 200) {
          ElMessage.success("保存成功");
          loading.close()
          if (callChildC) {
          callChildC();
        }
        } else {
          ElMessage.error(res.msg);
          loading.close()
        }
        
      })
    } else if(event.data.params.content === "GTMX"){
      const loading = ElLoading.service({
        lock: true,
        text: '报表正在生成中，请稍等...',
        background: 'rgba(0, 0, 0, 0.7)',
      })
      const files = event.data.params.formData.files
      console.log(files, 'filesfiles')
      const tableData = JSON.parse(event.data.params.formData.tableData)
      const fileFormData = new FormData()
      files.forEach(item => {
        fileFormData.append('multipartFile', item)
      })
      fileFormData.append("gtList", JSON.stringify(tableData));
      fileFormData.append('prjTaskInfoId', event.data.params.formData.proId)
      fileFormData.append('stage', event.data.params.formData.stage)
     fileFormData.append('equipmentInfos', JSON.stringify({ th: `${route.query.proCode}-1` }))
      generatorGtReport(fileFormData).then(res => {
        if (res.code === 200) {
          ElMessage.success("保存成功");
          loading.close()
          if (callChildC) {
          callChildC();
        }
        } else {
          ElMessage.error(res.msg);
          loading.close()
        }
        
      })
    }
  } else if (event.data.type==="SNG_BZ"){
    console.log("获取水泥杆制品数据",event.data.params)
    let SNGArr=[]
    event.data.params.idList.forEach((item) => {
      SNGArr.push(item.equipmentId)
    })
    let paramsSNG={
      taskId:taskId,
      equipmentIds:SNGArr
    }
    getAnnotateAuxiliaryEquipment(paramsSNG).then(result=>{
      console.log( result,'ddddddddddd' )
      let options={name:'JGFZH_Draw',tableData:JSON.stringify(result.data)}
    sendMessage(appStore.iframe, {type:'onlycad', content:'Marking_OfCementPole_Products', options}, (res1) => {
          console.log(res1,'ssss')
        });
    })
  }
  // 提示消息
  if (type === "showMessage") {
    if (params.msgType === 'success') {
      ElMessage.success(params.content)
    } else if (params.msgType === 'error') {
      ElMessage.error(params.content)
    } else if(params.content) {
      ElMessage.warning(params.content)
    }
  } else if (type === "saveEngineeringVersion") {
    //用户保存工程版本
    changeEngineeringVersion(params).then((res) => {
      if (res.code === 200) {
        ElMessage.success("已保存工程版本")
        // 获取当前URL对象
      const currentUrl = new URL(window.location.href);
      const currentParams = new URLSearchParams(currentUrl.search);
      
      // 处理startFlag逻辑
      if (currentParams.get('startFlag')) {
        proxy.$modal.loading("图模复用中，请稍候...");
        const options = {
          extendVersionId: currentParams.get('oldVersionId'),
        };
        
        sendMessage(appStore.iframe, {type:'greeting', content:'SDK_versionExtend', options}, (res1) => {
          if (res1.cmd === "SDK_versionExtend" && res1.params.status === "000000") {
            proxy.$message.success(res1.params.message);
            proxy.$modal.closeLoading();
            
            // 删除参数
            currentParams.delete('startFlag');
            currentParams.delete('oldVersionId');
            
            // 更新URL
            const newSearch = currentParams.toString();
            const newUrl = newSearch 
              ? `${currentUrl.pathname}?${newSearch}${currentUrl.hash}`
              : `${currentUrl.pathname}${currentUrl.hash}`;
            
            window.history.replaceState({}, '', newUrl);
          }
        });
      }
      } else {
        ElMessage.success("工程版本保存失败")
      }
      console.log('保存工程版本', res)
    })
  } else if (type === "reply") {
    console.log('onlineDesign', type, cmd, params)

    if(cmd === 'SDK_undo'){
      sdkUndo(params)
    } else if(cmd === 'SDK_redo'){
      sdkRedo(params)
    } else if(cmd === 'SDK_removeDevice'){
      sdkRemoveDevice(params)
    }
    if(cmd == 'cad_removeDevice'){
      console.log(params);
      
   const deleteMachineParams =  params.map(item=>{
        return {
          ...item,
          taskId:taskId
        }
      })
      undoEquipmentInfoByFlag(deleteMachineParams).then(res => {
    if (res.code == 200) {
      ElMessage.success('删除成功')
    }
  })
  // getHzgctuByTaskId(taskId).then((res)=>{
  //     initHzgctu(res.data)
  //   })
    }
    if (params?.status === "000000") {

    }
  }
});


// // 处理点击事件
// const oniframeMessage = (param) => {
//   const myiframe = cadIframeRef.value
//   if (myiframe && myiframe.contentWindow) {
//     myiframe.contentWindow.postMessage(param, "*");
//     // 先移除之前的事件监听器，防止重复绑定
//     window.removeEventListener('message', handleMessage);
//     // 添加新的事件监听器
//     window.addEventListener('message', handleMessage);
//   }
// }
// // 事件处理函数
// const handleMessage = (event) => {
//   if (event.data.type === 'parentCad') {
    
//   }
// }

</script>
<style>
.el-popper {
  z-index: 99999 !important;
}

.el-popper .el-select-dropdown {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 259px;
}
</style>
<style lang="scss" scoped>
.online-design-container {
  //height: 100%;
  height: calc(100vh - 50px);
  padding: 0;
  margin: 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .top-menu {
  }
}

.home {
  //height: calc(100vh - 84px);
  flex: 1;
  padding: 0;
  // padding-top: 20px;
  ::v-deep .el-select {
    width: 257px;
    height: 27px;
  }

  ::v-deep .el-form-item.el-form-item--default {
    margin-bottom: 0;
    line-height: 0;
  }

  ::v-deep .el-radio__label {
    color: #282B33;
    font-weight: 400;
  }

  ::v-deep .el-form-item--default .el-form-item__content {
    line-height: normal;
  }

  ::v-deep .el-form-item__error {
    right: 22px !important;
    top: 12px !important;
    left: auto;
  }

  ::v-deep .el-select__placeholder {
    color: #797979;
    font-weight: 400;
    font-size: 14px;
  }

  ::v-deep .el-input__inner {
    color: #797979;
    font-weight: 400;
    font-size: 14px;
  }

  h4 {
    font-family: "Noto Sans SC", sans-serif;
  }

  .title-text {
    color: #008486;
    font-weight: bold;
    font-family: "Noto Sans SC", sans-serif;
  }

  .check_radio {
    ::v-deep .el-checkbox__label {
      color: #282b33;
      font-weight: 400;
    }
  }

  .line {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 1px;
    text-align: center;
    font-family: "Noto Sans SC", sans-serif;
    font-weight: bold;

    :last-child {
      margin-bottom: 0;
    }

    ::v-deep .el-table .el-table__header-wrapper th {
      background: #50a9aa !important;
      font-size: 14px;
      color: #ffffff;
      font-weight: 600;
    }

    .tw-ipt {
      width: 84px;
      height: 40px;
      background: #b5dddd;
      //   font-weight: 550;
      font-size: 14px;
      color: #282b33;
      display: flex;
      justify-content: center;
      align-items: center;
      // span {
      //   width: 60px;
      // }
    }

    .radio-group {
      color: #282b33;
      font-weight: 400;

      ::v-deep .el-radio__label {
        color: #282b33;
        font-weight: 400;
      }
    }

    .checkbox-group {
      ::v-deep .el-checkbox__label {
        color: #282b33;
        font-weight: 400;
      }
    }

    .input-table {
      margin-top: -2px;
      margin-bottom: 1px;
    }

    .tw-sct {
      width: 105px;
      height: 40px;
      background: #e4f2f2;
      display: flex;
      justify-content: center;
      align-items: center;

      .in-item {
        width: 82px;
        height: 27px;
      }
    }

    .line-item {
      width: 84px;
      height: 40px;
      background: #b5dddd;
      //   font-weight: 550;
      font-size: 14px;
      color: #282b33;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 0 10px;
      // span {
      //   width: 60px;
      // }
    }

    .line-input {
      // flex: 1;
      width: 294px;
      height: 40px;
      background: #e4f2f2;
      display: flex;
      justify-content: center;
      align-items: center;

      .in-item {
        width: 257px;
        height: 27px;
      }
    }

    .line-no-display {
      width: 294px;
      height: 40px;
      background: #e4f2f2;
      display: flex;
      align-items: center;
    }

    .radio-input {
      width: 294px;
      height: 60px;
      display: flex;
      background: #e4f2f2;
      flex-direction: column;

      .in-item {
        height: 20px;

        ::v-deep .el-input__wrapper {
          width: 150px;
        }
      }
    }

    //按钮
    .line-bnt {
      width: 84px;
      height: 30px;
      background: #0e8b8d;
      border-radius: 5px;
      color: white;
      line-height: 30px;
      cursor: pointer;
      font-size: 12px;
    }
  }

  // 标注样式
  .bz-line {
    display: flex;
    justify-content: flex-start;
    margin-bottom: 1px;
    font-family: "Noto Sans SC", sans-serif;
    font-weight: bold;
    height: 150px;

    .bz-left {
      width: 100px;
      margin-left: 10px;

      .bz-border {
        padding: 0 10px;
        border: #69b5b6 2px solid;
        height: 120px;
      }
    }

    .bz-right {
      width: 500px;
      margin-left: 10px;

      .input-table {
        margin-top: -2px;
        margin-bottom: 1px;
      }

      ::v-deep .el-table .el-table__header-wrapper th {
        background: #50a9aa !important;
        font-size: 14px;
        color: #ffffff;
        font-weight: 600;
      }
    }
  }

  .material {
    ::v-deep .el-table .el-table__header-wrapper th {
      background: #50a9aa !important;
      font-size: 14px;
      color: #ffffff;
      font-weight: 600;
    }

    .active {
      background: #0e8b8d !important;
      color: white !important;
    }
  }

  .small-material {
    ::v-deep .el-table .el-table__header-wrapper th {
      background: #50a9aa !important;
      font-size: 11px !important;
      color: #ffffff;
      font-weight: 600;
    }

    ::v-deep .el-table td .el-table__cell div {
      font-size: 10px !important;
      font-weight: 600;
    }
  }

  .meter-draw {
    ::v-deep .el-table .el-table__header-wrapper th {
      background: #50a9aa !important;
      font-size: 14px;
      color: #ffffff;
      font-weight: 600;
    }

    .draw {
      font-size: 12px;
      color: #282b33;
      font-weight: bold;

      span {
        margin-left: 10px;
      }
    }
  }

  .draw-footer {
    margin: 0 10px 10px 10px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    border: 1px solid #badddd;
  }

  .photo-box {
    padding: 10px;
    display: flex;
    height: 450px;
    justify-content: flex-start;
    background: #e4f2f2;

    .photo-left {
      width: 200px;
      height: 430px;

      .left-tree {
        border: 2px solid #badddd;
        padding: 10px;
        height: 400px;

        .el-tree {
          background: transparent;
        }

        ::v-deep .el-tree-node:focus > .el-tree-node__content {
          background: rgba(80, 169, 170, 0.4);
        }

        ::v-deep .el-tree-node :hover {
          background: rgba(80, 169, 170, 0.1) !important;
        }
      }
    }

    .photo-right {
      margin-left: 10px;
      width: 600px;
      height: 430px;

      .photo-border {
        height: 30px;
        width: 600px;
        border: 2px solid #badddd;
      }

      .photo-table {
        width: 350px;

        ::v-deep .el-table .el-table__header-wrapper th {
          background: #50a9aa !important;
          font-size: 14px;
          color: #ffffff;
          font-weight: 600;
        }
      }

      .photo-img {
        margin-top: -2px;
        width: 250px;
        background: white;
      }
    }
  }

  ::v-deep .el-checkbox__input.is-checked .el-checkbox__inner {
    background: #fff;
  }

  /* 修改Element UI中el-checkbox选中时的对号颜色 */
  ::v-deep .el-checkbox__inner:after {
    border-color: #07888a; /* 将对号颜色改为红色 */
  }

  #myiframe {
    height: 100%;
  }
}
</style>
