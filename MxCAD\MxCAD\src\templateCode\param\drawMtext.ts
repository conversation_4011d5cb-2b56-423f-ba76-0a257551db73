import { MxCpp } from "mxcad";

function drawMtext() {
    let mxcad = MxCpp.getCurrentMxCAD();
    //清空当前显示内容
    mxcad.newFile();

    //把颜色改回黑白色
    mxcad.drawColorIndex = 0;

    mxcad.addLayer("MtextLayer");

    //设置当前图层为"MtextLayer"

    mxcad.drawLayer = "MtextLayer"; 

    mxcad.drawTextStyle = "";

    mxcad.drawMText(0, -100, "控件:\\P多行文字测试", 50, 400, 0, 1);

    mxcad.zoomAll();
    mxcad.updateDisplay();
};

// 调用画多行文字的方法
drawMtext();