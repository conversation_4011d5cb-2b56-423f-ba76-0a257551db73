<template>
  <!--10kV-->
  <div>
    <div class="item_title">
      <el-icon>
        <List/>
      </el-icon>
      <span>10kV</span>
    </div>
    <el-descriptions style="margin-top: 5px;width: 70%" border label-width="15%" :column="1">
      <el-descriptions-item label="常用水泥杆">
        <el-select
            v-model="data.CementTower10KV"
            multiple
            collapse-tags
            collapse-tags-tooltip
            :max-collapse-tags="3"
            placeholder="请选择"
        >
          <el-option
              v-for="item in modelJKOption.CementTower10KVOption"
              :key="item.materialsprojectid"
              :label="item.spec"
              :value="item.materialsprojectid"
          />
        </el-select>
      </el-descriptions-item>
      <el-descriptions-item label="常用导线">
        <el-select
            v-model="data.Line10KV"
            multiple
            collapse-tags
            collapse-tags-tooltip
            :max-collapse-tags="7"
            placeholder="请选择"
        >
          <el-option
              v-for="item in modelJKOption.Line10KVOption"
              :key="item.moduleid"
              :label="item.modulename"
              :value="item.moduleid"
          />
        </el-select>
      </el-descriptions-item>
    </el-descriptions>
  </div>
  <!--排列方式-->
  <div style="margin-top: 10px">
    <div class="item_title">
      <el-icon>
        <List/>
      </el-icon>
      <span>排列方式</span>
    </div>
    <el-table class="input-table" size="small" :data="plfsData" border>
      <el-table-column width="150" label="" align="center" prop="type"></el-table-column>
      <el-table-column label="单回" align="center" prop="oneH">
        <template #default="scope">
          <el-select size="small" style="width: 80%" v-model="data[scope.row.keys[scope.column.no-1]]"
                     placeholder="请选择">
            <el-option
                v-for="item in plfsDataOption.oneHOption"
                :key="item.value"
                :label="item.label"
                :value="item.value">
            </el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column label="双回" align="center" prop="twoH">
        <template #default="scope">
          <el-select size="small" style="width: 80%" v-model="data[scope.row.keys[scope.column.no-1]]"
                     placeholder="请选择">
            <el-option
                v-for="item in plfsDataOption.twoHOption"
                :key="item.value"
                :label="item.label"
                :value="item.value">
            </el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column label="三回" align="center" prop="threeH">
        <template #default="scope">
          <el-select size="small" style="width: 80%" v-model="data[scope.row.keys[scope.column.no-1]]"
                     placeholder="请选择">
            <el-option
                v-for="item in plfsDataOption.threeHOption"
                :key="item.value"
                :label="item.label"
                :value="item.value">
            </el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column label="四回" align="center" prop="fourH">
        <template #default="scope">
          <el-select size="small" style="width: 80%" v-model="data[scope.row.keys[scope.column.no-1]]"
                     placeholder="请选择">
            <el-option
                v-for="item in plfsDataOption.fourHOption"
                :key="item.value"
                :label="item.label"
                :value="item.value">
            </el-option>
          </el-select>
        </template>
      </el-table-column>
    </el-table>
  </div>
  <!--低压-->
  <div style="margin-top: 10px">
    <div class="item_title">
      <el-icon>
        <List/>
      </el-icon>
      <span>低压</span>
    </div>
    <el-descriptions style="margin-top: 5px;width: 70%" border label-width="15%" :column="1">
      <el-descriptions-item label="常用水泥杆">
        <el-select
            v-model="data.CementTowerLow"
            multiple
            collapse-tags
            collapse-tags-tooltip
            :max-collapse-tags="3"
            placeholder="请选择"
        >
          <el-option
              v-for="item in modelJKOption.materialsLowOption"
              :key="item.materialsprojectid"
              :label="item.spec"
              :value="item.materialsprojectid"
          />
        </el-select>
      </el-descriptions-item>
      <el-descriptions-item label="常用导线">
        <el-select
            v-model="data.LineLow"
            multiple
            collapse-tags
            collapse-tags-tooltip
            :max-collapse-tags="7"
            placeholder="请选择"
        >
          <el-option
              v-for="item in modelJKOption.moduleLineLowOption"
              :key="item.moduleid"
              :label="item.modulename"
              :value="item.moduleid"
          />
        </el-select>
      </el-descriptions-item>
    </el-descriptions>
  </div>
  <!--其他参数-->
  <div style="margin-top: 10px">
    <div class="item_title">
      <el-icon>
        <List/>
      </el-icon>
      <span>其他参数</span>
    </div>
    <el-descriptions style="margin-top: 5px;width: 70%" border label-width="15%" :column="3">
      <el-descriptions-item label="耐张段长度(m)">
        <el-input v-model="data.NaiZhangMaxLength" type="number" style="width: 70%" placeholder="请输入"/>
      </el-descriptions-item>
      <el-descriptions-item label="塔间最大档距(m)">
        <el-input v-model="data.TaMaxDistance" type="number" style="width: 70%" placeholder="请输入"/>
      </el-descriptions-item>
      <el-descriptions-item label="塔间最小档距(m)">
        <el-input v-model="data.TaMinDistance" type="number" style="width: 70%" placeholder="请输入"/>
      </el-descriptions-item>
    </el-descriptions>
  </div>
  <div style="margin-top: 20px">
    <el-alert title="注：系统给出均为默认参数，如需调整，请进行更改" type="error" :closable="false"/>
  </div>
</template>
<script setup>
import {
  getGridMaterials10KVData,
  getGridMaterialsLowData,
  getGridModuleLine10KVData, getGridModuleLineLowData
} from "@/api/taskBaseData/index.js";

const data = defineModel('baseData', {type: Object, required: true});

const plfsData = computed(() => {
  return [
    {
      type: '直线',
      keys: ['SingleLine', 'DoubleLine', 'TribleLine', 'QuardraLine']
    },
    {
      type: '转角',
      keys: ['SingleCorner', 'DoubleCorner', 'TribleCorner', 'QuardraCorner']
    },
    {
      type: '耐张',
      keys: ['SingleNaiZhang', 'DoubleNaiZhang', 'TribleNaiZhang', 'QuardraNaiZhang']
    },
    {
      type: '终端',
      keys: ['SingleTerminal', 'DoubleTerminal', 'TribleTerminal', 'QuardraTerminal']
    },
  ]
})
const plfsDataOption = ref({
  oneHOption: [{label: '水平', value: '水平'}, {label: '三角', value: '三角'}],
  twoHOption: [{label: '双水平', value: '双水平'}, {label: '双垂直', value: '双垂直'}, {
    label: '双三角',
    value: '双三角'
  }],
  threeHOption: [{label: '上双三角、下单水平', value: '上双三角、下单水平'}, {
    label: '上双垂直、下单水平',
    value: '上双垂直、下单水平'
  }],
  fourHOption: [{label: '上双三角、下双三角', value: '上双三角、下双三角'}, {
    label: '上双垂直、下双垂直',
    value: '上双垂直、下双垂直'
  }],
})
const modelJKOption = ref({
  CementTower10KVOption: [],
  Line10KVOption: [],
  materialsLowOption: [],
  moduleLineLowOption: []
})

const getGridMaterials10KV = () => {
  getGridMaterials10KVData().then((res) => {
    modelJKOption.value.CementTower10KVOption = res.data
  })
}
const getGridModuleLine10KV = () => {
  getGridModuleLine10KVData().then((res) => {
    modelJKOption.value.Line10KVOption = res.data
  })
}

const getGridMaterialsLowOpt = () => {
  getGridMaterialsLowData().then((res) => {
    modelJKOption.value.materialsLowOption = res.data
  })
}

const getGridModuleLineLowOpt = () => {
  getGridModuleLineLowData().then((res) => {
    modelJKOption.value.moduleLineLowOption = res.data
  })
}

onMounted(() => {
  getGridMaterials10KV()
  getGridModuleLine10KV()
  getGridMaterialsLowOpt()
  getGridModuleLineLowOpt()
})

</script>
<style scoped lang="scss">
.item_title {
  display: flex;
  align-items: center;
  color: #50adaa;
  font-size: 14px;
}
</style>
