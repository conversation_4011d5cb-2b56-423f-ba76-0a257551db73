import { MxCADUiPrPoint, McDbPolyline, McDbArc, MxCpp } from "mxcad";

//画圆弧
async function Mx_drawArc() {
    // 根据起点，中点，端点三个点确定一段圆弧 
    // 获取起点
    let getFristPoint = new MxCADUiPrPoint();
    getFristPoint.setMessage('指定起点');
    let fristPoint = await getFristPoint.go();
    if (!fristPoint) return

    // 获取第二个点
    let getSecondPoint = new MxCADUiPrPoint();
    getSecondPoint.setMessage('指定圆弧的第二个点');
    //动态绘制取点对象
    getSecondPoint.setUserDraw((pt, pw) => {
        let pl = new McDbPolyline();
        pl.addVertexAt(fristPoint)
        pl.addVertexAt(pt);
        pw.drawMcDbEntity(pl)
    })
    let secondPoint = await getSecondPoint.go();
    if (!secondPoint) return;

    // 获取端点
    let getThirdPoint = new MxCADUiPrPoint();
    getThirdPoint.setMessage('指定圆弧的端点');
    //动态绘制圆弧
    getThirdPoint.setUserDraw((pt, pw) => {
        let arc = new McDbArc();
        arc.computeArc(fristPoint.x, fristPoint.y, secondPoint.x, secondPoint.y, pt.x, pt.y);//三点画圆弧
        pw.drawMcDbEntity(arc)
    });
    let thirdPoint = await getThirdPoint.go();
    if (!thirdPoint) return

    // 画圆弧
    let arc = new McDbArc();
    arc.computeArc(fristPoint.x, fristPoint.y, secondPoint.x, secondPoint.y, thirdPoint.x, thirdPoint.y);//三点画圆弧

    const mxcad = MxCpp.getCurrentMxCAD();
    mxcad.drawEntity(arc);
};

// 调用画圆弧的方法
Mx_drawArc();

