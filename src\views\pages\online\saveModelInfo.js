
/** 得到设备存储模型 未转字符串
 * @param {*} pointType Point  Line  ParentMiddle  MLine
 * @param {*} coordinates 
 * @returns 
 */
export const getEquipmentModel = (pointType, coordinates, params) => {
  const {moduleId, legendTypeKey, legendState, legendGuidKey, engineeringId} = params
  const points = []
  if(pointType === "Point"){
    points.push({
      XYZ:[{
        coordinate: [...coordinates.split(' '), 0].join(',')
      }],
      LatLon:[]
    })
  } else if(pointType === "Line" || pointType === "MLine"){
    points.push({
      XYZ:[{
        coordinate: coordinates.map(element => {
          return [...element.split(' '), 0].join(',')
        }).join(';')
      }],
      LatLon:[]
    })
  } else if(pointType === "ParentMiddle"){
    points.push({
      XYZ:[{
        coordinate:[...coordinates.split(' '), 0].join(',')
      }],
      LatLon:[]
    })
  }
  return {
    moduleId: moduleId ?? '', // 顶层id
    legendTypeKey: legendTypeKey ?? '', // 图元类型
    legendState: legendState ?? '', // 状态
    legendGuidKey: legendGuidKey ?? '', // 设备id
    engineeringId: engineeringId ?? '',
    privatepropertys: {},
    equipmentInfo: {
      positionInfo: {
        pointType,
        points,
        angle: 0,
      },
      VirtualEquipmentType:[]
    },
    topologyrelations: {
      AssociatedIn: [],
      AssociatedOut: [],
      AssociatedParent: [],
      AssociatedChild: [],
      AssociatedLabel: [],
      AssociatedLabelOwner: [],
      AssociatedFile: [],
    }
  }
}


export const getEquipmentModel1 = (pointType, coordinates, params) => {
  const {moduleId, legendTypeKey, legendState, legendGuidKey, engineeringId,addFlag} = params
  const points = []
  if(pointType === "Point"){
    points.push({
      XYZ:[{
        coordinate: [...coordinates.split(' '), 0].join(',')
      }],
      LatLon:[]
    })
  } else if(pointType === "Line" || pointType === "MLine"){
    points.push({
      XYZ:[{
        coordinate: coordinates.map(element => {
          return [...element.split(' '), 0].join(',')
        }).join(';')
      }],
      LatLon:[]
    })
  } else if(pointType === "ParentMiddle"){
    points.push({
      XYZ:[{
        coordinate:[...coordinates.split(' '), 0].join(',')
      }],
      LatLon:[]
    })
  }
  return {
    moduleId: moduleId ?? '', // 顶层id
    legendTypeKey: legendTypeKey ?? '', // 图元类型
    legendState: legendState ?? '', // 状态
    legendGuidKey: legendGuidKey ?? '', // 设备id
    engineeringId: engineeringId ?? '',
    addFlag:addFlag??'',
    privatepropertys: {},
    equipment_info: {
      positionInfo: {
        pointType,
        points,
        angle: 0,
      },
      VirtualEquipmentType:[]
    },
    topologyrelations: {
      AssociatedIn: [],
      AssociatedOut: [],
      AssociatedParent: [],
      AssociatedChild: [],
      AssociatedLabel: [],
      AssociatedLabelOwner: [],
      AssociatedFile: [],
    }
  }
}
