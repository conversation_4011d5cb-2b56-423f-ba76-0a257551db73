import{M as f}from"./index-8X61wlK0.js";import{M as c}from"./index--PcCuGx1.js";import{r as V,t as x,v as r}from"./index-D95UjFey.js";import{h as _,a0 as g,_ as h,$ as v,a1 as w,m as B,u as l,B as i}from"./vue-DfH9C9Rx.js";import"./vuetify-B_xYg4qv.js";import"./mxcad-CfPpL1Bn.js";import"./mxdraw-C_n_7lEs.js";import"./mapbox-gl-PN3pDJHq.js";import"./xlsx-Ck5MUZvf.js";const C={class:"px-1"},T=_({__name:"index",setup(M){const{isShow:o,showDialog:s}=V,{items:m,color:n,tab:t,method:u}=x(),p=[{name:"确定",fun:()=>{r&&r(n.value,u.value),s(!1)},primary:!0},{name:"取消",fun:()=>s(!1)}];return(d,e)=>(g(),h(f,{modelValue:l(o),"onUpdate:modelValue":e[1]||(e[1]=a=>i(o)?o.value=a:null),footerBtnList:p,"max-width":"580",title:d.t("496")},{default:v(()=>[w("div",C,[B(c,{modelValue:l(t),"onUpdate:modelValue":e[0]||(e[0]=a=>i(t)?t.value=a:null),items:l(m),"tabs-props":{grow:!0},height:386},null,8,["modelValue","items"])])]),_:1},8,["modelValue","title"]))}});export{T as default};
