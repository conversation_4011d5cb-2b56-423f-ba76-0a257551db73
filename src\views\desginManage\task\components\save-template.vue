<script setup>
import {saveTemplateData} from "@/api/desginManage/GccsDialog.js";
import {ElMessage} from "element-plus";

const props = defineProps({
  id: {
    type: String,
    default: "",
  },
  params: {
    type: Function,
    default: () => {
    },
  }
})

const visible = defineModel('visible', {type: Boolean, default: false})

const formRef = ref()
const form = reactive({templateName: ''})
const rules = reactive({
  templateName: [
    {required: true, message: '请输入', trigger: 'blur'},
  ]
})

const loading = ref(false)
const onSubmit = (formEl) => {
  if (!formEl) return
  formEl.validate((valid) => {
    if (!valid) return

    loading.value = true
    const data = {
      objId: props.id,
      templateName: form.templateName,
      templateType: 'UserEngineeringSetting',
      templateData: JSON.stringify(props.params()),
    }
    saveTemplateData(data).then(res => {
      if (res.code === 200) {
        visible.value = false
        ElMessage.success(res.msg)
      } else {
        ElMessage.error(res.msg)
      }
    }).finally(() => {
      loading.value = false
    })
  })
}

const resetForm = (formEl) => {
  if (!formEl) return
  formEl.resetFields()
}

const closeVisible = () => {
  resetForm(formRef.value)
}
</script>

<template>
  <el-dialog title="另存为模板" v-model="visible" width="30%" append-to-body @close="closeVisible">
    <el-form ref="formRef" :model="form" :rules="rules">
      <el-form-item label="名称" prop="templateName">
        <el-input v-model="form.templateName"/>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" :loading="loading" @click="onSubmit(formRef)">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">

</style>
