<script setup>
import {Search} from "@element-plus/icons-vue";
import {getModuleTreeByEquipmentId, getModuleInfoById, getModulesData} from "@/api/onlineDesign/index.js";
import {useCAdApp} from "@/hooks/useCAdApp.js";
import {useIframeCommunication} from "@/hooks/useIframeCommunication.js";
import {downloadFileDWG} from "@/api/task/index.js";
import {ElMessage} from "element-plus";


const {cadAppSrc} = useCAdApp()

const {sendMessage} = useIframeCommunication()

const iframeSrc = cadAppSrc({
  isPreview: '1'
})

const visible = defineModel('visible', {type: Boolean, default: false})

const props = defineProps({
  currentData: {
    type: Object,
    default: null
  },
  equipmentId: {
    type: String,
    default: ''
  }
})
const emit = defineEmits(['submit'])

const loading = ref(false);

const getTreeData = () => {
  loading.value = true;
  const {flag, moduleid, childmoduleid, id} = props.currentData
  const moduleId = flag ? moduleid : childmoduleid
  const params = {
    equipmentId: props.equipmentId,
    nodeId: id,
    moduleId,
    flag,
  }
  getModuleTreeByEquipmentId(params).then(res => {
    treeData.isComponent = res.data.isComponent
    treeData.data = res.data.moduleTree
    console.log('treeData', treeData.data)
  }).finally(()=> {
    loading.value = false
  })
}


const defaultProps = {
  children: 'children',
  label: 'moduletypename',
}

const treeData = reactive({
  isComponent: false,
  data: []
})
const handleNodeClick = (data) => {
  console.log(data)

  currentNodeData.value = data
  reset()
  query()
}

const moduleTableData = ref([])
const getModuleTableData = () => {
  loading.value = true;
  const {searchValue, ...rest} = form
  let value = []
  if (searchValue) {
    value = searchValue.split(' ').filter(item => item)
  }
  console.log('value', value)
  const params = {
    moduletypekey: currentNodeData.value?.moduletypekey,
    isComponent: treeData.isComponent,
    ...rest,
    searchValue: value
  }
  getModulesData(params).then((res) => {
    moduleTableData.value = res.data
  }).finally(()=> {
    loading.value = false
  })
}

const currentModuleTableRow = ref(null)

const handleCurrentChange = (val) => {
  console.log('handleCurrentChange', val)
  if(val) {
    currentModuleTableRow.value = val
    getTabData()
  }
}


const formRef = ref(null)

const form = reactive({
  searchType: '1',
  searchName: '',
  searchValue:''
})


const multipleData = ref(
    {
      compose: [],
      draw: [],
      property: []
    }
)

const getTabData = () => {
  const params = {
    moduleId: currentModuleTableRow.value?.moduleid,
  }
  getModuleInfoById(params).then(res => {
    multipleData.value = res.data
  })
}

const drawDefaultProps = {
  children: 'children',
  label: 'name',
}

const currentNodeData = ref(null)

const handleDrawNodeClick = (data) => {
  console.log(data)
  getDwgFile(data)
}
const cadIframeRef = ref(null)

const getDwgFile = (data) => {
  const id = data.drawingid
  if (!id) return
  downloadFileDWG(id).then(res => {
    nextTick(() => {
      setTimeout(() => {
        const params = {
          type: "cadPreview",
          content: "Mx_cadPreview",
          formData: {
            openUrl: res,
          }
        }
        sendMessage(cadIframeRef.value, params, (res => {
        }))
      }, 2000)
    })
  })
}



const query = () => {
  getModuleTableData()
}

const reset = () => {
  formRef.value.resetFields()
}

const activeName = ref('1')


const onSubmit = () => {
  if(!currentModuleTableRow.value) return ElMessage.error('请选择！')
  emit('submit', currentModuleTableRow.value)
  visible.value = false
}

const closeVisible = () => {
  visible.value = false
}

onMounted(() => {
  getTreeData()
})
</script>

<template>
  <el-dialog
      v-model="visible"
      append-to-body
      draggable
      overflow
      :title="props.currentData.title"
      width="60%"
      @close="closeVisible"
  >
    <div v-loading="loading" class="" style="">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card header="模块类别">
            <el-scrollbar height="650px">
              <el-tree
                  :data="treeData.data"
                  :props="defaultProps"
                  highlight-current
                  @node-click="handleNodeClick"
              />
            </el-scrollbar>
          </el-card>
        </el-col>
        <el-col :span="18">
          <el-form ref="formRef" :model="form" inline>
            <el-form-item label="" prop="searchType">
              <el-select v-model="form.searchType" clearable disabled style="width: 160px;">
                <el-option label="局部" value="1"></el-option>
                <el-option label="圈部" value="2"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="" prop="searchName">
              <el-select v-model="form.searchName" clearable style="width: 160px;">
                <el-option label="模块名称" value="modulename"></el-option>
                <el-option label="模块编码" value="modulecode"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="" prop="searchValue">
              <el-input v-model="form.searchValue" placeholder="请输入关键字">
                <template #append>
                  <el-button :icon="Search" @click="query"/>
                </template>
              </el-input>
            </el-form-item>
          </el-form>
          <el-table :data="moduleTableData" :height="350" border highlight-current-row stripe style="width: 100%;margin-bottom: 8px" @current-change="handleCurrentChange">
<!--            <el-table-column type="selection" width="55" align="center"/>-->
            <el-table-column align="center" label="模块名称" prop="modulename" width=""/>
            <el-table-column align="center" label="模块编码" prop="modulecode" width=""/>
            <el-table-column align="center" label="电压等级" prop="voltage"/>
          </el-table>
          <el-tabs v-model="activeName">
            <el-tab-pane label="组成" name="1">
              <el-table :data="multipleData.compose" :height="250" border stripe style="width: 100%">
                <el-table-column align="center" label="物料名称" prop="materialName">
                </el-table-column>
                <el-table-column align="center" label="物料规格" prop="spec">
                </el-table-column>
                <el-table-column align="center" label="单位" prop="designUnit" width="80">
                </el-table-column>
                <el-table-column align="center" label="数量" prop="num" width="80">
                </el-table-column>
              </el-table>
            </el-tab-pane>
            <el-tab-pane label="属性" name="2">
              <el-table :data="multipleData.property" :height="250" border stripe style="width: 100%">
                <el-table-column align="center" label="属性名" prop="propertyName"/>
                <el-table-column align="center" label="属性值" prop="propertyValue"/>
              </el-table>
            </el-tab-pane>
            <el-tab-pane label="图纸" name="3">
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-card header="图纸列表">
                    <el-scrollbar height="200px">
                    <el-tree
                        :data="multipleData.draw"
                        :props="drawDefaultProps"
                        highlight-current
                        @node-click="handleDrawNodeClick"
                    >
                      <template #default="{ node, data }">
                        <el-tooltip
                            :content="node.label"
                            effect="dark"
                            placement="right"
                        >
                          <el-text class="" truncated>
                            {{ node.label }}
                          </el-text>
                        </el-tooltip>
                      </template>
                    </el-tree>
                    </el-scrollbar>
                  </el-card>
                </el-col>
                <el-col :span="16">
                  <el-card header="预览">
                    <div class="" style="height: 200px">
                      <iframe
                          ref="cadIframeRef"
                          :src="iframeSrc"
                          style="width: 100%;height: 100%"
                      />
                    </div>
                  </el-card>
                </el-col>
              </el-row>
            </el-tab-pane>
          </el-tabs>
        </el-col>
      </el-row>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeVisible">取消</el-button>
        <el-button type="primary" @click="onSubmit">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>

</style>
