import {McDbCircle, MxCADUiPrDist, MxCADUiPrPoint, MxCpp} from "mxcad";

// 画圆
async function Mx_drawCircle(){
    // 创建圆
    const circle = new McDbCircle();

    // 获取圆心
    const getCenter = new MxCADUiPrPoint();
    getCenter.setMessage('请确定圆心位置\n');
    const center = await getCenter.go();
    if (!center) return;
    circle.center = center;

    // 获取圆半径
    const getRadius = new MxCADUiPrDist();
    getRadius.setBasePt(center);
    getRadius.setMessage('请输入圆半径');
    // 动态绘制圆
    getRadius.setUserDraw((pt, pw) => {
        const r = pt.distanceTo(center);
        circle.radius = r;
        pw.drawMcDbEntity(circle)
    })
    const radiusVal = await getRadius.go();
    if (!radiusVal) return;
    circle.radius = getRadius.value();

    // 绘制圆
    const mxobj = MxCpp.getCurrentMxCAD();
    mxobj.drawEntity(circle);
};

// 调用画圆的方法
Mx_drawCircle();