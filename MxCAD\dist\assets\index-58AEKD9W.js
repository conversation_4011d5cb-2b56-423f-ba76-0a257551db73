import{M as ne}from"./index-itnQ6avM.js";import{Q as re,b as ue,n as de,v as me,f as R,ai as pe,a6 as fe,m as ce,c as be,e as X,P as H,X as J,a as ge,U as w,Z as Ve,V as Q}from"./index-CzBriCFR.js";import{M as $,g as ye,h as ve,y as K,X as xe}from"./mxcad-DrgW2waE.js";import{d as C,w as Me,r as Pe,h as ke,c as De,a3 as ee,a4 as a,u as l,B as U,_ as te,a0 as b,m as s,Q as _,V as p,n as we,a9 as _e}from"./vue-Cj9QYd7Z.js";import{B as z,a1 as Be,b as Ue,d as Ie,z as le,I as S,L as I}from"./vuetify-BqCp6y38.js";import"./mxdraw-BQ5HhRCY.js";import"./handsontable-Ch5RdAT_.js";import"./mapbox-gl-DQAr7S0z.js";const Ce=()=>{const F=C(),k=C(!1),{isShow:G,showDialog:L}=re(!1,"Mx_Insert",i=>{L(),i&&(F.value={name:i.name||"",filePath:i.filePath,id:i.id??i.filePath},k.value=!!i.isBlockLibrary)}),T=C([]);Me(G,i=>{i&&(T.value=fe().filter(r=>!r.startsWith("*")).map(r=>({name:r,id:r})))}),ue("Mx_add_insert_blocks_list",i=>{const r=i.type,x=i.hash,M=i.file;let o="";if(x){let{mxfilepath:e=""}=de()||{};o=me()+e+x+"."+r+".mxweb"}else r==="mxweb"&&(o=URL.createObjectURL(M.source.source));const t={name:M.name,filePath:o,id:x||o};T.value.unshift(t),F.value=t});const f=async()=>{{const i=ge();await ce(!1,"Mx_add_insert_blocks_list"),i&&be("MxFullscreen")}},E=R(!0,"Mx_InsertDialog_is_get_insertion_point"),u=R(!0,"Mx_InsertDialog_is_get_proportion"),g=R(!0,"Mx_InsertDialog_is_uniform_proportion"),d=R(!0,"Mx_InsertDialog_is_get_rotation"),V=R(!1,"Mx_InsertDialog_is_decomposition"),c=R(!1,"Mx_InsertDialog_is_auto_compute_origin"),y=C(!1),A=C(!0),v=Pe({x:0,y:0,z:0}),D=pe({x:1,y:1,z:1},"Mx_InsertDialog_proportion"),B=C(0);function O(i,r,x,M){const o=i-x,t=r-M;let e=0;return o==0?e=Math.PI/2:e=Math.atan(Math.abs(t/o)),o<0&&t>=0?e=Math.PI-e:o<0&&t<0?e=Math.PI+e:o>=0&&t<0&&(e=Math.PI*2-e),e+Math.PI}return{isShow:G,showDialog:L,insertBlock:async()=>{let i=$.App.getCurrentMxCAD();const{filePath:r,hash:x,name:M}=F.value||{},o=i.getDatabase().getBlockTable();let t;if(M)t=o.get(M);else if(!r)return X().warning("请选择正确的图块");if((!t||!t.isValid())&&r&&(H().showLoading(),X().info("图块加载中..."),t=await i.insertBlock(r,x||r),H().hideLoading(),!t.isValid())){X().error("图块加载失败");return}if(k.value?A.value:c.value){let n=t.getMcDbBlockTableRecord();if(!n)return;let m=n.getBoundingBox();if(m.ret){let N=m.minPt.addvec(m.maxPt.sub(m.minPt).mult(.5));n.origin=N}}let e=new ye;e.blockTableRecordId=t;let h=e.getBoundingBox(),W=0;if(h.ret){let n=h.maxPt.distanceTo(h.minPt);n>1e-5&&(W=i.getMxDrawObject().screenCoordLong2Doc(100)/n,e.setScale(W))}let P=new ve;if(L(!1),E.value){P.setMessage(`
指定插入基点`),P.setUserDraw((m,N)=>{e.position=m,N.drawMcDbEntity(e)});let n=await P.go();if(!n)return;e.position=n}else e.position=new K(v.x,v.y,v.z);if(u.value){const n=h.minPt,m=h.maxPt;let N=m.x-n.x,se=m.y-n.y,Y=(N+se)/3;Y<1e-5&&(Y=1),P.setMessage(`
输入比例因子<1.00>`),e.setScale(-W),P.setUserDraw((oe,ie)=>{const q=oe.distanceTo(e.position);if(q<1e-5)return;let Z=q/Y;Z>1e5&&(Z=1e5),e.setScale(Z),ie.drawMcDbEntity(e)}),await P.go()}else g.value?e.setScale(D.x):e.scaleFactors=new K(D.x,D.y,D.z);d.value?(P.setBasePt(e.position),P.setUserDraw((n,m)=>{e.rotation=O(e.position.x,e.position.y,n.x,n.y),m.drawMcDbEntity(e)}),await P.go()):e.rotation=B.value;const ae=i.drawEntity(e);(k.value?y.value:V.value)&&setTimeout(()=>{let n=new xe;$.App.MxCADAssist.MxExplode(n.imp),n.copyFormAryId([ae]),$.App.MxCADAssist.MxExplode(n.imp),J()}),J(),X().success("插入图块成功!")},list:T,currentItem:F,openFile:f,isGetInsertionPoint:E,insertionPoint:v,isGetProportion:u,proportion:D,isUniformProportion:g,isGetRotation:d,rotation:B,isDecomposition:V,isAutoComputeOrigin:c,isBlockLibrary:k,isBlockLibraryDecomposition:y,isBlockLibraryAutoComputeOrigin:A}},Le={class:"px-3"},Ae={class:"d-flex"},he={class:""},Oe=ke({__name:"index",setup(F){const{isShow:k,showDialog:G,insertBlock:L,list:T,currentItem:f,openFile:E,isGetInsertionPoint:u,insertionPoint:g,isGetProportion:d,proportion:V,isUniformProportion:c,isGetRotation:y,rotation:A,isDecomposition:v,isAutoComputeOrigin:D,isBlockLibrary:B,isBlockLibraryDecomposition:O,isBlockLibraryAutoComputeOrigin:j}=Ce(),i=C(),r=[{name:"确定",fun:L,primary:!0},{name:"关闭",fun:()=>G(!1)}],x={n:()=>{(i.value?.$el).getElementsByTagName("input")[0]?.focus()},b:E,s:()=>{u.value=!u.value},e:()=>{d.value=!d.value},c:()=>{y.value=!y.value},u:()=>{c.value=!c.value},d:()=>{v.value=!v.value},enter:()=>{L()}},M=De(()=>f.value?.filePath?new URL(f.value.filePath).pathname.split("/").pop():"");return(o,t)=>(te(),ee(ne,{title:o.t("531"),modelValue:l(k),"onUpdate:modelValue":t[14]||(t[14]=e=>U(k)?k.value=e:null),"max-width":"470",footerBtnList:r,keys:x},{default:a(()=>[b("div",Le,[s(le,null,{default:a(()=>[s(z,{class:"mt-1"},{default:a(()=>[b("div",Ae,[s(l(Be),{class:"mt-1",ref_key:"autocomplete",ref:i,items:l(T),"item-title":"name","return-object":"",modelValue:l(f),"onUpdate:modelValue":t[0]||(t[0]=e=>U(f)?f.value=e:null)},{prepend:a(()=>[s(w,{class:"","key-name":"N"},{default:a(()=>[_(p(o.t("209")),1)]),_:1})]),_:1},8,["items","modelValue"]),s(Ve,{onClick:l(E),class:"ml-1"},{default:a(()=>[s(w,{"key-name":"B"},{default:a(()=>[_(p(o.t("210")),1)]),_:1}),s(Ue,{icon:"class:iconfont more"})]),_:1},8,["onClick"])]),l(f)&&l(f).filePath?(te(),ee(Ie,{key:0,text:l(f).filePath},{activator:a(({props:e})=>[b("p",we({class:"mt-1 text-truncate"},e),p(o.t("532"))+": "+p(M.value),17)]),_:1},8,["text"])):_e("",!0)]),_:1}),s(z,{cols:3,class:"h-100"})]),_:1}),s(le,{"align-stretch":""},{default:a(()=>[s(z,{cols:"4","align-self":"stretch"},{default:a(()=>[s(Q,{title:o.t("479"),class:"h-100"},{default:a(()=>[s(S,{class:"",modelValue:l(u),"onUpdate:modelValue":t[1]||(t[1]=e=>U(u)?u.value=e:null)},{label:a(()=>[s(w,{"key-name":"S"},{default:a(()=>[_(p(o.t("215")),1)]),_:1})]),_:1},8,["modelValue"]),s(I,{class:"mt-1",type:"number",modelValue:l(g).x,"onUpdate:modelValue":t[2]||(t[2]=e=>l(g).x=e),disabled:l(u)},{prepend:a(()=>t[15]||(t[15]=[b("span",{class:""}," X:",-1)])),_:1},8,["modelValue","disabled"]),s(I,{class:"mt-1",type:"number",modelValue:l(g).y,"onUpdate:modelValue":t[3]||(t[3]=e=>l(g).y=e),disabled:l(u)},{prepend:a(()=>t[16]||(t[16]=[b("span",{class:""}," Y:",-1)])),_:1},8,["modelValue","disabled"]),s(I,{class:"mt-1",type:"number",modelValue:l(g).z,"onUpdate:modelValue":t[4]||(t[4]=e=>l(g).z=e),disabled:l(u)},{prepend:a(()=>t[17]||(t[17]=[b("span",{class:""}," Z:",-1)])),_:1},8,["modelValue","disabled"])]),_:1},8,["title"])]),_:1}),s(z,{cols:"4","align-self":"stretch"},{default:a(()=>[s(Q,{title:o.t("240"),class:"h-100"},{default:a(()=>[s(S,{class:"",modelValue:l(d),"onUpdate:modelValue":t[5]||(t[5]=e=>U(d)?d.value=e:null)},{label:a(()=>[s(w,{"key-name":"E"},{default:a(()=>[_(p(o.t("215")),1)]),_:1})]),_:1},8,["modelValue"]),s(I,{class:"mt-1",type:"number",step:"0.001",modelValue:l(V).x,"onUpdate:modelValue":t[6]||(t[6]=e=>l(V).x=e),disabled:l(d)},{prepend:a(()=>t[18]||(t[18]=[b("span",{class:""}," X:",-1)])),_:1},8,["modelValue","disabled"]),s(I,{class:"mt-1",type:"number",modelValue:l(V).y,"onUpdate:modelValue":t[7]||(t[7]=e=>l(V).y=e),step:"0.001",disabled:l(d)||l(c)},{prepend:a(()=>t[19]||(t[19]=[b("span",{class:""}," Y:",-1)])),_:1},8,["modelValue","disabled"]),s(I,{class:"mt-1",type:"number",modelValue:l(V).z,"onUpdate:modelValue":t[8]||(t[8]=e=>l(V).z=e),step:"0.001",disabled:l(d)||l(c)},{prepend:a(()=>t[20]||(t[20]=[b("span",{class:""}," Z:",-1)])),_:1},8,["modelValue","disabled"]),s(S,{class:"ml-4 mt-1",modelValue:l(c),"onUpdate:modelValue":t[9]||(t[9]=e=>U(c)?c.value=e:null)},{label:a(()=>[s(w,{"key-name":"U"},{default:a(()=>[_(p(o.t("533")),1)]),_:1})]),_:1},8,["modelValue"])]),_:1},8,["title"])]),_:1}),s(z,{cols:"4","align-self":"stretch"},{default:a(()=>[s(Q,{title:o.t("354"),class:"h-100"},{default:a(()=>[s(S,{class:"",modelValue:l(y),"onUpdate:modelValue":t[10]||(t[10]=e=>U(y)?y.value=e:null)},{label:a(()=>[s(w,{"key-name":"C"},{default:a(()=>[_(p(o.t("215")),1)]),_:1})]),_:1},8,["modelValue"]),s(I,{class:"mt-1",type:"number",modelValue:l(A),"onUpdate:modelValue":t[11]||(t[11]=e=>U(A)?A.value=e:null),disabled:l(y)},{prepend:a(()=>[b("span",he,p(o.t("281"))+":",1)]),_:1},8,["modelValue","disabled"])]),_:1},8,["title"])]),_:1})]),_:1}),s(S,{class:"mt-2","model-value":l(B)?l(O):l(v),"onUpdate:modelValue":t[12]||(t[12]=e=>{l(B)?O.value=!!e:v.value=!!e})},{label:a(()=>[s(w,{"key-name":"D"},{default:a(()=>[_(p(o.t("534")),1)]),_:1})]),_:1},8,["model-value"]),s(S,{class:"mt-2","model-value":l(B)?l(j):l(D),"onUpdate:modelValue":t[13]||(t[13]=e=>{l(B)?j.value=!!e:D.value=!!e})},{label:a(()=>[s(w,{"key-name":"D"},{default:a(()=>[_(p(o.t("535")),1)]),_:1})]),_:1},8,["model-value"])])]),_:1},8,["title","modelValue"]))}});export{Oe as default};
