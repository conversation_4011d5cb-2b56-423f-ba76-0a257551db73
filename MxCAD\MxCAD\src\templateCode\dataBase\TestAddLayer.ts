import { McDbLayerTableRecord, MxCpp } from "mxcad";

// 添加图层
function MxTest_AddLayer() {
    // 获取当前mxcad对象
    let mxcad = MxCpp.App.getCurrentMxCAD();

    // 获取数据库图层表
    let layerTable = mxcad.getDatabase().getLayerTable();
    // 判断图层表中是否存在xxx11图层
    if (!layerTable.has("xxx11")) {
        // 不存在则创建xxx11图层
        let newLayer = new McDbLayerTableRecord();
        newLayer.name = "xxx11";
        layerTable.add(newLayer);
    }

    if (layerTable.has("xxx11")) {
        // 存在则打印日志
        console.log("add layer ok");
    }
};

// 调用添加图层的方法
MxTest_AddLayer(); 