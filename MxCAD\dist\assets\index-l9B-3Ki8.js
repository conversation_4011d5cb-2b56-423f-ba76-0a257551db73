import{M as V}from"./index-itnQ6avM.js";import{aG as c,l as g,aH as o,aI as x,aJ as r,V as _}from"./index-CzBriCFR.js";import{L as m}from"./vuetify-BqCp6y38.js";import{h,d as p,w,a3 as y,a4 as n,u as T,B,_ as D,a0 as i,m as u,V as f}from"./vue-Cj9QYd7Z.js";import"./mxcad-DrgW2waE.js";import"./mxdraw-BQ5HhRCY.js";import"./handsontable-Ch5RdAT_.js";import"./mapbox-gl-DQAr7S0z.js";const M={class:"px-3"},R={class:"px-2"},U={class:""},k={class:""},I=h({__name:"index",setup(E){const t=p(""),l=p(0);w(o,e=>{e||(t.value="",l.value=0)});const d=[{name:"确定",fun:()=>{x({text:t.value,angle:THREE.MathUtils.degToRad(l.value)}),t.value=""},primary:!0},{name:"关闭",fun:()=>r(!1)}];c((e={})=>{typeof e.text=="string"&&(t.value=e.text),typeof e.angle=="number"&&(l.value=g(THREE.MathUtils.radToDeg(e.angle),2))});const v={esc:()=>r(!1),enter:d[0].fun};return(e,a)=>(D(),y(V,{title:e.t("645"),"max-width":"360",modelValue:T(o),"onUpdate:modelValue":a[2]||(a[2]=s=>B(o)?o.value=s:null),footerBtnList:d,persistent:"",keys:v},{default:n(()=>[i("div",M,[u(_,{title:e.t("646")},{default:n(()=>[i("div",R,[u(m,{class:"mt-1",modelValue:t.value,"onUpdate:modelValue":a[0]||(a[0]=s=>t.value=s),autofocus:""},{prepend:n(()=>[i("span",U,f(e.t("599"))+":",1)]),_:1},8,["modelValue"]),u(m,{class:"mt-1 w-50",modelValue:l.value,"onUpdate:modelValue":a[1]||(a[1]=s=>l.value=s),type:"number"},{prepend:n(()=>[i("span",k,f(e.t("483"))+":",1)]),_:1},8,["modelValue"])])]),_:1},8,["title"])])]),_:1},8,["title","modelValue"]))}});export{I as default};
