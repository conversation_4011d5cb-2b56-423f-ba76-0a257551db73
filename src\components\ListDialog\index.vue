<template>
  <div v-if="flag" :style="dialogPositionStyle" class="list-dialog">
    <ul>
      <li
          :class="active === index ? 'active' : ''"
          v-for="(item, index) in list"
          @click="handleClick(item, index)"
          :key="index"
      >
        <!-- <span >{{ item.value }}</span> -->
        <span
            style="
            width: 50px;
            text-align: center;
            display: block;
            height: 37px;
            position: relative;
          "
        >
          <img v-if="item.value" style="position: absolute;top: 50%;left: 50%;transform:
          translate(-50%, -55%);width: 26px;height: 26px;"
               :src='imageSrc(item.value)' alt="裂图">
        </span>
        <span>{{ item.name }}</span>
      </li>
    </ul>
  </div>
</template>
<script setup>
import mitt from "mitt";
import useAppStore from "@/store/modules/app";

const {proxy} = getCurrentInstance();
import {ElMessageBox} from "element-plus";

const emit = defineEmits();
const appStore = useAppStore();
const EventBus = mitt();
const props = defineProps({
  list: {
    type: Array,
    default: () => [],
  },
  flag: {
    type: Boolean,
    default: false,
  },
  info: {
    type: Object,
    default: () => ({}),
  },
  dialogPositionStyle: {
    type: Object,
    default: () => ({}),
  },
});
watch(
    () => props.info,
    (newInfo, oldInfo) => {
      active.value = "";
      //   let doc=document.querySelector('.list-dialog');
      //   doc.style.left=(newInfo.index*120-5)+'px';
    }
);
const active = ref(0);
// 动态加载图片路径
const imageSrc = (path) => {
  try {
    return new URL(`/src/assets/images/在线设计/${path}`, import.meta.url).href;
  } catch (e) {
    console.error('图片路径错误:', path);
    return '';
  }
};

const handleClick = (item, index) => {
  const list = document.querySelectorAll(".li .onlineFlase");
  list.forEach((lit) => {
    lit.style.removeProperty("background-image");
  });
  active.value = index;
  emit("update", false, item);
};
</script>
<style scoped lang="scss">
.list-dialog {
  background: #ffffff;
  border-radius: 6px 6px 6px 6px;
  //border: 1px solid #44A3A6;
  width: 184px;
  position: absolute;
  z-index: 1000;
  left: -5px;
  margin-left: 10px;
  margin-top: 4px;

  ul {
    border-radius: 6px 6px 6px 6px;
    border: 1px solid #44a3a6;
    list-style: none;
    margin: 0;
    padding: 0;
    max-height: 700px;
    overflow-y: auto;

    :first-child {
      margin-top: 1px;
    }

    li {
      font-family: "Noto Sans SC", sans-serif;
      display: flex;
      width: 180px;
      height: 38px;
      background: #e4f2f2;
      border-radius: 4px 4px 4px 4px;
      font-weight: 500;
      font-size: 14px;
      color: #003634;
      line-height: 38px;
      margin-left: 1.5px;
      margin-bottom: 1px;
    }

    li:hover {
      cursor: pointer;
    }

    .active {
      background: #0e8b8d;
      color: #fff;
    }
  }
}
</style>
