import request from '@/utils/request'

export function getList(params) {
    return request({
        url: '/personalization',
        method: 'get',
        params,
    })
}
export function saveData(data) {
    return request({
        url: '/personalization',
        method: 'post',
        data,
    })
}

export function deletePersonalization(data) {
    return request({
        url: '/personalization/deletePersonalization',
        method: 'post',
        data,
    })
}


export function getSubPoleModelList(params) {
    return request({
        url: '/rodLineJointDraw/getModuleByProperty',
        method: 'get',
        params,
    })
}

export function getModuleByTowerHeight(params) {
    return request({
        url: '/rodLineJointDraw/getModuleByTowerHeight',
        method: 'get',
        params,
    })
}