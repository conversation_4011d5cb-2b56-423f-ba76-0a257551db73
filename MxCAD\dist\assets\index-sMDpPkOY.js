import{h as K,d as v,w as H,a3 as U,a4 as t,u as R,B as X,_ as A,a0 as Y,m as e,Q as n,V as y,n as Z,H as ee,I as le,a9 as P}from"./vue-Cj9QYd7Z.js";import{Q as ae,s as M,$ as Q,q as te,r as se,U as V,W as ue,D as oe,V as ne,a2 as S,a8 as re,ay as ie,_ as me}from"./index-CzBriCFR.js";import{M as de}from"./index-itnQ6avM.js";import{M as fe,a as $,i as ve}from"./mxcad-DrgW2waE.js";import{B as o,V as x,z as b,h as ce,a as pe,G as ye,H as q,I as Ve}from"./vuetify-BqCp6y38.js";import"./mxdraw-BQ5HhRCY.js";import"./handsontable-Ch5RdAT_.js";import"./mapbox-gl-DQAr7S0z.js";const Ce={class:"px-2 mt-5"},ke=K({__name:"index",setup(ge){const{isShow:_,showDialog:G}=ae(!1,"Mx_QuickSelect");let c={};const I=v([]),g=v("所有图元");H(_,s=>{s&&re(()=>{c={};const a=new ve;a.allSelect(),a.forEach(l=>{const u=l.getObjectName();c[u]?c[u].push(l):c[u]=[l]}),I.value=[{title:"所有图元",value:"all"},...Object.keys(c).map(l=>({title:ie[l]||l,value:l}))],g.value=I.value[0].value})});const O=[{title:"Color",value:1},{title:"Layer",value:2},{title:"LineType",value:3}],i=v(O[0].value),j=[{title:"= "+M("等于"),value:"="},{title:"!= "+M("不等于"),value:"!="},{title:"> "+M("大于"),value:">"},{title:M("全部选择"),value:"all"}],m=v(j[0].value),w=v(!0),{list:h}=Q(te()),C=v(h.value[0]?.name),{lineTypeList:D}=Q(se()),k=v(D.value[0]?.name),L=v(!0);H(i,()=>{C.value||(C.value=h.value[0].name),k.value||(k.value=D.value[0].name)});const B=v(),z=[{name:"确定",fun:()=>{G(!1);const s=fe.getCurrentMxCAD(),a=l=>{let u;if(i.value===1&&(u="trueColor"),i.value===2&&(u="layer"),i.value===3&&(u="linetype"),!u)return;const F=p=>{if(p instanceof $&&u==="trueColor"){const r=new $,d=B.value?.color?.method;if(d!==S.kByColor&&(d===S.kByLayer&&p.method===d||d===S.kByBlock&&p.method===d||d===S.kByACI&&p.colorIndex===B.value?.color?.index))return!0;const{r:f,g:W,b:J}=new THREE.Color(B.value?.color?.color);r.setRGB(f*255,W*255,J*255);const N=Number(r.getColorValue()),T=Number(p.getColorValue());return m.value==="="?N===T:m.value==="!="?N!==T:m.value===">"?N<T:!1}return!1};L.value||s.mxdraw.clearMxCurrentSelect();const E=[];c[l]?.forEach(p=>{let r=!1;const d=p.getMcDbEntity();if(d){if(m.value==="all")r=!0;else{const f=d[u];F(f)&&(r=!0),m.value==="="?(u==="layer"&&f===C.value||u==="linetype"&&f===k.value)&&(r=!0):m.value==="!="?(u==="layer"&&f!==C.value||u==="linetype"&&f!==k.value)&&(r=!0):m.value===">"&&(u==="layer"&&f>C.value||u==="linetype"&&f>k.value)&&(r=!0),w.value||(r=!r)}r&&E.push(p)}}),s.addCurrentSelect(E,E.length<30)};g.value==="all"?Object.keys(c).forEach(l=>{a(l)}):w.value?a(g.value):Object.keys(c).filter(l=>l!==g.value).forEach(l=>{a(l)}),s.updateDisplay()},primary:!0},{name:"关闭",fun:()=>G(!1)}];return(s,a)=>(A(),U(de,{title:s.t("292"),"max-width":"320",modelValue:R(_),"onUpdate:modelValue":a[6]||(a[6]=l=>X(_)?_.value=l:null),footerBtnList:z},{default:t(()=>[Y("div",Ce,[e(b,null,{default:t(()=>[e(o,{cols:"4",class:"text-right"},{default:t(()=>[e(V,{"key-name":"V"},{default:t(()=>[n(y(s.t("617")),1)]),_:1}),a[7]||(a[7]=n(": "))]),_:1}),e(o,{cols:"6"},{default:t(()=>[e(x,{class:"mx-1",items:[s.t("618")],"model-value":s.t("618")},null,8,["items","model-value"])]),_:1}),e(o,{cols:"2"},{default:t(()=>[e(ue,{disabled:""})]),_:1})]),_:1}),e(b,null,{default:t(()=>[e(o,{cols:"4",class:"text-right"},{default:t(()=>[e(V,{"key-name":"B"},{default:t(()=>[n(y(s.t("619")),1)]),_:1}),a[8]||(a[8]=n(": "))]),_:1}),e(o,{cols:"6"},{default:t(()=>[e(x,{class:"mx-1",items:I.value,modelValue:g.value,"onUpdate:modelValue":a[0]||(a[0]=l=>g.value=l)},null,8,["items","modelValue"])]),_:1}),e(o,{cols:"2"})]),_:1}),e(b,null,{default:t(()=>[e(o,{cols:"4",class:"text-right","align-self":"start"},{default:t(()=>[e(V,{"key-name":"P"},{default:t(()=>[n(y(s.t("620")),1)]),_:1}),a[9]||(a[9]=n(": "))]),_:1}),e(o,{cols:"6"},{default:t(()=>[e(ce,{items:O,border:"",height:"160",density:"compact",variant:"text"},{item:t(({props:l})=>[e(pe,Z(l,{onClick:u=>i.value=l.value,class:i.value===l.value?"bg-light-blue-darken-2":""}),null,16,["onClick","class"])]),_:1})]),_:1}),e(o,{cols:"2"})]),_:1}),e(b,null,{default:t(()=>[e(o,{cols:"4",class:"text-right"},{default:t(()=>[e(V,{"key-name":"O"},{default:t(()=>[n(y(s.t("621")),1)]),_:1}),a[10]||(a[10]=n(": "))]),_:1}),e(o,{cols:"6"},{default:t(()=>[e(x,{class:"mx-1",items:j,modelValue:m.value,"onUpdate:modelValue":a[1]||(a[1]=l=>m.value=l)},null,8,["modelValue"])]),_:1}),e(o,{cols:"2"})]),_:1}),e(b,null,{default:t(()=>[e(o,{cols:"4",class:"text-right"},{default:t(()=>[e(V,{"key-name":"V"},{default:t(()=>[n(y(s.t("622")),1)]),_:1}),a[11]||(a[11]=n(": "))]),_:1}),e(o,{cols:"6"},{default:t(()=>[ee(e(oe,{ref_key:"selectColor",ref:B},null,512),[[le,i.value===1]]),i.value===2?(A(),U(x,{key:0,modelValue:C.value,"onUpdate:modelValue":a[2]||(a[2]=l=>C.value=l),items:R(h),"item-title":"name","item-value":"name"},null,8,["modelValue","items"])):P("",!0),i.value===3?(A(),U(x,{key:1,modelValue:k.value,"onUpdate:modelValue":a[3]||(a[3]=l=>k.value=l),items:R(D),"item-title":"name","item-value":"name"},null,8,["modelValue","items"])):P("",!0)]),_:1}),e(o,{cols:"2"})]),_:1}),e(ne,{title:s.t("617")},{default:t(()=>[e(ye,{class:"",inline:!1,modelValue:w.value,"onUpdate:modelValue":a[4]||(a[4]=l=>w.value=l)},{default:t(()=>[e(q,{value:!0},{label:t(()=>[e(V,{"key-name":"I"},{default:t(()=>[n(y(s.t("623")),1)]),_:1})]),_:1}),e(q,{value:!1,class:"mt-1"},{label:t(()=>[e(V,{"key-name":"E"},{default:t(()=>[n(y(s.t("624")),1)]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1},8,["title"]),e(Ve,{class:"mt-2",modelValue:L.value,"onUpdate:modelValue":a[5]||(a[5]=l=>L.value=l)},{label:t(()=>[e(V,{"key-name":"A"},{default:t(()=>[n(y(s.t("625")),1)]),_:1})]),_:1},8,["modelValue"])])]),_:1},8,["title","modelValue"]))}}),De=me(ke,[["__scopeId","data-v-4863abd3"]]);export{De as default};
