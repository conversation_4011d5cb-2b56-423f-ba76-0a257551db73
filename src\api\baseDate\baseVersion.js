import request from '@/utils/request'

// 查询参数列表
export function listVersion(query) {
  return request({
    url: '/base/version/list',
    method: 'get',
    params: query
  })
}

// 新增版本数据
export function addVersion(query) {
  return request({
    url: '/base/version',
    method: 'post',
    data: query
  })
}

// 修改版本数据
export function updateVersion(query) {
  return request({
    url: '/base/version/update',
    method: 'post',
    data: query
  })
}

// 删除版本数据
export function delVersion(query) {
  return request({
    url: '/base/version/' + query,
    method: 'post'
  })
}