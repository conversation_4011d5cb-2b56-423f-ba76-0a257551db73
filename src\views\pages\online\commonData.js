export const voltageLevelData = [
    {id: "0", text: "20kV", value: "24"},
    {id: "1", text: "10kV", value: "22"},
    {id: "2", text: "380V", value: "08"},
    {id: "3", text: "220V", value: "08"}]

/** 根据中文的杆塔类型返回杆塔legendTypeKey
 * @param {*} towerType 
 * @returns 
 */
export const getLegendTypeKeyByTowerType = (towerType) => {
    let legendTypeKey = "";
    switch (towerType) {
        case "水泥杆":
        legendTypeKey = "TY_SNG";
        break;
        case "钢管杆":
        legendTypeKey = "TY_GGG";
        break;
        case "窄基塔":
        legendTypeKey = "TY_TT";
        break;
        case "宽基塔":
        legendTypeKey = "TY_TT";
        break;
        case "水泥双杆":
        legendTypeKey = "TY_SNSG";
        break;
    }
    return legendTypeKey;
};
/** 根据中文的杆塔类型返回SDK图元ID
 * @param {*} towerType 
 * @returns 
 */
export const getSymbolIdByTowerType = (towerType) => {
  let SymbolId = "";
  switch (towerType) {
      case "水泥杆":
        SymbolId = "252011";
      break;
      case "钢管杆":
        SymbolId = "252008";
      break;
      case "窄基塔":
        SymbolId = "252016";
      break;
      case "宽基塔":
        SymbolId = "252016";
      break;
      case "水泥双杆":
        SymbolId = "252018";
      break;
  }
  return SymbolId;
};


/** 根据回路数得到基础参照里杆塔排列方式的key
 * @param {*} loop 
 * return {Array} 直线，转角，耐张，终端
 */
export const getTowerArrangementKeyByLoop = (loop) => {
    if(loop === "双回"){
        return ["DoubleLine", "DoubleCorner", "DoubleNaiZhang", "DoubleTerminal"];
    } else if(loop === "三回"){
        return ["TribleLine", "TribleCorner", "TribleNaiZhang", "TribleTerminal"];
    } else if(loop === "四回"){
        return ["QuardraLine", "QuardraCorner", "QuardraNaiZhang", "QuardraTerminal"];
    } else{
      return ["SingleLine", "SingleCorner", "SingleNaiZhang", "SingleTerminal"];
    }
}

/** 根据电压等级和图元类型得未选型的模块类型
 * @param {*} voltage 
 * @param {*} legendTypeKey 
 * @returns 
 */
export const getNoModuleTypeKey = (voltage, legendTypeKey) =>{
  switch(legendTypeKey){
    case 'TY_DHJKX': return "10DDX";
    case 'TY_DY_380DHJKX': case 'TY_DY_220DHJKX': return "DY_DDX";
    case 'TY_SNG':
      if(voltage === '10kV' || voltage === '20kV'){
        return '10SNG_DZZHJ'
      }
      return 'DY_SNGZ'
    case 'TY_SNSG':
      return '10SNSG'
    case 'TY_GGG':
      return '10GGG'
    case 'TY_TT': 
      return '10KJTGW'
    case 'TY_DLD':
      if(voltage === '10kV'){
        return '10DLDL'
      } else if(voltage === '20kV'){
        return '20DLDL'
      }
      return 'DY_DLDL'
    // case 'TY_XSBDZ': 
    //   return 'XA'
    case 'TY_DLZJT':
      return 'DLZJT'
      case 'TY_HWX':
        if(voltage === '10kV'){
          return 'HA'
        } else if(voltage === '20kV'){
          return '20HA'
        }
        case 'TY_DLFZX':
        if(voltage === '10kV'){
          return '10DLDJX'
        } else if(voltage === '20kV'){
          return '20DLDJX'
        }
        case 'TY_XSBDZ':
          if(voltage === '10kV'){
            return 'XA'
          } else if(voltage === '20kV'){
            return '20XA'
          }
          case 'TY_PDZ':
          if(voltage === '10kV'){
            return 'PB'
          } else if(voltage === '20kV'){
            return '20PB'
          }
          case 'TY_KGZ':
            return 'KB'
  }
  return null
}
//根据sdk返回的CLASSNAME找出对应关系PWPolePSR-TY_SNG
export const getlegendTypeKey = (className, voltage = '') =>{
  switch(className){
    case 'PWPolePSR': return "TY_SNG";
    case 'Well' : return "TY_ZTJ"
    case 'PWConductorSecPSR': return (voltage === '08' || voltage === '07') ? 'TY_DY_380DHJKX' : 'TY_DHJKX'
    case 'PWCableSecPSR': return (voltage === '08' || voltage === '07') ? 'TY_380DLD' : 'TY_DLD'
    case 'PWPreComSubstationPSR': return 'TY_XSBDZ'
    case 'PWBranchStationPSR': return (voltage === '08' || voltage === '07') ? 'TY_DYDLFZX' : 'TY_DLFZX'
    case 'PWSwitchingStationPSR': return 'TY_KGZ'
    case 'PWStationPSR': return 'TY_PDZ'
    case 'PWPowerCablePSR': return 'TY_HWX'
    case 'PWReactiveCompensatBoxPBPSR': return 'TY_WGBCQ'
    case 'PWCableJointPSR': return 'TY_DLZJT'
  }
}
//根据sdk的图元id返回B端图元类型
export const getLegendTypeKeyBySymbolId = (symbolId) =>{
  let legendTypeKey = ''
  switch (symbolId) {
    case "500301":
      legendTypeKey = "TY_PG";
    break;
    case "500311":
      legendTypeKey = "TY_PG";
    break;
    case "500303":
      legendTypeKey = "TY_DLG";
    break;
    case "500302":
      legendTypeKey = "TY_ZM";
      break;
  }
  return legendTypeKey
}
//模块类型得到通道中文类型
export const getFSLXByModuleType = (moduleType) => {
  switch (moduleType) {
      case "PG":
        return "排管";
      case "DLG":
        return "电缆沟";
      case "FSLB":
        return "非开挖拉管";
  }
  return "排管";
};
//得到图元状态中文类型
export const getLegendState = (state) => {
  switch (state) {
    case "New":
      return "新建";
    case "Original":
      return "原有";
    case "Remove":
      return "拆除";
    case "Reform":
      return "拆除";
  }
  return "新建";
}