import {MxCpp, McDbCircle , McCmColor, McGePoint3d} from "mxcad"

//画圆
function drawCircle() {
  const mxcad = MxCpp.getCurrentMxCAD();
  // 创建新画布
  mxcad.newFile();

  //绘制圆
  const circle_1 = new McDbCircle(-100, 300, 0, 20)
  circle_1.trueColor = new McCmColor(255, 0, 0)
  mxcad.drawEntity(circle_1)

  const circle_2 = new McDbCircle()
  circle_2.center = new McGePoint3d(-100, 300, 0)
  circle_2.radius = 10
  circle_2.trueColor = new McCmColor(0, 255, 0)
  mxcad.drawEntity(circle_2)

  mxcad.drawCircle(-100, 300, 30)

  mxcad.zoomAll()
  mxcad.zoomScale(0.8)
};

// 调用画圆方法
drawCircle();
