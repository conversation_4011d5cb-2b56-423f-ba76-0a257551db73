import { MxFun, DetailedResult } from "mxdraw";
import {
  McDb<PERSON>lockReference,
  McGePoint3d,
  MxCADPluginBase,
  MxCADUI,
  MxCADUiPrPoint,
  MxCpp,
  McDbDatabase,
  MxCADUiPrEntity,
  McDbAttributeDefinition,
  MxCADSelectionSet,
  MxCADResbuf,
  McDbAttribute,
  McCmColor,
  McDbPolyline,
  MxCADUiPrDist,
} from "mxcad";
import { BlockFun } from "./components/drawbock";
import { addLayer } from "./components/layer";
import { TzFile } from "./components/TzFile";
import { TzTest } from "./components/TzTest";
import {
  blockExcelFun,
  blockInsertFrameFun,
  pictureFrame,
  steelExcelFun,
  cableExcelFun,
  demolitionMaterials,
  cabcleChannel,
  steelExcelFun1,
  cabcleCircle,
  ljqcExcelFun,
  gxylExcelFun,
  jcylExcelFun,
  gtmxExcelFun,
} from "./components/drawPictureFrame";
import{dqhztFun} from "./components/drawDqhzt";
import code from "../../templateCode";
import { BlockDrawListFun } from "./components/drawList";
import { filePdf } from "./components/filePdf";
import { useInsertText } from "./utils/useInsertText.js";
import {
  Mx_FormatPainterGss,
  Mx_FormatPainterOne,
  Mx_FormatPainterBoss,
} from "./components/gssFun";
import {init as JGJLJY} from "../../JGJLJY"

import {generateExcelReport} from "../../bhne/generateReport"

const { initInsertText, saveFiles } = useInsertText();
window.addEventListener("message", (event) => {
  if (event.data.type === "MsgCad") {
    console.log(event.data);
    if (event.data.content === "Mx_hzqtty") {
      Mx_hzqtty(event.data.formData);
    } else if (event.data.content === "Mx_bztl") {
      Mx_bztl(event.data.formData);
    } else if (event.data.content === "Mx_drawingList") {
      Mx_drawingList(event.data.formData);
    } else if (event.data.content === "Mx_tzdw") {
      Mx_tzdw(event.data.formData);
    } else if (event.data.content === "Mx_pdfDy") {
      Mx_Pdf(event.data.formData);
    } else if (event.data.content === "Mx_FormatPainterGss") {
      Mx_FormatPainterGss(event.data.formData);
    } else if (event.data.content === "Mx_FormatPainterBoss") {
      Mx_FormatPainterBoss(event.data.formData);
    } else if (event.data.content === "Mx_bztlStyle") {
      Mx_bztlStyle(event.data.formData);
    } else if (event.data.content === "GXYL") {
      generateExcelReport(event.data.formData, gxylExcelFun, 6);
    } else if (event.data.content === "JCYL") {
      generateExcelReport(event.data.formData, jcylExcelFun, 4);
    } else if (event.data.content === "DLTDTJ") {
      Mx_dltdtj(event.data.formData)
    } else if (event.data.content === "GTMX") {
      generateExcelReport(event.data.formData, gtmxExcelFun, 30, 650);
    }else if (event.data.content === "JGJLJY") {
      JGJLJY()
    }
  } else if (event.data.type === "cadPreview") {
    console.log(event.data,'dddddddddddd')
    if (event.data.content === "Mx_cadPreview") {
      Mx_cadPreview(event.data.formData.openUrl);
    } else if (event.data.content === "Mx_InsertFrame") {
      Mx_InsertFrame(event.data.formData);
    } else if (event.data.content === "Mx_InsertCr") {
      Mx_InsertCr(event.data.formData);
    } else if (event.data.content === "Mx_TzfileTk") {
      Mx_TzfileTk(event.data.formData);
    } else if (event.data.content === "Mx_zdTzfileTk") {
      Mx_zdTzfileTk(event.data.formData);
    } else if (event.data.content === "Mx_InsertText") {
      initInsertText(event.data.formData);
    } else if (event.data.content === "Mx_SaveFiles") {
      saveFiles(event);
    } else if (event.data.content === "Mx_cadtext") {
      Mx_cadtext(event.data.formData);
    } else if (event.data.content === "Mx_cjwz") {
      Mx_cjwz(event.data.formData);
    } else if (event.data.content === "Mx_zyclqd") {
      generateExcelReport(event.data.formData, blockExcelFun, 70, 225);
    } else if (event.data.content === "Mx_gggcl") {
      generateExcelReport(event.data.formData, steelExcelFun, 70, 225);
    } else if (event.data.content === "Mx_dlmx") {
      generateExcelReport(event.data.formData, cableExcelFun, 70, 225);
    } else if (event.data.content === "Mx_splicing") {
      Mx_splicing(event.data.formData)
    }else if (event.data.content === 'Mx_ljqc') {
      Mx_ljqc(event.data.formData)
    }else if (event.data.content === 'Mx_dqhzt') {
      Mx_dqhzt(event.data.formData)
    }else if (event.data.content === 'Mx_tdnfwzt') {
      Mx_tdnfwzt(event.data.formData)
    }else if(event.data.content === 'Mx_crbjk'){
      Mx_crbjk(event.data.formData)
    }
  }
});
// 初始加载
export function init() {
    // // 注册命令
    MxFun.addCommand("Mx_Pdf", Mx_Pdf);
    MxFun.addCommand("Mx_FormatPainterOne", Mx_FormatPainterOne);
    // 调用CAD方法
    // MxFun.sendStringToExecute(event.data.content)
}
function Mx_bztlStyle(value) {
  console.log(value,'11111')
  const ss = new MxCADSelectionSet();
  let filter = new MxCADResbuf();
  filter.AddMcDbEntityTypes("INSERT");
  //选择所有图形元素
  ss.allSelect(filter);
  if (ss.count() == 0) return;
  ss.forEach((idF) => {
    let ent = idF.getMcDbEntity() as McDbAttribute;
    let blkRef: McDbBlockReference = ent;
    let aryId = blkRef.getAllAttribute();
    const GTowerHeght = ent.getxDataString('GTowerHeght')
    console.log(GTowerHeght,'GTowerHeght')
    aryId.forEach((id,index) => {
      let attribt: McDbAttribute = id.getMcDbEntity() as McDbAttribute;
      let tag = attribt.tag;
        console.log(tag,'tagtagtagtag')
      
      const arrNew = JSON.parse(value.arr);
      if (arrNew.length > 0) {
        arrNew.forEach((itemD) => {
          // attribt.isInvisible=fa
          // console.log(itemD,'itemDitemDitemD',tag)
          if (itemD.TagName === (tag=='设备编号'?'编号':tag)) {
            attribt.isInvisible = itemD.IsShow === "1" ? false : true;
            let [r,g,b]=itemD.FontColor.slice(4,-1).split(',').map(Number)
            attribt.trueColor=new McCmColor(r,g,b)
            attribt.textString='123131'
            // console.log( attribt.isInvisible,'1111111')
          } else if(itemD.TagName==(tag=='模块编号'?'杆高':tag)) {
            // console.log('杆高')
            attribt.isInvisible = itemD.IsShow === "1" ? false : true;
            let [r,g,b]=itemD.FontColor.slice(4,-1).split(',').map(Number)
            attribt.trueColor=new McCmColor(r,g,b)
            attribt.textString=GTowerHeght
          }else if(itemD.TagName==(tag=='电压等级'?'转角':tag)){
            attribt.isInvisible = itemD.IsShow === "1" ? false : true;
            let [r,g,b]=itemD.FontColor.slice(4,-1).split(',').map(Number)
            attribt.trueColor=new McCmColor(r,g,b)
            attribt.textString='转角'
          }
        });
      } else {
        attribt.isInvisible = false;
      }
      // console.log( attribt.isInvisible,'enddddddd')
    });
  
    ent.assertObjectModification();
  });
  MxCpp.getCurrentMxCAD().updateDisplay();
  MxCpp.getCurrentMxCAD().regen();
}
async function Mx_zdTzfileTk(value) {
  const array = [];
  const list = value.arrList;
  for (const item of list) {
    const obj = await TzFile(item);
    array.push({
      ...obj,
      drawingid: item.drawingid,
      assessmenttype: item.tzmc,
    });
  }
  MxCpp.App.getCurrentMxCAD().newFile();
  console.log(array, "当前的 array"); // 打印当前数组内容
  window.parent.postMessage(
    {
      type: "parentCad",
      params: {
        content: "自动匹配图纸带有图框图签",
        formData: {
          files: array, // 发送所有生成的文件
        },
      },
    },
    "*"
  );
}
async function Mx_TzfileTk(value) {
  const array = [];
  const list = value.arrList;
  for (const item of list) {
    const obj = await TzFile(item);
    array.push({
      ...obj,
      drawingid: item.drawingid,
      assessmenttype: item.tzmc,
    });
  }
  MxCpp.App.getCurrentMxCAD().newFile();
  console.log(array, "当前的 array"); // 打印当前数组内容
  window.parent.postMessage(
    {
      type: "parentCad",
      params: {
        content: "插入图纸带有图框图签",
        formData: {
          files: array, // 发送所有生成的文件
        },
      },
    },
    "*"
  );
}

async function Mx_Pdf(value) {
  let fileArr = [];
  // 使用 map + async/await 来处理异步操作
  const filePromises = JSON.parse(value.arr).map(async (item) => {
    const res = await filePdf(item); // 等待 filePdf 完成
    fileArr.push(res); // 添加到 fileArr
  });
  // 等待所有异步操作完成
  await Promise.all(filePromises);
  console.log(fileArr, "fileArr");
  // 在所有数据处理完成后，发送文件到父窗口
  window.parent.postMessage(
    {
      type: "parentCad",
      params: {
        content: "批量打印Pdf",
        formData: {
          files: fileArr, // 发送所有生成的文件
        },
      },
    },
    "*"
  );
}
async function Mx_tzdw(value) {
  const dataBase: McDbDatabase = MxCpp.getCurrentMxCAD().getDatabase();
  const objectId = dataBase.handleToIdIndex(value.handler);
  const entVal = objectId.getMcDbEntity();
  // 得到实体的包围盒
  const { ret, minPt, maxPt } = entVal.getBoundingBox();
  if (ret) {
    // 将显示范围设置为该包围盒的大小
    MxCpp.getCurrentMxCAD().zoomW(minPt, maxPt);
  }
}
async function Mx_drawingList(value) {
  if (!value) return;
  const res = await BlockDrawListFun(
    { spanWidth: 100, spanClass: "图纸目录模板" },
    `/assets/TuK/图纸目录模板1.mxweb`,
    1,
    JSON.parse(value?.arrList),
    JSON.parse(value?.tableData)
  );
  console.log(res, "图纸目录Mx_drawingList");
  window.parent.postMessage(
    {
      type: "parentCad",
      params: {
        content: "图纸目录File",
        formData: {
          files: res.fileArr, // 发送所有生成的文件
        },
      },
    },
    "*"
  );
}
function Mx_InsertCr(value) {
  blockInsertFrameFun(value, "插入", "非文件").then((res) => {
    if (res.show) {
      // 在所有数据处理完成后，发送文件到父窗口
      window.parent.postMessage(
        {
          type: "parentCad",
          params: {
            content: "插入图框",
            handle: res.handle,
          },
        },
        "*"
      );
    }
  });
}
function Mx_InsertFrame(value) {
  blockInsertFrameFun(value, "预览", "非文件").then((res) => {});
}
async function Mx_cadPreview(value) {
  console.log(value,'预览接收数据')
    // 2025年4月2日 fanchen 设置改到加载cad初始加载功能
    // const config = await MxPluginContext.getUiConfig()
    // config.mLeftButtonBarData.isShow = false;
    // config.mRightButtonBarData.isShow = false;
    // config.isShowFooter = false;
    // config.isShowHeader = false;
    // config.isShowModelNav = false;
    const blobUrl = value ? URL.createObjectURL(value) : ''
    console.log("🚀 ~ Mx_cadPreview ~ blobUrl:", blobUrl)
    const waitForMxCAD = async (timeout = 5000) => {
        const start = Date.now();
        while (Date.now() - start < timeout) {
            const instance = MxCpp.getCurrentMxCAD();
            if (instance && typeof instance.openWebFile === "function") {
                return instance;
            }
            await new Promise(resolve => setTimeout(resolve, 500));
        }
        return null;
    };
    const instance = await waitForMxCAD();
    if (instance) {
        try {
            await instance.openWebFile(blobUrl); // 确保执行
        } catch (error) {
            console.error("打开 CAD 文件失败：", error);
        } finally {
            // const mxdivElements = document.querySelector('#mxdiv');
            // mxdivElements.style.height = 'auto'
        }
    } else {
        console.error("MxCAD 初始化超时，无法打开文件");
    }
    
}

// 测试
async function Mx_cadtext(value) {
  const array = [];
  const list = value.openUrl;
  for (const item of list) {
    const obj = await TzTest(item);
    array.push({ ...obj });
  }
  console.log("🚀 ~ Mx_cadtext ~ array:", value, array);

  MxCpp.App.getCurrentMxCAD().newFile();
  window.parent.postMessage(
    {
      type: "parentCad",
      params: {
        content: "应力弧垂表",
        formData: {
          files: array, // 发送所有生成的文件
        },
      },
    },
    "*"
  );
}

// 利旧清册
async function Mx_ljqc(value) {
    let flag = false; // 用于控制递归是否已经开始
    let allFiles = []; // 用于存储所有生成的文件
    flag = true; // 标记递归开始
    // 递归处理数据，分批次生成文件
    const processInChunks = async (startIndex = 0, chunkSize = 70, offsetX = 0, num = 1) => {
        const arr = value.tableData ? JSON.parse(value.tableData) : []
        const chunk = arr.slice(startIndex, startIndex + chunkSize); // 获取当前批次的数据
        // 如果当前批次有数据，调用 
        if (chunk.length > 0) {
            const newValue = { ...value, tableData: JSON.stringify(chunk), offsetX, num };
            const file = await ljqcExcelFun(newValue);
            // 将生成的文件存储到 allFiles 数组中
            allFiles.push(file);

            // 递归处理剩余数据
            if (startIndex + chunkSize < arr.length) {
                await processInChunks(startIndex + chunkSize, chunkSize, offsetX + 225, num + 1);
            }
        }
    };
    // 启动递归处理
    await processInChunks();
    
    // 在所有数据处理完成后，发送文件到父窗口
    window.parent.postMessage(
        {
            type: 'parentCad',
            params: {
                content: '利旧物资表',
                formData: {
                    files: allFiles, // 发送所有生成的文件
                    tableData: value.tableData
                }
            }
        },
        '*'
    );
    // 递归处理完成后，重置标志
    flag = false;
}

//电气一次图设计

async function Mx_splicing(value) {
  // 1. 初始化配置
  const CONFIG = {
    CHUNK_SIZE: 70,
    OFFSET_STEP: 225,
    MXCAD_TIMEOUT: 2000,
    POLL_INTERVAL: 500,
  };

  try {
    // 2. 数据预处理
    const arr = value.tableData ? JSON.parse(value.tableData) : [];
    if (!arr.length) {
      // throw new Error("无有效表格数据");
      return;
    }

    // 3. 分块处理（并行优化版）
    const processChunk = async (chunk, index) => {
      const newValue = {
        ...value,
        tableData: JSON.stringify(chunk),
        offsetX: index * CONFIG.OFFSET_STEP,
        num: index + 1,
      };
      return steelExcelFun1(newValue);
    };

    const chunkPromises = [];
    for (let i = 0; i < arr.length; i += CONFIG.CHUNK_SIZE) {
      const chunk = arr.slice(i, i + CONFIG.CHUNK_SIZE);
      chunkPromises.push(processChunk(chunk, i / CONFIG.CHUNK_SIZE));
    }

    const allFiles = await Promise.all(chunkPromises);
    const blobUrl = URL.createObjectURL(allFiles[0]);
    if (value.isFlags) {
      // 4. CAD文件处理
      const instance = await (async () => {
        const start = Date.now();
        while (Date.now() - start < CONFIG.MXCAD_TIMEOUT) {
          const inst = MxCpp.getCurrentMxCAD();
          if (inst?.openWebFile) return inst;
          await new Promise((r) => setTimeout(r, CONFIG.POLL_INTERVAL));
        }
        return null;
      })();

      if (!instance) {
        console.warn("MxCAD实例未就绪");
      } else {
        await instance.openWebFile(blobUrl);
      }
    }

    if (!value.isFlags) {
      // 5. 结果上报
      window.parent?.postMessage(
        {
          type: "parentCad",
          params: {
            content: "电气一次图设计",
            formData: {
              files: allFiles,
              volName: value.volName,
              // tableData: value.tableData,
              processedAt: new Date().toISOString(),
            },
          },
        },
        "*"
      );
    }

    // 6. 资源清理
    return () => {
      URL.revokeObjectURL(blobUrl);
      console.debug("资源已清理");
    };
  } catch (error) {
    console.error("处理过程中出错:", error);
    window.parent?.postMessage(
      {
        type: "cadError",
        error: error.message,
      },
      "*"
    );
    throw error;
  }
}
//电气平面布置图绘制
async function Mx_dqhzt(value) {
  let flag = false; // 用于控制递归是否已经开始
  let allFiles = []; // 用于存储所有生成的文件
  flag = true; // 标记递归开始
  // 递归处理数据，分批次生成文件
  const processInChunks = async (startIndex = 0, chunkSize = 70, offsetX = 0, num = 1) => {
      const arr = value.tableData ? JSON.parse(value.tableData) : []
      console.log("🚀 ~ processInChunks ~ arr:", arr)
      const chunk = arr.slice(startIndex, startIndex + chunkSize); // 获取当前批次的数据
      // 如果当前批次有数据，调用 
      if (chunk.length > 0) {
          const newValue = { ...value, tableData: JSON.stringify(chunk), offsetX, num };
          const file = await dqhztFun(newValue);
          // 将生成的文件存储到 allFiles 数组中
          allFiles.push(file);

          // 递归处理剩余数据
          if (startIndex + chunkSize < arr.length) {
              await processInChunks(startIndex + chunkSize, chunkSize, offsetX + 225, num + 1);
          }
      }
  };
  // 启动递归处理
  await processInChunks();
  
  // 在所有数据处理完成后，发送文件到父窗口
  window.parent.postMessage(
      {
          type: 'parentCad',
          params: {
              content: '电气平面布置图绘制',
              formData: {
                  files: allFiles, // 发送所有生成的文件
                  tableData: value.tableData
              }
          }
      },
      '*'
  );
  // 递归处理完成后，重置标志
  flag = false;
}

// 生成拆旧物资清册
async function Mx_cjwz(value) {
  let flag = false; // 用于控制递归是否已经开始
  console.log("🚀 ~ Mx_cjwz ~ value:", value);
  let allFiles = []; // 用于存储所有生成的文件
  flag = true; // 标记递归开始
  // 递归处理数据，分批次生成文件
  const processInChunks = async (
    startIndex = 0,
    chunkSize = 70,
    offsetX = 0,
    num = 1
  ) => {
    const arr = value.tableData ? JSON.parse(value.tableData) : [];
    if(arr.length===0){
       arr.push({
    materialname:'',
    materialdescription:'',
    DJLS:'',
    GD2:'',
    num:'',
    designunit:'',
    state:''
})
    }
    const chunk = arr.slice(startIndex, startIndex + chunkSize); // 获取当前批次的数据
    // 如果当前批次有数据，调用 
    if (chunk.length > 0) {
      const newValue = {
        ...value,
        tableData: JSON.stringify(chunk),
        offsetX,
        num,
      };
      const file = await demolitionMaterials(newValue);

      // 将生成的文件存储到 allFiles 数组中
      allFiles.push(file);

      // 递归处理剩余数据
      if (startIndex + chunkSize < arr.length) {
        await processInChunks(
          startIndex + chunkSize,
          chunkSize,
          offsetX + 225,
          num + 1
        );
      }
    }
  };
  // 启动递归处理
  await processInChunks();
  // 在所有数据处理完成后，发送文件到父窗口
  window.parent.postMessage(
    {
      type: "parentCad",
      params: {
        content: "拆旧物资清册",
        formData: {
          files: allFiles, // 发送所有生成的文件
          tableData: value.tableData,
        },
      },
    },
    "*"
  );
  // 递归处理完成后，重置标志
  flag = false;
}

// 电缆通道土建表
async function Mx_dltdtj(value) {
    let flag = false; // 用于控制递归是否已经开始
    console.log("🚀 ~ Mx_dltdtj ~ value:", value)
    let allFiles = []; // 用于存储所有生成的文件
    flag = true; // 标记递归开始
    // 递归处理数据，分批次生成文件
    const processInChunks = async (startIndex = 0, chunkSize = 70, offsetX = 0, num = 1) => {
        const arr = value.tableData ? JSON.parse(value.tableData) : []
        const chunk = arr.slice(startIndex, startIndex + chunkSize); // 获取当前批次的数据
        // 如果当前批次有数据，调用 
        if (chunk.length > 0) {
            const newValue = { ...value, tableData: JSON.stringify(chunk), offsetX, num };
            const file = await cabcleChannel(newValue);
            
            // 将生成的文件存储到 allFiles 数组中
            allFiles.push(file);

            // 递归处理剩余数据
            if (startIndex + chunkSize < arr.length) {
                await processInChunks(startIndex + chunkSize, chunkSize, offsetX + 225, num + 1);
            }
        }
    };
    // 启动递归处理
    await processInChunks();
    // 在所有数据处理完成后，发送文件到父窗口
    window.parent.postMessage(
        {
            type: 'parentCad',
            params: {
                content: 'DLTDTJ',
                formData: {
                    files: allFiles, // 发送所有生成的文件
                    tableData: value.tableData,
                    proId: value.proId,
                    stage: value.stage
                }
            }
        },
        '*'
    );
    // 递归处理完成后，重置标志
    flag = false;
}
async function Mx_crbjk(value) {
  console.log(value,'ddddd')
   const mxcad = MxCpp.App.getCurrentMxCAD();
  
  let width = 390;
  // 设置矩形高度
  let height = 270;
  // 设置矩形的中心点
  const getCenterPt = new MxCADUiPrPoint();
  
  getCenterPt.setMessage("请点击确定矩形中心");
  const centerPt = await getCenterPt.go();
  console.log(centerPt,'centerPt')
  if (!centerPt) return;

  // 根据矩形的中心点和宽高计算矩形的四个顶点
  let pt1 = new McGePoint3d(centerPt.x + width / 2, centerPt.y + height / 2, centerPt.z)
  let pt2 = new McGePoint3d(centerPt.x - width / 2, centerPt.y + height / 2, centerPt.z)
  let pt3 = new McGePoint3d(centerPt.x - width / 2, centerPt.y - height / 2, centerPt.z)
  let pt4 = new McGePoint3d(centerPt.x + width / 2, centerPt.y - height / 2, centerPt.z)
  let pl = new McDbPolyline;// 构造一个多段线对象
  // 依次添加矩形顶点
  pl.addVertexAt(pt1)
  pl.addVertexAt(pt2)
  pl.addVertexAt(pt3)
  pl.addVertexAt(pt4)

  pl.isClosed = true; // 设置多段线闭合
  mxcad.drawEntity(pl); // 绘制多段线对象
} 
// 通道内敷设位置图
async function Mx_tdnfwzt(value) {
  let flag = false; // 用于控制递归是否已经开始
  let allFiles = []; // 用于存储所有生成的文件
  flag = true; // 标记递归开始
  // 递归处理数据，分批次生成文件
  const processInChunks = async (startIndex = 0, chunkSize = 70, offsetX = 0, num = 1) => {
      const arr = value.tableData ? JSON.parse(value.tableData) : []
      const chunk = arr.slice(startIndex, startIndex + chunkSize); // 获取当前批次的数据
      // 如果当前批次有数据，调用 blockExcelFun
      if (chunk.length > 0) {
          const newValue = { ...value, tableData: JSON.stringify(chunk), offsetX, num };
          const file = await cabcleCircle(newValue);
          
          // 将生成的文件存储到 allFiles 数组中
          allFiles.push(file);

          // 递归处理剩余数据
          if (startIndex + chunkSize < arr.length) {
              await processInChunks(startIndex + chunkSize, chunkSize, offsetX + 225, num + 1);
          }
      }
  };
  // 启动递归处理
  await processInChunks();
  // 在所有数据处理完成后，发送文件到父窗口
  window.parent.postMessage(
      {
          type: 'parentCad',
          params: {
              content: '通道内敷设位置图',
              formData: {
                  files: allFiles, // 发送所有生成的文件
                  tableData: value.tableData,
                  proId: value.proId,
                  stage: value.stage
              }
          }
      },
      '*'
  );
  // 递归处理完成后，重置标志
  flag = false;
}
// 标注管理-标注图例
function Mx_bztl(value) {
  const array = value.map((item) => {
    return {
      ...item,
      spanClass: item.legendname,
      spanWidth: 30,
      fileName: `/assets/ModuleLegens/${item.legendname}.mxweb`,
      text: item.legendname,
    };
  });
  pictureFrame(array);
}
// 工具管理-绘制其他图元
function Mx_hzqtty(item) {
  const text = item.stateText === "改造" ? "换装" : item.stateText;
  const str = item.tylbText + text;
  addLayer(item.tylb);
  BlockFun(
    { spanWidth: 100, spanClass: str, IsShowArr: item.IsShowArr },
    `/assets/ModuleLegens/${str}.mxweb`,
    1,
    JSON.parse(item.attribDefList)
  ).then((res) => {
    console.log(res, "resresres");
    window.parent.postMessage(
      {
        type: "parentCad",
        params: {
          content: "绘制其他图元",
          formData: {
            legendGuidKey: res.id.id,
            handle: res.handle,
            pointsXYZ: res.pointsXYZ.x + "," + res.pointsXYZ.y + ",0",
          },
        },
      },
      "*"
    );
  });
}
