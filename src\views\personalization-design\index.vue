<script setup>
import SteelPipePole from './components/steel-pipe-pole'
import {getList, getModuleByTowerHeight, getSubPoleModelList} from "@/api/personalized-setting/index.js";
import {ElMessageBox} from "element-plus";
const activeName = ref('1')

const hlEditRender = reactive({
  name: 'VxeSelect',
  options: [
      {
        label: '单回',
        value: '单回'
      },
      {
        label: '双回',
        value: '双回'
      }
  ]
})

const gtLxEditRender = reactive({
  name: 'VxeSelect',
  options: [
      {
        label: '直线',
        value: '直线'
      },
      {
        label: '耐张',
        value: '耐张'
      },
      {
        label: '转角',
        value: '转角'
      },
      {
        label: '终端',
        value: '终端'
      }
  ]
})

const materialLabel = computed(() => {
  return activeName.value === '1' ? '钢管杆' : '窄基塔'
})

const getModuleList = (cellParams, targetParams) => {
  const { row, column } = cellParams

  row.moduleId = ''
  row.moduleName = ''

  const {rodHeight, shaojing} = row
  if(!rodHeight || !shaojing) return
  const material = materialLabel.value
  const params = {
    material,
    towerHeght: rodHeight,
    shaoJing: shaojing
  }
  getSubPoleModelList(params).then((res) => {
    if(res.code === 200 && res.data.length) {
      row.moduleId = res.data[0].moduleid
      row.moduleName = res.data[0].modulename
      row.moduleOptions = res.data
    }
  })
}
const ggEditRender = reactive({
  name: 'VxeSelect',
  options: [
      {
        label: '10m',
        value: '10'
      },
      {
        label: '13m',
        value: '13'
      },
      {
        label: '16m',
        value: '16'
      }
  ],
  events: {
    change (cellParams, targetParams) {
      getModuleList(cellParams, targetParams)
    }
  }
})

const sjEditRender = reactive({
  name: 'VxeSelect',
  options: [
      {
        label: '270mm',
        value: '270'
      },
      {
        label: '310mm',
        value: '310'
      },
      {
        label: '350mm',
        value: '350'
      },
      {
        label: '390mm',
        value: '390'
      }
  ],
  events: {
    change (cellParams, targetParams) {
      getModuleList(cellParams, targetParams)
    }
  }
})


const zjGtLxEditRender = reactive({
  name: 'VxeSelect',
  options: [
    {
      label: '直线',
      value: '直线'
    },
    {
      label: '转角',
      value: '转角'
    },
    {
      label: '终端',
      value: '终端'
    },
  ]
})

const zjGgEditRender = reactive({
  name: 'VxeSelect',
  options: [
    {
      label: '13m',
      value: '13'
    },
    {
      label: '15m',
      value: '15'
    },
    {
      label: '18m',
      value: '18'
    },
  ],
  events: {
    change (cellParams, targetParams) {
      getZjModuleList(cellParams, targetParams)
    }
  }
})
const getZjModuleList = (cellParams, targetParams) => {
  const { row, column } = cellParams
  const {rodHeight} = row

  row.moduleId = ''
  row.moduleName = ''

  if(!rodHeight) return
  const params = {
    towerHeght: rodHeight,
  }
  getModuleByTowerHeight(params).then((res) => {
    if(res.code === 200 && res.data.length) {
      row.moduleId = res.data[0].moduleid
      row.moduleName = res.data[0].modulename
      row.moduleOptions = res.data
    }
  })
}


const gtColumns = [
  { type: 'checkbox', width: 70 },
  { type: 'seq', width: 70 },
  { field: 'loopNumber', title: '回路数', editRender: hlEditRender },
  { field: 'towerLx', title: '杆塔类型', editRender: gtLxEditRender },
  { field: 'minAngle', title: '最小角度', editRender: { name: 'VxeInput' } },
  { field: 'maxAngle', title: '最大角度', editRender: { name: 'VxeInput' } },
  { field: 'rodHeight', title: '杆高', editRender: ggEditRender },
  { field: 'shaojing', title: '稍径', editRender: sjEditRender },
  { field: 'moduleId', title: '模块型号', editRender: {}, slots: { edit: 'edit_moduleId', default: 'default_moduleId' } },
]
const zjColumns = [
      { type: 'checkbox', width: 70 },
      { type: 'seq', width: 70 },
      { field: 'loopNumber', title: '回路数', editRender: hlEditRender },
      { field: 'towerLx', title: '杆塔类型', editRender: zjGtLxEditRender },
      { field: 'minAngle', title: '最小角度', editRender: { name: 'VxeInput' } },
      { field: 'maxAngle', title: '最大角度', editRender: { name: 'VxeInput' } },
      { field: 'rodHeight', title: '塔高', editRender: zjGgEditRender },
      { field: 'moduleId', title: '模块型号', editRender: {}, slots: { edit: 'edit_moduleId', default: 'default_moduleId' } },
    ]

const handleClick = (tab, event) => {
  console.log(tab, event)
  getTableList()
}

const tabBeforChange = async (tab, event) => {
  console.log('tabBeforChange',tab, event)
      /*await ElMessageBox.confirm(
        `已选择${materialLabel.value}，切换杆塔类型会导致当前杆塔类型的个性化规则清空，是否确定？`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(() => {
        console.log('hahah', activeName.value)
        return true
      }).catch(() => {
        return reject()
      })*/
}

const tableData = ref([])
const getTableList = () => {
  getList()
      .then((res) => {
        const isActive1 = activeName.value === '1'
        tableData.value = res.data.filter(item =>
            isActive1 ? item.shaojing : !item.shaojing
        ).map(item => {
          return{
            ...item,
            moduleOptions: []
          }
        }).map(item => {
          initModuleList(item)
          return item
        })
      })
      .catch((error) => {
        console.error('获取数据失败:', error)
      })
}

const initModuleList = (row) => {
    const {moduleId, rodHeight, shaojing} = row
    if(!moduleId) return
    let api = null
    if(rodHeight && shaojing) {
        api = getSubPoleModelList({
    material: materialLabel.value,
    towerHeght: rodHeight,
    shaoJing: shaojing
  })
    } else if(rodHeight && !shaojing) {
        api = getModuleByTowerHeight({towerHeght: rodHeight})
    }
    if(api) {
        api.then((res) => {
            if(res.code === 200 && res.data.length) {
                row.moduleOptions = res.data
            }
        })
    }
}
onMounted(() => {
  getTableList()
})
</script>

<template>
<div class="app-container" style="background: white;">
  <el-tabs v-model="activeName" :before-leave="tabBeforChange" class="demo-tabs" @tab-click="handleClick">
    <el-tab-pane label="钢管杆" name="1">
      <steel-pipe-pole v-if="activeName === '1'" :activeName="activeName" :columns="gtColumns" :data="tableData" @getList="getTableList" />
    </el-tab-pane>
    <el-tab-pane label="窄基塔" name="2">
      <steel-pipe-pole v-if="activeName === '2'" :activeName="activeName" :columns="zjColumns" :data="tableData" @getList="getTableList" />
    </el-tab-pane>
  </el-tabs>
</div>
</template>

<style lang="scss" scoped>

</style>