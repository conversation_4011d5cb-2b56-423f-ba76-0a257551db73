<template>
  <div class="app-container">
    <div class="form-left">
      <div style="color: dodgerblue; font-weight: 600; margin-bottom: 11px">
        图纸类别
      </div>
      <table-tree
        :treeData="treeData"
        :defaultProps="{ label: 'moduleTypeName', children: 'children' }"
        @nodeClick="handleNodeClick"
      ></table-tree>
    </div>
    <div class="form-right">
      <div style="width: 100%; height: 100%">
        <el-form
          v-model="queryParams"
          ref="queryRef"
          :inline="true"
          label-width="100px"
        >
          <el-form-item label="资源库选择">
            <el-select
              v-model="queryParams.baseVersionId"
              @change="versionChange"
              placeholder="请选择"
              style="width: 180px"
            >
              <el-option
                v-for="item in versionList"
                :key="item.objId"
                :label="item.versionName"
                :value="item.objId"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="仅此类别">
            <el-input
              v-model="queryParams.model"
              placeholder="可输入物料名称、ERP编码或技术规范ID，多个条件用空格隔开"
              clearable
              style="width: 240px"
            />
          </el-form-item>
          <el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery"
                >搜索</el-button
              >
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form-item>
        </el-form>
        <div class="sub-photo" v-if="btnFlag == 2">
          <div class="photo-left">
            <div>
              <el-button
                type="text"
                plain
                icon="Plus"
                :disabled="single"
                @click="handleUpdate"
                >新增</el-button
              >
              <el-button
                type="text"
                plain
                icon="Delete"
                :disabled="single"
                @click="handleUpdate"
                >删除</el-button
              >
            </div>
           
          </div>
         
        </div>
        <div class="sub-photo">
          <div class="photo-left">
            <div class="btn">
              <el-button
                type="text"
                plain
                icon="Plus"
                :disabled="single"
                @click="handleUpdate"
                >新增</el-button
              >
              <el-button
                type="text"
                plain
                icon="Delete"
                :disabled="single"
                @click="handleUpdate"
                >删除</el-button
              >
            </div>
            <el-table
              v-loading="loading"
              :data="userList"
              @selection-change="handleSelectionChange"
            >
              <el-table-column
                align="center"
                label="序号"
                width="55"
              ></el-table-column>
              <el-table-column
                align="center"
                type="selection"
                width="55"
              ></el-table-column>
              <el-table-column
                label="图纸名称"
                align="center"
                key="nickName"
                prop="nickName"
              />
              <el-table-column
                label="图号"
                align="center"
                key="nickName"
                prop="nickName"
              />
            </el-table>
          </div>
          <div class="photo-right">
            <div style="display:flex;align-items:center;height:30px;border-bottom:2px solid #ccc;">
              <div style="background:#2573ef;width:5px;height:18px;margin-right:10px;"></div>
              <div style="color:#2573ef;font-weight:400;font-size:18px;">预览</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import {
  addBaseModules,
  getBaseModules,
  delBaseModules,
  exportBaseModules,
  baseModulesList,
  updateBaseModules,
  baseModulesTreeList,
} from "@/api/module/index";
import { ref } from "vue";
import { onMounted } from "vue";
import { listVersion } from "@/api/baseDate/baseVersion";
import useUserStore from "@/store/modules/user";

const userStore = useUserStore();
const treeData = ref([]);
const moduleList = ref([]);
const loadList = ref([
  {
    moduleTypeKey: "2sds",
    moduleName: "2323",
    moduleCode: "2323",
    moduleVoltage: "2323",
  },
  {
    moduleTypeKey: "2sds",
    moduleName: "2323",
    moduleCode: "2323",
    moduleVoltage: "2323",
  },
  {
    moduleTypeKey: "2sds",
    moduleName: "2323",
    moduleCode: "2323",
    moduleVoltage: "2323",
  },
  {
    moduleTypeKey: "2sds",
    moduleName: "2323",
    moduleCode: "2323",
    moduleVoltage: "2323",
  },
  {
    moduleTypeKey: "2sds",
    moduleName: "2323",
    moduleCode: "2323",
    moduleVoltage: "2323",
  },
  {
    moduleTypeKey: "2sds",
    moduleName: "2323",
    moduleCode: "2323",
    moduleVoltage: "2323",
  },
  {
    moduleTypeKey: "2sds",
    moduleName: "2323",
    moduleCode: "2323",
    moduleVoltage: "2323",
  },
  {
    moduleTypeKey: "2sds",
    moduleName: "2323",
    moduleCode: "2323",
    moduleVoltage: "2323",
  },
  {
    moduleTypeKey: "2sds",
    moduleName: "2323",
    moduleCode: "2323",
    moduleVoltage: "2323",
  },
  {
    moduleTypeKey: "2sds",
    moduleName: "2323",
    moduleCode: "2323",
    moduleVoltage: "2323",
  },
  {
    moduleTypeKey: "2sds",
    moduleName: "2323",
    moduleCode: "2323",
    moduleVoltage: "2323",
  },
  {
    moduleTypeKey: "2sds",
    moduleName: "2323",
    moduleCode: "2323",
    moduleVoltage: "2323",
  },
  {
    moduleTypeKey: "2sds",
    moduleName: "2323",
    moduleCode: "2323",
    moduleVoltage: "2323",
  },
  {
    moduleTypeKey: "2sds",
    moduleName: "2323",
    moduleCode: "2323",
    moduleVoltage: "2323",
  },
  {
    moduleTypeKey: "2sds",
    moduleName: "2323",
    moduleCode: "2323",
    moduleVoltage: "2323",
  },
  {
    moduleTypeKey: "2sds",
    moduleName: "2323",
    moduleCode: "2323",
    moduleVoltage: "2323",
  },
  {
    moduleTypeKey: "2sds",
    moduleName: "2323",
    moduleCode: "2323",
    moduleVoltage: "2323",
  },
]);
const queryParams = ref({
  resources: "",
  model: "",
  baseVersionId: "",
});
const btnFlag = ref(1);
const versionList = ref([]);
const queryList = (item) => {
  let obj = {
    baseVersionId: item,
  };
  queryParams.value = { ...queryParams.value, ...obj };
  loading.value = true;
  baseModulesList(queryParams.value).then((res) => {
    moduleList.value = res.rows;
    loading.value = false;
  });
};

/**版本值改变事件 */
function versionChange(row) {
  treeQueryList(row);
}

//模块管理列表
const handleRowClick = (row) => {};

const treeQueryList = (versionId) => {
  const queryParams = {
    baseVersionId: versionId,
  };
  baseModulesTreeList(queryParams).then((res) => {
    treeData.value = res.data;
  });
};

/**获取版本下拉数据 */
function getlistVersion() {
  userStore.getInfo().then((res) => {
    const queryItem = {
      pageNum: 1,
      pageSize: 10000,
      companyId: undefined,
    };
    queryItem.companyId = res.dept.deptId;
    listVersion(queryItem).then((response) => {
      versionList.value = response.rows;
      if (versionList.value.length > 0) {
        queryParams.value.baseVersionId = versionList.value[0].objId;
        treeQueryList(versionList.value[0].objId);
      }
    });
  });
}

const loading = ref(false);
//批量操作
const handleSelectionChange = () => {};

const handleNodeClick = (node, item) => {
  if (node.children.length == 0) {
    queryList(node.baseVersionId);
  }
};

onMounted(() => {
  // queryList();
  getlistVersion();
});
</script>
<style lang="scss" scoped>
.app-container {
  display: flex;
  height: calc(100vh - 120px);
  .form-left {
    width: 20%;
    height: 100%;
    margin-right: 10px;
  }
  .form-right {
    width: 80%;
    height: 100%;
  }
  .module-btn {
    background-color: #f5f5f5;
    width: 100%;
    display: flex;
    justify-content: flex-start;
    font-size: 13px;
    div {
      border: 1px solid #ccc;
      padding: 4px 10px 2px 10px;
      color: #73797e;
      border-radius: 5px 5px 0 0;
      margin-right: 5px;
      cursor: pointer;
    }
    .active {
      font-weight: bold;
      color: #000;
      background: #fff;
    }
  }
  .sub-bottom {
    background-color: #f5f5f5;
    width: 100%;
  }
  // 图纸
  .sub-photo {
    display: flex;
    justify-content: space-between;
    width: 100%;
    height: 100%;
    .photo-left {
      width: 48%;
      height: 100%;
      .btn{
       background:rgba(166,215,213,0.3);
       padding:10px;
       padding:5px;
      }
    }
    .photo-right {
      width: 48%;
      height: 100%;
      border: 2px solid #ccc;
    }
  }
}
</style>
