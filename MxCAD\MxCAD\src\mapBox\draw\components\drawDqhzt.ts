import { McDbBlockTableRecord,MxCADUiPrPoint, McGePoint3d, McDbPolyline, MxCpp,MxTools, McDbText, McDb, McCmColor, McDbBlockReference, McDbAttributeDefinition,ColorIndexType,McDbMText,McGeVector3d } from "mxcad";
import {MxFun} from "mxdraw";
import {MxDrawTable} from "./drawTable";

export async function dqhztFun(value: any) {
    console.log("🚀 ~ dqhztFun ~ value:", value);
    const arr = value.tableData ? JSON.parse(value.tableData) : [];
    const drawingScale = 100;
    const offsetX = 0; // 默认值为 0 图纸是420x297
    const PAPER_WIDTH = 420/2;
    const PAPER_HEIGHT = 297/2;
    // 创建间隔数据
    const intervals = arr.map((_, i) => createInterval(i));
    
    // 创建新的CAD文件
    const mxcad = MxCpp.getCurrentMxCAD();
    mxcad.newFile();
     let leftHeight = 0 // 左边框宽
       let rightHeight = 0 // 右边框宽
       let bottomWidth = 0 // 下边框宽
       let borderWidth = 0 // 外框宽
       let TqBorderWidth = 0 // 图签宽
       let blkFilePath = import.meta.url.substring(0, import.meta.url.lastIndexOf('/')) + `/assets/TuK/A4-210x297 标准改1.dwg.mxweb`;
       // 插入图块文件
       let blkrecId = await mxcad.insertBlock(blkFilePath, 'A4-210x297 标准改1.dwg');
       if (!blkrecId.isValid()) {
           return
       }
   
       let blkRef = new McDbBlockReference();
       blkRef.blockTableRecordId = blkrecId;
       blkRef.position = new McGePoint3d(offsetX,0,0);
       mxcad.drawEntity(blkRef);
       let blkRecord: any = blkrecId.getMcDbBlockTableRecord();
       let ids = blkRecord.getAllEntityId();
       ids.forEach((id: any, index: any) => {
           if (!id.isKindOf("McDbAttributeDefinition")) return;
           let attribDef = id.getMcDbEntity() as McDbAttributeDefinition;
           if (attribDef.tag === '左边框宽') {
               leftHeight = Number(attribDef.textString)
           } else if (attribDef.tag === '右边框宽') {
               rightHeight = Number(attribDef.textString)
           } else if (attribDef.tag === '外框宽') {
               borderWidth = Number(attribDef.textString)
           } else if (attribDef.tag === '下边框高') {
               bottomWidth = Number(attribDef.textString)
           }
       })
       let blkFilePathT = import.meta.url.substring(0, import.meta.url.lastIndexOf('/')) + `/assets/TuK/标准图签改.dwg.mxweb`;
       let blkrecIdT = await mxcad.insertBlock(blkFilePathT, '标准图签改.dwg');
       if (!blkrecIdT.isValid()) {
           return
       }
       let blkRefT = new McDbBlockReference();
       blkRefT.blockTableRecordId = blkrecIdT;
       let blkRecordT: any = blkrecIdT.getMcDbBlockTableRecord();
       let idsT = blkRecordT.getAllEntityId();
       idsT.forEach((idT: any, index: any) => {
           if (!idT.isKindOf("McDbAttributeDefinition")) return;
           let attribDef = idT.getMcDbEntity() as McDbAttributeDefinition;
           if (attribDef.tag === '图签宽') {
               TqBorderWidth = Number(attribDef.textString)
           }
       })
       blkRefT.position = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + offsetX,bottomWidth,0);
       mxcad.drawEntity(blkRefT);
   
       // 创建文本实体
       const text = new McDbText();
       text.textString = '电气平面布置图'; // 使用传入的文本参数
       text.position = new McGePoint3d(115 + offsetX,275,0);
       text.alignmentPoint = new McGePoint3d(115 + offsetX,275,0);
       text.height = 7; // 文本高度
       text.widthFactor = 1; // 文本宽度缩放
       text.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
       text.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
       text.trueColor = new McCmColor(0, 0, 0); // 文本颜色（紫色）
       // 绘制文本
       mxcad.drawEntity(text);
   
       // 供电公司
       const textG = new McDbText();
       const positionG = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 10 + offsetX,bottomWidth + 16,0)
       textG.textString = value.countyOrganisationName ? value.countyOrganisationName : ''; // 使用传入的文本参数
       textG.position = positionG;
       textG.alignmentPoint = positionG;
       textG.height = 2; // 文本高度
       textG.widthFactor = 1; // 文本宽度缩放
       textG.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
       textG.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
       textG.trueColor = new McCmColor(0, 0, 0)
       // 绘制文本
       mxcad.drawEntity(textG);
   
       // 项目名称
       const textPrject = new McDbText();
       const positionPrject = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 55 + offsetX,bottomWidth + 16,0)
       textPrject.textString = value.projectName ? value.projectName : ''; // 使用传入的文本参数
       textPrject.position = positionPrject;
       textPrject.alignmentPoint = positionPrject;
       textPrject.height = 2; // 文本高度
       textPrject.widthFactor = 1; // 文本宽度缩放
       textPrject.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
       textPrject.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
       textPrject.trueColor = new McCmColor(0, 0, 0)
       // 绘制文本
       mxcad.drawEntity(textPrject);
   
       // 阶段
       const textJd = new McDbText();
       const positionJd = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 80 + offsetX,bottomWidth + 16,0)
       const textString = value.stage && value.stage === '1' ? '需求' : value.stage && value.stage === '2' ? '可研' : value.stage && value.stage === '3' ? '初设' : value.stage && value.stage === '4' ? '施工' : value.stage && value.stage === '5' ? '施工变更' : value.stage && value.stage === '5' ? '竣工' : ''
       textJd.textString = textString; // 使用传入的文本参数
       textJd.position = positionJd;
       textJd.alignmentPoint = positionJd;
       textJd.height = 2; // 文本高度
       textJd.widthFactor = 1; // 文本宽度缩放
       textJd.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
       textJd.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
       textJd.trueColor = new McCmColor(0, 0, 0)
       // 绘制文本
       mxcad.drawEntity(textJd);
   
       // 时间
       const textTime = new McDbText();
       const positionTime = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 40 + offsetX,bottomWidth + 1.5,0)
       const textStringTime = min_getTime()
       textTime.textString = textStringTime; // 使用传入的文本参数
       textTime.position = positionTime;
       textTime.alignmentPoint = positionTime;
       textTime.height = 1; // 文本高度
       textTime.widthFactor = 1; // 文本宽度缩放
       textTime.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
       textTime.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
       textTime.trueColor = new McCmColor(0, 0, 0)
       // 绘制文本
       mxcad.drawEntity(textTime);
   
       // 图号：项目编号+n
       const textCode = new McDbText();
       const positionCode = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 60 + offsetX,bottomWidth + 1.5,0)
       const textStringCode = value.proCode ? value.proCode + '-' + value.num : ''; // 使用传入的文本参数
       textCode.textString = textStringCode; // 使用传入的文本参数
       textCode.position = positionCode;
       textCode.alignmentPoint = positionCode;
       textCode.height = 1; // 文本高度
       textCode.widthFactor = 1; // 文本宽度缩放
       textCode.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
       textCode.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
       textCode.trueColor = new McCmColor(0, 0, 0)
       // 绘制文本
       mxcad.drawEntity(textCode);
   
       // 图框名称：主要设备清单+当前页1,2,3
       const textK = new McDbText();
       const positionK = new McGePoint3d(borderWidth - TqBorderWidth - rightHeight + 55 + offsetX,bottomWidth + 12,0)
       const textStringK = '电气平面布置图-' + value.num; // 使用传入的文本参数
       textK.textString = textStringK; // 使用传入的文本参数
       textK.position = positionK;
       textK.alignmentPoint = positionK;
       textK.height = 2; // 文本高度
       textK.widthFactor = 1; // 文本宽度缩放
       textK.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
       textK.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
       textK.trueColor = new McCmColor(0, 0, 0)
       // 绘制文本
       mxcad.drawEntity(textK);
// 初始基点位置 (从顶部开始，向下排列)
let baseY = 240;
const titleHeight = 500/drawingScale; // 标题文字高度
let l=115
// 1. 单列布置
// 添加"单列布置"标题 (放在布局上方)
const singleRowTitlePos = new McGePoint3d(l, baseY, 0);
await drawTextCopy(mxcad, singleRowTitlePos, '单列布置', 5);
baseY -= titleHeight; // 向下移动标题高度

// 绘制单列布置
const singleRowWidth = getTotalWidth(intervals, drawingScale, 'single');
console.log("单列布置总长度：", singleRowWidth);
const singleRowBase = new McGePoint3d(l, baseY-titleHeight, 0);
arrangeSingleRow(intervals, singleRowBase, drawingScale,singleRowWidth);
drawAllEntities(mxcad, intervals);
baseY -= getTotalHeight(intervals, drawingScale, 'single') +titleHeight;

// 2. 双列面对面布置
// 添加"双列面对面布置"标题 (放在布局上方)
const faceToFaceTitlePos = new McGePoint3d(l, baseY, 0);
await drawTextCopy(mxcad, faceToFaceTitlePos, '双列面对面布置', 5);
baseY -= titleHeight; // 向下移动标题高度

// 绘制双列面对面布置
const faceToFaceBase = new McGePoint3d(l, baseY-titleHeight, 0);
arrangeFaceToFace(intervals, faceToFaceBase, drawingScale);
drawAllEntities(mxcad, intervals);
baseY -= getTotalHeight(intervals, drawingScale, 'faceToFace') +titleHeight;

// 3. 双列背对背布置
// 添加"双列背对背布置"标题 (放在布局上方)
const backToBackTitlePos = new McGePoint3d(l, baseY-titleHeight, 0);
await drawTextCopy(mxcad, backToBackTitlePos, '双列背对背布置', 5);
baseY -= titleHeight; // 向下移动标题高度

// 绘制双列背对背布置
const backToBackBase = new McGePoint3d(l, baseY-titleHeight, 0);
arrangeBackToBack(intervals, backToBackBase, drawingScale);
drawAllEntities(mxcad, intervals);
    
    // 调整视图
    adjustView(mxcad);
    
    // 保存文件
    return saveToFile(mxcad);
}

// 计算布局总高度
function getTotalHeight(intervals: any[], scale: number, layoutType: string): number {
    const maxDepth = Math.max(...intervals.map(i => i.Depth / scale));
    return maxDepth;
}

// 其他辅助函数保持不变...
function createInterval(index: number) {
    const polyline = new McDbPolyline();
    for (let i = 0; i < 4; i++) {
        polyline.addVertexAt(new McGePoint3d(0, 0, 0));
    }
    polyline.isClosed = true;

    const text = new McDbMText();
    text.textHeight = 2.5;
    text.location = new McGePoint3d(0, 0, 0);
    text.attachment = McDb.AttachmentPoint.kMiddleCenter;
    text.contents = `G${index + 1}`;
    
    return {
        pl: polyline,
        Length: 1052,
        Depth: 640,
        txt: text
    };
}

function arrangeSingleRow(intervals: any[], basePT: McGePoint3d, scale: number,singleRowWidth:number) {
    for (const item of intervals) {
        const length = item.Length / scale;
        const depth = item.Depth / scale;
        let pole=singleRowWidth/2
        item.txt.location = new McGePoint3d(
            basePT.x + (length / 2)-pole,
            basePT.y + (depth / 2),
            0
        );
        
        item.pl.setPointAt(0, new McGePoint3d(basePT.x-pole, basePT.y, 0));
        item.pl.setPointAt(1, new McGePoint3d(basePT.x-pole + length, basePT.y, 0));
        item.pl.setPointAt(2, new McGePoint3d(basePT.x-pole + length, basePT.y + depth, 0));
        item.pl.setPointAt(3, new McGePoint3d(basePT.x-pole, basePT.y + depth, 0));
        
        basePT.x += length;
    }
}

function arrangeFaceToFace(intervals: any[], basePT: McGePoint3d, scale: number) {
    const half = Math.ceil(intervals.length / 2);
    
    // 第一排
    for (let i = 0; i < half; i++) {
        const item = intervals[i];
        const length = item.Length / scale;
        const depth = item.Depth / scale;
        
        item.txt.location = new McGePoint3d(
            basePT.x + (length / 2),
            basePT.y + (depth / 2),
            0
        );
        
        item.pl.setPointAt(0, new McGePoint3d(basePT.x, basePT.y, 0));
        item.pl.setPointAt(1, new McGePoint3d(basePT.x + length, basePT.y, 0));
        item.pl.setPointAt(2, new McGePoint3d(basePT.x + length, basePT.y + depth, 0));
        item.pl.setPointAt(3, new McGePoint3d(basePT.x, basePT.y + depth, 0));
        
        basePT.x += length;
    }
    
    // 第二排
    const distance = 20 / scale;
    basePT.x = 115;
    basePT.y -= distance;
    
    for (let i = half; i < intervals.length; i++) {
        const item = intervals[i];
        const length = item.Length / scale;
        const depth = item.Depth / scale;
        
        item.txt.location = new McGePoint3d(
            basePT.x - (length / 2),
            basePT.y - (depth / 2),
            0
        );
        item.txt.rotation = Math.PI;
        
        item.pl.setPointAt(0, new McGePoint3d(basePT.x, basePT.y, 0));
        item.pl.setPointAt(1, new McGePoint3d(basePT.x - length, basePT.y, 0));
        item.pl.setPointAt(2, new McGePoint3d(basePT.x - length, basePT.y - depth, 0));
        item.pl.setPointAt(3, new McGePoint3d(basePT.x, basePT.y - depth, 0));
        
        basePT.x -= length;
    }
}

function arrangeBackToBack(intervals: any[], basePT: McGePoint3d, scale: number) {
    const half = Math.ceil(intervals.length / 2);
    
    // 第一排
    for (let i = 0; i < half; i++) {
        const item = intervals[i];
        const length = item.Length / scale;
        const depth = item.Depth / scale;
        
        item.txt.location = new McGePoint3d(
            basePT.x + (length / 2),
            basePT.y - (depth / 2),
            0
        );
        item.txt.rotation = Math.PI;
        
        item.pl.setPointAt(0, new McGePoint3d(basePT.x, basePT.y, 0));
        item.pl.setPointAt(1, new McGePoint3d(basePT.x + length, basePT.y, 0));
        item.pl.setPointAt(2, new McGePoint3d(basePT.x + length, basePT.y - depth, 0));
        item.pl.setPointAt(3, new McGePoint3d(basePT.x, basePT.y - depth, 0));
        
        basePT.x += length;
    }
    
    // 第二排
    const distance = (countDistance(intervals) / scale) + (10 / scale);
    basePT.x = 115;
    basePT.y -= distance;
    
    for (let i = half; i < intervals.length; i++) {
        const item = intervals[i];
        const length = item.Length / scale;
        const depth = item.Depth / scale;
        
        item.txt.location = new McGePoint3d(
            basePT.x - (length / 2),
            basePT.y + (depth / 2),
            0
        );
        
        item.pl.setPointAt(0, new McGePoint3d(basePT.x, basePT.y, 0));
        item.pl.setPointAt(1, new McGePoint3d(basePT.x - length, basePT.y, 0));
        item.pl.setPointAt(2, new McGePoint3d(basePT.x - length, basePT.y + depth, 0));
        item.pl.setPointAt(3, new McGePoint3d(basePT.x, basePT.y + depth, 0));
        
        basePT.x -= length;
    }
}

function drawAllEntities(mxcad: any, intervals: any[]) {
    for (const interval of intervals) {
        mxcad.drawEntity(interval.pl);
        mxcad.drawEntity(interval.txt);
    }
}

function adjustView(mxcad: any) {
    mxcad.zoomAll();
    mxcad.regen();
    mxcad.updateDisplay();
}
function getTotalWidth(intervals: any[], scale: number, layoutType: string): number {
    switch(layoutType) {
        case 'single':
            return intervals.reduce((sum, item) => sum + item.Length / scale, 0);
        
        case 'faceToFace':
        case 'backToBack':
            const half = Math.ceil(intervals.length / 2);
            const firstRowWidth = intervals.slice(0, half)
                .reduce((sum, item) => sum + item.Length / scale, 0);
            const secondRowWidth = intervals.slice(half)
                .reduce((sum, item) => sum + item.Length / scale, 0);
            return Math.max(firstRowWidth, secondRowWidth);
        
        default:
            return 0;
    }
}
function saveToFile(mxcad: any): Promise<File> {
    return new Promise((resolve, reject) => {
        mxcad.saveFile(void 0, (data: any) => {
            if (!data) {
                reject(new Error("保存文件失败"));
                return;
            }
            
            const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
            const blob = new Blob([data.buffer], { 
                type: isSafari ? "application/octet-stream" : "application/octet-binary" 
            });
            
            resolve(new File([blob], 'interval_layout.mxweb', { 
                type: blob.type 
            }));
        }, false, true);
    });
}

function countDistance(intervals: any[]): number {
    const half = Math.ceil(intervals.length / 2);
    
    const maxDepth1 = Math.max(...intervals.slice(0, half).map(i => i.Depth));
    const maxDepth2 = Math.max(...intervals.slice(half).map(i => i.Depth));
    
    return maxDepth1 + maxDepth2;
}

//封装文字显示
async function drawTextCopy(mxcad, newCenterPt, textString, textHeight) {
    const text = new McDbText();
    text.textString = textString;
    text.position = new McGePoint3d(newCenterPt.x, newCenterPt.y, newCenterPt.z);
    text.alignmentPoint = new McGePoint3d(newCenterPt.x, newCenterPt.y, newCenterPt.z);
    text.height = textHeight;
    text.widthFactor = 1;
    text.horizontalMode = McDb.TextHorzMode.kTextCenter;
    text.verticalMode = McDb.TextVertMode.kTextVertMid;
    text.trueColor = new McCmColor(0, 0, 0);
    mxcad.drawEntity(text);
} 
// 获取当前时间
function min_getTime() {
    const date = new Date()
    const y = date.getFullYear()
    let m = date.getMonth() + 1
    m = m < 10 ? '0' + m : m
    let d = date.getDate()
    d = d < 10 ? '0' + d : d
    return y + '-' + m + '-' + d
}