<template>
  <div>
    <el-row justify="space-between">
      <div>
        <el-button type="primary" @click="pushEvent">新增</el-button>
        <el-button type="danger" @click="removeSelectEvent">删除</el-button>
      </div>
      <div>
        <el-button type="primary" @click="saveChange">保存</el-button>
      </div>
    </el-row>
    <vxe-grid ref="gridRef" :columns="columns" :data="tableData" v-bind="gridOptions">
      <template #edit_moduleId="{ row }">
        <vxe-select v-model="row.moduleId" :option-props="{label: 'modulename',value:'moduleid'}" :options="row.moduleOptions" @change="moduleChange(row)"></vxe-select>
      </template>
      <template #default_moduleId="{ row }">
        <span>{{ row.moduleName }}</span>
      </template>

    </vxe-grid>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import {deletePersonalization, saveData} from "@/api/personalized-setting/index.js";
import {ElMessage, ElMessageBox} from "element-plus";

const props = defineProps({
  columns: {
    type: Array,
    default: () => []
  },
  data: {
    type: Array,
    default: () => []
  },
  activeName: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['getList'])

const gridRef = ref()
const gridOptions = reactive({
  border: true,
  showOverflow: true,
  height: 400,
  editConfig: {
    trigger: 'click',
    mode: 'cell',
  },
})

const tableData = ref([])

watch(() => props.data, (newValue) => {
  tableData.value = newValue
})


const moduleChange = (row) => {
  row.moduleName = row.moduleOptions.find(item => item.moduleid === row.moduleId)?.modulename
}
const insertRecords = []
const pushEvent = () => {
  const newRow = {temporaryId: `${new Date().getTime()}`}
  tableData.value.push(newRow)
  insertRecords.push(newRow)
  nextTick(() => {
    const $grid = gridRef.value
    if ($grid) {
      $grid.setEditRow(newRow)
    }
  })
}
const removeSelectEvent = async () => {
  const $grid = gridRef.value
  if ($grid) {
    const selectRecords = $grid.getCheckboxRecords()
    if (selectRecords.length > 0) {
      await deleteChange()
    } else {
      ElMessage.error('未选择数据')
    }
  }
}

const deleteChange = async () => {
  const $grid = gridRef.value
  if (!$grid) return
  try {
    await ElMessageBox.confirm("是否删除?", "消息", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })

    const selectRecords = $grid.getCheckboxRecords()
    if (selectRecords.length === 0) {
      ElMessage.error('请选择要删除的记录')
      return
    }

    // 分离新记录和已有记录
    const tempIds = new Set(insertRecords.map(v => v.temporaryId))
    const existingIds = selectRecords.filter(item => item.id).map(v => v.id)

    // 立即更新本地数据
    tableData.value = tableData.value.filter(item =>
        !tempIds.has(item?.temporaryId) &&
        !existingIds.includes(item.id)
    )

    // 需要服务端删除的记录
    if (existingIds.length) {
      const res = await deletePersonalization(existingIds)
      if (res.code !== 200) {
        ElMessage.error(res.msg)
        return
      }
    }

    $grid.removeCheckboxRow()
    ElMessage.success('删除成功')
    // emit('getList') // 统一刷新数据
  } catch (error) {
    // 取消删除或错误处理
    if (error !== 'cancel') {
      ElMessage.error('删除操作失败')
    }
  }
}

const saveChange = () => {
  console.log('saveChange')
  const $grid = gridRef.value
  const insertRecords = $grid.getInsertRecords()
  const data = tableData.value
  console.log('saveChange',insertRecords, data)
  saveData(data).then((res) => {
    if(res.code === 200) {
      ElMessage.success('保存成功')
      emit('getList')
    } else {
      ElMessage.error(res.msg)
    }
  })
}

</script>
