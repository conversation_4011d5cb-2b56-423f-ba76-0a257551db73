import{h as J,d as L,a0 as i,_ as S,$ as r,a1 as e,V as o,m as d,a3 as c,a4 as z,ab as Q,u as n,F as M,B as X,c as le,K as W,Q as g,H as m,U as E,a5 as ne,W as oe,a2 as ie,A as ae}from"./vue-DfH9C9Rx.js";import{M as Y}from"./index-8X61wlK0.js";import{J as ce,I as de,O as re,_ as G,w as ue,n as T,W as me,X as q,Y as F}from"./index-D95UjFey.js";import{S as P,i as pe,b as y,R as C,e as _e}from"./vuetify-B_xYg4qv.js";import{u as he}from"./hooks-B307GJzH.js";import"./mxcad-CfPpL1Bn.js";import"./mxdraw-C_n_7lEs.js";import"./mapbox-gl-PN3pDJHq.js";import"./xlsx-Ck5MUZvf.js";const ve=()=>{const V=ce(),{createLineType:h}=V,{lineTypeList:f}=de(V);return{lineTypeList:f,createLineType:h}},ke={class:"mt-2"},ge={class:"text-center"},Ce={class:"text-center"},fe={class:"text-center"},be=["onClick"],xe=J({__name:"index",emits:["change"],setup(V,{expose:h,emit:f}){const{createLineType:D,lineTypeList:b}=ve(),v=L(0),{isShow:x,showDialog:w,onReveal:R}=re(!1),B=f,U=[{name:"确定",fun:()=>{B("change",b.value[v.value]),w(!1)},primary:!0},{name:"取消",fun:()=>w(!1)}];return R(u=>{v.value=b.value.findIndex(k=>k.name===u.name)}),h({showDialog:w}),(u,k)=>(i(),S(Y,{title:u.t("线型管理器"),"max-width":"500",modelValue:n(x),"onUpdate:modelValue":k[0]||(k[0]=p=>X(x)?x.value=p:null),footerBtnList:U},{default:r(()=>[e("div",ke,[e("p",null,o(u.t("已加载的线型")),1),d(P,{height:"300"},{default:r(()=>[e("thead",null,[e("tr",null,[e("th",ge,o(u.t("线型")),1),e("th",Ce,o(u.t("外观")),1),e("th",fe,o(u.t("说明")),1)])]),e("tbody",null,[(i(!0),c(M,null,z(n(b),(p,$)=>(i(),c("tr",{key:p.id,class:Q(v.value===$?"active":""),onClick:A=>v.value=$},[e("td",null,o(p.name),1),e("td",null,o(p.explain),1),e("td",null,o(p.appearance),1)],10,be))),128))])]),_:1})])]),_:1},8,["title","modelValue"]))}}),we=G(xe,[["__scopeId","data-v-ccffb7e5"]]),ye={class:"d-flex align-center"},Le={class:"d-flex align-center my-2"},Ve={class:"w-100",style:{"z-index":"1"}},De={class:"w-100"},$e={class:"w-20"},Te={class:""},Se={class:""},Me={class:""},Re={class:"w-20"},Be={class:"w-20"},Ue={class:"w-100",ref:"tbody"},Ne=["id","onClick","onContextmenu"],je={class:"text-no-wrap"},Fe={style:{width:"18px"},class:"d-inline-block"},ze=["for"],Ae=["disabled","id","onClick","onUpdate:modelValue"],He=["onClick"],Ke=["onClick"],Oe=["onClick"],We=["onClick"],Ee=["onClick"],qe={class:"w-100 my-3",cellpadding:"20"},Je={class:"w-100"},Qe={class:"w-100"},Xe={class:"w-auto px-6"},Ye={class:"d-flex justify-end w-auto"},Ge=J({__name:"index",setup(V){const h=L(),{onClickLayer:f,onClickStopTD:D,onClickLayerName:b,resumeIndex:v,reverseSelection:x,selectColor:w,selectLineType:R,setIndex:B,onRightClickLayer:U,initLayerList:u,setLayerList:k,list:p,bodyRightClickMenuOptions:$,btnList:A,isShiftKeyMultipleChoice:Pe,isCtrlKeyMultipleChoice:Ze,rootId:Z}=he({onAddLayer(){const s=(h.value?.$el).getElementsByClassName("v-table__wrapper")[0];ae(()=>{s.scrollTo({top:s.scrollHeight-s.clientHeight,behavior:"smooth"})})}});u();const H=L([]),I=l=>{const{map:s}=l(H.value,(O,j)=>j);s.length>0&&B(s)},K=L(),ee=l=>{K.value?.showDialog(!0,l)},N=L(""),te=ue(l=>{l===null&&(l=""),N.value=l},500),se=le(()=>p.value.filter(l=>l.name.toLowerCase().includes(N.value.toLowerCase())));return(l,s)=>{const O=W("box-selection"),j=W("right-click-menu");return i(),c(M,null,[d(Y,{maxWidth:"800",ref:"layerDialog",modelValue:n(F),"onUpdate:modelValue":s[4]||(s[4]=t=>X(F)?F.value=t:null),title:l.t("531")},{actions:r(()=>s[9]||(s[9]=[e("div",{class:"mt-1"},null,-1)])),default:r(()=>[e("div",ye,[e("div",Le,[(i(!0),c(M,null,z(n(A),(t,_)=>(i(),S(T,{onClick:t.fun,class:"mx-2 px-2",key:t.name+_},{default:r(()=>[g(o(l.t(t.name)),1)]),_:2},1032,["onClick"]))),128))]),d(pe,{"model-values":N.value,"onUpdate:modelValue":n(te),class:"pa-1",clearable:"",density:"compact",label:l.t("63"),variant:"solo","hide-details":"","single-line":""},{"append-inner":r(()=>[d(y,{icon:"$mdi-magnify",class:"v-icon--clickable"})]),_:1},8,["model-values","onUpdate:modelValue","label"])]),m((i(),S(n(P),{class:"w-100 layer-table",height:"300",ref_key:"boxRef",ref:h,onClick:s[0]||(s[0]=t=>n(v)())},{default:r(()=>[e("thead",Ve,[e("tr",De,[e("th",$e,[s[5]||(s[5]=e("div",{style:{width:"10px"},class:"d-inline-block"},null,-1)),s[6]||(s[6]=g()),e("span",null,o(l.t("188")+l.t("209")),1)]),e("th",Te,o(l.t("223")),1),e("th",Se,o(l.t("224")),1),e("th",Me,o(l.t("225")),1),e("th",Re,o(l.t("226")),1),e("th",Be,o(l.t("227")),1)])]),m((i(),c("tbody",Ue,[(i(!0),c(M,null,z(se.value,(t,_)=>(i(),c("tr",{class:Q(["text-center layer-info",t.isSelect?"active":""]),ref_for:!0,ref_key:"refItems",ref:H,key:t.id,id:t.id.toString(),onClick:E(a=>n(f)(_),["prevent"]),onContextmenu:E(a=>n(U)(_,a),["prevent"])},[m((i(),c("td",je,[e("div",Fe,[t.status?(i(),S(y,{key:0,icon:"class:iconfont gou",size:"x-small",color:"#16FD21"})):ne("",!0)]),e("label",{class:"d-inline-block",for:t.id.toString()},[m(e("input",{disabled:t.id===n(Z),id:t.id.toString(),class:"text-truncate text-center",type:"text",onClick:a=>n(b)(a,_),"onUpdate:modelValue":a=>t.name=a},null,8,Ae),[[oe,t.name]])],8,ze)])),[[C]]),m((i(),c("td",{class:"text-orange",onClick:a=>n(D)(a,t,"visible",!t.visible,_)},[d(y,{icon:t.visible?"yanjing1":"yanjing"},null,8,["icon"])],8,He)),[[C,void 0,void 0,{prevent:!0}]]),m((i(),c("td",{class:"text-orange",onClick:a=>n(D)(a,t,"lock",!t.lock,_)},[d(y,{icon:t.lock?"suo":"jiesuo1"},null,8,["icon"])],8,Ke)),[[C]]),m((i(),c("td",{onClick:a=>n(D)(a,t,"print",!t.print,_)},[d(y,{icon:t.print?"dayin":"budayinbiaoqian"},null,8,["icon"])],8,Oe)),[[C]]),m((i(),c("td",{class:"d-flex align-center justify-center",onClick:a=>n(w)(t.color)},[e("div",{class:"colorBox mr-2",style:ie({background:`${t.color.color}`})},null,4),e("span",null,o(n(me)(t.color.name)),1)],8,We)),[[C]]),m((i(),c("td",{class:"w-20 text-truncate",onClick:a=>ee(t.lineType)},[g(o(t.lineType.name),1)],8,Ee)),[[C]])],42,Ne))),128))])),[[O,I]])]),_:1})),[[j,n($)]]),d(_e,{color:"#576375",class:"border-opacity-75"}),e("table",qe,[e("thead",Je,[e("tr",Qe,[s[7]||(s[7]=e("th",{class:"w-20"},null,-1)),e("th",Xe,[d(T,{onClick:s[1]||(s[1]=t=>n(x)())},{default:r(()=>[g(o(l.t("228")),1)]),_:1})]),s[8]||(s[8]=e("th",{class:"w-50"},null,-1)),e("th",Ye,[d(T,{isAction:"",primary:"",onClick:s[2]||(s[2]=t=>(n(k)(),n(q)(!1)))},{default:r(()=>[g(o(l.t("229")),1)]),_:1}),d(T,{class:"ml-10 mr-6",onClick:s[3]||(s[3]=t=>n(q)())},{default:r(()=>[g(o(l.t("230")),1)]),_:1})])])])])]),_:1},8,["modelValue","title"]),d(we,{ref_key:"linearManager",ref:K,onChange:n(R)},null,8,["onChange"])],64)}}}),ct=G(Ge,[["__scopeId","data-v-1c961198"]]);export{ct as default};
