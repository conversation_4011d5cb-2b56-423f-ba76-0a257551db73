import{f as G,a6 as F,s as r,l as I,o as ee,Q as ae,a7 as te,e as A,X as le,U as y,V as X,W,_ as se}from"./index-CzBriCFR.js";import{M as oe}from"./index-itnQ6avM.js";import{h as ne,i as ie,M as $,j as ce,g as re,y as ue}from"./mxcad-DrgW2waE.js";import{d as w,r as de,h as me,a3 as fe,a4 as t,u as l,B as R,_ as H,a0 as C,m as e,Q as v,V as k,ac as Y,$ as Z}from"./vue-Cj9QYd7Z.js";import{g as be,B as q,I as J,L as K,G as pe,H as h,b as ke,z as _e}from"./vuetify-BqCp6y38.js";import"./mxdraw-BQ5HhRCY.js";import"./handsontable-Ch5RdAT_.js";import"./mapbox-gl-DQAr7S0z.js";var M=(d=>(d[d.HOLD=0]="HOLD",d[d.CONVERT_TO_BLOCK=1]="CONVERT_TO_BLOCK",d[d.DELETE=2]="DELETE",d))(M||{});const ye=()=>{const d=w(""),B=w([]);ee(()=>{B.value=F()});const m=G(!1,"Mx_CreateBlocksDialog_is_specify_on_screen_get_base_point"),n=de({x:0,y:0,z:0}),T=async()=>{const b=new ne;b.clearLastInputPoint(),b.setMessage(r("465"));const p=await b.go();if(!p)return;const{x:_,y:g,z:u}=p;return n.x=I(_,3),n.y=I(g,3),n.z=I(u,3),p},i=G(!1,"Mx_CreateBlocksDialog_is_specify_on_screen_select_object"),N=G(2,"Mx_CreateBlocksDialog_select_Object_operation_type"),c=w(!0),V=w(0);let f;const x=async()=>{if(f=new ie,V.value=0,!await f.userSelect(r("376")))return;const b=f.count();if(V.value=b,b===0){c.value=!0;return}return c.value=!1,f},S=()=>{const p=$.getCurrentMxCAD().getDatabase().getBlockTable(),_=new ce;return _.name=d.value,p.add(_)},L=(b,p,_)=>{p.getMcDbBlockTableRecord()?.getAllEntityId().forEach(E=>{E.getMcDbEntity()?.erase()});let g=p.getMcDbBlockTableRecord();if(!g)return;b.forEach(E=>{const j=E.getMcDbEntity();if(!j)return;const U=j.clone();g&&g.appendAcDbEntity(U)}),g.origin=_;let u=new re;return u.blockTableRecordId=p,u.position=_,u},P=w("");return{blockName:d,blockNames:B,getBlockNames:F,basePoint:n,getMouseClickGetBasePoint:T,isSpecifyOnScreenGetBasePoint:m,isNoSelectObject:c,isSpecifyOnScreenSelectObject:i,selectObjectOperationType:N,selectObject:x,createBlock:S,loadBlock:L,explainText:P,selectCount:V}},ve={class:"px-3"},ge={class:"f-flex justify-center align-center mt-2"},Ve={class:"f-flex justify-center align-center mt-2"},xe={class:"d-flex flex-column"},Ce={key:0,class:"f-flex justify-center align-center mt-2"},Be={key:1},De=me({__name:"index",setup(d){const{isShow:B,showDialog:m}=ae(!1,"Mx_Block",()=>{m(!0),_()}),{blockName:n,blockNames:T,basePoint:i,getMouseClickGetBasePoint:N,isSpecifyOnScreenGetBasePoint:c,isNoSelectObject:V,isSpecifyOnScreenSelectObject:f,selectObjectOperationType:x,selectObject:S,selectCount:L,createBlock:P,loadBlock:b,explainText:p}=ye(),_=()=>{i.x=0,i.y=0,i.z=0,n.value="",u=void 0,V.value=!0,T.value=F()},g=async()=>{m(!1),await N(),m(!0)};let u;const E=async()=>{m(!1),u=await S(),m(!0)},U=[{name:"确定",fun:async()=>{const{open:o}=te();let a=!1;const s=T.value.includes(n.value);if(n.value==="")return A().error(r("图块名不能为空")+"!");if(s)try{await new Promise((D,O)=>{o({title:r("是否替换该图块"),text:r("已定义")+n.value+r("的")+r("图块")+"，"+r("是否替换")+"?",define:()=>{a=!0,D()},cancel:()=>{a=!1,O()},defineTitle:r("是"),cancelTitle:r("否")})})}catch{return}let z;if(a?z=$.getCurrentMxCAD().getDatabase().getBlockTable().get(n.value):z=P(),c.value&&(m(!1),await N()),f.value&&(m(!1),u=await S()),!u||V.value)return A().error(r("没有为块")+n.value+r("选择对象")+"!");const Q=b(u,z,new ue(i.x,i.y,i.z));if(!Q)return A().error(r("创建块失败")+"!");x.value!==M.HOLD&&(u.forEach(D=>{const O=D.getMcDbEntity();O&&O.erase()}),le()),x.value===M.CONVERT_TO_BLOCK&&$.getCurrentMxCAD().drawEntity(Q),m(!1)},primary:!0},{name:"关闭",fun:()=>{m(!1)}}];return(o,a)=>(H(),fe(oe,{title:o.t("213"),modelValue:l(B),"onUpdate:modelValue":a[7]||(a[7]=s=>R(B)?B.value=s:null),"max-width":"400",footerBtnList:U},{default:t(()=>[C("div",ve,[e(be,{class:"mt-2",modelValue:l(n),"onUpdate:modelValue":a[0]||(a[0]=s=>R(n)?n.value=s:null),items:l(T)},{prepend:t(()=>[e(y,{"key-name":"N"},{default:t(()=>[v(k(o.t("209")),1)]),_:1})]),_:1},8,["modelValue","items"]),e(_e,{"align-stretch":""},{default:t(()=>[e(q,{cols:6,"align-self":"stretch"},{default:t(()=>[e(X,{title:o.t("214"),class:"h-100"},{default:t(()=>[e(J,{modelValue:l(c),"onUpdate:modelValue":a[1]||(a[1]=s=>R(c)?c.value=s:null)},{label:t(()=>[e(y,{"key-name":"S"},{default:t(()=>[v(k(o.t("215")),1)]),_:1})]),_:1},8,["modelValue"]),C("div",ge,[e(W,{disabled:l(c),onClick:g},null,8,["disabled"]),e(y,{class:Y(l(c)?"text-disabled":""),"key-name":"S"},{default:t(()=>[v(k(o.t("216")),1)]),_:1},8,["class"])]),e(K,{class:"mt-3",modelValue:l(i).x,"onUpdate:modelValue":a[2]||(a[2]=s=>l(i).x=s),type:"number",disabled:l(c)},{prepend:t(()=>a[8]||(a[8]=[C("span",{class:""}," X:",-1)])),_:1},8,["modelValue","disabled"]),e(K,{class:"mt-1",modelValue:l(i).y,"onUpdate:modelValue":a[3]||(a[3]=s=>l(i).y=s),type:"number",disabled:l(c)},{prepend:t(()=>a[9]||(a[9]=[C("span",{class:""}," Y: ",-1)])),_:1},8,["modelValue","disabled"]),e(K,{class:"mt-1",modelValue:l(i).z,"onUpdate:modelValue":a[4]||(a[4]=s=>l(i).z=s),type:"number",disabled:l(c)},{prepend:t(()=>a[10]||(a[10]=[C("span",{class:""}," Z:",-1)])),_:1},8,["modelValue","disabled"])]),_:1},8,["title"])]),_:1}),e(q,{cols:6,"align-self":"stretch"},{default:t(()=>[e(X,{title:o.t("208"),class:"h-100"},{default:t(()=>[e(J,{modelValue:l(f),"onUpdate:modelValue":a[5]||(a[5]=s=>R(f)?f.value=s:null)},{label:t(()=>[e(y,{class:"","key-name":"S"},{default:t(()=>[v(k(o.t("215")),1)]),_:1})]),_:1},8,["modelValue"]),C("div",Ve,[e(W,{disabled:l(f),onClick:E},null,8,["disabled"]),e(y,{class:Y(l(f)?"text-disabled":""),"key-name":"I"},{default:t(()=>[v(k(o.t("205")),1)]),_:1},8,["class"])]),e(pe,{column:"",class:"mt-2",modelValue:l(x),"onUpdate:modelValue":a[6]||(a[6]=s=>R(x)?x.value=s:null)},{default:t(()=>[C("div",xe,[e(h,{value:l(M).HOLD,class:"mt-1"},{label:t(()=>[e(y,{class:"","key-name":"R"},{default:t(()=>[v(k(o.t("217")),1)]),_:1})]),_:1},8,["value"]),e(h,{value:l(M).CONVERT_TO_BLOCK,class:"mt-1"},{label:t(()=>[e(y,{class:"","key-name":"C"},{default:t(()=>[v(k(o.t("218")),1)]),_:1})]),_:1},8,["value"]),e(h,{value:l(M).DELETE,class:"mt-1"},{label:t(()=>[e(y,{class:"","key-name":"D"},{default:t(()=>[v(k(o.t("219")),1)]),_:1})]),_:1},8,["value"])])]),_:1},8,["modelValue"]),l(V)?(H(),Z("div",Ce,[e(ke,{icon:"jinggao"}),e(y,{class:"","key-name":"I"},{default:t(()=>[v(k(o.t("220")),1)]),_:1})])):(H(),Z("div",Be,k(o.t("221")+":"+l(L)+o.t("207")),1))]),_:1},8,["title"])]),_:1})]),_:1})])]),_:1},8,["title","modelValue"]))}}),Le=se(De,[["__scopeId","data-v-49e41a89"]]);export{Le as default};
