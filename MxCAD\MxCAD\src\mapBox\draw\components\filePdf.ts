import {
    McDbBlockReference, MxCpp, McDbDatabase, McGePoint3d, McDbAttributeDefinition,McDbBlockTableRecord,McDbEntity
} from "mxcad"
export async function filePdf (value) {
    const dataBase:McDbDatabase = MxCpp.getCurrentMxCAD().getDatabase();
    const objectId = dataBase.handleToIdIndex(value.handler);
    const entVal = objectId.getMcDbEntity();
    // 得到实体的包围盒
    const {ret, minPt, maxPt} = entVal.getBoundingBox();
    if(ret){
        // MxCpp.getCurrentMxCAD().zoomW(minPt, maxPt);
        // 返回一个 Promise
        return new Promise((resolve, reject) => {
            MxCpp.getCurrentMxCAD().saveFile(void 0, (data) => {
                let blob: Blob;
                const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
                if (isSafari) {
                    blob = new Blob([data.buffer], { type: "application/octet-stream" });
                } else {
                    blob = new Blob([data.buffer], { type: "application/octet-binary" });
                }
                const file = new File([blob], value.name + '.mxweb', { type: isSafari ? "application/octet-stream" : "application/octet-binary" });
                resolve(file); // 返回文件对象
            }, false, true);
        });
    }
}