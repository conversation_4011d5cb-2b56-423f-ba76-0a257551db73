import { serializeMap, deserializeMap } from '@/utils/serializeMap.js'

const useProjectInfoStore = defineStore(
    'projectInfo',
    {
        state: () => ({
            projectInfo: JSON.parse(localStorage.getItem('projectInfo') || '{}'),
            geographicConditionsSetting: {},
            baseSetting: JSON.parse(localStorage.getItem('baseSetting') || '{}'),
            calculationSetting: JSON.parse(localStorage.getItem('calculationSetting') || '{}'),
        }),
        actions: {
            addProjectInfo(params) {
                localStorage.removeItem('projectInfo')
                this.projectInfo = params
                localStorage.setItem('projectInfo', JSON.stringify(params))
            },
            getProjectInfo(key) {
                return this.projectInfo[key];
            },

            //工程的 地理条件设置
            addGeographicConditionsSetting(params) {
                this.geographicConditionsSetting = params.data
                if(!localStorage.getItem('geographicConditions')){
                    const map = new Map()
                    map.set(params.objId, params.data)
                    localStorage.setItem('geographicConditions', serializeMap(map))
                } else{
                    const map =  deserializeMap(localStorage.getItem('geographicConditions'))
                    map.set(params.objId, params.data)
                    localStorage.setItem('geographicConditions', serializeMap(map))
                }
            },
            //工程的 地理条件设置
            getGeographicConditionsSetting(objId) {
                const geographicConditions = localStorage.getItem('geographicConditions')
                if(!geographicConditions){
                    return null;
                }
                return deserializeMap(geographicConditions).get(objId);
            },

            //基础参数设置 绑定用户
            addBaseSetting(params) {
                localStorage.setItem('baseSetting', JSON.stringify(params))
                this.baseSetting = params
            },
            //基础参数设置 绑定用户
            getBaseSetting(key) {
                if(localStorage.getItem('baseSetting')){
                    return JSON.parse(JSON.parse(localStorage.getItem('baseSetting'))[key])
                }
                return null;
            },

            //计算参数设置 绑定用户
            addCalculationSetting(params) {
                localStorage.setItem('calculationSetting', params)
                this.calculationSetting = params
            },
            //计算参数设置 绑定用户
            getCalculationSetting(key) {
                if(localStorage.getItem('calculationSetting')){
                    return JSON.parse(localStorage.getItem('calculationSetting'))[key]
                }
                return null;
            }
        },
        persist: true
    })

export default useProjectInfoStore
