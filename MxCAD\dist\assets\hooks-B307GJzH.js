import{ah as z,I as B,aC as N,aD as G,aE as M,G as H,J as U,aF as q,af as Q,a5 as W}from"./index-D95UjFey.js";import"./mxcad-CfPpL1Bn.js";import"./mapbox-gl-PN3pDJHq.js";import{r as X}from"./vue-DfH9C9Rx.js";const R=z(),{add:Y,remove:T,putCurrent:k,toggle:Z,setValue:u,setIndex:o,getIndex:O,stringifyJSON:$,create:ue,initLayerList:ee,setLayerList:te}=R,{index:a,list:r,currentLayer:ae,rootId:g}=B(R);let L=!1;const le=s=>{s=typeof s=="boolean"?s:L,u("visible",s,r.value.map((c,f)=>f)),L=!s},ce=({onAddLayer:s})=>{const c=N("Shift"),f=N("Control"),y=()=>Array.isArray(a.value)&&a.value.length>1,D=(e,t,...l)=>{const[n,p,S]=l;if(c.value||f.value)return e.preventDefault();if(!t.isSelect)return u(n,p,S);if(y())return e.stopPropagation(),u(n,p),!1;u(n,p,S)};let i;const h=(e,t)=>{if(t&&t.target&&A(t.target),c.value){typeof i>"u"&&(i=a.value);const l=Math.max(i,e);o(q(l===e?i:e,l));return}if(f.value){let l=[];if(Array.isArray(a.value)){const n=a.value.indexOf(e);n>=0&&a.value.length>1?(l=[...a.value],l.splice(n,1)):l=[...a.value,e]}else l=[a.value,e];o(l);return}o(e),i=e,d()},E=(e,t)=>{(!y()||!r.value[e].isSelect)&&h(e,t)};let v;const d=()=>{Array.isArray(a.value)?v=a.value[a.value.length-1]:v=a.value},C=()=>{s&&s(Y()),c&&(i=r.value.length-1)};let m;const A=e=>{if(e)if(e.tagName==="INPUT")m=e;else{let l=e?.getElementsByTagName("input")[0];l?m=l:A(e.parentNode)}},x=e=>{const t=O();if(Array.isArray(t)&&t.length>1)return;const l=Array.isArray(t)?t[0]:t;ae.value!==r.value[l]&&r.value[l].id!==g.value&&e&&setTimeout(()=>{e.focus(),e.select()})},K=(e,t)=>{m=e.target,a.value===t&&x(m)},j=()=>{v&&o(v),v=void 0},w=()=>{o(r.value.map((e,t)=>t))},F=()=>{o([]),d()},I=()=>{const e=Array.isArray(a.value)?a.value:[a.value];let t=[];d(),Z("isSelect",r.value.map((l,n)=>(e.includes(n)||t.push(n),n))),a.value=t},J=G({hasIcon:!0,iconType:"svg-icon",menuList:[{label:"置为当前",fn:()=>k(void 0,!0),disabled:y},{label:"新建图层",fn:()=>C()},{label:"删除",fn:()=>T(),disabled:()=>{if(y())return!1;let e=O(a.value);return e=typeof e=="number"?e:e[0],r.value[e]?.id===g.value},icon:M("youjianshanchu")},{label:"重命名",tips:"F2",fn:()=>x(m),disabled:y,icon:M("youjianzhongmingming")},{line:!0},{label:"全部选择",fn:()=>w()},{label:"全部清除",fn:()=>F()},{label:"反转选择",fn:()=>I()}]}),{createColor:P}=H(),V=e=>{Q("Mx_Color",{call:(t,l)=>{u("color",P({...W(t,l)}),a)},color:e})};U();const _=e=>{u("lineType",e,a)},b=X([{name:"新增图层",fun:()=>{C()}},{name:"删除图层",fun:()=>T()},{name:"置为当前",fun:()=>k()},{name:"关闭所有图层",fun:()=>{le(),b[3].name=L?"开启所有图层":"关闭所有图层"}}]);return{onClickLayer:h,onClickStopTD:D,onClickLayerName:K,resumeIndex:j,reverseSelection:I,selectColor:V,selectLineType:_,setIndex:o,onRightClickLayer:E,initLayerList:ee,setLayerList:()=>{te($())},list:r,bodyRightClickMenuOptions:J,isShiftKeyMultipleChoice:c,isCtrlKeyMultipleChoice:f,btnList:b,rootId:g}};export{le as d,ce as u};
