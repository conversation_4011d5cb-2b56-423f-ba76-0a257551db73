import request from '@/utils/request'

// 查询图纸列表
export function listDrawing(query) {
  return request({
    url: '/jsObjInsertingDraw/list',
    method: 'post',
    params: query
  })
}

// 查询图纸详细
export function getDrawing(id) {
  return request({
    url: '/jsObjInsertingDraw/getInfo/' + id,
    method: 'post'
  })
}

// 新增图纸
export function addDrawing(data) {
  return request({
    url: '/jsObjInsertingDraw/add',
    method: 'post',
    data,
    headers: {
      "Content-Type": "multipart/form-data",
    },
  })
}

// 修改图纸
export function updateDrawing(data) {
  return request({
    url: '/jsObjInsertingDraw/update',
    method: 'post',
    data,
    headers: {
      "Content-Type": "multipart/form-data",
    },
  })
}

// 删除图纸
export function delDrawing(id) {
  return request({
    url: '/jsObjInsertingDraw/delete/' + id,
    method: 'post'
  })
}

// 删除图纸
export function delBatch(id) {
  return request({
    url: '/jsObjInsertingDraw/delBatch',
    method: 'post',
    data: id
  })
}

// 下载
export function downloadFile(params){
    return request({
        url: '/jsObjInsertingDraw/convertToPdf',
        method: 'get',
        params: params,
        responseType: 'blob'
    })
}

export function test(data) {
  return request({
    url: '/jsObjInsertingDraw/test',
    method: 'post',
    data: data
  })
}

export function showTable() {
  return request({
    url: '/jsObjInsertingDraw/showTable',
    method: 'post'
  })
}


