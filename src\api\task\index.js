import request from '@/utils/request.js'
// 设计成果_规范管理
export function specificationTree(query) {
    return request({
        url: '/template/getSpecificationTree',
        method: 'post',
        params: query
    })
}
// 设计成果_规范管理(删除)
export function deleteTree(params) {
    return request({
        url: '/system/specification/delete',
        method: 'post',
        params
    })
}
// 在线设计页面侧边栏保存
export function saveFile(data){
    return request({
        url: '/template/saveFile',
        method: 'post',
        data: data
    })
}

// 下载
export function downloadFile(params){
    return request({
        url: '/template/convertToPdf',
        method: 'get',
        params: params,
        responseType: 'blob'
    })
}
// 下载DWG
export function downloadFileDWG(id){
    return request({
        url: '/system/specification/yulanMxweb/' + id,
        method: 'get',
        responseType: 'blob'
    })
}
//下载excel

export function downloadFileExcel(id){
    return request({
        url: '/system/specification/yulanExcel/' + id,
        method: 'get',
        responseType: 'blob'
    })
}

