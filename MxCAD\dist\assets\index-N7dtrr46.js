import{d as G,h as $,a0 as E,_ as H,$ as l,a1 as s,m as e,u as r,B as k,Q as u,V as n,H as B,I as L}from"./vue-DfH9C9Rx.js";import{M as J}from"./index-8X61wlK0.js";import{b as M,s as A,k as K,u as Q,a as i,M as N,c as V,e as W,_ as X}from"./index-D95UjFey.js";import{r as Y}from"./index-BH_Qf1-v.js";import{e as q,h as z,k as Z}from"./mxcad-CfPpL1Bn.js";import{E as o,J as ee,K as S,C as _,i as p,h as T,V as le,j as te}from"./vuetify-B_xYg4qv.js";import"./mxdraw-C_n_7lEs.js";import"./mapbox-gl-PN3pDJHq.js";import"./xlsx-Ck5MUZvf.js";const F=""+new URL("jingao-DVjn0eNy.png",import.meta.url).href,se=c=>{const b=M(!0,"Mx_ArrayDialog_is_array_rect"),h=M(5,"Mx_ArrayDialog_row"),g=M(5,"Mx_ArrayDialog_col"),C=M(200,"Mx_ArrayDialog_row_offset"),x=M(200,"Mx_ArrayDialog_col_offset"),v=M(0,"Mx_ArrayDialog_angle");let f=[];const w=G(0),U=async()=>(c(!1),f=await q.userSelect(A("458")),w.value=f.length,c(!0),f),D=async y=>{c(!1);const m=new z;m.clearLastInputPoint(),y&&m.setMessage(y);const j=await m.go();if(!j)return c(!0);m.setUserDraw((t,a)=>{a.drawLine(t.toVector3(),j?.toVector3())});const R=await m.go();return R?(c(!0),j.distanceTo(R)):c(!0)},O=async()=>{c(!1);const y=new Z;y.clearLastInputPoint(),y.setMessage(A("459"));const m=await y.go();typeof m=="number"&&(v.value=K(m,3)),c(!0)},I=Q();return{isArrayRect:b,row:h,col:g,rowOffset:C,colOffset:x,angle:v,selectCount:w,selectObject:U,getLength:D,getAngle:O,onConfirmClick:()=>{if(b.value){if(w.value<=0)return I.error(A("460")+"!");Y({iColNum:g.value,iRowNum:h.value,dAng:v.value,aryId:f,dColOffset:x.value,dRowOffset:C.value})}c(!1)}}},ae={class:"px-3"},oe={class:"d-flex justify-center align-center"},ne={class:"d-flex justify-center align-center"},ue={class:"px-2"},de={class:"w-100"},ie={class:"w-100 mt-3"},re={class:"w-50"},ce={class:"w-25"},fe={class:"w-25 ml-1"},me={class:"d-flex flex-column justify-center align-center h-100"},_e={class:"w-100"},pe={class:"w-50"},ve={class:"w-25"},ge={class:"d-flex flex-column algin-center justify-center ml-4"},ye={class:"mr-12"},Ve={class:"px-3"},we={class:"d-flex flex-column algin-center justify-center ml-4"},ke={class:"h-100 pl-2"},be={class:"d-flex algin-center ml-2 mb-1",style:{"line-height":"1.5rem"}},Ce={class:"mt-2"},xe=$({__name:"index",setup(c){const{isShow:b,showDialog:h}=W,{isArrayRect:g,row:C,col:x,rowOffset:v,colOffset:f,angle:w,selectCount:U,selectObject:D,getLength:O,getAngle:I,onConfirmClick:P}=se(h),y=async()=>{const t=await O(A("指定行间距"));t&&(v.value=t)},m=async()=>{const t=await O(A("指定列间距"));t&&(f.value=t)},j=async()=>{const t=await O(A("指定单位单元"));t&&(f.value=t,v.value=t)},R=[{name:"确定",fun:P,primary:!0},{name:"取消",fun:()=>h(!1)}];return(t,a)=>(E(),H(J,{title:t.t("290"),modelValue:r(b),"onUpdate:modelValue":a[6]||(a[6]=d=>k(b)?b.value=d:null),"max-width":"520",footerBtnList:R},{default:l(()=>[s("div",ae,[e(_,{class:"mt-1","align-stretch":""},{default:l(()=>[e(o,{cols:7,class:"h-100","align-self":"start"},{default:l(()=>[e(ee,{modelValue:r(g),"onUpdate:modelValue":a[0]||(a[0]=d=>k(g)?g.value=d:null),inline:""},{default:l(()=>[e(S,{value:!0,class:"mr-12"},{label:l(()=>[e(i,{"key-name":"R"},{default:l(()=>[u(n(t.t("189")),1)]),_:1})]),_:1}),e(S,{value:!1},{label:l(()=>[e(i,{"key-name":"P"},{default:l(()=>[u(n(t.t("190")),1)]),_:1})]),_:1})]),_:1},8,["modelValue"]),B(s("div",null,[e(_,null,{default:l(()=>[e(o,{cols:"6"},{default:l(()=>[e(p,{class:"mt-2 ml-2",type:"number",modelValue:r(C),"onUpdate:modelValue":a[1]||(a[1]=d=>k(C)?C.value=d:null)},{prepend:l(()=>[s("div",oe,[a[7]||(a[7]=s("div",{class:"box box-row mr-1"},[s("div",{class:"box-line"})],-1)),e(i,{"key-name":"W",colon:""},{default:l(()=>[u(n(t.t("191")),1)]),_:1})])]),_:1},8,["modelValue"])]),_:1}),e(o,{cols:"6"},{default:l(()=>[e(p,{class:"mt-2 ml-2",type:"number",modelValue:r(x),"onUpdate:modelValue":a[2]||(a[2]=d=>k(x)?x.value=d:null)},{prepend:l(()=>[s("div",ne,[a[8]||(a[8]=s("div",{class:"box box-col mr-1"},[s("div",{class:"box-line"})],-1)),e(i,{"key-name":"O",colon:""},{default:l(()=>[u(n(t.t("192")),1)]),_:1})])]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(N,{class:"mt-1",title:t.t("471")},{default:l(()=>[s("div",ue,[e(_,{class:"mt-2"},{default:l(()=>[e(o,{cols:"4"},{default:l(()=>[s("p",de,[e(i,{"key-name":"F",colon:""},{default:l(()=>[u(n(t.t("193")),1)]),_:1})]),s("p",ie,[e(i,{"key-name":"M",colon:""},{default:l(()=>[u(n(t.t("194")),1)]),_:1})])]),_:1}),e(o,{cols:"8",class:"d-flex"},{default:l(()=>[s("div",re,[e(p,{class:"",type:"number",modelValue:r(v),"onUpdate:modelValue":a[3]||(a[3]=d=>k(v)?v.value=d:null)},null,8,["modelValue"]),e(p,{class:"mt-3",type:"number",modelValue:r(f),"onUpdate:modelValue":a[4]||(a[4]=d=>k(f)?f.value=d:null)},null,8,["modelValue"])]),s("div",ce,[e(V,{class:"h-100",onClick:j})]),s("div",fe,[s("div",me,[e(V,{onClick:y}),e(V,{class:"mt-2",onClick:m})])])]),_:1})]),_:1}),e(_,null,{default:l(()=>[e(o,{cols:"4"},{default:l(()=>[s("p",_e,[e(i,{"key-name":"A",colon:""},{default:l(()=>[u(n(t.t("195")),1)]),_:1})])]),_:1}),e(o,{cols:"8",class:"d-flex"},{default:l(()=>[s("div",pe,[e(p,{class:"mt-1",type:"number",step:"0.01",modelValue:r(w),"onUpdate:modelValue":a[5]||(a[5]=d=>k(w)?w.value=d:null)},null,8,["modelValue"])]),s("div",ve,[e(V,{class:"mt-1",onClick:r(I)},null,8,["onClick"])]),a[9]||(a[9]=s("div",{class:"w-25 ml-1"},null,-1))]),_:1})]),_:1}),e(_,{class:"mt-2"},{default:l(()=>[e(o,{cols:"3"},{default:l(()=>[s("div",ge,[e(T,{src:F,alt:t.t("196"),width:"24px"},null,8,["alt"]),u(" "+n(t.t("196")),1)])]),_:1}),e(o,{cols:"9"},{default:l(()=>[u(n(t.t("197")),1)]),_:1})]),_:1})])]),_:1},8,["title"])],512),[[L,r(g)]]),B(s("div",null,[e(_,{class:"d-flex mx-3 mt-1"},{default:l(()=>[s("p",ye,n(t.t("198"))+":",1),e(p,null,{prepend:l(()=>a[10]||(a[10]=[s("span",{class:""},"X:",-1)])),_:1}),e(p,{class:"ml-1"},{prepend:l(()=>a[11]||(a[11]=[s("span",{class:""},"Y:",-1)])),_:1}),e(V)]),_:1}),e(N,{title:t.t("472")},{default:l(()=>[s("div",Ve,[e(i,{"key-name":"M",colon:""},{default:l(()=>[u(n(t.t("199")),1)]),_:1}),e(le,{"bg-color":"grey-lighten-2",class:"",items:[]}),e(_,null,{default:l(()=>[e(o,{cols:"6"},{default:l(()=>[e(i,{"key-name":"I",colon:""},{default:l(()=>[u(n(t.t("200")),1)]),_:1})]),_:1}),e(o,{cols:"3"},{default:l(()=>[e(p,{class:"mt-1"})]),_:1}),e(o,{cols:"3"})]),_:1}),e(_,null,{default:l(()=>[e(o,{cols:"6"},{default:l(()=>[e(i,{"key-name":"F",colon:""},{default:l(()=>[u(n(t.t("201")),1)]),_:1})]),_:1}),e(o,{cols:"3"},{default:l(()=>[e(p,{class:"mt-1"})]),_:1}),e(o,{cols:"3"},{default:l(()=>[e(V)]),_:1})]),_:1}),e(_,null,{default:l(()=>[e(o,{cols:"6"},{default:l(()=>[e(i,{"key-name":"B",colon:""},{default:l(()=>[u(n(t.t("202")),1)]),_:1})]),_:1}),e(o,{cols:"3"},{default:l(()=>[e(p,{class:"mt-1",disabled:""})]),_:1}),e(o,{cols:"3"},{default:l(()=>[e(V,{disabled:""})]),_:1})]),_:1}),e(_,{class:"mt-2"},{default:l(()=>[e(o,{cols:"3"},{default:l(()=>[s("div",we,[e(T,{src:F,alt:t.t("196"),width:"24px"},null,8,["alt"]),u(" "+n(t.t("196")),1)])]),_:1}),e(o,{cols:"9"},{default:l(()=>[u(n(t.t("203")),1)]),_:1})]),_:1})])]),_:1},8,["title"]),e(te,{class:"mt-2"},{label:l(()=>[e(i,{"key-name":"T"},{default:l(()=>[u(n(t.t("204")),1)]),_:1})]),_:1})],512),[[L,!r(g)]])]),_:1}),e(o,{cols:5,"align-self":"start"},{default:l(()=>[s("div",ke,[s("div",be,[e(V,{onClick:r(D)},null,8,["onClick"]),e(i,{"key-name":"S"},{default:l(()=>[u(n(t.t("205")),1)]),_:1})]),s("p",Ce,n(t.t("206")+r(U)+t.t("207")+t.t("208")),1),a[12]||(a[12]=s("div",{class:"obj-box"},null,-1))])]),_:1})]),_:1})])]),_:1},8,["title","modelValue"]))}}),Pe=X(xe,[["__scopeId","data-v-cc32e175"]]);export{Pe as default};
