import { IMcDbDwgFiler, McDb, McDbCircle, McDbCustomEntity, McDbEntity, McDbHatch, McDbLine, McDbPolyline, McDbText, McGePoint3d, McGePoint3dArray, McGeVector3d, MxCADWorldDraw, MxCpp } from "mxcad";
import { MxFun } from "mxdraw";

// 形位公差自定义实体
class MxDbGeometricTolerances extends McDbCustomEntity {
    // 定义MxDbGeometricTolerances内部对象 
    // 公差值数组
    private toleranceArr: string[] = ['0.025'];
    // 基准值数组
    private referenceArr: string[] = [];
    // 符号(roundness:圆度)
    private symbol: string = "roundness";
    // 公差框长
    private boxHeight: number = MxFun.screenCoordLong2Doc(10);
    // 标注起点
    private startPoint: McGePoint3d = new McGePoint3d();
    // 标注转折点
    private turningPoint: McGePoint3d = new McGePoint3d();
    // 标注终点
    private endPoint: McGePoint3d = new McGePoint3d();
    // 构造函数
    constructor(imp?: any) {
        super(imp);
    }
    // 创建函数
    public create(imp: any) {
        return new MxDbGeometricTolerances(imp)
    }
    // 获取类名
    public getTypeName(): string {
        return "MxDbGeometricTolerances";
    }
    //设置或获取基准值
    public set referenceValue(val: string[]) {
        this.referenceArr = val;
    }
    public get referenceValue(): string[] {
        return this.referenceArr;
    }
    //设置或获取公差值
    public set toleranceValue(val: string[]) {
        this.toleranceArr = val;
    }
    public get toleranceValue(): string[] {
        return this.toleranceArr;
    }
    //设置或获取图标类型
    public set symbolType(val: string) {
        this.symbol = val;
    }
    public get symbolType(): string {
        return this.symbol;
    }
    // 读取自定义实体数据
    public dwgInFields(filter: IMcDbDwgFiler): boolean {
        const _referenceArr = filter.readString("referenceArr").val;
        const _toleranceArr = filter.readString("toleranceArr").val;
        this.symbol = filter.readString("symbol").val;
        this.startPoint = filter.readPoint("startPoint").val;
        this.turningPoint = filter.readPoint("turningPoint").val;
        this.endPoint = filter.readPoint("endPoint").val;
        this.referenceArr = _referenceArr.split(",");
        this.toleranceArr = _toleranceArr.split(",");
        return true;
    }
    // 写入自定义实体数据
    public dwgOutFields(filter: IMcDbDwgFiler): boolean {
        const _referenceArr = this.referenceArr.toString();
        const _toleranceArr = this.toleranceArr.toString();
        filter.writeString("referenceArr", _referenceArr);
        filter.writeString("toleranceArr", _toleranceArr);
        filter.writeString("symbol", this.symbol);
        filter.writePoint("startPoint", this.startPoint);
        filter.writePoint("turningPoint", this.turningPoint);
        filter.writePoint("endPoint", this.endPoint);
        return true;
    }

    // 移动自定义对象的夹点
    public moveGripPointsAt(iIndex: number, dXOffset: number, dYOffset: number, dZOffset: number) {
        this.assertWrite();
        if (iIndex === 0) {
            this.startPoint.x += dXOffset;
            this.startPoint.y += dYOffset;
            this.startPoint.z += dZOffset;
        } else if (iIndex === 1) {
            this.turningPoint.x += dXOffset;
            this.turningPoint.y += dYOffset;
            this.turningPoint.z += dZOffset;
            this.endPoint.x += dXOffset;
            this.endPoint.y += dYOffset;
            this.endPoint.z += dZOffset;
        } else if (iIndex === 2) {
            this.endPoint.x += dXOffset;
        }
    };
    // 获取自定义对象的夹点
    public getGripPoints(): McGePoint3dArray {
        let ret = new McGePoint3dArray()
        ret.append(this.startPoint);
        ret.append(this.turningPoint);
        ret.append(this.endPoint);
        return ret;
    };

    // 绘制形位公差框
    private drawBox(): { entityArr: McDbEntity[], lastPt: McGePoint3d } {
        let lastPt = new McGePoint3d();
        let endPt, turnPt;
        if (this.endPoint.x > this.turningPoint.x) {
            endPt = this.endPoint.clone();
            turnPt = this.turningPoint.clone();
        } else {
            endPt = this.turningPoint.clone();
            turnPt = this.endPoint.clone();
        }
        const entityArr = [];
        const vec = endPt.sub(turnPt).normalize();
        const pt1 = endPt.clone().addvec(vec.clone().perpVector().mult(this.boxHeight / 2));
        const pt2 = endPt.clone().subvec(vec.clone().perpVector().mult(this.boxHeight / 2));
        const line1 = new McDbLine(pt1, pt2);
        entityArr.push(line1)

        const pt3 = pt1.clone().addvec(vec.clone().mult(this.boxHeight));
        const pt4 = pt2.clone().addvec(vec.clone().mult(this.boxHeight));
        const line2 = new McDbLine(pt3, pt4);
        entityArr.push(line2);
        lastPt = endPt.clone().addvec(vec.clone().mult(this.boxHeight));
        /**
         * 绘制符号
         * 可单独抽离为一个方法
         */
        if (this.symbol === "roundness") {
            // 绘制圆度符号
            const center = endPt.clone().addvec(vec.clone().mult(this.boxHeight / 2));
            const radius = this.boxHeight * (3 / 4) / 2;
            const circle = new McDbCircle(center.x, center.y, center.z, radius);
            entityArr.push(circle);
        }else if (this.symbol === "line"){
            // 绘制直线符号
            const center = endPt.clone().addvec(vec.clone().mult(this.boxHeight / 2));
            const _vec = vec.clone().mult(this.boxHeight * (3 / 4) / 2);
            const line = new McDbLine(center.clone().addvec(_vec), center.clone().subvec(_vec));
            entityArr.push(line);
        };
        
        // 绘制公差
        if (this.toleranceArr.length) {
            const { arr, lastPt: lastpos } = this.drawBoxInfo(this.toleranceArr, lastPt, vec);
            entityArr.push(...arr);
            lastPt = lastpos;
        }
        // 绘制基准
        if (this.referenceArr.length) {
            const { arr, lastPt: lastpos } = this.drawBoxInfo(this.referenceArr, lastPt, vec);
            entityArr.push(...arr);
            lastPt = lastpos;
        };
        if (!this.toleranceArr.length && !this.referenceArr.length) {
            const line5 = new McDbLine(pt1, pt3);
            const line6 = new McDbLine(pt2, pt4);
            entityArr.push(...[line5, line6]);
        } else {
            const lastPt1 = lastPt.clone().addvec(vec.clone().perpVector().mult(this.boxHeight / 2));
            const lastPt2 = lastPt.clone().subvec(vec.clone().perpVector().mult(this.boxHeight / 2));
            const line5 = new McDbLine(pt1, lastPt1);
            const line6 = new McDbLine(pt2, lastPt2);
            entityArr.push(...[line5, line6]);
        };
        return { entityArr, lastPt }
    }
    private textInfo(val: string, height: number): { text: McDbText, length: number } {
        const text = new McDbText();
        text.height = height;
        text.textString = val;
        text.horizontalMode = McDb.TextHorzMode.kTextLeft;
        text.position = text.alignmentPoint = this.endPoint;
        const mxcad = MxCpp.getCurrentMxCAD();
        const id = mxcad.drawEntity(text);
        id.erase();
        const { minPt, maxPt } = id.getMcDbEntity().getBoundingBox();
        const length = maxPt.x - minPt.x;
        return { text, length }
    }
    private drawBoxInfo(arr: string[], lastPt: McGePoint3d, vec: McGeVector3d): { arr: McDbEntity[], lastPt: McGePoint3d } {
        const textHeight = this.boxHeight * (3 / 4);
        const entityArr = [];
        arr.forEach((val) => {
            const { text, length } = this.textInfo(val, textHeight);
            const _boxlength = length + this.boxHeight * (1 / 4) < this.boxHeight ? this.boxHeight : length + this.boxHeight * (1 / 4);
            const pos = lastPt.clone().addvec(vec.clone().mult(_boxlength / 2 - length / 2));
            text.position = text.alignmentPoint = pos.subvec(vec.clone().perpVector().mult(textHeight * (2 / 5)));
            lastPt.addvec(vec.clone().mult(_boxlength));
            const _pt1 = lastPt.clone().clone().addvec(vec.clone().perpVector().mult(this.boxHeight / 2));
            const _pt2 = lastPt.clone().clone().subvec(vec.clone().perpVector().mult(this.boxHeight / 2));
            const line = new McDbLine(_pt1, _pt2);
            entityArr.push(...[text, line])
        });
        return { arr: entityArr, lastPt }
    }
    // 画箭头
    private drawArrow(): McDbEntity {
        const pt1 = this.startPoint;
        const pt2 = this.turningPoint;
        const arrowLength = this.boxHeight / 2
        const vec = pt2.sub(pt1).normalize().mult(arrowLength);
        const pt = pt1.clone().addvec(vec);
        const _vec = vec.clone().rotateBy(Math.PI / 2).normalize().mult(arrowLength / 8);
        const pt3 = pt.clone().addvec(_vec);
        const pt4 = pt.clone().subvec(_vec);
        const solid = new McDbHatch();
        solid.appendLoop(new McGePoint3dArray([pt1, pt3, pt4]));
        return solid
    }
    // 绘制实体
    public worldDraw(draw: MxCADWorldDraw): void {
        // 绘制引线
        const pl = new McDbPolyline();
        pl.addVertexAt(this.startPoint);
        pl.addVertexAt(this.turningPoint);
        pl.addVertexAt(this.endPoint);
        draw.drawEntity(pl);
        // 绘制形位公差框
        const {entityArr, lastPt} = this.drawBox();
        entityArr.forEach(entity => {
            if (this.endPoint.x <= this.turningPoint.x) {
                entity.move(lastPt, this.endPoint);
            }
            draw.drawEntity(entity);
        })
        // 绘制箭头
        const arrow = this.drawArrow();
        draw.drawEntity(arrow);
    }
    // 设置标注起点
    public setStartPoint(pt: McGePoint3d) {
        this.assertWrite();
        this.startPoint = pt.clone();
    }
    // 获取标注起点
    public getStartPoint() {
        return this.startPoint;
    }
    // 设置标注转折点
    public setTurningPoint(pt: McGePoint3d) {
        this.assertWrite();
        this.turningPoint = pt.clone();
    }
    // 获取标注转折点
    public getTurningPoint() {
        return this.turningPoint;
    }
    // 设置标注终点
    public setEndPoint(pt: McGePoint3d) {
        this.assertWrite();
        this.endPoint = new McGePoint3d(pt.x, this.turningPoint.y);
    }
    // 获取标注终点
    public getEndPoint() {
        return this.endPoint;
    }
}

function Mx_drawGeometricTolerance() {
    const mxcad = MxCpp.getCurrentMxCAD();
    mxcad.newFile();
    const gt1 = new MxDbGeometricTolerances();
    gt1.setStartPoint(new McGePoint3d());
    gt1.setTurningPoint(new McGePoint3d(500, 500));
    gt1.setEndPoint(new McGePoint3d(800, 500));
    gt1.symbolType = "roundness"
    gt1.toleranceValue = ['0.025'];
    gt1.referenceValue = ['A', 'B'];
    mxcad.drawEntity(gt1);
    mxcad.zoomAll();
}

export function init() {
    new MxDbGeometricTolerances().rxInit();
    MxFun.addCommand("Mx_Test_DrawGT", Mx_drawGeometricTolerance);
}