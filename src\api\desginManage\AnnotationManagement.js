import request from '@/utils/request'

// 标注管理-标注设置管理-获取图元类型列表接口
export function getByLegendTypeKeyList(data) {
    return request({
        url: '/system/tagsetting/getByLegendTypeKeyList',
        method: 'post',
        data: data
    })
}

// 标注管理-标注设置管理-根据 LegendTypeKey 获取对应的 Module 数据
export function getLegendtypekey(data) {
    return request({
        url: '/system/tagsetting/getLegendtypekey',
        method: 'post',
        data: data
    })
}

// 标注管理-标注设置管理-修改字体颜色和字体样式
export function updateByTypeKey(data) {
    return request({
        url: '/system/tagsetting/updateByTypeKey',
        method: 'post',
        data: data
    })
}

// 标注管理-标注图例-获取标注图例信息
export function getLegendByTaskInfoId(params) {
    return request({
        url: '/system/tagsetting/getLegendByTaskInfoId',
        method: 'get',
        params: params
    })
}

// 标注管理-标注图例-获取所有设备类型
export function getTagManagementData(params) {
    return request({
        url: '/system/tagsetting/getTagManagementData',
        method: 'get',
        params: params
    })
}
