import{Q as b,ar as D}from"./index-CzBriCFR.js";import{M as I}from"./index-itnQ6avM.js";import{b as v}from"./vuetify-BqCp6y38.js";import{h as V,_ as t,a3 as d,a4 as C,a0 as y,u as a,a9 as m,$ as g,V as h,B as L,d as B,a5 as M,F as S}from"./vue-Cj9QYd7Z.js";import"./mxcad-DrgW2waE.js";import"./mxdraw-BQ5HhRCY.js";import"./handsontable-Ch5RdAT_.js";import"./mapbox-gl-DQAr7S0z.js";const U={class:"my-2 d-flex align-center"},F={key:0,style:{"white-space":"pre"}},N={key:1,style:{"white-space":"pre"},class:""},$=V({__name:"MessageBoxDialog",props:{options:{}},setup(o,{expose:i}){const l=b(!1),{isShow:r,showDialog:c,confirm:u,cancel:f}=l,s=Object.assign(D,{question:{icon:"class:iconfont info",color:"rgb(47,144,207)"},none:void 0});let p=[{name:"确定",fun:()=>{},primary:!0},{name:"取消",fun:()=>c(!1)}];const k={escape:()=>{if(o.options.cancelId){const e=p[o.options.cancelId];e.fun.apply(e)}else c(!1),f()},enter:()=>{if(o.options.defaultId){const e=p[o.options.defaultId];e.fun.apply(e)}}};return o.options.buttons&&(p=o.options.buttons.map((e,n)=>({name:e,fun:()=>{u(n),c(!1)},primary:o.options.defaultId===n}))),i({useDialog:l}),(e,n)=>(t(),d(I,{title:e.options.title,modelValue:a(r),"onUpdate:modelValue":n[0]||(n[0]=w=>L(r)?r.value=w:null),footerBtnList:a(p),keys:k,"max-width":"620",width:"auto"},{default:C(()=>[y("div",U,[e.options.type&&a(s)[e.options.type]?(t(),d(v,{key:0,size:36,icon:a(s)[e.options.type]?.icon,class:"mr-2",color:a(s)[e.options.type]?.color},null,8,["icon","color"])):m("",!0),y("div",null,[e.options.message?(t(),g("p",F,h(e.options.message),1)):m("",!0),e.options.detail?(t(),g("p",N,h(e.options.detail),1)):m("",!0)])])]),_:1},8,["title","modelValue","footerBtnList"]))}}),G=V({__name:"index",setup(o){const i=B([]),l=B([]);return(r,c)=>(t(!0),g(S,null,M(i.value,(u,f)=>(t(),d($,{ref_for:!0,ref_key:"dialogs",ref:l,"onUpdate:modelValue":s=>!s&&i.value.splice(f,1),options:u},null,8,["onUpdate:modelValue","options"]))),256))}});export{G as default};
