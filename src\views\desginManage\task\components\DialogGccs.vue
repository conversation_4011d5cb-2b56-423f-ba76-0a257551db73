<template>
    <div class="app-container" style="padding: 0;">
      <div style="margin-bottom: 8px">
        <el-button type="primary" @click="saveTemplateHandle"
          >另存为模板</el-button
        >
        <el-button type="primary" @click="importTemplateHandle"
          >导入模板</el-button
        >
      </div>
      <div style="overflow: auto">
        <el-collapse class="elCollapse" v-model="activeNames">
          <el-collapse-item title="气象区设置" name="1">
            <div
              style="
                display: flex;
                align-items: center;
                justify-content: space-between;
              "
            >
              <div style="display: flex; align-items: center">
                <span>工程所属气象区: </span>
                <el-select
                  style="width: 100px"
                  size="small"
                  v-model="qxqid"
                  placeholder="请选择"
                  @change="qxqChange"
                >
                  <el-option
                    v-for="item in qxqList"
                    :key="item.objId"
                    :label="item.climateZoneName"
                    :value="item.objId"
                  >
                  </el-option>
                </el-select>
              </div>
              <div style="display: flex; align-items: center">
                <div>冰的密度 0.9x10^3</div>
                <el-popover
                  placement="right"
                  :width="400"
                  :visible="visible.qxqVisible"
                >
                  <template #reference>
                    <el-button
                      style="margin: 10px"
                      type="primary"
                      size="small"
                      round
                      @click="addQxq"
                      >调整</el-button
                    >
                  </template>
                  <div style="font-weight: 700; margin-bottom: 5px">
                    新增气象区
                  </div>
                  <div style="display: flex; align-items: center">
                    <el-select
                      size="small"
                      style="width: 200px"
                      v-model="qxqForm.select"
                      placeholder="请选择"
                      @change="qxqFormSelectChange"
                    >
                      <el-option
                        v-for="item in qxqForm.options"
                        :key="item.objId"
                        :label="item.climateZoneName"
                        :value="item.objId"
                      />
                    </el-select>
                    <el-input
                      style="width: 200px"
                      v-model="qxqForm.input"
                      size="small"
                    />
                  </div>
                  <el-table :data="qxqData" border stripe>
                    <el-table-column
                      prop="climateConditionName"
                      label="工况"
                    ></el-table-column>
                    <el-table-column prop="climateTemperture" label="温度(℃)">
                      <template #default="scope">
                        <el-input
                          type="number"
                          v-if="
                            scope.row.climateConditionName !== '覆冰厚度' &&
                            scope.row.climateConditionName !== '覆冰密度'
                          "
                          v-model="scope.row.climateTemperture"
                          size="small"
                        />
                      </template>
                    </el-table-column>
                    <el-table-column prop="fs" label="风速(m/s)">
                      <template #default="scope">
                        <el-input
                          type="number"
                          v-if="
                            scope.row.climateConditionName === '覆冰工况' ||
                            scope.row.climateConditionName === '最大风速' ||
                            scope.row.climateConditionName === '安装工况' ||
                            scope.row.climateConditionName === '外过电压' ||
                            scope.row.climateConditionName === '内过电压'
                          "
                          v-model="scope.row.climateWindSpeed"
                          size="small"
                        />
                      </template>
                    </el-table-column>
                    <el-table-column prop="bh" label="冰厚/覆冰(mm)">
                      <template #default="scope">
                        <el-input
                          type="number"
                          v-if="
                            scope.row.climateConditionName === '覆冰工况' ||
                            scope.row.climateConditionName === '覆冰厚度' ||
                            scope.row.climateConditionName === '覆冰密度'
                          "
                          v-model="scope.row.climateIce"
                          size="small"
                        />
                      </template>
                    </el-table-column>
                  </el-table>
                  <div
                    style="display: flex; justify-content: end; margin-top: 20px"
                  >
                    <el-button size="small" @click="visible.qxqVisible = false"
                      >取 消</el-button
                    >
                    <el-button size="small" type="primary" @click="qxqDialogAdd"
                      >确 定</el-button
                    >
                  </div>
                </el-popover>
              </div>
            </div>
            <el-table
              v-loading="false"
              stripe
              border
              :data="qxList"
              row-key="menuId"
              :default-expand-all="false"
              :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
            >
              <el-table-column
                prop="climateConditionName"
                label="工况"
              ></el-table-column>
              <el-table-column
                prop="climateTemperture"
                label="温度(℃)"
              ></el-table-column>
              <el-table-column
                prop="climateWindSpeed"
                label="风速(m/s)"
              ></el-table-column>
              <el-table-column
                prop="climateIce"
                label="冰厚/覆冰(mm)"
              ></el-table-column>
            </el-table>
          </el-collapse-item>
        </el-collapse>
        <el-descriptions
          style="margin-top: 10px"
          border
          :column="2"
          label-width="160px"
        >
          <template #title>
            <div style="font-size: 14px; color: #303133; font-weight: normal">
              地理参数
            </div>
          </template>
          <el-descriptions-item label="海拔高度">
            <el-select
              style="width: 200px"
              size="small"
              v-model="qtForm.Altitude"
              placeholder="请选择"
            >
              <el-option
                v-for="item in gccsList.Altitude"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-descriptions-item>
          <el-descriptions-item label="污秽等级">
            <el-select
              size="small"
              style="width: 200px"
              v-model="qtForm.FilthLevels"
              multiple
              placeholder="请选择"
            >
              <el-option
                v-for="item in gccsList.FilthLevel"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-descriptions-item>
          <el-descriptions-item label="架空地形选择">
            <el-popover
              placement="right"
              :width="400"
              :visible="visible.jkdxVisible"
            >
              <template #reference>
                <el-input
                  readonly
                  v-model="dlcsForm.jkdx"
                  style="width: 200px"
                  size="small"
                  placeholder="请选择"
                  @click="visible.jkdxVisible = true"
                />
              </template>
              <div style="font-weight: 700; margin-bottom: 5px">架空地形选择</div>
              <el-table
                :data="gccsList.TerrainJk"
                border
                stripe
                :row-key="(row) => row.value"
              >
                <el-table-column property="value" label="地形" />
                <el-table-column property="bl" label="比例">
                  <template #default="scope">
                    <el-input type="number" v-model="scope.row.bl" size="small" />
                  </template>
                </el-table-column>
              </el-table>
              <div style="display: flex; justify-content: end; margin-top: 20px">
                <el-button size="small" @click="visible.jkdxVisible = false"
                  >取 消</el-button
                >
                <el-button
                  size="small"
                  type="primary"
                  @click="
                    popConfBtn('架空地形', 'jkdx', 'jkdxVisible', 'TerrainJk')
                  "
                  >确 定
                </el-button>
              </div>
            </el-popover>
          </el-descriptions-item>
          <el-descriptions-item label="电缆地形选择">
            <el-popover
              placement="right"
              :width="400"
              :visible="visible.dldxVisible"
            >
              <template #reference>
                <el-input
                  readonly
                  v-model="dlcsForm.dldx"
                  style="width: 200px"
                  size="small"
                  placeholder="请选择"
                  @click="visible.dldxVisible = true"
                />
              </template>
              <div style="font-weight: 700; margin-bottom: 5px">电缆地形选择</div>
              <el-table :data="gccsList.TerrainDx" border stripe>
                <el-table-column property="value" label="地形" />
                <el-table-column property="bl" label="比例">
                  <template #default="scope">
                    <el-input v-model="scope.row.bl" size="small" />
                  </template>
                </el-table-column>
              </el-table>
              <div style="display: flex; justify-content: end; margin-top: 20px">
                <el-button size="small" @click="visible.dldxVisible = false"
                  >取 消</el-button
                >
                <el-button
                  size="small"
                  type="primary"
                  @click="
                    popConfBtn('电缆地形', 'dldx', 'dldxVisible', 'TerrainDx')
                  "
                  >确 定
                </el-button>
              </div>
            </el-popover>
          </el-descriptions-item>
          <el-descriptions-item label="地质选择">
            <el-popover
              placement="right"
              :width="400"
              :visible="visible.dzVisible"
            >
              <template #reference>
                <el-input
                  readonly
                  v-model="dlcsForm.dz"
                  style="width: 200px"
                  size="small"
                  placeholder="请选择"
                  @click="visible.dzVisible = true"
                />
              </template>
              <div style="font-weight: 700; margin-bottom: 5px">地质选择</div>
              <el-table :data="gccsList.Geological" border stripe>
                <el-table-column property="value" label="地形" />
                <el-table-column property="bl" label="比例">
                  <template #default="scope">
                    <el-input v-model="scope.row.bl" size="small" />
                  </template>
                </el-table-column>
              </el-table>
              <div style="display: flex; justify-content: end; margin-top: 20px">
                <el-button size="small" @click="visible.dzVisible = false"
                  >取 消</el-button
                >
                <el-button
                  size="small"
                  type="primary"
                  @click="popConfBtn('地质选择', 'dz', 'dzVisible', 'Geological')"
                  >确 定
                </el-button>
              </div>
            </el-popover>
          </el-descriptions-item>
        </el-descriptions>
        <el-descriptions
          style="margin-top: 10px"
          border
          :column="2"
          label-width="160px"
        >
          <template #title>
            <div style="font-size: 14px; color: #303133; font-weight: normal">
              其他参数
            </div>
          </template>
          <el-descriptions-item label="架空运距设置(千米)">
            <el-popover
              placement="right"
              :width="400"
              :visible="visible.jkyjVisible"
            >
              <template #reference>
                <el-input
                  readonly
                  v-model="dlcsForm.jkyj"
                  style="width: 200px"
                  size="small"
                  placeholder="请选择"
                  @click="visible.jkyjVisible = true"
                />
              </template>
              <div style="font-weight: 700; margin-bottom: 5px">架空运距设置</div>
              <el-table :data="gccsList.HaulDistanceJk" border stripe>
                <el-table-column property="value" label="运距类型" />
                <el-table-column label="运距长度">
                  <template #default="scope">
                    <el-input v-model="scope.row.bl" size="small" />
                  </template>
                </el-table-column>
              </el-table>
              <div style="display: flex; justify-content: end; margin-top: 20px">
                <el-button size="small" @click="visible.jkyjVisible = false"
                  >取 消</el-button
                >
                <el-button
                  size="small"
                  type="primary"
                  @click="
                    popConfBtn(
                      '架空运距设置',
                      'jkyj',
                      'jkyjVisible',
                      'HaulDistanceJk'
                    )
                  "
                  >确 定
                </el-button>
              </div>
            </el-popover>
          </el-descriptions-item>
          <el-descriptions-item label="电缆运距设置(千米)">
            <el-popover
              placement="right"
              :width="400"
              :visible="visible.dlyjVisible"
            >
              <template #reference>
                <el-input
                  readonly
                  v-model="dlcsForm.dlyj"
                  style="width: 200px"
                  size="small"
                  placeholder="请选择"
                  @click="visible.dlyjVisible = true"
                />
              </template>
              <div style="font-weight: 700; margin-bottom: 5px">电缆运距设置</div>
              <el-table :data="gccsList.HaulDistanceDl" border stripe>
                <el-table-column property="value" label="运距类型" />
                <el-table-column property="bl" label="运距长度">
                  <template #default="scope">
                    <el-input v-model="scope.row.bl" size="small" />
                  </template>
                </el-table-column>
              </el-table>
              <div style="display: flex; justify-content: end; margin-top: 20px">
                <el-button size="small" @click="visible.dlyjVisible = false"
                  >取 消</el-button
                >
                <el-button
                  size="small"
                  type="primary"
                  @click="
                    popConfBtn(
                      '电缆运距设置',
                      'dlyj',
                      'dlyjVisible',
                      'HaulDistanceDl'
                    )
                  "
                  >确 定
                </el-button>
              </div>
            </el-popover>
          </el-descriptions-item>
          <el-descriptions-item label="供电区域">
            <el-select
              style="width: 200px"
              size="small"
              v-model="qtForm.PowerLoad"
              placeholder="请选择"
            >
              <el-option
                v-for="item in gccsList.PowerLoad"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-descriptions-item>
          <el-descriptions-item label="防雷等级">
            <el-select
              style="width: 200px"
              size="small"
              v-model="qtForm.AntiThunderLevel"
              placeholder="请选择"
            >
              <el-option
                v-for="item in gccsList.AntiThunderLevel"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-descriptions-item>
        </el-descriptions>
        <div class="container-footer">
          <el-button @click="cancelDialog">取 消</el-button>
          <el-button type="primary" @click="submitFormDialog">确 定</el-button>
        </div>
      </div>
      <save-template
        v-model:visible="saveTemplateVisible"
        :id="taskId"
        :params="getParams"
      />
      <import-template
        v-model:visible="importTemplateVisible"
        @updateParams="getTemplateData"
      />
    </div>
  </template>
    
    <script name="EngineeringParameterTemplate" setup>
  import {
    getInfoById,
    getDicts,
    addInfo,
    getModuleByTypeKeys,
    getProjectMaterialsByLX,
    getProjectMaterials,
    addEngineeringParame,
    getEngineeringParameter,
    getTemplateDataByType,
  } from "@/api/desginManage/GccsDialog.js";
  import {
    getEngineeringSetting,
    engineeringSettingSetUp,
  } from "@/api/insertSag/index.js";
  
  import { ElMessageBox } from "element-plus";
  import ImportTemplate from "@/views/desginManage/task/components/import-template.vue";
  import SaveTemplate from "@/views/desginManage/task/components/save-template.vue";
  
  const activeNames = ref("1");
  const activeNameGccs = ref("first");
  const gcssqxqValue = ref("");
  const qxqid = ref("");
  const gccsList = ref({
    TerrainJk: [],
    TerrainDx: [],
    Geological: [],
    HaulDistanceJk: [],
    HaulDistanceDl: [],
    AntiThunderLevel: [],
    PowerLoad: [],
    FilthLevel: [],
    Altitude: [],
  });
  const qtForm = ref({
    Altitude: "", // 海拔高度
    FilthLevels: "", // 污秽等级
    PowerLoad: "", // 供电区域
    AntiThunderLevel: "", // 防雷等级
  });
  const qxqList = ref([]);
  const addForm = ref({
    YuDu: "", // 裕度(系数)
    WallHolderDistance: "", // 沿墙支架距离
    PoleNumber: "", // 架空-杆号牌(杆用)
    PoleTTNumber: "", // 架空-杆号牌(塔用)
    QuNiaoDevice: "", // 架空-驱鸟器
    QuNiaoQiBiLi: "", // 架空-驱鸟器间距
    QuNiaoWindmills: "", // 架空-驱鸟器风车
    FangNiaoFCBiLi: "", // 架空-驱鸟器风车间距
    FangLeiMokuai: "", // 架空-防雷方案
    FangLeiBiLi: "", // 架空-防雷间距
    CementProductIsMatching: false, // 架空-底卡盘是否配置
    CableYuDu: "", // 电缆-裕度(系数)
    ZhongJianTouYuXian: "", // 电缆-中间余线
    txtShangGanYuXian: "", // 电缆-上杆余线(米, 不含杆高)
    ChuXianYuXian: "", // 电缆-土建站房余线(米)
    XiangBianYuXian: "", // 电缆-箱式站余线(米)
    DianLanJingYuXian: "", // 电缆-电缆井余线(米)
    WallSpacing: "", // 其他-电缆墙间距(米)
    PothookSpacing: "", // 其他-电缆挂钩间距(米)
    DianLanGuanZhen: "", // 其他-电缆管枕(埋管)
    DianLanGuanZhenSpacing: "", // 其他-电缆管枕间距(米/个)
    JingShiPai: "", // 其他-警示牌(电缆沟)
    JingShiPaiJianJu: "", // 其他-警示牌间距(米/个)
    BiaoZhiGan: "", // 其他-标志桩(全部类型通道)
    BiaoZhiGanJianJu: "", // 其他-标志桩间距(米/个)
    GaiBan: "", // 其他-盖板(电缆沟)
    GaiBanLength: "", // 其他-盖板(电缆沟)-长
    GaiBanHeight: "", // 其他-盖板(电缆沟)-高
  });
  const allOptions = ref({
    ghpGyOptions: [],
    ghpTyOptions: [],
    qnqOptions: [],
    qnqFcOptions: [],
    flFaOptions: [],
    dlgzOptions: [],
    jspOptions: [],
    bzzOptions: [],
    gbOptions: [],
  });
  const qxList = ref([]);
  const qxqForm = ref({
    select: "",
    input: "",
    options: [],
  });
  const visible = ref({
    jkdxVisible: false,
    dldxVisible: false,
    dzVisible: false,
    jkyjVisible: false,
    dlyjVisible: false,
    qxqVisible: false,
  });
  const dlcsForm = ref({
    hbgd: "",
    whdj: [],
    jkdx: "",
    dldx: "",
    dz: "",
    jkyj: "",
    dlyj: "",
  });
  const qxqData = ref([
    {
      climateConditionName: "最高温度",
      climateTemperture: "",
      climateWindSpeed: "",
      climateIce: "",
    },
    {
      climateConditionName: "最低温度",
      climateTemperture: "",
      climateWindSpeed: "",
      climateIce: "",
    },
    {
      climateConditionName: "平均气温",
      climateTemperture: "",
      climateWindSpeed: "",
      climateIce: "",
    },
    {
      climateConditionName: "覆冰工况",
      climateTemperture: "",
      climateWindSpeed: "",
      climateIce: "",
    },
    {
      climateConditionName: "覆冰厚度",
      climateTemperture: "",
      climateWindSpeed: "",
      climateIce: "",
    },
    {
      climateConditionName: "覆冰密度",
      climateTemperture: "",
      climateWindSpeed: "",
      climateIce: "",
    },
    {
      climateConditionName: "最大通风",
      climateTemperture: "",
      climateWindSpeed: "",
      climateIce: "",
    },
    {
      climateConditionName: "安装工况",
      climateTemperture: "",
      climateWindSpeed: "",
      climateIce: "",
    },
    {
      climateConditionName: "外过电压",
      climateTemperture: "",
      climateWindSpeed: "",
      climateIce: "",
    },
    {
      climateConditionName: "内过电压",
      climateTemperture: "",
      climateWindSpeed: "",
      climateIce: "",
    },
  ]);
  const props = defineProps({
  taskId: {
    type: String,
    required: true,
  },
});
  // QNQ(驱鸟器), QNFC(驱鸟风车), DlGZ(电缆管枕), JSP(警示牌), BZZ(标志桩), GB(盖板)
  const getProjectMaterialsFun = (materialsTypekey) => {
    return getProjectMaterials({ materialsTypeKey: materialsTypekey }).then(
      (res) => {
        let resrult = [];
        materialsTypekey.split(",").forEach((item1) => {
          resrult = res.data[item1].map((item) => ({
            label: item.materialname,
            value: item.materialsprojectid,
          }));
        });
        return resrult;
      }
    );
  };
  const dealStr = (str) => {
    return str
      ? str
          .split(";")
          .filter(Boolean)
          .map((item) => {
            const [value, blWithPercentage] = item.split(":");
            const bl = parseFloat(blWithPercentage.replace("%", ""));
            return { value, bl };
          })
      : [];
  };
  const popConfBtn = (type, str, visibleStr, value) => {
    let formattedData;
    if (value != "HaulDistanceJk" && value != "HaulDistanceDl") {
      formattedData = gccsList.value[value]
        .map((item) => {
          return `${item.value}:${item.bl ? item.bl : 0}%`; // 假设 item 是你要显示的数值
        })
        .join(";");
    } else {
      formattedData = gccsList.value[value]
        .map((item) => {
          return `${item.value}:${item.bl ? item.bl : 0}千米`; // 假设 item 是你要显示的数值
        })
        .join(";");
    }
  
    if (type !== "架空运距设置" && type !== "电缆运距设置") {
      // 计算 bl 的总和
      const totalBl = gccsList.value[value].reduce((sum, item) => {
        const bl = item.bl === "" ? 0 : parseFloat(item.bl) || 0;
        return sum + bl;
      }, 0);
      // 判断 bl 总和是否为 100
      if (totalBl !== 100) {
        ElMessageBox.alert("比例分配应为100%，请您重新输入", "消息", {
          confirmButtonText: "OK",
          callback: (action) => {},
        });
      } else {
        dlcsForm.value[str] = formattedData;
        visible.value[visibleStr] = false;
      }
    } else {
      dlcsForm.value[str] = formattedData;
      visible.value[visibleStr] = false;
    }
  };
  
  const getParams = () => {
    return {
      GeographicName: qxqid.value,
      Altitude: qtForm.value.Altitude,
      FilthLevels: qtForm.value.FilthLevels.toString(),
      JiaKongTerrain: dlcsForm.value.jkdx,
      DianLanTerrain: dlcsForm.value.dldx,
      Geological: dlcsForm.value.dz,
      PowerLoad: qtForm.value.PowerLoad,
      AntiThunderLevel: qtForm.value.AntiThunderLevel,
      JiaKongHaulDistance: dlcsForm.value.jkyj,
      DianLanHaulDistance: dlcsForm.value.dlyj,
    };
  };
  
  const submitFormDialog = () => {
    for (const key in addForm.value) {
      if (addForm.value.hasOwnProperty(key)) {
        if (
          addForm.value[key] === "不配置" ||
          addForm.value[key] === null ||
          addForm.value[key] === undefined
        ) {
          addForm.value[key] = "";
        }
      }
    }
    const params = {
        taskId:props.taskId,
      entrance: "2",
      geographicConditionsSetting: JSON.stringify(getParams()),
    };
    engineeringSettingSetUp(params).then((res) => {
      ElMessageBox.alert(res.msg, "消息", {
        confirmButtonText: "OK",
        callback: (action) => {
          getQueryList();
        },
      });
    });
  };
  
  const importTemplateVisible = ref(false);
  const importTemplateHandle = () => {
    importTemplateVisible.value = true;
  };
  
  const saveTemplateVisible = ref(false);
  const saveTemplateHandle = () => {
    saveTemplateVisible.value = true;
  };
  
  const getListDicts = () => {
    // 获取字典数据
    getDicts({
      dictionarKeys: [
        "Altitude",
        "FilthLevel",
        "Terrain",
        "Geological",
        "PowerLoad",
        "AntiThunderLevel",
        "HaulDistance",
      ],
    }).then((res) => {
      if (res.code === 200 && res.data) {
        gccsList.value.AntiThunderLevel = res.data.AntiThunderLevel;
        gccsList.value.PowerLoad = res.data.PowerLoad;
        gccsList.value.FilthLevel = res.data.FilthLevel;
        gccsList.value.Altitude = res.data.Altitude;
      }
    });
  };
  const addQxq = () => {
    qxqForm.value.options = qxqList.value.filter(
      (item) => item.useType === "general"
    );
    visible.value.qxqVisible = true;
  };
  const qxqDialogAdd = () => {
    if (!qxqForm.value.select) {
      ElMessageBox.alert("请选择父级气象区", "消息", {
        confirmButtonText: "OK",
        callback: (action) => {},
      });
    }
    const params = {
      parentId: qxqForm.value.select,
      climateZoneName: qxqForm.value.input,
      useType: "self",
      rows: qxqData.value,
    };
    addInfo(params).then((res) => {
      ElMessageBox.alert(res.msg, "消息", {
        confirmButtonText: "OK",
        callback: (action) => {
          visible.value.qxqVisible = false;
          getListInfo();
        },
      });
    });
  };
  const qxqChange = (val) => {
    qxList.value = qxqList.value.find(
      (item) => item.objId == val && item.rows
    ).rows;
  };
  const qxqFormSelectChange = (val) => {
    qxqData.value = qxqList.value.find(
      (item) => item.objId == val && item.rows
    ).rows;
  };
  const getListInfo = () => {
    //   获取气象数据
    getInfoById().then((res) => {
      console.log(res);
      if (res.code === 200 && res.data) {
        qxqList.value = res.data;
        getQueryList();
      }
    });
  };
  
  const getTemplateData = (val) => {
    const geographicConditionsSetting = JSON.parse(val);
    if (!qxqid.value) {
      // 如果 qxqid 为空才赋值
      qxqid.value = geographicConditionsSetting.GeographicName;
    }
    qtForm.value.Altitude = geographicConditionsSetting.Altitude;
    qtForm.value.FilthLevels = geographicConditionsSetting.FilthLevels.split(",");
    dlcsForm.value.jkdx = geographicConditionsSetting.JiaKongTerrain;
    dlcsForm.value.dldx = geographicConditionsSetting.DianLanTerrain;
    dlcsForm.value.dz = geographicConditionsSetting.Geological;
    qtForm.value.PowerLoad = geographicConditionsSetting.PowerLoad;
    qtForm.value.AntiThunderLevel = geographicConditionsSetting.AntiThunderLevel;
    dlcsForm.value.jkyj = geographicConditionsSetting.JiaKongHaulDistance;
    dlcsForm.value.dlyj = geographicConditionsSetting.DianLanHaulDistance;
  };
  
  const getQueryList = () => {
    getEngineeringSetting({taskId:props.taskId}).then((res) => {
      qxqid.value = res.data.GeographicName;
      if (qxqList.value.length > 0) {
        qxqChange(qxqid.value)
      }
      qtForm.value.Altitude = res.data.Altitude;
      qtForm.value.FilthLevels = res.data.FilthLevels.split(",");
      qtForm.value.PowerLoad = res.data.PowerLoad;
      qtForm.value.AntiThunderLevel = res.data.AntiThunderLevel;
      // 架空运距设置
      gccsList.value.HaulDistanceJk = dealStr(res.data.JiaKongHaulDistance);
      if (gccsList.value.HaulDistanceJk.length > 0) {
        popConfBtn("架空运距设置", "jkyj", "jkyjVisible", "HaulDistanceJk");
      }
      // 架空地形
      gccsList.value.TerrainJk = dealStr(res.data.JiaKongTerrain);
      if (gccsList.value.TerrainJk.length > 0) {
        popConfBtn("架空地形", "jkdx", "jkdxVisible", "TerrainJk");
      }
      // 电缆地形
      gccsList.value.TerrainDx = dealStr(res.data.JiaKongTerrain);
      if (gccsList.value.TerrainDx.length > 0) {
        popConfBtn("电缆地形", "dldx", "dldxVisible", "TerrainDx");
      }
      // 地质选择
      gccsList.value.Geological = dealStr(res.data.Geological);
      if (gccsList.value.Geological.length > 0) {
        popConfBtn("地质选择", "dz", "dzVisible", "Geological");
      }
      // 电缆运距设置
      gccsList.value.HaulDistanceDl = dealStr(res.data.JiaKongHaulDistance);
      if (gccsList.value.HaulDistanceDl.length > 0) {
        popConfBtn("电缆运距设置", "dlyj", "dlyjVisible", "HaulDistanceDl");
      }
    });
  };
  // 在组件挂载时获取数据
  onMounted(() => {
    getListInfo();
    getListDicts();
    console.log("🚀 ~ onMounted ~ props.taskId:", props.taskId)
  });
  </script>
    
    <style scoped lang="scss">
  /* 你的样式 */
  .elCollapse {
    ::v-deep .el-collapse-item__arrow {
      margin-left: 8px;
      margin-bottom: 1px;
    }
  }
  .container-footer {
    border-top: 1px solid var(--el-border-color);
    padding: 20px 0;
    text-align: right;
  }
  </style>
    