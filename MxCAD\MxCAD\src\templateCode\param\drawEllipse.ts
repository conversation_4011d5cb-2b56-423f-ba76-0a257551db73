import {McDb<PERSON>llipse, McGeVector3d, MxCpp, Mc<PERSON>e<PERSON>oint3d, McCmColor } from "mxcad";
 
//画椭圆弧
function drawEllipse() {
  const mxcad = MxCpp.getCurrentMxCAD();
  // 创建新画布
  mxcad.newFile();

  //绘制椭圆
  const ellipse = new McDbEllipse();
  ellipse.center = new McGePoint3d(-200, -200);
  ellipse.majorAxis = new McGeVector3d(0, 300, 0);
  ellipse.radiusRatio = 0.5;
  ellipse.trueColor = new McCmColor(255, 233, 0);
  mxcad.drawEntity(ellipse);

  const ellipse_arc = new McDbEllipse();
  ellipse_arc.center = new McGePoint3d(-380, -200);
  ellipse_arc.majorAxis = new McGeVector3d(0, 150, 0);
  ellipse_arc.minorAxis = new McGeVector3d(280, 0, 0);
  ellipse_arc.startAngle = Math.PI / 2;
  ellipse_arc.endAngle = Math.PI * 3 / 2;
  ellipse_arc.trueColor = new McCmColor(0, 255, 255);
  mxcad.drawEntity(ellipse_arc);

  mxcad.drawEllipse(0, -200, 200, 0, 1.5);

  mxcad.drawColor = new McCmColor(0, 255, 0);
  mxcad.drawEllipseArc(200, -200, 200, 0, 1.5, 100, 650);

  mxcad.zoomAll();
  mxcad.zoomScale(0.8);
};

// 调用画椭圆的方法
drawEllipse();