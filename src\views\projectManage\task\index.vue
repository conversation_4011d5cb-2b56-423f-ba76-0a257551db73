<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true">
      <el-form-item label="工程名称" prop="projectName">
        <el-input
        v-model="queryParams.projectName"
        placeholder="请输入工程名称"
        clearable
        style="width: 200px"
        />
      </el-form-item>
      <el-form-item label="任务状态" prop="projectCode">
        <el-select
          v-model="queryParams.value"
          placeholder="请选择"
          style="width: 200px"
          >
          <el-option label="未开始" value="0"/>
          <el-option label="进行中" value="1"/>
          <el-option label="已完成" value="2"/>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="getList">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-tabs
      v-model="activeName"
      class="demo-tabs"
      @tab-change="handleClick"
      style="margin-top: 15px"
    >
      <el-tab-pane label="需求编制任务" name="1">
        <el-table
          class="demo-table"
          v-loading="loading"
          stripe
          :data="taskList"
          row-key="menuId"
          :default-expand-all="isExpandAll"
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        >
        <el-table-column prop="projectName" label="项目名称" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column prop="compilingOrganisationName" label="所属市公司" width="120" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column prop="powersupplyStationOrganisationName" label="所属县公司" width="120" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column prop="voltageLevel" label="电压等级" width="80"></el-table-column>
        <el-table-column prop="createTime" label="任务下达时间" width="110"></el-table-column>
        <el-table-column prop="state" label="任务状态" width="80">
          <template #default="scope">
            <span v-if="scope.row.state === '1'">进行中</span>
            <span v-else-if="scope.row.state === '2'">已完成</span>
            <span v-else>未开始</span>
          </template>
        </el-table-column>
        <el-table-column prop="" label="数据来源" width="80"></el-table-column>
  <!--      <el-table-column prop="sjly" label="获取状态" width="80"></el-table-column>-->
        <el-table-column label="操作" align="center" width="400" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button type="warning" round @click="gccsBtn(scope.row)">工程参数设置</el-button>
            <el-button color="#1abc9c" style="color: #fff;" round  @click="startOnlineDesgin(scope.row)">设计</el-button>
            <el-button type="primary" round @click="htBtn(scope.row, '2')">移交</el-button>
          </template>
        </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.current"
          v-model:limit="queryParams.size"
          @pagination="getList"
        />
      </el-tab-pane>
      <el-tab-pane label="可研设计任务" name="2">
        <el-table
          class="demo-table"
          v-loading="loading"
          stripe
          :data="taskList"
          row-key="menuId"
          :default-expand-all="isExpandAll"
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        >
          <el-table-column prop="projectName" label="项目名称" :show-overflow-tooltip="true"></el-table-column>
          <el-table-column prop="compilingOrganisationName" label="所属市公司" width="120" :show-overflow-tooltip="true"></el-table-column>
          <el-table-column prop="powersupplyStationOrganisationName" label="所属县公司" width="120" :show-overflow-tooltip="true"></el-table-column>
          <el-table-column prop="voltageLevel" label="电压等级" width="80"></el-table-column>
          <el-table-column prop="createTime" label="任务下达时间" width="110"></el-table-column>
          <el-table-column prop="state" label="任务状态" width="80">
            <template #default="scope">
              <span v-if="scope.row.state === '1'">进行中</span>
              <span v-else-if="scope.row.state === '2'">已完成</span>
              <span v-else>未开始</span>
            </template>
          </el-table-column>
          <el-table-column prop="" label="数据来源" width="80"></el-table-column>
          <!--      <el-table-column prop="sjly" label="获取状态" width="80"></el-table-column>-->
          <el-table-column label="操作" align="center" width="400" class-name="small-padding fixed-width">
            <template #default="scope">
              <!--            <el-button color="#0e8b8f" style="color: #fff;" :disabled="true" round>获取</el-button>-->
              <el-button v-if="scope.row.state === '2'" color="red" style="color: #fff;" round @click="htBtn(scope.row, '1')">回退</el-button>
              <div v-else>
                <el-button type="warning" round @click="gccsBtn(scope.row)" :disabled="true">工程参数设置</el-button>
                <el-button color="#1abc9c" style="color: #fff;" round  @click="startOnlineDesgin(scope.row)">设计</el-button>
                <el-button type="primary" round @click="htBtn(scope.row, '2')">移交</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.current"
          v-model:limit="queryParams.size"
          @pagination="getList"
        />
      </el-tab-pane>
      <el-tab-pane label="初步设计任务" name="3">
        <el-table
          class="demo-table"
          v-loading="loading"
          stripe
          :data="taskList"
          row-key="menuId"
          :default-expand-all="isExpandAll"
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        >
          <el-table-column prop="projectName" label="项目名称" :show-overflow-tooltip="true"></el-table-column>
          <el-table-column prop="compilingOrganisationName" label="所属市公司" width="120" :show-overflow-tooltip="true"></el-table-column>
          <el-table-column prop="powersupplyStationOrganisationName" label="所属县公司" width="120" :show-overflow-tooltip="true"></el-table-column>
          <el-table-column prop="voltageLevel" label="电压等级" width="80"></el-table-column>
          <el-table-column prop="createTime" label="任务下达时间" width="110"></el-table-column>
          <el-table-column prop="state" label="任务状态" width="80">
            <template #default="scope">
              <span v-if="scope.row.state === '1'">进行中</span>
              <span v-else-if="scope.row.state === '2'">已完成</span>
              <span v-else>未开始</span>
            </template>
          </el-table-column>
          <el-table-column prop="" label="数据来源" width="80"></el-table-column>
          <!--      <el-table-column prop="sjly" label="获取状态" width="80"></el-table-column>-->
          <el-table-column label="操作" align="center" width="400" class-name="small-padding fixed-width">
            <template #default="scope">
              <!--            <el-button color="#0e8b8f" style="color: #fff;" :disabled="true" round>获取</el-button>-->
              <el-button v-if="scope.row.state === '2'" color="red" style="color: #fff;" round @click="htBtn(scope.row, '1')">回退</el-button>
              <div v-else>
                <el-button type="warning" round @click="gccsBtn(scope.row)" :disabled="true">工程参数设置</el-button>
                <el-button color="#1abc9c" style="color: #fff;" round  @click="startOnlineDesgin(scope.row)">设计</el-button>
                <el-button type="primary" round @click="htBtn(scope.row, '2')">移交</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.current"
          v-model:limit="queryParams.size"
          @pagination="getList"
        />
      </el-tab-pane>
      <el-tab-pane label="施工图设计任务" name="4">
        <el-table
          class="demo-table"
          v-loading="loading"
          stripe
          :data="taskList"
          row-key="menuId"
          :default-expand-all="isExpandAll"
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        >
          <el-table-column prop="projectName" label="项目名称" :show-overflow-tooltip="true"></el-table-column>
          <el-table-column prop="compilingOrganisationName" label="所属市公司" width="120" :show-overflow-tooltip="true"></el-table-column>
          <el-table-column prop="powersupplyStationOrganisationName" label="所属县公司" width="120" :show-overflow-tooltip="true"></el-table-column>
          <el-table-column prop="voltageLevel" label="电压等级" width="80"></el-table-column>
          <el-table-column prop="createTime" label="任务下达时间" width="110"></el-table-column>
          <el-table-column prop="state" label="任务状态" width="80">
            <template #default="scope">
              <span v-if="scope.row.state === '1'">进行中</span>
              <span v-else-if="scope.row.state === '2'">已完成</span>
              <span v-else>未开始</span>
            </template>
          </el-table-column>
          <el-table-column prop="" label="数据来源" width="80"></el-table-column>
          <!--      <el-table-column prop="sjly" label="获取状态" width="80"></el-table-column>-->
          <el-table-column label="操作" align="center" width="400" class-name="small-padding fixed-width">
            <template #default="scope">
              <!--            <el-button color="#0e8b8f" style="color: #fff;" :disabled="true" round>获取</el-button>-->
              <el-button v-if="scope.row.state === '2'" color="red" style="color: #fff;" round @click="htBtn(scope.row, '1')">回退</el-button>
              <div v-else>
                <el-button type="warning" round @click="gccsBtn(scope.row)" :disabled="true">工程参数设置</el-button>
                <el-button color="#1abc9c" style="color: #fff;" round  @click="startOnlineDesgin(scope.row)">设计</el-button>
                <el-button type="primary" round @click="htBtn(scope.row, '2')">移交</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.current"
          v-model:limit="queryParams.size"
          @pagination="getList"
        />
      </el-tab-pane>
      <el-tab-pane label="施工(设计变更任务)" name="5">
        <el-table
          class="demo-table"
          v-loading="loading"
          stripe
          :data="taskList"
          row-key="menuId"
          :default-expand-all="isExpandAll"
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        >
          <el-table-column prop="projectName" label="项目名称" :show-overflow-tooltip="true"></el-table-column>
          <el-table-column prop="compilingOrganisationName" label="所属市公司" width="120" :show-overflow-tooltip="true"></el-table-column>
          <el-table-column prop="powersupplyStationOrganisationName" label="所属县公司" width="120" :show-overflow-tooltip="true"></el-table-column>
          <el-table-column prop="voltageLevel" label="电压等级" width="80"></el-table-column>
          <el-table-column prop="createTime" label="任务下达时间" width="110"></el-table-column>
          <el-table-column prop="state" label="任务状态" width="80">
            <template #default="scope">
              <span v-if="scope.row.state === '1'">进行中</span>
              <span v-else-if="scope.row.state === '2'">已完成</span>
              <span v-else>未开始</span>
            </template>
          </el-table-column>
          <el-table-column prop="" label="数据来源" width="80"></el-table-column>
          <!--      <el-table-column prop="sjly" label="获取状态" width="80"></el-table-column>-->
          <el-table-column label="操作" align="center" width="400" class-name="small-padding fixed-width">
            <template #default="scope">
              <!--            <el-button color="#0e8b8f" style="color: #fff;" :disabled="true" round>获取</el-button>-->
              <el-button v-if="scope.row.state === '2'" color="red" style="color: #fff;" round @click="htBtn(scope.row, '1')">回退</el-button>
              <div v-else>
                <el-button type="warning" round @click="gccsBtn(scope.row)" :disabled="true">工程参数设置</el-button>
                <el-button color="#1abc9c" style="color: #fff;" round  @click="startOnlineDesgin(scope.row)">设计</el-button>
                <el-button type="primary" round @click="htBtn(scope.row, '2')">移交</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.current"
          v-model:limit="queryParams.size"
          @pagination="getList"
        />
      </el-tab-pane>
      <el-tab-pane label="竣工图设计任务" name="6">
        <el-table
          class="demo-table"
          v-loading="loading"
          stripe
          :data="taskList"
          row-key="menuId"
          :default-expand-all="isExpandAll"
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        >
          <el-table-column prop="projectName" label="项目名称" :show-overflow-tooltip="true"></el-table-column>
          <el-table-column prop="compilingOrganisationName" label="所属市公司" width="120" :show-overflow-tooltip="true"></el-table-column>
          <el-table-column prop="powersupplyStationOrganisationName" label="所属县公司" width="120" :show-overflow-tooltip="true"></el-table-column>
          <el-table-column prop="voltageLevel" label="电压等级" width="80"></el-table-column>
          <el-table-column prop="createTime" label="任务下达时间" width="110"></el-table-column>
          <el-table-column prop="state" label="任务状态" width="80">
            <template #default="scope">
              <span v-if="scope.row.state === '1'">进行中</span>
              <span v-else-if="scope.row.state === '2'">已完成</span>
              <span v-else>未开始</span>
            </template>
          </el-table-column>
          <el-table-column prop="" label="数据来源" width="80"></el-table-column>
          <!--      <el-table-column prop="sjly" label="获取状态" width="80"></el-table-column>-->
          <el-table-column label="操作" align="center" width="400" class-name="small-padding fixed-width">
            <template #default="scope">
              <el-button type="warning" round @click="gccsBtn(scope.row)" :disabled="true">工程参数设置</el-button>
              <el-button color="#1abc9c" style="color: #fff;" round  @click="startOnlineDesgin(scope.row)">设计</el-button>
              <el-button type="primary" round @click="htBtn(scope.row, '2')">移交</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.current"
          v-model:limit="queryParams.size"
          @pagination="getList"
        />
      </el-tab-pane>
  </el-tabs>
    <DialogGccs :getEngineeringList="getEngineeringList" :taskId="taskId" :qxqList="qxqList" :gccsList="gccsList" title="工程参数设置" v-model="isDialogVisible" @cancelDialog="isDialogVisible = false" @close="isDialogVisible = false"></DialogGccs>
  </div>
</template>
<script setup name="Menu">
import DialogGccs from "@/views/desginManage/task/components/DialogGccs.vue";
import {
  getDicts,
  getEngineering,
  getEngineeringParameter,
  getInfoById,
  rollbackEngineering
} from '@/api/desginManage/GccsDialog.js'
import { ref } from 'vue';
import {ElMessageBox} from "element-plus";
import {handleThemeStyle} from "@/utils/theme.js";
import useSettingsStore from "@/store/modules/settings.js";
  const isDialogVisible = ref(false)
  const getEngineeringList = ref({})
  const loading = ref(false);
  const isExpandAll = ref(false);
  const gccsList = ref({})
  const qxqList = ref([])
  const taskList = ref([{projectName: '111'}]);
  const activeName = ref('1')
  const taskId = ref('')
  const total = ref(1);
  const data = reactive({
   queryParams: {
      current: 1,
      size: 10,
      type: '项目名称',
      value: '',
      checked: false,
      projectName: '',
      status: '',
      stage: '1'
   },
  })
  const { queryParams } = toRefs(data);
  const gccsBtn = (row) => {
    taskId.value = row.objId
    isDialogVisible.value = true
    getEngineeringParameter({ engineeringId: row.objId }).then(res => {
      getEngineeringList.value = res.data
    })
  }
  // 处理点击事件
  const oniframeMessage = (param) => {
    const myiframe = document.querySelector("#myiframe").querySelector("iframe");
    if (myiframe && myiframe.contentWindow) {
      myiframe.contentWindow.postMessage(
        param,
        "*"
      );
    }
  }
  const handleClick = (val) => {
    getList()
  }
  const getList = async () => {
    queryParams.value.stage = activeName.value
    if (queryParams.value.checked) {
      queryParams.value.status = '2'
    } else {
      queryParams.value.status = ''
    }
    if (queryParams.value.type === '项目名称') {
      queryParams.value.projectName = queryParams.value.value
    } else if (queryParams.value.type === '任务状态') {
      queryParams.value.status = queryParams.value.value
    } else {
      queryParams.value.projectName = ''
      queryParams.value.status = ''
    }

     const res = await getEngineering(queryParams.value)

        console.log(res.data.records, '1111')
        taskList.value = res.data.records
        total.value = res.data.total
  }
  const queryTypeChange = () => {
    queryParams.value.projectName = ''
    queryParams.value.status = ''
    queryParams.value.value = ''
  }

  // 回退
  const htBtn = (row, status) => {
    ElMessageBox.confirm(
      '确定要进行此操作嘛?',
      '消息',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    ).then(() => {
      rollbackEngineering({ status: status, id: row.objId }).then(res => {
        ElMessageBox.alert(res.msg, '消息', {
          confirmButtonText: 'OK',
          callback: (action) => {
            getList()
          },
        })
      })
    }).catch(() => {})
  }

  /**按条件查询列表 */
  function handleQuery() {

  }

  /**重置 */
  function resetQuery() {

  }

  /*启动在线设计*/
  function startOnlineDesgin(row) {
    const lineIds = ["10000201-183477"]
   window.open('/online/onlineDesign?id=' + row.objId)
   oniframeMessage({ type: "greeting", content: 'SDK_loadMap', options: {lineIds: lineIds} })
  }

onMounted(() => {
  getList()
})
</script>
