<template>
  <div :class="{ 'has-logo': showLogo }" class="sidebar-container">
    <logo v-if="showLogo" :collapse="isCollapse"/>
    <div v-if="route.name=='OnlineDesign'">
      <div
          style="display: flex; color:#fff; align-items: center;  background-color: #0e8b8d;height:36px;font-size:14px;text-align: center;cursor: pointer;margin-bottom: 20px;">
        <span :class="flag==1?'active':''" style="flex: 1;border: 1px solid #639593;height: 36px;line-height: 36px;"
              @click="flag=1">成果目录</span>
        <span :class="flag==2?'active':''" style="flex: 1;border: 1px solid #639593;height: 36px;line-height: 36px;"
              @click="flag=2">属性</span>
        <span :class="flag==3?'active':''" style="flex: 1;border: 1px solid #639593;height: 36px;line-height: 36px;"
              @click="flag=3">设备树</span>
      </div>
      <tree-data
          v-if="flag === 1"
          :treeData="data"
          :defaultProps="defaultProps"
          @handleNodeContextMenu="handleNodeContextMenu"
          @nodeClick="nodeClick"
      ></tree-data>
      <!-- 右键菜单的弹出框 -->
      <el-popover
          v-model:visible="showMenu"
          placement="bottom"
          width="150"
      >
        <div style="font-size: 14px">
          <div class="file-actions">
            <!-- 添加文件按钮 -->
            <div class="action-item" @click="dialogVisible = true" v-if="level === 3">
              <el-icon color="green" class="icon">
                <CirclePlusFilled/>
              </el-icon>
              <span class="label">添加文件</span>
            </div>

            <!-- 删除所有文件按钮 -->
            <div class="action-item delete" @click="delTree">
              <el-icon color="red" class="icon">
                <Delete/>
              </el-icon>
              <span class="label">{{ delTitle }}</span>
            </div>
          </div>
          <!--            <div><el-button type="primary" icon="CirclePlus" plain style="width: 100%;" @click="dialogVisible = true">新增</el-button></div>-->
          <!--            <div><el-button type="danger" icon="Delete" plain style="width: 100%;" @click="delTree">{{ delTitle }}</el-button></div>-->
        </div>
        <template #reference>
          <!-- 参考元素，动态放置在鼠标点击位置 -->
          <div
              ref="popoverRef"
              class="popover-reference"
              :style="menuPosition"
          ></div>
        </template>
      </el-popover>
      <property v-if="flag === 2"/>
    </div>

    <el-scrollbar v-else wrap-class="scrollbar-wrapper">
      <el-menu
          :default-active="activeMenu"
          :collapse="isCollapse"
          :background-color="getMenuBackground"
          :text-color="getMenuTextColor"
          :unique-opened="true"
          :active-text-color="theme"
          :collapse-transition="false"
          mode="vertical"
          :class="sideTheme"
      >
        <sidebar-item
            v-for="(route, index) in sidebarRouters"
            :key="route.path + index"
            :item="route"
            :base-path="route.path"
        />
      </el-menu>
    </el-scrollbar>
  </div>
  <!-- 新增-->
  <el-dialog
      :show-close="false"
      v-model="dialogVisible"
      title="添加文件"
      width="500"
      @close="handleClose"
  >
    <el-descriptions border :column="1" label-width="100px">
      <el-descriptions-item label="文件类型">
        <el-select v-model="addForm.fileType" placeholder="请选择文件类型" style="width: 200px">
          <el-option
              v-for="item in fileTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
          />
        </el-select>

      </el-descriptions-item>
      <el-descriptions-item label="文件名称">
        <div style="width: 350px">
          <FileUpload @dataTreeList="dataTreeList"
                      :formData="{ prjTaskInfoId: taskId, specificationParentId: selectedNode.id }" fileSize="500" ref="fileUp"
                      uploadFileUrl="/template/saveFile" :isShowTip="false" :fileType="[]"></FileUpload>
          <!--          <el-upload-->
          <!--              v-model:file-list="fileList"-->
          <!--              class="upload-demo"-->
          <!--              action=""-->
          <!--              :on-remove="handleRemove"-->
          <!--              :auto-upload="false"-->
          <!--          >-->
          <!--            <el-button type="primary">选取文件</el-button>-->
          <!--          </el-upload>-->
        </div>
      </el-descriptions-item>
    </el-descriptions>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="addTreeBtn">确定</el-button>
      </div>
    </template>
  </el-dialog>
  <!-- 在线预览 -->
  <el-dialog
      v-model="dialogPdf"
      fullscreen
      top="90vh"
      width="70%"
      draggable
  >
    <div style="height: 94vh">
      <iframe
          id="ifr"
          :src="browseUrl"
          width="100%"
          height="100%"
          frameborder="0"
          scrolling="yes"
          style="border: 1px solid lightgray"
      ></iframe>
    </div>
  </el-dialog>
</template>

<script setup>
import Logo from './Logo'
import SidebarItem from './SidebarItem'
import variables from '@/assets/styles/variables.module.scss'
import useAppStore from '@/store/modules/app'
import useSettingsStore from '@/store/modules/settings'
import usePermissionStore from '@/store/modules/permission'
import TreeData from '@/components/TreeData'
import {useRoute} from "vue-router";
import {deleteTree, downloadFile, saveFile, specificationTree} from "@/api/task/index.js";
import Property from '@/views/pages/online/sidebar/property'
import { ElLoading } from 'element-plus'
const flag=ref(1)
const route = useRoute();
const appStore = useAppStore()
const settingsStore = useSettingsStore()
const permissionStore = usePermissionStore()
const sidebarRouters = computed(() => permissionStore.sidebarRouters);
const showLogo = computed(() => settingsStore.sidebarLogo);
const sideTheme = computed(() => settingsStore.sideTheme);
const theme = computed(() => settingsStore.theme);
const isCollapse = computed(() => !appStore.sidebar.opened);
const data = ref([])
const taskId = route.query.id
const stage = route.query.stage
const selectedNode = ref(null) // 当前选中的节点
const delTitle = ref('删除所有文件')
const menuPosition = ref({}) // 菜单位置
const showMenu = ref(false) // 控制弹框显示
const dialogVisible = ref(false)
const dialogPdf = ref(false)
const browseUrl = ref('')
const level = ref(null)
const {proxy} = getCurrentInstance();
const addForm = ref({
  fileType: '1',
  fileName: ''
})
const fileTypeOptions = ref([
  {label: '设计说明书', value: '1'},
  {label: '设计文件总目录', value: '2'},
])

const defaultProps = {
  children: 'children',
  label: 'specificationName',
}
const fileList = ref([])

const handleRemove = (file, uploadFiles) => {
  fileList.value = uploadFiles
}
const fileUp = ref(null);
// 新增树弹框确定按钮
const addTreeBtn = () => {
  fileUp.value.handleUpload(); // 触发上传
  dialogVisible.value = false
}
// 点击文件实现文件预览
const nodeClick = (item, node) => {
  console.log("🚀 ~ nodeClick ~ node:", node)
  // 文件在第4层级
  if (node.level === 4) {
    const loading = ElLoading.service({
      lock: true,
      text: '加载中...',
      background: 'rgba(0, 0, 0, 0.7)',
    })
    downloadFile({fileId: item.id}).then(res => {
      dialogPdf.value = true
      const openUrl = window.URL.createObjectURL(res)
      browseUrl.value = import.meta.env.VITE_PDF_URL + '?file=' + encodeURIComponent(openUrl)
      loading.close()
    })
  }
}
// 查询树形数据
const dataTreeList = () => {
  specificationTree({prjTaskInfoId: taskId, stage: stage}).then(res => {
    data.value = res
  })
}
const delTree = () => {
  const isFile = level.value === 4 ? '1' : '2'
  proxy.$modal.confirm('确定要' + delTitle.value + '吗?').then(function () {
    return deleteTree({id: selectedNode.value.id, isFile: isFile});
  }).then(() => {
    dataTreeList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {
  });
}
// 树形侧边栏新增对话框关闭事件
const handleClose = () => {
  fileUp.value.onClose()
}

// 树形侧边栏鼠标右键事件
const handleNodeContextMenu = (event, data, node, tree) => {
  level.value = node.level
  if (node.level > 2) {
    if (node.level === 3) {
      delTitle.value = '删除所有文件'
    } else if (node.level === 4) {
      delTitle.value = '删除文件'
    }
    selectedNode.value = data; // 保存当前节点数据
    // 动态设置菜单位置
    menuPosition.value = {
      position: 'absolute',
      top: `${event.clientY}px`,
      left: `${event.clientX}px`,
    };
    showMenu.value = true; // 显示菜单
  }
}

onMounted(() => {
  // dataTreeList()
  console.log(showLogo,'111111111111')
});


// 获取菜单背景色
const getMenuBackground = computed(() => {
  if (settingsStore.isDark) {
    return 'var(--sidebar-bg)';
  }
  return sideTheme.value === 'theme-dark' ? variables.menuBg : variables.menuLightBg;
});

// 获取菜单文字颜色
const getMenuTextColor = computed(() => {
  if (settingsStore.isDark) {
    return 'var(--sidebar-text)';
  }
  return sideTheme.value === 'theme-dark' ? variables.menuText : variables.menuLightText;
});

const activeMenu = computed(() => {
  const {meta, path} = route;
  if (meta.activeMenu) {
    return meta.activeMenu;
  }
  return path;
});
</script>

<style lang="scss" scoped>
.sidebar-container {
  background-color: v-bind(getMenuBackground);

  .scrollbar-wrapper {
    background-color: v-bind(getMenuBackground);
  }

  .el-menu {
    border: none;
    height: 100%;
    width: 100% !important;

    .el-menu-item, .el-sub-menu__title {
      &:hover {
        background-color: var(--menu-hover, rgba(0, 0, 0, 0.06)) !important;
      }
    }

    .el-menu-item {
      color: v-bind(getMenuTextColor);

      &.is-active {
        color: var(--menu-active-text, #409eff);
        background-color: var(--menu-hover, rgba(0, 0, 0, 0.06)) !important;
      }
    }

    .el-sub-menu__title {
      color: v-bind(getMenuTextColor);
    }
  }

  .active {
    background: #0C5657;
    border: 1px solid #639593;
    font-weight: bold;
    font-size: 14px;
    color: #FFD500;
  }
}

.file-actions {
  display: flex;
  flex-direction: column;
}

.action-item {
  display: flex;
  align-items: center;
  padding: 8px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.action-item:hover {
  background-color: #f5f7fa;
  border-radius: 4px;
}

.icon {
  margin-right: 8px;
}
</style>
