<template>
   <data-dialog
      @close="closeDialog"
      dataWidth="500"
      v-if="appStore.mapIndex == '设计完整性分析'"
    >
      <template #header>
        <h4 style="margin-left: 10px; margin-top: 0px; line-height: 39px">
          设计完整性分析
        </h4>
      </template>
      <template #body>
        <div class="small-material" style="background: #e4f2f2">
          <div style="padding: 10px">
            <el-table
              class="small-size"
              border
              style="margin-top: -2px; height: 300px; width: 480px"
              size="small"
              :data="integrity"
            >
              <el-table-column align="center" prop="name" label="档案资料名称" >
              </el-table-column>
              <el-table-column align="center" prop="checkResult" label="分析结果">
                  <template #default="scope">
                    <span v-if="scope.row.checkResult=='缺少资料'" style="color:red;">{{ scope.row.checkResult }}</span>
                    <span v-if="scope.row.checkResult!='缺少资料'" style="color:cyandsd;">{{ scope.row.checkResult }}</span>
                  </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </template>
    </data-dialog>
    </template>
<script setup>
import DataDialog from "@/components/DataDialog/index.vue";
import useAppStore from "@/store/modules/app.js";
import { Calendar } from '@element-plus/icons-vue'
import {
  Integrity
} from "@/api/desginManage/ToolManagement.js";
const route = useRoute();

const appStore = useAppStore();
const closeDialog = () => {
    appStore.mapIndex = "";
};
const activeName = ref('first')
const handleClick = () => {

}
const integrity = ref({
  name:"",
  checkResult:"" 
})
const checkedNz = ref(true)
const dataList = () => {
  Integrity({id:route.query.id}).then(res => {
    console.log(res.data)
    integrity.value=res.data
  })
}
watch(
    () => appStore.mapIndex,
    (newInfo, oldInfo) => {
        console.log(newInfo, oldInfo);
        if (newInfo == "设计完整性分析") {
            dataList()
      }
    }
);
</script>
<style scoped lang="scss">
@use '../../index' as *;

::v-deep .el-tabs__header {
    margin: 0;
}

.threePane-tabs>.el-tabs__content {
    padding: 32px;
    color: #6b778c;
    font-size: 32px;
    font-weight: 600;
}

.threePane-tabs .custom-tabs-label .el-icon {
    vertical-align: middle;
}

.threePane-tabs .custom-tabs-label span {
    vertical-align: middle;
    margin-left: 4px;
}
</style>