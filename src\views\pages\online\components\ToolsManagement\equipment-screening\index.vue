<script setup>
import {useIframeCommunication} from "@/hooks/useIframeCommunication.js";
import {screenEquipment} from "@/api/onlineDesign/index.js";

const {sendMessage, cleanup} = useIframeCommunication()

const cadAppRef = inject('cadAppRef');

const cmd = 'Mx_EquipmentScreening'

const taskId = new URLSearchParams(new URL(window.location.href).search).get(
    "id"
  );
sendMessage(
    cadAppRef.value,
    {content:cmd, type: 'onlycad',options:{name:'JGFZH_Draw'}},
    (res)=> {
      console.log('Mx_EquipmentScreening-callback-res', res)
      const ids = res.ids
      if(ids.length) {
        getEquipmentList(ids)
      }
})

const getEquipmentList = (ids) => {
  screenEquipment({equipmentIds:ids,taskId}).then(res => {
    if(res.code === 200) {
      const data = res.data
      if (data) {
        sendEquipmentType(data)
      }
    }
  })
}

const sendEquipmentType = (data) => {
  const params = {
    cmd: 'Mx_EquipmentSelectByType',
    type: 'sendStringToExecute',
    params: {data}
  }
  sendMessage(cadAppRef.value,
      params,
      (res)=> {

      })
}


</script>

<template>

</template>

<style lang="scss" scoped>

</style>
