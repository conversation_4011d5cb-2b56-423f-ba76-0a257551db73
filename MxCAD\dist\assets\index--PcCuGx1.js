import{o as b,p as c,f,a4 as h,a7 as V,a8 as w}from"./vuetify-B_xYg4qv.js";import{h as _,a0 as a,a3 as s,m as l,$ as t,F as m,a4 as i,_ as d,Q as y,V as P,a2 as g,a5 as v,n as r,L as T}from"./vue-DfH9C9Rx.js";import{_ as k}from"./index-D95UjFey.js";const C={class:"mx-border mt-2"},W={key:0,class:"w-100 h-100 border-bottom"},B=_({__name:"index",props:{modelValue:{default:0},items:{},height:{default:300},tabsProps:{},windowProps:{},windowItemProps:{},isTabMinWidthAuto:{type:Boolean}},emits:["update:modelValue"],setup(I,{emit:u}){const p=u,n=e=>{p("update:modelValue",e)};return(e,M)=>(a(),s("div",C,[l(c,r({"model-value":e.modelValue,"onUpdate:modelValue":n},e.tabsProps),{default:t(()=>[(a(!0),s(m,null,i(e.items,o=>(a(),d(b,{key:o.tab,class:"mx-tab","selected-class":"tab-selected",style:g(e.isTabMinWidthAuto?"min-width: auto;":"")},{default:t(()=>[y(P(e.t(o.tab)),1)]),_:2},1032,["style"]))),128)),!e.tabsProps||!e.tabsProps.grow?(a(),s("div",W)):v("",!0)]),_:1},16,["model-value"]),l(w,r({"model-value":e.modelValue,"onUpdate:modelValue":n},e.windowProps),{default:t(()=>[(a(!0),s(m,null,i(e.items,o=>(a(),d(V,r({key:o.tab,ref_for:!0},e.windowItemProps),{default:t(()=>[l(f,{height:e.height,class:"mt-2"},{default:t(()=>[l(h,{class:"px-3 py-0"},{default:t(()=>[(a(),d(T(o.component)))]),_:2},1024)]),_:2},1032,["height"])]),_:2},1040))),128))]),_:1},16,["model-value"])]))}}),F=k(B,[["__scopeId","data-v-45baabe0"]]);export{F as M};
