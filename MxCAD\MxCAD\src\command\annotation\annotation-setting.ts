import {MxFun} from "mxdraw";

import {McDbBlockReference, McDbAttribute, MxCADSelectionSet, MxCADResbuf, MxCpp, McCmColor} from "mxcad"
async function annotationSetting() {

    const mxcad = MxCpp.getCurrentMxCAD()
    const ss = new MxCADSelectionSet();
//选择所有图形元素
    ss.allSelect();
    ss.forEach((id)=> {
        let ent = id.getMcDbEntity();
        if (!ent) return;
        console.log('annotationSetting',ent)
        // 遍历块中的属性文字 ent:块实体
        let blkRef: McDbBlockReference = ent;
        let aryId = blkRef.getAllAttribute();
        aryId.forEach((id) => {
            let attribt: McDbAttribute = id.getMcDbEntity() as any;
            console.log(attribt.tag,';',attribt.textString);
            // 修改属性文字值
            if(attribt.textString) {
                attribt.trueColor = new McCmColor(255, 255, 255)
            }
        })
    })

    const filter = new MxCADResbuf();
// 添加对象类型，选择集只选择文字类型的对象
    filter.AddMcDbEntityTypes("TEXT,MTEXT")
//选择所有文本对象
    ss.allSelect(filter);


    mxcad.regen();
    mxcad.updateDisplay(true, 300)

}
export function init() {
    MxFun.addCommand("Annotation_Setting", annotationSetting);

}
