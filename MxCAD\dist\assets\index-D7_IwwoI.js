import{O as b,a3 as D}from"./index-D95UjFey.js";import{M as I}from"./index-8X61wlK0.js";import{b as v}from"./vuetify-B_xYg4qv.js";import{h as V,a0 as t,_ as d,$ as C,a1 as y,u as a,a5 as m,a3 as g,V as h,B as L,d as B,a4 as M,F as S}from"./vue-DfH9C9Rx.js";import"./mxcad-CfPpL1Bn.js";import"./mxdraw-C_n_7lEs.js";import"./mapbox-gl-PN3pDJHq.js";import"./xlsx-Ck5MUZvf.js";const U={class:"my-2 d-flex align-center"},F={key:0,style:{"white-space":"pre"}},N={key:1,style:{"white-space":"pre"},class:""},O=V({__name:"MessageBoxDialog",props:{options:{}},setup(o,{expose:i}){const l=b(!1),{isShow:r,showDialog:c,confirm:u,cancel:f}=l,s=Object.assign(D,{question:{icon:"class:iconfont info",color:"rgb(47,144,207)"},none:void 0});let p=[{name:"确定",fun:()=>{},primary:!0},{name:"取消",fun:()=>c(!1)}];const k={escape:()=>{if(o.options.cancelId){const e=p[o.options.cancelId];e.fun.apply(e)}else c(!1),f()},enter:()=>{if(o.options.defaultId){const e=p[o.options.defaultId];e.fun.apply(e)}}};return o.options.buttons&&(p=o.options.buttons.map((e,n)=>({name:e,fun:()=>{u(n),c(!1)},primary:o.options.defaultId===n}))),i({useDialog:l}),(e,n)=>(t(),d(I,{title:e.options.title,modelValue:a(r),"onUpdate:modelValue":n[0]||(n[0]=w=>L(r)?r.value=w:null),footerBtnList:a(p),keys:k,"max-width":"620",width:"auto"},{default:C(()=>[y("div",U,[e.options.type&&a(s)[e.options.type]?(t(),d(v,{key:0,size:36,icon:a(s)[e.options.type]?.icon,class:"mr-2",color:a(s)[e.options.type]?.color},null,8,["icon","color"])):m("",!0),y("div",null,[e.options.message?(t(),g("p",F,h(e.options.message),1)):m("",!0),e.options.detail?(t(),g("p",N,h(e.options.detail),1)):m("",!0)])])]),_:1},8,["title","modelValue","footerBtnList"]))}}),H=V({__name:"index",setup(o){const i=B([]),l=B([]);return(r,c)=>(t(!0),g(S,null,M(i.value,(u,f)=>(t(),d(O,{ref_for:!0,ref_key:"dialogs",ref:l,"onUpdate:modelValue":s=>!s&&i.value.splice(f,1),options:u},null,8,["onUpdate:modelValue","options"]))),256))}});export{H as default};
