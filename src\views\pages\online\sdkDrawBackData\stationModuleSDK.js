import { saveAllInfo, saveAllInfos, getEquipmentState} from "@/api/desginManage/draw.js";
import { ElMessage } from "element-plus";
import { generateUuid } from "@/utils";
import { getEquipmentModel } from "@/views/pages/online/saveModelInfo.js";

// 站房路径图元绘制结束
export const drawEndStation = (params, listForm, drawInfo) => {
  if(!params) return
  console.log('drawEndStation', params, listForm)
  const taskId = new URLSearchParams(new URL(window.location.href).search).get(
    "id"
  );
  let sdkArr = params.data;
  const sdkList = sdkArr.find(
    (item) => drawInfo.sdkClassName.split('-')[0] === item.CLASS_NAME
  );
  const Intervals = {
    in: [],
    out: []
  }
  for(let item of drawInfo.bayInfo.filter(o=>o.bayType === "6")){
    Intervals.in.push({id: generateUuid(), name: item.bayNo})
  }
  for(let item of drawInfo.bayInfo.filter(o=>o.bayType === "1")){
    Intervals.out.push({id: generateUuid(), name: item.bayNo})
  }
  const paramsData = getEquipmentModel("Point", `${sdkList.X} ${sdkList.Y}`, {
    moduleId: listForm.schemeName, // 顶层id HighVoltage\LowVoltage\BYQ
    legendTypeKey: drawInfo.legendtypekey, // 图元类型
    legendState: listForm.status, // 状态
    legendGuidKey: sdkList.DEV_ID, // 设备id
    engineeringId: taskId
  })
  paramsData.privatepropertys = JSON.stringify({
    UserNumber: listForm.stationNum,
    TZ: listForm.soilType,
    schemeType: listForm.schemeNum,
    PSMJ: listForm.roadArea,
    LMPSLX: listForm.roadType,
    Voltagerating: listForm.voltageLevel,
    Intervals: JSON.stringify(Intervals)    
  })
  console.log("处理完后的数据", paramsData);
  saveAllInfo(paramsData).then((res) => {
    if (res.code === 200) {
      ElMessage.success("保存成功");
    } else {
      ElMessage.error(res.msg);
    }
  });
}
