import { MxCADUiPrEntity } from "mxcad";

// 写扩展数据
async function MxTest_WritexData() {
    // 选择对象
    let selEntity = new MxCADUiPrEntity();
    selEntity.setMessage("选择对象");
    let id = await selEntity.go();
    if (!id.isValid()) return;
     
    // 获取对象
    let ent = id.getMcDbEntity();
    if (ent === null) return;
    // 写扩展数据
    ent.setxDataString("DataName", "xxxxx");
    //ent.setxData(new MxCADResbuf([{type:MxCADResbufDataType.kExDataName,val:"DataName"},{type:MxCADResbufDataType.kString,val:"yyyyy"}]));
};

// 调用写扩展数据的方法
 MxTest_WritexData();

