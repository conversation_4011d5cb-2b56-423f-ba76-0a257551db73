import{M as a,w as t}from"./mxdraw-BQ5HhRCY.js";import{R as i}from"./mxcad-DrgW2waE.js";import{ak as m}from"./vue-Cj9QYd7Z.js";import{Z as r,_ as d}from"./vuetify-BqCp6y38.js";import{B as p,D as f,E as w,F as u,G as x,H as l,I as C,J as M,f as c,K as g,L as D,N as b,b as v,c as I,O as h,P as y,e as L,Q as T,R as o,z as _,S as A}from"./index-CzBriCFR.js";import{M as F}from"./index-itnQ6avM.js";import{M as S}from"./index-D2jCiJ2B.js";import{T as U}from"./TextEllipsis-C4aaa1NU.js";import{a as B}from"./mapbox-gl-DQAr7S0z.js";import"./handsontable-Ch5RdAT_.js";const E={MxBtnList:p,MxColorSelect:f,MxDialog:F,MxMenu:w,MxTabsWindow:S,TextEllipsis:U},P={getApp:u,setCustomDataListLength:x,store:{useFocus:l,useTextStyle:C},getUiConfig:M,utils:{browserCacheRef:c},useTheme:g,useFileName:D,useUserInfo:b,addDrawerComponent(s,e){a.callEvent("__addDrawerComponent",{name:s,componentInfo:e})},components:{...E},addCommand:v,callCommand:I,getMxCADUIImplement:h,useLoading:y,useMessage:L,useDialogIsShow:T,on:o.on.bind(o),off:o.off.bind(o),emit:o.emit.bind(o),once:o.once.bind(o)};window.Mx=t;window.MxCAD=i;window.Vue=m;window.axios=_;window.MxPluginContext=P;window.vuetify=r;window.vuetifyComponents=d;window.mapboxgl=B;a.on("__addDrawerComponent",s=>{const{name:e,componentInfo:n}=s;A(e,n)});
