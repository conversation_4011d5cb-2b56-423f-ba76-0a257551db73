import {
    MxCpp,
    MxCADUiPrKeyWord,
    MxCADUiPrEntity,
    MxCADSelectionSet,
    MxCADResbuf,
    MxCADUtility,
    McObjectId,
    MxCADUiPrPoint,
    McDbBlockReference
} from "mxcad";
import { MxFun} from "mxdraw";


async function Mx_EquipmentScreening(data: any) {
    const ss = new MxCADSelectionSet();
    const filter = new MxCADResbuf();
// 添加对象类型，选择集只选择文字类型的对象
//     filter.AddMcDbEntityTypes("TEXT,MTEXT")
// 加filter过滤选择集, 这里只会选择文字对象
    ss.userSelect("请选择设备筛选区域", filter).then((is) => {
        if (is) {
            // 得到框选的两个选择点
            const { pt1, pt2 } = ss.getSelectPoint()
            ss.getIds()
            const ids = []
            ss.forEach((id) => {
                let ent = id.getMcDbEntity();
                if (!ent) return;
                const equId = ent.getxDataString('equipmentId')?.val
                console.log(id, ent)
                ids.push(equId || id.id);
            })
            const params = {
                ids
            }
            MxFun.postMessageToParentFrame({messageId: data?.id, params });


        }
    })
}

async function Mx_EquipmentSelectByType(data: any) {
    const mxcad = MxCpp.getCurrentMxCAD();
    let selEntity = new MxCADUiPrEntity();
    selEntity.setMessage("请选择设备类型：");
    const equipmentData = data.params.data
    const equipmentString = `[${generateDeviceString(equipmentData)}]`;
    const getKey = new MxCADUiPrKeyWord
    getKey.setMessage('选择设备类型')
    getKey.setKeyWords(equipmentString)
    const keyVal = await getKey.go()
    const equipmentList = findEquipmentByNumber(equipmentData, keyVal);

    const ss = new MxCADSelectionSet();
    let filter = new MxCADResbuf();
    // filter.AddMcDbEntityTypes("INSERT");
    //选择所有图形元素
    ss.allSelect(filter);
    if (ss.count() == 0) return;
    ss.forEach((idF)=> {
        console.log('idf', idF)
        const ent = idF.getMcDbEntity() as McDbBlockReference;
        const equId = ent.getxDataString('equipmentId')?.val
        console.log('equId', equId)
        const item = equipmentList.find(item => [equId, idF.id].includes(item.equipmentId));
        if (item) {
            console.log('item', item)
            // const {x, y, z} = ent.position;
            // let objId = MxCADUtility.findEntAtPoint(x,y,z,-1)
            mxcad.addCurrentSelect(idF)
        }
    })

}

// 动态生成设备类型编号字符串（如 1 2 3）
const generateNumberString = (data) => {
    let index = 1;  // 编号从 1 开始
    const numberString = Object.keys(data).map(() => {
        return index++;  // 根据顺序生成纯数字编号
    }).join(" ");  // 将数字编号连接成字符串，用空格分隔

    return numberString;
};

// 动态生成设备类型列表字符串
const generateDeviceString = (data) => {
    let index = 1;  // 设备编号从 1 开始
    const deviceString = Object.keys(data).map((key) => {
        return `${key}(${index++})`;  // 根据顺序生成编号和设备类型
    }).join("/");  // 将生成的字符串连接起来，用空格分隔

    return deviceString;
};

// 根据编号查找对应的设备数据
const findEquipmentByNumber = (data, number) => {
    const keys = Object.keys(data);  // 获取所有的设备类型
    const equipmentType = keys[number - 1];  // 根据输入的编号（从1开始）找到设备类型
    return data[equipmentType] || null;  // 返回对应的设备数据，如果没有则返回 null
};


export function init() {
    MxFun.addCommand("Mx_EquipmentScreening", Mx_EquipmentScreening);
    MxFun.addCommand("Mx_EquipmentSelectByType", Mx_EquipmentSelectByType);
}
