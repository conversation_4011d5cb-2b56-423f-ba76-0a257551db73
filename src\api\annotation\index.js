import request from "@/utils/request.js";

export function getEquipmentInfoString(data) {
    return request({
        url: '/rodLineJointDraw/getEquipmentInfoString',
        method: 'post',
        data: data
    })
}


export function getEquipmentNote(params) {
    return request({
        url: '/rodLineJointDraw/getEquipmentNote',
        method: 'get',
        params
    })
}

export function getExplanatoryDrawings(params) {
    return request({
        url: '/system/explanatoryDrawings/getExplanatoryDrawings',
        method: 'get',
        params
    })
}