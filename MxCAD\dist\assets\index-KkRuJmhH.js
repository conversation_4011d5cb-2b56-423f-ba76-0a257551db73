import{U as f,Z as L,V as I,a8 as R,a9 as r,aa as _,ab as b,ac as O,ad as B,ae as j,af as P,ag as i,ah as t,Q as F}from"./index-CzBriCFR.js";import{M as $}from"./index-itnQ6avM.js";import{M as z}from"./index-D2jCiJ2B.js";import{I as h,z as N,B as g,b as Q,g as q,L as T}from"./vuetify-BqCp6y38.js";import{h as M,_ as S,$ as U,m as n,a4 as o,Q as d,V as u,a0 as G,F as W,a5 as X,a3 as A,d as D,w as V,r as E,k as w,u as v,B as Z}from"./vue-Cj9QYd7Z.js";import{y as x}from"./mxcad-DrgW2waE.js";import"./mxdraw-BQ5HhRCY.js";import"./handsontable-Ch5RdAT_.js";import"./mapbox-gl-DQAr7S0z.js";const H={class:"d-flex flex-wrap"},J={class:"d-flex my-1 w-50"},K=M({__name:"ObjectSnaps",props:{data:{},options:{},allSelect:{type:Function},allCancel:{type:Function}},setup(l){return(e,s)=>(S(),U("div",null,[n(h,{modelValue:e.options.isOpen,"onUpdate:modelValue":s[0]||(s[0]=a=>e.options.isOpen=a)},{label:o(()=>[n(f,{"key-name":"S"},{default:o(()=>[d(u(e.t("启动对象捕捉")),1)]),_:1})]),_:1},8,["modelValue"]),n(I,{title:e.t("对象捕捉模式"),class:"mt-2"},{default:o(()=>[n(N,null,{default:o(()=>[n(g,{cols:9},{default:o(()=>[G("div",H,[(S(!0),U(W,null,X(e.data,(a,m)=>(S(),U("div",J,[n(h,{class:"",modelValue:a.check,"onUpdate:modelValue":c=>a.check=c},{prepend:o(()=>[n(Q,{icon:a.icon,class:"mr-1"},null,8,["icon"])]),label:o(()=>[n(f,{"key-name":a.key},{default:o(()=>[d(u(a.name),1)]),_:2},1032,["key-name"])]),_:2},1032,["modelValue","onUpdate:modelValue"])]))),256))])]),_:1}),n(g,{cols:2,"align-self":"start"},{default:o(()=>[n(L,{onClick:e.allSelect},{default:o(()=>[d(u(e.t("全部选择")),1)]),_:1},8,["onClick"]),n(L,{class:"mt-3",onClick:e.allCancel},{default:o(()=>[d(u(e.t("全部取消")),1)]),_:1},8,["onClick"])]),_:1})]),_:1})]),_:1},8,["title"])]))}}),Y=M({__name:"PolarTracking",props:{options:{}},setup(l){return(e,s)=>(S(),A(N,null,{default:o(()=>[n(g,{cols:5},{default:o(()=>[n(h,{modelValue:e.options.isOpen,"onUpdate:modelValue":s[0]||(s[0]=a=>e.options.isOpen=a)},{label:o(()=>[n(f,{"key-name":"P"},{default:o(()=>[d(u(e.t("527")),1)]),_:1})]),_:1},8,["modelValue"]),n(I,{title:e.t("519"),class:"mt-2",style:{height:"200px"}},{default:o(()=>[n(f,{"key-name":"I",colon:""},{default:o(()=>[d(u(e.t("520")),1)]),_:1}),n(q,{class:"mt-2",items:[5,10,15,20,30,45,60,90],modelValue:e.options.polarang,"onUpdate:modelValue":s[1]||(s[1]=a=>e.options.polarang=a)},null,8,["modelValue"])]),_:1},8,["title"])]),_:1}),n(g,{cols:7})]),_:1}))}}),ee=M({__name:"GridSnap",props:{options:{}},setup(l){return(e,s)=>(S(),A(N,null,{default:o(()=>[n(g,{cols:7},{default:o(()=>[n(h,{modelValue:e.options.isOpen,"onUpdate:modelValue":s[0]||(s[0]=a=>e.options.isOpen=a)},{label:o(()=>[n(f,{"key-name":"S"},{default:o(()=>[d(u(e.t("518")),1)]),_:1})]),_:1},8,["modelValue"]),n(I,{title:e.t("519"),class:"mt-2",style:{height:"200px"}},{default:o(()=>[n(f,{"key-name":"I",class:"mt-1",colon:""},{default:o(()=>[d(u(e.t("520")),1)]),_:1}),n(T,{class:"mt-1",modelValue:e.options.ptSnapUnit.x,"onUpdate:modelValue":s[1]||(s[1]=a=>e.options.ptSnapUnit.x=a)},{prepend:o(()=>[n(f,{"key-name":"P",colon:""},{default:o(()=>[d(u(e.t("521")),1)]),_:1})]),_:1},8,["modelValue"]),n(T,{class:"mt-1",modelValue:e.options.ptSnapUnit.y,"onUpdate:modelValue":s[2]||(s[2]=a=>e.options.ptSnapUnit.y=a)},{prepend:o(()=>[n(f,{"key-name":"C",colon:""},{default:o(()=>[d(u(e.t("522")),1)]),_:1})]),_:1},8,["modelValue"])]),_:1},8,["title"])]),_:1}),n(g,{cols:5})]),_:1}))}}),ne=()=>{const l=E({options:{isOpen:!1},data:[{icon:"class:iconfont fangkuang",check:i(t.End),type:t.End,name:"端点",key:"E"},{icon:"class:iconfont merge",check:i(t.Ins),type:t.Ins,name:"插入点",key:"S"},{icon:"class:iconfont sanjiao",check:i(t.Mid),type:t.Mid,name:"中心",key:"M"},{icon:"class:iconfont chuizu",check:i(t.Perp),type:t.Perp,name:"垂足",key:"P"},{icon:"class:iconfont yuan",check:i(t.Cen),type:t.Cen,name:"圆心",key:"C"},{icon:"class:iconfont qiedian",check:i(t.Tan),type:t.Tan,name:"切点",key:"N"},{icon:"class:iconfont yuanzhoncha",check:i(t.Node),type:t.Node,name:"节点",key:"D"},{icon:"class:iconfont zuijindian",check:i(t.Near),type:t.Near,name:"最近点",key:"R"},{icon:"class:iconfont lingxingfangkuang",check:i(t.Quad),type:t.Quad,name:"象限点",key:"Q"},{icon:"class:iconfont shizikuang",check:i(t.App),type:t.App,name:"外观交点",key:"A"},{icon:"class:iconfont cha",check:i(t.Int),type:t.Int,name:"交点",key:"I"},{icon:"class:iconfont pinghangdu",check:i(t.Par),type:t.Par,name:"平行",key:"L"},{icon:"class:iconfont yanshen",check:i(t.Ext),type:t.Ext,name:"延伸",key:"X"}],allSelect:function(){l.data.forEach(e=>{e.check=!0})},allCancel:function(){l.data.forEach(e=>{e.check=!1})}});return{tab:"对象捕捉",props:l,component:()=>w(K,l)}},oe=()=>{const l=E({options:{isOpen:!1,polarang:0}});return{tab:"极轴追踪",props:l,component:()=>w(Y,l)}},ae=()=>{const l=E({options:{ptSnapUnit:new x,isOpen:!1}});return{tab:"栅格和捕捉",props:l,component:()=>w(ee,l)}},te=l=>{const e=D([]),s=D(()=>{});return R(()=>{const a=ne(),m=oe(),c=ae();e.value=[a,m,c],V(()=>_.GRIDMODE,p=>{c.props.options.isOpen=p===r.On}),V(()=>_.AUTOSNAP,p=>{m.props.options.isOpen=p===r.On}),V(()=>_.OSMODE,p=>{a.props.options.isOpen=p===r.On}),V(l,p=>{p&&(c.props.options.isOpen=b("GRIDMODE")===r.On,c.props.options.ptSnapUnit=O().getSysVarPoint("SNAPUNIT"),m.props.options.isOpen=b("AUTOSNAP")===r.On,m.props.options.polarang=Math.round(O().getSysVarDouble("POLARANG")*(180/Math.PI)),a.props.options.isOpen=b("OSMODE")===r.On)}),s.value=()=>{let p;a.props.data.forEach(({check:k,type:y})=>{p=B(y,k,p)}),typeof p<"u"&&j(p),P("GRIDMODE",c.props.options.isOpen?r.On:r.Off),P("AUTOSNAP",m.props.options.isOpen?r.On:r.Off),O().setSysVarPoint2d("SNAPUNIT",c.props.options.ptSnapUnit),O().setSysVarDouble("POLARANG",m.props.options.polarang*(Math.PI/180)),P("OSMODE",a.props.options.isOpen?r.On:r.Off)}}),{items:e,define:s}},se={class:"px-3"},ke=M({__name:"index",setup(l){const e=D(0),{isShow:s,showDialog:a}=F(!1,"showDraftingSettingsDialog",(k={})=>{a(),k.tab&&(e.value=k.tab)}),{items:m,define:c}=te(s),p=[{name:"确定",fun:()=>{c.value(),a(!1)},primary:!0},{name:"关闭",fun:()=>a(!1)}];return(k,y)=>(S(),A($,{title:k.t("523"),modelValue:v(s),"onUpdate:modelValue":y[1]||(y[1]=C=>Z(s)?s.value=C:null),footerBtnList:p,"max-width":"600"},{default:o(()=>[G("div",se,[n(z,{items:v(m),modelValue:e.value,"onUpdate:modelValue":y[0]||(y[0]=C=>e.value=C),height:312,tabsProps:{grow:!0}},null,8,["items","modelValue"])])]),_:1},8,["title","modelValue"]))}});export{ke as default};
