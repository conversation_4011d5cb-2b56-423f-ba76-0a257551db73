import request from "@/utils/request.js";

export function getTemplate(params) {
    return request({
        url: '/templateData/getTemplate',
        method: 'get',
        params,
    })
}

export function uploadTemplate(data) {
    return request({
        url: '/templateData/uploadTemplate',
        method: 'post',
        data,
        headers: {
            "Content-Type": "multipart/form-data",
        },
    })
}


export function updateTemplateStatus(params) {
    return request({
        url: '/templateData/updateTemplateStatus',
        method: 'get',
        params,
    })
}

export function downTemplate(params) {
    return request({
        url: '/templateData/downTemplate',
        method: 'get',
        params,
        responseType: 'blob'
    })
}

export function deleteTemplate(params) {
    return request({
        url: '/templateData/deleteTemplate',
        method: 'get',
        params,
    })
}

export function updateTemplate(data) {
    return request({
        url: '/templateData/updateTemplate',
        method: 'post',
        data,
    })
}