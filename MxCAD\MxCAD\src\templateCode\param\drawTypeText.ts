import { MxCpp, McCmColor } from "mxcad";

function Mx_Test_TrueText() {
    let mxcad = MxCpp.getCurrentMxCAD();
    //清空当前显示内容
    mxcad.newFile();
    MxCpp.App.loadFonts([], [], ["syadobe", "simsun"], () => {

        // 添加一个黑体文字样式
        mxcad.AddTureTypeTextStyle("ht_style", "syadobe");

        // 添加一个宋体文字样式
        mxcad.AddTureTypeTextStyle("st_style", "simsun");

        // 设置当前样式为黑体
        mxcad.drawTextStyle = "ht_style";

        mxcad.drawColor = new McCmColor(200, 255, 50);
        mxcad.drawText(0, 3500, "绘图控件TrueType文字测试1", 100, 0, 0, 1);

        // 设置当前样式为黑体
        mxcad.drawTextStyle = "st_style";
        mxcad.drawColor = new McCmColor(200, 100, 0);
        mxcad.drawText(0, 3200, "绘图控件TrueType文字测试2", 100, 0, 0, 1);

        mxcad.zoomAll();
        mxcad.regen();
        mxcad.updateDisplay();
    });
};

// 调用画多种文字样式的文字的方法
Mx_Test_TrueText()