import{Q as V,Z as p,V as c,U as g}from"./index-CzBriCFR.js";import{M as w}from"./index-itnQ6avM.js";import{B as t,L as o,b as r,I as d,z as u,G as v,H as _}from"./vuetify-BqCp6y38.js";import{h as y,a3 as B,a4 as e,u as D,B as C,_ as j,a0 as a,m as l,V as n,Q as k}from"./vue-Cj9QYd7Z.js";import"./mxcad-DrgW2waE.js";import"./mxdraw-BQ5HhRCY.js";import"./handsontable-Ch5RdAT_.js";import"./mapbox-gl-DQAr7S0z.js";const F={class:"px-3"},M={class:""},R={class:""},X={class:"mt-1 mb-4",style:{"margin-right":"42px"}},I={class:""},N={class:""},S={class:""},G={class:"w-100 d-flex justify-space-between"},L={class:""},P={class:""},Q={class:""},T={class:""},U={class:"d-flex flex-column justify-center align-center"},z={class:"text-center"},W=y({__name:"index",setup(A){const{isShow:i,showDialog:m}=V(!1,"ExportPdf"),h=[{name:"开始转换",fun:()=>{},primary:!0},{name:"取消",fun:()=>m(!1)}];return(s,f)=>(j(),B(w,{modelValue:D(i),"onUpdate:modelValue":f[0]||(f[0]=b=>C(i)?i.value=b:null),footerBtnList:h,"max-width":"450",title:"CAD转PDF"},{default:e(()=>[a("div",F,[l(u,{class:"mt-1"},{default:e(()=>[l(t,{cols:"8"},{default:e(()=>[l(o,null,{prepend:e(()=>[a("span",M,n(s.t("484"))+":",1)]),append:e(()=>[l(p,null,{default:e(()=>[l(r,{icon:"class:iconfont more"})]),_:1})]),_:1})]),_:1}),l(t,{cols:"4"},{default:e(()=>[l(d,{class:"ml-2",label:s.t("485")},null,8,["label"])]),_:1})]),_:1}),l(u,null,{default:e(()=>[l(t,null,{default:e(()=>[l(o,null,{prepend:e(()=>[a("span",R,n(s.t("486"))+":",1)]),append:e(()=>[l(p,null,{default:e(()=>[l(r,{icon:"class:iconfont more"})]),_:1})]),_:1})]),_:1})]),_:1}),a("div",X,[l(u,null,{default:e(()=>[l(t,null,{default:e(()=>[l(o,null,{prepend:e(()=>[a("span",I,n(s.t("487"))+":",1)]),_:1})]),_:1})]),_:1}),l(u,{justify:"space-between"},{default:e(()=>[l(t,{cols:"5"},{default:e(()=>[l(o,null,{prepend:e(()=>[a("span",N,n(s.t("488"))+":",1)]),_:1})]),_:1}),l(t,{cols:"5"},{default:e(()=>[l(o,null,{prepend:e(()=>[a("span",S,n(s.t("489"))+":",1)]),_:1})]),_:1})]),_:1}),l(u,{justify:"end","no-gutters":""},{default:e(()=>[l(t,{cols:"8"}),l(t,{cols:"4"},{default:e(()=>[l(d,{class:"",label:s.t("490")},null,8,["label"])]),_:1})]),_:1}),l(d,{class:"",label:s.t("491")},null,8,["label"]),l(d,{class:"mt-1",label:s.t("492")},null,8,["label"]),l(c,{title:s.t("493")},{default:e(()=>[l(v,{class:""},{default:e(()=>[a("div",G,[l(_,{label:s.t("494"),value:"1"},null,8,["label"]),l(_,{label:s.t("495"),value:"2"},null,8,["label"]),l(_,{label:s.t("496"),value:"3"},null,8,["label"])])]),_:1}),l(u,null,{default:e(()=>[l(t,{cols:"6"},{default:e(()=>[l(o,null,{prepend:e(()=>[a("span",L,n(s.t("497"))+"X1:",1)]),_:1})]),_:1}),l(t,{cols:"6"},{default:e(()=>[l(o,null,{prepend:e(()=>[a("span",P,n(s.t("497"))+"X1:",1)]),_:1})]),_:1})]),_:1}),l(u,null,{default:e(()=>[l(t,{cols:"6"},{default:e(()=>[l(o,null,{prepend:e(()=>[a("span",Q,n(s.t("497"))+"X1:",1)]),_:1})]),_:1}),l(t,{cols:"6"},{default:e(()=>[l(o,null,{prepend:e(()=>[a("span",T,n(s.t("497"))+"X1:",1)]),_:1})]),_:1})]),_:1})]),_:1},8,["title"]),l(c,{title:s.t("493")},{default:e(()=>[a("div",U,[a("p",z,n(s.t("498")),1),l(p,{class:"mt-2"},{default:e(()=>[l(g,{"key-name":"B"},{default:e(()=>[k(n(s.t("499"))+"F",1)]),_:1})]),_:1})])]),_:1},8,["title"])])])]),_:1},8,["modelValue"]))}});export{W as default};
