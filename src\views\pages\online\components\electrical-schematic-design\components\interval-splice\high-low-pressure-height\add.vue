<script setup>
import { downloadFileDWG } from "@/api/task/index.js";
import { useCAdApp } from "@/hooks/useCAdApp.js";
import {
  getListByVoltage,
  getMaterialsproject,
  getFaxzByWlId,
  getTxyl,
  getJgcs,
} from "@/api/insertSag/index.js";
import { useIframeCommunication } from "@/hooks/useIframeCommunication";
import { watch } from "vue";
const { sendMessage, cleanup } = useIframeCommunication();
const emit = defineEmits(["update"]);
const visible = defineModel("visible", { type: Boolean, default: false });
const props = defineProps({
  named: {
    type: String,
    required: true,
  },
});
const deviceTypeList = ref([]);
const materialTableData = ref([]);
const totalList = ref([]);
const cadIframeRef = ref(null);
const intervalList = ref([]);
const schemeData = ref([]);
const loading = ref(false);
const materialForm = reactive({
  dl: "",
  type: "",
});

const changeVoltage = (e) => {
  getListByVoltage(e).then((res) => {
    deviceTypeList.value = res.data;
    materialForm.type = res.data[0].materialstypekey;
    changeDevice();
  });
};

const changeDevice = () => {
  getMaterialsproject({
    materialstypekey: materialForm.type,
    voltage: materialForm.dl,
  }).then((res) => {
    materialTableData.value = res.data;
    handleRowClick(res.data[0].materialsprojectid);
  });
};

const { cadAppSrc } = useCAdApp();
const cadIframeSrc = cadAppSrc({
  isPreview: "1",
});
const getDwgFile = (data) => {
  const id = data.drawingid;
  if (!id) return;
  getTxyl(id).then((res) => {
    nextTick(() => {
      setTimeout(() => {
        const params = {
          type: "cadPreview",
          content: "Mx_cadPreview",
          formData: {
            openUrl: res,
            isFlags: true
          },
        };
        sendMessage(cadIframeRef.value, params, (res) => {});
      }, 2000);
    });
  });
};
const form = reactive({
  name: "",
});

const rules = reactive({});

const closeVisible = () => {
  visible.value = false;
  dialogList.value = [];
};
let historyList = ref([]);
const handleRowClick = async (row) => {
  // 
  loading.value = true;
  totalList.value = [
    {
      extensiondescription: row.extensiondescription,
      materialdescription: row.materialdescription,
      materialcodeerp: row.materialcodeerp,
    },
  ];
  const id = parseInt(row.materialsprojectid);
  // historyList.value = [];
  try {
    const res = await getFaxzByWlId(id);
    // 使用 for...of 循环正确处理异步
    // for (const item of res.data) {
    //   const jgcsRes = await getJgcs(item.intervalid);
    //   historyList.value.push(jgcsRes.data);
    // }
    schemeData.value = res.data;
    for (let i = 0; i < schemeData.value.length; i++) {
      const element = schemeData.value[i];
      for (let j = 0; j < changeNumList.value.length; j++) {
        const elements = changeNumList.value[j];
        if(element.intervalid===elements.intervalid){
          element.num=elements.num
        }
      }
    }
    console.log("🚀 ~ handleRowClick ~ schemeData.value:", schemeData.value)
    loading.value = false;
  } catch (error) {
    console.error("处理过程中出错:", error);
  }
};
let changeNumList = ref([]);
let dialogList = ref([]); //数据整合数组
const changeNum = (e) => {
  changeNumList.value=tChange(changeNumList.value,totalList.value,e)
    let arr =[]
    arr = findAndMerge(changeNumList.value, intervalList.value);
    dialogList.value.push(...arr)
    dialogList.value=replaceBYid(dialogList.value,...arr).filter(item=>item.num!=='')
};
const changeNum1 = (e) => {
  let arr =[]
    arr = findAndMerge(changeNumList.value, intervalList.value);
    dialogList.value.push(...arr)
    dialogList.value=replaceBYid(dialogList.value,...arr)
    console.log("🚀 ~ dialogList.value:", dialogList.value)

};
const tChange=(arr,list,e)=>{
  const existingIndex = arr.findIndex(
    item => item.intervalid === e.intervalid
  );
  const newItem = { ...list[0], ...e };
  const newArr=[...arr]
  if (existingIndex >= 0) {
    newArr[existingIndex] = newItem;
  } else {
    newArr.push(newItem);
  }
  return newArr
}
const replaceBYid=(arr,e)=>{
 return[e,...arr.filter(item =>item.intervalid!==e.intervalid )]
}
const handleSchemeClick = async (row) => {
  await getDwgFile(row);
  let jgcsRes
  let flag=false;
  if(dialogList.value.length>0){
    for(const item of dialogList.value){
      if(item.intervalid===row.intervalid){
        const arr=item.matched
        intervalList.value = arr;
        flag=true
        break;
      }
    }
  }else{
     jgcsRes = await getJgcs(row.intervalid);
     intervalList.value=jgcsRes.data
  }
  if(!flag){
    jgcsRes = await getJgcs(row.intervalid);
     intervalList.value=jgcsRes.data
  }
};
onMounted(() => {
  if (props.named === "1") {
    materialForm.dl = "10kV";
  } else if (props.named === "2") {
    materialForm.dl = "380V";
  }
  changeVoltage(materialForm.dl);
});
const onSubmit = () => {
  visible.value = false;
  let result=dialogList.value.reduce((acc,item)=>{
    return acc.concat(Array(parseInt(item.num)).fill(item))
  },[])
  console.log("🚀 ~ result ~ result:", result)
  emit("update", result);
  dialogList.value = [];
};

//处理方案和间隔参数----数据逻辑
function updateNestedArray(originalArray, newItem) {
  const aValue = newItem.intervalid; // 获取新对象的a值
  // 遍历原始数组
  for (let i = 0; i < originalArray.length; i++) {
    const subArray = originalArray[i];
    // 检查子数组中的对象是否有匹配的a值
    for (let j = 0; j < subArray.length; j++) {
      if (subArray[j].intervalid === aValue) {
        // 如果找到匹配的a值，覆盖整个子数组
        originalArray[i] = [newItem];
        return originalArray;
      }
    }
  }
  originalArray.push([newItem]);
  return originalArray;
}
//处理方案和间隔参数----数据逻辑
function findAndMerge(arr, list) {
  const result = [];

  // 遍历arr中的每个对象
  for (const obj of arr) {
    const matchedSubArray = [];
    const aValue = obj.intervalid;

    // 在list中查找包含相同a值的子数组
    for (const subList of list) {
      // const found = subList.some((item) => item.intervalid === aValue);
      if(subList.intervalid===aValue){
        matchedSubArray.push(subList);
      }
    }

    // 如果有匹配结果，创建新对象
    if (matchedSubArray.length > 0) {
      result.push({
        ...obj,
        matched: matchedSubArray,
      });
    } else {
      
    }
  }

  return result;
}
</script>

<template>
  <el-dialog
    v-model="visible"
    title="间隔拼接-间隔选择"
    width="60%"
    draggable
    overflow
    append-to-body
    @close="closeVisible"
  >
    <div class="">
      <el-row :gutter="20">
        <el-col :span="16">
          <div class="general-box">
            <div class="general-box-header">
              <div class="general-box-title">物料选择</div>
            </div>
            <div class="general-box-container">
              <el-form ref="formRef" :model="materialForm" :inline="true">
                <el-form-item label="电压等级" prop="">
                  <el-select
                    style="width: 200px"
                    v-model="materialForm.dl"
                    @change="changeVoltage"
                    placeholder="请选择"
                  >
                    <el-option
                      v-if="props.named == 1"
                      label="10kV"
                      value="10kV"
                    />
                    <el-option
                      v-if="props.named == 1"
                      label="20kV"
                      value="20kV"
                    />
                    <el-option
                      v-if="props.named == 2"
                      label="380V"
                      value="380V"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="设备类别" prop="">
                  <el-select
                    v-model="materialForm.type"
                    @change="changeDevice"
                    placeholder="请选择"
                    style="width: 200px"
                  >
                    <el-option
                      v-for="item in deviceTypeList"
                      :key="item.materialstypekey"
                      :label="item.materialtypename"
                      :value="item.materialstypekey"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-form>
              <el-table
                @row-click="handleRowClick"
                highlight-current-row
                :data="materialTableData"
                border
                stripe
                height="300px"
                style="width: 100%"
              >
                <el-table-column
                  type="index"
                  label=""
                  align="center"
                  width="50"
                />
                <el-table-column
                  prop="materialcodeerp"
                  label="物料编码"
                  align="center"
                  width=""
                />
                <el-table-column
                  prop="materialdescription"
                  label="物料描述"
                  align="center"
                  width=""
                />
                <el-table-column
                  prop="extensiondescription"
                  label="扩展描述"
                  align="center"
                  width=""
                />
              </el-table>
            </div>
          </div>
          <div class="general-box">
            <div class="general-box-header">
              <div class="general-box-title">方案选择</div>
            </div>
            <div class="general-box-container">
              <el-table
                v-loading="loading"
                highlight-current-row
                :data="schemeData"
                @row-click="handleSchemeClick"
                border
                stripe
                height="300px"
                style="width: 100%"
              >
                <el-table-column
                  type="index"
                  label=""
                  align="center"
                  width="50"
                />
                <el-table-column
                  prop="intervalname"
                  label="方案名称"
                  align="center"
                  width=""
                />
                <el-table-column label="数量" align="center" width="120">
                  <template #default="scope">
                    <el-input
                      @change="changeNum(scope.row)"
                      type="number"
                      min="1"
                      v-model="scope.row.num"
                      placeholder="请输入数量"
                    ></el-input>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="general-box">
            <div class="general-box-header">
              <div class="general-box-title">图形预览</div>
            </div>
            <div style="height: 300px" class="general-box-container">
              <iframe
                ref="cadIframeRef"
                :src="cadIframeSrc"
                style="width: 100%; height: 300px"
              />
            </div>
          </div>
          <div class="general-box">
            <div class="general-box-header">
              <div class="general-box-title">间隔参数</div>
            </div>
            <div style="height: 350px" class="general-box-container">
              <el-form
                ref="formRef"
                :model="form"
                :rules="rules"
                label-width="130"
                v-for="item in intervalList"
              >
                <el-form-item :label="item.intervalparametername" prop="dl">
                  <el-input  @change="changeNum1(item)" v-model="item.intervalparametervalue" clearable>
                    <template v-if="item.dw" #append>{{ item.dw }}</template>
                  </el-input>
                </el-form-item>
              </el-form>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeVisible">取消</el-button>
        <el-button type="primary" @click="onSubmit">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss"></style>
