<template>
  <div>
    <p>应用正在运行在: {{ appUrl }}</p>
    <button @click="getPrinters">获取打印机列表</button>
    <ul>
      <li v-for="printer in printers" :key="printer.name">{{ printer.name }}</li>
    </ul>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';

// 用来存储打印机列表
const printers = ref([]);
const appUrl = ref('');

// 获取打印机列表的函数
const getPrinters = async () => {
  printers.value = await window.electron.getPrinters();
};

onMounted(() => {
  // 动态设置 URL（从主进程获取）
  appUrl.value = 'http://**************:18082/';
});
</script>
