import{d as _,c as z,w as oe,z as ue,R as de,h as ye,a0 as B,_ as W,$ as I,a1 as A,m as E,n as q,u as K,ab as H,U as J,B as ne,A as Ve,a3 as R,Q as M,V as N,F as ee,a4 as ie,a5 as ce}from"./vue-DfH9C9Rx.js";import{M as fe}from"./index-8X61wlK0.js";import{av as L,s as o,af as De,aw as O,C as ae,u as j,O as Ae,ax as _e,_ as me,ay as Te}from"./index-D95UjFey.js";import"./mxcad-CfPpL1Bn.js";import"./mapbox-gl-PN3pDJHq.js";import{i as Q,v as Ie,a as pe,V as Ne,a0 as Pe,a1 as Fe,c as Be,b as U,k as Me,a2 as Ue,m as je,l as Oe,S as ze,d as te}from"./vuetify-B_xYg4qv.js";import"./mxdraw-C_n_7lEs.js";import"./xlsx-Ck5MUZvf.js";const Re=navigator.platform.toLowerCase().includes("win")||navigator.userAgent.includes("Windows"),We=()=>{const l=_([]),p=_(new Map),r=_({isLoading:!1,hasChanges:!1,lastError:null}),f=e=>{const n=l.value.find(s=>s.id===e);if(!n)return!1;const t=L.isKeySuspended(e),a=n.isSuspended;return t!==a&&(console.warn("Shortcut suspension state mismatch:",{id:e,managerState:t,localState:a}),a?L.suspendKey(e):L.resumeKey(e)),!0},d=(e,n)=>{const t=Array.isArray(e)?e:[e],a=[];return l.value.forEach(s=>{if(n&&s.id===n||s.isSuspended)return;const y=[];Array.isArray(s.key)?y.push(...s.key):s.key&&y.push(s.key),Re&&s.winKey&&(Array.isArray(s.winKey)?y.push(...s.winKey):y.push(s.winKey));const k=(x,g)=>{const T={ctrlKey:!!x.ctrlKey,shiftKey:!!x.shiftKey,altKey:!!x.altKey,metaKey:!!x.metaKey,keyCode:x.keyCode},P={ctrlKey:!!g.ctrlKey,shiftKey:!!g.shiftKey,altKey:!!g.altKey,metaKey:!!g.metaKey,keyCode:g.keyCode},h=T.ctrlKey===P.ctrlKey&&T.shiftKey===P.shiftKey&&T.altKey===P.altKey&&T.metaKey===P.metaKey,D=T.keyCode===P.keyCode;if(T.keyCode==="*"||P.keyCode==="*")return h;const S=h&&D;return S&&console.debug("Key comparison details:",{key1:T,key2:P,modifiersMatch:h,keyCodeMatch:D}),S};t.some(x=>y.some(g=>{const T=k(x,g);return T&&console.debug("Conflict detected:",{new:{keyCode:x.keyCode,ctrlKey:!!x.ctrlKey,shiftKey:!!x.shiftKey,altKey:!!x.altKey,metaKey:!!x.metaKey},existing:{keyCode:g.keyCode,ctrlKey:!!g.ctrlKey,shiftKey:!!g.shiftKey,altKey:!!g.altKey,metaKey:!!g.metaKey},shortcut:{id:s.id,label:s.info.label,command:s.info.command}}),T}))&&a.push(s)}),a.length>0&&console.warn("Shortcut conflicts found:",{newKeys:t.map(s=>({keyCode:s.keyCode,ctrlKey:!!s.ctrlKey,shiftKey:!!s.shiftKey,altKey:!!s.altKey,metaKey:!!s.metaKey})),conflicts:a.map(s=>({id:s.id,label:s.info.label,command:s.info.command,key:Array.isArray(s.key)?s.key.map(y=>({keyCode:y.keyCode,ctrlKey:!!y.ctrlKey,shiftKey:!!y.shiftKey,altKey:!!y.altKey,metaKey:!!y.metaKey})):s.key?{keyCode:s.key.keyCode,ctrlKey:!!s.key.ctrlKey,shiftKey:!!s.key.shiftKey,altKey:!!s.key.altKey,metaKey:!!s.key.metaKey}:null}))}),a},C=async e=>{const n=l.value.findIndex(t=>t.id===e);n!==-1&&(l.value.splice(n,1),L.unregister(e),r.value.hasChanges=!0)},i=()=>{try{const e=L.getRegisteredShortcuts(),n=L.getSuspendedKeys();l.value=e.filter(t=>!!(t.info&&typeof t.info.label=="string"&&typeof t.info.command=="string"&&typeof t.info.category=="string"&&typeof t.info.description=="string")).map(t=>{p.value.set(t.id,{key:t.key,winKey:t.winKey,when:t.when,action:t.action,isNoPreventDefault:t.isNoPreventDefault,info:t.info});const a=n.includes(t.id);return{...t,isSuspended:a}});for(const t of l.value)t.isSuspended&&L.suspendKey(t.id);l.value.forEach(t=>{f(t.id)})}catch(e){console.error(o("754")+":",e),r.value.lastError=e}},u=async(e,n)=>{try{r.value.isLoading=!0;const t=l.value.findIndex(V=>V.id===e);if(t===-1)return;const a=p.value.get(e);if(!a)return;const{skipConflictCheck:s,...y}=n;if(!s&&y.key){if(Array.isArray(y.key)){if(y.key.length===0)throw new Error(o("755"));y.key.forEach((x,g)=>{if(!x.keyCode)throw new Error(`${o("756")}${o("757")}${o("758")} ${g+1} ${o("207")}${o("759")}`)})}else if(!y.key.keyCode)throw new Error(o("760"));const V=d(y.key,e);if(V.length>0){const x=V.map(g=>({label:g.info.label,command:g.info.command,key:Array.isArray(g.key)?g.key[0]:g.key}));throw console.warn("Shortcut conflict detected:",{newKey:y.key,conflicts:x}),new Error(`${o("761")}：${o("436")}${o("394")} "${V.map(g=>g.info.label).join('", "')}" ${o("762")}`)}}const k={...l.value[t],...y,action:a.action,when:a.when};l.value[t]=k,"isSuspended"in y?y.isSuspended?(L.unregister(e),L.suspendKey(e),console.debug("Suspended shortcut:",e)):(L.resumeKey(e),L.registerWithId(e,{key:k.key,winKey:k.winKey,when:a.when,action:a.action,isNoPreventDefault:k.isNoPreventDefault,info:a.info}),console.debug("Resumed and re-registered shortcut with original ID:",e)):k.isSuspended||await L.updateShortcut(e,{key:k.key,winKey:k.winKey,when:a.when,action:a.action,isNoPreventDefault:k.isNoPreventDefault,info:a.info}),r.value.hasChanges=!0,console.debug("Shortcut updated:",{id:e,isSuspended:k.isSuspended,key:k.key})}catch(t){throw r.value.lastError=t,console.error(o("763")+":",t),t}finally{r.value.isLoading=!1}},w=async()=>{try{for(const e of l.value)L.unregister(e.id);L.resumeAllKeys(),l.value=[];for(const[e,n]of p.value.entries())L.registerWithId(e,{key:n.key,winKey:n.winKey,when:n.when,action:n.action,isNoPreventDefault:n.isNoPreventDefault,info:n.info}),l.value.push({id:e,key:n.key,winKey:n.winKey,when:n.when,action:n.action,isNoPreventDefault:n.isNoPreventDefault,info:n.info,isSuspended:!1});localStorage.removeItem("mxcad-shortcuts"),localStorage.removeItem("mxcad-suspended-keys"),await i(),r.value.hasChanges=!0,console.debug("Reset to default completed, shortcuts count:",l.value.length)}catch(e){throw console.error(o("764")+":",e),e}},b=async e=>{try{r.value.isLoading=!0;const n=Math.max(...l.value.map(s=>s.id),0)+1,t={id:n,key:e.key,winKey:e.winKey,info:e.info,action:s=>{e.info.command&&De(e.info.command)},isSuspended:!1},a=d(e.key);if(a.length>0)throw new Error(`${o("761")}：${o("436")}${o("394")} "${a.map(s=>s.info.label).join('", "')}" ${o("762")}`);return L.registerWithId(n,{key:e.key,winKey:e.winKey,action:t.action,info:e.info}),l.value.push(t),p.value.set(n,{key:e.key,winKey:e.winKey,action:t.action,info:e.info}),r.value.hasChanges=!0,n}catch(n){throw r.value.lastError=n,console.error(o("765")+":",n),n}finally{r.value.isLoading=!1}},c=async e=>{const n=l.value.findIndex(t=>t.id===e);n!==-1&&(l.value[n]={...l.value[n],key:[],winKey:void 0},L.unregister(e),r.value.hasChanges=!0)};return i(),{shortcuts:l,operationState:r,updateShortcut:u,resetToDefault:w,validateShortcutState:f,checkConflict:d,removeShortcut:C,addShortcut:b,emptiedShortcut:c}},qe=l=>{const p=_(""),r=_("asc"),f=_("label"),d=(e,n)=>{if(!n)return!0;const t=n.toLowerCase().trim();if(!t)return!0;const a=Array.isArray(e.key)?e.key.map(s=>O(s).toLowerCase()):e.key?[O(e.key).toLowerCase()]:[];return e.info.label.toLowerCase().includes(t)||e.info.command.toLowerCase().includes(t)||(e.info.description?.toLowerCase()||"").includes(t)||a.some(s=>s.includes(t))},C=z(()=>{let e=[...l.value];return p.value&&(e=e.filter(n=>d(n,p.value))),e.sort((n,t)=>{const a=c(n,f.value),s=c(t,f.value),y=a.localeCompare(s);return r.value==="asc"?y:-y}),e}),i=z(()=>{const e=l.value.length,n=C.value.length;return{total:e,filtered:n,hasFilter:p.value!==""}}),u=e=>{f.value===e?r.value=r.value==="asc"?"desc":"asc":(f.value=e,r.value="asc")},w=()=>{p.value="",r.value="asc",f.value="label"},b=()=>{p.value=""},c=(e,n)=>{switch(n){case"label":return e.info.label.toLowerCase();case"command":return e.info.command.toLowerCase();case"category":return e.info.category.toLowerCase();default:return""}};return{searchText:p,sortOrder:r,sortBy:f,filteredShortcuts:C,searchStats:i,resetAll:w,resetFilter:b,toggleSort:u}},He=(l,p)=>{const r=_(null),f=_(!1);let d=null,C=!1;const i=async b=>{if(!(!r.value||!f.value||C))try{if(C=!0,b.preventDefault(),b.stopPropagation(),b.stopImmediatePropagation(),["Control","Shift","Alt","Meta","Escape"].includes(b.key)){b.key==="Escape"&&w();return}const c={keyCode:b.code,ctrlKey:b.ctrlKey,shiftKey:b.shiftKey,altKey:b.altKey,metaKey:b.metaKey},e=p(c,r.value.id);if(e.length>0){const n=ae(),t=e.map(a=>`"${a.info.label}"`).join("、");await n.open({title:o("761"),text:`${o("766")} "${O(c)}" ${o("767")}：
${t}`,defineTitle:o("229")});return}await l(r.value.id,{key:c,winKey:void 0}),w()}catch(c){console.error(o("763")+":",c),await ae().open({title:o("768"),text:o("763"),defineTitle:o("229")})}finally{C=!1}},u=b=>{d&&(d(),d=null),r.value=b,f.value=!0,C=!1;const c=n=>{f.value&&(n.preventDefault(),n.stopPropagation(),i(n))};document.addEventListener("keydown",c,!0);const e=n=>{const a=n.target.closest(".shortcut-input");a&&a.classList.contains("editing")?(n.preventDefault(),n.stopPropagation()):w()};document.addEventListener("click",e,!0),d=()=>{document.removeEventListener("keydown",c,!0),document.removeEventListener("click",e,!0),C=!1}},w=()=>{d&&(d(),d=null),r.value=null,f.value=!1,C=!1,setTimeout(()=>{C=!1},100)};return{editingShortcut:r,isEditing:f,handleKeyPress:i,startEdit:u,cancelEdit:w}},se=[{id:"all",name:o("全部"),icon:"$mdi-folder-multiple-outline",description:o("显示所有快捷键")},{id:"common",name:o("常用命令"),icon:"$mdi-star-outline",description:o("常用的基础操作命令")},{id:"draw",name:o("绘图命令"),icon:"$mdi-pencil-outline",description:o("绘制图形相关的命令")},{id:"edit",name:o("编辑命令"),icon:"$mdi-pencil-ruler",description:o("编辑修改图形的命令")},{id:"view",name:o("视图命令"),icon:"$mdi-eye-outline",description:o("视图操作相关的命令")},{id:"dim",name:o("标注命令"),icon:"$mdi-ruler",description:o("尺寸标注相关的命令")},{id:"custom",name:o("自定义命令"),icon:"$mdi-cog-outline",description:o("用户自定义的命令")}],Je=l=>{const p=_("all"),r=_(se),f=z(()=>{const c=new Map;return r.value.forEach(e=>{c.set(e.id,0)}),l.value.forEach(e=>{const n=e.info.category;c.set(n,(c.get(n)||0)+1)}),c.set("all",l.value.length),c}),d=c=>{c!=="all"&&f.value.get(c)===0||(p.value=c)},C=c=>r.value.find(e=>e.id===c),i=()=>{p.value="all"},u=c=>C(c)?.icon||se[0].icon,w=c=>C(c)?.name||c,b=c=>p.value==="all"?c:c.filter(e=>e.info.category===p.value);return{categories:r.value,currentCategory:p,categoryCounts:f,switchCategory:d,getCategoryInfo:C,getCategoryIcon:u,getCategoryName:w,filterByCategory:b,resetCategory:i}};function Qe(l,p,r=!1){let f=null,d=!0;return{exec:function(...u){const w=this;if(r&&d){l.apply(w,u),d=!1;return}f&&clearTimeout(f),f=setTimeout(()=>{l.apply(w,u),f=null,r&&(d=!0)},p)},cancel:()=>{f&&(clearTimeout(f),f=null),r&&(d=!0)}}}const Ge=(l,p)=>{const r=_(!1),f=_(!1),d=_({lastSyncTime:null,isSyncing:!1,syncError:null}),C=new Map,i=()=>{l.value.forEach(t=>{C.set(t.id,{when:t.when,action:t.action,isNoPreventDefault:t.isNoPreventDefault})})},{exec:u,cancel:w}=Qe(async()=>{try{d.value.isSyncing=!0,await e(),d.value.lastSyncTime=new Date}catch(t){d.value.syncError=t,console.error(o("773")+":",t)}finally{d.value.isSyncing=!1}},1e3),b=async()=>{try{f.value=!0;const t={version:"1.0.0",shortcuts:l.value.map(k=>({id:k.id,key:k.key,winKey:k.winKey,info:{label:k.info.label,command:k.info.command,category:k.info.category,description:k.info.description},isSuspended:!!k.isSuspended}))},a=new Blob([JSON.stringify(t,null,2)],{type:"application/json"}),s=URL.createObjectURL(a),y=document.createElement("a");y.href=s,y.download=`shortcuts-${new Date().toISOString().slice(0,10)}.json`,document.body.appendChild(y),y.click(),document.body.removeChild(y),URL.revokeObjectURL(s)}catch(t){console.error(o("774")+":",t),ae().open({title:o("768"),text:o("774"),defineTitle:o("229")})}finally{f.value=!1}},c=async()=>{try{r.value=!0;const t=document.createElement("input");t.type="file",t.accept=".json";const s=await(await new Promise((V,x)=>{t.onchange=g=>{const T=g.target.files;T&&T.length>0?V(T[0]):x(new Error("未选择文件"))},t.click()})).text(),y=JSON.parse(s);if(!y.version||!Array.isArray(y.shortcuts))throw new Error("无效的配置文件格式");for(const V of y.shortcuts)if(l.value.find(g=>g.id===V.id)){const g=C.get(V.id);g&&await p(V.id,{key:V.key,winKey:V.winKey,isSuspended:V.isSuspended,...g,skipConflictCheck:!0})}j().success("快捷键配置导入成功")}catch(t){console.error("导入快捷键配置失败:",t),j().error("导入快捷键配置失败")}finally{r.value=!1}},e=async()=>{try{d.value.isSyncing=!0;const t={version:"1.0.0",shortcuts:l.value.map(a=>({id:a.id,key:a.key,winKey:a.winKey,info:a.info,isSuspended:!!a.isSuspended}))};localStorage.setItem("mxcad-shortcuts",JSON.stringify(t));for(const a of l.value)a.isSuspended?await L.suspendKey(a.id):await L.resumeKey(a.id);for(const a of l.value){const s={key:a.key,winKey:a.winKey,when:a.when,action:a.action,isNoPreventDefault:a.isNoPreventDefault,info:a.info};await L.updateShortcut(a.id,s)}d.value.lastSyncTime=new Date,d.value.syncError=null}catch(t){throw d.value.syncError=t,console.error("保存快捷键配置失败:",t),j().error("保存快捷键配置失败"),t}finally{d.value.isSyncing=!1}},n=async()=>{try{const t=localStorage.getItem("mxcad-shortcuts");if(!t)return;const a=JSON.parse(t);for(const s of a.shortcuts)if(l.value.find(k=>k.id===s.id)){const k=C.get(s.id);k&&await p(s.id,{key:s.key,winKey:s.winKey,isSuspended:s.isSuspended,...k,skipConflictCheck:!0})}}catch(t){throw console.error("加载快捷键配置失败:",t),t}};return oe(l,()=>{},{deep:!0}),ue(async()=>{i(),await n()}),de(()=>{w()}),{syncState:d,exportToFile:b,importFromFile:c,saveToLocalStorage:e,loadFromLocalStorage:n,cancelAutoSave:w}},Xe=(l,p)=>Ge(l,p),Ye=(l,p)=>({suspendShortcut:async i=>{if(l.value.find(w=>w.id===i))try{await p(i,{isSuspended:!0}),L.suspendKey(i)}catch(w){throw console.error(o("769")+":",w),w}},resumeShortcut:async i=>{if(l.value.find(w=>w.id===i))try{await p(i,{isSuspended:!1}),L.resumeKey(i)}catch(w){throw console.error(o("770")+":",w),w}},suspendAllShortcuts:async()=>{try{for(const i of l.value)await p(i.id,{isSuspended:!0})}catch(i){throw console.error(o("771")+":",i),i}},resumeAllShortcuts:async()=>{try{for(const i of l.value)await p(i.id,{isSuspended:!1});L.resumeAllKeys()}catch(i){throw console.error(o("772")+":",i),i}}}),Ze={class:"pa-4"},et=ye({__name:"AddShortcutDialog",props:{checkConflict:{type:Function}},emits:["save"],setup(l,{expose:p,emit:r}){const{isShow:f,showDialog:d}=Ae(!1,"MxCAD_AddShortcut"),C=_(!1),i=_({label:"",command:"",key:""}),u=_({label:"",command:void 0,category:"custom",description:"",key:null}),w={label:[h=>!!h||o("命令名称不能为空")],command:[h=>!!h||o("命令不能为空")],key:[h=>!!h||o("快捷键不能为空")]},b=_(),c=_([]),e=_(!1),n=h=>{if(!h){c.value=[];return}const D=h.toLowerCase();c.value=_e.value.filter(S=>S.toLowerCase().includes(D)),e.value=!0},t=h=>{u.value.command=h,u.value.label||(u.value.label=h),e.value=!1},a=_(!1),s=z(()=>u.value.key?O(u.value.key):o("点击设置快捷键")),y=l,k=async h=>{if(!a.value)return;if(h.preventDefault(),h.stopPropagation(),["Control","Shift","Alt","Meta","Escape"].includes(h.key)){h.key==="Escape"&&(a.value=!1,document.removeEventListener("keydown",k,!0));return}const D={keyCode:h.code,ctrlKey:h.ctrlKey,shiftKey:h.shiftKey,altKey:h.altKey,metaKey:h.metaKey},S=y.checkConflict(D);S.length>0?(j().warning(`${o("快捷键冲突")}：${o("与")}${o("命令")} "${S.map(X=>X.info.label).join('", "')}" ${o("冲突")}`),u.value.key=null):u.value.key=D,a.value=!1,document.removeEventListener("keydown",k,!0)},V=()=>{if(i.value={label:"",command:"",key:""},!u.value.label){i.value.label=o("命令名称不能为空");return}if(!u.value.command){i.value.command=o("命令不能为空");return}if(!u.value.key){i.value.key=o("快捷键不能为空");return}P("save",{key:u.value.key,info:{label:u.value.label,command:u.value.command,category:u.value.category,description:u.value.description}}),g(),d(!1)},x=()=>{a.value=!0,document.addEventListener("keydown",k,!0)},g=()=>{a.value=!1,document.removeEventListener("keydown",k,!0),u.value={label:"",command:"",category:"custom",description:"",key:null},i.value={label:"",command:"",key:""},c.value=[],e.value=!1,C.value=!1},T=[{name:o("确定"),fun:V,primary:!0},{name:o("取消"),fun:()=>{g(),d(!1)}}],P=r;return p({showDialog:d}),(h,D)=>(B(),W(fe,{title:h.t("794"),modelValue:K(f),"onUpdate:modelValue":D[5]||(D[5]=S=>ne(f)?f.value=S:null),maxWidth:"600",footerBtnList:T},{default:I(()=>[A("div",Ze,[E(Fe,{onSubmit:J(V,["prevent"])},{default:I(()=>[E(Q,{modelValue:u.value.label,"onUpdate:modelValue":D[0]||(D[0]=S=>u.value.label=S),rules:w.label,placeholder:h.t("783"),required:"",class:"mb-3","error-messages":i.value.label},null,8,["modelValue","rules","placeholder","error-messages"]),E(Ie,{modelValue:u.value.command,"onUpdate:modelValue":[D[1]||(D[1]=S=>u.value.command=S),t],rules:w.command,placeholder:h.t("394"),required:"",class:"mb-3",ref_key:"commandInput",ref:b,items:c.value,"menu-props":{modelValue:e.value,maxHeight:200},"onUpdate:search":n,"error-messages":i.value.command},{item:I(({item:S,props:G})=>[E(pe,q(G,{title:S.raw,onClick:X=>t(S.raw)}),null,16,["title","onClick"])]),_:1},8,["modelValue","rules","placeholder","items","menu-props","error-messages"]),E(Ne,{modelValue:u.value.category,"onUpdate:modelValue":D[2]||(D[2]=S=>u.value.category=S),items:K(se).filter(S=>S.id!=="all"),"item-title":"name","item-value":"id",required:"",class:"mb-3"},null,8,["modelValue","items"]),E(Pe,{modelValue:u.value.description,"onUpdate:modelValue":D[3]||(D[3]=S=>u.value.description=S),label:h.t("784"),rows:"3",class:"mb-3"},null,8,["modelValue","label"]),E(Q,{"model-value":s.value,onClick:x,rules:w.key,class:H({editing:a.value}),placeholder:a.value?h.t("785"):h.t("793"),"error-messages":i.value.key,readonly:"",clearable:"","clear-icon":"$mdi-recovery","onClick:clear":D[4]||(D[4]=J(S=>u.value.key=null,["stop"]))},null,8,["model-value","rules","class","placeholder","error-messages"])]),_:1})])]),_:1},8,["title","modelValue"]))}}),tt=me(et,[["__scopeId","data-v-74bbef1d"]]),ot={class:"shortcut-dialog pa-4"},nt={class:"d-flex align-center mb-4"},at={class:"d-flex"},st={class:"d-flex align-center justify-center"},lt={class:"d-flex align-center justify-center"},rt={class:"text-center"},it={class:"text-center"},ct={class:"text-center",style:{width:"86px"}},ut={class:"text-center text-no-wrap"},dt={class:"text-center text-no-wrap text-medium-emphasis"},yt={class:"text-center"},ft={class:"d-flex align-center"},mt={class:"text-center text-caption text-medium-emphasis description-cell"},pt={class:"text-center"},vt={class:"d-flex justify-center"},gt=ye({__name:"index",setup(l){const{isShow:p,showDialog:r}=Te,{shortcuts:f,updateShortcut:d,resetToDefault:C,checkConflict:i,removeShortcut:u,addShortcut:w,emptiedShortcut:b}=We(),{categories:c,currentCategory:e,categoryCounts:n,switchCategory:t,getCategoryIcon:a,getCategoryName:s,filterByCategory:y,resetCategory:k}=Je(f),{searchText:V,sortOrder:x,sortBy:g,filteredShortcuts:T,searchStats:P,toggleSort:h,resetAll:D}=qe(f),{editingShortcut:S,isEditing:G,handleKeyPress:X,startEdit:Y,cancelEdit:Z}=He(d,i),{syncState:ht,exportToFile:ve,importFromFile:ge,saveToLocalStorage:he,loadFromLocalStorage:ke}=Xe(f,d),{suspendShortcut:we,resumeShortcut:Se}=Ye(f,d),Ke=z(()=>y(T.value));oe(p,async m=>{if(!m)try{Z()}catch($){console.error("关闭对话框清理失败:",$)}},{immediate:!0});const Ce=m=>{m.isSuspended?Se(m.id):we(m.id)},be=()=>{C(),D(),k()},le=_(!1),xe=[{name:o("确定"),fun:async()=>{try{await he(),le.value=!le.value,r(!1)}catch(m){console.error("保存快捷键配置失败:",m)}},primary:!0},{name:o("恢复系统默认"),fun:be},{name:o("导出配置"),fun:ve},{name:o("导入配置"),fun:ge},{name:o("取消"),fun:async()=>{try{await ke(),r(!1)}catch(m){console.error("加载快捷键配置失败:",m)}}}];oe(S,m=>{m&&Ve(()=>{const $=document.querySelector(".shortcut-input.editing input");$ instanceof HTMLElement&&$.focus()})}),ue(()=>{document.addEventListener("click",m=>{S.value&&!m.target.closest(".shortcut-input")&&Z()})}),de(()=>{try{Z()}catch(m){console.error("组件卸载清理失败:",m)}});const $e=m=>m.key?Array.isArray(m.key)?m.key.length>0?O(m.key[0]):"":O(m.key):"",re=_(null),Ee=async m=>{try{await w(m),j().success(o("添加快捷键成功"))}catch($){console.error("添加快捷键失败:",$),j().error($ instanceof Error?$.message:o("添加快捷键失败"))}};return(m,$)=>(B(),R(ee,null,[E(fe,{title:m.t("779"),modelValue:K(p),"onUpdate:modelValue":$[4]||($[4]=v=>ne(p)?p.value=v:null),maxWidth:"1000",footerBtnList:xe},{default:I(()=>[A("div",ot,[A("div",nt,[E(Q,{modelValue:K(V),"onUpdate:modelValue":$[0]||($[0]=v=>ne(V)?V.value=v:null),placeholder:m.t("780"),"prepend-inner-icon":"$mdi-magnify",clearable:"",class:"flex-grow-1"},null,8,["modelValue","placeholder"]),E(Be,{color:"primary",class:"ml-4",onClick:$[1]||($[1]=v=>re.value?.showDialog(!0))},{default:I(()=>[E(U,null,{default:I(()=>$[5]||($[5]=[M("$mdi-plus")])),_:1}),M(" "+N(m.t("781")),1)]),_:1})]),A("div",at,[E(Me,{density:"compact",nav:"",rounded:"lg",width:240,class:"mr-4"},{default:I(()=>[E(Ue,null,{default:I(()=>[M(N(m.t("782")),1)]),_:1}),(B(!0),R(ee,null,ie(K(c),v=>(B(),W(pe,{key:v.id,active:K(e)===v.id,onClick:F=>K(t)(v.id),class:H({"selected-category":K(e)===v.id}),rounded:"lg"},{prepend:I(()=>[E(U,{icon:K(a)(v.id),size:"small",color:K(e)===v.id?"primary":""},null,8,["icon","color"])]),default:I(()=>[E(je,{class:"d-flex align-center"},{default:I(()=>[M(N(m.t(K(s)(v.id)))+" ",1),E(Oe,{size:"x-small",class:"ml-auto",color:K(e)===v.id?"primary":"",variant:"flat"},{default:I(()=>[M(N(K(n).get(v.id)),1)]),_:2},1032,["color"])]),_:2},1024)]),_:2},1032,["active","onClick","class"]))),128))]),_:1}),E(ze,{class:"w-100",height:"300px"},{default:I(()=>[A("thead",null,[A("tr",null,[A("th",{class:"text-center sortable",onClick:$[2]||($[2]=v=>K(h)("label"))},[A("div",st,[M(N(m.t("783"))+" ",1),K(g)==="label"?(B(),W(U,{key:0,size:"small",class:"ml-1",icon:K(x)==="asc"?"$mdi-arrow-up":"$mdi-arrow-down",color:"primary"},null,8,["icon"])):ce("",!0)])]),A("th",{class:"text-center sortable",onClick:$[3]||($[3]=v=>K(h)("command"))},[A("div",lt,[M(N(m.t("394"))+" ",1),K(g)==="command"?(B(),W(U,{key:0,size:"small",class:"ml-1",icon:K(x)==="asc"?"$mdi-arrow-up":"$mdi-arrow-down"},null,8,["icon"])):ce("",!0)])]),A("th",rt,N(m.t("766")),1),A("th",it,N(m.t("784")),1),A("th",ct,N(m.t("607")),1)])]),A("tbody",null,[(B(!0),R(ee,null,ie(Ke.value,v=>(B(),R("tr",{key:v.id,class:H({suspended:v.isSuspended})},[A("td",ut,N(m.t(v.info.label)),1),A("td",dt,N(v.info.command),1),A("td",yt,[A("div",ft,[E(Q,{"model-value":$e(v),class:H(["shortcut-input mx-auto",{editing:K(S)===v,disabled:v.isSuspended}]),onClick:J(F=>K(Y)(v),["stop"]),onFocus:F=>K(Y)(v),readonly:!0,placeholder:K(S)===v?m.t("785"):"",clearable:"","clear-icon":"$mdi-recovery","onClick:clear":J(()=>{K(b)(v.id),K(Y)(v)},["stop"])},null,8,["model-value","onClick","onFocus","class","placeholder","onClick:clear"])])]),A("td",mt,[E(te,{text:v.info.description,location:"top","max-width":"300","open-delay":500},{activator:I(({props:F})=>[A("div",q({ref_for:!0},F,{class:"description-text"}),N(v.info.description),17)]),_:2},1032,["text"])]),A("td",pt,[A("div",vt,[E(te,{text:v.isSuspended?m.t("786"):m.t("787"),location:"top","open-delay":500},{activator:I(({props:F})=>[E(U,q({ref_for:!0},F,{icon:v.isSuspended?"$mdi-play":"$mdi-pause",size:"x-small",class:"v-icon--clickable mr-2",color:v.isSuspended?"warning":"",onClick:Le=>Ce(v)}),{default:I(()=>$[6]||($[6]=[])),_:2},1040,["icon","color","onClick"])]),_:2},1032,["text"]),E(te,{text:m.t("788"),location:"top","open-delay":500},{activator:I(({props:F})=>[E(U,q({ref_for:!0},F,{icon:"$mdi-close",size:"x-small",class:"v-icon--clickable",onClick:Le=>K(u)(v.id)}),null,16,["onClick"])]),_:2},1032,["text"])])])],2))),128))])]),_:1})])])]),_:1},8,["title","modelValue"]),E(tt,{ref_key:"addShortcutDialog",ref:re,onSave:Ee,checkConflict:K(i)},null,8,["checkConflict"])],64))}}),Et=me(gt,[["__scopeId","data-v-7e6f57fc"]]);export{Et as default};
