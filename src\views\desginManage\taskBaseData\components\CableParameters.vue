<template>
  <!--  10kV-->
  <div>
    <div class="item_title">
      <el-icon>
        <List/>
      </el-icon>
      <span>10kV</span>
    </div>
    <el-descriptions style="margin-top: 5px;width: 70%" border label-width="15%" :column="1">
      <el-descriptions-item label="常用电缆">
        <el-select
            v-model="baseData.CableSpec10KV"
            multiple
            collapse-tags
            collapse-tags-tooltip
            :max-collapse-tags="4"
            placeholder="请选择"
        >
          <el-option
              v-for="item in modelDLOption.CableSpec10KVOption"
              :key="item.moduleid"
              :label="item.modulename"
              :value="item.moduleid"
          />
        </el-select>
      </el-descriptions-item>
    </el-descriptions>
  </div>
  <!--  低压-->
  <div style="margin-top: 10px">
    <div class="item_title">
      <el-icon>
        <List/>
      </el-icon>
      <span>低压</span>
    </div>
    <el-descriptions style="margin-top: 5px;width: 70%" border label-width="15%" :column="1">
      <el-descriptions-item label="常用电缆">
        <el-select
            v-model="baseData.CableSpecLow"
            multiple
            collapse-tags
            collapse-tags-tooltip
            :max-collapse-tags="4"
            placeholder="请选择"
        >
          <el-option
              v-for="item in modelDLOption.CementTowerLowOption"
              :key="item.moduleid"
              :label="item.modulename"
              :value="item.moduleid"
          />
        </el-select>
      </el-descriptions-item>
    </el-descriptions>
  </div>
  <!--  电缆井参数-->
  <div style="margin-top: 10px">
    <div class="item_title">
      <el-icon>
        <List/>
      </el-icon>
      <span>电缆井参数</span>
    </div>
    <div style="padding: 8px;display: flex; align-items: center">
      <div style="display: flex; align-items: center;cursor:pointer" @click="tableDataDljAdd">
        <el-icon color="#50adaa">
          <CirclePlusFilled/>
        </el-icon>
        <div style="font-size: 14px">增加</div>
      </div>
      <el-divider direction="vertical"/>
      <div style="display: flex; align-items: center;cursor:pointer" @click="tableDataDljDel">
        <el-icon color="#e75f5f">
          <DeleteFilled/>
        </el-icon>
        <div style="font-size: 14px">删除</div>
      </div>
    </div>
    <el-table :data="tableDataDlj" border style="width: 100%;height: 30vh" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55"/>
      <el-table-column prop="MinQuantity" label="最小数量">
        <template #default="scope">
          <el-input v-model="scope.row.MinQuantity" type="number" style="width: 70%" placeholder="请输入"/>
        </template>
      </el-table-column>
      <el-table-column prop="MaxQuantity" label="最大数量">
        <template #default="scope">
          <el-input v-model="scope.row.MaxQuantity" type="number" style="width: 70%" placeholder="请输入"/>
        </template>
      </el-table-column>
      <el-table-column prop="ZhiXianWell" label="直线">
        <template #default="scope">
          <el-select
              v-model="scope.row.ZhiXianWell"
              placeholder="请选择"
          >
            <el-option
                v-for="item in modelDLOption.allWellOption"
                :key="item.moduleid"
                :label="item.modulename"
                :value="item.moduleid"
            />
          </el-select>
        </template>
      </el-table-column>
      <el-table-column prop="ZhuanJiaoWell" label="转角">
        <template #default="scope">
          <el-select
              v-model="scope.row.ZhuanJiaoWell"
              placeholder="请选择"
          >
            <el-option
                v-for="item in modelDLOption.allWellOption"
                :key="item.moduleid"
                :label="item.modulename"
                :value="item.moduleid"
            />
          </el-select>
        </template>
      </el-table-column>
      <el-table-column prop="SanTongWell" label="三通">
        <template #default="scope">
          <el-select
              v-model="scope.row.SanTongWell"
              placeholder="请选择"
          >
            <el-option
                v-for="item in modelDLOption.allWellOption"
                :key="item.moduleid"
                :label="item.modulename"
                :value="item.moduleid"
            />
          </el-select>
        </template>
      </el-table-column>
      <el-table-column prop="SiTongWell" label="四通">
        <template #default="scope">
          <el-select
              v-model="scope.row.SiTongWell"
              placeholder="请选择"
          >
            <el-option
                v-for="item in modelDLOption.allWellOption"
                :key="item.moduleid"
                :label="item.modulename"
                :value="item.moduleid"
            />
          </el-select>
        </template>
      </el-table-column>
    </el-table>
  </div>
  <!--其他参数-->
  <div style="margin-top: 10px">
    <div class="item_title">
      <el-icon>
        <List/>
      </el-icon>
      <span>其他参数</span>
    </div>
    <el-descriptions style="margin-top: 5px;width: 70%" border label-width="15%" :column="2">
      <el-descriptions-item label="电缆井间距(m)">
        <el-input v-model="baseData.WellSpacing" type="number" style="width: 70%" placeholder="请输入整数"/>
      </el-descriptions-item>
      <el-descriptions-item label="电缆井中间头间距(m)">
        <el-input v-model="baseData.MiddleHeadSpacing" type="number" style="width: 70%" placeholder="请输入整数"/>
      </el-descriptions-item>
    </el-descriptions>
  </div>
  <div style="margin-top: 20px">
    <el-alert title="注：系统给出均为默认参数，如需调整，请进行更改" type="error" :closable="false"/>
  </div>
</template>
<script setup>
import {getAllGridModuleWellData, getGridMaterialsCableData} from "@/api/taskBaseData/index.js";

const {proxy} = getCurrentInstance();

const baseData = defineModel('baseData', {type: Object, required: true});

const tableDataDlj = computed(() => {
  return baseData.value.CableWells
})
// 新增行数据
const newRow = reactive({
  "MinQuantity": '',
  "MaxQuantity": '',
  "ZhiXianWell": "",
  "ZhiXianWellText": null,
  "ZhuanJiaoWell": "",
  "ZhuanJiaoWellText": null,
  "SanTongWell": "",
  "SanTongWellText": null,
  "SiTongWell": "",
  "SiTongWellText": null,
  "BaJiaoSiTongWell": null,
  "BaJiaoSiTongWellText": null
});
const modelDLOption = ref({
  CableSpec10KVOption: [],
  CementTowerLowOption: [],
  allWellOption: []
})

// 电缆井选中的行
const selectedRows = ref([]);
// 电缆井处理勾选
const handleSelectionChange = (rows) => {
  selectedRows.value = rows;
};
// 电缆井新增
const tableDataDljAdd = () => {
  const newId = tableDataDlj.value.length + 1;
  console.log('tableDataDlj.value', tableDataDlj.value)
  console.log('newId', newId)
  tableDataDlj.value.push({id: newId, ...newRow});
}
// 电缆井删除
const tableDataDljDel = () => {
  selectedRows.value.forEach((row) => {
    const index = tableDataDlj.value.findIndex((item) => item.id === row.id);
    if (index !== -1) {
      tableDataDlj.value.splice(index, 1);
    }
  });
  selectedRows.value = []; // 清空已选中行
}

const getGridMaterialsCableFor10kv = () => {
  const params = {
    moduleTypeKey: '10DLDL,20DLDL'
  }
  getGridMaterialsCableData(params).then((res) => {
    modelDLOption.value.CableSpec10KVOption = res.data
  })
}
const getGridMaterialsCableForLow = () => {
  const params = {
    moduleTypeKey: 'DY_DLDL'
  }
  getGridMaterialsCableData(params).then((res) => {
    modelDLOption.value.CementTowerLowOption = res.data
  })
}


const getAllGridModuleWell = () => {
  getAllGridModuleWellData().then((res) => {
    modelDLOption.value.allWellOption = res.data
  })
}

onMounted(() => {
  getGridMaterialsCableFor10kv()
  getGridMaterialsCableForLow()
  getAllGridModuleWell()
})
</script>
<style scoped lang="scss">
.item_title {
  display: flex;
  align-items: center;
  color: #50adaa;
  font-size: 14px;
}
</style>
