<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="6">
        <el-tree
          :data="treeData"
          :props="defaultProps"
          class="tree-container"
          highlight-current
          node-key="id"
          @node-click="handleNodeClick"
        >

        </el-tree>
      </el-col>
      <el-col v-loading="loading"
              :span="18">
        <iframe
          :src="pdfViewerUrl"
          class="pdf-viewer"
          frameborder="0"
        />
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import {getFileList, previewFile} from "@/api/data-file-management/index.js";

const loading = ref(false);
// 树形数据（示例）
const treeData = ref([
  {
    id: 1,
    label: '用户手册',
    children: [
      { id: 11, label: '第一章 基础操作', pdf: 'manual-1.pdf' },
      { id: 12, label: '第二章 高级功能', pdf: 'manual-2.pdf' }
    ]
  }
]);
const getList = () => {
  const params = {
    fileModule: '2'
  }
  getFileList(params).then((res) => {
    treeData.value = res.data;
    if(res.data?.length) {
      currentId.value = res.data[0].id;
      getContentData();
    }
  });
};

const defaultProps = {
  children: 'children',
  label: 'fileName'
};

// PDF查看器路径
const pdfViewerUrl = ref()

const currentId = ref();
const handleNodeClick = (data) => {
    currentId.value = data.id;
    getContentData();
};

const getContentData = async () => {
  loading.value = true;
    const params = { fileId: currentId.value }
  previewFile(params).then(res=> {
    const openUrl = window.URL.createObjectURL(res)
    pdfViewerUrl.value = '.' + import.meta.env.VITE_PDF_URL + '?file=' + encodeURIComponent(openUrl)
    console.log('pdfViewerUrl',pdfViewerUrl.value)

  }).finally(()=> {
    loading.value = false;
  })

}
onMounted(() => {
  getList();
});
</script>

<style scoped>
.app-container {
  height: calc(100vh - 84px);
  padding: 20px;
}

.tree-container {
  border-right: 1px solid #e6e6e6;
  height: calc(100vh - 124px);
  overflow-y: auto;
}

.pdf-viewer {
  width: 100%;
  height: calc(100vh - 124px);
  background: #f5f5f5;
}
</style>