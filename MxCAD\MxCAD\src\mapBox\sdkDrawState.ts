class SDKDrawState {
    //当前绘制命令
    _currentDrawCMD: any
    //当前绘制SDK方法
    _currentDrawAPI: any
    //当前绘制状态
    _currentDrawState: boolean
    constructor(){
        this._currentDrawCMD = null
        this._currentDrawAPI = null
        this._currentDrawState = false
    }
    setCurrentDrawInfo(cmd, api, state: boolean){
        console.log(`当前sdk命令：${this._currentDrawCMD}，方法：${this._currentDrawAPI}，状态：${this._currentDrawState}`)
        console.log(`变成sdk命令：${cmd}，方法：${api}，状态：${state}`)

        this._currentDrawCMD = cmd
        this._currentDrawAPI = api
        this._currentDrawState = state
    }
    changeCurrentDrawInfo(api, state: boolean){
        console.log(`当前sdk命令：${this._currentDrawCMD}，方法：${this._currentDrawAPI}，状态：${this._currentDrawState}`)
        console.log(`变成：方法：${this._currentDrawAPI}，状态：${this._currentDrawState}`)
        
        this._currentDrawAPI = api
        this._currentDrawState = state
    }

    getSDKDrawInfo(){
        return { 
            drawCMD: this._currentDrawCMD,
            drawAPI: this._currentDrawAPI,
            drawState: this._currentDrawState  
        }
    }

    getSDKDrawState(){
        return this._currentDrawState
    }
}

export default new SDKDrawState()