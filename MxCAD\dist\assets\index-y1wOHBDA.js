import{d as v,h as I,z as P,a3 as U,a4 as C,u as x,B as E,_ as M,a0 as u,m as V,Q as $,V as _,H as N,W,$ as k,a5 as j,ac as K,F as O}from"./vue-Cj9QYd7Z.js";import{M as z}from"./index-itnQ6avM.js";import{r as Q,$ as Z,Q as q,a7 as G,s as f,e as H,ap as F,Z as J,aq as X,_ as Y}from"./index-CzBriCFR.js";import{Z as ee,G as te,M as B,a2 as S,e as ae,d as se}from"./mxcad-DrgW2waE.js";import{D as ne}from"./mxdraw-BQ5HhRCY.js";import{S as ie}from"./vuetify-BqCp6y38.js";import"./handsontable-Ch5RdAT_.js";import"./mapbox-gl-DQAr7S0z.js";const le=()=>{const b=v(0),h=Q(),{createLineType:D}=h,{currentLineType:T,lineTypeList:p}=Z(h),L=v([]),m=q(!1,"Mx_Linetype",async()=>{const t=new ee,a=new te;t.setKeyWords("[从文件加载线型(F)/设置全局比例(S)/设置默认比例(R)/修改对象比例(A)]"),t.setDynamicInputType(ne.kNoInput);const i=B.getCurrentMxCAD(),n=await t.go();if(n==="F"&&m.showDialog(!0,T.value),n==="S"){if(a.setMessage(S("LinearManagerDialog_LTSCALE",`输入比例<${i.getSysVarDouble("LTSCALE")}>`)),!await a.go())return;i.setSysVarDouble("LTSCALE",a.value())}if(n==="R"){if(a.setMessage(S("LinearManagerDialog_CELTSCALE",`输入比例<${i.getSysVarDouble("CELTSCALE")}>`)),!await a.go())return;i.setSysVarDouble("CELTSCALE",a.value())}if(n==="A"){const o=await ae.userSelect(`
选择修改线型比例的对象`),s=[];if(o.forEach(r=>{if(r.type===se.kMxCAD){const e=r.getMcDbEntity();e&&s.push(e)}}),s.length===0||(a.setMessage(S("LinearManagerDialog_Object_lineType",`输入比例<${s[0].linetypeScale}>`)),!await a.go()))return;s.forEach(r=>{r.linetypeScale=a.value()})}}),y=v("");return{lineTypeList:L,createLineType:D,index:b,filePath:y,parseTextLineTypes:t=>{const a=t.split(`
`),i=[];let n=null;for(let o=0;o<a.length;o++){const s=a[o].trim();if(s.startsWith("*")){n&&i.push(n);const r=s.split(","),e=r[0].slice(1).trim(),c=r.slice(1).join(",").trim(),l=a[o+1].trim().replace(/^A,/,"");n=D({name:e,appearance:c,explain:c,value:l}),o+=1}}return n&&i.push(n),i},addLineType:async t=>{let a=!1;const i=B.getCurrentMxCAD(),n=i.database.getLinetypeTable().get(t.name);if(n.isErase())a=!0;else{if(!await new Promise(e=>{G().open({title:"MxCAD",text:f("254")+" "+t.name+f("700")+","+f("701"),cancelTitle:f("212"),defineTitle:f("211"),cancel:()=>{e(!1)},define:()=>{e(!0)}})}))return;n.erase()}if(!i.addLinetypeEx(t.name,t.value||"","").isValid()){H().error(f("254")+":"+t.name+f("702")+"!");return}a&&p.value.push(t);const s=p.value.find(({name:r})=>t.name===r);s&&(s.value=t.value,s.explain=s.appearance=t.appearance),T.value=s||t},...m}},oe={class:"d-flex algin-center mt-3"},re={class:"mt-2"},ce={class:"w-100"},ue={class:"w-100"},pe={class:"text-left"},de={class:"text-left"},fe={class:"w-100"},me=["onClick"],ye={class:"text-left"},ge={class:"text-left"},_e=I({__name:"index",emits:["change"],setup(b,{expose:h,emit:D}){const{createLineType:T,lineTypeList:p,showDialog:L,isShow:m,index:y,filePath:g,parseTextLineTypes:w,addLineType:t,onReveal:a}=le(),i=()=>{t(p.value[y.value]),L(!1)},n=[{name:"确定",fun:i,primary:!0},{name:"取消",fun:()=>L(!1)}],o=v(),s=()=>{o.value&&o.value.click()},r=async e=>{const l=e.target.files;if(!l)return;const d=l[0],A=await X(d),R=await F(A);p.value=w(R),g.value=d.name};return P(async()=>{const e=new URL(""+new URL("mx-CeEsFOMF.lin",import.meta.url).href,import.meta.url).href,l=await(await fetch(e)).blob(),d=await F(l);g.value=e,p.value=w(d)}),a(e=>{y.value=p.value.findIndex(({name:c})=>e.name===c)}),h({showDialog:L}),(e,c)=>(M(),U(z,{title:e.t("线型加载或重载"),"max-width":"600",modelValue:x(m),"onUpdate:modelValue":c[1]||(c[1]=l=>E(m)?m.value=l:null),footerBtnList:n},{default:C(()=>[u("div",oe,[V(J,{onClick:s,class:"ml-1"},{default:C(()=>[$(_(e.t("文件"))+"(F)...",1)]),_:1}),N(u("input",{class:"form__inset w-100",disabled:!0,"onUpdate:modelValue":c[0]||(c[0]=l=>E(g)?g.value=l:null)},null,512),[[W,x(g)]])]),u("input",{type:"file",ref_key:"fillSelectEl",ref:o,onChange:r,style:{display:"none"},accept:".lin"},null,544),u("div",re,[u("p",null,_(e.t("可用线型")),1),V(ie,{class:"w-100",cellpadding:"20",height:"300"},{default:C(()=>[u("thead",ce,[u("tr",ue,[u("th",pe,_(e.t("线型")),1),u("th",de,_(e.t("说明")),1)])]),u("tbody",fe,[(M(!0),k(O,null,j(x(p),(l,d)=>(M(),k("tr",{key:l.id,class:K(x(y)===d?"active":""),onClick:A=>y.value=d,onDblclick:i},[u("td",ye,_(l.name),1),u("td",ge,_(l.appearance),1)],42,me))),128))])]),_:1})])]),_:1},8,["title","modelValue"]))}}),Me=Y(_e,[["__scopeId","data-v-9a7fa6b9"]]);export{Me as default};
