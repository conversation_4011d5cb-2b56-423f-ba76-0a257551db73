<template>
  <div class="app-container" style="background: white;">
    <div style=" overflow: auto">
      <el-collapse class="elCollapse" v-model="activeNames">
        <el-collapse-item name="1">
          <template #title>
            <div style="display: flex;align-items: center">
              <span>架空</span>
              <el-popover placement="right" :width="400" trigger="hover">
                <template #reference>
                  <el-icon style="margin-left: 5px" size="15">
                    <QuestionFilled/>
                  </el-icon>
                </template>
                <el-image
                    style="width: 100%; height: 250px"
                    src=""
                    :zoom-rate="1.2"
                    :max-scale="7"
                    :min-scale="0.2"
                    :preview-src-list="[]"
                    :initial-index="4"
                    fit="cover"
                />
              </el-popover>
            </div>
          </template>
          <el-descriptions style="margin-top: 10px" border :column="2" label-width="160px">
            <el-descriptions-item label="裕度(系数)">
              <el-input v-model="addForm.YuDu" size="small" style="width: 140px" placeholder="请输入"/>
            </el-descriptions-item>
            <el-descriptions-item label="沿墙支架距离">
              <el-input v-model="addForm.WallHolderDistance" size="small" style="width: 140px" placeholder="请输入"/>
            </el-descriptions-item>
            <el-descriptions-item label="杆号牌(杆用)">
              <el-select style="width: 500px" size="small" v-model="addForm.PoleNumber" placeholder="请选择">
                <el-option
                    v-for="item in allOptions.ghpGyOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                </el-option>
              </el-select>
            </el-descriptions-item>
            <el-descriptions-item label="杆号牌(塔用)">
              <el-select style="width: 140px" size="small" v-model="addForm.PoleTTNumber" placeholder="请选择">
                <el-option
                    v-for="item in allOptions.ghpTyOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                </el-option>
              </el-select>
            </el-descriptions-item>
            <!--                <el-descriptions-item label="接地引线设置" :span="2">-->
            <!--                  <el-select style="width: 500px" size="small" v-model="gcssqxqValue" placeholder="请选择">-->
            <!--                    <el-option-->
            <!--                      v-for="item in gcssqxqOptions"-->
            <!--                      :key="item.value"-->
            <!--                      :label="item.label"-->
            <!--                      :value="item.value">-->
            <!--                    </el-option>-->
            <!--                  </el-select>-->
            <!--                  <el-button style="margin-left: 10px" size="small" type="primary" round>图纸同步</el-button>-->
            <!--                </el-descriptions-item>-->
            <el-descriptions-item label="驱鸟器">
              <el-select style="width: 500px" size="small" v-model="addForm.QuNiaoDevice" placeholder="请选择">
                <el-option
                    v-for="item in allOptions.qnqOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                </el-option>
              </el-select>
            </el-descriptions-item>
            <el-descriptions-item label="驱鸟器间距">
              <el-input v-model="addForm.QuNiaoQiBiLi" size="small" style="width: 140px" placeholder="请输入"/>
            </el-descriptions-item>
            <el-descriptions-item label="驱鸟器风车">
              <el-select style="width: 500px" size="small" v-model="addForm.QuNiaoWindmills" placeholder="请选择">
                <el-option
                    v-for="item in allOptions.qnqFcOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                </el-option>
              </el-select>
            </el-descriptions-item>
            <el-descriptions-item label="驱鸟器风车间距">
              <el-input v-model="addForm.FangNiaoFCBiLi" size="small" style="width: 140px" placeholder="请输入"/>
            </el-descriptions-item>
            <el-descriptions-item label="防雷方案">
              <el-select style="width: 500px" size="small" v-model="addForm.FangLeiMokuai" placeholder="请选择">
                <el-option
                    v-for="item in allOptions.flFaOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                </el-option>
              </el-select>
            </el-descriptions-item>
            <el-descriptions-item label="防雷间距">
              <el-input v-model="addForm.FangLeiBiLi" size="small" style="width: 140px" placeholder="请输入"/>
            </el-descriptions-item>
            <el-descriptions-item label="底卡盘是否配置">
              <el-radio-group v-model="addForm.CementProductIsMatching">
                <el-radio :value="true">是</el-radio>
                <el-radio :value="false">否</el-radio>
              </el-radio-group>
            </el-descriptions-item>
          </el-descriptions>
        </el-collapse-item>
        <el-collapse-item name="2">
          <template #title>
            <div style="display: flex;align-items: center">
              <span>电缆余线</span>
              <el-icon style="margin-left: 5px" size="15">
                <QuestionFilled/>
              </el-icon>
            </div>
          </template>
          <el-descriptions style="margin-top: 10px" border :column="2" label-width="180px">
            <el-descriptions-item label="裕度(系数)">
              <el-input v-model="addForm.CableYuDu" size="small" style="width: 140px" placeholder="请输入"/>
            </el-descriptions-item>
            <el-descriptions-item label="中间余线(米)">
              <el-input v-model="addForm.ZhongJianTouYuXian" size="small" style="width: 140px" placeholder="请输入"/>
            </el-descriptions-item>
            <el-descriptions-item label="上杆余线(米, 不含杆高)">
              <el-input v-model="addForm.ShangGanYuXian" size="small" style="width: 140px" placeholder="请输入"/>
            </el-descriptions-item>
            <el-descriptions-item label="土建站房余线(米)">
              <el-input v-model="addForm.ChuXianYuXian" size="small" style="width: 140px" placeholder="请输入"/>
            </el-descriptions-item>
            <el-descriptions-item label="箱式站余线(米)">
              <el-input v-model="addForm.XiangBianYuXian" size="small" style="width: 140px" placeholder="请输入"/>
            </el-descriptions-item>
            <el-descriptions-item label="电缆井余线(米)">
              <el-input v-model="addForm.DianLanJingYuXian" size="small" style="width: 140px" placeholder="请输入"/>
            </el-descriptions-item>
          </el-descriptions>
        </el-collapse-item>
        <el-collapse-item name="3">
          <template #title>
            <div style="display: flex;align-items: center">
              <span>其他电缆配置</span>
              <el-icon style="margin-left: 5px" size="15">
                <QuestionFilled/>
              </el-icon>
            </div>
          </template>
          <el-descriptions style="margin-top: 10px" border :column="2" label-width="180px">
            <el-descriptions-item label="电缆墙间距(米)">
              <el-input v-model="addForm.WallSpacing" size="small" style="width: 140px" placeholder="请输入"/>
            </el-descriptions-item>
            <el-descriptions-item label="电缆挂钩间距(米)">
              <el-input v-model="addForm.PothookSpacing" size="small" style="width: 140px" placeholder="请输入"/>
            </el-descriptions-item>
            <el-descriptions-item label="电缆管枕(埋管)">
              <el-select style="width: 500px" size="small" v-model="addForm.DianLanGuanZhen" placeholder="请选择">
                <el-option
                    v-for="item in allOptions.dlgzOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                </el-option>
              </el-select>
            </el-descriptions-item>
            <el-descriptions-item label="电缆管枕间距(米/个)">
              <el-input v-model="addForm.DianLanGuanZhenSpacing" size="small" style="width: 140px"
                        placeholder="请输入"/>
            </el-descriptions-item>
            <el-descriptions-item label="警示牌(电缆沟)">
              <el-select style="width: 500px" size="small" v-model="addForm.JingShiPai" placeholder="请选择">
                <el-option
                    v-for="item in allOptions.jspOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                </el-option>
              </el-select>
            </el-descriptions-item>
            <el-descriptions-item label="警示牌间距(米/个)">
              <el-input v-model="addForm.JingShiPaiJianJu" size="small" style="width: 140px" placeholder="请输入"/>
            </el-descriptions-item>
            <el-descriptions-item label="标志桩(全部类型通道)">
              <el-select style="width: 500px" size="small" v-model="addForm.BiaoZhiGan" placeholder="请选择">
                <el-option
                    v-for="item in allOptions.bzzOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                </el-option>
              </el-select>
            </el-descriptions-item>
            <el-descriptions-item label="标志桩间距(米/个)">
              <el-input v-model="addForm.BiaoZhiGanJianJu" size="small" style="width: 140px" placeholder="请输入"/>
            </el-descriptions-item>
            <el-descriptions-item label="盖板(电缆沟)">
              <el-select style="width: 500px" size="small" v-model="addForm.GaiBan" placeholder="请选择">
                <el-option
                    v-for="item in allOptions.gbOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                </el-option>
              </el-select>
            </el-descriptions-item>
            <el-descriptions-item label="盖板尺寸">
              <div style="display: flex;align-items: center">
                <el-input v-model="addForm.GaiBanLength" size="small" style="width: 40px" placeholder="长"/>
                <span style="margin: 0 10px">*</span>
                <el-input v-model="addForm.GaiBanHeight" size="small" style="width: 40px" placeholder="宽"/>
              </div>
            </el-descriptions-item>
          </el-descriptions>
        </el-collapse-item>
      </el-collapse>
    </div>
    <div class="container-footer">
      <el-button :loading="submitLoading" @click="submit" type="primary">确定</el-button>
    </div>
  </div>
</template>

<script setup>
import {
  getInfoById,
  getDicts,
  addInfo,
  getModuleByTypeKeys,
  getProjectMaterialsByLX, getProjectMaterials, addEngineeringParame, getEngineeringParameter
} from "@/api/desginManage/GccsDialog.js";
import {ElMessage, ElMessageBox} from "element-plus";
import {getCalculationSettingList} from "@/api/config-management/parameter-calculation.js";
import {addSettingList} from "@/api/config-management/index.js";
const getData = () => {
  getCalculationSettingList({taskId:props.taskId?props.taskId:''}).then((res) => {
    addForm.value = JSON.parse(res.data.calculationSetting)
  })
}
const props = defineProps({
  taskId: {
    type: String,
    required: true,
  },
});
const activeNames = ref(['1'])

const gccsList = ref({
  TerrainJk: [],
  TerrainDx: [],
  Geological: [],
  HaulDistanceJk: [],
  HaulDistanceDl: [],
})
const addForm = ref({
  YuDu: '', // 裕度(系数)
  WallHolderDistance: '', // 沿墙支架距离
  PoleNumber: '', // 架空-杆号牌(杆用)
  PoleTTNumber: '', // 架空-杆号牌(塔用)
  QuNiaoDevice: '', // 架空-驱鸟器
  QuNiaoQiBiLi: '', // 架空-驱鸟器间距
  QuNiaoWindmills: '', // 架空-驱鸟器风车
  FangNiaoFCBiLi: '', // 架空-驱鸟器风车间距
  FangLeiMokuai: '', // 架空-防雷方案
  FangLeiBiLi: '', // 架空-防雷间距
  CementProductIsMatching: false, // 架空-底卡盘是否配置
  CableYuDu: '', // 电缆-裕度(系数)
  ZhongJianTouYuXian: '', // 电缆-中间余线
  ShangGanYuXian: '', // 电缆-上杆余线(米, 不含杆高)
  ChuXianYuXian: '', // 电缆-土建站房余线(米)
  XiangBianYuXian: '', // 电缆-箱式站余线(米)
  DianLanJingYuXian: '', // 电缆-电缆井余线(米)
  WallSpacing: '', // 其他-电缆墙间距(米)
  PothookSpacing: '', // 其他-电缆挂钩间距(米)
  DianLanGuanZhen: '', // 其他-电缆管枕(埋管)
  DianLanGuanZhenSpacing: '', // 其他-电缆管枕间距(米/个)
  JingShiPai: '', // 其他-警示牌(电缆沟)
  JingShiPaiJianJu: '', // 其他-警示牌间距(米/个)
  BiaoZhiGan: '', // 其他-标志桩(全部类型通道)
  BiaoZhiGanJianJu: '', // 其他-标志桩间距(米/个)
  GaiBan: '', // 其他-盖板(电缆沟)
  GaiBanLength: '', // 其他-盖板(电缆沟)-长
  GaiBanHeight: '', // 其他-盖板(电缆沟)-高
})
const allOptions = ref({
  ghpGyOptions: [],
  ghpTyOptions: [],
  qnqOptions: [],
  qnqFcOptions: [],
  flFaOptions: [],
  dlgzOptions: [],
  jspOptions: [],
  bzzOptions: [],
  gbOptions: [],
})
const visible = ref({
  jkdxVisible: false,
  dldxVisible: false,
  dzVisible: false,
  jkyjVisible: false,
  dlyjVisible: false,
  qxqVisible: false,
})
const whdjOptions = ref([
  {label: 'a级', value: 'a级'},
  {label: 'b级', value: 'b级'},
  {label: 'c级', value: 'c级'},
  {label: 'd级', value: 'd级'},
  {label: 'e级', value: 'e级'},
])
const gcssqxqOptions = ref([
  {label: 'xxxx', value: 'xxxx'}
])
// 防雷装置数据获取
const getModuleByTypeKeysFun = () => {
  getModuleByTypeKeys({moduleTypeKey: 'FLYXJD'}).then(res => {
    allOptions.value.flFaOptions = res.data.map((item) => ({
      label: item.moduleName,
      value: item.moduleId,
    }))
    allOptions.value.flFaOptions.unshift({
      label: '不配置',
      value: '不配置'
    })
  })
}
// 获取杆号牌杆用, 杆号牌塔用数据获取  (杆号牌杆用, 杆号牌塔用)
const getProjectMaterialsByLXFun = (text) => {
  return getProjectMaterialsByLX({value: text}).then(res => {
    let resrult = []
    text.split(',').forEach((item1) => {
      resrult = res.data[item1].map((item) => ({
        label: item.materialname,
        value: item.materialsprojectid,
      }))
    })
    return resrult
  })
}
// QNQ(驱鸟器), QNFC(驱鸟风车), DlGZ(电缆管枕), JSP(警示牌), BZZ(标志桩), GB(盖板)
const getProjectMaterialsFun = (materialsTypekey) => {
  return getProjectMaterials({materialsTypeKey: materialsTypekey}).then(res => {
    let resrult = []
    let arr=[]
    materialsTypekey.split(',').forEach((item1) => {
      if(res.data[item1]){
        arr=res.data[item1].map((item) => ({
        label: item.materialname,
        value: item.materialsprojectid,
        name:item1
      }))
        resrult.push(...arr)
      }
    })
    return resrult
  })
}

const submitFormDialog = () => {
};

const handleClickGccs = (value) => {
  visible.value.jkdxVisible = false
  visible.value.dldxVisible = false
  visible.value.dzVisible = false
  visible.value.jkyjVisible = false
  visible.value.dlyjVisible = false
  visible.value.qxqVisible = false
  getModuleByTypeKeysFun() // 防雷装置数据
  // 杆号牌杆用,杆号牌塔用
  getProjectMaterialsByLXFun('杆号牌杆用,杆号牌塔用').then(option => {
    allOptions.value.ghpGyOptions = option['杆号牌杆用']
    if(allOptions.value.ghpGyOptions){
      allOptions.value.ghpGyOptions.unshift({
        label: '不配置',
        value: '不配置'
      })
    }else{
      allOptions.value.ghpGyOptions = [{label: '不配置', value: '不配置'}]
    }
    allOptions.value.ghpTyOptions = option['杆号牌塔用']
    if(allOptions.value.ghpTyOptions){
      allOptions.value.ghpTyOptions.unshift({
        label: '不配置',
        value: '不配置'
      })
    }else{
      allOptions.value.ghpTyOptions = [{label: '不配置', value: '不配置'}]
    }
  })
  getProjectMaterialsFun('GB,QNQ,QNFC,DlGZ,JSP,BZZ').then(option => {
  option.forEach(item=>{
    if(item.name&&item.name === 'QNQ'){
      allOptions.value.qnqOptions.push(item)
    }
    if(item.name&&item.name === 'QNFC'){
      allOptions.value.qnqFcOptions.push(item)
    }
if(item.name&&item.name === 'DlGZ'){
  allOptions.value.dlgzOptions.push(item)
}
if(item.name&&item.name === 'JSP'){
      allOptions.value.jspOptions.push(item)
    }
    if(item.name&&item.name === 'BZZ'){
      allOptions.value.bzzOptions.push(item)
    }
if(item.name&&item.name === 'GB'){
  allOptions.value.gbOptions.push(item)
}
  })


    if(allOptions.value.qnqOptions){
      allOptions.value.qnqOptions.unshift({
        label: '不配置',
        value: '不配置'
      })
    }else{
      allOptions.value.gbOptions = [{label: '不配置', value: '不配置'}]
    }
    if(allOptions.value.qnqFcOptions){
      allOptions.value.qnqFcOptions.unshift({
        label: '不配置',
        value: '不配置'
      })
    }else{
      allOptions.value.qnqFcOptions = [{label: '不配置', value: '不配置'}]
    }

    if(allOptions.value.dlgzOptions){
      allOptions.value.dlgzOptions.unshift({
        label: '不配置',
        value: '不配置'
      })
    }else{
      allOptions.value.dlgzOptions = [{label: '不配置', value: '不配置'}]
    }

    if(allOptions.value.jspOptions){
      allOptions.value.jspOptions.unshift({
        label: '不配置',
        value: '不配置'
      })
    }else{
      allOptions.value.jspOptions = [{label: '不配置', value: '不配置'}]
    }

    if(allOptions.value.bzzOptions){
      allOptions.value.bzzOptions.unshift({
        label: '不配置',
        value: '不配置'
      })
    }else{
      allOptions.value.bzzOptions = [{label: '不配置', value: '不配置'}]
    }

    if(allOptions.value.gbOptions){
      allOptions.value.gbOptions.unshift({
        label: '不配置',
        value: '不配置'
      })
    }else{
      allOptions.value.gbOptions = [{label: '不配置', value: '不配置'}]
    }
   
  })
}

handleClickGccs()
const getListDicts = () => {
  // 获取字典数据
  getDicts({"dictionarKeys": ["Altitude", "FilthLevel", "Terrain", "Geological", "PowerLoad", "AntiThunderLevel", "HaulDistance"]}).then(res => {
    if (res.code === 200 && res.data) {
      gccsList.value = res.data
      gccsList.value.HaulDistanceJk = res.data.HaulDistance
      gccsList.value.HaulDistanceDl = res.data.HaulDistance
      gccsList.value.TerrainJk = res.data.Terrain
      gccsList.value.TerrainDl = res.data.Terrain
      gccsList.value.TerrainDx = res.data.Terrain
    }
  })
}

const submitLoading = ref(false);
const submit = () => {
  console.log('addForm.value', addForm.value)
  submitLoading.value = true
  const params = {
    calculationSetting: JSON.stringify(addForm.value),
  }
  addSettingList(params).then(res => {
    if (res.code === 200) {
      ElMessage.success(res.msg)
    } else {
      ElMessage.error(res.msg)
    }
  }).finally(() => {
    submitLoading.value = false
  })
}
// 在组件挂载时获取数据
onMounted(() => {
  getData()
  // getListDicts()
})
</script>
<style lang="scss" scoped>
.container-footer {
  border-top: 1px solid var(--el-border-color);
  padding: 10px 0;
  text-align: right;
}
</style>
