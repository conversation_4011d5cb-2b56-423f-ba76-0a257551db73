<template>
  <div class="app-container">
    <el-form v-show="showSearch" ref="queryRef" :inline="true" :model="queryParams">
      <el-form-item label="模板名称" prop="name">
        <el-input
            v-model="queryParams.name"
            clearable
            placeholder="请输入名称"
            style="width: 200px"
            @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button icon="Search" type="primary" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
<!--        <el-button
            icon="Plus"
            plain
            type="primary"
            @click="handleAdd"
        >新增</el-button>-->
      </el-col>
      <el-col :span="1.5">
        <el-upload
            ref="uploadRef"
            :http-request="uploadChange"
            :before-upload="beforeAvatarUpload"
            :limit="1"
            :show-file-list="false"
            accept=".zip"
            action="#"
        >
          <el-button
              icon="UploadFilled"
              plain
              type="primary"
          >导入</el-button>
        </el-upload>
      </el-col>
    </el-row>

    <el-table
        v-loading="loading"
        :data="tableData"
    >
      <el-table-column align="center" label="模板名称" prop="template" width="260"></el-table-column>
      <el-table-column align="center" label="模板内容" prop="xmlData"	 show-overflow-tooltip width=""></el-table-column>
      <el-table-column align="center" label="是否启用" prop="isUse" width="100">
        <template #default="scope">
          <el-switch
              v-model="scope.row.isUse"
              :active-value="0"
              :inactive-value="1"
              @change="handleStatusChange(scope.row)"
          />
        </template>
      </el-table-column>

      <el-table-column align="center" label="操作" prop="isUse" width="250">
        <template #default="scope">
          <el-button text type="primary" @click="handleDownload(scope.row)">下载</el-button>
          <el-button text type="primary" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button text type="danger" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>

    </el-table>
    <DialogGccs v-if="isDialogVisibleGc" :id="currentId" v-model="isDialogVisibleGc" :getEngineeringList="getEngineeringList" title="工程参数设置" @cancelDialog="cancelDialog" @getList="getList"></DialogGccs>

  </div>
</template>

<script name="EngineeringParameterTemplate" setup>
import DialogGccs from './components/DialogGccs.vue'
import {
  deleteTemplate,
  downTemplate,
  getTemplate,
  updateTemplateStatus,
  uploadTemplate
} from "@/api/engineering-parameter-template/index.js";
import {blobValidate} from "@/utils/ruoyi.js";
import {ElMessage} from "element-plus";

const { proxy } = getCurrentInstance();

const tableData = ref([]);
const loading = ref(true);
const showSearch = ref(true);

const data = reactive({
  form: {},
  queryParams: {
    name: undefined,
  },
  rules: {},
});

const { queryParams, form, rules } = toRefs(data);

/** 查询部门列表 */
function getList() {
  loading.value = true;
  getTemplate(queryParams.value).then(response => {
    tableData.value = response.data
    loading.value = false;
  });
}


/** 搜索按钮操作 */
function handleQuery() {
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

const uploadRef = ref()
function beforeUpload(file) {
  if (file.type.indexOf("image/") == -1) {
    proxy.$modal.msgError("文件格式错误，请上传图片类型,如：JPG，PNG后缀的文件。");
  } else {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      options.img = reader.result;
      options.filename = file.name;
    };
  }
}

const uploadChange = async (e) => {
  try {
    if (e.file.size / 1024 / 1024 > 50) {
      ElMessage.error('文件大小限制50Mb')
      return false
    }
    const formData = new FormData();
    formData.append("file", e.file);
    const res = await uploadTemplate(formData)
    if(res.code === 200) {
      proxy.$modal.msgSuccess("上传成功");
      getList();
    } else {
      proxy.$modal.msgError(res.msg);
    }
  }
  catch (e) {

  }
  finally {
    uploadRef.value.clearFiles()
  }
}

/** 新增按钮操作 */
function handleAdd() {
  isDialogVisibleGc.value = true
}

const getEngineeringList = ref({})
const isDialogVisibleGc = ref(false)


const handleStatusChange = (row) => {
  const params = {
    id: row.id,
    status: row.isUse
  }
  updateTemplateStatus(params).then(res=> {
    if(res.code === 200) {
      proxy.$modal.msgSuccess("修改成功");
      getList();
    } else {
      proxy.$modal.msgError(res.msg);
    }
  })
}

const beforeAvatarUpload=(rawFile)=>{
  if(rawFile.size / 1024 / 1024 > 10) {
    proxy.$modal.msgWarning('上传载文件大小不能超过10M');
    return false
  }
  return true
}

const currentId = ref('')
const handleEdit = (row) => {
  if(!row.jsonData) return
  currentId.value = row.id
  getEngineeringList.value = JSON.parse(row.jsonData)
  console.log(data)
  isDialogVisibleGc.value = true
}

const cancelDialog = () => {
  currentId.value = ''
  isDialogVisibleGc.value = false
}

const handleDownload = (row) => {
  const {id, template} = row
  downTemplate({id}).then(res => {
    const isBlob = blobValidate(res);
    if (isBlob) {
      const blob = new Blob([res])
      saveAs(blob, `${template}.xml`)
    }
  })
}


/** 删除按钮操作 */
function handleDelete(row) {
  proxy.$modal.confirm('是否确认删除名称为"' + row.template + '"的数据项?').then(function() {
    return deleteTemplate({id: row.id});
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

getList();
</script>
