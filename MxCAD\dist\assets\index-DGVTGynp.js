import{f as k,Q as _,e as A,s as v,Y as B,U as C,Z as q,V as c,a as J}from"./index-CzBriCFR.js";import{M as ee}from"./index-itnQ6avM.js";import{M as E,h as F,y as G,k as te,a as le}from"./mxcad-DrgW2waE.js";import{M as z,h as ae}from"./mxdraw-BQ5HhRCY.js";import{g as se,b as oe,B as j,K as ne,I as N,L as w,$ as ie,z as ue}from"./vuetify-BqCp6y38.js";import{h as de,d as U,r as Z,a3 as re,a4 as s,u as f,B as P,_ as me,a0 as m,m as a,Q as g,V as p}from"./vue-Cj9QYd7Z.js";import"./handsontable-Ch5RdAT_.js";import"./mapbox-gl-DQAr7S0z.js";const fe=z.sendStringToExecute.bind(z),pe={class:"px-3"},ge={class:"d-flex algin-center mt-2 w-75"},Ve={class:""},ve={class:""},ce={class:""},we={class:""},Re=de({__name:"index",setup(be){const D=U([]),n=U(null),R=U(1),O=t=>{D.value.some(({url:e})=>e===t?.url)||t&&D.value.unshift(t)},L=()=>{const t=document.createElement("input");t.type="file",t.style.display="none",t.accept="image/*",document.body.appendChild(t);const e=J();t.click(),I(!0),window.addEventListener("focus",()=>{setTimeout(()=>{e&&fe("MxFullscreen")},100)},{once:!0}),t.onchange=()=>{if(t.files&&t.files[0]){const l=URL.createObjectURL(t.files[0]);n.value={url:l,fileName:t.files[0].name},S(n.value),window.addEventListener("unload",function(){URL.revokeObjectURL(l)})}},t.remove()},i=Z({width:0,height:0}),S=t=>{typeof t=="string"?t.startsWith("http")&&(n.value={url:t,fileName:t}):n.value=t;const{url:e,fileName:l}=n.value||{};if(!e||!l)return;const o=n.value,u=E.getCurrentMxCAD();try{u.loadImage(e,r=>{if(!r){console.log("loadImage failed"),n.value=null,A().error(v("加载图片失败"));return}i.width=r.width,i.height=r.height,o&&O(o)},l||e)}catch(r){console.log("loadImage failed",r),n.value=null,A().error(v("加载图片失败"))}},d=Z({x:0,y:0}),V=k(!1,"Mx_AttachPictureDialog_isGetPt"),b=k(!1,"Mx_AttachPictureDialog_isGetZoomRatio"),y=k(!1,"Mx_AttachPictureDialog_isGetRotationAngle"),M=U(1),x=U(0),Q=async()=>{if(!n.value)return;if(n.value.url==="")return A().warning(v("请先选择图片"));I(!1);const t=E.getCurrentMxCAD();if(V.value){const o=new F;o.setMessage(v("指定插入点"));const u=await o.go();if(!u)return;d.x=u.x,d.y=u.y}if(b.value){const o=new F,u=new G(d.x,d.y);o.setBasePt(u),o.setMessage(v("指定缩放比例")),o.setUserDraw(($,H)=>{const T=$.distanceTo(u)/i.width,K=i.width*T,W=i.height*T,X=new THREE.Vector3(u.x+K,u.y+W);H.drawRect(u.toVector3(),X)});const r=await o.go();if(!r)return;M.value=B(r.distanceTo(u)/i.width,3)}if(y.value){const o=new te;o.setMessage(v("指定旋转角度"));const u=new G(d.x,d.y);o.setBasePt(u);const r=await o.go();if(!r)return;o.getDetailedResult()===ae.kCoordIn?x.value=B(r,3):x.value=B(r/(Math.PI/180),3)}let l=t.drawImage(d.x,d.y,i.width*M.value,i.height*M.value,x.value,n.value.fileName||n.value.url,!0,i.width,i.height).getMcDbEntity();if(l){const o=l.trueColor;l.trueColor=new le(o.red,o.green,o.blue,R.value*255)}},{isShow:h,showDialog:I}=_(!1,"_InsertImage",()=>{h.value||L()}),Y=[{name:"确定",fun:Q,primary:!0},{name:"关闭",fun:()=>I(!1)}];return(t,e)=>(me(),re(ee,{title:t.t("477"),modelValue:f(h),"onUpdate:modelValue":e[12]||(e[12]=l=>P(h)?h.value=l:null),"max-width":"600",footerBtnList:Y},{default:s(()=>[m("div",pe,[m("div",ge,[a(se,{class:"mt-1 mr-2","return-object":"","item-title":"fileName","item-value":"url",items:D.value,modelValue:n.value,"onUpdate:modelValue":[e[0]||(e[0]=l=>n.value=l),e[1]||(e[1]=l=>S(l))],modelModifiers:{lazy:!0}},{prepend:s(()=>[m("span",Ve,[g(p(t.t("209"))+"(",1),e[13]||(e[13]=m("span",{class:"text-decoration-underline"},"N",-1)),e[14]||(e[14]=g(") "))])]),_:1},8,["items","modelValue"]),a(q,{onClick:L},{default:s(()=>[a(C,{"key-name":"B"},{default:s(()=>[g(p(t.t("210")),1)]),_:1}),e[15]||(e[15]=g()),a(oe,{icon:"class:iconfont more"})]),_:1})]),a(ue,{"align-stretch":"",class:"mt-2"},{default:s(()=>[a(j,{cols:8,"align-self":"auto"},{default:s(()=>[a(c,{title:t.t("478"),class:"h-100"},{default:s(()=>[a(ne,{src:n.value?.url,crossorigin:"anonymous",height:"425px"},null,8,["src"])]),_:1},8,["title"])]),_:1}),a(j,{cols:4,"align-self":"auto"},{default:s(()=>[a(c,{title:t.t("479")},{default:s(()=>[a(N,{modelValue:f(V),"onUpdate:modelValue":e[2]||(e[2]=l=>P(V)?V.value=l:null)},{label:s(()=>[a(C,{"key-name":"S"},{default:s(()=>[g(p(t.t("215")),1)]),_:1})]),_:1},8,["modelValue"]),a(w,{class:"mt-1",modelValue:d.x,"onUpdate:modelValue":e[3]||(e[3]=l=>d.x=l),disabled:f(V)},{prepend:s(()=>e[16]||(e[16]=[m("span",{class:""}," X:",-1)])),_:1},8,["modelValue","disabled"]),a(w,{class:"mt-1",modelValue:d.y,"onUpdate:modelValue":e[4]||(e[4]=l=>d.y=l),disabled:f(V)},{prepend:s(()=>e[17]||(e[17]=[m("span",{class:""}," Y:",-1)])),_:1},8,["modelValue","disabled"])]),_:1},8,["title"]),a(c,{title:t.t("44")},{default:s(()=>[a(N,{modelValue:f(b),"onUpdate:modelValue":e[5]||(e[5]=l=>P(b)?b.value=l:null)},{label:s(()=>[a(C,{"key-name":"S"},{default:s(()=>[g(p(t.t("215")),1)]),_:1})]),details:s(l=>e[18]||(e[18]=[])),_:1},8,["modelValue"]),a(w,{class:"mt-1 ml-1",type:"number",disabled:f(b),modelValue:M.value,"onUpdate:modelValue":e[6]||(e[6]=l=>M.value=l)},null,8,["disabled","modelValue"])]),_:1},8,["title"]),a(c,{title:t.t("480")},{default:s(()=>[a(w,{class:"mt-1",type:"number",modelValue:i.width,"onUpdate:modelValue":e[7]||(e[7]=l=>i.width=l)},{prepend:s(()=>[m("span",ve,p(t.t("481"))+":",1)]),_:1},8,["modelValue"]),a(w,{class:"mt-1",type:"number",modelValue:i.height,"onUpdate:modelValue":e[8]||(e[8]=l=>i.height=l)},{prepend:s(()=>[m("span",ce,p(t.t("482"))+":",1)]),_:1},8,["modelValue"])]),_:1},8,["title"]),a(c,{title:t.t("483")},{default:s(()=>[a(N,{class:"",modelValue:f(y),"onUpdate:modelValue":e[9]||(e[9]=l=>P(y)?y.value=l:null)},{label:s(()=>[a(C,{"key-name":"S"},{default:s(()=>[g(p(t.t("215")),1)]),_:1})]),_:1},8,["modelValue"]),a(w,{class:"mt-1",type:"number",modelValue:x.value,"onUpdate:modelValue":e[10]||(e[10]=l=>x.value=l),disabled:f(y)},{prepend:s(()=>[m("span",we,p(t.t("281"))+":",1)]),_:1},8,["modelValue","disabled"])]),_:1},8,["title"]),a(c,{title:t.t("842")},{default:s(()=>[a(ie,{modelValue:R.value,"onUpdate:modelValue":e[11]||(e[11]=l=>R.value=l),max:1,min:0,step:.2,"thumb-label":""},null,8,["modelValue"])]),_:1},8,["title"])]),_:1})]),_:1})])]),_:1},8,["title","modelValue"]))}});export{Re as default};
