import{Q as h,V as d,_}from"./index-CzBriCFR.js";import{M as V}from"./index-itnQ6avM.js";import{B as u,h as y,a as g,k as v,j as k,I as B,$ as b,z as w}from"./vuetify-BqCp6y38.js";import{h as x,d as L,a3 as C,a4 as e,u as D,B as I,_ as c,a0 as t,m as s,$ as M,a5 as S,V as l,F}from"./vue-Cj9QYd7Z.js";import"./mxcad-DrgW2waE.js";import"./mxdraw-BQ5HhRCY.js";import"./handsontable-Ch5RdAT_.js";import"./mapbox-gl-DQAr7S0z.js";const N={class:"px-3 mt-2"},R={class:"d-flex algin-center",style:{width:"50px"}},U={class:"ml-7"},$={class:"ml-2"},j={class:""},A={class:""},E={class:"mt-2"},Q=x({__name:"index",setup(T){const{isShow:n,showDialog:p}=h(!1,"Mx_LWeight"),z=[{name:"确定",fun:()=>{},primary:!0},{name:"取消",fun:()=>p(!1)}],o=L(.2),r=[{name:"ByLayer",size:.01},{name:"ByBlock",size:.01},{name:"默认",size:.01},{name:"0.00mm",size:.01},{name:"0.05mm",size:.05},{name:"0.09mm",size:.09},{name:"0.13mm",size:.13},{name:"0.15mm",size:.15},{name:"0.18mm",size:.18},{name:"0.20mm",size:.2},{name:"0.25mm",size:.25},{name:"0.30mm",size:.3},{name:"0.35mm",size:.35},{name:"0.40mm",size:.4},{name:"0.50mm",size:.5},{name:"0.53mm",size:.53},{name:"0.60mm",size:.6},{name:"0.70mm",size:.7},{name:"0.80mm",size:.8},{name:"0.90mm",size:.9},{name:"1.00mm",size:1},{name:"1.06mm",size:1.06},{name:"1.20mm",size:1.2},{name:"1.40mm",size:1.4},{name:"1.58mm",size:1.58},{name:"2.00mm",size:2},{name:"2.11mm",size:2.11}];return(a,i)=>(c(),C(V,{title:a.t("537"),"max-width":"500",modelValue:D(n),"onUpdate:modelValue":i[1]||(i[1]=m=>I(n)?n.value=m:null),footerBtnList:z},{default:e(()=>[t("div",N,[s(w,null,{default:e(()=>[s(u,{cols:"5"},{default:e(()=>[s(d,{title:a.t("183")},{default:e(()=>[s(y,{density:"compact",class:"list-border overflow-y py-0",selected:[r[0].name],"active-class":"bg-light-blue-darken-2",height:"160"},{default:e(()=>[(c(),M(F,null,S(r,(m,f)=>s(g,{key:f,value:m.name,mandatory:"",class:"pa-0","min-height":"20",height:"20"},{prepend:e(()=>[t("div",R,[s(v,{class:"border-opacity-100",thickness:m.size*o.value*10,length:"50"},null,8,["thickness"])])]),default:e(()=>[s(k,{textContent:l(m.name),class:"ml-2"},null,8,["textContent"])]),_:2},1032,["value"])),64))]),_:1},8,["selected"])]),_:1},8,["title"])]),_:1}),s(u,{cols:"7","align-self":"auto"},{default:e(()=>[s(d,{title:a.t("538")},{default:e(()=>[t("div",U,l(a.t("249"))+"(mm) ",1)]),_:1},8,["title"]),s(B,{class:"my-2 ml-10"},{label:e(()=>[t("span",$,l(a.t("539")),1)]),_:1}),s(d,{title:a.t("540")},{default:e(()=>[s(b,{step:"0.1",class:"my-4","tick-size":"4","thumb-size":10,min:"0",max:"1",modelValue:o.value,"onUpdate:modelValue":i[0]||(i[0]=m=>o.value=m),"thumb-label":"","hide-details":"",color:"#007AD9"},{prepend:e(()=>[t("span",j,l(a.t("541")),1)]),append:e(()=>[t("span",A,l(a.t("542")),1)]),_:1},8,["modelValue"])]),_:1},8,["title"])]),_:1})]),_:1}),t("p",E,l(a.t("543"))+"：ByLayer",1)])]),_:1},8,["title","modelValue"]))}}),X=_(Q,[["__scopeId","data-v-a7792cb8"]]);export{X as default};
