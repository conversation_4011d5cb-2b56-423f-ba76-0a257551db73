<script setup>
import HighLowPressureHeight from "./high-low-pressure-height";
import HighLowPressureLow from "./high-low-pressure-low";
import Transformer from "./transformer";
import CustomScheme from "./custom-scheme";
import useAppStore from "@/store/modules/app.js";
import { useIframeCommunication } from "@/hooks/useIframeCommunication";
import { stationBuild } from "@/views/pages/online/sdkDrawBackData/stationBuild";
import { watch } from "vue";
import { intervalAddFile, readStationData } from "@/api/insertSag/index.js";
import { inject } from "vue";
import { ElLoading } from "element-plus";
const { proxy } = getCurrentInstance();
const { sendMessage, cleanup } = useIframeCommunication();
const appStore = useAppStore();
const visible = defineModel("visible", { type: Boolean, default: false });
const cadAppRef = inject("cadAppRef");
const activeName = ref("1");
const heightList = ref([]);
const lowList = ref([]);
const byqList = ref([]);
//高压文件
const heightFile = ref({});
//低压文件
const lowFile = ref({});
const handleClick = (tab) => {
  // if (tab.props.name == "1") {
  //   heightList.value = appStore.intervalList;
  // } else if(tab.props.name == "2") {
  //   lowList.value = appStore.intervalLowList;
  // }
};
const callChildC = inject("callChildC");
const taskId = new URLSearchParams(new URL(window.location.href).search).get(
  "id"
);
const closeVisible = () => {
  visible.value = false;
  appStore.setIntervalList([], "1");
  appStore.setIntervalLowList([], "1");
  // resetForm(formRef.value)
};

const checked = ref(true);

const customSchemeVisible = ref(false);
const saveCustomSchemeHandle = () => {
  customSchemeVisible.value = true;
};

const onSubmit = async () => {};
//生成随机数
function generateRandomNumber(digits = 5) {
  const min = 10 ** (digits - 1);
  const max = 10 ** digits - 1;
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

// 使用
const systemConfig = async () => {
  try {
    const loading = ElLoading.service({
      lock: true,
      text: "间隔拼接中...",
      background: "rgba(0, 0, 0, 0.7)",
    });
    const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms));
    if (heightFile.value.length) {
      await sucFile(heightFile.value, "高压");
      await delay(2000); // 等待 1 秒
    }
    if (lowFile.value.length) {
      await sucFile(lowFile.value, "低压");
      await delay(2000); // 等待 1 秒
    }

    // 2. 生成随机ID并处理数据
    const ids = [];
    const processList = (list) => {
      if (!list.length) return;
      const randomNum = generateRandomNumber(5);
      list.forEach((item) => (item.id = randomNum));
      ids.push(randomNum);
    };

    processList(heightList.value);
    processList(lowList.value);

    // 3. 准备选项数据
    // const activeList = activeName.value === "1"
    //   ? heightList.value
    //   : activeName.value === "2"
    //     ? lowList.value
    //     : [];
    // console.log("🚀 ~ systemConfig ~ activeList:", activeList)

    if (!heightList.value.length) {
      proxy.$message.warning("请选择间隔数据！");
      loading.close();
      return;
    }
    let sdkList=[]
     sdkList= heightList.value.filter(item=>!item.sdkId)
     if (!sdkList.length) {
      proxy.$message.warning("暂无新添加间隔数据，请添加间隔！");
      loading.close();
      return;
    }
    const options = sdkList.map((item) => ({
      bayName: item.intervalnames,
      bayID: item.type,
      pointInfo: {
        pointname: item.pointName,
        aliasName: item.pointName,
      },
      bayType: item.intervalnum,
    }));

    // 5. 发送消息
    const res = await new Promise((resolve, reject) => {
      sendMessage(
        cadAppRef.value,
        {
          type: "greeting",
          content: "SDK_add_interval_splicing",
          options,
        },
        (response) => {
          console.log("🚀 ~ res ~ response:", response);
          // response?.params?.status === "000000"
          //   ? resolve(response)
          //   : reject(new Error('操作失败'));
          if (response?.params?.status === "000000") {
            resolve(response);
          } else {
            loading.close();
            // visible.value = false;
            reject(new Error("操作失败"));
          }
        }
      );
    });
    if (res.params.status == "000000") {  
      setTimeout(() => {
        sendMessage(
          cadAppRef.value,
          {
            type: "greeting",
            content: "SDK_add_interval_full",
            options: appStore.sdkClassName.blockId.toString(),
          },
          (response) => {
            for (let i = 0; i < heightList.value.length; i++) {
              const element = heightList.value[i];
              for (let j = 0; j < response.params.result.PWBayPSR.length; j++) {
                const elements = response.params.result.PWBayPSR[j];
                if(element.intervalnames===elements.NAME&&element.type===elements.BAY_NO){
                  element.sdkId=elements.DEV_ID
                }
              }
            }
            // 4. 异步构建站房
        stationBuild(
          heightList.value,
          lowList.value,
          byqList.value,
          appStore.sdkClassName.blockId,
          ids,
          fileNames.value
        );
          }
        );
      }, 500);
      loading.close();
      visible.value = false;
    } else {
      loading.close();
      // visible.value = false;
    }
    return res;
  } catch (error) {
    console.error("系统配置失败:", error);
    throw error; // 或者显示错误消息
  }
};

const sucFile = (arr, volName) => {
  oniframeMessage({
    type: "cadPreview",
    content: "Mx_splicing",
    formData: {
      tableData: JSON.stringify(arr),
      volName: volName,
      isFlags: false,
    },
  });
};
let fileNames = ref("");
// 获取当前站房数据
const queryStationData = () => {
  readStationData(taskId, appStore.sdkClassName.blockId).then((res) => {
    if (res.data) {
      let arr = JSON.parse(res.data.privatepropertys);
      if (arr.name) {
        fileNames.value = arr.name;
      } else {
        fileNames.value = "";
      }
      if (arr?.HighVoltage?.length > 0) {
        appStore.setIntervalList(arr.HighVoltage, "");
      }
      if (!Object.hasOwn(arr, "HighVoltage")) {
        appStore.setIntervalList([], "");
      }
      if (arr?.LowVoltage?.length > 0) {
        appStore.setIntervalLowList(arr.LowVoltage, "");
      }
      if (!Object.hasOwn(arr, "LowVoltage")) {
        appStore.setIntervalLowList([], "");
      }
      if (arr?.BYQ?.length > 0) {
        appStore.setByqList(arr.BYQ);
      }
      if (!Object.hasOwn(arr, "BYQ")) {
        appStore.setByqList([]);
      }
    } else {
      appStore.setIntervalList([], "");
      appStore.setIntervalLowList([], "");
      appStore.setByqList([]);
    }
  });
};

const handleMessage = async (event) => {
  let objArr = [];
  // 1. 验证消息结构
  if (!isValidCadMessage(event)) return;
  // 2. 按电压类型分类存储
  const { formData: obj } = event.data.params;
  if (["高压", "低压"].includes(obj.volName)) {
    objArr.push(obj);
  }
  try {
    // 3. 根据数据条件执行不同处理
    const hasHeight = heightList.value.length > 0;
    const hasLow = lowList.value.length > 0;
    const hasMultipleObj = objArr.length > 1;

    const heightLowFlag = hasHeight && hasLow && hasMultipleObj;
    const lowFlag = hasHeight !== hasLow; // 最简洁的写法
    if (heightLowFlag) {
      const result = await processBatchFiles(objArr, taskId);
      if (result?.code == "200") {
        callChildC?.();
      }
    }
    if (lowFlag) {
      const result = await processSingleFile(objArr, taskId);
      if (result?.code == "200") {
        callChildC?.();
      }
    }
    // const result = shouldProcessBatch
    //   ? await processBatchFiles(objArr.value, taskId)
    //   :'' ;
    // await processSingleFile(obj, taskId)
    // 4. 处理结果
  } catch (error) {
    console.error("文件处理失败:", error);
    // proxy.$message.error("处理文件时出错");
  }
};
const oniframeMessage = (param) => {
  const myiframe = appStore.iframeHide;
  if (myiframe && myiframe.contentWindow) {
    myiframe.contentWindow.postMessage(param, "*");
    // 先移除之前的事件监听器，防止重复绑定
    // window.removeEventListener('message', handleMessage);
    // 添加新的事件监听器
    window.addEventListener("message", handleMessage, { once: true });
  }
};
// 辅助函数：验证消息结构
function isValidCadMessage(event) {
  return (
    event.data?.type === "parentCad" &&
    event.data?.params?.content === "电气一次图设计"
  );
}

// 辅助函数：批量处理文件
async function processBatchFiles(dataList, taskId) {
  const { formData, validFiles } = processUploadFiles(dataList, taskId);
  return executeFileUpload(formData);
}

// 辅助函数：单文件处理
async function processSingleFile(fileData, taskId) {
  const { formData, validFiles } = processUploadFiles(fileData, taskId);
  return executeFileUpload(formData);
}

// Helper function: Process and validate files
const processUploadFiles = (data, taskId) => {
  const formData = new FormData();
  formData.append("taskId", taskId);

  const validFiles = (Array.isArray(data) ? data : [data])
    .filter((obj) => obj.files[0] && ["高压", "低压"].includes(obj.volName))
    .map((obj) => {
      const prefix = obj.volName;
      let newFileName;
      if (fileNames.value) {
        newFileName = fileNames.value;
      } else {
        newFileName = `${
          appStore.sdkClassName.clickItemName
        }${prefix}${generateRandomNumber(5)}.dwg`;
        fileNames.value = newFileName;
      }
      formData.append("files", obj.files[0], newFileName);
      return { ...obj };
    });

  return { formData, validFiles };
};

// Helper function: Handle file upload
const executeFileUpload = async (formData) => {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), 30000);

  try {
    const response = await intervalAddFile(formData, {
      signal: controller.signal,
    });
    clearTimeout(timeoutId);
    return response;
  } catch (error) {
    clearTimeout(timeoutId);
    throw error;
  }
};

onMounted(() => {
  queryStationData();
});

//监听高压数据
watch(
  () => appStore.intervalList,
  (newValue) => {
    heightFile.value = newValue;
    heightList.value = newValue;
    heightList.value.forEach((element) => {
      element.pointName = element.intervalnames + element.type;
    });
  },
  { deep: true }
);
//监听低压数据
watch(
  () => appStore.intervalLowList,
  (newValue) => {
    lowFile.value = newValue;
    lowList.value = newValue;
    lowList.value.forEach((element) => {
      element.pointName = element.intervalnames + element.type;
    });
  },
  { deep: true }
);
//监听变压器数据
watch(
  () => appStore.byqList,
  (newValue) => {
    byqList.value = [];
    newValue.forEach((element) => {
      if (element.num) {
        byqList.value.push(element);
      }
    });
  },
  { deep: true }
);
</script>

<template>
  <el-dialog
    v-model="visible"
    title="间隔配置"
    width="60%"
    draggable
    overflow
    append-to-body
    @close="closeVisible"
  >
    <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
      <el-tab-pane label="高压" name="1">
        <div class="">
          <high-low-pressure-height
            :name="activeName"
            v-if="activeName === '1'"
          />
        </div>
      </el-tab-pane>
      <!-- <el-tab-pane label="低压" name="2">
        <div class="">
          <high-low-pressure-low :name="activeName" v-if="activeName === '2'" />
        </div>
      </el-tab-pane> -->
      <el-tab-pane label="变压器" name="3">
        <transformer v-if="activeName === '3'" />
      </el-tab-pane>
    </el-tabs>
    <template #footer>
      <div class="dialog-footer">
        <!--        <el-button @click="closeVisible">取消</el-button>-->
        <el-space>
          <!-- <el-checkbox v-model="checked" label="是否配置图框" /> -->
          <!-- :disabled="tableList.length===0" -->
          <el-button @click="systemConfig" type="primary"
            >生成系统配置图</el-button
          >
          <!-- <el-button type="primary" @click="saveCustomSchemeHandle">另存为自定义方案</el-button> -->
        </el-space>
      </div>
    </template>
    <custom-scheme
      v-if="customSchemeVisible"
      v-model:visible="customSchemeVisible"
    />
  </el-dialog>
</template>

<style scoped lang="scss"></style>
