import{M as p}from"./index-8X61wlK0.js";import{M as d}from"./index--PcCuGx1.js";import{R as f,S as o,T as t,U as i}from"./index-D95UjFey.js";import{h as V,a0 as g,_ as x,$ as _,a1 as w,m as c,u as s,B as l}from"./vue-DfH9C9Rx.js";import"./vuetify-B_xYg4qv.js";import"./mxcad-CfPpL1Bn.js";import"./mxdraw-C_n_7lEs.js";import"./mapbox-gl-PN3pDJHq.js";import"./xlsx-Ck5MUZvf.js";const h={class:"px-3"},R=V({__name:"index",setup(B){const{items:r,define:m}=f(t),n=[{name:"确定",fun:()=>{m.value(),i(!1)},primary:!0},{name:"关闭",fun:()=>i(!1)}];return(u,e)=>(g(),x(p,{title:u.t("草图设置"),modelValue:s(t),"onUpdate:modelValue":e[1]||(e[1]=a=>l(t)?t.value=a:null),footerBtnList:n,"max-width":"600"},{default:_(()=>[w("div",h,[c(d,{items:s(r),modelValue:s(o),"onUpdate:modelValue":e[0]||(e[0]=a=>l(o)?o.value=a:null),height:312,tabsProps:{grow:!0}},null,8,["items","modelValue"])])]),_:1},8,["title","modelValue"]))}});export{R as default};
