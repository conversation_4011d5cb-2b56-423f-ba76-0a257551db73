import { MxCpp, MxCADResbuf, MxCADSelectionSet, MxCADUiPrEntity,McCmColor, McDbLayerTableRecord, McDb } from "mxcad"
// 新增图层, 如果图层表中纯在图层则切换图层
export async function addLayer(layerName: string) {
    const mxcad = MxCpp.getCurrentMxCAD()
    // 获取当前CAD数据库的图层表
    let layerTable = mxcad.getDatabase().getLayerTable();
    let aryId = layerTable.getAllRecordId();
    let layerExists = false;
    // 遍历所有图层，检查目标图层是否已存在
    aryId.forEach((id) => {
        let layerRec = id.getMcDbLayerTableRecord();
        if (layerRec === null) return;
        // 如果找到了匹配的图层名称
        if (layerRec.name === layerName) {
            layerExists = true;
            console.log(`图层 '${layerName}' 已存在，切换到该图层`);
            // 直接切换到该图层
            mxcad.drawLayer = layerName;
        }
    });
    // 如果图层不存在，则新增该图层
    if (!layerExists) {
        console.log(`图层 '${layerName}' 不存在，正在新增图层`);
        mxcad.addLayer(layerName);
        mxcad.drawLayer = layerName;
    }
    const layerName1 = mxcad.getDatabase().getCurrentlyLayerName();
    const layerId = mxcad.getDatabase().getCurrentlyLayerId();
    console.log("当前图层",layerName1, layerId);
}

// 获取图层得所有设备
export function getLayer(layerName: string) {
    // 创建一个过滤数据连表对象
    const filter = new MxCADResbuf();
    // 把层名加入过滤条件，8是DXF组码，它代表layerName表示是的是一个层名。
    filter.AddString(layerName, 8);
    //定义选择集对象
    let ss = new MxCADSelectionSet();
    // 选择图上的所有对象
    ss.allSelect(filter);
    // 遍历所有对象，设置对象高亮
    ss.forEach(id=>{
        const ent = id.getMcDbEntity();
        ent.highlight(true);
    });
    MxCpp.getCurrentMxCAD().updateDisplay();
}
