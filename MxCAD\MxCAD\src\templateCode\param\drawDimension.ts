
import { McDbAlignedDimension,McDb, McDbRotatedDimension, MxCpp, McGePoint3d, McCmColor } from "mxcad";
//画标注
function drawDimension() {
  const mxcad = MxCpp.getCurrentMxCAD();
  // 创建新画布
  mxcad.newFile();

  // 画标注
  const mDimension = new McDbAlignedDimension()
  mDimension.xLine1Point = new McGePoint3d(-1800, 800)
  mDimension.xLine2Point = new McGePoint3d(1800, 800)
  mDimension.dimLinePoint = new McGePoint3d(800, 500)
  mDimension.textAttachment = McDb.AttachmentPoint.kTopLeft
  mDimension.trueColor = new McCmColor(200, 255, 0)
  mDimension.oblique = 0
  mxcad.drawEntity(mDimension)

  const rDimension = new McDbRotatedDimension()
  rDimension.xLine1Point = new McGePoint3d(-1800, -800)
  rDimension.xLine2Point = new McGePoint3d(1800, -800)
  rDimension.dimLinePoint = new McGePoint3d(-800, -500)
  rDimension.textAttachment = McDb.AttachmentPoint.kTopLeft
  rDimension.textRotation = 0.6
  rDimension.trueColor = new McCmColor(200, 255, 0)
  rDimension.oblique = 0
  rDimension.rotation = 0
  rDimension.dimensionText = "标注文本"
  mxcad.drawEntity(rDimension)

  mxcad.zoomCenter(0, 0)
};

// 调用画标注方法
drawDimension();