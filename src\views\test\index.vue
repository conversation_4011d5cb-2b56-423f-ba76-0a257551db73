<template>
  <div>
    <el-form :model="form" label-width="120px">

      <el-form-item label="Activity form">
        <el-input v-model="form.str" type="textarea" />
      </el-form-item>
      <el-form-item label="表名称">
        <el-select
            filterable
            placeholder="请选择数据版本"
        >
          <el-option
              v-for="item in tableName"
              :key="item.value"
              :label="item.label"
              :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
      </el-form-item>
    </el-form>

  </div>

  <div>
    <el-table  style="height:calc(100vh - 280px);overflow-y:scroll" :data="tableData" >
      <el-table-column
          :show-overflow-tooltip = true
          v-for="column in columns"
          :key="column.prop"
          :prop="column.prop"
          :label="column.label"
          :width="column.width">
      </el-table-column>
    </el-table>
  </div>



</template>

<script lang="ts" setup>
import {onMounted, reactive} from 'vue'
import { test,showTable} from "@/api/drawing/drawing.js";

// do not use same name with ref
const form = reactive({
  str: ''
})
const { proxy } = getCurrentInstance();
const tableData = ref([]); // 存储表格数据
const columns = ref([]); // 存储列信息，初始为空数组
const tableName = ref([]);

function handleQuery(){
  columns.value = null;
  tableData.value = null;
  test(form).then((res)=>{
    console.log(res)


    if(res.code== 200){
      proxy.$modal.msgSuccess("成功");
      if(res.data.columns != null){
        columns.value = res.data.columns; // 假设响应体中有columns字段定义表头信息
        tableData.value = res.data.rows;
      }
    }


  })
}

const dataList = () => {
  showTable().then(response =>{
    tableName.value = response.data.map((item) => ({
      value: item,
      label: item,
    }))

    console.log(response);
  })
}
onMounted(() => {
  dataList();
});
</script>
