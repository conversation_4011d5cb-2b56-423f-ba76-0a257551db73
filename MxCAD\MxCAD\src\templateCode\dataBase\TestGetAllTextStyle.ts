import { MxCpp } from "mxcad";

// 获取所有文字样式
function MxTest_GetAllTextStyle() {
    // 获取当前mxcad对象
    let mxobj = MxCpp.getCurrentMxCAD();
    // 获取文字样式表
    let textSyleTable = mxobj.getDatabase().getTextStyleTable();
    // 获取文字样式表记录id数组
    let aryId = textSyleTable.getAllRecordId();
    // 遍历文字样式表记录
    aryId.forEach((id) => {
        let textSyleRec = id.getMcDbTextStyleTableRecord();
        if (textSyleRec === null) return;
        let out: any = {};
        out.name = textSyleRec.name;
        out.font = textSyleRec.font();
        out.fileName = textSyleRec.fileName;
        out.bigFontFileName = textSyleRec.bigFontFileName;
        out.textSize = textSyleRec.textSize;
        out.xScale = textSyleRec.xScale;
        console.log(out);
    });
};

// 调用获取所有文字样式的方法
MxTest_GetAllTextStyle();
