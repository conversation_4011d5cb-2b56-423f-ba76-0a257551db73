import{M as C}from"./index-8X61wlK0.js";import{u as U}from"./hooks-CmmFp-On.js";import{T as k}from"./TextEllipsis-Do4nnv80.js";import{am as D,an as A,ao as E,_ as B}from"./index-D95UjFey.js";import{b as F,L as T,S as L,x as z}from"./vuetify-B_xYg4qv.js";import{h as N,d as P,c as R,a0 as l,_ as c,$ as f,a1 as s,m as p,V as g,a3 as m,a4 as M,F as j,a5 as H,u as _}from"./vue-DfH9C9Rx.js";import"./mxcad-CfPpL1Bn.js";import"./mxdraw-C_n_7lEs.js";import"./mapbox-gl-PN3pDJHq.js";import"./xlsx-Ck5MUZvf.js";const X={class:"mt-2"},$={style:{"z-index":"1"}},q={class:"text-center"},G={class:"text-left"},J={style:{height:"26px"}},K={class:"d-flex justify-center",style:{width:"30px"}},O={key:0,class:"border_box"},Q={style:{height:"3px"}},W=N({__name:"index",setup(Y){const{dialog:r}=U(),n=P([]),x=e=>{if(e===0)return"class:iconfont gou";if(e===1)return"class:iconfont cha"},v=e=>{if(e===0)return"#33CD2A";if(e===1)return"#ff0000"};r.onReveal(e=>{const t=[];e.externalReference.forEach(a=>{t.push({name:a,uploadState:3,progress:0,type:"ref",hash:e.hash})}),e.images.forEach(a=>{t.push({name:a,uploadState:3,progress:0,hash:e.hash,type:"img"})}),n.value=t});const y=()=>{let{baseUrl:e=""}=D()||{};e.substring(0,16)=="http://localhost"&&(e=A()+e.substring(16));const t=document.createElement("input");t.setAttribute("type","file"),t.setAttribute("accept",".dwg,image/*"),t.style.display="none",t.setAttribute("multiple","multiple"),t.setAttribute("capture","camera"),document.body.appendChild(t),t.onchange=()=>{if(!t.files||t.files.length<1)return;const a=Array.from(t.files);n.value.filter(o=>a.some(d=>{if(d.name===o.name)return o.source=d,!0})).forEach(o=>{const{type:d,name:w,source:h,hash:V}=o;o.uploadState=2;const u=new FormData;h&&u.append("file",h),u.append("src_dwgfile_hash",V),u.append("ext_ref_file",w),E({url:e+(d==="img"?"/mxcad/up_ext_reference_image":"/mxcad/up_ext_reference_dwg"),method:"post",headers:{"Content-Type":"multipart/form-data"},data:u,onUploadProgress(i){i.total&&(o.progress=i.loaded/i.total*100,console.log(o.progress))}}).then(i=>{i.data.code===0?(o.uploadState=0,t.remove()):(o.uploadState=1,t.remove())},()=>{o.uploadState=1,t.remove()})})},setTimeout(()=>{t.click()},100)},S=R(()=>n.value.some(e=>e.uploadState===2)),b=[{name:"选择文件",fun:y,disabled:()=>n.value.every(e=>e.uploadState===0)},{name:"取消",fun:()=>{r.cancel(!1),r.showDialog(!1)}},{name:"继续打开文件",fun:()=>{r.confirm(!0),r.showDialog(!1)}}];return(e,t)=>(l(),c(C,{title:e.t("637"),"max-width":"450",modelValue:_(r).isShow.value,"onUpdate:modelValue":t[0]||(t[0]=a=>_(r).isShow.value=a),footerBtnList:b},{default:f(()=>[s("div",X,[p(L,{height:"150",class:"mb-2",hover:!1},{default:f(()=>[s("thead",$,[s("tr",null,[s("th",null,g(e.t("638")),1),s("th",q,g(e.t("639")),1)])]),s("tbody",G,[(l(!0),m(j,null,M(n.value,(a,o)=>(l(),m("tr",J,[s("td",null,[s("div",K,[a.uploadState!==2?(l(),m("div",O,[p(F,{icon:x(a.uploadState),color:v(a.uploadState),size:"16"},null,8,["icon","color"])])):(l(),c(T,{key:1,rotate:360,size:16,width:2,"model-value":a.progress,color:"teal",indeterminate:a.progress===100},null,8,["model-value","indeterminate"]))])]),s("td",null,[p(k,{class:"text-left",width:285,text:a.name,"slice-num":10},null,8,["text"])])]))),256))])]),_:1}),s("div",Q,[S.value?(l(),c(z,{key:0,indeterminate:"",color:"green"})):H("",!0)])])]),_:1},8,["title","modelValue"]))}}),ie=B(W,[["__scopeId","data-v-50879d03"]]);export{ie as default};
