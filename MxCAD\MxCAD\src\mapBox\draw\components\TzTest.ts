import { McDbBlockTableRecord,MxCADUiPrPoint, McGePoint3d, McDbPolyline, MxCpp,MxTools, McDbText, McDb, McCmColor, McDbBlockReference, McDbAttributeDefinition, McDbMText } from "mxcad";
export async function TzTest(value) {
    let mxcad = MxCpp.App.getCurrentMxCAD();
    mxcad.newFile();
    let blkTable =  mxcad.getDatabase().getBlockTable();
    let blkRecId = blkTable.add(new McDbBlockTableRecord());
    // 根据ObjectId再次得到刚刚添加的图块记录
    let blkTableRecord:McDbBlockTableRecord = blkRecId.getMcDbBlockTableRecord()
    console.log("🚀 ~ TzTest ~ value:", value)
    const offsetX =  0; // 默认值为 0
    let blkFilePath = import.meta.url.substring(0, import.meta.url.lastIndexOf('/')) + `/assets/TuK/A4-210x297 标准改1.dwg.mxweb`;
     // 插入图块文件
     let blkrecId = await mxcad.insertBlock(blkFilePath, 'A4-210x297 标准改1.dwg');
     if (!blkrecId.isValid()) {
         return
     }
     let blkRef = new McDbBlockReference();
     blkRef.blockTableRecordId = blkrecId;
     blkRef.position = new McGePoint3d(offsetX,0,0);
     blkTableRecord.appendAcDbEntity(blkRef)
    //  mxcad.drawEntity(blkRef);
     let blkFilePathNr =  value.filePath ? URL.createObjectURL(value.filePath) : ''
     let blkrecIdNr = await mxcad.insertBlock(blkFilePathNr, '图纸名称');
     if (!blkrecIdNr.isValid()) {
         return
     }

     let dwgBTR:McDbBlockTableRecord = blkrecIdNr.getMcDbBlockTableRecord() as any;
     const boundboxPoints = dwgBTR.getBoundingBox()
     dwgBTR.origin = boundboxPoints.minPt;

     let blkRefNr = new McDbBlockReference();
     blkRefNr.blockTableRecordId = blkrecIdNr;
     blkRefNr.position = new McGePoint3d(0, 0, 0)
     blkTableRecord.appendAcDbEntity(blkRefNr)
      // 实例化块参照 这里需要设置我们刚刚添加图块记录得到的ObjectId
      let blkRefObj = new McDbBlockReference();
      blkRefObj.blockTableRecordId = blkRecId;
      // 最后设置位置 渲染图块
      let newBlkRefId = mxcad.drawEntity(blkRefObj);
      mxcad.updateDisplay()
      mxcad.regen();
      // 获取该对象的实体
      const ent = newBlkRefId.getMcDbEntity();
      if(!ent) return { id: null, handle: null };
      // 获取对象ID
      const entId = ent.getObjectID();
      // 获取对象句柄
      const sHandle = ent.getHandle();
    return new Promise((resolve, reject) => {
        MxCpp.getCurrentMxCAD().saveFile(void 0, (data) => {
            let blob: Blob;
            const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
            if (isSafari) {
                blob = new Blob([data.buffer], { type: "application/octet-stream" });
            } else {
                blob = new Blob([data.buffer], { type: "application/octet-binary" });
            }
            const file = new File([blob], value.fileName + '.mxweb', { type: isSafari ? "application/octet-stream" : "application/octet-binary" });
            resolve({file: file, sHandle}); // 返回文件对象
        }, false, true);
    });
}