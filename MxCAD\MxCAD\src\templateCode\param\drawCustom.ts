import {
    MxCpp, McDbLine, McGePoint3d, McDbCustomEntity, IMcDbDwgFiler,MxCADWorldDraw,McGePoint3dArray,
    McGeVector3d,McDbText,McDb,MxCADResbuf,MxCADSelectionSet,McObjectId,McDbCircle } from "mxcad"

// 线文本自定义实体
   // 新创建 McDbLineText 类，继承McDbCustomEntity
   class McDbTestLineText extends McDbCustomEntity {
    //设置McDbLineText类中的两个直线端点 pt1、pt2
    //以及显示线段长度的文字_text和文字大小_textsize
    private pt1: McGePoint3d = new McGePoint3d();
    private pt2: McGePoint3d = new McGePoint3d();
    private _text: string = "";
    private _textsize: number = 10;
    //构造函数
    constructor(imp?: any) {
        super(imp);
    }
    //创建函数
    public create(imp: any) {
        return new McDbTestLineText(imp)
    }
    //获取类型
    public getTypeName(): string {
        return "McDbTestLineText";
    }
    //设置或获取文本值
    public set text(val: string) {
        this._text = val;
    }
    public get text(): string {
        return this._text;
    }
    //设置或获取文本大小
    public set textsize(val: number) {
        this._textsize = val;
    }
    public get textsize(): number {
        return this._textsize;
    }
    //读取自定义实体数据pt1、pt2、_text、_textsize
    public dwgInFields(filter: IMcDbDwgFiler): boolean {
        this.pt1 = filter.readPoint("pt1").val;
        this.pt2 = filter.readPoint("pt2").val;
        this._text = filter.readString("text").val;
        this._textsize = filter.readDouble("textsize").val;
        return true;
    }
    //写入自定义实体数据pt1、pt2、_text、_textsize
    public dwgOutFields(filter: IMcDbDwgFiler): boolean {
        filter.writePoint("pt1", this.pt1);
        filter.writePoint("pt2", this.pt2);
        filter.writeString("text", this._text);
        filter.writeDouble("textsize", this._textsize);
        return true;
    }
    //自定义同步函数，当其他对象与该对象相连时同步数据
    private fineLink(pt: McGePoint3d): any {
        let ret: any = {};
        let myId = this.getObjectID();
        let dSearch = this._textsize * 0.5;;
        let filter = new MxCADResbuf();
        filter.AddString("McDbCustomEntity", 5020);
        let ss = new MxCADSelectionSet();
        ss.crossingSelect(pt.x - dSearch, pt.y - dSearch, pt.x + dSearch, pt.y + dSearch, filter);
        ss.forEach((id) => {
            if (id == myId)
                return;
            let ent = id.getMcDbEntity();
            if (!ent) return;
            if (ent instanceof McDbLineText) {
                let line = (ent as McDbLineText);
                let linkPoint = line.getPoint1();
                let link_pos = 0;
                // 得到直线与图块连接的端点坐标.
               let dist = line.getPoint1().distanceTo(pt);
                if (dist > line.getPoint2().distanceTo(pt)) {
                    dist = line.getPoint2().distanceTo(pt);
                    linkPoint = line.getPoint2();
                    link_pos = 1;
                }
                if (dist < dSearch) {
                   ret[id.id] = { link_point: linkPoint,link_pos:link_pos };
                }
            }
        });
        return ret;
    }
    //处理夹点编辑效果
    public moveGripPointsAt(iIndex: number, dXOffset: number, dYOffset: number, dZOffset: number) {
        this.assertWrite();
        let pt:McGePoint3d = this.pt1.clone();
        let new_pt:McGePoint3d = pt;
        if (iIndex == 0) {
            this.pt1.x += dXOffset;
            this.pt1.y += dYOffset;
            this.pt1.z += dZOffset;
            new_pt = this.pt1;
        }
        else if (iIndex == 1) {
            pt = this.pt2.clone();
            this.pt2.x += dXOffset;
            this.pt2.y += dYOffset;
            this.pt2.z += dZOffset;
            new_pt = this.pt2;
        }
        if (this.getObjectID().isValid()) {
            // 同步，与连接的其它对象。
            let linkobj = this.fineLink(pt)
            Object.keys(linkobj).forEach((id_val:any)=>{
                let idFind = new McObjectId(id_val);
                let lineFind = (idFind.getMcDbEntity() as McDbLineText);
                if(linkobj[id_val].link_pos == 0){
                    lineFind.setPoint1(new_pt);
                }
                else{
                    lineFind.setPoint2(new_pt);
                }
              });
        }
    };
    //设置对象编辑点位
    public getGripPoints(): McGePoint3dArray {
        let ret = new McGePoint3dArray()
        ret.append(this.pt1);
        ret.append(this.pt2);
        return ret;
    };
    //动态绘制
    public worldDraw(draw: MxCADWorldDraw): void {
        let circle_r = this._textsize * 0.4;
        let vec2 = this.pt2.sub(this.pt1);
        vec2.normalize().mult(circle_r);
        let tmpline = new McDbLine(this.pt1.clone().addvec(vec2),
                                   this.pt2.clone().subvec(vec2));
        draw.drawEntity(tmpline);
        let vec = this.pt2.sub(this.pt1).mult(0.5);
        let midpt = this.pt1.clone().addvec(vec);
        if (vec.dotProduct(McGeVector3d.kXAxis) < 0) {
            vec.negate();
        }
        let ange = vec.angleTo2(McGeVector3d.kXAxis, McGeVector3d.kNegateZAxis);
        let str = this._text;
        if (str.length == 0) {
            str = this.pt1.distanceTo(this.pt2).toFixed(2);
        }
        vec.perpVector();
        if (vec.dotProduct(McGeVector3d.kYAxis) < 0) {
            vec.negate();
        }
        vec.normalize().mult(this._textsize * 0.2);
        let text = new McDbText();
        text.textString = str;
        text.position = midpt.clone().addvec(vec);
        text.alignmentPoint = midpt.clone().addvec(vec);
        text.rotation = ange;
        text.verticalMode = McDb.TextVertMode.kTextBottom;
        text.horizontalMode = McDb.TextHorzMode.kTextCenter;
        text.height = this._textsize;
        draw.drawEntity(text)
        let circle1 = new McDbCircle();
        circle1.center = this.pt1;
        circle1.radius = circle_r;
        draw.drawEntity(circle1);

        let circle2= new McDbCircle();
        circle2.center = this.pt2;
        circle2.radius = circle_r;
        draw.drawEntity(circle2);
    }
    // 设置pt1
    public setPoint1(pt1: McGePoint3d) {
        this.assertWrite();
        this.pt1 = pt1.clone();
    }
    // 设置pt2
    public setPoint2(pt2: McGePoint3d) {
        this.assertWrite();
        this.pt2 = pt2.clone();
    }
    // 获取pt1
    public getPoint1() {
        return this.pt1;
    }
    //获取pt2
    public getPoint2() {
        return this.pt2;
    }
}

// 绘线文本自定义实体
async function MxTest_LineText() {
    let mxcad = MxCpp.getCurrentMxCAD();
    //清空当前显示内容
    mxcad.newFile();

    let pt1 = new McGePoint3d(100, 100, 0);
    let pt2 = new McGePoint3d(200, 150, 0);
    let pt3 = new McGePoint3d(400, 50, 0);
    let pt4 = new McGePoint3d(600, 60, 0);
    let pt5 = new McGePoint3d(200, 300, 0);

    let textsize = 5;

    let myline1 = new McDbTestLineText();
    myline1.setPoint1(pt1);
    myline1.setPoint2(pt2);
    myline1.textsize = textsize;
    myline1.text = "xxxx";
    mxcad.drawEntity(myline1);


    let myline2 = new McDbTestLineText();
    myline2.setPoint1(pt2);
    myline2.setPoint2(pt3);
    myline2.textsize = textsize;
    mxcad.drawEntity(myline2);

    let myline3 = new McDbTestLineText();
    myline3.setPoint1(pt3);
    myline3.setPoint2(pt4);
    myline3.textsize = textsize;
    mxcad.drawEntity(myline3);


    let myline4 = new McDbTestLineText();
    myline4.setPoint1(pt2);
    myline4.setPoint2(pt5);
    myline4.textsize = textsize;
    mxcad.drawEntity(myline4);


    //把所有的实体都放到当前显示视区
    mxcad.zoomW(new McGePoint3d(-300, -300, 0), new McGePoint3d(650, 500, 0));
    //更新视区显示
    mxcad.updateDisplay();
}

// 自定义实体注册
new McDbTestLineText().rxInit();
// 调用绘线文本自定义实体方法
MxTest_LineText();

