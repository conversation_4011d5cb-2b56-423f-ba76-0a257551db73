import request from '@/utils/request'

export function getPropertyData(data) {
    return request({
        url: '/rodLineJointDraw/getPropertyList',
        // url: '/m1/2043178-2086468-default/m2/2043178-2086468-default/258327405',
        method: 'post',
        data
    })
}

export function getEdit5Value(params) {
    return request({
        url: '/rodLineJointDraw/getEdit5Value',
        method: 'get',
        params
    })
}

export function propertyChangeValue(data) {
    return request({
        url: '/rodLineJointDraw/propertyChangeValue',
        method: 'post',
        data
    })
}

export function getDrawByLegendGuidKey(data) {
    return request({
        // url: '/rodLineJointDraw/getDrawByLegendGuidKey',
        url: '/rodLineJointDraw/getDrawings',
        method: 'post',
        data
    })
}

export function getPropertyByLegendGuidKey(data) {
    return request({
        // url: '/rodLineJointDraw/getPropertyByLegendGuidKey',
        url: 'rodLineJointDraw/getModuleProperties',
        method: 'post',
        data
    })
}


export function getInfoByLegendGuidKey(params) {
    return request({
        url: '/rodLineJointDraw/getInfoByLegendGuidKey',
        method: 'get',
        params
    })
}

export function getModulematerialsTree(params) {
    return request({
        url: 'rodLineJointDraw/getModulematerialsTree',
        method: 'get',
        params
    })
}


export function getTuQianList(params) {
    return request({
        url: '/insertFrame/getTuQianList',
        method: 'get',
        params
    })
}

export function getVarSelectOption(params) {
    return request({
        url: '/insertFrame/getSelectOption',
        method: 'get',
        params
    })
}

export function getModuleTree(params) {
    return request({
        url: '/rodLineJointDraw/getMaterialTree',
        method: 'get',
        params
    })
}

export function getModuleTreeByEquipmentId(data) {
    return request({
        url: 'rodLineJointDraw/getModuleTreeByEquipmentId',
        method: 'post',
        data
    })
}

export function getModulesData(data) {
    return request({
        url: 'rodLineJointDraw/getModules',
        method: 'post',
        data
    })
}

export function getMaterialsByType(data) {
    return request({
        url: 'rodLineJointDraw/getMaterialsByType',
        method: 'post',
        data
    })
}

export function getMaterialDrawings(params) {
    return request({
        url: 'rodLineJointDraw/getMaterialDrawings',
        method: 'get',
        params
    })
}

export function getModuleInfoById(params) {
    return request({
        url: '/rodLineJointDraw/getModuleInfoById',
        method: 'get',
        params
    })
}

export function saveWithOldEquipmentId(data) {
    return request({
        url: 'rodLineJointDraw/saveWithOldEquipmentId',
        method: 'post',
        data
    })
}
export function getLegendInfo(data) {
    return request({
        url: 'rodLineJointDraw/getLegendtypekeyName',
        method: 'post',
        data,
    })
}

export function uploadTuQianFile(data) {
    return request({
        url: '/insertFrame/uploadTuQianFile',
        method: 'post',
        data,
        headers: {
            "Content-Type": "multipart/form-data",
        }
    })
}

export function getTuQianFileByPath(data) {
    return request({
        url: '/insertFrame/getTuQianFileByPath',
        method: 'post',
        data,
        responseType: 'blob'
    })
}

export function downloadTuQian(data) {
    return request({
        url: '/insertFrame/downloadTuQian',
        method: 'post',
        data,
        responseType: 'blob'
    })
}

export function saveTuQian(data) {
    return request({
        url: '/insertFrame/saveTuQian',
        method: 'post',
        data,
        headers: {
            "Content-Type": "multipart/form-data",
        }
    })
}


export function screenEquipment(data) {
    return request({
        url: '/insertFrame/screenEquipment',
        method: 'post',
        data,
    })
}

export function getTukuang(id) {
    return request({
        url: `/insertFrame/tzcf/getTukuang/${id}`,
        method: 'get',
    })
}

export function tzcfUploadFile(data) {
    return request({
        url: '/insertFrame/tzcf/uploadFile',
        method: 'post',
        data,
        headers: {
            "Content-Type": "multipart/form-data",
        },
    })
}
