///////////////////////////////////////////////////////////////////////////////
//版权所有（C）2002-2022，成都梦想凯德科技有限公司。
//本软件代码及其文档和相关资料归成都梦想凯德科技有限公司,应用包含本软件的程序必须包括以下版权声明
//此应用程序应与成都梦想凯德科技有限公司达成协议，使用本软件、其文档或相关材料
//https://www.mxdraw.com/
///////////////////////////////////////////////////////////////////////////////

import { McDbEntity, McDbLine, McDbPoint, McDbPolyline, MxCADUiPrPoint, MxCADUtility, MxCpp, McGePoint3d, McDbArc, McDbCircle, McDbEllipse, McGeVector3d, McDbHatch, McDbText, McDbMText, McDb, McDbRasterImageDef, McDbRasterImage, McDbBlockReference, McDbDimension } from "mxcad";
import { DetailedResult, DynamicInputType, MxDbRegularPolygonShape, MxFun } from "mxdraw";
function calculateCenter(start: McGePoint3d, end: McGePoint3d, bulge: number) {
  const dist = start.distanceTo(end)
  const dx = dist / 2
  const dy = dx * bulge
  const r = (dx * dx + dy * dy) / (2 * dy)
  const midX = (start.x + end.x) / 2
  const midY = (start.y + end.y) / 2

  return new McGePoint3d(midX, midY).addvec(end.clone().sub(start).perpVector().normalize().mult(r - dy))
}
function calculateLineAngle(center: McGePoint3d, start: McGePoint3d, end: McGePoint3d) {
  const deg = (Math.PI * 2) / 360
  // 计算直线的角度
  let startAngle = ((Math.atan2(start.y - center.y, start.x - center.x) * 180) / Math.PI) * deg
  let endAngle = ((Math.atan2(end.y - center.y, end.x - center.x) * 180) / Math.PI) * deg

  return {
    startAngle,
    endAngle
  }
}
MxFun.addCommand("list", async () => {
  // 选择对象获取信息
  const objIds = await MxCADUtility.userSelect("选择对象")
  const getPoint = new MxCADUiPrPoint()
  MxFun.acutPrintf("找到" + objIds.length + "个\n")
  const objId = objIds.pop()
  if (!objId) return
  const ent = objId.getMcDbEntity()
  if (!objId.isValid() || !ent) {
    MxFun.acutPrintf("对象无效\n")
  } else {
    if (await printInfo(ent) === false) return
  }
  while (objIds.length) {
    getPoint.setMessage("按 ENTER 键继续")
    getPoint.setDisableDynInput(true)
    await getPoint.go()
    // 程序中止、按下Esc键、新的命令运行时退出
    if (getPoint.getDetailedResult() === DetailedResult.kCodeAbort) return
    if (getPoint.getDetailedResult() === DetailedResult.kEcsIn) return
    if (getPoint.getDetailedResult() === DetailedResult.kNewCommadIn) return
    const objId = objIds.pop()
    if (!objId) break;
    const ent = objId.getMcDbEntity()
    if (!objId.isValid() || !ent) {
      MxFun.acutPrintf("对象无效\n")
      continue
    }
    if (await printInfo(ent) === false) return
  }
})
// 记录对象的名称
const entNames = {
  "McDbPoint": "点",
  "McDbLine": "直线",
  "McDbPolyline": "多段线",
  "McDbArc": "圆弧",
  "McDbCircle": "圆",
  "McDbEllipse": "椭圆",
  "McDbHatch": "填充",
  "McDbText": "单行文本",
  "McDbMText": "多行文本",
  "McDbRasterImage": "光栅图片",
  "McDbBlockReference": "块参照",
}
// 附着名称字典
const map = {
  Top: "上",
  Middle: "中",
  Bottom: "下",
  Base: "基",
  Left: "左",
  Center: "中",
  Right: "右",
  Align: "对齐",
  Fit: "适应",
  Mid: "中"
};

// 获取文字的附着名称
const getAttachmentName = (str: string) => {
  return str.replace(/k(\w+)/g, function (_, p1) {
    let key = p1.replace(/([A-Z])/g, "-$1") as string
    const arr = key.split("-")
    const name = arr.reduce((name, key) => {
      if (map[key]) {
        return name + map[key];
      } else {
        return "";
      }
    })
    return name
  });
}
// 打印信息
async function printInfo(ent: McDbEntity) {
  // 得到所有对象的基本信息
  const layer = ent.layer
  const layoutBlockName = MxCpp.getCurrentDatabase().currentSpace.name
  const isMainModel = layoutBlockName === "*Model_Space"
  const modeName = isMainModel ? "模型空间" : "图纸空间"
  const handle = ent.getHandle()
  console.log(ent.objectName)

  let entName = entNames[ent.objectName] || ent.objectName
  if(ent instanceof McDbDimension) {
    entName = "标注"
  }
  MxFun.acutPrintf(`\n                  ${entName}        图层: "${layer}"`)
  MxFun.acutPrintf(`\n                           空间: "${modeName}"`)

  MxFun.acutPrintf(`\n                    句柄 = ${handle}`)
  // 点对象的信息
  if (ent instanceof McDbPoint) {
    MxFun.acutPrintf(`\n                 于 点， X= ${ent.position.x.toFixed(4)}  Y= ${ent.position.y.toFixed(4)}  Z= ${ent.position.z.toFixed(4)}`)
  }
  // 直线对象的信息
  if (ent instanceof McDbLine) {
    MxFun.acutPrintf(`\n                 自 点， X= ${ent.startPoint.x.toFixed(4)}  Y= ${ent.startPoint.y.toFixed(4)}  Z= ${ent.startPoint.z.toFixed(4)}`)
    MxFun.acutPrintf(`\n                 到 点， X= ${ent.startPoint.x.toFixed(4)}  Y= ${ent.startPoint.y.toFixed(4)}  Z= ${ent.startPoint.z.toFixed(4)}`)
    let deltaX = ent.endPoint.x - ent.startPoint.x;
    let deltaY = ent.endPoint.y - ent.startPoint.y;
    let deltaZ = ent.endPoint.z - ent.startPoint.z;

    // 使用atan2计算角度，结果是弧度，转换为度
    let angleInDegrees = THREE.MathUtils.radToDeg(Math.atan2(deltaY, deltaX));

    MxFun.acutPrintf(`\n          长度 = ${ent.getLength().val.toFixed(4)}，在 XY 平面中的角度 = ${Math.floor(angleInDegrees)}`)
    MxFun.acutPrintf(`\n                  增量 X =  ${deltaX.toFixed(4)}，增量 Y =   ${deltaY.toFixed(4)}，增量 Z =   ${deltaZ.toFixed(4)}`)
  }

  // 多段线对象的信息
  if (ent instanceof McDbPolyline) {
    MxFun.acutPrintf(`\n            ${ent.isClosed ? "闭合" : "打开"}`)
    MxFun.acutPrintf(`\n        固定宽度    ${ent.constantWidth.toFixed(4)}`)
    MxFun.acutPrintf(`\n            面积   ${ent.getArea().val.toFixed(4)}`)
    MxFun.acutPrintf(`\n         周长   ${ent.getLength().val.toFixed(4)}`)

    // 输出多段线的每个点的信息
    const num = ent.numVerts()
    // 通过取点对象控制输出量 只有在按下空格或者回车才会继续打印剩余信息
    const getPoint = new MxCADUiPrPoint()

    // 记录点包含的其他信息如线宽、凸度值等
    const widthInfos: {
      val1: number;
      val2: number;
      ret: boolean;
    }[] = []
    const bulges: number[] = []
    // 记录是否打印线宽
    let isPrintWidth = false

    for (let index = 0; index < num; index++) {
      const width = ent.getWidthsAt(index)
      const bulge = ent.getBulgeAt(index)
      if (width.val1 !== 0 || width.val2 !== 0) {
        isPrintWidth = true
      }
      widthInfos.push(width)
      bulges.push(bulge)
    }
    // 控制输出打印量
    let accumulated = 0
    const maxIndex = 3

    for (let index = 0; index < num; index++) {
      if (accumulated > maxIndex) {
        while (true) {
          getPoint.setMessage("按 ENTER 键继续")
          getPoint.disableAllTrace()
          getPoint.clearLastInputPoint()
          // 设置不需要动态的输入框
          getPoint.setDynamicInputType(DynamicInputType.kNoInput)
          const pt = await getPoint.go()
          if (getPoint.getDetailedResult() === DetailedResult.kCodeAbort) return
          if (getPoint.getDetailedResult() === DetailedResult.kEcsIn) return
          if (getPoint.getDetailedResult() === DetailedResult.kNewCommadIn) return false
          // 只有回车和空格或者鼠标左键点击才能继续往下走打印的流程， 否则始终循环等待用户输入
          if (getPoint.getDetailedResult() === DetailedResult.kNullEnterIn || getPoint.getDetailedResult() === DetailedResult.kNullSpaceIn || pt) {
            accumulated = 0
            break;
          }
        }
      }
      const point = ent.getPointAt(index).val
      const bulge = bulges[index]

      MxFun.acutPrintf(`\n          于端点  X=  ${point.x.toFixed(1)}  Y=  ${point.y.toFixed(1)}  Z=  ${point.z.toFixed(1)}`)
      if (isPrintWidth) {
        MxFun.acutPrintf(`\n    起点宽度  ${widthInfos[index].val1.toFixed(4)}`)
        MxFun.acutPrintf(`\n      端点宽度  ${widthInfos[index].val2.toFixed(4)}`)
      }
      // 凸度不为0时 且
      if (Math.abs(bulge) > 0) {
        MxFun.acutPrintf(`\n             凸度       ${bulge.toFixed(1)}`)
        const nextPoint = ent.getPointAt(index + 1).val
        const center = calculateCenter(point, nextPoint, bulge)
        MxFun.acutPrintf(`\n            圆心  X=  ${center.x.toFixed(1)}  Y=  ${center.y.toFixed(1)}  Z=  ${center.z.toFixed(1)}`)
        MxFun.acutPrintf(`\n            半径    ${center.distanceTo(point).toFixed(1)}`)
        const { startAngle, endAngle } = calculateLineAngle(center, point, nextPoint)
        MxFun.acutPrintf(`\n       起点角度       ${THREE.MathUtils.radToDeg(startAngle).toFixed(0)}`)
        MxFun.acutPrintf(`\n       端点角度       ${THREE.MathUtils.radToDeg(endAngle).toFixed(0)}`)
        // 最后一次循环加上最后一个端点信息
        if (index === num - 1) {
          MxFun.acutPrintf(`\n          于端点  X=  ${nextPoint.x.toFixed(1)}  Y=  ${nextPoint.y.toFixed(1)}  Z=  ${nextPoint.z.toFixed(1)}`)
        }
      }
      accumulated++
    }
  }
  // 圆弧的信息
  if (ent instanceof McDbArc) {
    MxFun.acutPrintf(`\n                圆心 点， X= ${ent.center.x.toFixed(4)}  Y= ${ent.center.y.toFixed(4)}  Z= ${ent.center.z.toFixed(4)}`)
    MxFun.acutPrintf(`\n                半径  ${ent.radius.toFixed(4)}`)
    MxFun.acutPrintf(`\n                起点 角度    ${THREE.MathUtils.radToDeg(ent.startAngle).toFixed(0)}`)
    MxFun.acutPrintf(`\n                端点 角度    ${THREE.MathUtils.radToDeg(ent.endAngle).toFixed(0)}`)
    MxFun.acutPrintf(`\n            长度 ${ent.getLength().val.toFixed(4)}`)
  }
  // 圆的信息
  if (ent instanceof McDbCircle) {
    MxFun.acutPrintf(`\n                圆心 点， X= ${ent.center.x.toFixed(4)}  Y= ${ent.center.y.toFixed(4)}  Z= ${ent.center.z.toFixed(4)}`)
    MxFun.acutPrintf(`\n                半径  ${ent.radius.toFixed(4)}`)
    MxFun.acutPrintf(`\n                周长 ${ent.getLength().val.toFixed(4)}`)
    MxFun.acutPrintf(`\n                面积 ${(Math.PI * ent.radius * ent.radius).toFixed(4)}`)
  }

  // 椭圆的信息
  if (ent instanceof McDbEllipse) {
    // 得到椭圆的半轴长度
    const radius1 = ent.majorAxis.length()
    const radius2 = ent.minorAxis.length()
    // 是否是椭圆 为false表示是椭圆弧
    const isEllipse = ent.startAngle === 0 && ent.endAngle === 2 * Math.PI
    if (isEllipse) {
      const calculateEllipseCircumference = (radius1: number, radius2: number) => {
        const h = Math.pow((radius1 - radius2), 2) / Math.pow((radius1 + radius2), radius2);
        const circumference = Math.PI * (radius1 + radius2) * (1 + (3 * h) / (10 + Math.sqrt(4 - 3 * h)));
        return circumference;
      }
      MxFun.acutPrintf(`\n                               面积: ${(Math.PI * radius1 * radius2).toFixed(4)}`)
      MxFun.acutPrintf(`\n                               圆周: ${calculateEllipseCircumference(radius1, radius2).toFixed(4)}`)
    }
    else {
      // 计算椭圆弧的长度
      const arcLengthOfEllipse = (a: number, b: number, theta1: number, theta2: number, numSteps: number) => {
        // a: 长半轴
        // b: 短半轴
        // 定义被积函数
        const integrand = function (theta: number) {
          return Math.sqrt(a * a * Math.sin(theta) * Math.sin(theta) + b * b * Math.cos(theta) * Math.cos(theta));
        };

        // 使用复合辛普森法则进行数值积分
        let integral = 0;
        const stepSize = (theta2 - theta1) / numSteps;
        for (let i = 0; i <= numSteps; i++) {
          const theta = theta1 + i * stepSize;
          if (i === 0 || i === numSteps) {
            integral += integrand(theta);
          } else if (i % 2 === 0) {
            integral += 2 * integrand(theta);
          } else {
            integral += 4 * integrand(theta);
          }
        }

        integral *= stepSize / 3;
        return Math.abs(integral);
      }

      MxFun.acutPrintf(`\n                               长度: ${arcLengthOfEllipse(radius1, radius2, ent.startAngle, ent.endAngle, 30).toFixed(4)}`)
    }
    MxFun.acutPrintf(`\n                              中心点: X = ${ent.center.x.toFixed(4)} , Y = ${ent.center.y.toFixed(4)}   , Z = ${ent.center.z.toFixed(4)}`)
    // 判断哪个是半轴长度较长的轴
    const isMaxAxis = radius1 > radius2
    const maxAxis = isMaxAxis ? ent.majorAxis : ent.minorAxis
    const minAxis = isMaxAxis ? ent.minorAxis : ent.majorAxis
    MxFun.acutPrintf(`\n                               长轴: X = ${maxAxis.x.toFixed(4)}  , Y = ${maxAxis.y.toFixed(4)}  , Z = ${maxAxis.z.toFixed(4)}`)
    MxFun.acutPrintf(`\n                               短轴: X = ${minAxis.x.toFixed(4)}  , Y = ${minAxis.y.toFixed(4)}  , Z = ${minAxis.z.toFixed(4)}`)
    // 椭圆弧的其他信息
    if (!isEllipse) {
      // 计算椭圆弧的起点和端点
      const calculateEllipseEndpoints = (center: McGePoint3d, v1: McGeVector3d, v2: McGeVector3d, theta1: number, theta2: number) => {
        // 椭圆中心坐标
        const cx = center.x;
        const cy = center.y;

        // 主轴向量
        const vx1 = v1.x;
        const vy1 = v1.y;

        //  副轴向量
        const vx2 = v2.x;
        const vy2 = v2.y;

        // 计算椭圆的起始点和结束点
        const alpha = Math.atan2(vy1, vx1)

        const a = Math.sqrt(vx1 * vx1 + vy1 * vy1);  // 长轴半径
        const b = Math.sqrt(vx2 * vx2 + vy2 * vy2);  // 短轴半径
        // 根据角度计算起始点和结束点
        const startEndpointX = cx + a * Math.cos(theta1) * Math.cos(alpha) - b * Math.sin(theta1) * Math.sin(alpha);
        const startEndpointY = cy + a * Math.cos(theta1) * Math.sin(alpha) + b * Math.sin(theta1) * Math.cos(alpha);
        const endEndpointX = cx + a * Math.cos(theta2) * Math.cos(alpha) - b * Math.sin(theta2) * Math.sin(alpha);
        const endEndpointY = cy + a * Math.cos(theta2) * Math.sin(alpha) + b * Math.sin(theta2) * Math.cos(alpha);

        return {
          startPoint: new McGePoint3d(startEndpointX, startEndpointY),
          endPoint: new McGePoint3d(endEndpointX, endEndpointY)
        };
      }

      const { startPoint, endPoint } = calculateEllipseEndpoints(ent.center, ent.majorAxis, ent.minorAxis, ent.startAngle, ent.endAngle)
      MxFun.acutPrintf(`\n                               起点: X = ${startPoint.x.toFixed(4)} , Y = ${startPoint.y.toFixed(4)}  , Z = ${startPoint.z.toFixed(4)}`)
      MxFun.acutPrintf(`\n                               端点: X = ${endPoint.x.toFixed(4)} , Y = ${endPoint.y.toFixed(4)}  , Z = ${endPoint.z.toFixed(4)}`)
      MxFun.acutPrintf(`\n                             起点角度: ${THREE.MathUtils.radToDeg(ent.startAngle).toFixed(0)}`)
      MxFun.acutPrintf(`\n                             端点角度: ${THREE.MathUtils.radToDeg(ent.endAngle).toFixed(0)}`)
    }
    MxFun.acutPrintf(`\n                             半径比例: ${ent.radiusRatio.toFixed(4)}`)
  }

  // 填充对象信息
  if(ent instanceof McDbHatch) {
    const patternName =  ent.patternName()
    MxFun.acutPrintf(`\n                 填充图案 ${patternName}`)
    MxFun.acutPrintf(`\n                 图案填充比例    ${ent.patternScale.toFixed(4)}`)
    MxFun.acutPrintf(`\n                 图案填充角度      ${THREE.MathUtils.radToDeg(ent.patternAngle).toFixed(0)}`)
    const pl = new McDbPolyline()
    const pls: McDbPolyline[] = []
    pl.isClosed = true
    for (let index = 0; index < ent.numLoops; index++) {
      const { vertices, bulges } = ent.getLoopAt(index)
      vertices.forEach((pt, index)=> {
        const bulge = bulges[index]
        pl.addVertexAt(pt, Number(bulge.toFixed(14)))
      })
      pls.push(pl)
      MxCpp.getCurrentMxCAD().drawEntity(pl)
    }
    const totalArea = pls.reduce((totalArea, pl)=> totalArea + pl.getArea().val, 0)
    MxFun.acutPrintf(`\n                 面积      ${totalArea.toFixed(4)}`)
  }

  // 单行文字信息
  if(ent instanceof McDbText) {
    MxFun.acutPrintf(`\n              样式 = "${ent.textStyle}"`)
    MxFun.acutPrintf(`\n                起点 点， X=  ${ent.position.x.toFixed(4)}  Y= ${ent.position.y.toFixed(4)}  Z=   ${ent.position.z.toFixed(4)}`)
    MxFun.acutPrintf(`\n                高度    ${ent.height.toFixed(4)}`)
    MxFun.acutPrintf(`\n                文字 ${ent.textString}`)
    MxFun.acutPrintf(`\n                旋转 角度    ${ent.rotation.toFixed(0)}`)
    MxFun.acutPrintf(`\n                宽度 比例因子    ${ent.widthFactor.toFixed(4)}`)
  }
  // 多行文字信息
  if(ent instanceof McDbMText) {
    MxFun.acutPrintf(`\n              样式 = "${ent.textStyle}"`)
    MxFun.acutPrintf(`\n位置:         X=  ${ent.location.x.toFixed(4)}  Y=   ${ent.location.y.toFixed(4)}  Z=   ${ent.location.z.toFixed(4)}`)
    MxFun.acutPrintf(`\n宽度:           ${ent.width.toFixed(4)}`)
    MxFun.acutPrintf(`\n法向:         X=   ${ent.normal.x.toFixed(4)}  Y=   ${ent.normal.y.toFixed(4)}  Z=   ${ent.normal.z.toFixed(4)}`)
    MxFun.acutPrintf(`\n旋转:                 ${ent.rotation.toFixed(0)}`)
    MxFun.acutPrintf(`\n文字高度:        ${ent.textHeight.toFixed(4)}`)

    MxFun.acutPrintf(`\n附着:        ${getAttachmentName(McDb.AttachmentPoint[ent.attachment])}`)
    MxFun.acutPrintf(`\n内容:        ${ent.contents}`)
  }
  // 光栅图片信息
  if(ent instanceof McDbRasterImage) {
    const imgDef = ent.imageDefId().getMcDbRasterImageDef()
    const getFileNameFromPath = (path: string)=> {
      return path.split('/').pop().split('\\').pop();
    }
    MxFun.acutPrintf(`\n图像:                   ${getFileNameFromPath(imgDef.sourceFileName)}`)

    const getClipBoundaryType = (ent: McDbRasterImage)=> {
      const type = ent.clipBoundaryType()
      if(type === McDb.ClipBoundaryType.kInvalid) return "无效"
      if(type === McDb.ClipBoundaryType.kPoly) return "多边形"
      if(type === McDb.ClipBoundaryType.kRect) return "矩形"
    }
    MxFun.acutPrintf(`\n剪裁边界类型:      ${getClipBoundaryType(ent)}`)
    const clipBoundary =  ent.clipBoundary()
    MxFun.acutPrintf(`\n边界点数目: ${clipBoundary.length()}`)
    ent.clipBoundary().forEach((pt)=> {
      MxFun.acutPrintf(`\n       位于点，X=   ${pt.x.toFixed(4)}, Y=   ${pt.y.toFixed(4)}, Z=   ${pt.z.toFixed(4)}`)
    })
  }

  // 图块信息
  if(ent instanceof McDbBlockReference) {
    MxFun.acutPrintf(`\n块名:               "${ent.blockName}"`)
    MxFun.acutPrintf(`\n插入点:             X= ${ent.position.x.toFixed(4)}, Y=   ${ent.position.y.toFixed(4)}, Z=   ${ent.position.z.toFixed(4)}`)
    MxFun.acutPrintf(`\nX 比例因子:         ${ent.scaleFactors.x.toFixed(4)}`)
    MxFun.acutPrintf(`\nY 比例因子:         ${ent.scaleFactors.y.toFixed(4)}`)
    MxFun.acutPrintf(`\n  旋转角度:         ${ent.rotation.toFixed(0)}`)
    MxFun.acutPrintf(`\nZ 比例因子:         ${ent.scaleFactors.z.toFixed(4)}`)
    MxFun.acutPrintf(`\nZ 比例因子:         ${ent.scaleFactors.z.toFixed(4)}`)
    MxFun.acutPrintf(`\n  缩放比例:         ${ent.getScale().toFixed(4)}`)
  }

  // 标注信息
  if(ent instanceof McDbDimension) {
    MxFun.acutPrintf(`\n文字位置:           X= ${ent.textPosition.x.toFixed(4)} Y= ${ent.textPosition.y.toFixed(4)} Z= ${ent.textPosition.z.toFixed(4)}`)
    MxFun.acutPrintf(`\n是否使用的默认文字位置:           ${ent.isUsingDefaultTextPosition()}`)
    MxFun.acutPrintf(`\n高度:          ${ent.elevation.toFixed(4)}`)
    MxFun.acutPrintf(`\n文本内容:       ${ent.dimensionText}`)
    MxFun.acutPrintf(`\n文本旋转角度:   ${THREE.MathUtils.radToDeg(ent.textRotation).toFixed(0)}`)
    const dimStyle = ent.dimensionStyle.getMcDbDimStyleTableRecord()
    MxFun.acutPrintf(`\n标注样式:       ${dimStyle.name}`)
    MxFun.acutPrintf(`\n文字附着:      ${getAttachmentName(McDb.AttachmentPoint[ent.textAttachment])}`)
    MxFun.acutPrintf(`\n水平旋转角度:   ${THREE.MathUtils.radToDeg(ent.horizontalRotation).toFixed(0)}`)
    MxFun.acutPrintf(`\n水平旋转角度:   ${THREE.MathUtils.radToDeg(ent.horizontalRotation).toFixed(0)}`)
  }
  MxFun.acutPrintf("\n命令")
}

