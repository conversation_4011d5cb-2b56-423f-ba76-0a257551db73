import{M as v}from"./index-8X61wlK0.js";import{aJ as t,aK as l,aL as o,M as x,aM as p,aN as g}from"./index-D95UjFey.js";import{i as f}from"./vuetify-B_xYg4qv.js";import{h as w,w as h,a0 as M,_ as B,$ as n,a1 as i,m as r,u,B as m,V}from"./vue-DfH9C9Rx.js";import"./mxcad-CfPpL1Bn.js";import"./mxdraw-C_n_7lEs.js";import"./mapbox-gl-PN3pDJHq.js";import"./xlsx-Ck5MUZvf.js";const y={class:"px-3"},T={class:"px-2"},U={class:""},k={class:""},H=w({__name:"index",setup(D){h(o,e=>{e||(t.value="",l.value=0)});const d=()=>{g({text:t.value,angle:THREE.MathUtils.degToRad(l.value)}),t.value=""},c=[{name:"确定",fun:()=>{d()},primary:!0},{name:"关闭",fun:()=>p(!1)}],_={esc:()=>p(!1),enter:()=>{d()}};return(e,a)=>(M(),B(v,{title:e.t("640"),"max-width":"360",modelValue:u(o),"onUpdate:modelValue":a[2]||(a[2]=s=>m(o)?o.value=s:null),footerBtnList:c,persistent:"",keys:_},{default:n(()=>[i("div",y,[r(x,{title:e.t("641")},{default:n(()=>[i("div",T,[r(f,{class:"mt-1",modelValue:u(t),"onUpdate:modelValue":a[0]||(a[0]=s=>m(t)?t.value=s:null),autofocus:""},{prepend:n(()=>[i("span",U,V(e.t("594"))+":",1)]),_:1},8,["modelValue"]),r(f,{class:"mt-1 w-50",modelValue:u(l),"onUpdate:modelValue":a[1]||(a[1]=s=>m(l)?l.value=s:null),type:"number"},{prepend:n(()=>[i("span",k,V(e.t("479"))+":",1)]),_:1},8,["modelValue"])])]),_:1},8,["title"])])]),_:1},8,["title","modelValue"]))}});export{H as default};
