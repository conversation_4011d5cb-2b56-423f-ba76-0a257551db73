import{m as b,n as c,e as h,a2 as V,a3 as f,a4 as w}from"./vuetify-BqCp6y38.js";import{h as _,_ as a,$ as s,m as d,a4 as t,F as m,a5 as i,a3 as l,Q as y,V as P,a1 as g,a9 as v,n as r,L as T}from"./vue-Cj9QYd7Z.js";import{_ as k}from"./index-CzBriCFR.js";const C={class:"mx-border mt-2"},W={key:0,class:"w-100 h-100 border-bottom"},B=_({__name:"index",props:{modelValue:{default:0},items:{},height:{default:300},tabsProps:{},windowProps:{},windowItemProps:{},isTabMinWidthAuto:{type:Boolean}},emits:["update:modelValue"],setup(I,{emit:u}){const p=u,n=e=>{p("update:modelValue",e)};return(e,M)=>(a(),s("div",C,[d(c,r({"model-value":e.modelValue,"onUpdate:modelValue":n},e.tabsProps),{default:t(()=>[(a(!0),s(m,null,i(e.items,o=>(a(),l(b,{key:o.tab,class:"mx-tab","selected-class":"tab-selected",style:g(e.isTabMinWidthAuto?"min-width: auto;":"")},{default:t(()=>[y(P(e.t(o.tab)),1)]),_:2},1032,["style"]))),128)),!e.tabsProps||!e.tabsProps.grow?(a(),s("div",W)):v("",!0)]),_:1},16,["model-value"]),d(w,r({"model-value":e.modelValue,"onUpdate:modelValue":n},e.windowProps),{default:t(()=>[(a(!0),s(m,null,i(e.items,o=>(a(),l(f,r({key:o.tab,ref_for:!0},e.windowItemProps),{default:t(()=>[d(h,{height:e.height},{default:t(()=>[d(V,{class:"px-3 py-0"},{default:t(()=>[(a(),l(T(o.component)))]),_:2},1024)]),_:2},1032,["height"])]),_:2},1040))),128))]),_:1},16,["model-value"])]))}}),F=k(B,[["__scopeId","data-v-e3ed8050"]]);export{F as M};
