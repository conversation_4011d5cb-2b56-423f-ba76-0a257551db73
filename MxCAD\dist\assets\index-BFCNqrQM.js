import{M as j}from"./index-8X61wlK0.js";import{V as O,a as p,n as W,M as P}from"./index-D95UjFey.js";import{E as g,$ as X,b as Y,d as Z,C as I,j as U,i as f}from"./vuetify-B_xYg4qv.js";import{h as Q,d as q,c as H,a0 as R,_ as T,$ as t,a1 as u,m as s,u as l,B as b,Q as r,V as d,n as J,a5 as K}from"./vue-DfH9C9Rx.js";import"./mxcad-CfPpL1Bn.js";import"./mxdraw-C_n_7lEs.js";import"./mapbox-gl-PN3pDJHq.js";import"./xlsx-Ck5MUZvf.js";const _={class:"px-3"},c={class:"d-flex"},h={class:""},me=Q({__name:"index",setup(ee){const{isShow:D,showDialog:$,insertBlock:x,list:A,currentItem:n,openFile:w,isGetInsertionPoint:m,insertionPoint:v,isGetProportion:i,proportion:y,isUniformProportion:V,isGetRotation:k,rotation:N,isDecomposition:B,isAutoComputeOrigin:E,isBlockLibrary:C,isBlockLibraryDecomposition:L,isBlockLibraryAutoComputeOrigin:M}=O(),z=q(),F=[{name:"确定",fun:x,primary:!0},{name:"关闭",fun:()=>$(!1)}],G={n:()=>{(z.value?.$el).getElementsByTagName("input")[0]?.focus()},b:w,s:()=>{m.value=!m.value},e:()=>{i.value=!i.value},c:()=>{k.value=!k.value},u:()=>{V.value=!V.value},d:()=>{B.value=!B.value},enter:()=>{x()}},S=H(()=>n.value?.filePath?new URL(n.value.filePath).pathname.split("/").pop():"");return(o,e)=>(R(),T(j,{title:o.t("526"),modelValue:l(D),"onUpdate:modelValue":e[14]||(e[14]=a=>b(D)?D.value=a:null),"max-width":"470",footerBtnList:F,keys:G},{default:t(()=>[u("div",_,[s(I,null,{default:t(()=>[s(g,{class:"mt-1"},{default:t(()=>[u("div",c,[s(l(X),{class:"mt-1",ref_key:"autocomplete",ref:z,items:l(A),"item-title":"name","return-object":"",modelValue:l(n),"onUpdate:modelValue":e[0]||(e[0]=a=>b(n)?n.value=a:null)},{prepend:t(()=>[s(p,{class:"","key-name":"N"},{default:t(()=>[r(d(o.t("209")),1)]),_:1})]),_:1},8,["items","modelValue"]),s(W,{onClick:l(w),class:"ml-1"},{default:t(()=>[s(p,{"key-name":"B"},{default:t(()=>[r(d(o.t("210")),1)]),_:1}),s(Y,{icon:"class:iconfont more"})]),_:1},8,["onClick"])]),l(n)&&l(n).filePath?(R(),T(Z,{key:0,text:l(n).filePath},{activator:t(({props:a})=>[u("p",J({class:"mt-1 text-truncate"},a),d(o.t("527"))+": "+d(S.value),17)]),_:1},8,["text"])):K("",!0)]),_:1}),s(g,{cols:3,class:"h-100"})]),_:1}),s(I,{"align-stretch":""},{default:t(()=>[s(g,{cols:"4","align-self":"stretch"},{default:t(()=>[s(P,{title:o.t("475"),class:"h-100"},{default:t(()=>[s(U,{class:"",modelValue:l(m),"onUpdate:modelValue":e[1]||(e[1]=a=>b(m)?m.value=a:null)},{label:t(()=>[s(p,{"key-name":"S"},{default:t(()=>[r(d(o.t("215")),1)]),_:1})]),_:1},8,["modelValue"]),s(f,{class:"mt-1",type:"number",modelValue:l(v).x,"onUpdate:modelValue":e[2]||(e[2]=a=>l(v).x=a),disabled:l(m)},{prepend:t(()=>e[15]||(e[15]=[u("span",{class:""}," X:",-1)])),_:1},8,["modelValue","disabled"]),s(f,{class:"mt-1",type:"number",modelValue:l(v).y,"onUpdate:modelValue":e[3]||(e[3]=a=>l(v).y=a),disabled:l(m)},{prepend:t(()=>e[16]||(e[16]=[u("span",{class:""}," Y:",-1)])),_:1},8,["modelValue","disabled"]),s(f,{class:"mt-1",type:"number",modelValue:l(v).z,"onUpdate:modelValue":e[4]||(e[4]=a=>l(v).z=a),disabled:l(m)},{prepend:t(()=>e[17]||(e[17]=[u("span",{class:""}," Z:",-1)])),_:1},8,["modelValue","disabled"])]),_:1},8,["title"])]),_:1}),s(g,{cols:"4","align-self":"stretch"},{default:t(()=>[s(P,{title:o.t("240"),class:"h-100"},{default:t(()=>[s(U,{class:"",modelValue:l(i),"onUpdate:modelValue":e[5]||(e[5]=a=>b(i)?i.value=a:null)},{label:t(()=>[s(p,{"key-name":"E"},{default:t(()=>[r(d(o.t("215")),1)]),_:1})]),_:1},8,["modelValue"]),s(f,{class:"mt-1",type:"number",step:"0.001",modelValue:l(y).x,"onUpdate:modelValue":e[6]||(e[6]=a=>l(y).x=a),disabled:l(i)},{prepend:t(()=>e[18]||(e[18]=[u("span",{class:""}," X:",-1)])),_:1},8,["modelValue","disabled"]),s(f,{class:"mt-1",type:"number",modelValue:l(y).y,"onUpdate:modelValue":e[7]||(e[7]=a=>l(y).y=a),step:"0.001",disabled:l(i)||l(V)},{prepend:t(()=>e[19]||(e[19]=[u("span",{class:""}," Y:",-1)])),_:1},8,["modelValue","disabled"]),s(f,{class:"mt-1",type:"number",modelValue:l(y).z,"onUpdate:modelValue":e[8]||(e[8]=a=>l(y).z=a),step:"0.001",disabled:l(i)||l(V)},{prepend:t(()=>e[20]||(e[20]=[u("span",{class:""}," Z:",-1)])),_:1},8,["modelValue","disabled"]),s(U,{class:"ml-4 mt-1",modelValue:l(V),"onUpdate:modelValue":e[9]||(e[9]=a=>b(V)?V.value=a:null)},{label:t(()=>[s(p,{"key-name":"U"},{default:t(()=>[r(d(o.t("528")),1)]),_:1})]),_:1},8,["modelValue"])]),_:1},8,["title"])]),_:1}),s(g,{cols:"4","align-self":"stretch"},{default:t(()=>[s(P,{title:o.t("354"),class:"h-100"},{default:t(()=>[s(U,{class:"",modelValue:l(k),"onUpdate:modelValue":e[10]||(e[10]=a=>b(k)?k.value=a:null)},{label:t(()=>[s(p,{"key-name":"C"},{default:t(()=>[r(d(o.t("215")),1)]),_:1})]),_:1},8,["modelValue"]),s(f,{class:"mt-1",type:"number",modelValue:l(N),"onUpdate:modelValue":e[11]||(e[11]=a=>b(N)?N.value=a:null),disabled:l(k)},{prepend:t(()=>[u("span",h,d(o.t("281"))+":",1)]),_:1},8,["modelValue","disabled"])]),_:1},8,["title"])]),_:1})]),_:1}),s(U,{class:"mt-2","model-value":l(C)?l(L):l(B),"onUpdate:modelValue":e[12]||(e[12]=a=>{l(C)?L.value=!!a:B.value=!!a})},{label:t(()=>[s(p,{"key-name":"D"},{default:t(()=>[r(d(o.t("529")),1)]),_:1})]),_:1},8,["model-value"]),s(U,{class:"mt-2","model-value":l(C)?l(M):l(E),"onUpdate:modelValue":e[13]||(e[13]=a=>{l(C)?M.value=!!a:E.value=!!a})},{label:t(()=>[s(p,{"key-name":"D"},{default:t(()=>[r(d(o.t("530")),1)]),_:1})]),_:1},8,["model-value"])])]),_:1},8,["title","modelValue"]))}});export{me as default};
