<template>
<data-dialog
      @close="closeDialog"
      dataWidth="500"
      v-if="appStore.mapIndex == '设计合理性分析'"
  >
    <template #header>
      <h4 style="margin-left: 10px; margin-top: 0px; line-height: 39px">
        合理性分析
      </h4>
    </template>
    <template #body>
      <div class="small-material" style="background: #e4f2f2">
        <div style="padding: 10px">
          <el-table
              class="small-size"
              border
              style="margin-top: -2px; height: 300px; width: 480px"
              size="small"
              :data="reasonable"
          >
            <!-- <el-table-column label="序号" width="50"></el-table-column> -->
            <el-table-column align="center" prop="buildType" label="类型">
            </el-table-column>
            <el-table-column align="center" prop="legendName" label="名称">
            </el-table-column>
            <el-table-column align="center" prop="alertMessage" label="提示">
            </el-table-column>
            <!-- <el-table-column align="center" label="操作">
              <template #default="scope">
                  <span
                      style="
                      padding: 4px 10px;
                      border-radius: 10px;
                      color: black;
                      background-color: #b5dddd;
                      cursor: pointer;
                    "
                  >查看</span
                  >
              </template>
            </el-table-column> -->
          </el-table>
        </div>
      </div>
    </template>
  </data-dialog>
</template>
<script setup>
import DataDialog from "@/components/DataDialog/index.vue";
import useAppStore from "@/store/modules/app.js";
import {
    getreasonable
} from "@/api/desginManage/ToolManagement.js";
const route = useRoute();

const appStore = useAppStore();
const closeDialog = () => {
    appStore.mapIndex = "";
};
const activeName = ref('first')
const handleClick = () => {

}
const reasonable = ref({
    buildType:"",
    legendName:"",
    alertMessage:""
})
const dataList = () => {
    getreasonable({taskId:route.query.id}).then(res => {
      console.log(reasonable)
      console.log(reasonable.value)
    // treeData.value = convertcurrent(res.data)
    console.log(res.data)
    reasonable.value=res.data
    console.log(reasonable.value)
  })
}
watch(
    () => appStore.mapIndex,
    (newInfo, oldInfo) => {
        console.log(newInfo, oldInfo);
        if (newInfo == "设计合理性分析") {
            dataList()
      }
    }
);
</script>
<style scoped lang="scss">
@use '../../index' as *;

::v-deep .el-tabs__header {
    margin: 0;
}

.threePane-tabs>.el-tabs__content {
    padding: 32px;
    color: #6b778c;
    font-size: 32px;
    font-weight: 600;
}

.threePane-tabs .custom-tabs-label .el-icon {
    vertical-align: middle;
}

.threePane-tabs .custom-tabs-label span {
    vertical-align: middle;
    margin-left: 4px;
}
</style>