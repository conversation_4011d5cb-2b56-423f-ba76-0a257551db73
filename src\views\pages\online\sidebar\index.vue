<script setup>
import Logo from "@/layout/components/Sidebar/Logo.vue";
import useSettingsStore from "@/store/modules/settings.js";
import useAppStore from "@/store/modules/app.js";

import ResultsCatalog from "./results-catalog";
import Property from "./property";

const appStore = useAppStore()
const settingsStore = useSettingsStore()
const showLogo = computed(() => settingsStore.sidebarLogo);
const isCollapse = computed(() => !appStore.sidebar.opened);
const radio = ref('1')
const childRefB = ref(null)
const MethodB = () => {
  if (childRefB.value) {
    childRefB.value.dataTreeList();
  }
};

// 暴露方法给父组件
defineExpose({MethodB});
watch(
  () => appStore.property,
  (newInfo, oldInfo) => {
    radio.value='2'
  console.log(newInfo,'sdk点击数据')
  })
</script>

<template>
  <div class="sidebar-container">
    <logo v-if="showLogo" :collapse="isCollapse"/>
    <el-radio-group class="radio-group" v-model="radio" size="large">
      <el-radio-button label="成果目录" value="1"/>
      <el-radio-button label="属性" value="2"/>
      <el-radio-button label="设备树" value="3"/>
    </el-radio-group>
    <el-scrollbar height="calc(100vh - 180px - 40px)">
      <results-catalog v-show="radio === '1'" ref="childRefB"/>
      <property v-show="radio === '2'" :list="appStore.property" />
    </el-scrollbar>
  </div>
</template>

<style scoped lang="scss">
.sidebar-container {
  background: var(--sidebar-bg);

  .radio-group {
    display: flex;

    .el-radio-button {
      flex: 1;

      :deep(.el-radio-button__inner) {
        width: 100%;
      }
    }
  }
}
</style>
