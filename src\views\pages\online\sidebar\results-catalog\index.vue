<script setup>
import {McObject} from "mxcad"
const mxcad = new McObject()
import TreeData from "@/components/TreeData/index.vue";
import {ElLoading} from 'element-plus'
import {deleteTree, downloadFile, downloadFileDWG, specificationTree,downloadFileExcel} from "@/api/task/index.js";
import {useRoute} from "vue-router";
import {tansParams} from "@/utils/ruoyi.js";
import {getToken} from "@/utils/auth.js";
import "@vue-office/excel/lib/index.css"
import VueOfficeExcel from "@vue-office/excel";
import VueOfficeDocx from "@vue-office/docx";
import {getName,designFilePart} from "@/api/insertSag/index.js";
import SparkMD5 from "spark-md5"
const {proxy} = getCurrentInstance();
const urlRoute = new useRoute()
const iframe = ref(null);
const route = useRoute();
const cadOrPdf = ref('')
const data = ref([])
const defaultProps = {
  children: 'children',
  label: 'specificationName',
}
let specificationName=ref('')
let fileLists = ref({})
// 树形侧边栏鼠标右键事件
const handleNodeContextMenu = (event, data, node, tree) => {
  specificationName.value=data.specificationName
  fileLists.value = data
  level.value = node.level
  if (node.level > 2) {
    if (node.level === 3) {
      delTitle.value = '删除所有文件'
    } else if (node.level === 4) {
      delTitle.value = '删除文件'
    }
    selectedNode.value = data; // 保存当前节点数据
    // 动态设置菜单位置
    menuPosition.value = {
      position: 'absolute',
      top: `${event.clientY - 200}px`,
      left: `${event.clientX}px`,
    };
    showMenu.value = true; // 显示菜单
  }
}
const num = ref(0)
// 点击文件实现文件预览
const nodeClick = (item, node) => {
  if (item.specificationName) {
    cadOrPdf.value = item.specificationName
    console.log("🚀 ~ nodeClick ~ cadOrPdf.value:", cadOrPdf.value)
    // 文件在第4层级
    if (node.level === 4) {
      const loading = ElLoading.service({
        lock: true,
        text: '加载中...',
        background: 'rgba(0, 0, 0, 0.7)',
      })
      const urlNew = item.specificationName.slice(-4) === '.dwg' || item.specificationName.slice(-6) === '.mxweb' ? downloadFileDWG(item.id) : downloadFileExcel(item.id)
      urlNew.then(res => {
        dialogPdf.value = true
        // const blob = new Blob([res]);
        const openUrl = window.URL.createObjectURL(res)
        if (item.specificationName.slice(-4) === '.dwg' || item.specificationName.slice(-6) === '.mxweb') {
          let params = tansParams({
            ...urlRoute.query,
            token: getToken() || urlRoute.query?.token,
            mod: 'preview'
          })
          // const src = params.length !== 0
          //   ? `${import.meta.env.VITE_APP_MX_APP_SRC}?${params.endsWith('&') ? params.substring(0, params.length - 1) : params}`
          //   : `${import.meta.env.VITE_APP_MX_APP_SRC}`
            const src=`${import.meta.env.VITE_APP_MX_APP_SRC}?${params.endsWith('&') ? params.substring(0, params.length - 1) : params}`
          console.log('params', params, getToken(), src)
          const iframeElement = document.createElement("iframe");
          iframe.value = iframeElement;
          nextTick(async () => {
            const myiframe = document.getElementById("myiframeYl");
            iframeElement.src = src
            iframeElement.id = "MXCADYl";
            iframeElement.style.width = "100%";
            iframeElement.style.height = "100%";
            myiframe.append(iframeElement);
            setTimeout(() => {
              oniframeMessage({
                type: "cadPreview",
                content: "Mx_cadPreview",
                formData: {
                  openUrl: res,
                  num: num.value++
                }
              })
            },5000) //延迟 解决左侧打开的时候 不显示

          });
        } else {
          browseUrl.value=openUrl;
          // browseUrl.value = import.meta.env.VITE_PDF_URL + '?file=' + encodeURIComponent(openUrl)
          console.log("🚀 ~ nodeClick ~  browseUrl.value:",  browseUrl.value)
          
        }
        loading.close()
      })
    }

  }
}

const onClonePdf = () => {
  if (cadOrPdf.value.slice(-4) === '.dwg' || cadOrPdf.value.slice(-6) === '.mxweb') {
    const myiframe = document.getElementById("myiframeYl");
    const existingIframe = document.getElementById("MXCADYl");
    if (existingIframe) {
      myiframe.removeChild(existingIframe);
    }
  }
}

// 处理点击事件
const oniframeMessage = (param) => {
  const myiframe = document.querySelector("#myiframeYl").querySelector("iframe");
  if (myiframe && myiframe.contentWindow) {
    myiframe.contentWindow.postMessage(param, "*");
    // 先移除之前的事件监听器，防止重复绑定
    window.removeEventListener('message', handleMessage);
    // 添加新的事件监听器
    window.addEventListener('message', handleMessage);
  }
}
const handleMessage = (event) => {}

const selectedNode = ref(null) // 当前选中的节点

const level = ref(null)

const dialogPdf = ref(false)

const browseUrl = ref('')

const taskId = route.query.id
const stage = route.query.stage
const loading=ref(false)
// 查询树形数据
const dataTreeList = () => {
  console.log("🚀 ~ dataTreeList ~ 触发:", '触发')
  loading.value=true;
  specificationTree({prjTaskInfoId: taskId, stage: stage}).then(res => {
    data.value = res
    loading.value=false;
  })
}
defineExpose({ dataTreeList });
const showMenu = ref(false) // 控制弹框显示
const dialogVisible = ref(false)

const delTree = () => {
  const isFile = level.value === 4 ? '1' : '2'
  proxy.$modal.confirm('确定要' + delTitle.value + '吗?').then(function () {
    return deleteTree({id: selectedNode.value.id, isFile: isFile});
  }).then(() => {
    dataTreeList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {
  });
}

const delTitle = ref('删除所有文件')
const menuPosition = ref({}) // 菜单位置
const renderedHandler = () => {
  console.log("渲染完成");
};
const options = {
    // widthOffset: 10,  //如果渲染出来的结果感觉单元格宽度不够，可以在默认渲染的列表宽度上再加 Npx宽
    // heightOffset: 10, //在默认渲染的列表高度上再加 Npx高
    transformData: (workbookData) => {
      console.log("transformData", workbookData)
      workbookData.forEach((sheet) => {
        let rows = sheet.rows;
        //记录每一列的最大字符数
        const columnsLength = new Array(Object.keys(sheet.cols).length - 1).fill().map(() => { return { width: 0, rowIndex: 0 } });
        const rowsLength = new Array(Object.keys(rows).length - 1).fill().map(() => { return { width: 0, rowIndex: 0 } });
        for (let row in rows) {
          if(!rows[row].cells) continue
          for (let key in rows[row].cells) {
            let cell = rows[row].cells[key];
            if (cell.text) {
              let two;
              if(cell.text.richText){
                 two = countChineseCharacters(cell.text.richText[0].text)
              }else{
                two = countChineseCharacters(cell.text)
              }
              let cellValueLength = (cell.text.toString().length - two) * 6 + two * 1.5 * 6 //中文字符占1.5个宽，英文字符占1个宽;
              if (cellValueLength > columnsLength[parseInt(key)].width) {
                columnsLength[parseInt(key)].width = cellValueLength;
                columnsLength[parseInt(key)].rowIndex = parseInt(row)
              }
              //列宽度小于当前单元格的字符数
              if (sheet.cols[parseInt(key)].width < cellValueLength && cellValueLength > rowsLength[parseInt(row)].width) {
                rowsLength[parseInt(row)].width = cellValueLength;
                rowsLength[parseInt(row)].rowIndex = parseInt(row)
              }
            }
          }
        }
        for (let col in sheet.cols){
          if(!sheet.cols[col].width) continue
          const current = columnsLength[parseInt(col)]
          //列宽度小于当前单元格的字符数
          if (sheet.cols[col].width < current.width) {
            //sheet.cols[col].width = current.width * 12
            for(let r=0; r < rowsLength.length; r++){
              const item = rowsLength[r]
              if (item.width === 0) continue
              const editCol = rows[item.rowIndex.toString()].cells[col]
              if(!editCol || !editCol.text) continue
              let colWidth = sheet.cols[col].width
              if(editCol.merge && editCol.merge.length > 1 && editCol.merge[0] < editCol.merge[1]) {//合并单元格
                colWidth = 0
                for(let m=editCol.merge[0];m<=editCol.merge[1];m++){
                  colWidth += sheet.cols[(parseInt(col) + m).toString()].width
                }
              }
              colWidth -= 1.5 * 6 * 2 //减去边框宽度
              let newText = []
              let tempWidth = 0
              editCol.text=typeof(editCol.text)=='number'?editCol.text.toString():editCol.text
              console.log("🚀 ~ workbookData.forEach ~ editCol.text:", editCol.text)
              if(editCol.text.richText){
                editCol.text = editCol.text.richText[0].text
              }
              for(let c of editCol.text){
                tempWidth += (countChineseCharacters(c) > 0 ? 1.7 * 6 : 6)
                if(colWidth < tempWidth){
                  newText.push('\n')
                  tempWidth = 0
                }
                newText.push(c)
              }
              let rowCount = newText.filter(o=>o === '\n').length//换行个数
              rowCount += 1
              rows[item.rowIndex.toString()].height = rowCount * 24
              editCol.text = newText.join('')
            }
          }
        }
        console.log("columnsLength", columnsLength, rowsLength)
      })
      return workbookData
    }, //将获取到的excel数据进行处理之后且渲染到页面之前，可通过transformData对即将渲染的数据及样式进行修改，此时每个单元格的text值就是即将渲染到页面上的内容
}
const countChineseCharacters = (str)=> {
  // 匹配所有Unicode汉字范围（包括基本汉字和扩展汉字）
  const chineseRegex = /[\u4e00-\u9fa5\u3400-\u4dbf\uf900-\ufaff]/g;
  str = typeof str === 'string' ? str : str.toString()
  const matches = str.match(chineseRegex);
  return matches ? matches.length : 0;
}
const errorHandler = () => {
  console.log("渲染失败");
};
const fileUp = ref(null);
const addForm = ref({
  fileType: '',
  fileName: ''
})
const fileTypeOptions = ref([
  {label: '设计说明书', value: '1'},
  {label: '设计文件总目录', value: '2'},
])
const fileList = ref([])
const fileList1 = ref([])
const clickList=()=>{
  dialogVisible.value = true
  fileList1.value=[]
  getName(specificationName.value,stage).then(res => {
    if(res.code==200){
      fileList.value=res.data
      addForm.value.fileType=res.data[0].name
    }
  })
} 
// 新增树弹框确定按钮
const addTreeBtn = async () => {
  // fileUp.value.handleUpload(); // 触发上传
     const loading = ElLoading.service({
      lock: true,
      text: "上传中请稍后...",
      background: "rgba(255, 255, 255, 0.7)",
    });

  const { value: chunks } = FileFPArr;
  const { fileName, md5 } = upLoadForm.value;
  const parentId = selectedNode.value?.id;
  const totalChunks = chunks.length;

  for (let index = 0; index < totalChunks; index++) {
    const chunk = chunks[index];

    const formData = new FormData();
    formData.append('file', chunk);
    formData.append('fileName', fileName);
    formData.append('total', totalChunks);
    formData.append('number', index + 1);
    formData.append('specificationParentId', parentId);
    formData.append('md5', md5);
    formData.append('taskId', taskId);
    formData.append('newDate', new Date().getTime());

    console.log(`📤 正在上传第 ${index + 1} 片`);

    try {
      const res = await designFilePart(formData);
      console.log(`✅ 第 ${index + 1} 片上传成功`, res);
      
    } catch (error) {
      console.error(`❌ 第 ${index + 1} 片上传失败`, error);
      proxy.$modal.alert(`第 ${index + 1} 片上传失败`);
      break; // 可选：失败后是否停止上传？
    }
  }

  console.log("🎉 所有分片上传完成");
  proxy.$modal.msgSuccess("上传完成");
  loading.close()
  dialogVisible.value = false;
  dataTreeList()
 
}
// 树形侧边栏新增对话框关闭事件
const handleClose = () => {
  // fileUp.value.onClose()
}

onMounted(() => {
  dataTreeList()
});
const scale=ref(1)
const zoomIn = () => {
  scale.value += 0.1
}
const zoomOut = () => {
  scale.value -= 0.1
}
const FileFPArr=ref([])
const upLoadForm=ref({md5:'',fileName:''})
const creatChuank=(file,chunkSize)=>{
    const chunks = []; // 存放分片 Blob
  const buffers = []; // 存放分片 ArrayBuffer 用于计算 MD5

  return new Promise((resolve) => {
    let pendingReads = 0;

    for (let i = 0; i < file.size; i += chunkSize) {
      const chunk = file.slice(i, i + chunkSize);
      chunks.push(chunk);

      const reader = new FileReader();
      pendingReads++;

      reader.onload = function (e) {
        buffers.push(e.target.result);

        if (--pendingReads === 0) {
          // 所有分片读取完成，合并 buffer 并计算 MD5
          const md5 = SparkMD5.ArrayBuffer.hash(concatenateBuffers(buffers));
          resolve({ chunks, md5 }); // 返回 { chunks: [...], md5: "xxxx" }
        }
      };

      reader.readAsArrayBuffer(chunk);
    }
  });
}
// const arrayBufferToBase64 = (buffer) => {
//   let binary = '';
//   const bytes = new Uint8Array(buffer);
//   const len = bytes.byteLength;
//   for (let i = 0; i < len; i++) {
//     binary += String.fromCharCode(bytes[i]);
//   }
//   return window.btoa(binary);
// };
// function concatenateBuffers(bufferList) {
//   let totalLength = bufferList.reduce((acc, buf) => acc + buf.byteLength, 0);
//   const temp = new Uint8Array(totalLength);
//   let offset = 0;

//   for (const buffer of bufferList) {
//     temp.set(new Uint8Array(buffer), offset);
//     offset += buffer.byteLength;
//   }

//   return temp.buffer;
// }
const concatenateBuffers=(bufferList)=>{
    const totalLength = bufferList.reduce((acc, buf) => acc + buf.byteLength, 0);
  const result = new Uint8Array(totalLength);
  let offset = 0;

  for (const buffer of bufferList) {
    result.set(new Uint8Array(buffer), offset);
    offset += buffer.byteLength;
  }

  return result.buffer;
}
const handleUploadFiles=async(file)=>{
const result = await creatChuank(file.file,5 * 1024 * 1024)
  console.log("handleUploadFiles", file)
  FileFPArr.value=result.chunks
  upLoadForm.value.md5=result.md5
  upLoadForm.value.fileName=addForm.value.fileType+'_'+file.file.name
   console.log('✅ 文件 MD5:', result.md5);
}
</script>

<template>
  <div class="">
    <tree-data
    v-loading="loading"
        :treeData="data"
        :defaultProps="defaultProps"
        @handleNodeContextMenu="handleNodeContextMenu"
        @nodeClick="nodeClick"
    ></tree-data>
    <!-- 右键菜单的弹出框 -->
    <el-popover
        v-model:visible="showMenu"
        placement="bottom"
        width="150"
    >
      <div style="font-size: 14px">
        <div class="file-actions">
          <!-- 添加文件按钮 -->
          
          <div class="action-item" @click="clickList" v-if="level === 3">
            <el-icon color="green" class="icon">
              <CirclePlusFilled/>
            </el-icon>
            <span class="label">添加文件</span>
          </div>

          <!-- 删除所有文件按钮 -->
          <div v-if="fileLists?.children?.length>0||delTitle==='删除文件'" class="action-item delete" @click="delTree">
            <el-icon color="red" class="icon">
              <Delete/>
            </el-icon>
            <span class="label">{{ delTitle }}</span>
          </div>
        </div>
        <!--            <div><el-button type="primary" icon="CirclePlus" plain style="width: 100%;" @click="dialogVisible = true">新增</el-button></div>-->
        <!--            <div><el-button type="danger" icon="Delete" plain style="width: 100%;" @click="delTree">{{ delTitle }}</el-button></div>-->
      </div>
      <template #reference>
        <!-- 参考元素，动态放置在鼠标点击位置 -->
        <div
            ref="popoverRef"
            class="popover-reference"
            :style="menuPosition"
        ></div>
      </template>
    </el-popover>

    <!-- 新增-->
    <el-dialog
        :show-close="false"
        v-model="dialogVisible"
        title="添加文件"
        width="500"
        @close="handleClose"
    >
      <el-descriptions border :column="1" label-width="100px">
        <el-descriptions-item label="文件类型">
          <el-select v-model="addForm.fileType" placeholder="请选择文件类型" style="width: 200px">
            <el-option
                v-for="item in fileList"
                :key="item.name"
                :label="item.name"
                :value="item.name"
            />
          </el-select>

        </el-descriptions-item>
        <el-descriptions-item label="文件名称">
          <div style="width: 350px">
            <!-- <FileUpload @dataTreeList="dataTreeList"
                        :formData="{ prjTaskInfoId: taskId, specificationParentId: selectedNode.id,name:addForm.fileType }" fileSize="500" ref="fileUp"
                        uploadFileUrl="template/saveFile" :isShowTip="false" :fileType="[]" ></FileUpload> -->
                     <el-upload
                         v-model:file-list="fileList1"
                         class="upload-demo"
                         action=""
                         :http-request="handleUploadFiles"
                     >
                       <el-button type="primary">选取文件</el-button>
                     </el-upload>
          </div>
        </el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="addTreeBtn">确定</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 在线预览 -->
    <el-dialog
        v-model="dialogPdf"
        fullscreen
        top="40vh"
        width="70%"
        draggable
        @close="onClonePdf"
        :destroy-on-close="true"
    >
      <div style="height: 94vh">
        <div style="height: 94vh; overflow: hidden;" v-if="cadOrPdf.slice(-4) === '.dwg' || cadOrPdf.slice(-6) === '.mxweb'">
          <div id="myiframeYl" style="height: 94vh;"></div>
        </div>
        <div v-else-if="cadOrPdf.slice(-4) === '.xls'||cadOrPdf.slice(-5) === '.xlsx'" :style="{ height:'90vh' }">
          <div style="display: flex;margin-bottom: 10px;">
           <el-button @click="zoomIn">放大</el-button>
           <el-button @click="zoomOut">缩小</el-button>
          </div>
          <vue-office-excel
          :style="{ transform: `scale(${scale})`, transformOrigin: 'top left',height:'90vh' }"
          :src="browseUrl"
          :options="options"
          @rendered="renderedHandler"
          @error="errorHandler"
        />
        </div>
       
        <div v-else-if="cadOrPdf.slice(-4) === '.doc'||cadOrPdf.slice(-5) === '.docx'" :style="{ height:'90vh' }">
          <!-- <div style="display: flex;margin-bottom: 10px;">
           <el-button @click="zoomIn">放大</el-button>
           <el-button @click="zoomOut">缩小</el-button>
          </div> -->
          <vue-office-docx
          :style="{ height:'95vh' }"
          :src="browseUrl"
          @rendered="renderedHandler"
          @error="errorHandler"
        />
        </div>
       
      </div>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.file-actions {
  display: flex;
  flex-direction: column;
}

.action-item {
  display: flex;
  align-items: center;
  padding: 8px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.action-item:hover {
  background-color: #f5f7fa;
  border-radius: 4px;
}

.icon {
  margin-right: 8px;
}
</style>
