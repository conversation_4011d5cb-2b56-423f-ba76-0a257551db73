//绘制电缆中间接头
import {
  getEquipmentState,
  saveEquipmentInfos,
} from "@/api/desginManage/draw.js";
import { ElMessage } from "element-plus";
import { getEquipmentModel1 } from "@/views/pages/online/saveModelInfo.js";

// 全局变量
let inListFlag = '';
export const cableHead = async (arr, cableForm, zjtList, index) => {
  const taskId = new URLSearchParams(new URL(window.location.href).search).get(
    "id"
  );
  // 参数校验
  if (!taskId) {
    ElMessage.error("任务ID获取失败");
    return;
  }
  // 初始化数据容器
  const paramsSaveAllInfos = new Map();
  try {
    // 中间头拓扑关系初始化
    const towerTopologyrelations = {
      AssociatedIn: [],
      AssociatedOut: [],
      AssociatedParent: [],
      AssociatedChild: [],
      AssociatedLabel: [],
      AssociatedLabelOwner: [],
      AssociatedFile: [],
    };
    //电缆线拓扑关系
    const lineTopologyrelations = {
      AssociatedIn: [], // 进线拓扑
      AssociatedOut: [], // 出线拓扑
      AssociatedParent: [], //
      AssociatedChild: [], //
      AssociatedLabel: [], // 目前不涉及
      AssociatedLabelOwner: [], // 目前不涉及
      AssociatedFile: [], // 目前不涉及
    }

    // 获取关联运行电缆头
    const tempTowers = arr.filter(o => o.CLASS_NAME === 'PWCableJointPSR');
    // 获取线路状态
    // 电缆线处理逻辑封装
    const processLine = async (line, legendState) => {
      try {
        const coordinates = line.SHAPE.match(/(\d+\.\d+)\s+(\d+\.\d+)/g);
        if (!coordinates || coordinates.length < 2) {
          console.warn("导线坐标格式异常", line.DEV_ID);
          return;
        }
        
        // 构建导线数据
        const lineData = getEquipmentModel1("Line", coordinates, {
          legendGuidKey: line.DEV_ID,
          engineeringId: taskId,
          legendState,
          addFlag:line.addFlag?line.addFlag:'null',
        })
        lineData.privatepropertys = {
          Span: (line.LENGTH).toFixed(2),
          TDDJ: (line.LENGTH).toFixed(2),
        }
        lineData.topologyrelations = { ...lineTopologyrelations }
        paramsSaveAllInfos.set(line.DEV_ID, lineData);
      } catch (err) {
        console.error("导线处理异常", line.DEV_ID, err);
      }
    };
    const Voltagerating=zjtList[index].voltage?zjtList[index].voltage:''
    // 处理进出电缆线
    for (const tempTower of tempTowers) {
      const inLine = arr.find(o => o.END_DEV_ID === tempTower.DEV_ID  &&o.CLASS_NAME === "PWCableSecPSR");
      const outLine = arr.find(o => o.START_DEV_ID === tempTower.DEV_ID  &&o.CLASS_NAME === "PWCableSecPSR");
      const legendState = await updateLineLegendState(tempTower.DEV_ID, taskId);
      towerTopologyrelations.AssociatedParent=[];
      if (inLine) {
        lineTopologyrelations.AssociatedOut = [];
        lineTopologyrelations.AssociatedIn = [];
        towerTopologyrelations.AssociatedIn=[];
        lineTopologyrelations.AssociatedChild=[];
        lineTopologyrelations.AssociatedOut.push(inLine.END_DEV_ID);
        lineTopologyrelations.AssociatedChild.push(inLine.END_DEV_ID)
        towerTopologyrelations.AssociatedIn.push(inLine.DEV_ID)
        towerTopologyrelations.AssociatedParent.push(inLine.DEV_ID)
        if(inLine.END_DEV_ID&&inLine.START_DEV_ID){
      const flag=  findValue(tempTowers,inLine.END_DEV_ID,inLine.START_DEV_ID)
      if(flag){
        lineTopologyrelations.AssociatedIn.push(inLine.START_DEV_ID) 
        lineTopologyrelations.AssociatedChild.push(inLine.START_DEV_ID)
      }  
        }
    const legendState = await updateLineLegendState(inLine.DEV_ID, taskId);
        await processLine(inLine,legendState,inLine.LENGTH);
      }
      if (outLine) {
        lineTopologyrelations.AssociatedOut = [];
        lineTopologyrelations.AssociatedIn = [];
        towerTopologyrelations.AssociatedOut=[];
        lineTopologyrelations.AssociatedChild=[];
        towerTopologyrelations.AssociatedOut.push(outLine.DEV_ID)
        lineTopologyrelations.AssociatedChild.push(outLine.START_DEV_ID)
        lineTopologyrelations.AssociatedIn.push(outLine.START_DEV_ID);
        towerTopologyrelations.AssociatedParent.push(outLine.DEV_ID)
        const legendState = await updateLineLegendState(outLine.DEV_ID, taskId);
        await processLine(outLine,legendState,outLine.LENGTH);
      }
      // 构建中间头数据
      const towerData = getEquipmentModel1("Point", `${tempTower.X} ${tempTower.Y}`, {
        moduleId: cableForm.cable1, // 待填充
        legendTypeKey: zjtList[index].legendTypeKey,
        legendState,
        legendGuidKey: tempTower.DEV_ID,
        engineeringId: taskId,
        addFlag:tempTower.addFlag?tempTower.addFlag:'null'
      })
      towerData.privatepropertys = {
        Voltagerating,// 电压等级
      }
      towerData.topologyrelations = { ...towerTopologyrelations }
      paramsSaveAllInfos.set(tempTower.DEV_ID, towerData);
    }

    // 并行处理所有型号推导
    const finalData = [];
    paramsSaveAllInfos.forEach((value, key) => {
      finalData.push(value);
    });
    // 保存操作
    const res = await saveEquipmentInfos(finalData);
    if (res.code === 200) {
      ElMessage.success("保存成功");
    } else {
      ElMessage.error(res.msg);
    }
  } catch (err) {
    console.error("主流程异常", err);
    ElMessage.error(`操作失败: ${err.message}`);
  }
}
function findValue(arr, head, tall) {
  let found
  let found1
  if (head) {
      found = arr.find(item => item.DEV_ID === head)
  }
  if (tall) {
    found1 = arr.find(item => item.DEV_ID === tall)
  }
  if (found&&found1) return true
  if (!found||!found1) return false
}
// 优化后的状态获取函数
async function updateLineLegendState(id, taskId) {
  if (inListFlag === id) return "New";

  try {
    const res = await getEquipmentState({ equipmentId: id, taskId });
    inListFlag = id;
    return res.data?.trim() || "New";
  } catch (err) {
    console.error("状态获取失败", id, err);
    return "ErrorState";
  }
}