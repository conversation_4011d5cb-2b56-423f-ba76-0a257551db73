import { MxCpp, MxCADUiPrPoint, McDbPolyline, MxCADUiPrDist, McGeVector3d, McGePoint3d, McDbArc, MxCADUtility, McDbLine, McDb} from "mxcad";

// 画多段线
async function Mx_drawPolyline() {
    // 获取画布实例
    const mxcad = MxCpp.App.getCurrentMxCAD();
    const getPoint = new MxCADUiPrPoint();
    getPoint.setMessage("\n指定起点:");
    let firstPoint = await getPoint.go();
    if (!firstPoint) return;
    let pointArr: any[] = [];
    let drawObjIds: any[] = [];
    let isArc: boolean = false;
    let dStartWidth: number = 0;
    let dEndWidth: number = 0;
    pointArr.push({ pt: firstPoint, dBulge: 0, dStartWidth, dEndWidth })
    let isAutoClose: boolean = false;
    let isReverse: boolean = false;//圆弧方向是否取反
    let arcDirection: any = null //指定圆弧切向
    while (true) {
        if (!isArc) {
            //绘直线
            const getNextPoint = new MxCADUiPrPoint;
            getNextPoint.setMessage("\n指定下一个点");
            // 提示消息
            if (pointArr.length < 2) {
                getNextPoint.setKeyWords('[圆弧(A)/宽度(W)]')
            } else {
                getNextPoint.setKeyWords('[放弃(U)/圆弧(A)/宽度(W)/闭合(C)]')
            }
            let lastPoint = pointArr[pointArr.length - 1];//鼠标点击的最后一个点
            // 等待鼠标点击期间会执行setUserDraw动态绘制函数中的回调函数
            getNextPoint.setUserDraw((pt, pw) => {
                // pt 就是鼠标所在图纸位置的坐标
                // pw 是提供了一些动态绘制方法的对象
                let pl = new McDbPolyline();
                pl.addVertexAt(lastPoint.pt, lastPoint.dBulge, lastPoint.dStartWidth, lastPoint.dEndWidth)
                pl.addVertexAt(pt, 0, dStartWidth, dEndWidth);
                pw.drawMcDbEntity(pl);
            })
            let nextPoint = await getNextPoint.go();
            if (nextPoint !== null) {
                // 获取到了鼠标点击点
                pointArr.push({ pt: nextPoint, dBulge: 0, dStartWidth, dEndWidth });
                let pl = new McDbPolyline();
                pl.addVertexAt(lastPoint.pt, lastPoint.dBulge, lastPoint.dStartWidth, lastPoint.dEndWidth);
                pl.addVertexAt(nextPoint, 0, dStartWidth, dEndWidth);
                drawObjIds.push(mxcad.drawEntity(pl))
            } else if (getNextPoint.getStatus() === Mx.MrxDbgUiPrBaseReturn.kKeyWord) {
                // 用户输入关键字
                let keyWord = getNextPoint.keyWordPicked()
                if (keyWord === 'A') {
                    // 绘制圆弧
                    isArc = true;
                } else if (keyWord === 'W') {
                    // 设置宽度
                    /**
                     * 输入空格宽度不变
                     */
                    let getWidth = new MxCADUiPrDist();
                    getWidth.setMessage('指定起点宽度:');
                    // 获取线宽
                    let dWVal = await getWidth.go();
                    if (dWVal !== null && getWidth.getStatus() !== Mx.MrxDbgUiPrBaseReturn.kNone) {
                        dStartWidth = getWidth.value();
                        pointArr[pointArr.length - 1].dStartWidth = dStartWidth;
                    }
                    getWidth.setMessage('指定端点宽度');
                    dWVal = await getWidth.go();
                    if (dWVal !== null && getWidth.getStatus() !== Mx.MrxDbgUiPrBaseReturn.kNone) {
                        dEndWidth = getWidth.value();
                        pointArr[pointArr.length - 1].dEndWidth = dEndWidth;
                    }
                } else if (keyWord === 'U') {
                    // 回退操作
                    if (pointArr.length > 1 && drawObjIds.length > 1) {
                        pointArr.pop();
                        drawObjIds[drawObjIds.length - 1].erase();
                        drawObjIds.pop();
                    }
                } else if (keyWord === 'C') {
                    isAutoClose = true;
                    break;
                }
            } else {
                break;
            }
        } else {
            // 绘制圆弧
            let getNextPoint = new MxCADUiPrPoint();
            getNextPoint.setMessage('指定圆弧的端点(按住 ctrl 键取相反方向)\n');
            getNextPoint.setKeyWords('[第二个点(S)/角度(A)/圆心(CE)/放弃(U)/直线(L)/宽度(W)/闭合(CL)]');
            // 计算圆弧的切向量.
            let vecArcTangent = new McGeVector3d();
            // 开端的圆弧切向量取水平方向./获取圆弧切向
            if (pointArr.length < 2) {
                // 开端的圆弧切向量取水平方向.
                vecArcTangent.copy(McGeVector3d.kYAxis);
            } else {
                let length = pointArr.length;
                let p1 = pointArr[length - 2].pt;
                let dBulge = pointArr[length - 2].dBulge;
                let p2 = pointArr[length - 1].pt;
                if (dBulge === 0) {
                    // 前一段是个直线.
                    vecArcTangent = new McGePoint3d(p2.x, p2.y, 0.0).sub(new McGePoint3d(p1.x, p1.y, 0.0));
                }
                else {
                    // 前一段为曲线，求圆弧的切向量
                    let tmpPl = new McDbPolyline;
                    tmpPl.addVertexAt(p1, dBulge);
                    tmpPl.addVertexAt(p2);
                    let tmpVec = tmpPl.getFirstDeriv(new McGePoint3d(p2.x, p2.y, 0.0));//曲线上任意一点的切向量
                    if (tmpVec.ret) {
                        vecArcTangent = tmpVec.val;
                    }
                    else {
                        vecArcTangent.copy(McGeVector3d.kXAxis);
                    }
                }
            }
            let lastPoint = pointArr[pointArr.length - 1];
            //监听ctrl键是否被按下
            document.addEventListener('keydown', function (event) {
                if (event.ctrlKey && event.keyCode === 17) {
                    isReverse = true;
                }
            });
            // ctrl键松开
            document.addEventListener('keyup', function () {
                isReverse = false;
            });
            getNextPoint.setUserDraw((pt, pw) => {
                let pl = new McDbPolyline();
                let d = CMxDrawPolylineDragArcDraw_CalcArcBulge(lastPoint.pt, pt, arcDirection ? arcDirection : vecArcTangent);
                if (isReverse) d = -d;
                pl.addVertexAt(lastPoint.pt, d, lastPoint.dStartWidth, lastPoint.dEndWidth);
                pl.addVertexAt(pt, 0, dStartWidth, dEndWidth);
                pw.drawMcDbEntity(pl);
            })
            let nextPoint = await getNextPoint.go();
            if (nextPoint != null) {
                let dBulge = CMxDrawPolylineDragArcDraw_CalcArcBulge(lastPoint.pt, nextPoint, arcDirection ? arcDirection : vecArcTangent);
                if (isReverse) dBulge = -dBulge;
                lastPoint.dBulge = dBulge;
                pointArr.push({ pt: nextPoint, dBulge: 0, dStartWidth, dEndWidth });
                let pl = new McDbPolyline();
                pl.addVertexAt(lastPoint.pt, dBulge, lastPoint.dStartWidth, lastPoint.dEndWidth);
                pl.addVertexAt(nextPoint, 0, dStartWidth, dEndWidth);
                drawObjIds.push(mxcad.drawEntity(pl));
            } else if (getNextPoint.getStatus() === Mx.MrxDbgUiPrBaseReturn.kKeyWord) {
                // 获取用户输入关键字
                let keyWord = getNextPoint.keyWordPicked();
                if (keyWord === 'L') {
                    // 变直线
                    isArc = false;
                } else if (keyWord === 'U') {
                    // 回退
                    if (pointArr.length > 1 && drawObjIds.length > 1) {
                        pointArr.pop();
                        drawObjIds[drawObjIds.length - 1].erase();
                        drawObjIds.pop();
                    }
                } else if (keyWord === 'S') {
                    // 用户指定圆弧的第二个点
                    let getPoint2 = new MxCADUiPrPoint();//用户输入的指定点
                    getPoint2.setMessage("指定圆弧的第二个点");
                    let pt2: any = await getPoint2.go();
                    if (pt2 === null) return
                    let getPoint3 = new MxCADUiPrPoint();
                    getPoint3.setMessage("指定圆弧的端点");
                    let lastPoint = pointArr[pointArr.length - 1]
                    getPoint3.setUserDraw((pt, pw) => {
                        let arc = new McDbArc();
                        arc.computeArc(lastPoint.pt.x, lastPoint.pt.y, pt2.x, pt2.y, pt.x, pt.y);//三点绘制圆弧
                        pw.drawMcDbEntity(arc);
                    });
                    let pt3 = await getPoint3.go();
                    if (!pt3) break;
                    let retB = MxCADUtility.calcBulge(lastPoint.pt, pt2, pt3);//开始点,中点，结束点 三点计算当前弧线的凸度
                    if (!retB.ret) break;
                    pointArr[pointArr.length - 1] = retB.val;
                    pointArr.push({ pt: pt3, dBulge: 0, dStartWidth, dEndWidth });//最后一个点
                    let pl = new McDbPolyline();
                    pl.addVertexAt(lastPoint.pt, retB.val, dStartWidth, dEndWidth);
                    pl.addVertexAt(pt3);
                    drawObjIds.push(mxcad.drawEntity(pl));
                } else if (keyWord === 'W') {
                    // 设置宽度
                    let getWidth = new MxCADUiPrDist();
                    getWidth.setMessage('指定起点宽度:');
                    // 获取线宽
                    let dWVal = await getWidth.go();
                    if (dWVal !== null && getWidth.getStatus() !== Mx.MrxDbgUiPrBaseReturn.kNone) {
                        dStartWidth = getWidth.value();
                        pointArr[pointArr.length - 1].dStartWidth = dStartWidth;
                    }
                    getWidth.setMessage('指定端点宽度');
                    dWVal = await getWidth.go();
                    if (dWVal !== null && getWidth.getStatus() !== Mx.MrxDbgUiPrBaseReturn.kNone) {
                        dEndWidth = getWidth.value();
                        pointArr[pointArr.length - 1].dEndWidth = dEndWidth;
                    }
                } else if (keyWord === 'D') {
                    // 指定圆弧切向
                    let getDirection = new MxCADUiPrPoint();
                    getDirection.setMessage('指定圆弧起点的切向:');
                    let dirVal = await getDirection.go();
                    if (dirVal === null) break;
                    arcDirection = new McGeVector3d(dirVal.x, dirVal.y, dirVal.z)
                } else if (keyWord === 'CE') {
                    // 确定圆心
                    let getCenter = new MxCADUiPrPoint();
                    getCenter.setMessage('指定圆弧的圆心:');
                    let arcCenter: any = await getCenter.go();
                    if (!arcCenter) return;
                    // 起始角度：起点与圆心的连线与X轴正方向的夹角
                    // 结束角度：结束点与圆心的连线与X轴正方向的夹角
                    let getNextPoint = new MxCADUiPrPoint();
                    let lastPoint = pointArr[pointArr.length - 1];
                    let r: number = 0;//圆弧凸度
                    let arc = new McDbArc();//圆弧
                    // 开端的圆弧切向量取水平方向./获取圆弧切向
                    getNextPoint.setUserDraw(async (pt, pw) => {
                        // 起始角度
                        arc.startAngle = lastPoint.pt.sub(arcCenter).angleTo1(new McGeVector3d(arcCenter.x, 0, 0))//始终不变
                        arc.endAngle = pt.sub(arcCenter).angleTo1(new McGeVector3d(arcCenter.x, 0, 0))
                        arc.center = arcCenter;
                        arc.radius = arcCenter.distanceTo(lastPoint.pt);
                        if (pt.y < arcCenter.y) arc.endAngle = -arc.endAngle;
                        // // 获取圆弧上的第三个点
                        let lineX = new McDbPolyline();
                        lineX.addVertexAt(new McGePoint3d(arcCenter.x, 0, 0));
                        lineX.addVertexAt(new McGePoint3d(0, 0, 0));
                        let pointsX = arc.IntersectWith(lineX, 3);//X轴交点
                        // 获取凸度
                        let rLine = new McDbPolyline();
                        rLine.addVertexAt(arcCenter);
                        rLine.addVertexAt(pt);
                        let currentPoint = arc.IntersectWith(rLine, 2);//当前线段与圆弧的交点
                        let cp: any = null;
                        if (currentPoint.length() === 1) {
                            cp = currentPoint.at(0);
                        } else {
                            if (pt.distanceTo(currentPoint.at(0)) <= pt.distanceTo(currentPoint.at(1))) {
                                cp = currentPoint.at(0);
                            } else {
                                cp = currentPoint.at(1);
                            }
                        }
                        let retB = MxCADUtility.calcBulge(lastPoint.pt, pointsX.at(0), cp);
                        if (retB.ret) {
                            r = retB.val;
                            let pl = new McDbPolyline()
                            pl.addVertexAt(lastPoint.pt, r);
                            pl.addVertexAt(cp);
                            pw.drawMcDbEntity(pl)
                        }
                    })
                    let nextPoint = await getNextPoint.go();
                    if (!nextPoint) break;
                    // 获取凸度
                    let rLine = new McDbPolyline();
                    rLine.addVertexAt(arcCenter);
                    rLine.addVertexAt(nextPoint);
                    let currentPoint = arc.IntersectWith(rLine, 2);
                    let cp: any = null;
                    if (currentPoint.length() === 1) {
                        cp = currentPoint.at(0);
                    } else {
                        if (nextPoint.distanceTo(currentPoint.at(0)) <= nextPoint.distanceTo(currentPoint.at(1))) {
                            cp = currentPoint.at(0);
                        } else {
                            cp = currentPoint.at(1);
                        }
                    }
                    let pl = new McDbPolyline()
                    pl.addVertexAt(lastPoint.pt, r, dStartWidth, dEndWidth);
                    pl.addVertexAt(cp);
                    pointArr[pointArr.length - 1].dBulge = r;
                    pointArr.push({ pt: cp, dBulge: 0, dStartWidth, dEndWidth })
                    drawObjIds.push(mxcad.drawEntity(pl));

                } else if (keyWord === 'CL') {
                    // 闭合
                    isAutoClose = true;
                    break;
                }
            } else {
                break;
            }

        }
    }
    // 删除监听事件
    document.removeEventListener('keydown', () => isReverse = false)
    document.removeEventListener('keyup', () => isReverse = false)
    // 最终绘制
    /**
     * 先删除所有临时对象再重新绘制多边形
     */
    for (let i = 0; i < drawObjIds.length; i++) {
        drawObjIds[i].erase();
    }
    if (pointArr.length > 1) {
        let pl = new McDbPolyline();
        pointArr.forEach(item => {
            pl.addVertexAt(item.pt, item.dBulge, item.dStartWidth, item.dEndWidth)
        })
        pl.isClosed = isAutoClose;
        mxcad.drawEntity(pl);
    }
}

// 计算圆弧凸度
function CMxDrawPolylineDragArcDraw_CalcArcBulge(firstPoint, nextPoint, vecArcTangent): number {
    if (firstPoint.isEqualTo(nextPoint))
        return 0.0;
    let midPt = firstPoint.c().addvec(nextPoint.c().sub(firstPoint).mult(0.5));

    let vecMid = nextPoint.c().sub(firstPoint);
    vecMid.rotateBy(Math.PI / 2.0, McGeVector3d.kZAxis);

    let tmpMidLine = new McDbLine(midPt, midPt.c().addvec(vecMid));

    let vecVertical = vecArcTangent.c();
    vecVertical.rotateBy(Math.PI / 2.0, McGeVector3d.kZAxis);

    let tmpVerticalLine = new McDbLine(firstPoint, firstPoint.c().addvec(vecVertical));

    let aryPoint = tmpMidLine.IntersectWith(tmpVerticalLine, McDb.Intersect.kExtendBoth);
    if (aryPoint.isEmpty())
        return 0.0;

    let arcCenPoint = aryPoint.at(0);

    let dR = arcCenPoint.distanceTo(firstPoint);

    vecMid.normalize();
    vecMid.mult(dR);

    let arcMidPt1 = arcCenPoint.c().addvec(vecMid);
    let arcMidPt2 = arcCenPoint.c().subvec(vecMid);
    let vecArcDir1 = arcMidPt1.c().sub(firstPoint);
    let vecArcDir2 = arcMidPt2.c().sub(firstPoint);
    let arcMidPt = arcMidPt1;
    if (vecArcDir1.angleTo1(vecArcTangent) > vecArcDir2.angleTo1(vecArcTangent)) {
        arcMidPt = arcMidPt2;
    }
    return MxCADUtility.calcBulge(firstPoint, arcMidPt, nextPoint).val;
}

// 调用画多段线的方法
Mx_drawPolyline();