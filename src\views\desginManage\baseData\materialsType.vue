<template>
  <div class="app-container">
    <div class="form-left">
      <div style="color: dodgerblue; font-weight: 600; margin-bottom: 11px">物料类型</div>
      <table-tree @nodeClick="nodeClick" :treeData="materialTypeOptions" :defaultProps="{ label: 'materialsTypeName', children: 'children' }"></table-tree>      
    </div>
    <div class="form-right">
      <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="68px">
        <el-form-item label="资源库" prop="baseVersionId">
          <el-select v-model="queryParams.baseVersionId" @change="versionChange" placeholder="请选择" style="width: 180px;">
            <el-option v-for="item in versionList" :key="item.objId" :label="item.versionName" :value="item.objId"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="属性名称" prop="materialsName">
          <el-input
          v-model="queryParams.propertyName"
          placeholder="请输入属性名称"
          clearable
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <el-row :gutter="20">
        <el-col :span="11">
            <el-table
                ref="staffTable"
                v-loading="listLoading"
                row-key="id"
                :data="staffList"
                border
                fit
                highlight-current-row
                @selection-change="handleStaffChange"
            >
                <el-table-column type="selection" :reserve-selection="true" width="55"></el-table-column>
                <el-table-column label="备选属性" prop="propertyName" align="center" />
            </el-table>
        </el-col>
        <el-col :span="2" style="align-items:center;display: flex;flex-direction:column;justify-content: center;">
            <el-button
                @click="addStaff"
                type="primary"
                :disabled="!staffData.length"
                icon="ArrowRight"
                circle
            ></el-button>
            <el-button
                @click="removeStaff"
                type="primary"
                :disabled="!selectedStaffData.length"
                icon="ArrowLeft"
                circle
                style="margin-left: 0;margin-top: 10px;"
            ></el-button>
        </el-col>
        <el-col :span="11">
            <el-table
                ref="selectedStaffTable"
                v-loading="listLoading"
                :data="selectedStaffList"
                row-key="id"
                border
                fit
                highlight-current-row
                @selection-change="handleSelectedStaffChange"
              >
                <el-table-column type="selection" :reserve-selection="true" width="55"></el-table-column>
                <el-table-column label="已选属性" prop="propertyName" align="center" />
    
                <el-table-column label="显示" align="center">
                  <template slot-scope="{row}">
                    <el-checkbox label="" value="Value 1" />
                  </template>
                </el-table-column>
            </el-table>
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<script setup name="materialsType">
  import { getBaseMaterialsTypeTreeList } from "@/api/baseDate/BaseMaterialsType";
  import { listVersion } from "@/api/baseDate/baseVersion";
  import { ref } from "vue";
  import useUserStore from '@/store/modules/user'

  const userStore = useUserStore()
  const { proxy } = getCurrentInstance();
  const versionList = ref([])
  const listLoading = ref(false)
  const tableKey = ref(0)
  const staffList = ref([{id:1,propertyName:"设备属性1"},{id:2,propertyName:"设备属性2"}])
  const staffData = ref([])
  const selectedStaffList = ref([])
  const selectedStaffData = ref([])

  const materialTypeOptions = ref([])

  const data = reactive({
    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 100000,
      propertyName: null,
      baseVersionId: null
    },
    rules: {
    }
  });

  const { queryParams, form, rules } = toRefs(data);

  function handleSelectedStaffChange(rows) {
    selectedStaffData.value = [];
    if (rows) {
      rows.forEach(row => {
        if (row) {
          selectedStaffData.value.push(row);
        }
      });
    }
  }

  function handleStaffChange(rows) {
    staffData.value = [];
      if (rows) {
        rows.forEach(row => {
          if (row) {
            staffData.value.push(row);
          }
        });
      }
  }

  function addStaff() {
    setTimeout(() => {
      proxy.$refs["staffTable"].clearSelection();
      proxy.$refs["selectedStaffTable"].clearSelection();
    }, 0);
    let repeat = false;
    selectedStaffList.value.forEach(item => {
      if (staffData.value[0] && item.propertyName === staffData.value[0].propertyName) {
        repeat = true;
        return;
      }
    });
    if (repeat === false) {
      staffData.value.forEach(item => {
        selectedStaffList.value.push(item);
      });
      for (let i = 0; i < staffList.value.length; i++) {
        for (let j = 0; j < staffData.value.length; j++) {
          if (staffList.value[i] && staffData.value[j] && staffList.value[i].propertyName === staffData.value[j].propertyName) {
            staffList.value.splice(i, 1);
          }
        }
      }
    }
  }

  function removeStaff() {
    setTimeout(() => {
      proxy.$refs["staffTable"].clearSelection();
      proxy.$refs["selectedStaffTable"].clearSelection();
    }, 0);
    selectedStaffData.value.forEach(item => {
      staffList.value.push(item);
    });
    for (let i = 0; i < selectedStaffList.value.length; i++) {
      for (let j = 0; j < selectedStaffData.value.length; j++) {
        if (selectedStaffList.value[i] && selectedStaffData.value[j] && selectedStaffList.value[i].propertyName === selectedStaffData.value[j].propertyName) {
          selectedStaffList.value.splice(i, 1);
        }
      }
    }
  }

  /**查询按钮事件 */
  function handleQuery() {

  }

  /**重置查询条件 */
  function resetQuery() {

  }

  /**物料类型树查询 */
  function nodeClick() {

  }

  /**更换版本方法 */
  function versionChange(row) {
    getBaseMaterialsTypeTree(row)
  }

  /**获取版本下拉数据 */
  function getlistVersion() {
    userStore.getInfo().then(res => {
        const queryItem = {
          pageNum: 1,
          pageSize: 10000,
          companyId: undefined
        }
        queryItem.companyId = res.dept.deptId 
        listVersion(queryItem).then(response => {
          versionList.value = response.rows;
          if (versionList.value.length > 0) {
            queryParams.value.baseVersionId = versionList.value[0].objId
            getBaseMaterialsTypeTree(versionList.value[0].objId)
          }
        })
    })
  }

  /**物料类型树形图 */
  function getBaseMaterialsTypeTree(versionId){
    const queryParams = {
      baseVersionId: versionId
    }
    getBaseMaterialsTypeTreeList(queryParams).then(res => {
      materialTypeOptions.value = res.data
    })
  }

  getlistVersion()
</script>
<style lang='scss' scoped>
  /* 选中某行时的背景色*/
  .el-table__body tr.current-row > td {
    background-color: #a0eddf !important;
  }
  
  /*鼠标移入某行时的背景色*/
  .el-table--enable-row-hover .el-table__body tr:hover > td {
    background-color: #a0eddf;
  }

  .app-container {
    display: flex;
    height: calc(100vh - 120px);
    .form-left {
      width: 20%;
      height: 100%;
      margin-right: 10px;
    }
    .form-right {
      width: 80%;
      height: 100%;
    }
    .module-btn {
      background-color: #f5f5f5;
      width: 100%;
      display: flex;
      justify-content: flex-start;
      font-size: 13px;
      div {
        border: 1px solid #ccc;
        padding: 4px 10px 2px 10px;
        color: #73797e;
        border-radius: 5px 5px 0 0;
        margin-right: 5px;
        cursor: pointer;
      }
      .active {
        font-weight: bold;
        color: #000;
        background: #fff;
      }
    }
    .sub-bottom {
      background-color: #f5f5f5;
      width: 100%;
    }
    // 图纸
    .sub-photo{
      display: flex;
      justify-content: space-between;
      width: 100%;
      height: 100%;
      .photo-left{
        width: 48%;
        height: 100%;
      }
      .photo-right{
        width: 48%;
        height: 100%;
        border:2px solid #ccc;
      }
    }
  }
</style>