import{ap as J,Q as q,Z as K,aq as st,_ as at,f as G,s as x,e as rt,X as _t,V as k,U as P,W as mt}from"./index-CzBriCFR.js";import{M as Q}from"./index-itnQ6avM.js";import{d as R,h as Z,a3 as X,a4 as l,u as r,B as C,_ as B,a0 as c,H as pt,W as gt,m as t,Q as w,V as S,$ as tt,a5 as ut,n as ct,F as et,w as ft}from"./vue-Cj9QYd7Z.js";import{c as M,d as dt,K as nt,z as T,B as b,V as At,b as vt,g as $}from"./vuetify-BqCp6y38.js";import{h as St,e as z,M as wt,c as ht,O as Dt}from"./mxcad-DrgW2waE.js";import{M as It}from"./mxdraw-BQ5HhRCY.js";import"./handsontable-Ch5RdAT_.js";import"./mapbox-gl-DQAr7S0z.js";const bt=""+new URL("ACAD_ISO02W100-CgczrAa0.png",import.meta.url).href,Ct=""+new URL("ACAD_ISO03W100-AzcC3_-f.png",import.meta.url).href,Rt=""+new URL("ACAD_ISO04W100-IbgtVBfA.png",import.meta.url).href,Lt=""+new URL("ACAD_ISO05W100-CEGXYqwd.png",import.meta.url).href,Ut=""+new URL("ACAD_ISO06W100-DJBzAH1k.png",import.meta.url).href,Ot=""+new URL("ACAD_ISO07W100-D_Mm-GmR.png",import.meta.url).href,Nt=""+new URL("ACAD_ISO08W100-Cq7PdjEU.png",import.meta.url).href,Wt=""+new URL("ACAD_ISO09W100-HuEA3bgs.png",import.meta.url).href,Vt=""+new URL("ACAD_ISO10W100-CpfOB3wW.png",import.meta.url).href,xt=""+new URL("ACAD_ISO11W100-C-YvJa_U.png",import.meta.url).href,Bt=""+new URL("ACAD_ISO12W100-BJObCzYl.png",import.meta.url).href,Et=""+new URL("ACAD_ISO13W100-CnVTTEXT.png",import.meta.url).href,yt=""+new URL("ACAD_ISO14W100-Dt_4eew3.png",import.meta.url).href,kt=""+new URL("ACAD_ISO15W100-DQCL7ZHX.png",import.meta.url).href,Pt=""+new URL("ANGLE-C0JXMnJI.png",import.meta.url).href,Mt=""+new URL("ANSI31-DNoWrKeq.png",import.meta.url).href,Tt=""+new URL("ANSI32-C1D8NgmN.png",import.meta.url).href,Ht=""+new URL("ANSI33-ChicYIJo.png",import.meta.url).href,Ft=""+new URL("ANSI34-VGH_akWK.png",import.meta.url).href,jt=""+new URL("ANSI35-Di7w2V2Y.png",import.meta.url).href,Yt=""+new URL("ANSI36-XpcWXjaS.png",import.meta.url).href,Gt=""+new URL("ANSI37-D7GYIXmO.png",import.meta.url).href,Xt=""+new URL("ANSI38-BFp_zzVO.png",import.meta.url).href,$t=""+new URL("AR-B816-DX1HtyXm.png",import.meta.url).href,zt=""+new URL("AR-B816C-CIka6EB3.png",import.meta.url).href,Jt=""+new URL("AR-SAND-BpLNDMKH.png",import.meta.url).href,qt=""+new URL("BRASS-5al0fgci.png",import.meta.url).href,Kt=""+new URL("BRSTONE-BxLw_BHN.png",import.meta.url).href,Qt=""+new URL("CLAY-9bOpp4wn.png",import.meta.url).href,Zt=""+new URL("CROSS-PtiPbaRk.png",import.meta.url).href,te=""+new URL("DASH-CZmcszsI.png",import.meta.url).href,ee=""+new URL("DOLMIT-Ds4N0lwQ.png",import.meta.url).href,ne=""+new URL("DOTS-DFwG-nhe.png",import.meta.url).href,le=""+new URL("EARTH-C5ec_3YI.png",import.meta.url).href,oe=""+new URL("ESCHER-DfDvcwpb.png",import.meta.url).href,ie=""+new URL("GRASS-Bos1Dol8.png",import.meta.url).href,se=""+new URL("GRATE-ntK-iw_6.png",import.meta.url).href,ae=""+new URL("HEX-DvwF6L-t.png",import.meta.url).href,re=""+new URL("HONEY-BwjEJk47.png",import.meta.url).href,_e=""+new URL("INSUL-DdipjSEk.png",import.meta.url).href,me=""+new URL("SOLID-BE-LInwT.png",import.meta.url).href;function lt(_){const s=[];let o=null;return _.split(`
`).forEach(p=>{if(p.trim()!=="")if(p.startsWith("*")){o&&(o.value+=")",s.push(o));const g=p.substring(1).split(",").map(m=>m.trim());o={id:g[0],name:g[1],value:"(",imgPath:new URL(Object.assign({"../imgs/ACAD_ISO02W100.png":bt,"../imgs/ACAD_ISO03W100.png":Ct,"../imgs/ACAD_ISO04W100.png":Rt,"../imgs/ACAD_ISO05W100.png":Lt,"../imgs/ACAD_ISO06W100.png":Ut,"../imgs/ACAD_ISO07W100.png":Ot,"../imgs/ACAD_ISO08W100.png":Nt,"../imgs/ACAD_ISO09W100.png":Wt,"../imgs/ACAD_ISO10W100.png":Vt,"../imgs/ACAD_ISO11W100.png":xt,"../imgs/ACAD_ISO12W100.png":Bt,"../imgs/ACAD_ISO13W100.png":Et,"../imgs/ACAD_ISO14W100.png":yt,"../imgs/ACAD_ISO15W100.png":kt,"../imgs/ANGLE.png":Pt,"../imgs/ANSI31.png":Mt,"../imgs/ANSI32.png":Tt,"../imgs/ANSI33.png":Ht,"../imgs/ANSI34.png":Ft,"../imgs/ANSI35.png":jt,"../imgs/ANSI36.png":Yt,"../imgs/ANSI37.png":Gt,"../imgs/ANSI38.png":Xt,"../imgs/AR-B816.png":$t,"../imgs/AR-B816C.png":zt,"../imgs/AR-SAND.png":Jt,"../imgs/BRASS.png":qt,"../imgs/BRSTONE.png":Kt,"../imgs/CLAY.png":Qt,"../imgs/CROSS.png":Zt,"../imgs/DASH.png":te,"../imgs/DOLMIT.png":ee,"../imgs/DOTS.png":ne,"../imgs/EARTH.png":le,"../imgs/ESCHER.png":oe,"../imgs/GRASS.png":ie,"../imgs/GRATE.png":se,"../imgs/HEX.png":ae,"../imgs/HONEY.png":re,"../imgs/INSUL.png":_e,"../imgs/SOLID.png":me})[`../imgs/${g[0]}.png`],import.meta.url).href}}else{if(!o){console.error("Invalid pattern file format. Missing pattern start marker (*)");return}const g=p.trim().split(",").map(h=>h.startsWith(".")?"0"+h:h).join(),m=(o.value==="("?"(":" (")+g+")";o.value+=m}}),o&&s.push(o),s}var L=(_=>(_.ANSI="ANSI",_.ISO="ISO",_.ANY="ANY",_))(L||{});const H={ANSI:new URL(""+new URL("mx-CCuJTE_q.pat",import.meta.url).href,import.meta.url).href,ISO:new URL(""+new URL("mxiso-BxzaJzF8.pat",import.meta.url).href,import.meta.url).href,ANY:new URL(""+new URL("mxuser-tWg2Arwx.pat",import.meta.url).href,import.meta.url).href},A=R(),U=R(0),F={};Object.keys(H).forEach(async _=>{const s=H[_],v=await(await fetch(s)).blob(),p=await J(v);F[_]=lt(p)});const O=R(""),E=_=>{O.value=H[_],A.value=F[_];let s=0;N.value&&(s=A.value.indexOf(N.value)),N.value=A.value[s]},N=R(),pe=_=>{if(A.value){const s=A.value.indexOf(_);s>=0&&(U.value=s)}N.value=_},ge=()=>({patContent:A,activeIndex:U,defaultPatContents:F,switchPath:E,filePath:O,item:N,onchange:pe}),ue={class:"px-3"},ce={class:"d-flex algin-center mt-3"},fe={class:"mt-2"},de={class:"fill_box"},Ae=["onClick"],ve={class:"d-inline-block text-truncate"},Se=Z({__name:"FillSelectDialog",emits:["change"],setup(_,{expose:s,emit:o}){const{isShow:v,showDialog:p}=q(!1),g=R(),m=()=>{g.value&&g.value.click()},h=async u=>{const n=u.target,i=n.files;if(!i)return;const D=i[0],d=await st(D);if(typeof d!="object")return;n.value="";const V=await J(d);A.value=lt(V)},y=o,W=()=>{if(A.value){const u=A.value[U.value];y("change",u)}p(!1)},I=[{name:"确定",fun:W,primary:!0},{name:"关闭",fun:()=>p(!1)}];return s({showDialog:p}),(u,n)=>(B(),X(Q,{title:u.t("600"),"max-width":"400",modelValue:r(v),"onUpdate:modelValue":n[4]||(n[4]=i=>C(v)?v.value=i:null),footerBtnList:I},{default:l(()=>[c("div",ue,[c("div",ce,[pt(c("input",{class:"form__inset w-100",disabled:!0,"onUpdate:modelValue":n[0]||(n[0]=i=>C(O)?O.value=i:null)},null,512),[[gt,r(O)]]),t(K,{onClick:m,class:"ml-1"},{default:l(()=>[w(S(u.t("269"))+"(F)...",1)]),_:1})]),c("input",{type:"file",ref_key:"fillFileSelect",ref:g,onChange:h,style:{display:"none"},accept:".pat"},null,544),c("div",fe,[t(M,{density:"compact",class:"mr-2",onClick:n[1]||(n[1]=i=>r(E)(r(L).ANSI))},{default:l(()=>n[5]||(n[5]=[w("ANSI")])),_:1}),t(M,{density:"compact",class:"mr-2",onClick:n[2]||(n[2]=i=>r(E)(r(L).ISO))},{default:l(()=>n[6]||(n[6]=[w("ISO")])),_:1}),t(M,{density:"compact",onClick:n[3]||(n[3]=i=>r(E)(r(L).ANY))},{default:l(()=>[w(S(u.t("601")),1)]),_:1})]),c("div",de,[(B(!0),tt(et,null,ut(r(A),(i,D)=>(B(),X(dt,{text:i.id+" "+i.name,location:"bottom","open-delay":800},{activator:l(({props:d})=>[c("div",ct({ref_for:!0},d,{class:["fill_pattern",r(U)===D?"fill_pattern_active":""],onClick:V=>U.value=D,onDblclick:W}),[t(nt,{src:i.imgPath||"",width:"32",height:"32"},null,8,["src"]),c("span",ve,S(i.id),1)],16,Ae)]),_:2},1032,["text"]))),256))])])]),_:1},8,["title","modelValue"]))}}),we=at(Se,[["__scopeId","data-v-7f7d957f"]]),he={class:"px-3"},De={class:"d-flex align-center"},Ie={class:"mr-2"},Ve=Z({__name:"index",setup(_){const{isShow:s,showDialog:o}=q(!1,"Mx_Hatch"),v=R(),{patContent:p,onchange:g,item:m,switchPath:h}=ge(),y=ft(s,e=>{e&&!p.value&&(h(L.ANSI),y())}),W=e=>{v.value?.showDialog(e)},I=G(0,"PatternFillingDialog_angle"),u=G(11,"PatternFillingDialog_proportion");let n,i;const D=async()=>{o(!1);const e=new St;e.clearLastInputPoint(),e.setMessage(`
`+x("指定填充区域内部一点")+":"),e.disableAllTrace(!0),e.setDisableOsnap(!0);const a=await e.go();a&&(i=a,n="point"),o(!0)};let d;const V=async()=>{o(!1);const e=await z.userSelect(x("选择对象"));if(e&&e.length>0){const f=e[0].getMcDbEntity();f&&(d=f),n="object"}o(!0)},j=async()=>{if(n==="point"&&i){const e=z.builderHatchFromPoint(i);if(!e){It.acutPrintf(x("没有找到闭合区域")+`
`),rt().error(x("没有找到闭合区域"));return}let a=wt.getCurrentMxCAD();m.value&&(a.addPatternDefinition(m.value.id,m.value.value),a.drawPatternDefinition=m.value.id),e.patternAngle=I.value,a.drawHatch(e,u.value*10),o(!1),_t(),n=i=void 0}n==="object"&&d&&(d instanceof ht&&new Dt,d=n=void 0,o(!1))},Y=()=>{n=d=i=void 0,o(!1)},ot=[{name:"确定",fun:j,primary:!0,disabled:()=>typeof n>"u"},{name:"关闭",fun:Y}],it={enter:()=>{document.activeElement?.tagName!=="INPUT"&&j()},esc:Y,k:D,b:V};return(e,a)=>(B(),tt(et,null,[t(Q,{title:e.t("602"),"max-width":"360",modelValue:r(s),"onUpdate:modelValue":a[4]||(a[4]=f=>C(s)?s.value=f:null),footerBtnList:ot,keys:it},{default:l(()=>[c("div",he,[t(k,{title:e.t("603"),class:"mt-2"},{default:l(()=>[t(T,null,{default:l(()=>[t(b,{cols:"8"},{default:l(()=>[t(At,{items:r(p),"item-title":"id",modelValue:r(m),"onUpdate:modelValue":[a[0]||(a[0]=f=>C(m)?m.value=f:null),r(g)],"return-object":""},{prepend:l(()=>[w(S(e.t("362"))+": ",1)]),_:1},8,["items","modelValue","onUpdate:modelValue"])]),_:1}),t(b,{cols:"4"},{default:l(()=>[t(K,{style:{"min-width":"60px"},onClick:a[1]||(a[1]=f=>W(!0))},{default:l(()=>[t(vt,{icon:"class:iconfont more"})]),_:1})]),_:1})]),_:1}),t(T,null,{default:l(()=>[t(b,{cols:"8"},{default:l(()=>[c("div",De,[c("span",Ie,S(e.t("604"))+":",1),t(nt,{src:r(m)?.imgPath||"",width:"32",height:"32",style:{flex:"unset"}},null,8,["src"])])]),_:1}),t(b,{cols:"4"})]),_:1})]),_:1},8,["title"]),t(k,{title:e.t("605"),class:"mt-2"},{default:l(()=>[t(T,null,{default:l(()=>[t(b,{cols:"6"},{default:l(()=>[t(P,{"key-name":"G",colon:""},{default:l(()=>[w(S(e.t("281")),1)]),_:1}),t($,{modelValue:r(I),"onUpdate:modelValue":a[2]||(a[2]=f=>C(I)?I.value=f:null),items:[0,5,10,15,20,30,45,60,90,95,100,120,135,150]},null,8,["modelValue"])]),_:1}),t(b,{cols:"6"},{default:l(()=>[t(P,{"key-name":"S",colon:""},{default:l(()=>[w(S(e.t("240")),1)]),_:1}),t($,{modelValue:r(u),"onUpdate:modelValue":a[3]||(a[3]=f=>C(u)?u.value=f:null),items:[.25,.5,.75,1,1.25,1.5,1.75,2,11]},null,8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["title"]),t(k,{title:e.t("606"),class:"mt-2"},{default:l(()=>[c("div",null,[t(mt,{onClick:D}),t(P,{"key-name":"K"},{default:l(()=>[w(S(e.t("607"))+": "+S(e.t("216")),1)]),_:1})])]),_:1},8,["title"])])]),_:1},8,["title","modelValue"]),t(we,{ref_key:"fillSelectDialog",ref:v,onChange:r(g)},null,8,["onChange"])],64))}});export{Ve as default};
