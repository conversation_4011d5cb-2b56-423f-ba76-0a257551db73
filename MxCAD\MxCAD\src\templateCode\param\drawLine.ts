import { MxCpp, McGePoint3d, McDb<PERSON><PERSON>, McCmColor} from "mxcad";

//画直线
function drawLine() {
  const mxcad = MxCpp.getCurrentMxCAD();
  // 创建新画布
  mxcad.newFile();

  // 绘制直线
  let line_1 = new McDbLine()
  let pt1 = new McGePoint3d(1000, 200, 0)
  let pt2 = new McGePoint3d(-1000, -200, 0)
  line_1.startPoint = pt1
  line_1.endPoint = pt2
  line_1.trueColor = new McCmColor(0, 255, 0)
  mxcad.drawEntity(line_1)

  let line_2 = new McDbLine(1000, 500, 0, -1000, -500, 0)
  line_2.trueColor = new McCmColor(255, 0, 0)
  line_2.linetypeScale = 0.4
  mxcad.drawEntity(line_2)

  mxcad.drawLine(1000, 800, -1000, -800)

  mxcad.zoomAll()
  mxcad.zoomScale(0.8)
};

// 调用画直线方法
drawLine();
