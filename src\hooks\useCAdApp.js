import {useRoute} from "vue-router";
import {tansParams} from "@/utils/ruoyi.js";
import {getToken} from "@/utils/auth.js";

export function useCAdApp() {
    const route = useRoute()
    console.log("🚀 ~ useCAdApp ~ route.query:", route.query)
    const cadAppSrc = (params = {}) => {
        return `${import.meta.env.VITE_APP_MX_APP_SRC}?
${tansParams({
            ...route.query,
            token: getToken(),
            mod: 'preview',
            ...params
        })}`
    }
    return {
        cadAppSrc,
    }
}
