import { MxCpp, McGePoint3d, McDbText, McCmColor } from "mxcad";

function drawText() {
  const mxcad = MxCpp.getCurrentMxCAD();
  // 创建新画布
  mxcad.newFile();

  // 绘制文字
  let text = new McDbText()
  text.textString = 'Hello World'
  text.position = new McGePoint3d(1000, 200, 0)
  text.height = 50
  text.trueColor = new McCmColor(0, 255, 0)
  mxcad.drawEntity(text)

  mxcad.zoomAll()
  mxcad.zoomScale(0.8)
};

// 调用画文字的方法
drawText();