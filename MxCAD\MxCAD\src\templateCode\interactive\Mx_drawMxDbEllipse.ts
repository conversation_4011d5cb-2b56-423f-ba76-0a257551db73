import { McDbEllipse,MxCADUiPrPoint, McDbLine, McGePoint3d, MxCADUiPrDist, MxCpp } from "mxcad";

// 绘制椭圆
async function drawMxDbEllipse() {
 
    // 创建椭圆对象
    const ellipse = new McDbEllipse();
    
    // 指定椭圆的轴端点
    const getPt1 = new MxCADUiPrPoint();
    getPt1.setMessage("\n指定椭圆的轴端点:");
    const pt1 = await getPt1.go();
    if (!pt1) return;

    // 指定椭圆轴的另一个端点
    const getPt2 = new MxCADUiPrPoint();
    getPt2.setMessage("\n指定椭圆轴的另一个端点:");
    getPt2.setUserDraw((pt, pw) => {
        const line = new McDbLine(pt, pt1);
        pw.drawMcDbEntity(line);
    })
    const pt2 = await getPt2.go();
    if (!pt2) return;

    // 获取椭圆中心点
    const center = new McGePoint3d((pt1.x + pt2.x) / 2, (pt1.y + pt2.y) / 2);
    ellipse.center = center;
    ellipse.majorAxis = pt1.sub(pt2);
    const xRadius = pt1.distanceTo(pt2) / 2

    // 指定另一条半轴长度
    const getDist = new MxCADUiPrDist();
    getDist.setMessage("指定另一条半轴长度")
    getDist.setBasePt(center)
    getDist.setUserDraw((pt, pw) => {
        const yRadius = center.distanceTo(pt) / 2
        // 设置椭圆的副轴长度与主轴长度的比值
        ellipse.radiusRatio = yRadius / xRadius
        pw.drawMcDbEntity(ellipse)
    })
    let length = await getDist.go()
    if (!length) return
    if (getDist.getDetailedResult() === Mx.DetailedResult.kCoordIn) {
        const yRadius = length / 2
        ellipse.radiusRatio = yRadius / xRadius
    }
    MxCpp.getCurrentMxCAD().drawEntity(ellipse)
};

// 调用绘制椭圆的方法
drawMxDbEllipse();