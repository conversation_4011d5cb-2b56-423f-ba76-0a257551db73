<script setup>
import {useIframeCommunication} from "@/hooks/useIframeCommunication.js";
import {getEquipmentInfoString} from "@/api/annotation/index.js";
import {useRoute} from "vue-router";
const { proxy } = getCurrentInstance();
const route = useRoute()

const taskId = route.query.id
const { sendMessage, cleanup } = useIframeCommunication();

const cadAppRef = inject("cadAppRef");

const init = () => {
  const msg = {
    content: "Cable_Annotation",
    type: "onlycad",
    options:{name:'JGFZH_Draw'}
  }
  sendMessage(cadAppRef.value, msg, (res => {
    if(res.type!="showMessage")
    getEquData(res.idList)
  }))
}

const getEquData = (idList) => {
  const newList=[...new Set(idList)]
  const data = {
    taskId,
    equipmentIds: newList,
    type: 'TY_DL'
  }
  getEquipmentInfoString(data).then(res => {
    if(res.code === 200 && res.data.length) {
      const msg = {
        cmd: "Cable_Annotation_Draw",
        type: "sendStringToExecute",
        param: {
          labelList: res.data
        }
      }
      sendMessage(cadAppRef.value, msg, (res => {
      }))
    }else{
      proxy.$message.warning('未找到设备信息！')
    }
  })
}

init()

</script>

<template>

</template>

<style lang="scss" scoped>

</style>