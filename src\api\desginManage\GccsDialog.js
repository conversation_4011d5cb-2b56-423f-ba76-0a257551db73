import request from '@/utils/request'

//  查询字典
export function getDicts(data) {
    return request({
        url: '/base/dictionary/getDicts',
        method: 'post',
        data: data
    })
}

// 根据气象父id查询气象数据
export function getInfoById(params) {
    return request({
        url: '/base_climate_zone/getInfoById',
        method: 'get',
        params: params
    })
}

// 新增气象数据
export function addInfo(data) {
    return request({
        url: '/base_climate_zone/addInfo',
        method: 'post',
        data: data
    })
}

//#region 工程相关 engineering
//  获取工程列表查询
export function getEngineering(data) {
    return request({
        url: '/engineering/getEngineering',
        method: 'post',
        data: data
    })
}

//  新增或修改
export function addOrUpdate(data) {
    return request({
        url: '/engineering/addOrUpdate',
        method: 'post',
        data: data
    })
}

//  工程信息查询【根据项目名称、项目编号、供电公司】
export function getList(data) {
    return request({
        url: '/engineering/getList',
        method: 'post',
        data: data
    })
}

//  删除工程信息以及相关信息
export function delEngineering(data) {
    return request({
        url: '/engineering/delEngineering',
        method: 'post',
        data: data
    })
}

//  回退工程
export function rollbackEngineering(params) {
    return request({
        url: '/engineering/rollback',
        method: 'get',
        params: params
    })
}
//  回退工程---copy
export function rollback(id) {
    return request({
        url: `/engineering/huitui/${id}`,
        method: 'get',
    })
}
//  移交工程---copy
export function yijiao(id) {
    return request({
        url: `/engineering/yijiao/${id}`,
        method: 'get',
    })
}
export function qianshou(id) {
    return request({
        url: `/engineering/qianshou/${id}`,
        method: 'get',
    })
}
// 用户 设计：未开始改到进行中
export function changeEngineeringStage(params) {
    return request({
        url: '/engineering/rollback2',
        method: 'get',
        params: params
    })
}
// 用户 点击移交
export function moveEngineeringDesiger(params) {
    return request({
        url: '/engineering/rollback3',
        method: 'get',
        params: params
    })
}
// 用户保存sdk版本
export function changeEngineeringVersion(params) {
    return request({
        url: '/engineering/changeEngineeringVersion',
        method: 'get',
        params: params
    })
}
// 工程查询接口
export function selectEngineeringByCondition(data) {
    return request({
        url: '/engineering/selectEngineeringByCondition',
        method: 'post',
        data: data
    })
}
// 工程方案提交
export function simulateSubmit(params) {
    return request({
        url: '/engineering/simulateSubmit',
        method: 'get',
        params
    })
}
// 工程方案提交-临时假接口
export function simulateSubmit2(params) {
    return request({
        url: '/engineering/simulateSubmit2',
        method: 'get',
        params
    })
}


// 工程复用
export function copyEngineeringReuse(params) {
    return request({
        url: '/engineering/engineeringReuse',
        method: 'get',
        params
    })
}
//#endregion

//#region 工程参数设置
// 工程参数设置- 获取特定物料属性的物料 (value = 杆号牌杆用, 杆号牌塔用)
export function getProjectMaterialsByLX(params) {
    return request({
        url: '/rodLineJointDraw/getProjectMaterialsByLX',
        method: 'get',
        params: params
    })
}

// 工程参数设置-获取特定物料类型的物料 (materialsTypekey = QNQ(驱鸟器), QNFC(驱鸟风车), DlGZ(电缆管枕), JSP(警示牌), BZZ(标志桩), GB(盖板))
export function getProjectMaterials(params) {
    return request({
        url: '/rodLineJointDraw/getProjectMaterials',
        method: 'get',
        params: params
    })
}

// 工程参数设置-通过key获取module (moduleTypeKey = FLYXJD 防雷装置数据获取)
export function getModuleByTypeKeys(params) {
    return request({
        url: '/rodLineJointDraw/getModuleByTypeKeys',
        method: 'get',
        params: params
    })
}

// 工程参数设置-保存
export function addEngineeringParame(params) {
    return request({
        url: '/rodLineJointDraw/addEngineeringParameter',
        method: 'post',
        data: params
    })
}

// 工程参数设置-根据工程id获取工程参数
export function getEngineeringParameter(params) {
    return request({
        url: '/rodLineJointDraw/getEngineeringParameter',
        method: 'get',
        params: params
    })
}
//#endregion


// 架空线路绘制-电压等级下拉
export function getEleicLevel(params) {
    return request({
        url: '/rodLineJointDraw/getEleicLevel',
        method: 'get',
        params: params
    })
}

// 一次性数据
export function getData(params) {
    return request({
        url: '/rodLineJointDraw/getData',
        method: 'get',
        params: params
    })
}

// 获取杆塔基础
export function getBasicTower(params) {
    return request({
        url: '/rodLineJointDraw/getBasicTower',
        method: 'get',
        params: params
    })
}

// 根据杆塔类型获取状态
export function getDataByKey(params) {
    return request({
        url: '/base/legendType/getDataByKey',
        method: 'get',
        params: params
    })
}

// 柱上变台方案类别列表与柱上变台列表
export function getTableScheme(params) {
    return request({
        url: '/rodLineJointDraw/getTableScheme',
        method: 'get',
        params: params
    })
}

// 根据线路状态获取线路类型下拉
export function getOriginalRodLineJointDraw(params) {
    return request({
        url: '/rodLineJointDraw/getOriginalRodLineJointDraw',
        method: 'get',
        params: params
    })
}

// 根据线路状态获取线路类型下拉
export function getEngineerLine(params) {
    return request({
        url: '/rodLineJointDraw/getEngineerLine',
        method: 'get',
        params: params
    })
}

// 拉线绘制-拉线类型
export function getPullType(params) {
    return request({
        url: '/rodLineJointDraw/getPullType',
        method: 'get',
        params: params
    })
}

// 拉线绘制-拉线方案
export function getPullScheme(params) {
    return request({
        url: '/rodLineJointDraw/getPullScheme',
        method: 'get',
        params: params
    })
}

// 柱上设备绘制-杆高绘制
export function getCementPoleHeight(params) {
    return request({
        url: '/rodLineJointDraw/getCementPoleHeight',
        method: 'get',
        params: params
    })
}

// 线路绘制-横担长度数据
export function getHengDanLengthData(params) {
    return request({
        url: '/rodLineJointDraw/getHengDanLengthData',
        method: 'get',
        params: params
    })
}

// 保存模版数据
export function saveTemplateData(data) {
    return request({
        url: '/engineering/saveTemplateData',
        method: 'post',
        data
    })
}

// 获取模版的下拉列表
export function getTemplateList(params) {
    return request({
        url: '/engineering/getTemplateList',
        method: 'get',
        params
    })
}

// 获取模版数据 根据模版类型
export function getTemplateDataByType(params) {
    return request({
        url: '/engineering/getTemplateDataByType',
        method: 'get',
        params
    })
}

export function getHzgctuByTaskId(params) {
    return request({
        url: `/equipmentInfo/getHzgctuByTaskId/${params}`,
        method: 'get'
    })
}

