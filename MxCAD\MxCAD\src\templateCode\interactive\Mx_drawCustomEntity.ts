import { IMcDbDwgFiler, McDbCustomEntity, McDbPolyline, McGePoint3d, McGePoint3dArray, MxCADUiPrPoint, MxCADWorldDraw, MxCpp } from "mxcad";

// 新创建 McDbTestLineCustomEntity 类继承 McDbCustomEntity
class McDbTestRectangle extends McDbCustomEntity {
    // 定义McDbTestLineCustomEntity内部的点对象 
    // 矩形角点pt1、pt2
    private pt1: McGePoint3d = new McGePoint3d();
    private pt2: McGePoint3d = new McGePoint3d();
    // 构造函数
    constructor(imp?: any) {
        super(imp);
    }
    // 创建函数
    public create(imp: any) {
        return new McDbTestRectangle(imp)
    }
    // 获取类名
    public getTypeName(): string {
        return "McDbTestRectangle";
    }
    // 读取自定义实体数据pt1、pt2
    public dwgInFields(filter: IMcDbDwgFiler): boolean {
        this.pt1 = filter.readPoint("pt1").val;
        this.pt2 = filter.readPoint("pt2").val;
        return true;
    }
    // 写入自定义实体数据pt1、pt2
    public dwgOutFields(filter: IMcDbDwgFiler): boolean {
        filter.writePoint("pt1", this.pt1);
        filter.writePoint("pt2", this.pt2);
        return true;
    }

    // 移动自定义对象的夹点
    public moveGripPointsAt(iIndex: number, dXOffset: number, dYOffset: number, dZOffset: number) {
        this.assertWrite();
        if (iIndex == 0) {
            this.pt1.x += dXOffset;
            this.pt1.y += dYOffset;
            this.pt1.z += dZOffset;
        }
        else if (iIndex == 1) {
            this.pt2.x += dXOffset;
            this.pt2.y += dYOffset;
            this.pt2.z += dZOffset;
        }
    };
    // 获取自定义对象的夹点
    public getGripPoints(): McGePoint3dArray {
        let ret = new McGePoint3dArray()
        ret.append(this.pt1);
        ret.append(this.pt2);
        return ret;
    };
    // 绘制实体
    public worldDraw(draw: MxCADWorldDraw): void {
        const pl = new McDbPolyline();
        pl.isClosed = true;
        pl.addVertexAt(this.pt1);
        pl.addVertexAt(new McGePoint3d(this.pt2.x, this.pt1.y));
        pl.addVertexAt(this.pt2);
        pl.addVertexAt(new McGePoint3d(this.pt1.x, this.pt2.y));
        draw.drawEntity(pl);
    }
    // 设置pt1
    public setPoint1(pt1: McGePoint3d) {
        this.assertWrite();
        this.pt1 = pt1.clone();
    }
    // 设置pt2
    public setPoint2(pt2: McGePoint3d) {
        this.assertWrite();
        this.pt2 = pt2.clone();
    }
    // 获取pt1
    public getPoint1() {
        return this.pt1;
    }
    // 获取pt2
    public getPoint2() {
        return this.pt2;
    }
}


async function MxTest_DrawCustomEntity() {
    let mxcad = MxCpp.getCurrentMxCAD();
    const getPoint = new MxCADUiPrPoint();
    getPoint.setMessage("\n指定第一个角点:");
    let pt1 = await getPoint.go();
    if (!pt1) return;
    const myRect = new McDbTestRectangle();
    myRect.setPoint1(pt1);

    getPoint.setMessage("\n指定第二个角点:");
    getPoint.setUserDraw((pt, pw) => {
        myRect.setPoint2(pt);
        pw.drawMcDbEntity(myRect);
    })
    let pt2 = await getPoint.go();
    if (!pt2) return;

    myRect.setPoint2(pt2);
    mxcad.drawEntity(myRect);
}

// 注册自定义实体
new McDbTestRectangle().rxInit();
// 调用方法绘制自定义实体
MxTest_DrawCustomEntity();
