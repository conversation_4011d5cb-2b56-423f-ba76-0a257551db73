import { MxCpp, McDbPoint, McCmColor } from "mxcad"

//画点
function drawPoint() {
  const mxcad = MxCpp.getCurrentMxCAD();
  // 创建新画布
  mxcad.newFile();

  // 循环画点
  const point = new McDbPoint()
  const color = new McCmColor(0, 255, 0)
  point.trueColor = color
  for (let i = 0; i < 100; i++) {
    point.setPosition(i, 200 + i)
    mxcad.drawEntity(point)
  }
  for (let i = 0; i < 100; i++) {
    mxcad.drawPoint(i, 150 + i)
  }

  mxcad.zoomAll()
  mxcad.zoomScale(0.8)
};

// 调用画点的函数
drawPoint();