import request from '@/utils/request'
import { getNoModuleTypeKey } from '@/views/pages/online/commonData'

const noModuleIDLog = new Map()

//推导杆塔型号
export function getPole(data) {
    return request({
        url: '/autoRule/getPole',
        method: 'post',
        data
    })
}
/**
 * 得到未选型的模块
 * @param {*} data 电压等级和模块类型 voltage,moduletypekey
 * @returns 
 */
export function getNoModuleId(params) {
    return request({
        url: '/autoRule/getModulesByVoltageAndType',
        method: 'get',
        params
    })
}
/**
 * 得到未选型的模块
 * @param {*} data 电压等级和模块类型 voltage,legendTypeKey
 * @returns 
 */
export async function getNoModuleIdByVoltageLegendType(voltage, legendTypeKey) {
    console.log('当前noModuleIDLog个数', noModuleIDLog.size)
    const mapKey = voltage + "#" + legendTypeKey
    if(noModuleIDLog.has(mapKey)) return noModuleIDLog.get(mapKey)
        const moduletypekey = getNoModuleTypeKey(voltage, legendTypeKey)
    if(!moduletypekey) return ''
    const noModuleID = await getNoModuleId({voltage, moduletypekey})
    if(noModuleID.code === 200 && noModuleID.data.length > 0){
        noModuleIDLog.set(mapKey, noModuleID.data[0].moduleid)
        return noModuleID.data[0].moduleid
    }
    noModuleIDLog.set(mapKey, '')
    return ''
}

