import{M as f}from"./index-8X61wlK0.js";import{M as o,Z as d,$ as _,_ as h}from"./index-D95UjFey.js";import{E as u,k as V,a as y,e as v,m as g,j as k,U as B,C as b}from"./vuetify-B_xYg4qv.js";import{h as w,d as x,a0 as c,_ as C,$ as e,a1 as t,m as s,a3 as L,a4 as D,V as l,F as M,u as F,B as I}from"./vue-DfH9C9Rx.js";import"./mxcad-CfPpL1Bn.js";import"./mxdraw-C_n_7lEs.js";import"./mapbox-gl-PN3pDJHq.js";import"./xlsx-Ck5MUZvf.js";const S={class:"px-3 mt-2"},U={class:"d-flex algin-center",style:{width:"50px"}},E={class:"ml-7"},N={class:"ml-2"},R={class:""},$={class:""},j={class:"mt-2"},A=w({__name:"index",setup(T){const p=[{name:"确定",fun:()=>{},primary:!0},{name:"取消",fun:()=>_(!1)}],n=x(.2),r=[{name:"ByLayer",size:.01},{name:"ByBlock",size:.01},{name:"默认",size:.01},{name:"0.00mm",size:.01},{name:"0.05mm",size:.05},{name:"0.09mm",size:.09},{name:"0.13mm",size:.13},{name:"0.15mm",size:.15},{name:"0.18mm",size:.18},{name:"0.20mm",size:.2},{name:"0.25mm",size:.25},{name:"0.30mm",size:.3},{name:"0.35mm",size:.35},{name:"0.40mm",size:.4},{name:"0.50mm",size:.5},{name:"0.53mm",size:.53},{name:"0.60mm",size:.6},{name:"0.70mm",size:.7},{name:"0.80mm",size:.8},{name:"0.90mm",size:.9},{name:"1.00mm",size:1},{name:"1.06mm",size:1.06},{name:"1.20mm",size:1.2},{name:"1.40mm",size:1.4},{name:"1.58mm",size:1.58},{name:"2.00mm",size:2},{name:"2.11mm",size:2.11}];return(a,i)=>(c(),C(f,{title:a.t("532"),"max-width":"500",modelValue:F(d),"onUpdate:modelValue":i[1]||(i[1]=m=>I(d)?d.value=m:null),footerBtnList:p},{default:e(()=>[t("div",S,[s(b,null,{default:e(()=>[s(u,{cols:"5"},{default:e(()=>[s(o,{title:a.t("183")},{default:e(()=>[s(V,{density:"compact",class:"list-border overflow-y py-0",selected:[r[0].name],"active-class":"bg-light-blue-darken-2",height:"160"},{default:e(()=>[(c(),L(M,null,D(r,(m,z)=>s(y,{key:z,value:m.name,mandatory:"",class:"pa-0","min-height":"20",height:"20"},{prepend:e(()=>[t("div",U,[s(v,{class:"border-opacity-100",thickness:m.size*n.value*10,length:"50"},null,8,["thickness"])])]),default:e(()=>[s(g,{textContent:l(m.name),class:"ml-2"},null,8,["textContent"])]),_:2},1032,["value"])),64))]),_:1},8,["selected"])]),_:1},8,["title"])]),_:1}),s(u,{cols:"7","align-self":"auto"},{default:e(()=>[s(o,{title:a.t("533")},{default:e(()=>[t("div",E,l(a.t("249"))+"(mm) ",1)]),_:1},8,["title"]),s(k,{class:"my-2 ml-10"},{label:e(()=>[t("span",N,l(a.t("534")),1)]),_:1}),s(o,{title:a.t("535")},{default:e(()=>[s(B,{step:"0.1",class:"my-4","tick-size":"4","thumb-size":10,min:"0",max:"1",modelValue:n.value,"onUpdate:modelValue":i[0]||(i[0]=m=>n.value=m),"thumb-label":"","hide-details":"",color:"#007AD9"},{prepend:e(()=>[t("span",R,l(a.t("536")),1)]),append:e(()=>[t("span",$,l(a.t("537")),1)]),_:1},8,["modelValue"])]),_:1},8,["title"])]),_:1})]),_:1}),t("p",j,l(a.t("538"))+"：ByLayer",1)])]),_:1},8,["title","modelValue"]))}}),Q=h(A,[["__scopeId","data-v-4686d92d"]]);export{Q as default};
