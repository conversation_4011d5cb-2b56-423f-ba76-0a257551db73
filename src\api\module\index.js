import request from '@/utils/request'

// ​/base_modules新增模块-post
export function addBaseModules(data) {
  return request({
    url: '​/base_modules',
    method: 'post',
    data: data
  })
}
// ​/base_modules{objId}获取模块详细信息-get
export function getBaseModules(params) {
    return request({
      url: '/monitor/cache'+params,
      method: 'get',
    })
  }
// /base_modules/{objIds} 删除模块-post
export function delBaseModules(data) {
    return request({
      url: '​/base_modules'+data,
      method: 'post',
    })
  }
// /base_modules/export 导出模块-post
export function exportBaseModules() {
    return request({
      url: '/base_modules/export',
      method: 'post',
    })
  }
// /base_modules/list 模块列表-get
export function baseModulesList(query) {
  return request({
    url: '/base_modules/list',
    method: 'get',
    params: query
  })
}
// ​/base_modules​/update修改模块-post
export function updateBaseModules(data) {
    return request({
      url: '​/base_modules​/update',
      method: 'post',
      data: data
    })
  }

   // /base_modules_type/treeList 模块类型树-get
  export function baseModulesTreeList(query) {
    return request({
      url: '/base_modules_type/treeList',
      method: 'get',
      query:query
    })
  }


