import {MxCpp} from "mxcad";

/** 生成报表 多合一
 * @param formData 
 * @param excelFun 
 * @param chunkSize 当前图框内容纳数据个数 
 * @param pageOffsetX 图框和图框之间 间距
 */
export const generateExcelReport = async (formData, excelFun, chunkSize, pageOffsetX = 520) => {
  console.log("命令方法得到页面数据：", formData);
  let flag = false; // 用于控制递归是否已经开始
  let allFiles = []; // 用于存储所有生成的文件
  flag = true; // 标记递归开始
  let mxcad = MxCpp.App.getCurrentMxCAD();
  mxcad.newFile();
 
  const processInChunks = async (
    startIndex = 0,
    chunkSize,
    offsetX = 0,
    num = 1
  ) => {
    const arr = formData.tableData ? JSON.parse(formData.tableData) : [];
    const chunk = arr.slice(startIndex, startIndex + chunkSize); // 获取当前批次的数据
    // 如果当前批次有数据，调用 blockExcelFun
    if (chunk.length > 0) {
      const newValue = {
        ...formData,
        tableData: JSON.stringify(chunk),
        offsetX,
        num,
      };
      await excelFun(newValue, startIndex);
      // 递归处理剩余数据
      if (startIndex + chunkSize < arr.length) {
        await processInChunks(
          startIndex + chunkSize,
          chunkSize,
          offsetX + pageOffsetX,
          num + 1
        );
      }
    }
  };
  // 启动递归处理
  const offsetX = formData.offsetX || 0
  await processInChunks(0, chunkSize, offsetX);
  const filesBlob = new Promise((resolve, reject) => {
    mxcad.saveFile(void 0, (data) => {
        let blob: Blob;
        const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
        if (isSafari) {
            blob = new Blob([data.buffer], { type: "application/octet-stream" });
        } else {
            blob = new Blob([data.buffer], { type: "application/octet-binary" });
        }
        const file = new File([blob], 'test.mxweb', { type: isSafari ? "application/octet-stream" : "application/octet-binary" });
        resolve(file); // 返回文件对象
    }, false, true);
  });
  filesBlob.then((file: File) => {
    allFiles.push(file); // 将文件添加到数组中
    // 在所有数据处理完成后，发送文件到父窗口
    window.parent.postMessage(
      {
        type: "parentCad",
        params: {
          content: formData.content,
          formData: {
            files: allFiles, // 发送所有生成的文件
            tableData: formData.tableData,
            proId: formData.proId,
            stage: formData.stage
          },
        },
      },
      "*"
    );
  }).finally(() => {
    // 递归处理完成后，重置标志
    flag = false;
  })
}
