import{M as g}from"./index-itnQ6avM.js";import{q as M,$ as k}from"./index-CzBriCFR.js";import{u as C}from"./hooks-bxDgWiT9.js";import{L as I,a as B,h as w}from"./vuetify-BqCp6y38.js";import{h as D,d as c,c as R,a3 as d,a4 as p,u as S,B as F,_ as m,a0 as K,m as v,$ as N,a5 as T,F as U}from"./vue-Cj9QYd7Z.js";import"./mxcad-DrgW2waE.js";import"./mxdraw-BQ5HhRCY.js";import"./handsontable-Ch5RdAT_.js";import"./mapbox-gl-DQAr7S0z.js";const $={class:"d-flex algin-center"},P=D({__name:"index",setup(q){const{dialog:o}=C(),{isShow:i}=o,f=c(!1);o.onReveal(l=>{f.value=!!l.isMultiple});const x=M(),{list:h}=k(x),a=c([]),r=R(()=>h.value.filter(e=>e.name).map(({name:e})=>e)),n=c(""),V=()=>{n.value===""?a.value=[]:a.value=[r.value.findIndex(l=>l.toLocaleLowerCase()===n.value.toLocaleLowerCase())]},y=[{name:"确定",fun:()=>{o.confirm(a.value.map(l=>r.value[l]))},primary:!0},{name:"关闭",fun:()=>o.showDialog(!1)}],L=(l,e)=>{if(f.value){if(l.ctrlKey)a.value.indexOf(e)===-1?a.value=[...a.value,e]:a.value=a.value.filter(s=>s!==e);else if(l.shiftKey){const t=a.value;let s=Math.min(...t),u=Math.max(...t);s>e?s=e:u=e,a.value=r.value.map((E,_)=>_).slice(s,u+1)}}else a.value=[e]};return(l,e)=>(m(),d(g,{maxWidth:"400px",ref:"layerDialog",modelValue:S(i),"onUpdate:modelValue":e[1]||(e[1]=t=>F(i)?i.value=t:null),title:"选择图层",footerBtnList:y},{default:p(()=>[K("div",$,[v(I,{placeholder:l.t("626"),modelValue:n.value,"onUpdate:modelValue":[e[0]||(e[0]=t=>n.value=t),V]},null,8,["placeholder","modelValue"])]),v(w,{density:"compact",color:"primary",style:{height:"300px"}},{default:p(()=>[(m(!0),N(U,null,T(r.value,(t,s)=>(m(),d(B,{class:"ma-0",title:t,ref_for:!0,ref:"refItems",key:s,onClick:u=>L(u,s),active:a.value.includes(s)},null,8,["title","onClick","active"]))),128))]),_:1})]),_:1},8,["modelValue"]))}});export{P as default};
