import { MxFun } from "mxdraw";
import { McDbBlockReference, MxCADUiPrPoint, MxCpp, MxCADUiPrAngle, McGeVector3d, McDbEntity } from "mxcad";
import { formData } from './../mapBox/customize'

export function init(){
  MxFun.addCommand("KY", ky);
}

const ky = () => {
  console.log('命令方法得到页面数据：', formData.value)
  InsertBlock(`/assets/CrossDWG/${formData.value.spanClass}.mxweb`, 1)
}
// 插入图块
async function InsertBlock(fileName, scale) {
  // 设置图块路径
  let blkFilePath = import.meta.url.substring(0, import.meta.url.lastIndexOf('/')) + fileName;
  // 插入图块文件
  let mxcad = MxCpp.App.getCurrentMxCAD();
  let blkrecId = await mxcad.insertBlock(blkFilePath, formData.value.spanClass);
  if (!blkrecId.isValid()) {
      // 插入图块
      return;
  }

  // 设置图块大小
  let blkRef = new McDbBlockReference();
  blkRef.blockTableRecordId = blkrecId;
  let box = blkRef.getBoundingBox();
  if (box.ret && formData.value.spanWidth) {
    let dLen = Math.abs(box.maxPt.x - box.minPt.x);
    blkRef.setScale(parseFloat(formData.value.spanWidth) / dLen)//formData.value.spanWidth
    //blkRef.setScale(mxcad.getMxDrawObject().screenCoordLong2Doc(1000) / dLen);
  }

  // 指定插入基点
  let getPoint = new MxCADUiPrPoint();
  getPoint.setMessage("\指定插入基点");

  // 动态绘制图块
  getPoint.setUserDraw((v, worldDraw) => {
      blkRef.position = v;
      worldDraw.drawMcDbEntity(blkRef);
  });

  let insertPT = await getPoint.go();
  if (!insertPT) return;
  blkRef.position = insertPT;

  // 指定旋转角度
  let getAngle = new MxCADUiPrAngle();
  getAngle.setMessage('请指定旋转角度')
  getAngle.setBasePt(insertPT);
  getAngle.setUserDraw((pt, worldDraw) => {
    let a = pt.sub(insertPT).angleTo2(McGeVector3d.kXAxis, McGeVector3d.kNegateZAxis)
    blkRef.rotate(insertPT, a - blkRef.rotation)
    worldDraw.drawMcDbEntity(blkRef);
  })
  let val = await getAngle.go();
  if (val) {
    const angle = getAngle.value() - blkRef.rotation;
    blkRef.rotate(insertPT, angle)
  } else {
    console.log('取消旋转')
    blkRef.rotate(insertPT, 0)
  }
  
  // 绘制图块
  let newBlkRefId = mxcad.drawEntity(blkRef);
  if (!newBlkRefId.isValid) {
      console.log("insert error");
      return;
  }
}