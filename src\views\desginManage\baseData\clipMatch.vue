<template>
  <div class="app-container">
    <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
        <el-tab-pane label="耐张线夹" name="first">
            <div class="sub-bottom">
        <div class="btn">
              <el-checkbox label="匹配时不改变线夹类别"></el-checkbox>
          <el-button  style="margin-left: 10px" type="text" plain icon="Printer" @click="handleAdd"
            >保存</el-button
          >
        </div>
          </div>
          <el-table
            v-loading="loading"
            :data="userList"
            @selection-change="handleSelectionChange"
          >
            <el-table-column
              label="线夹类别"
              align="center"
              key="nickName"
              prop="nickName"
            />
            <el-table-column
              label="规格型号"
              align="center"
              key="deptName"
              prop="dept.deptName"
            />
            <el-table-column
              label="主导线"
              align="center"
              key="deptName"
              prop="dept.deptName"
            />
            <el-table-column
              label="支导线"
              align="center"
              key="deptName"
              prop="dept.deptName"
            />
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="并沟线夹" name="second">
            <div class="sub-bottom">
        <div class="btn">
              <el-checkbox label="匹配时不改变线夹类别"></el-checkbox>
          <el-button style="margin-left: 10px" type="text" plain icon="Printer" @click="handleAdd"
            >保存</el-button
          >
        </div>
          </div>
          <el-table
            v-loading="loading"
            :data="userList"
            @selection-change="handleSelectionChange"
          >
            <el-table-column
              label="线夹类别"
              align="center"
              key="nickName"
              prop="nickName"
            />
            <el-table-column
              label="规格型号"
              align="center"
              key="deptName"
              prop="dept.deptName"
            />
            <el-table-column
              label="主导线"
              align="center"
              key="deptName"
              prop="dept.deptName"
            />
            <el-table-column
              label="支导线"
              align="center"
              key="deptName"
              prop="dept.deptName"
            />
          </el-table>
        </el-tab-pane>
        </el-tabs>
  </div>
</template>
<script setup>
const activeName = ref('first');
const handleClick=(node)=>{
    activeName.value = node.name;
}
onMounted(() => {
});
</script>
<style lang="scss" scoped>
.app-container {
  display: flex;
  height: calc(100vh - 120px);
  padding: 10px;
  .demo-tabs{
    width: 100%;
  }
  .btn{
       background:rgba(166,215,213,0.3);
       padding:5px;
       margin:0 !important;
       display: flex;;
      }
  .sub-bottom {
    background-color: #f5f5f5;
    width: 100%;
  }
}
</style>
