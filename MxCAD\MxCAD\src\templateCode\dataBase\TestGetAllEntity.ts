import { McDbAlignedDimension, McDbArc, McDbBlockReference, McDbCircle, McDbDimension, McDbHatch, McDbLine, McDbMText, McDbPolyline, McDbRasterImage, McDbRotatedDimension, McDbText, MxCADSelectionSet } from "mxcad";


// 点对象转字符串
function McGePoint3dToString(pt): string {
    return "x=" + pt.x + ",y=" + pt.y + ",z=" + pt.z;
}

// 实体对象转json对象
function McDbEntityToJsonObject(ent): object {
    let obj: any = {}
    obj.ObjectName = ent.objectName;// 对象名
    obj.id = ent.getObjectID().id;// 对象id
    obj.handle = ent.getHandle(); // 对象句柄
    obj.layer = ent.layer; // 对象所在图层
    obj.color = ent.trueColor.getColorString(); // 对象颜色说明字符串
    obj.colorVal = ent.trueColor.getColorValue(ent.layerId); // 对象颜色字符串
    obj.linetype = ent.linetype; // 对象线型
    obj.textStyle = ent.textStyle; // 对象文字样式
    obj.dxf0 = ent.dxf0;// 得到对象的DXF组码的类型名

    if (ent instanceof McDbText) {
        // 文字对象
        let txt = ent as McDbText;
        obj.position = McGePoint3dToString(txt.position);
        obj.textString = txt.textString;
    }
    else if (ent instanceof McDbMText) {
        // 多行文字对象
        let mtxt = ent as McDbMText;
        obj.contents = mtxt.contents;
        obj.location = McGePoint3dToString(mtxt.location);
    }
    else if (ent instanceof McDbLine) {
        // 直线对象
        let line = ent as McDbLine;
        obj.startPoint = McGePoint3dToString(line.startPoint);
        obj.endPoint = McGePoint3dToString(line.endPoint);
    }
    else if (ent instanceof McDbCircle) {
        // 圆对象
        let circle = ent as McDbCircle;
        obj.center = McGePoint3dToString(circle.center);
        obj.radius = circle.radius;
    }
    else if (ent instanceof McDbArc) {
        // 圆弧对象
        let arc = ent as McDbArc;
        obj.center = McGePoint3dToString(arc.center);
        obj.startAngle = arc.startAngle;
        obj.endAngle = arc.endAngle;
    }
    else if (ent instanceof McDbPolyline) {
        // 多段线对象
        let polyline = ent as McDbPolyline;
        let num = polyline.numVerts();
        obj.num = num;
        for (let i = 0; i < num; i++) {
            let pt = polyline.getPointAt(i);
            let bulge = polyline.getBulgeAt(i);
            obj["pt" + i] = McGePoint3dToString(pt.val);
            obj["bulge" + i] = bulge;
        }
    }
    else if (ent instanceof McDbBlockReference) {
        // 块引用对象
        let blkRef = ent as McDbBlockReference;
        obj.position = McGePoint3dToString(blkRef.position);
        obj.blockTransform = blkRef.blockTransform;
        obj.blockName = blkRef.blockName;
    }
    else if (ent instanceof McDbAlignedDimension) {
        // 对齐标注对象
        let alignedDim = ent as McDbAlignedDimension;
        obj.xLine1Point = McGePoint3dToString(alignedDim.xLine1Point);
        obj.xLine2Point = McGePoint3dToString(alignedDim.xLine2Point);
        let txts = alignedDim.GetAllText();
        txts.forEach((val, index) => {
            obj["txt:" + index] = val;
        });
    }
    else if (ent instanceof McDbRotatedDimension) {
        // 旋转标注对象
        let rotatedDim = ent as McDbRotatedDimension;
        obj.xLine1Point = McGePoint3dToString(rotatedDim.xLine1Point);
        obj.xLine2Point = McGePoint3dToString(rotatedDim.xLine2Point);
        let txts = rotatedDim.GetAllText();
        txts.forEach((val, index) => {
            obj["txt:" + index] = val;
        });
    }
    else if (ent instanceof McDbDimension) {
        // 标注对象
        let dim = ent as McDbDimension;
        let txts = dim.GetAllText();
        txts.forEach((val, index) => {
            obj["txt:" + index] = val;
        });
    }
    else if (ent instanceof McDbHatch) {
        // 填充对象
        let hatch = ent as McDbHatch;
        obj.patternType = hatch.patternType();
        obj.patternName = hatch.patternName();
        obj.patternAngle = hatch.patternAngle;
        obj.patternScale = hatch.patternScale;
        obj.patternSpace = hatch.patternSpace;
        obj.patternDouble = hatch.patternDouble;
        obj.hatchStyle = hatch.hatchStyle();
        obj.isSolid = hatch.isSolid();
        obj.numLoops = hatch.numLoops;
        for (let i = 0; i < obj.numLoops; i++) {
            let loop: any = hatch.getLoopAt(i);
            loop.vertices.forEach((val: any, inidex: number) => {
                loop["pt:" + inidex] = {};
                loop["pt:" + inidex].x = val.x;
                loop["pt:" + inidex].y = val.y;
                loop["pt:" + inidex].z = val.z;
            })
            obj["loop:" + i] = loop;
        };
        obj.numPatternDefinitions = hatch.numPatternDefinitions;
        for (let i = 0; i < obj.numPatternDefinitions; i++) {
            obj["patternDefinitions:" + i] = hatch.getPatternDefinitionAt(i);
        };
    }
    else if (ent instanceof McDbRasterImage) {
        // 位图对象
        let image = ent as McDbRasterImage;
        let orientation = image.getOrientation();
        obj.orientation = {};
        obj.orientation.origin = orientation.origin.toVector3();
        obj.orientation.uCorner = orientation.uCorner.toVector3();
        obj.orientation.vOnPlane = orientation.vOnPlane.toVector3();

        let clipBoundary = image.clipBoundary();
        obj.clipBoundary = {};
        clipBoundary.forEach((pt, index) => {
            obj.clipBoundary["pt" + index] = pt.toVector3();
        });

        obj.clipBoundaryType = image.clipBoundaryType();

        let imageDef = image.imageDefId().getMcDbRasterImageDef();
        if (imageDef) {
            obj.filePath = imageDef.sourceFileName;
        }
    }

    return obj;
}
// 得到图纸所有对象
async function TestGetAllEntity() {

    let ss = new MxCADSelectionSet();
    ss.allSelect();
    ss.forEach((id) => {
        let ent: any = id.getMcDbEntity();
        if (!ent) return;
        ent = McDbEntityToJsonObject(ent);
        console.log(JSON.stringify(ent));
    })
};

// 调用得到图纸所有对象的方法
TestGetAllEntity()