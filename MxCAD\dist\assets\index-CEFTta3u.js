import{M as p}from"./index-8X61wlK0.js";import{n as u,M as f,E as l,F as x,_ as c}from"./index-D95UjFey.js";import{$ as _,E as n,x as V,C as g,c as v,b as D}from"./vuetify-B_xYg4qv.js";import{h as B,a0 as b,_ as h,$ as e,a1 as a,m as t,Q as i,V as r,u as w,B as M}from"./vue-DfH9C9Rx.js";import"./mxcad-CfPpL1Bn.js";import"./mxdraw-C_n_7lEs.js";import"./mapbox-gl-PN3pDJHq.js";import"./xlsx-Ck5MUZvf.js";const y={class:"px-3"},C={class:"d-flex overflow-y-auto",style:{"max-height":"300px",height:"300px"}},k={class:"d-flex flex-column text-center mx-2"},N=B({__name:"index",setup(z){const d=[{name:"开始转换",fun:()=>{},primary:!0},{name:"关闭",fun:()=>x(!1)}];return(o,s)=>(b(),h(p,{title:o.t("DWG剪切"),modelValue:w(l),"onUpdate:modelValue":s[0]||(s[0]=m=>M(l)?l.value=m:null),"max-width":"600",footerBtnList:d},{default:e(()=>[a("div",y,[t(_,{class:"mt-1",items:[]},{prepend:e(()=>[i(r(o.t("存储路径")),1)]),append:e(()=>[t(u,null,{default:e(()=>[i(r(o.t("选取路径")),1)]),_:1})]),_:1}),t(g,{class:"mt-1"},{default:e(()=>[t(n,{cols:"5"},{default:e(()=>s[1]||(s[1]=[a("span",null,"正在转换: 2021.5.11套房图纸",-1)])),_:1}),t(n,{cols:"7"},{default:e(()=>[t(V,{"model-value":"30",color:"#28D238","bg-color":"#D9D9D9",height:"24",class:"linear-border"})]),_:1})]),_:1}),t(f,{title:"剪切区域"},{default:e(()=>[a("div",C,[s[3]||(s[3]=a("div",{class:"d-flex flex-column text-center mx-2"},[a("div",{class:"cut-box bg-black"}),a("span",null,"2021.5.11套房图纸-1")],-1)),a("div",k,[t(v,{size:"120px",variant:"text",color:"#6D6B6B"},{default:e(()=>[t(D,{icon:"class:iconfont plus",size:"100px"})]),_:1}),s[2]||(s[2]=a("span",null,"添加",-1))])])]),_:1})])]),_:1},8,["title","modelValue"]))}}),G=c(N,[["__scopeId","data-v-73774b77"]]);export{G as default};
