import {McDbBlockReference, MxCADUiPrPoint, MxCpp, McDbAttributeDefinition, McDbAttribute} from "mxcad";
// 绘制线路
export async function BlockFun(formData, fileName, scale, attribDefList) {
    console.log("BlockFun",formData,attribDefList)
    // 设置图块路径
    let blkFilePath = import.meta.url.substring(0, import.meta.url.lastIndexOf('/')) + fileName;
    console.log(blkFilePath)
    // 插入图块文件
    let mxcad = MxCpp.App.getCurrentMxCAD();
    let blkrecId = await mxcad.insertBlock(blkFilePath, formData.spanClass);
    if (!blkrecId.isValid()) {
        // 插入图块
        return { id: null, handle: null };
    }

    let blkRef = new McDbBlockReference();
    blkRef.blockTableRecordId = blkrecId;
    let box = blkRef.getBoundingBox();
    if (box.ret && formData.spanWidth) {
        let dLen = Math.abs(box.maxPt.x - box.minPt.x);
        blkRef.setScale(parseFloat(formData.spanWidth) / dLen)
    }

    let getPoint = new MxCADUiPrPoint();
    getPoint.setMessage("\指定插入基点");

    getPoint.setUserDraw((v, worldDraw) => {
        blkRef.position = v;
        worldDraw.drawMcDbEntity(blkRef);
    });

    let pt = await getPoint.go();
    if (!pt) return;
    blkRef.position = pt;
    let newBlkRefId = mxcad.drawEntity(blkRef);
    if (!newBlkRefId.isValid) {
        console.log("insert error");
        return { id: null, handle: null };
    }
    blkRef = newBlkRefId.getMcDbEntity() as McDbBlockReference;

    // 如果块有属性定义，下面为块引创建属性定义。
    blkRef.disableDisplay(true);
    let blkRecord: any = blkrecId.getMcDbBlockTableRecord();
    let ids = blkRecord.getAllEntityId();
    if(ids.length === 1 && ids[0].isKindOf("McDbBlockReference")){
        const blockId = ids[0];
        const block = blockId.getMcDbEntity() as McDbBlockReference;
        const recordId = block.blockTableRecordId;
        blkRef.blockTableRecordId = recordId;
        blkRef.disableDisplay(true);
        setAttribute(newBlkRefId, recordId, attribDefList, formData);
    }else{
        setAttribute(newBlkRefId, blkrecId, attribDefList, formData);
    }
    // 获取该对象的实体
    const ent = newBlkRefId.getMcDbEntity();
    if(!ent) return { id: null, handle: null };
    // 获取对象ID
    const entId = ent.getObjectID();
    // 获取对象句柄
    const sHandle = ent.getHandle();
    console.log("对象id", entId);
    console.log("对象句柄", sHandle);
    console.log("对象坐标", blkRef.position);
    return { id: entId, handle: sHandle, pointsXYZ: blkRef.position };
}

function setAttribute(newBlkRefId, blkrecId, attribDefList, formData){
    const blkRef = newBlkRefId.getMcDbEntity() as McDbBlockReference;
    // 如果块有属性定义，下面为块引创建属性定义。
    blkRef.disableDisplay(true);
    let blkRecord: any = blkrecId.getMcDbBlockTableRecord();
    let ids = blkRecord.getAllEntityId();
    ids.forEach((id: any, index: any) => {
        if (!id.isKindOf("McDbAttributeDefinition")) return;
        let attribDef = id.getMcDbEntity() as McDbAttributeDefinition;
        console.log(attribDef.tag)
        let tag = attribDef.tag;
        let attrib = new McDbAttribute();
        attrib.position = attribDef.position;
        attrib.alignmentPoint = attribDef.alignmentPoint
        attrib.height = attribDef.height
        attrib.trueColor = attribDef.trueColor
        attrib.widthFactor = attribDef.widthFactor
        if (attribDefList.length > 0) {
            attribDefList.forEach(item => {
                if (item.name === tag) {
                    attrib.textString = item.value
                }
            })
        } else {
            attrib.textString = attribDef.textString
        }
        attrib.tag = tag;
        if (formData.IsShowArr.length > 0) {
            formData.IsShowArr.forEach(item => {
                if (item.TagName === tag) {
                    attrib.isInvisible = item.IsShow === '1' ? false : true
                }
            })
        } else {
            attrib.isInvisible = false
        }
        attrib.transformBy(blkRef.blockTransform);
        attrib = blkRef.appendAttribute(attrib).getMcDbEntity() as McDbAttribute;
        attrib.textStyle = attribDef.textStyle
        attrib.layer = attribDef.layer
    })
    blkRef.disableDisplay(false);
}