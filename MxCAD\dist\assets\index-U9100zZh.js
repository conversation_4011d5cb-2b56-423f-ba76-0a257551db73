import{d as T,c as O,w as oe,z as ue,R as de,h as ye,a3 as q,a4 as P,u as K,B as ne,_ as B,a0 as D,m as L,n as H,Q as M,ac as J,U as Q,$ as W,F as ee,A as Ae,V as _,a5 as ie,a9 as ce}from"./vue-Cj9QYd7Z.js";import{k as V,s as o,c as De,aE as z,a7 as ae,e as j,Q as fe,aF as Te,_ as me}from"./index-CzBriCFR.js";import{M as pe}from"./index-itnQ6avM.js";import"./mxcad-DrgW2waE.js";import"./mapbox-gl-DQAr7S0z.js";import{L as G,g as Ie,a as ve,V as Pe,a8 as _e,a9 as Ne,c as Fe,b as U,h as Be,aa as Me,j as Ue,i as je,S as ze,d as te}from"./vuetify-BqCp6y38.js";import"./mxdraw-BQ5HhRCY.js";import"./handsontable-Ch5RdAT_.js";const Oe=navigator.platform.toLowerCase().includes("win")||navigator.userAgent.includes("Windows"),Re=()=>{const l=T([]),p=T(new Map),r=T({isLoading:!1,hasChanges:!1,lastError:null}),f=e=>{const n=l.value.find(s=>s.id===e);if(!n)return!1;const t=V.isKeySuspended(e),a=n.isSuspended;return t!==a&&(console.warn("Shortcut suspension state mismatch:",{id:e,managerState:t,localState:a}),a?V.suspendKey(e):V.resumeKey(e)),!0},d=(e,n)=>{const t=Array.isArray(e)?e:[e],a=[];return l.value.forEach(s=>{if(n&&s.id===n||s.isSuspended)return;const y=[];Array.isArray(s.key)?y.push(...s.key):s.key&&y.push(s.key),Oe&&s.winKey&&(Array.isArray(s.winKey)?y.push(...s.winKey):y.push(s.winKey));const k=($,g)=>{const I={ctrlKey:!!$.ctrlKey,shiftKey:!!$.shiftKey,altKey:!!$.altKey,metaKey:!!$.metaKey,keyCode:$.keyCode},N={ctrlKey:!!g.ctrlKey,shiftKey:!!g.shiftKey,altKey:!!g.altKey,metaKey:!!g.metaKey,keyCode:g.keyCode},h=I.ctrlKey===N.ctrlKey&&I.shiftKey===N.shiftKey&&I.altKey===N.altKey&&I.metaKey===N.metaKey,b=I.keyCode===N.keyCode;if(I.keyCode==="*"||N.keyCode==="*")return h;const S=h&&b;return S&&console.debug("Key comparison details:",{key1:I,key2:N,modifiersMatch:h,keyCodeMatch:b}),S};t.some($=>y.some(g=>{const I=k($,g);return I&&console.debug("Conflict detected:",{new:{keyCode:$.keyCode,ctrlKey:!!$.ctrlKey,shiftKey:!!$.shiftKey,altKey:!!$.altKey,metaKey:!!$.metaKey},existing:{keyCode:g.keyCode,ctrlKey:!!g.ctrlKey,shiftKey:!!g.shiftKey,altKey:!!g.altKey,metaKey:!!g.metaKey},shortcut:{id:s.id,label:s.info.label,command:s.info.command}}),I}))&&a.push(s)}),a.length>0&&console.warn("Shortcut conflicts found:",{newKeys:t.map(s=>({keyCode:s.keyCode,ctrlKey:!!s.ctrlKey,shiftKey:!!s.shiftKey,altKey:!!s.altKey,metaKey:!!s.metaKey})),conflicts:a.map(s=>({id:s.id,label:s.info.label,command:s.info.command,key:Array.isArray(s.key)?s.key.map(y=>({keyCode:y.keyCode,ctrlKey:!!y.ctrlKey,shiftKey:!!y.shiftKey,altKey:!!y.altKey,metaKey:!!y.metaKey})):s.key?{keyCode:s.key.keyCode,ctrlKey:!!s.key.ctrlKey,shiftKey:!!s.key.shiftKey,altKey:!!s.key.altKey,metaKey:!!s.key.metaKey}:null}))}),a},C=async e=>{const n=l.value.findIndex(t=>t.id===e);n!==-1&&(l.value.splice(n,1),V.unregister(e),r.value.hasChanges=!0)},i=()=>{try{const e=V.getRegisteredShortcuts(),n=V.getSuspendedKeys();l.value=e.filter(t=>!!(t.info&&typeof t.info.label=="string"&&typeof t.info.command=="string"&&typeof t.info.category=="string"&&typeof t.info.description=="string")).map(t=>{p.value.set(t.id,{key:t.key,winKey:t.winKey,when:t.when,action:t.action,isNoPreventDefault:t.isNoPreventDefault,info:t.info});const a=n.includes(t.id);return{...t,isSuspended:a}});for(const t of l.value)t.isSuspended&&V.suspendKey(t.id);l.value.forEach(t=>{f(t.id)})}catch(e){console.error(o("760")+":",e),r.value.lastError=e}},u=async(e,n)=>{try{r.value.isLoading=!0;const t=l.value.findIndex(A=>A.id===e);if(t===-1)return;const a=p.value.get(e);if(!a)return;const{skipConflictCheck:s,...y}=n;if(!s&&y.key){if(Array.isArray(y.key)){if(y.key.length===0)throw new Error(o("761"));y.key.forEach(($,g)=>{if(!$.keyCode)throw new Error(`${o("762")}${o("763")}${o("764")} ${g+1} ${o("207")}${o("765")}`)})}else if(!y.key.keyCode)throw new Error(o("766"));const A=d(y.key,e);if(A.length>0){const $=A.map(g=>({label:g.info.label,command:g.info.command,key:Array.isArray(g.key)?g.key[0]:g.key}));throw console.warn("Shortcut conflict detected:",{newKey:y.key,conflicts:$}),new Error(`${o("767")}：${o("437")}${o("395")} "${A.map(g=>g.info.label).join('", "')}" ${o("768")}`)}}const k={...l.value[t],...y,action:a.action,when:a.when};l.value[t]=k,"isSuspended"in y?y.isSuspended?(V.unregister(e),V.suspendKey(e),console.debug("Suspended shortcut:",e)):(V.resumeKey(e),V.registerWithId(e,{key:k.key,winKey:k.winKey,when:a.when,action:a.action,isNoPreventDefault:k.isNoPreventDefault,info:a.info}),console.debug("Resumed and re-registered shortcut with original ID:",e)):k.isSuspended||await V.updateShortcut(e,{key:k.key,winKey:k.winKey,when:a.when,action:a.action,isNoPreventDefault:k.isNoPreventDefault,info:a.info}),r.value.hasChanges=!0,console.debug("Shortcut updated:",{id:e,isSuspended:k.isSuspended,key:k.key})}catch(t){throw r.value.lastError=t,console.error(o("769")+":",t),t}finally{r.value.isLoading=!1}},w=async()=>{try{for(const e of l.value)V.unregister(e.id);V.resumeAllKeys(),l.value=[];for(const[e,n]of p.value.entries())V.registerWithId(e,{key:n.key,winKey:n.winKey,when:n.when,action:n.action,isNoPreventDefault:n.isNoPreventDefault,info:n.info}),l.value.push({id:e,key:n.key,winKey:n.winKey,when:n.when,action:n.action,isNoPreventDefault:n.isNoPreventDefault,info:n.info,isSuspended:!1});localStorage.removeItem("mxcad-shortcuts"),localStorage.removeItem("mxcad-suspended-keys"),await i(),r.value.hasChanges=!0,console.debug("Reset to default completed, shortcuts count:",l.value.length)}catch(e){throw console.error(o("770")+":",e),e}},x=async e=>{try{r.value.isLoading=!0;const n=Math.max(...l.value.map(s=>s.id),0)+1,t={id:n,key:e.key,winKey:e.winKey,info:e.info,action:s=>{e.info.command&&De(e.info.command)},isSuspended:!1},a=d(e.key);if(a.length>0)throw new Error(`${o("767")}：${o("437")}${o("395")} "${a.map(s=>s.info.label).join('", "')}" ${o("768")}`);return V.registerWithId(n,{key:e.key,winKey:e.winKey,action:t.action,info:e.info}),l.value.push(t),p.value.set(n,{key:e.key,winKey:e.winKey,action:t.action,info:e.info}),r.value.hasChanges=!0,n}catch(n){throw r.value.lastError=n,console.error(o("771")+":",n),n}finally{r.value.isLoading=!1}},c=async e=>{const n=l.value.findIndex(t=>t.id===e);n!==-1&&(l.value[n]={...l.value[n],key:[],winKey:void 0},V.unregister(e),r.value.hasChanges=!0)};return i(),{shortcuts:l,operationState:r,updateShortcut:u,resetToDefault:w,validateShortcutState:f,checkConflict:d,removeShortcut:C,addShortcut:x,emptiedShortcut:c}},We=l=>{const p=T(""),r=T("asc"),f=T("label"),d=(e,n)=>{if(!n)return!0;const t=n.toLowerCase().trim();if(!t)return!0;const a=Array.isArray(e.key)?e.key.map(s=>z(s).toLowerCase()):e.key?[z(e.key).toLowerCase()]:[];return e.info.label.toLowerCase().includes(t)||e.info.command.toLowerCase().includes(t)||(e.info.description?.toLowerCase()||"").includes(t)||a.some(s=>s.includes(t))},C=O(()=>{let e=[...l.value];return p.value&&(e=e.filter(n=>d(n,p.value))),e.sort((n,t)=>{const a=c(n,f.value),s=c(t,f.value),y=a.localeCompare(s);return r.value==="asc"?y:-y}),e}),i=O(()=>{const e=l.value.length,n=C.value.length;return{total:e,filtered:n,hasFilter:p.value!==""}}),u=e=>{f.value===e?r.value=r.value==="asc"?"desc":"asc":(f.value=e,r.value="asc")},w=()=>{p.value="",r.value="asc",f.value="label"},x=()=>{p.value=""},c=(e,n)=>{switch(n){case"label":return e.info.label.toLowerCase();case"command":return e.info.command.toLowerCase();case"category":return e.info.category.toLowerCase();default:return""}};return{searchText:p,sortOrder:r,sortBy:f,filteredShortcuts:C,searchStats:i,resetAll:w,resetFilter:x,toggleSort:u}},qe=(l,p)=>{const r=T(null),f=T(!1);let d=null,C=!1;const i=async x=>{if(!(!r.value||!f.value||C))try{if(C=!0,x.preventDefault(),x.stopPropagation(),x.stopImmediatePropagation(),["Control","Shift","Alt","Meta","Escape"].includes(x.key)){x.key==="Escape"&&w();return}const c={keyCode:x.code,ctrlKey:x.ctrlKey,shiftKey:x.shiftKey,altKey:x.altKey,metaKey:x.metaKey},e=p(c,r.value.id);if(e.length>0){const n=ae(),t=e.map(a=>`"${a.info.label}"`).join("、");await n.open({title:o("767"),text:`${o("772")} "${z(c)}" ${o("773")}：
${t}`,defineTitle:o("229")});return}await l(r.value.id,{key:c,winKey:void 0}),w()}catch(c){console.error(o("769")+":",c),await ae().open({title:o("774"),text:o("769"),defineTitle:o("229")})}finally{C=!1}},u=x=>{d&&(d(),d=null),r.value=x,f.value=!0,C=!1;const c=n=>{f.value&&(n.preventDefault(),n.stopPropagation(),i(n))};document.addEventListener("keydown",c,!0);const e=n=>{const a=n.target.closest(".shortcut-input");a&&a.classList.contains("editing")?(n.preventDefault(),n.stopPropagation()):w()};document.addEventListener("click",e,!0),d=()=>{document.removeEventListener("keydown",c,!0),document.removeEventListener("click",e,!0),C=!1}},w=()=>{d&&(d(),d=null),r.value=null,f.value=!1,C=!1,setTimeout(()=>{C=!1},100)};return{editingShortcut:r,isEditing:f,handleKeyPress:i,startEdit:u,cancelEdit:w}},se=[{id:"all",name:o("265"),icon:"$mdi-folder-multiple-outline",description:o("747")},{id:"common",name:o("748"),icon:"$mdi-star-outline",description:o("749")},{id:"draw",name:o("750"),icon:"$mdi-pencil-outline",description:o("751")},{id:"edit",name:o("752"),icon:"$mdi-pencil-ruler",description:o("753")},{id:"view",name:o("754"),icon:"$mdi-eye-outline",description:o("755")},{id:"dim",name:o("756"),icon:"$mdi-ruler",description:o("757")},{id:"custom",name:o("758"),icon:"$mdi-cog-outline",description:o("759")}],He=l=>{const p=T("all"),r=T(se),f=O(()=>{const c=new Map;return r.value.forEach(e=>{c.set(e.id,0)}),l.value.forEach(e=>{const n=e.info.category;c.set(n,(c.get(n)||0)+1)}),c.set("all",l.value.length),c}),d=c=>{c!=="all"&&f.value.get(c)===0||(p.value=c)},C=c=>r.value.find(e=>e.id===c),i=()=>{p.value="all"},u=c=>C(c)?.icon||se[0].icon,w=c=>C(c)?.name||c,x=c=>p.value==="all"?c:c.filter(e=>e.info.category===p.value);return{categories:r.value,currentCategory:p,categoryCounts:f,switchCategory:d,getCategoryInfo:C,getCategoryIcon:u,getCategoryName:w,filterByCategory:x,resetCategory:i}};function Je(l,p,r=!1){let f=null,d=!0;return{exec:function(...u){const w=this;if(r&&d){l.apply(w,u),d=!1;return}f&&clearTimeout(f),f=setTimeout(()=>{l.apply(w,u),f=null,r&&(d=!0)},p)},cancel:()=>{f&&(clearTimeout(f),f=null),r&&(d=!0)}}}const Qe=(l,p)=>{const r=T(!1),f=T(!1),d=T({lastSyncTime:null,isSyncing:!1,syncError:null}),C=new Map,i=()=>{l.value.forEach(t=>{C.set(t.id,{when:t.when,action:t.action,isNoPreventDefault:t.isNoPreventDefault})})},{exec:u,cancel:w}=Je(async()=>{try{d.value.isSyncing=!0,await e(),d.value.lastSyncTime=new Date}catch(t){d.value.syncError=t,console.error(o("779")+":",t)}finally{d.value.isSyncing=!1}},1e3),x=async()=>{try{f.value=!0;const t={version:"1.0.0",shortcuts:l.value.map(k=>({id:k.id,key:k.key,winKey:k.winKey,info:{label:k.info.label,command:k.info.command,category:k.info.category,description:k.info.description},isSuspended:!!k.isSuspended}))},a=new Blob([JSON.stringify(t,null,2)],{type:"application/json"}),s=URL.createObjectURL(a),y=document.createElement("a");y.href=s,y.download=`shortcuts-${new Date().toISOString().slice(0,10)}.json`,document.body.appendChild(y),y.click(),document.body.removeChild(y),URL.revokeObjectURL(s)}catch(t){console.error(o("780")+":",t),ae().open({title:o("774"),text:o("780"),defineTitle:o("229")})}finally{f.value=!1}},c=async()=>{try{r.value=!0;const t=document.createElement("input");t.type="file",t.accept=".json";const s=await(await new Promise((A,$)=>{t.onchange=g=>{const I=g.target.files;I&&I.length>0?A(I[0]):$(new Error("未选择文件"))},t.click()})).text(),y=JSON.parse(s);if(!y.version||!Array.isArray(y.shortcuts))throw new Error("无效的配置文件格式");for(const A of y.shortcuts)if(l.value.find(g=>g.id===A.id)){const g=C.get(A.id);g&&await p(A.id,{key:A.key,winKey:A.winKey,isSuspended:A.isSuspended,...g,skipConflictCheck:!0})}j().success("快捷键配置导入成功")}catch(t){console.error("导入快捷键配置失败:",t),j().error("导入快捷键配置失败")}finally{r.value=!1}},e=async()=>{try{d.value.isSyncing=!0;const t={version:"1.0.0",shortcuts:l.value.map(a=>({id:a.id,key:a.key,winKey:a.winKey,info:a.info,isSuspended:!!a.isSuspended}))};localStorage.setItem("mxcad-shortcuts",JSON.stringify(t));for(const a of l.value)a.isSuspended?await V.suspendKey(a.id):await V.resumeKey(a.id);for(const a of l.value){const s={key:a.key,winKey:a.winKey,when:a.when,action:a.action,isNoPreventDefault:a.isNoPreventDefault,info:a.info};await V.updateShortcut(a.id,s)}d.value.lastSyncTime=new Date,d.value.syncError=null}catch(t){throw d.value.syncError=t,console.error("保存快捷键配置失败:",t),j().error("保存快捷键配置失败"),t}finally{d.value.isSyncing=!1}},n=async()=>{try{const t=localStorage.getItem("mxcad-shortcuts");if(!t)return;const a=JSON.parse(t);for(const s of a.shortcuts)if(l.value.find(k=>k.id===s.id)){const k=C.get(s.id);k&&await p(s.id,{key:s.key,winKey:s.winKey,isSuspended:s.isSuspended,...k,skipConflictCheck:!0})}}catch(t){throw console.error("加载快捷键配置失败:",t),t}};return oe(l,()=>{},{deep:!0}),ue(async()=>{i(),await n()}),de(()=>{w()}),{syncState:d,exportToFile:x,importFromFile:c,saveToLocalStorage:e,loadFromLocalStorage:n,cancelAutoSave:w}},Ge=(l,p)=>Qe(l,p),Xe=(l,p)=>({suspendShortcut:async i=>{if(l.value.find(w=>w.id===i))try{await p(i,{isSuspended:!0}),V.suspendKey(i)}catch(w){throw console.error(o("775")+":",w),w}},resumeShortcut:async i=>{if(l.value.find(w=>w.id===i))try{await p(i,{isSuspended:!1}),V.resumeKey(i)}catch(w){throw console.error(o("776")+":",w),w}},suspendAllShortcuts:async()=>{try{for(const i of l.value)await p(i.id,{isSuspended:!0});V.suspendAllKeys()}catch(i){throw console.error(o("777")+":",i),i}},resumeAllShortcuts:async()=>{try{for(const i of l.value)await p(i.id,{isSuspended:!1});V.resumeAllKeys()}catch(i){throw console.error(o("778")+":",i),i}}}),Ye={class:"pa-4"},Ze=ye({__name:"AddShortcutDialog",props:{checkConflict:{type:Function}},emits:["save"],setup(l,{expose:p,emit:r}){const{isShow:f,showDialog:d}=fe(!1,"MxCAD_AddShortcut"),C=T(!1),i=T({label:"",command:"",key:""}),u=T({label:"",command:void 0,category:"custom",description:"",key:null}),w={label:[h=>!!h||o("命令名称不能为空")],command:[h=>!!h||o("命令不能为空")],key:[h=>!!h||o("快捷键不能为空")]},x=T(),c=T([]),e=T(!1),n=h=>{if(!h){c.value=[];return}const b=h.toLowerCase();c.value=Te.value.filter(S=>S.toLowerCase().includes(b)),e.value=!0},t=h=>{u.value.command=h,u.value.label||(u.value.label=h),e.value=!1},a=T(!1),s=O(()=>u.value.key?z(u.value.key):o("点击设置快捷键")),y=l,k=async h=>{if(!a.value)return;if(h.preventDefault(),h.stopPropagation(),["Control","Shift","Alt","Meta","Escape"].includes(h.key)){h.key==="Escape"&&(a.value=!1,document.removeEventListener("keydown",k,!0));return}const b={keyCode:h.code,ctrlKey:h.ctrlKey,shiftKey:h.shiftKey,altKey:h.altKey,metaKey:h.metaKey},S=y.checkConflict(b);S.length>0?(j().warning(`${o("快捷键冲突")}：${o("与")}${o("命令")} "${S.map(R=>R.info.label).join('", "')}" ${o("冲突")}`),u.value.key=null):u.value.key=b,a.value=!1,document.removeEventListener("keydown",k,!0)},A=()=>{if(i.value={label:"",command:"",key:""},!u.value.label){i.value.label=o("命令名称不能为空");return}if(!u.value.command){i.value.command=o("命令不能为空");return}if(!u.value.key){i.value.key=o("快捷键不能为空");return}N("save",{key:u.value.key,info:{label:u.value.label,command:u.value.command,category:u.value.category,description:u.value.description}}),g(),d(!1)},$=()=>{a.value=!0,document.addEventListener("keydown",k,!0)},g=()=>{a.value=!1,document.removeEventListener("keydown",k,!0),u.value={label:"",command:"",category:"custom",description:"",key:null},i.value={label:"",command:"",key:""},c.value=[],e.value=!1,C.value=!1},I=[{name:o("确定"),fun:A,primary:!0},{name:o("取消"),fun:()=>{g(),d(!1)}}],N=r;return p({showDialog:d}),(h,b)=>(B(),q(pe,{title:h.t("800"),modelValue:K(f),"onUpdate:modelValue":b[5]||(b[5]=S=>ne(f)?f.value=S:null),maxWidth:"600",footerBtnList:I},{default:P(()=>[D("div",Ye,[L(Ne,{onSubmit:Q(A,["prevent"])},{default:P(()=>[L(G,{modelValue:u.value.label,"onUpdate:modelValue":b[0]||(b[0]=S=>u.value.label=S),rules:w.label,placeholder:h.t("789"),required:"",class:"mb-3","error-messages":i.value.label},null,8,["modelValue","rules","placeholder","error-messages"]),L(Ie,{modelValue:u.value.command,"onUpdate:modelValue":[b[1]||(b[1]=S=>u.value.command=S),t],rules:w.command,placeholder:h.t("395"),required:"",class:"mb-3",ref_key:"commandInput",ref:x,items:c.value,"menu-props":{modelValue:e.value,maxHeight:200},"onUpdate:search":n,"error-messages":i.value.command},{item:P(({item:S,props:X})=>[L(ve,H(X,{title:S.raw,onClick:R=>t(S.raw)}),{append:P(R=>b[6]||(b[6]=[])),_:2},1040,["title","onClick"]),b[7]||(b[7]=M("/> "))]),_:1},8,["modelValue","rules","placeholder","items","menu-props","error-messages"]),L(Pe,{modelValue:u.value.category,"onUpdate:modelValue":b[2]||(b[2]=S=>u.value.category=S),items:K(se).filter(S=>S.id!=="all"),"item-title":"name","item-value":"id",required:"",class:"mb-3"},null,8,["modelValue","items"]),L(_e,{modelValue:u.value.description,"onUpdate:modelValue":b[3]||(b[3]=S=>u.value.description=S),label:h.t("790"),rows:"3",class:"mb-3"},null,8,["modelValue","label"]),L(G,{"model-value":s.value,onClick:$,rules:w.key,class:J({editing:a.value}),placeholder:a.value?h.t("791"):h.t("799"),"error-messages":i.value.key,readonly:"",clearable:"","clear-icon":"$mdi-recovery","onClick:clear":b[4]||(b[4]=Q(S=>u.value.key=null,["stop"]))},null,8,["model-value","rules","class","placeholder","error-messages"])]),_:1})])]),_:1},8,["title","modelValue"]))}}),et=me(Ze,[["__scopeId","data-v-74196422"]]),tt={class:"shortcut-dialog pa-4"},ot={class:"d-flex align-center mb-4"},nt={class:"d-flex"},at={class:"d-flex align-center justify-center"},st={class:"d-flex align-center justify-center"},lt={class:"text-center"},rt={class:"text-center"},it={class:"text-center",style:{width:"86px"}},ct={class:"text-center text-no-wrap"},ut={class:"text-center text-no-wrap text-medium-emphasis"},dt={class:"text-center"},yt={class:"d-flex align-center"},ft={class:"text-center text-caption text-medium-emphasis description-cell"},mt={class:"text-center"},pt={class:"d-flex justify-center"},vt=ye({__name:"index",setup(l){const{isShow:p,showDialog:r}=fe(!1,"MxCAD_Shortcut"),{shortcuts:f,updateShortcut:d,resetToDefault:C,checkConflict:i,removeShortcut:u,addShortcut:w,emptiedShortcut:x}=Re(),{categories:c,currentCategory:e,categoryCounts:n,switchCategory:t,getCategoryIcon:a,getCategoryName:s,filterByCategory:y,resetCategory:k}=He(f),{searchText:A,sortOrder:$,sortBy:g,filteredShortcuts:I,searchStats:N,toggleSort:h,resetAll:b}=We(f),{editingShortcut:S,isEditing:X,handleKeyPress:R,startEdit:Y,cancelEdit:Z}=qe(d,i),{syncState:gt,exportToFile:ge,importFromFile:he,saveToLocalStorage:ke,loadFromLocalStorage:we}=Ge(f,d),{suspendShortcut:Se,resumeShortcut:Ke,suspendAllShortcuts:ht,resumeAllShortcuts:kt}=Xe(f,d),Ce=O(()=>y(I.value));oe(p,async m=>{if(!m)try{Z()}catch(E){console.error("关闭对话框清理失败:",E)}},{immediate:!0});const be=m=>{m.isSuspended?Ke(m.id):Se(m.id)},xe=()=>{C(),b(),k()},le=T(!1),$e=[{name:o("确定"),fun:async()=>{try{await ke(),le.value=!le.value,r(!1)}catch(m){console.error("保存快捷键配置失败:",m)}},primary:!0},{name:o("恢复系统默认"),fun:xe},{name:o("导出配置"),fun:ge},{name:o("导入配置"),fun:he},{name:o("取消"),fun:async()=>{try{await we(),r(!1)}catch(m){console.error("加载快捷键配置失败:",m)}}}];oe(S,m=>{m&&Ae(()=>{const E=document.querySelector(".shortcut-input.editing input");E instanceof HTMLElement&&E.focus()})}),ue(()=>{document.addEventListener("click",m=>{S.value&&!m.target.closest(".shortcut-input")&&Z()})}),de(()=>{try{Z()}catch(m){console.error("组件卸载清理失败:",m)}});const Ee=m=>m.key?Array.isArray(m.key)?m.key.length>0?z(m.key[0]):"":z(m.key):"",re=T(null),Le=async m=>{try{await w(m),j().success(o("添加快捷键成功"))}catch(E){console.error("添加快捷键失败:",E),j().error(E instanceof Error?E.message:o("添加快捷键失败"))}};return(m,E)=>(B(),W(ee,null,[L(pe,{title:m.t("785"),modelValue:K(p),"onUpdate:modelValue":E[4]||(E[4]=v=>ne(p)?p.value=v:null),maxWidth:"1000",footerBtnList:$e},{default:P(()=>[D("div",tt,[D("div",ot,[L(G,{modelValue:K(A),"onUpdate:modelValue":E[0]||(E[0]=v=>ne(A)?A.value=v:null),placeholder:m.t("786"),"prepend-inner-icon":"$mdi-magnify",clearable:"",class:"flex-grow-1"},null,8,["modelValue","placeholder"]),L(Fe,{color:"primary",class:"ml-4",onClick:E[1]||(E[1]=v=>re.value?.showDialog(!0))},{default:P(()=>[L(U,null,{default:P(()=>E[5]||(E[5]=[M("$mdi-plus")])),_:1}),M(" "+_(m.t("787")),1)]),_:1})]),D("div",nt,[L(Be,{density:"compact",nav:"",rounded:"lg",width:240,class:"mr-4"},{default:P(()=>[L(Me,null,{default:P(()=>[M(_(m.t("788")),1)]),_:1}),(B(!0),W(ee,null,ie(K(c),v=>(B(),q(ve,{key:v.id,active:K(e)===v.id,onClick:F=>K(t)(v.id),class:J({"selected-category":K(e)===v.id}),rounded:"lg"},{prepend:P(()=>[L(U,{icon:K(a)(v.id),size:"small",color:K(e)===v.id?"primary":""},null,8,["icon","color"])]),default:P(()=>[L(Ue,{class:"d-flex align-center"},{default:P(()=>[M(_(m.t(K(s)(v.id)))+" ",1),L(je,{size:"x-small",class:"ml-auto",color:K(e)===v.id?"primary":"",variant:"flat"},{default:P(()=>[M(_(K(n).get(v.id)),1)]),_:2},1032,["color"])]),_:2},1024)]),_:2},1032,["active","onClick","class"]))),128))]),_:1}),L(ze,{class:"w-100",height:"300px"},{default:P(()=>[D("thead",null,[D("tr",null,[D("th",{class:"text-center sortable",onClick:E[2]||(E[2]=v=>K(h)("label"))},[D("div",at,[M(_(m.t("789"))+" ",1),K(g)==="label"?(B(),q(U,{key:0,size:"small",class:"ml-1",icon:K($)==="asc"?"$mdi-arrow-up":"$mdi-arrow-down",color:"primary"},null,8,["icon"])):ce("",!0)])]),D("th",{class:"text-center sortable",onClick:E[3]||(E[3]=v=>K(h)("command"))},[D("div",st,[M(_(m.t("395"))+" ",1),K(g)==="command"?(B(),q(U,{key:0,size:"small",class:"ml-1",icon:K($)==="asc"?"$mdi-arrow-up":"$mdi-arrow-down"},null,8,["icon"])):ce("",!0)])]),D("th",lt,_(m.t("772")),1),D("th",rt,_(m.t("790")),1),D("th",it,_(m.t("612")),1)])]),D("tbody",null,[(B(!0),W(ee,null,ie(Ce.value,v=>(B(),W("tr",{key:v.id,class:J({suspended:v.isSuspended})},[D("td",ct,_(m.t(v.info.label)),1),D("td",ut,_(v.info.command),1),D("td",dt,[D("div",yt,[L(G,{"model-value":Ee(v),class:J(["shortcut-input mx-auto",{editing:K(S)===v,disabled:v.isSuspended}]),onClick:Q(F=>K(Y)(v),["stop"]),onFocus:F=>K(Y)(v),readonly:!0,placeholder:K(S)===v?m.t("791"):"",clearable:"","clear-icon":"$mdi-recovery","onClick:clear":Q(()=>{K(x)(v.id),K(Y)(v)},["stop"])},null,8,["model-value","onClick","onFocus","class","placeholder","onClick:clear"])])]),D("td",ft,[L(te,{text:v.info.description,location:"top","max-width":"300","open-delay":500},{activator:P(({props:F})=>[D("div",H({ref_for:!0},F,{class:"description-text"}),_(v.info.description),17)]),_:2},1032,["text"])]),D("td",mt,[D("div",pt,[L(te,{text:v.isSuspended?m.t("792"):m.t("793"),location:"top","open-delay":500},{activator:P(({props:F})=>[L(U,H({ref_for:!0},F,{icon:v.isSuspended?"$mdi-play":"$mdi-pause",size:"x-small",class:"v-icon--clickable mr-2",color:v.isSuspended?"warning":"",onClick:Ve=>be(v)}),{default:P(()=>E[6]||(E[6]=[])),_:2},1040,["icon","color","onClick"])]),_:2},1032,["text"]),L(te,{text:m.t("794"),location:"top","open-delay":500},{activator:P(({props:F})=>[L(U,H({ref_for:!0},F,{icon:"$mdi-close",size:"x-small",class:"v-icon--clickable",onClick:Ve=>K(u)(v.id)}),null,16,["onClick"])]),_:2},1032,["text"])])])],2))),128))])]),_:1})])])]),_:1},8,["title","modelValue"]),L(et,{ref_key:"addShortcutDialog",ref:re,onSave:Le,checkConflict:K(i)},null,8,["checkConflict"])],64))}}),Lt=me(vt,[["__scopeId","data-v-7529bc77"]]);export{Lt as default};
