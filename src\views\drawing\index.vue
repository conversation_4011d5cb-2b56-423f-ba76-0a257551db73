<template>

<div style="display: flex;padding-left: 20px;height: 90vh">
  <div style="border: 0.5px solid #409eff;margin-right: 10px;width: 15%">
      
      <div class="mb8" style="display: flex;margin-top: 20px;padding-left: 10px;">
      
       <div style="flex: 1; margin-left: 10px;">
         <span style="">图纸类型</span>
       </div>
       
       
       <div style="flex: 1; margin-right: 10px;">
        <el-button type="primary" size="small" :icon="Plus" circle @click="drawTypeAdd"/>
         <el-button type="primary" size="small" :icon="Edit" circle @click="drawTypeEdit"/>
         <el-button type="primary" size="small" :icon="Delete" circle @click="drawTypeDel"/>
       </div>
      </div>
       
       <el-tree style="height:calc(100vh - 185px);overflow-y:scroll"
       :data="treeData" :props="defaultProps" :expand-on-click-node="false"
       :highlight-current="true"
       @node-click="handleNodeClick" />
     </div>

  <div style="width: 85%;border: 0.5px solid #409eff;" >
    <div class="app-container" >
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
        <el-form-item label="图纸名称" prop="drawingname">
          <el-input
            v-model="queryParams.drawingname"
            placeholder="请输入图纸名称"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>


        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="Plus"
            size="small"
            @click="handleAdd"

          >新增</el-button>
        </el-col>
        <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          size="small"
          @click="handleDelBatch()"
        >批量删除</el-button>
      </el-col>
      </el-row>

      <el-table style="height:calc(100vh - 280px);overflow-y:scroll" v-loading="loading" :data="drawingList" highlight-current-row
      @selection-change="handleSelectionChange"
      @row-click="showDrawDwg">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="图纸名称" align="center" prop="drawingname" />
        <!-- <el-table-column label="图纸类别KEY" align="center" prop="drawingtypekey" /> -->
        <el-table-column label="图纸编码" align="center" prop="drawingcode" />
        <!-- <el-table-column label="图纸路径" align="center" prop="drawingpath" /> -->
        <el-table-column label="快名称" align="center" prop="blockname" />
        <el-table-column label="图纸类别" align="center" prop="assessmenttype" />
        <el-table-column label="图号" align="center" prop="drawingno" />

        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button link type="primary" icon="Edit" @click.stop="handleUpdate(scope.row)" >修改</el-button>
            <el-button link type="primary" icon="Delete" @click.stop="handleDelete(scope.row)" >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <pagination
        v-show="total>0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />

      <!-- 添加或修改图纸对话框 -->
      <el-dialog :title="title" v-model="open" width="500px" append-to-body>
        <el-form ref="drawingRef" :model="form" :rules="rules" label-width="120px">
          <el-form-item label="图纸名称" prop="drawingname">
            <el-input v-model="form.drawingname" placeholder="请输入图纸名称" />
          </el-form-item>
          <!-- <el-form-item label="图纸类别KEY" prop="drawingTypeKey">
            <el-input v-model="form.drawingTypeKey" placeholder="请输入图纸类别KEY" />
          </el-form-item> -->
          <el-form-item label="图纸编码" prop="drawingcode">
            <el-input v-model="form.drawingcode" placeholder="请输入图纸编码" />
          </el-form-item>
          <el-form-item label="快名称" prop="blockname">
            <el-input v-model="form.blockname" placeholder="请输入快名称" />
          </el-form-item>
          <el-form-item label="图号" prop="drawingno">
            <el-input v-model="form.drawingno" placeholder="请输入图号" />
          </el-form-item>
           

        <el-form-item label="文件" >
            <el-upload
              accept=".dwg"
              ref="uploadRef"
              class="upload-demo"
              :limit="1"
              :auto-upload="false"
              :on-change="handleChange"
              :file-list="fileList"
              :on-remove="handleRemove"
            >
              <template #trigger>
                <el-button type="primary">选择文件</el-button>
              </template>
            </el-upload>
          </el-form-item>

        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
          </div>
        </template>
      </el-dialog>

      <!-- 添加或修改图纸类型对话框 -->
    <el-dialog :title="title" v-model="openType" width="500px" append-to-body>
      <el-form ref="drawingtypeRef" :model="formType" :rules="rules" label-width="120px">
        <el-form-item label="图纸类别名称" prop="drawingTypeName">
          <el-input v-model="formType.drawingTypeName" placeholder="请输入图纸类别名称" />
        </el-form-item>
        <el-form-item label="图纸类别" prop="drawingTypeKey">
          <el-input v-model="formType.drawingTypeKey" placeholder="请输入图纸类别key" /> 
        </el-form-item>
        <el-form-item label="父级" prop="parentKey">
          <!-- <el-input v-model="formType.parentKey" placeholder="请输入父级key" /> -->
          <el-input v-model="formType.parentName" placeholder="请输入图纸类别key" />
        </el-form-item>
        <el-form-item label="可用标志" prop="drawingTypeState">
          <el-input v-model="formType.state" placeholder="请输入可用标志" />
        </el-form-item>
        <el-form-item label="分册标志" prop="drawingTypeBook">
          <el-input v-model="formType.book" placeholder="请输入分册标志" />
        </el-form-item>

        
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFormType">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    </div>
  </div>

  <!-- <el-drawer v-model="drawer" :with-header="false">
    <iframe
            id="ifr"
            :src="browseUrl"
            width="100%"
            height="100%"
            frameborder="0"
            scrolling="yes"
            style="border: 1px solid lightgray"
        ></iframe>
  </el-drawer> -->
  <el-drawer v-model="drawer" :with-header="false">
    <div style="height: 450px; overflow: hidden;"> <canvas id="myCanvas"></canvas></div>
  </el-drawer>
</div>
  
</template>

<script setup name="Drawing">
import { listDrawing, getDrawing, delDrawing, addDrawing, updateDrawing, delBatch, downloadFile } from "@/api/drawing/drawing.js";
import { Delete, Edit, Search, Share, Upload, Plus } from '@element-plus/icons-vue'
import { listTree, getDrawingtype, delDrawingtype, addDrawingtype, updateDrawingtype } from "@/api/drawing/drawingtype.js";
import {  McObject } from "mxcad"
const mxcad = new McObject()
const { proxy } = getCurrentInstance();
const drawer = ref(false)
const browseUrl = ref('')
const drawingList = ref([]);
const open = ref(false);
const openType = ref(false);

const loading = ref(false);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const titleType = ref("");

let fileList = ref([]); // 文件列表，显示已选择的文件
const uploadRef = ref();

const data = reactive({
  form: {
    drawingid: null,
    drawingname: null,
    drawingtypekey: null,
    drawingcode: null,
    drawingpath: null,
    blockname: null,
    assessmenttype: null,
    drawingno: null,
    operatetime: null
  },
  formType: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    drawingname: null,
    drawingtypekey: null,
    drawingcode: null,
    drawingpath: null,
    blockname: null,
    assessmenttype: null,
    drawingno: null,
    operatetime: null
  },
  rules: {
  }
});

const { queryParams, form, rules, formType } = toRefs(data);

/** 查询图纸列表 */
function getList() {
  loading.value = true;
  listDrawing(queryParams.value).then(response => {
    drawingList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  openType.value = false;
  reset();
  resetType();
}

// 表单重置
function reset() {
  form.value = {
    drawingid: null,
    drawingname: null,
    drawingtypekey: null,
    drawingcode: null,
    drawingpath: null,
    blockname: null,
    assessmenttype: null,
    drawingno: null,
    operatetime: null
  };
  proxy.resetForm("drawingRef");
}

// 表单重置
function resetType() {
  formType.value = {
    drawingTypeId: null,
    drawingTypeName: null,
    drawingTypeKey: null,
    parentKey: null,
    state: null,
    book: null,
    baseVersionId: null,
    parentName: null
  };
  proxy.resetForm("drawingtypeRef");
}
/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.drawingid);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

function showDrawDwg(row, column, event){
  browseUrl.value = '';
  drawer.value = true;
  let blob;
  downloadFile({ fileId: row.drawingid }).then(res => {
  mxcad.create({
    // canvas元素的id
    canvas: "#myCanvas",
    // 获取加载wasm相关文件(wasm/js/worker.js)路径位置
    
    locateFile: (fileName)=> {
      return new URL(`/node_modules/mxcad/dist/wasm/2d/${fileName}`, import.meta.url).href
    },
    
    // 需要初始化打开的文件url路径
    fileUrl: window.URL.createObjectURL(res),
    // 提供加载字体的目录路径
    fontspath:  new URL("/node_modules/mxcad/dist/fonts", import.meta.url).href,
  })
})
  

  // downloadFile({ fileId: row.id }).then(res => {

  //   let blob = new Blob([res], {
  //       type: "application/pdf;chartset=UTF-8"
  //     })
  //     // 然后使用window.URL.createObjectURL()方法将blob转成url地址赋值给iframe
  //     browseUrl.value = window.URL.createObjectURL(blob) + '#toolbar=0';

  //   // console.log(res)
  //   //   const openUrl = window.URL.createObjectURL(res)
  //   //   browseUrl.value = import.meta.env.VITE_PDF_URL + '?file=' + encodeURIComponent(openUrl)
  //   })
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  resetType();
  fileList = ref([]);
  open.value = true;
  title.value = "添加图纸";
  form.value.drawingtypekey = drawTypeKey;
  
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  resetType();
  const _id = row.drawingid || ids.value
  getDrawing(_id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改图纸";
    
    if(response.data.drawingpath != '' && response.data.drawingpath != null){
      fileList.value = [
      {
        name: response.data.drawingPath,
        url: '',
      }
    ]
    }
    
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["drawingRef"].validate(valid => {
    if (valid) {

      let formData = new FormData(); 
      for(let file of fileList.value){
        formData.append('fileList', file.raw);
          // if( (file.name != '' || file.name != null) && (file.url != ''|| file.url != null )){
          //   formData.append('fileList', file.raw);
          // }
        
      }
      for (const key in form.value) {
        console.log(key + "  " +form.value[key])
        if (form.value.hasOwnProperty(key)) {
          if(form.value[key] == null || key === "children"){
           
          }else{
            formData.append(key, form.value[key]);
          }
          
        }
      }
      
      console.log(fileList)
      if (form.value.drawingid != null) {
        updateDrawing(formData).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addDrawing(formData).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
     }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.drawingid || ids.value;
  proxy.$modal.confirm('是否确认删除？').then(function() {
    return delDrawing(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

//批量删除
const handleDelBatch = () => {
  if (ids.value.length === 0) {
    proxy.$message({
          type: 'warning',
          message: '请至少选择一个项目进行删除'
        });
        return;
      }
      proxy.$confirm("是否确认删除选中的数据项?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(function() {
          return delBatch(ids.value);
        })
        .then(() => {
          queryParams.pageNum = 1;
          getList();
          proxy.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/drawing/export', {
    ...queryParams.value
  }, `drawing_${new Date().getTime()}.xlsx`)
}


//左侧类型
const treeData = ref([]);
const dataList = () => {
  listTree().then(response =>{
    treeData.value = response.data;
  })
}
const defaultProps = {
  children: 'children',
  label: 'drawingTypeName',
}

//左侧点击事件
let drawTypeKey = ref("");
let drawTypeId = ref("");
let parentName = ref("");
const handleNodeClick = (data) => {
  console.log(data)
  drawTypeKey = data.drawingTypeKey
  queryParams.value.drawingtypekey = data.drawingTypeKey
  drawTypeId = data.drawingTypeId
  parentName = data.drawingTypeName;
  getList();
  
}

function handleChange(uploadFile,fileList2){
  fileList.value = fileList2
  console.log(fileList2)
  // fileList.value.push(uploadFile.raw) ;
}
const handleRemove = (file, fileList2) => {
  form.value.drawingpath = ''
  fileList.value = fileList2
  console.log('移除文件', file, fileList2); // 可以根据需要处理文件移除逻辑，例如更新 fileList 状态等。
};

function drawTypeAdd(){
  /** 新增按钮操作 */
  resetType();
  openType.value = true;
  titleType.value = "添加图纸类型";
  formType.value.parentKey = drawTypeKey;
  formType.value.parentName = parentName;
}

function drawTypeEdit(){
  /** 修改按钮操作 */
  resetType();
  
  getDrawingtype(drawTypeId).then(response => {
    formType.value = response.data;
    openType.value = true;
    titleType.value = "修改图纸类型";
    formType.value.parentName = parentName;
  });
  
}

/** 提交按钮 */
function submitFormType() {
  proxy.$refs["drawingtypeRef"].validate(valid => {
    if (valid) {
      if (formType.value.drawingTypeId != null) {
        updateDrawingtype(formType.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          openType.value = false;
          dataList();
        });
      } else {
        addDrawingtype(formType.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          openType.value = false;
          dataList();
        });
      }
    }
  });
}

function drawTypeDel(){
  if (drawTypeId.value == '') {
    proxy.$message({
          type: 'warning',
          message: '请选则要删除的图纸类型'
        });
        return;
      }
  proxy.$modal.confirm('是否确认删除？').then(function() {
    return delDrawingtype(drawTypeId);
  }).then(() => {
    dataList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

// getList();
// 页面加载时获取数据
onMounted(() => {
  dataList();
});

</script>
