import {
    McDbBlockReference,
    MxCADUiPrPoint,
    MxCpp,
    McDbAttributeDefinition,
    McDbAttribute,
    McGePoint3d,
    McDbText,
    McDb,
    McCmColor,
    McDbBlockTableRecord
} from "mxcad";
// // 绘制线路
export async function BlockDrawListFun(formData, fileName, scale, attribDefList, tableData) {
    console.log("🚀 ~ BlockDrawListFun ~ formData, fileName, scale, attribDefList, tableData:", formData, fileName, scale, attribDefList, tableData)
    let mxcad = MxCpp.App.getCurrentMxCAD();
    let blkFilePath = import.meta.url.substring(0, import.meta.url.lastIndexOf('/')) + fileName;
    let blkrecId = await mxcad.insertBlock(blkFilePath, formData.spanClass);
    if (!blkrecId.isValid()) {
        return { id: null, handle: null };
    }

    let blkRef = new McDbBlockReference();
    blkRef.blockTableRecordId = blkrecId;
    let box = blkRef.getBoundingBox();
    if (box.ret && formData.spanWidth) {
        let dLen = Math.abs(box.maxPt.x - box.minPt.x);
        blkRef.setScale(parseFloat(formData.spanWidth) / dLen);
    }

   /* let getPoint = new MxCADUiPrPoint();
    getPoint.setMessage("\指定插入基点");

    getPoint.setUserDraw((v, worldDraw) => {
        blkRef.position = v;
        worldDraw.drawMcDbEntity(blkRef);
    });

    let pt = await getPoint.go();*/
    let pt =  new McGePoint3d(0,0,0);
    if (!pt) return;

    let previousPosition = pt; // Save the position of the first block
    blkRef.position = pt;
    let newBlkRefId = mxcad.drawEntity(blkRef);
    if (!newBlkRefId.isValid) {
        console.log("insert error");
        return { id: null, handle: null };
    }
    blkRef = newBlkRefId.getMcDbEntity() as McDbBlockReference;
    const fileArr = []
    let drawingsCount = 0;
    let numPage = 1
    for (let i = 1; i < tableData.length; i++) {
        if (i % 23 === 0||numPage===1) { // 每23行插入一个新图纸
            // 复制上一张图纸的属性，创建新图纸
            let copiedBlkRef = blkRef.clone(); // 克隆当前图纸对象
          if(numPage===1){
            copiedBlkRef.position = new McGePoint3d(0,0,0); // 偏移量
          }else{
            copiedBlkRef.position = new McGePoint3d(previousPosition.x + 105, previousPosition.y, previousPosition.z); // 偏移量
          }  

            previousPosition = copiedBlkRef.position; // 更新上一张图纸的位置

            // 插入新图纸
            newBlkRefId = mxcad.drawEntity(copiedBlkRef);
            if (!newBlkRefId.isValid) {
                console.log("insert error");
                return { id: null, handle: null };
            }
            blkRef = newBlkRefId.getMcDbEntity() as McDbBlockReference;
            const newArr = attribDefList.map(item => {
                if (item.name === '当前页') {
                    item.value = numPage
                }
                return item
            })
            console.log(newArr, 'newArrnewArr')
            // 更新属性
            setAttribute(newBlkRefId, blkrecId, newArr, tableData);
            numPage++

            // 每次处理最多23行
            let pageCount = Math.ceil(tableData.length / 23);

            for (let pageIndex = 0; pageIndex < pageCount; pageIndex++) {
                // 获取当前页的数据（最多23行）
                let currentPageData = tableData.slice(pageIndex * 23, (pageIndex + 1) * 23);
                // 逐行处理当前页数据
                currentPageData.forEach((item, index) => {
                    textFun(mxcad, new McDbText(), new McGePoint3d(pt.x + 6.5 + (105 * pageIndex), pt.y - 50 - (index * 3.81), pt.z), index + 1);
                    textFun(mxcad, new McDbText(), new McGePoint3d(pt.x + 10.5 + (105 * pageIndex), pt.y - 50 - (index * 3.81), pt.z), item.zl);
                    textFun(mxcad, new McDbText(), new McGePoint3d(pt.x + 25.5 + (105 * pageIndex), pt.y - 50 - (index * 3.81), pt.z), item.th);
                    textFun(mxcad, new McDbText(), new McGePoint3d(pt.x + 55.5 + (105 * pageIndex), pt.y - 50 - (index * 3.81), pt.z), item.tm);
                    textFun(mxcad, new McDbText(), new McGePoint3d(pt.x + 85.5 + (105 * pageIndex), pt.y - 50 - (index * 3.81), pt.z), item.ty);
                });


            }
        }
        drawingsCount++;
    }

    const ent = newBlkRefId.getMcDbEntity();
    if (!ent) return { id: null, handle: null };
    const entId = ent.getObjectID();
    const sHandle = ent.getHandle();
    console.log("对象id", entId);
    console.log("对象句柄", sHandle);
    console.log("对象坐标", blkRef.position);
    MxCpp.getCurrentMxCAD().saveFile(void 0, (data) => {
        let blob: Blob;
        const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
        if (isSafari) {
            blob = new Blob([data.buffer], { type: "application/octet-stream" });
        } else {
            blob = new Blob([data.buffer], { type: "application/octet-binary" });
        }
        const file = new File([blob], '图纸目录' + '.mxweb', { type: isSafari ? "application/octet-stream" : "application/octet-binary" });
        fileArr.push(file)
    }, false, true);

    return { id: entId, handle: sHandle, pointsXYZ: blkRef.position, fileArr };
}

function textFun(mxcadBox,textG, position, text) {
    textG.textString = text // 使用传入的文本参数
    textG.position = position;
    textG.alignmentPoint = position;
    textG.height = 2; // 文本高度
    textG.widthFactor = 0.8; // 文本宽度缩放
    textG.horizontalMode = McDb.TextHorzMode.kTextCenter; // 水平对齐方式
    textG.verticalMode = McDb.TextVertMode.kTextBottom; // 垂直对齐方式
    textG.trueColor = new McCmColor(0, 0, 0)
    mxcadBox.drawEntity(textG);
}

function setAttribute(newBlkRefId, blkrecId, attribDefList, tableData){
    const blkRef = newBlkRefId.getMcDbEntity() as McDbBlockReference;
    // 如果块有属性定义，下面为块引创建属性定义。
    blkRef.disableDisplay(true);
    let blkRecord: any = blkrecId.getMcDbBlockTableRecord();
    let ids = blkRecord.getAllEntityId();
    ids.forEach((id: any, index: any) => {
        if (!id.isKindOf("McDbAttributeDefinition")) return;
        let attribDef = id.getMcDbEntity() as McDbAttributeDefinition;
        console.log("🚀 ~ ids.forEach ~ attribDef:", attribDef)
        let tag = attribDef.tag;
        let attrib = new McDbAttribute();
        attrib.position = attribDef.position;
        attrib.alignmentPoint = attribDef.alignmentPoint
        attrib.height = attribDef.height
        attrib.trueColor = attribDef.trueColor
        attrib.widthFactor = attribDef.widthFactor
        if (attribDefList.length > 0) {
            attribDefList.forEach(item => {
                if (item.name === tag) {
                    attrib.textString = item.value
                }
            })
        } else {
            attrib.textString = attribDef.textString
        }
        attrib.tag = tag;
        attrib.isInvisible = false;
        attrib.transformBy(blkRef.blockTransform);
        attrib = blkRef.appendAttribute(attrib).getMcDbEntity() as McDbAttribute;
        attrib.textStyle = attribDef.textStyle
        attrib.layer = attribDef.layer
    })
    blkRef.disableDisplay(false);
}


