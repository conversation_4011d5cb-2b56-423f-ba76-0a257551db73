import { MxCADPluginSampleCode } from "mxcad"
import "./list"
import "./group"
import listCmdCodeString from "./list?raw"
const filterCode = (code: string)=> {
  // 定义一个正则表达式来匹配指定格式的版权注释 通过开头和结尾固定的24个斜杠来判断版权注释
  const regex = /\/\/{24}[\s\S]*\/\/{24}/g;
  const result = code.replace(regex, '');
  return result
}

export const initCode = (codes: MxCADPluginSampleCode[])=> {
  codes.push({
    "type": "命令实现",
    "name": "list 命令",
    "code": filterCode(listCmdCodeString)
  })
}
