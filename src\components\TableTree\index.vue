<template>
  <el-input
    v-model="materialType"
    placeholder="请输入物料类型"
    clearable
    prefix-icon="Search"
    style="margin-bottom: 2px"
  />
  <el-aside style="height:calc(100% - 50px);width:100%;background: #fff;padding:0;">
    <el-tree
      style="treeD"
      :filter-node-method="filterNode"
      highlight-current
      @node-click="handleNodeClick"
      ref="treeRef"
      :data="treeData"
      :props="defaultProps"
      accordion
      default-expand-all
      node-key="id"
      :render-content="renderContent"
      :height="height"
    ></el-tree>
  </el-aside>
</template>

<script setup>
import { ref } from "vue";
const emit = defineEmits();
import { ElIcon } from 'element-plus';
import { FolderOpened,Document } from '@element-plus/icons-vue';
import fileIcon from '@/assets/images/file5.png'; // 引入图片路径
import folderIcon from '@/assets/images/file8.png'; // 引入图片路径
const props = defineProps({
  treeData: {
    type: Array,
    default: () => [],
  },
  defaultProps: {
    type: Object,
    default: () => ({}),
  },
  height: {
    type: [Number, String],
    default: "auto",
  },
});

const materialType=ref('')
const treeRef=ref(null)


/** 根据名称筛选部门树 */
watch(materialType, val => {
  treeRef.value.filter(val);
});
/** 通过条件过滤节点  */
const filterNode = (value, data,node) => {
  if (!value) return true;
  return node.label.indexOf(value) !== -1;
};
// const renderContent = (h, { node, data }) => {
//   // 确保 node 不为空
//   if (!node) {
//     return null;
//   }

//   // 判断是否为最后一级节点 (没有子节点)
//   const isLeafNode = !data.children || data.children.length === 0;

//   // 如果是最后一级节点，使用 Folder 图标；否则，使用 FolderOpened 图标
//   const IconComponent = isLeafNode ? Document : FolderOpened;

//   // 返回自定义的节点，使用相应的图标
//   return h('span', { class: 'custom-node' }, [
//     h(ElIcon, {}, [h(IconComponent)]),  // 使用相应的图标组件
//     node.label
//   ]);
// };
const renderContent = (h, { node, data }) => {
  // 确保 node 不为空
  if (!node) {
    return null;
  }

  // 判断是否为最后一级节点 (没有子节点)
  const isLeafNode = !data.children || data.children.length === 0;

  // 根据是否为最后一级节点选择不同的图片
  const iconSrc = isLeafNode 
    ? fileIcon // 使用 require 来加载图片
    : folderIcon; // 使用 require 来加载图片

  // 返回自定义的节点，使用相应的图片
  return h('span', { class: 'custom-node' }, [
    h('img', { src: iconSrc, alt: 'icon', class: 'custom-icon' }),  // 使用图片作为图标
    h('span', { class: 'custom-label' }, node.label)
  ]);
};
const handleNodeClick = (node, item) => {
  emit("nodeClick", node, item);
};

</script>

<style scoped>
.el-tree {
  background: transparent;
  color: black;
  overflow: auto;
}
/* .custom-node {
  position: relative;
  padding-left: 20px;
  font-size: 14px;
} */

.el-tree-node__content {
  position: relative;
}

.el-tree-node__children {
  padding-left: 20px;
}
/* .el-tree /deep/ .custom-node .el-icon  {
  color: #d0a935;
  margin-right: 10px;
} */
.el-tree /deep/ .custom-node {
 display: flex;
 align-items: center;
}
.el-tree /deep/ .custom-node .custom-icon{
margin-right: 5px;
}
</style>
