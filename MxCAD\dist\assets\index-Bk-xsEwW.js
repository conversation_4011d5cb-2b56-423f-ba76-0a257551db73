var Ct=Object.defineProperty;var kt=(n,e,r)=>e in n?Ct(n,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):n[e]=r;var G=(n,e,r)=>kt(n,typeof e!="symbol"?e+"":e,r);import{i as Et,a as ye,g as he,c as me,s as f,b as B,u as ot,C as Qe,d as vt,k as It,e as Me,f as Ze,h as rt,j as at,o as Lt,l as ve,m as ct,n as lt,p as Je,q as Tt,r as St,t as Rt,v as Vt,w as _t,x as $t,y as Wt,z as Nt,A as zt}from"./index-CzBriCFR.js";import{W as Kt,M as N,F as dt,I as ae,c as Le,h as Y,J as _,X as Ft,y as W,z as H,H as ce,K as J,x as Z,L as q,a as Ge,Y as je,k as re,e as U,Z as ut,G as Ce,m as Te,$ as Bt,i as gt,a0 as Ne,a1 as et,a2 as fe,a3 as Ot,g as Ht,a4 as Ut,l as jt,a5 as Xe}from"./mxcad-DrgW2waE.js";import"./mapbox-gl-DQAr7S0z.js";import{M as Xt}from"./index-BWTo6kTe.js";import{s as Yt}from"./hooks-IXT-OJS-.js";import{g as $,M as I,h as T,n as Se,D as ft,t as ue,u as ht,r as wt,v as Gt,a as Zt,f as qt}from"./mxdraw-BQ5HhRCY.js";import{p as Qt}from"./print-BKdhRc9P.js";import"./handsontable-Ch5RdAT_.js";import"./vue-Cj9QYd7Z.js";import"./vuetify-BqCp6y38.js";const Jt=(n=!1,e=!1,r=!1)=>new Promise(async(i,o)=>{let u;u=N.App.getCurrentMxCAD().getCurrentOriginaFileName(),u.length==0?u="temp_empty"+he(!0):u.indexOf(".")==-1?u+=he(!0):u.substring(u.length-6)!=he(!0)&&(u+=he(!0));let a=u;!N.App.getCurrentMxCAD().saveFile(u,async s=>{let t;/^((?!chrome|android).)*safari/i.test(navigator.userAgent)?t=new Blob([s.buffer],{type:"application/octet-stream"}):t=new Blob([s.buffer],{type:"application/octet-binary"});try{i({blob:t,data:s,filename:a})}catch(D){o(D),console.error(D)}},e,r,n?void 0:{compression:0})&&o(f("392"))}),yt=async()=>{try{if(!Et()){const n=ye(),{filename:e,blob:r,data:i}=await Jt(!0,!1,!1);await Kt({blob:r,filename:e,types:[{description:"webcad File",accept:{"application/octet-stream":[he(!0)]}}]}),n&&me("MxFullScreen")}}catch(n){console.error(n)}},en=async()=>{await yt()};async function tn(n,e){let r=n+"_preloading.json",i=await dt.getJsonFromUrl(r),o={ok:!0,tz:!1};if(!i)return new Promise((l,s)=>{l(o)});i.tz&&(o.tz=i.tz);let u=[];return i.images.forEach(l=>{l.substring(0,5)!="http:"&&l.substring(0,6)!="https:"&&u.push(l)}),i.images=u,i.images.length===0&&i.externalReference.length===0?new Promise((l,s)=>{l(o)}):(i.hash=e,(await Yt(i))?.data?o.ok=!0:o.ok=!1,new Promise((l,s)=>{l(o)}))}const nn=async()=>{const n=new ae;n.setMessage(f("选择需要离散的实体"));let e;n.setUserDraw(l=>{e=l});const r=await n.go();if(!r||!r.isValid())return;let i=r.getMcDbCurve();if(!i||!(i instanceof Le))return;const o=new Y;o.setUserDraw((l,s)=>{if(!i||!e)return;const t=i.clone();t&&(t.move(e,l),s.drawMcDbEntity(t))});const u=await o.go();if(!u)return;let a=i.clone();if(a)if(a.move(e,u),i instanceof _){const l=N.getCurrentMxCAD().drawEntity(a);let s=new Ft;s.copyFormAryId([l]),N.App.MxCADAssist.MxExplode(s.imp)}else{const l=a.getSamplePoints(.1);if(l.GetCount()===0)return;let s;const t=N.getCurrentMxCAD();l.forEach((w,D)=>{if(D===1010){const b=new W(w.x,w.y,w.z);if(s){const d=new H;d.startPoint=s,d.endPoint=b,d.colorIndex=a.colorIndex,d.trueColor=a.trueColor,d.drawOrder=a.drawOrder,d.layer=a.layer,d.layerId=a.layerId,d.linetype=a.linetype,d.linetypeScale=a.linetypeScale,d.lineweight=d.lineweight,d.textStyle=d.textStyle,t.drawEntity(d)}s=b}}),t.updateDisplay()}};B("_SampleCurve",nn);function Re(n,e,r){const i=e.clone(),o=r.clone(),u=n.clone(),a=u.sub(i),l=o.sub(i),s=u.sub(o);let t,w=a.crossProduct(l).length()/l.length(),D=a.dotProduct(l);const b=l.clone().mult(D/l.length()**2);return i.clone().addvec(b),D<0?t=a.length():D>Math.pow(l.length(),2)?t=s.length():t=w,Math.floor(t)}const sn=async()=>{const n=N.getCurrentMxCAD(),e=new Y;e.setMessage(f("342")),e.setKeyWords("");let r=await e.go(),i,o;if(e.getStatus()===$.kNone){const t=new ae,w=new ce;w.AddMcDbEntityTypes("CIRCLE,ARC,LINE,LWPOLYLINE");let D;const b=(d,m)=>{D=d;const x=U.findEntAtPoint(d.x,d.y,d.z,-1,w);if(!(x&&x.isValid()))return;const g=x.getMcDbEntity();g&&(o&&o.highlight(!1),g.highlight(!0),o=g)};for(;;){t.setMessage(f("343")),t.setUserDraw(b),t.setFilter(w);const m=(await t.go()).getMcDbEntity();if(o&&o.highlight(!1),t.getStatus()===$.kCancel)return;if(m instanceof J){const x=m.getStartPoint().val,g=m.radius,c=Z.kXAxis.clone().rotateBy(m.endAngle).mult(g),h=m.center.clone().addvec(c);if(!x||!h)return;r=x,i=h;break}else if(m instanceof q){const x=m.center,g=m.getStartPoint().val;if(!g)return;r=g,i=g.clone().addvec(x.sub(g).mult(2));break}else if(m instanceof H){r=m.startPoint,i=m.endPoint;break}else if(m instanceof _){for(let x=0;x<m.numVerts();x++){const g=m.getPointAt(x).val,c=m.getPointAt(x+1).val,y=m.getClosestPointTo(D,!1).val;c&&Re(y,g,c)===0&&(r=g,i=c)}break}else{I.acutPrintf(`
`+f("344"));continue}}}if(!r||!i&&(e.setMessage(f("345")),e.setUserDraw((t,w)=>{r&&w.drawMcDbLine(r.x,r.y,r.z,t.x,t.y,t.z)}),i=await e.go(),!i))return;let u,a,l,s;for(;;){e.setMessage(f("346")),e.setKeyWords(`[${f("347")}(T)/${f("281")}(A)${typeof s>"u"?f("352")+"/(H)/"+f("353")+"(V)":""}/${f("354")}(R)]`),e.clearLastInputPoint();let t;const w=r.clone(),D=i.clone();let b,d;e.setUserDraw(g=>{if(!r||!i)return;t&&t.erase(),o instanceof q&&(b||(b=o.center.clone().addvec(Z.kXAxis.clone().rotateBy(Math.PI/2).mult(o.radius))),d||(d=o.center.clone().addvec(Z.kXAxis.clone().rotateBy(-Math.PI/2).mult(o.radius))),g.y<b.y&&g.y>d.y&&(g.x>r.x||g.x<i.x)&&(r=b,i=d),g.x<w.x&&g.x>D.x&&(g.y>b.y||g.y<d.y)&&(r=w,i=D)),s==="H"&&(g.x<r.x?g.x=r.x:g.x>i.x&&(g.x=i.x)),s==="V"&&(g.y<r.y&&(g.y=r.y),g.y>i.y&&(g.y=i.y)),t=n.drawDimRotated(r.x,r.y,i.x,i.y,g.x,g.y,l||0);const c=t.getMcDbDimension();c&&(c.textPosition=g,c.useSetTextPosition(),u&&(c.dimensionText=u),a&&(c.textRotation=a),c.trueColor=new Ge(0,255,0))});const m=await e.go();if(e.isKeyWordPicked("T")){const g=new je;g.clearLastInputPoint(),g.setMessage(`${f("348")}<${u||t.getMcDbDimension()?.dimensionText||""}>`),g.setKeyWords("");const c=await g.go();if(typeof c!="string")return;u=c,t&&t.erase();continue}if(t&&t.erase(),e.isKeyWordPicked("A")){const g=new re;g.clearLastInputPoint(),g.setMessage(f("338"));const c=await g.go();if(!c||g.getStatus()===$.kCancel)return;g.getDetailedResult()===T.kCoordIn?a=c*(Math.PI/180):a=c;continue}if(e.isKeyWordPicked("H")){s="H";continue}if(e.isKeyWordPicked("V")){s="V";continue}if(e.isKeyWordPicked("R")){const g=new re;g.clearLastInputPoint(),g.setMessage(f("355"));const c=await g.go();if(!c||g.getStatus()===$.kCancel)return;g.getDetailedResult()===T.kCoordIn?l=c*(Math.PI/180):l=c;continue}if(!m)return;s==="H"&&(m.x<r.x?m.x=r.x:m.x>i.x&&(m.x=i.x)),s==="V"&&(m.y<r.y&&(m.y=r.y),m.y>i.y&&(m.y=i.y)),t=n.drawDimRotated(r.x,r.y,i.x,i.y,m.x,m.y,l||0);const x=t.getMcDbDimension();if(!x)return;x.textPosition=m,x.useSetTextPosition(),u&&(x.dimensionText=u),a&&(x.textRotation=a),x.trueColor=new Ge(0,255,0),n.updateDisplay();break}};B("_DrawRotatedDimension",sn);const on=async()=>{const n=N.getCurrentMxCAD();console.log(n.drawDimStyle);const e=new Y;e.setMessage(`${f("342")}<${f("205")}>`),e.setKeyWords("");let r=await e.go(),i;if(e.getStatus()===$.kNone){const a=new ae,l=new ce;l.AddMcDbEntityTypes("CIRCLE,ARC,LINE,LWPOLYLINE");let s,t;const w=(D,b)=>{s=D;const d=U.findEntAtPoint(D.x,D.y,D.z,-1,l);if(!(d&&d.isValid()))return;const m=d.getMcDbEntity();m&&(t&&t.highlight(!1),m.highlight(!0),t=m)};for(;;){a.setMessage(f("343")),a.setUserDraw(w),a.setFilter(l);const b=(await a.go()).getMcDbEntity();if(t&&t.highlight(!1),a.getStatus()===$.kCancel)return;if(b instanceof J){const d=b.getStartPoint().val,m=b.radius,x=Z.kXAxis.clone().rotateBy(b.endAngle).mult(m),c=b.center.clone().addvec(x);if(!d||!c)return;r=d,i=c;break}else if(b instanceof q){const d=b.center,m=b.getClosestPointTo(s,!1).val;if(!m)return;r=m,i=m.clone().addvec(d.sub(m).mult(2));break}else if(b instanceof H){r=b.startPoint,i=b.endPoint;break}else if(b instanceof _){for(let d=0;d<b.numVerts();d++){const m=b.getPointAt(d).val,x=b.getPointAt(d+1).val,g=b.getClosestPointTo(s,!1).val;x&&Re(g,m,x)===0&&(r=m,i=x)}break}else{I.acutPrintf(`
`+f("344"));continue}}}if(!r||!i&&(e.setMessage(f("345")),e.setUserDraw((a,l)=>{r&&l.drawMcDbLine(r.x,r.y,r.z,a.x,a.y,a.z)}),i=await e.go(),!i))return;let o,u;for(;;){e.setMessage(f("346")),e.setKeyWords(`[${f("347")}(T)/${f("281")}(A)]`),e.clearLastInputPoint();let a;e.setUserDraw(t=>{if(!r||!i)return;a&&a.erase(),a=n.drawDimAligned(r.x,r.y,i.x,i.y,t.x,t.y);const w=a.getMcDbDimension();w&&(o&&(w.dimensionText=o),u&&(w.textRotation=u))});const l=await e.go();if(e.isKeyWordPicked("T")){const t=new je;t.clearLastInputPoint(),t.setMessage(`${f("348")}<${o||a.getMcDbDimension()?.dimensionText||""}>`),t.setKeyWords("");const w=await t.go();if(typeof w!="string")return;o=w,a&&a.erase();continue}if(a&&a.erase(),e.isKeyWordPicked("A")){const t=new re;t.clearLastInputPoint(),t.setMessage(f("338"));const w=await t.go();if(!w||t.getStatus()===$.kCancel)return;t.getDetailedResult()===T.kCoordIn?u=w*(Math.PI/180):u=w;continue}if(!l)return;a=n.drawDimAligned(r.x,r.y,i.x,i.y,l.x,l.y);const s=a.getMcDbDimension();if(!s)return;s.textPosition=l,o&&(s.dimensionText=o),u&&(s.textRotation=u),n.updateDisplay();break}};B("_DrawAlignedDimension",on);async function rn(){let n=I.getCurrentDraw().getViewAngle();n-=Math.PI*.5,N.getCurrentMxCAD().zoomAngle(n)}async function an(){const n=new ut;n.setMessage(f("输入选项")),n.setKeyWords(`[${f("当前")} UCS(C)/${f("世界")}(W)/${f("角度")}(A)/${f("相对角度")}(X)]`);const e=await n.go();if(e?.toLocaleLowerCase(),e?.toLocaleLowerCase(),e?.toLocaleLowerCase()==="a"){const r=new re;r.clearLastInputPoint(),r.setMessage(f("输入视区旋转角度"));let i=await r.go();if(!i)return;r.getDetailedResult()===T.kCoordIn&&(i=i*(Math.PI/180)),N.getCurrentMxCAD().zoomAngle(i)}if(e?.toLocaleLowerCase()==="x"){const r=new re;r.clearLastInputPoint(),r.setMessage(f("输入视区旋转相对角度"));let i=await r.go();if(!i)return;r.getDetailedResult()===T.kCoordIn&&(i=i*(Math.PI/180));let o=I.getCurrentDraw().getViewAngle();o+=i,N.getCurrentMxCAD().zoomAngle(o)}}B("Mx_Plan90CCW",rn);B("Mx_Plan",an);function cn(){const n=N.getCurrentMxCAD();let e=n.mxdraw.getViewColor();const{createColor:r}=ot();me("Mx_Color",{color:r({color:Qe(e).toString()}),call:i=>{const o=Qe(i),u=o.red(),a=o.green(),l=o.blue();n.setViewBackgroundColor(u,a,l),I.callEvent("updateBackgroundColor",new Ge(u,a,l))}})}B("_ViewColor",cn);function mt(n,e){const r=document.timeline?document.timeline.currentTime:performance.now();let i=!1;function o(u){if(i)return;const a=u-r,l=Math.round(a/n);e(l);const s=(l+1)*n+r,t=document.timeline?document.timeline.currentTime:performance.now();return setTimeout(()=>{requestAnimationFrame(o)},s-t)}return o(r),()=>{i=!0}}function xt(n,e,r){let i=e,o=r;for(;o-i>1e-4;){let a=(i+o)/2;Math.floor(n/a)*a>n||a<e?o=a:i=a}let u=Math.floor(n/i);return n/u}function Ie(n,e,r,i){const o=n.distanceTo(e),u=[],a=xt(o,r,i);if(isNaN(a))return u;const l=o/a,s=e.sub(n).normalize();for(let t=0;t<l;t++)u.push(n.clone().addvec(s.clone().mult(a*t)));return u}function ln(n=new W,e=new W,r=3,i=Math.PI*2){const o=[];r=Math.max(3,r),o.push(e);const u=i/r;for(let a=1;a<r;a++){const l=Math.cos(u*a),s=Math.sin(u*a),t=n.clone(),w=e.clone(),D=w.x-t.x,b=w.y-t.y,d=D*l-b*s+t.x,m=D*s+b*l+t.y,x=new W(d,m);o.push(x)}return o}const dn=(n,e)=>{const r=new W(n.x,e.y,n.z),i=new W(e.x,n.y,e.z);return[n,r,e,i]};async function un(){const n=I.viewCoordLong2Cad(2);let e=Number(localStorage.getItem("mx_revcloud_minArcLength")),r=Number(localStorage.getItem("mx_revcloud_maxArcLength"));if(isNaN(e)||e<1e-4){const t=I.viewCoordLong2Cad(12);localStorage.setItem("mx_revcloud_minArcLength",t.toString())}if(isNaN(r)||r<1e-4){const t=I.viewCoordLong2Cad(12);localStorage.setItem("mx_revcloud_maxArcLength",t.toString())}const i=new Y;let o=-.45,u=!1,a=!1,l=!1;function s(t,w){t.colorIndex=w.colorIndex,t.trueColor=w.trueColor,t.drawOrder=w.drawOrder,t.layer=w.layer,t.layerId=w.layerId,t.linetype=w.linetype,t.linetypeScale=w.linetypeScale,t.lineweight=w.lineweight,t.textStyle=w.textStyle}for(;;){let t=f("356");u&&(t=f("357")),a&&(t=f("358")),i.setMessage(t),i.setKeyWords(`[${f("359")}(A)/${f("208")}(O)/${f("260")}(R)/${f("360")}(P)/${f("361")}(F)/${f("362")}(S)]`);const w=new _;i.clearLastInputPoint();const D=async d=>{if(i.clearLastInputPoint(),i.setMessage(f("363")),i.setKeyWords(`[${f("211")}(Y)/${f("212")}(N)]`),await i.go(),i.isKeyWordPicked("Y")){const m=d.numVerts();for(let x=0;x<m;x++){const g=d.getBulgeAt(x);d.setBulgeAt(x,-g)}}return!0};let b=await i.go();if(i.isKeyWordPicked("A")){const d=new Ce;d.setMessage(f("364"));let m=await d.go();if(typeof m!="number")return;if(m<1e-5){I.acutPrintf(`
`+f("365"));return}e=m,localStorage.setItem("mx_revcloud_minArcLength",m.toString());const x=async()=>{d.setMessage(f("366"));let g=await d.go();if(typeof g!="number")return!1;if(g<1e-5){I.acutPrintf(`
`+f("367"));return}if(g<e)return I.acutPrintf(`
`+f("368")),await x();r=g,localStorage.setItem("mx_revcloud_maxArcLength",g.toString())};if(await x()===!1)return;continue}if(i.isKeyWordPicked("O")){const d=new ce;d.AddMcDbEntityTypes("CIRCLE,ARC,LINE,LWPOLYLINE,ELLIPSE");const x=(await U.userSelect(f("205"),d))[0];if(!x)return;const g=x.getMcDbEntity();if(!g)return;if(g instanceof _){w.isClosed=g.isClosed,w.constantWidth=g.constantWidth,s(w,g);const c=g.numVerts();let y;for(let h=0;h<c;h++){const P=g.getPointAt(h).val;y&&P&&Ie(y,P,e,r).forEach(p=>{w.addVertexAt(p,o,void 0,l?n:void 0)}),y=P}return w.isClosed?Ie(y,g.getPointAt(0).val,e,r).forEach(h=>{w.addVertexAt(h,o,void 0,l?n:void 0)}):w.addVertexAt(y,o),await D(w),g.erase(),N.getCurrentMxCAD().drawEntity(w)}if(g instanceof H)return s(w,g),Ie(g.startPoint,g.endPoint,e,r).forEach(c=>{w.addVertexAt(c,o,void 0,l?n:void 0)}),w.addVertexAt(g.endPoint,o,void 0,l?n:void 0),await D(w),g.erase(),N.getCurrentMxCAD().drawEntity(w);if(g instanceof q){const c=g.getLength().val,y=c/xt(c,e,r),h=g.getStartPoint().val;return ln(g.center,h,y).forEach(P=>{w.addVertexAt(P,o,void 0,l?n:void 0)}),s(w,g),g instanceof q&&(w.isClosed=!0),await D(w),g.erase(),N.getCurrentMxCAD().drawEntity(w)}}if(i.isKeyWordPicked("R")){u=!0,a=!1;continue}if(i.isKeyWordPicked("P")){a=!0,u=!1;continue}if(i.isKeyWordPicked("F")){a=!1,u=!1;continue}if(i.isKeyWordPicked("S")){if(i.setMessage(f("369")),i.setKeyWords(`[${f("370")}(N)/${f("371")}(C)]`),await i.go(),i.getDetailedResult()===T.kEcsIn||i.getDetailedResult()===T.kNewCommadIn||i.getStatus()===$.kNone||i.getStatus()===$.kCancel)return;i.isKeyWordPicked("N")&&(l=!1),i.isKeyWordPicked("C")&&(l=!0);continue}if(!b)return;if(u){i.setMessage(f("350")),i.setMessage("");const d=(x,g)=>{if(!b)return;const[c,y,h,P]=dn(b,g),p=b.x<g.x&&b.y<g.y||b.x>g.x&&b.y>g.y;[[c,y],[y,h],[h,P],[P,c]].forEach(([A,M])=>{Ie(A,M,e,r).forEach(C=>{x.addVertexAt(C,p?o:-o,void 0,l?n:void 0)})}),x.isClosed=!0};i.setUserDraw((x,g)=>{const c=new _;d(c,x),g.drawMcDbEntity(c)});const m=await i.go();return m?(d(w,m),N.getCurrentMxCAD().drawEntity(w)):void 0}else if(a){let d=b;const m=[];m.push(d);const x=(g,c)=>{let y;c.forEach(h=>{y&&Ie(y,h,e,r).forEach(P=>{g.addVertexAt(P,o,void 0,l?n:void 0)}),y=h}),c.length>2?(g.isClosed=!0,Ie(y,c[0],e,r).forEach(h=>{g.addVertexAt(h,o,void 0,l?n:void 0)})):g.addVertexAt(y,o,void 0,l?n:void 0)};for(i.setUserDraw((g,c)=>{const y=new _;x(y,[...m,g]),c.drawMcDbEntity(y)});;){i.setMessage(f("372")),i.setKeyWords(m.length<2?"":`[${f("373")}(U)]`);const g=await i.go();if(i.isKeyWordPicked("U")){m.pop(),i.clearLastInputPoint();continue}if(i.getDetailedResult()===T.kNullEnterIn||i.getDetailedResult()===T.kMouseRightIn)return x(w,m),await D(w),N.getCurrentMxCAD().drawEntity(w);if(!g)return;m.push(g)}}else{w.addVertexAt(b,o,void 0,l?n:void 0);const d=b.clone();let m=0,x=b;const g=async()=>{y(),await D(w),N.getCurrentMxCAD().drawEntity(w)};i.setMessage(f("374")+"..."),i.setKeyWords(""),i.setUserDraw((P,p)=>{b&&(x=P,m=b.distanceTo(P),p.drawMcDbEntity(w.clone()),p.drawLine(b.toVector3(),P.toVector3()))}),i.clearLastInputPoint();let c=!1;const y=mt(20,async()=>{if(!(m<e)){if(x.distanceTo(d)<e){w.isClosed=!0,I.stopRunCommand(),c=!0;return}w.addVertexAt(x,o,void 0,l?n:void 0),b=x,m=0}}),h=await i.go();if((i.getDetailedResult()===T.kMouseRightIn||i.getDetailedResult()===T.kNullEnterIn)&&await g(),!h&&!c)return y();h&&w.addVertexAt(h,o,void 0,l?n:void 0),await g();break}}}B("_Revcloud",un);async function gn(){const n=ye(),e=()=>{n&&me("MxFullScreen")},r=(l,s,t=210,w=297,D=0)=>{let{baseUrl:b="",mxfilepath:d="",printPdfUrl:m=""}=vt()||{},x={width:""+t,height:""+w,roate_angle:D,bd_pt1_x:""+l.x,bd_pt1_y:""+l.y,bd_pt2_x:""+s.x,bd_pt2_y:""+s.y};N.getCurrentMxCAD().saveFileToUrl(m,(g,c)=>{try{let y=JSON.parse(c);if(y.ret=="ok"){let h=b+d+y.file;Qt(h),e()}else console.log(c)}catch{console.log("Mx: sserverResult error")}},void 0,JSON.stringify(x))};I.acutPrintf(`
`+f("316"));let i=N.getCurrentMxCAD(),o=0,u=!1;for(;;){let l=new Y;l.setMessage(`
`+f("317")+":"),l.setKeyWords(`[${f("318")}(S)/[${f("319")}(D)]`),l.disableAllTrace();let s=await l.go();if(l.isKeyWordPicked("D"))u=!0;else if(l.isKeyWordPicked("S")){let t=new ut;t.setMessage(f("320")),t.setKeyWords(`[A1(A)/A2(B)/A3(C)/A4(D)/${f("321")}16.55x23.90(E)]`),t.setDisableDynInput(!0);const w=await t.go();if(!w)return;let D={A:{w:594,h:841},B:{w:420,h:594},C:{w:297,h:420},D:{w:210,h:297},E:{w:165.5,h:239,nw:130,nh:190}};if(!D[w])return;let b=new je;b.setMessage(`${f("322")}(${f("323")})`);let d=await b.go();if(!d)return;var a=parseFloat(d);if(isNaN(a)){I.acutPrintf(f("324"));return}let m=D[w].w*a,x=D[w].h*a,g=0,c=0;if(D[w].nw&&(g=D[w].nw*a),D[w].nh&&(c=D[w].nh*a),u){let p=m;m=x,x=p,p=g,g=c,c=p}l=new Y,l.disableAllTrace(),l.setMessage(`
`+f("325")),l.setKeyWords(""),l.setUserDraw((p,A)=>{A.setColor(16711680);let M=new _,C=new W(p.x-m*.5,p.y-x*.5),L=new W(p.x-m*.5,p.y+x*.5),v=new W(p.x+m*.5,p.y+x*.5),E=new W(p.x+m*.5,p.y-x*.5);M.addVertexAt(C),M.addVertexAt(L),M.addVertexAt(v),M.addVertexAt(E),M.constantWidth=I.screenCoordLong2Doc(2),M.isClosed=!0,A.drawMcDbEntity(M);let k=[];if(k.push(C.toVector3()),k.push(L.toVector3()),k.push(v.toVector3()),k.push(E.toVector3()),A.setColor(12868),A.drawSolid(k,.5),g>0&&c>0){let S=new W(p.x-g*.5,p.y-c*.5),R=new W(p.x-g*.5,p.y+c*.5),F=new W(p.x+g*.5,p.y+c*.5),K=new W(p.x+g*.5,p.y-c*.5),V=[];V.push(S.toVector3()),V.push(R.toVector3()),V.push(F.toVector3()),V.push(K.toVector3()),V.push(S.toVector3());let O=i.mxdraw.viewCoordLong2Cad(3),j=Se.createDashedLines(V,16777215,O*2,O);A.drawEntity(j)}});let y=await l.go();if(!y)return e();let h=new W(y.x-m*.5,y.y-x*.5),P=new W(y.x+m*.5,y.y+x*.5);r(h,P,u?D[w].h:D[w].w,u?D[w].w:D[w].h,o);return}else{if(!s)return e();l.setMessage(`
`+f("326")+":"),l.setUserDraw((w,D)=>{if(!s)return e();D.setColor(16711680);let b=new _;b.addVertexAt(s),b.addVertexAt(new W(s.x,w.y)),b.addVertexAt(w),b.addVertexAt(new W(w.x,s.y)),b.constantWidth=I.screenCoordLong2Doc(2),b.isClosed=!0,D.drawMcDbEntity(b);let d=[];d.push(s.toVector3()),d.push(new THREE.Vector3(s.x,w.y)),d.push(w.toVector3()),d.push(new THREE.Vector3(w.x,s.y)),D.setColor(12868),D.drawSolid(d,.5)}),l.setDisableOsnap(!0),l.setDisableOrthoTrace(!0),l.setDynamicInputType(ft.kXYCoordInput);let t=await l.go();if(!t)return e();r(s,t,u?297:210,u?210:297,o);return}}}B("Plot",gn);async function fn(){const n=I.viewCoordLong2Cad(20),e=new Y;e.setDisableDynInput(!0),e.setDynamicInputType(ft.kNoInput);let r=await e.go();if(!r)return;const i=new _;let o=0,u=r;const a=mt(20,()=>{o<n||(i.addVertexAt(u),r=u,o=0)});e.setUserDraw((t,w)=>{r&&(u=t,o=r.distanceTo(t),w.drawMcDbEntity(i.clone()),w.drawLine(r.toVector3(),t.toVector3()))});const l=await e.go();if(!l)return a();a(),i.addVertexAt(l),N.getCurrentMxCAD().drawEntity(i)}B("MxET_Pencil",fn);function hn(n){const e=n.requestFullscreen||n.mozRequestFullScreen||n.webkitRequestFullScreen||n.msRequestFullscreen;e?e.call(n):(Me().error(f("309")+"!"),console.error(f("309")+"!"))}function wn(){const n=document.exitFullscreen||document.mozCancelFullScreen||document.webkitExitFullscreen||document.msExitFullscreen;n?n.call(document):(Me().error(f("309")+"!"),console.error(f("309")+"!"))}window.addEventListener("keydown",n=>{n.keyCode===122&&(n.returnValue=!1,me("MxFullScreen"))},!0);It.register({key:{keyCode:"Escape"},when(n){return n.isRunCommand?!1:ye()},action(){me("MxFullScreen")}});B("MxFullScreen",()=>{ye()?(wn(),"keyboard"in navigator&&navigator.keyboard.unlock()):(hn(document.body),"keyboard"in navigator&&navigator.keyboard.lock())});async function yn(){const n=new Ce;n.setMessage(f("指定圆环的内径"));const e=await n.go();if(!e)return;n.setMessage(f("指定圆环的外径"));const r=await n.go();if(!r)return;const i=new Y;i.clearLastInputPoint(),i.setMessage(f("指定圆环的中心点")),i.setUserDraw((D,b)=>{const d=new q,m=new q;d.radius=e/2,m.radius=r/2,d.center=D,m.center=D,b.drawMcDbEntity(d),b.drawMcDbEntity(m)});const o=await i.go();if(!o)return;const u=Math.abs(e-r)/4,a=Math.min(e,r),l=o.clone().addvec(Z.kXAxis.clone().mult(a/2+u)),s=o.clone().addvec(Z.kXAxis.clone().negate().mult(a/2+u)),t=new _,w=1;t.addVertexAt(l,w),t.addVertexAt(s,w),t.addVertexAt(l,w),t.isClosed=!0,t.constantWidth=u*2,N.getCurrentMxCAD().drawEntity(t)}B("_donut",yn);function Ve(n,e,r=0){const i=n.numVerts();for(let o=0;o<n.numVerts();o++){const u=n.getPointAt(o).val;let a=n.getPointAt(o+1).val;if(n.getBulgeAt(o)===0){if(o+1===i&&n.isClosed&&(a=n.getPointAt(0).val),a&&Re(e,u,a)<r)return{start:u,end:a,startIndex:o,endIndex:o+1}}else if(a){const s=n.getParamAtPoint(u).val,t=n.getParamAtPoint(a).val,w=n.getParamAtPoint(e).val;if(t>w&&w>s)return{start:u,end:a,startIndex:o,endIndex:o+1}}}if(n.isClosed){const o=n.getPointAt(0).val,u=n.getPointAt(n.numVerts()-1).val;if(Re(e,u,o)<r)return{start:u,end:o,startIndex:n.numVerts()-1,endIndex:0,isClosed:!0}}}async function mn(){const n=N.getCurrentMxCAD(),e=new ae;let r,i,o,u,a,l;const s=new ce;s.AddMcDbEntityTypes("CIRCLE,ARC,LINE,LWPOLYLINE");let t,w;const D=(g,c)=>{t=g;const y=U.findEntAtPoint(g.x,g.y,g.z,-1,s);if(!(y&&y.isValid()))return;const h=y.getMcDbEntity();h&&(w&&w.highlight(!1),h.highlight(!0),w=h)};for(;;){e.setMessage(`${f("238")}${f("284")}、${f("268")}、${f("283")}${f("327")} <${f("328")}>`),e.setUserDraw(D),e.setFilter(s);const c=(await e.go()).getMcDbEntity();if(w&&w.highlight(!1),e.getStatus()===$.kCancel)return;if(e.getStatus()===$.kNone){const y=new Y;y.clearLastInputPoint(),y.setMessage(f("329"));const h=await y.go();if(!h)return;y.setBasePt(h),y.setMessage(f("330"));const P=await y.go();if(!P)return;y.setMessage(f("331"));const p=await y.go();if(!p)return;r=h.x,i=h.y,o=P.x,u=P.y,a=p.x,l=p.y;break}else if(c instanceof J){const y=c.getStartPoint().val,h=c.radius,P=Z.kXAxis.clone().rotateBy(c.endAngle).mult(h),p=c.center,A=p.clone().addvec(P);if(!y||!A)return;o=y.x,u=y.y,a=A.x,l=A.y,r=p.x,i=p.y;break}else if(c instanceof q){const y=c.center;r=y.x,i=y.y,o=t.x,u=t.y;const h=new Y;h.setMessage(f("331"));const P=await h.go();if(!P)return;const p=c.getClosestPointTo(P,!1).val;a=p.x,l=p.y;break}else if(c instanceof H||c instanceof _){const y=new ae,h=new ce;h.AddMcDbEntityTypes("LINE,LWPOLYLINE"),y.setFilter(h);let P;w=null,y.setUserDraw((M,C)=>{D(M),P=M});const p=c.getClosestPointTo(t,!1).val;let A;if(o=p.x,u=p.y,c instanceof H)A=c,A.highlight(!0);else{const{start:M,end:C}=Ve(c,p,2)||{};M&&C&&(A=new H(M,C)),c.highlight(!1)}if(!A)return;for(;;){y.setMessage(f("332"));const M=await y.go();if(y.getStatus()===$.kCancel)return A.highlight(!1);if(!M||!M.isValid())continue;let C=M.getMcDbEntity(),L;if(C instanceof _){const{start:S,end:R}=Ve(C,y.getDocPickPoint(),2)||{};L=new H(S,R),C.highlight(!1)}else if(C instanceof H)L=C;else{I.acutPrintf(`
`+f("333"));continue}if(!L)return A.highlight(!1);A.highlight(!1),L.highlight(!1);const v=A.clone()?.IntersectWith(L.clone(),Te.Intersect.kExtendBoth);if(console.log(12),v.isEmpty())continue;const E=v.at(0),k=L.getClosestPointTo(P,!1).val;a=k.x,l=k.y,r=E.x,i=E.y;break}break}}const b=new Y;b.clearLastInputPoint();let d,m,x;if(![r,i,o,u,a,l].some(g=>typeof g!="number"))for(;;){b.setMessage(f("334")),b.setKeyWords(`[${f("285")}(T)/${f("281")}(A)/${f("335")}(Q)]`),b.setUserDraw((y,h)=>{let P=new Bt;P.compute(r,i,o,u,a,l,y.x,y.y),m&&(P.dimensionText=m),x&&(P.textRotation=x),h.drawMcDbEntity(P,!0)});const g=await b.go();if(b.isKeyWordPicked("T")){const y=new je;y.clearLastInputPoint(),y.setMessage(`${f("336")}${f("337")}<${m||d.getMcDbDimension()?.dimensionText||""}>`),y.setKeyWords("");const h=await y.go();if(typeof h!="string")return;m=h;continue}if(b.isKeyWordPicked("A")){const y=new re;y.clearLastInputPoint(),y.setMessage(f("338"));const h=await y.go();if(!h||y.getStatus()===$.kCancel)return;y.getDetailedResult()===T.kCoordIn?x=h*(Math.PI/180):x=h;continue}if(b.isKeyWordPicked("Q"),!g)return;n.drawUseDefaultProperties=!0,d=n.drawDimAngular(r,i,o,u,a,l,g.x,g.y);const c=d.getMcDbDimension();if(!c)return;m&&(c.dimensionText=m),x&&(c.textRotation=x);return}}B("_dimangular",mn);function xn(n,e,r){const{x:i,y:o}=e,{x:u,y:a}=r,{x:l,y:s}=n,t=Math.min(i,u),w=Math.min(o,a),D=Math.max(i,u),b=Math.max(o,a);return l>=t&&l<=D&&s>=w&&s<=b}function Ye(n,e,r,i,o){const u=e.sub(n),a=r.distanceTo(i)===0,l=r.x>i.x;o.forEach(s=>{if(s)if(l&&!a&&!(s instanceof q)&&!(s instanceof Ne&&(s.startAngle===0&&s.endAngle===Math.PI*2||s.endAngle===0&&s.startAngle===Math.PI*2))){const t=s.getGripPoints();if(t.isEmpty())return;t.forEach((w,D)=>{if(xn(w,r,i)&&!(s instanceof J&&D===2)&&!(s instanceof H&&D===2)){if(s instanceof _&&t.length()!==s.numVerts()){const b=s.numVerts();let d=0;for(let m=0;m<b;m++){if(s.getPointAt(m).val,s.getBulgeAt(m)!==0&&(d++,d===D))return;d++}}s.moveGripPointsAt(D,u.x,u.y,u.z)}})}else s.move(n,e)})}const Ke=Ze(0,"_stretch_offsetX"),Fe=Ze(0,"_stretch_offsetY"),Be=Ze(0,"_stretch_offsetZ");async function Pn(){const n=new gt;let e=U.getCurrentSelect(),r,i;if(e.length<=0){if(!await n.userSelect(f("376")))return;e=n.getIds();const t=n.getSelectPoint();r=t.pt1,i=t.pt2}else{const{point1:t,point2:w}=U.getCurrentSelectPoints();r=t,i=w}const o=new Y;o.setMessage(`
`+f("377")),o.setKeyWords(`[${f("378")}(D)]`);const u=await o.go(),a=async t=>{if(!(typeof t>"u"&&(o.setMessage(`
${f("379")}<${Ke.value}, ${Fe.value}, ${Be.value}>`),o.setKeyWords(""),o.clearLastInputPoint(),t=await o.go(),!t))){if(t){const{x:w,y:D,z:b}=t;Ke.value=w,Fe.value=D,Be.value=b}else{if(Ke.value===0&&Fe.value===0&&Be.value===0)return;t=new W(Ke.value,Fe.value,Be.value)}Ye(new W,t,r,i,e.map(w=>w.getMcDbEntity()))}};if(o.isKeyWordPicked("D")||o.getStatus()===$.kNone)return await a();if(!u)return;o.setMessage(`
${f("380")}${f("327")}<${f("381")}>`),o.setBasePt(u),o.setKeyWords(""),o.setUserDraw((t,w)=>{const D=e.map(b=>b.clone());Ye(u,t,r,i,D),D.forEach((b,d)=>{const m=e[d]?.getMcDbEntity();if(m){const x=m.trueColor.getColorValue(m.layerId);w.setColor(Number(x))}w.drawMcDbEntity(b)})});let l=await o.go();if(o.getStatus()===$.kNone)return await a(u);if(!l)return;const s=e.map(t=>t.getMcDbEntity()).filter(t=>!!t);Ye(u,l,r,i,s)}B("_stretch",Pn);class pn{constructor(e){G(this,"resizeObserver",null);G(this,"mutationObserver",null);G(this,"element",null);this.callback=e}addListener(e){this.element=e,this.setupResizeObserver(),this.setupMutationObserver()}removeListener(){this.resizeObserver&&(this.resizeObserver.unobserve(this.element),this.resizeObserver.disconnect(),this.resizeObserver=null),this.mutationObserver&&(this.mutationObserver.disconnect(),this.mutationObserver=null),this.element=null}setupResizeObserver(){typeof ResizeObserver<"u"&&(this.resizeObserver=new ResizeObserver(this.callback),this.resizeObserver.observe(this.element))}setupMutationObserver(){typeof MutationObserver<"u"&&(this.mutationObserver=new MutationObserver(this.callback),this.mutationObserver.observe(this.element,{attributes:!0}))}}function An(n,e){const r=new pn(e);return r.addListener(n),r}function bn(n){n.removeListener()}function Dn(n,e){let r;return function(...i){clearTimeout(r),r=setTimeout(()=>n(...i),e)}}function Mn(n,e,r){typeof e=="string"&&(e=parseFloat(e));const i=document.createElement("canvas"),o=i.getContext("2d");if(!o)return;o.font=e+"px "+r;let a=o.measureText(n).width,s=e*1.2;return i.remove(),a=Math.ceil(a),s=Math.ceil(s),{width:a,height:s}}function Cn(n){const e=document.createElement("template");e.innerHTML=n.trim();const r=document.createElement("div");r.style.position="absolute",r.style.visibility="hidden",r.style.pointerEvents="none",r.appendChild(e.content.cloneNode(!0)),document.body.appendChild(r);let{width:i,height:o}=r.getBoundingClientRect();return i=Math.ceil(i)+5,o=Math.ceil(o),document.body.removeChild(r),{width:i,height:o}}function kn(n){let e={watermark_txt:f("水印"),watermark_html:"",watermark_x:20,watermark_y:20,watermark_rows:200,watermark_cols:200,watermark_x_space:80,watermark_y_space:80,watermark_color:"#aaa",watermark_alpha:.4,watermark_fontsize:"15px",watermark_font:"微软雅黑",watermark_width:"auto",watermark_height:"auto",watermark_angle:15,watermark_className:"mx_mask_div"};e={...e,...n};const r=N.getCurrentMxCAD().mxdraw.getCanvas();let i=null,o=null,u;const a=()=>{document.querySelectorAll("."+e.watermark_className).forEach(w=>w.remove())},l=()=>{a(),u&&bn(u)},s=()=>{const t=document.createDocumentFragment(),w=r.parentElement,D=w.clientWidth,b=w.clientHeight;if(D===i&&b===o)return;i=D,o=b,a();const d=Math.max(w.scrollWidth,w.clientWidth),m=Math.max(w.scrollHeight,w.clientHeight);if(e.watermark_width==="auto"||e.watermark_height==="auto")if(e.watermark_html!==""){const{width:y,height:h}=Cn(e.watermark_html);e.watermark_width==="auto"&&(e.watermark_width=y),e.watermark_height==="auto"&&(e.watermark_height=h)}else{const y=e.watermark_txt.length,h=parseFloat(e.watermark_fontsize)||16,{width:P=y*h,height:p=h+e.watermark_x_space+e.watermark_y_space}=Mn(e.watermark_txt,e.watermark_fontsize,e.watermark_font)||{};e.watermark_width==="auto"&&(e.watermark_width=P),e.watermark_height==="auto"&&(e.watermark_height=p)}e.watermark_cols=Math.ceil(d/(e.watermark_x_space+e.watermark_width)),e.watermark_rows=Math.ceil(m/(e.watermark_y_space+e.watermark_height));let x,g;for(let y=0;y<e.watermark_rows;y++){g=e.watermark_y+(e.watermark_y_space+e.watermark_height)*y;for(let h=0;h<e.watermark_cols;h++){x=e.watermark_x+(e.watermark_width+e.watermark_x_space)*h;var c=document.createElement("div");c.id=(e.watermark_className||"")+y+h,c.className=e.watermark_className||"",e.watermark_html!==""?c.innerHTML=e.watermark_html:c.appendChild(document.createTextNode(e.watermark_txt)),c.style.webkitTransform="rotate(-"+e.watermark_angle+"deg)",c.style.MozTransform="rotate(-"+e.watermark_angle+"deg)",c.style.msTransform="rotate(-"+e.watermark_angle+"deg)",c.style.OTransform="rotate(-"+e.watermark_angle+"deg)",c.style.transform="rotate(-"+e.watermark_angle+"deg)",c.style.visibility="",c.style.position="absolute",c.style.left=x+"px",c.style.top=g+"px",c.style.overflow="hidden",c.style.zIndex="9999",c.style.pointerEvents="none",c.style.opacity=e.watermark_alpha.toString(),c.style.fontSize=e.watermark_fontsize,c.style.fontFamily=e.watermark_font,c.style.color=e.watermark_color,c.style.textAlign="center",c.style.width=e.watermark_width+"px",c.style.height=e.watermark_height+"px",c.style.display="block",t.appendChild(c)}}w.appendChild(t)};return s(),u=An(r,Dn(s,200)),l}B("_watermark",kn);async function En(){const n=N.getCurrentMxCAD();for(;;){const e=new ae;e.setMessage(f("349"));const r=await e.go();if(e.getStatus()===$.kCancel||e.getStatus()===$.kNone)return;const i=r.getMcDbEntity();if(i instanceof J||i instanceof q){const o=new Y;o.setMessage(f("350"));const u=await o.go();if(o.getStatus()===$.kCancel||o.getStatus()===$.kNone)return;if(u){const a=i.center.clone(),l=u.sub(a).normalize().mult(i.radius),s=a.clone().addvec(l),t=a.clone().addvec(l.clone().negate()),w=Math.min(t.distanceTo(u),s.distanceTo(u));n.drawDimDiametric(s.x,s.y,t.x,t.y,w)}break}else{I.acutPrintf(f("351"));continue}}}B("_DrawDiametricDimension",En);async function vn(){const n=N.getCurrentMxCAD();for(;;){const e=new ae;e.setMessage(f("349"));const r=await e.go();if(e.getStatus()===$.kCancel||e.getStatus()===$.kNone)return;const i=r.getMcDbEntity();if(i instanceof J||i instanceof q){const o=i.center.clone(),u=Z.kXAxis.clone().mult(i.radius),a=o.clone().addvec(u);n.drawDimDiametric(o.x,o.y,a.x,a.y,i.radius);break}else{I.acutPrintf(f("351"));continue}}}B("_DrawRadialDimension",vn);function Pt(n){return new Promise(e=>{const{hideLoading:r,showLoading:i}=at();try{let o=Me().info(f("441")+"...");const u=N.App.getCurrentMxCAD().openWebFile(n,a=>{a===0?(Me().success(f("442")),e(!0)):(Me().error(f("307")),e(!1)),r(),o()});rt(n).then(a=>{a/(1024*1024)>1&&u&&i()}),e(u)}catch{r(),e(!1)}})}function In(n){Pt(n)}B("_openMxweb",In);let tt=!1;Lt(()=>{if(tt)return;let n;const e=[],r=N.getCurrentMxCAD().mxdraw.getOrbitControls();r.addEventListener("change",()=>{clearTimeout(n),n=setTimeout(function(){const o=r.object.position.clone(),u=r.object.zoom,a=r.target.clone(),l=N.getCurrentMxCAD().mxdraw.getCamera();e.push({zoom:u,position:o,target:a,camera:l.clone(!1)})},500)});const i=o=>{if(!o)return!1;const u=N.getCurrentMxCAD().mxdraw,a=u.getOrbitControls(),l=a.object.position.clone(),s=a.object.zoom,t=a.target.clone(),w=N.getCurrentMxCAD().mxdraw.getCamera();return w.copy(o.camera,!1),w.updateProjectionMatrix(),s===o.zoom&&l.equals(o.position)&&t.equals(o.target)?i(e.pop()):(a.object.position.copy(o.position),a.object.zoom=o.zoom,a.target.copy(o.target),a.update(),u._mxdrawObj.mcObject.updateDisplayMatrixData(),!0)};B("Mx_WindowZoom",async()=>{let o=new Y;o.setMessage(`
`+f("393")),o.setKeyWords(`[${f("265")}(A)/${f("236")}(E)/${f("275")}(P)/${f("208")}(O)]`);let u=N.getCurrentMxCAD(),a=await o.go();if(o.isKeyWordPicked("A"))return u.mxdraw.zoomInitialStates();if(o.isKeyWordPicked("E"))return u.zoomAll(!0);if(o.isKeyWordPicked("P")){i(e.pop())||I.acutPrintf(`
`+f("394")+`
`+f("395"));return}if(o.isKeyWordPicked("O")){const s=await U.userSelect(`ZOOM ${f("205")}`);if(s.length<=0)return;const t=U.getMcDbEntitysBoundingBox(s);if(!t)return;const{minPt:w,maxPt:D}=t;return u.zoomW(w,D)}if(a==null)return;o.setUserDraw((s,t)=>{t.drawRect(et.cad2doc1(a),et.cad2doc1(s))}),o.setKeyWords("");let l=await o.go();l!=null&&u.zoomW(a,l)}),tt=!0});class ke extends ue{constructor(){super(...arguments);G(this,"dDashArray",.03);G(this,"dDashRatio",.1);G(this,"isAligned",!1);G(this,"fixedSize",!0);G(this,"referenceAxis",new THREE.Vector3(0,1,1));G(this,"offsetHeight");G(this,"isDrawsMallRound",!0)}getTypeName(){return"MxAuxiliaryLine"}create(){return new ke}onViewChange(){return this.fixedSize?(this.setNeedUpdateDisplay(!1),!0):!1}worldDraw(r){const{dDashArray:i,dDashRatio:o}=this.getDash();let u=r.getMxObject();r.setLineWidth(.7),r.setDash(i,o);let a=this.toSmallcoord(u,this.pt1),l=this.toSmallcoord(u,this.pt2),s=a,t=l;typeof this.offsetHeight>"u"&&(this.offsetHeight=u.screenCoordLong2World(40));const w=this.offsetHeight;if(this.isAligned){a.x>l.x&&(a=this.pt2,l=this.pt1);const d=l.clone().sub(a).cross(this.referenceAxis.clone().normalize()).normalize();s=a.clone().add(d.clone().multiplyScalar(w)),t=l.clone().add(d.clone().multiplyScalar(w))}else{const d=Math.abs(this.referenceAxis.y?a.y-l.y:a.x-l.x)+w;let m=w,x=d;(this.referenceAxis.x!==0&&a.x>l.x||this.referenceAxis.y!==0&&a.y>l.y)&&(a=this.pt2,l=this.pt1),this.referenceAxis.x>0&&(m=d,x=w),this.referenceAxis.y>0&&(m=d,x=w),s=a.clone().add(this.referenceAxis.clone().multiplyScalar(m)),t=l.clone().add(this.referenceAxis.clone().multiplyScalar(x))}s.setZ(a.z),t.setZ(l.z);const D=Se.createDashedLines([a,s,t,l],8421504,.1,.1);if(r.drawEntity(D),this.isDrawsMallRound){const d=u.screenCoordLong2World(1);r.drawCircle(a,d),r.drawCircle(l,d),r.drawCircle(s,d),r.drawCircle(t,d)}const b=s.distanceTo(t).toFixed(3);if(r.getType()===ht.kWorldDraw){const d=new wt;d.opacity=1,d.text=b,d.backgroundColor=16777185,d.color=16,d.position=new THREE.Vector3((s.x+t.x)/2,(s.y+t.y)/2),d.setFontSize(36),d.height=u.screenCoordLong2World(16),d.setLineWidthByPixels(!0),d.setUseSmallcoordDisplay(this.use_smallcoord_display),d.worldDraw(r)}else{const d=Se.creatTextSprite(b,new THREE.Vector3((s.x+t.x)/2,(s.y+t.y)/2),u.screenCoordLong2World(16),0,16777215);d&&r.drawEntity(d)}}getGripPoints(){return[this.pt1,this.pt2]}moveGripPointsAt(r,i){return r===0&&this.pt1.add(i),r===1&&this.pt2.add(i),!0}dwgIn(r){return super.dwgIn(r),this.dwgInHelp(r,["referenceAxis","isAligned","offsetHeight","fixedSize","isDrawsMallRound"]),!0}dwgOut(r){return r=super.dwgOut(r),this.dwgOutHelp(r,["referenceAxis","isAligned","offsetHeight","fixedSize","isDrawsMallRound"]),r}}class we extends Gt{constructor(){super(...arguments);G(this,"dDashArray",.03);G(this,"dDashRatio",.1);G(this,"pt1");G(this,"pt2");G(this,"center");G(this,"fixedSize",!0);G(this,"offsetDist");G(this,"isClockwise","auto");G(this,"isDrawsMallRound",!0);G(this,"isMaxRadius",!1);G(this,"radius")}getTypeName(){return"MxAuxiliaryArc"}create(){return new we}onViewChange(){return this.fixedSize?(this.setNeedUpdateDisplay(!1),!0):!1}calculateLineAngle(r,i,o){const u=Math.PI*2/360;let a=Math.atan2(i.y-r.y,i.x-r.x)*180/Math.PI*u,l=Math.atan2(o.y-r.y,o.x-r.x)*180/Math.PI*u;return{startAngle:a,endAngle:l}}getClockwise(){const r=this.getStartPoint(),i=this.getEndPoint();let o=this.isClockwise;if(o==="auto"){const u=r.clone().sub(this.center),a=i.clone().sub(this.center);o=u.x*a.y-u.y*a.x<0}return o}getAngle(){const r=this.center,i=this.getStartPoint(),o=this.getEndPoint();let u=this.getClockwise();const{startAngle:a,endAngle:l}=this.calculateLineAngle(r,i,o);let s=l-a;return!u&&s<0?s+=2*Math.PI:u&&s>0&&(s-=2*Math.PI),Math.abs(s)}getRadius(r=!1){return this.radius?this.radius+(r&&this.offsetDist||0):this.isMaxRadius?Math.max(this.center.distanceTo(this.pt1),this.center.distanceTo(this.pt2)):this.center.distanceTo(this.pt2)+(r&&this.offsetDist||0)}getStartPoint(r=I.getCurrentDraw()){let i=this.pt1;this.pt2;let o=this.center,u=i;typeof this.offsetDist>"u"&&(this.offsetDist=r.screenCoordLong2World(40));const a=this.offsetDist,l=i.clone().sub(o).normalize(),s=this.getRadius()+a;return u=o.clone().add(l.multiplyScalar(s)),u.setZ(i.z),u}getEndPoint(r=I.getCurrentDraw()){let i=this.pt2,o=this.center,u=i;typeof this.offsetDist>"u"&&(this.offsetDist=r.screenCoordLong2World(40));const a=this.offsetDist,l=i.clone().sub(o).normalize(),s=this.getRadius()+a;return u=o.clone().add(l.multiplyScalar(s)),u.setZ(i.z),u}worldDraw(r){const{dDashArray:i,dDashRatio:o}=this.getDash();let u=r.getMxObject();r.setDash(i,o),r.setLineWidth(.7),this.toSmallcoord(u,this.pt2);let a=this.center;typeof this.offsetDist>"u"&&(this.offsetDist=u.screenCoordLong2World(40));const l=this.offsetDist,s=this.getStartPoint(),t=this.getEndPoint(),w=this.getRadius()+l,{startAngle:D,endAngle:b}=this.calculateLineAngle(a,s,t);let d=this.getClockwise();const m=new THREE.ArcCurve(a.x,a.y,w,D,b,d),x=new THREE.Geometry().setFromPoints(m.getPoints(50)),g=Se.createDashedLines([s,a,t],8421504,.1,.1);if(r.setColor(8421504),r.drawEntity(g),r.drawGeometryLines(x),this.isDrawsMallRound){const A=u.screenCoordLong2World(2);r.drawCircle(s,A),r.drawCircle(t,A)}const c=this.getAngle(),y=THREE.MathUtils.radToDeg(c).toFixed(3)+"°",{x:h,y:P}=m.getPointAt(.5),p=new THREE.Vector3(h,P);if(r.getType()===ht.kWorldDraw){const A=new wt;A.opacity=1,A.text=y,A.backgroundColor=16777185,A.color=16,A.position=p,A.setFontSize(36),A.height=u.screenCoordLong2World(16),A.setLineWidthByPixels(!0),A.setUseSmallcoordDisplay(this.use_smallcoord_display),A.worldDraw(r)}else{const A=Se.creatTextSprite(y,p,u.screenCoordLong2World(16),0,16777215);A&&r.drawEntity(A)}}getGripPoints(){return[this.pt1,this.pt2,this.center]}moveGripPointsAt(r,i){return r===0&&this.pt1.add(i),r===1&&this.pt2.add(i),r===2&&this.center.add(i),!0}dwgIn(r){return this.dwgInHelp(r,["fixedSize","isDrawsMallRound","offsetDist","pt1","pt2","center","isClockwise","isMaxRadius","radius"]),!0}dwgOut(r){return this.dwgOutHelp(r,["fixedSize","isDrawsMallRound","offsetDist","pt1","pt2","center","isClockwise","isMaxRadius","radius"]),r}}function $e(n,e,r){if(n.isEqualTo(e))return 0;let i=n.c().addvec(e.c().sub(n).mult(.5)),o=e.c().sub(n);o.rotateBy(Math.PI/2,Z.kZAxis);let u=new H(i,i.c().addvec(o)),a=r.c();a.rotateBy(Math.PI/2,Z.kZAxis);let l=new H(n,n.c().addvec(a)),s=u.IntersectWith(l,Te.Intersect.kExtendBoth);if(s.isEmpty())return 0;let t=s.at(0),w=t.distanceTo(n);o.normalize(),o.mult(w);let D=t.c().addvec(o),b=t.c().subvec(o),d=D.c().sub(n),m=b.c().sub(n),x=D;return d.angleTo1(r)>m.angleTo1(r)&&(x=b),U.calcBulge(n,x,e).val}const pt=async(n,e)=>{let r=!0;const i=new Y;let o=!1;const u=d=>{d.key==="Control"&&(o=!0)},a=N.getCurrentMxCAD(),l=()=>o=!1;window.addEventListener("keydown",u),window.addEventListener("keyup",l);const s=()=>{window.removeEventListener("keydown",u),window.removeEventListener("keyup",l)};if(!n){i.clearLastInputPoint(),i.setMessage(f("396"));const d=await i.go();if(i.getDetailedResult()===T.kNewCommadIn)return s(),!1;if(!d)return s();n=d}const t=[{point:n}];for(;;)if(r){i.setMessage(f("397")),i.setKeyWords(`[${f("284")}(A)/${t.length>2?f("398")+"(C)/":""}${f("373")}(U)/${f("399")}(T)]`),i.setUserDraw((m,x)=>{const g=new _;t.forEach(({point:c,bulge:y})=>{g.addVertexAt(c,y)}),g.addVertexAt(m),x.drawMcDbEntity(g)});const d=await i.go();if(i.getDetailedResult()===T.kNullEnterIn||i.getDetailedResult()===T.kNullSpaceIn){const m=new _;t.forEach(({point:c,bulge:y})=>{m.addVertexAt(c,y)});const x=m.getLength().val,g=a.drawEntity(m);return e&&e(t),{dist:x,pl:m,plId:g}}if(i.getDetailedResult()===T.kNewCommadIn)return s(),!1;if(i.getStatus()===$.kCancel||i.getStatus()===$.kNone)return s();if(i.isKeyWordPicked("A")){r=!1;continue}else if(i.isKeyWordPicked("U")){t.length===0?I.acutPrintf(f("400")):(t.pop(),t[t.length-1]?.point&&i.setLastInputPoint(t[t.length-1].point),e&&e(t));continue}else if(i.isKeyWordPicked("T")){const m=new _;t.forEach(({point:c,bulge:y})=>{m.addVertexAt(c,y)});const x=m.getLength().val,g=a.drawEntity(m);return e&&e(t),{dist:x,pl:m,plId:g}}else if(i.isKeyWordPicked("C")){const m=new _;m.isClosed=!0,t.forEach(({point:c,bulge:y})=>{m.addVertexAt(c,y)});const x=m.getLength().val;e&&e(t);const g=a.drawEntity(m);return{dist:x,pl:m,plId:g}}else if(d){t.push({point:d}),e&&e(t);continue}}else{const d=i;d.setMessage(fe("NO1_ID_ARX_PL5",`${f("401")}(${f("402")})`)),d.setKeyWords(`[${f("281")}(A)/${f("403")}(CE)/${t.length>2?f("398")+"(C)/":""}${f("247")}(D)/${f("283")}(L)/${f("280")}(R)/${f("404")}(S)/${f("373")}(U)]`);let m=new Z;if(t.length<2)m.copy(Z.kXAxis);else{let c=t.length,y=t[c-2].point,h=t[c-2].bulge,P=t[c-1].point;if(!h||Math.abs(h)<1e-7)m=new W(P.x,P.y,0).sub(new W(y.x,y.y,0));else{let p=new _;p.addVertexAt(y,h),p.addVertexAt(P);let A=p.getFirstDeriv(new W(P.x,P.y,0));A.ret?m=A.val:m.copy(Z.kXAxis)}}let x=t[t.length-1];d.setUserDraw((c,y)=>{let h=$e(x.point,c,o?m.clone().negate():m),P=new _;t.forEach(({point:p,bulge:A})=>{P.addVertexAt(p,A)}),P.addVertexAt(x.point,h),P.addVertexAt(c),y.drawMcDbEntity(P)});let g=await d.go();if(d.getDetailedResult()===T.kNullEnterIn||d.getDetailedResult()===T.kNullSpaceIn){const c=new _;t.forEach(({point:P,bulge:p})=>{c.addVertexAt(P,p)});const y=c.getLength().val,h=a.drawEntity(c);return e&&e(t),{dist:y,pl:c,plId:h}}if(g!==null){let c={point:g,bulge:0};t[t.length-1].bulge=$e(x.point,c.point,o?m.clone().negate():m),t.push(c),e&&e(t)}else if(d.getStatus()==$.kKeyWord){if(d.isKeyWordPicked("A")){const c=new re;c.setBasePt(x.point),c.setMessage(fe("ID_ARX_PLGET_I_ANGLE",f("405")));const y=await c.go();if(c.getDetailedResult()===T.kNewCommadIn)return s(),!1;if(y===null)break;d.setMessage(`${f("401")}(${f("402")})`),d.setKeyWords(`[${f("403")}(CE)/${f("280")}(R)]`),d.setLastInputPoint(x.point);const h=t[t.length-1].point,P=M=>{const C=Math.PI/2-y/2,L=new W((M.x+h.x)/2,(M.y+h.y)/2),v=L.distanceTo(h),E=v/Math.sin(C),k=v/Math.tan(C),S=L.sub(h).rotateBy(Math.PI/2).normalize().mult(o?-E-k:E-k),R=L.addvec(S);return U.calcBulge(h,R,M).val};d.setUserDraw((M,C)=>{const L=P(M);let v=new _;t.forEach(({point:E,bulge:k})=>{v.addVertexAt(E,k)}),v.addVertexAt(x.point,L),v.addVertexAt(M),C.drawMcDbEntity(v)});const p=await d.go();if(d.isKeyWordPicked("CE")){d.setMessage(f("406")),d.setKeyWords(""),d.setBasePt(h),d.setUserDraw((E,k)=>{const S=E.distanceTo(h),R=E.addvec(E.sub(h).rotateBy(y).normalize().mult(S)),F=P(R);let K=new _;t.forEach(({point:V,bulge:O})=>{K.addVertexAt(V,O)}),K.addVertexAt(x.point,F),K.addVertexAt(R),k.drawMcDbEntity(K)});const M=await d.go();if(d.getDetailedResult()===T.kNewCommadIn)return s(),!1;if(!M)break;const C=M.distanceTo(h),L=M.addvec(M.sub(h).rotateBy(y).normalize().mult(C));let v={};v.bulge=0,v.point=L,t[t.length-1].bulge=P(L),t.push(v),d.setLastInputPoint(L),e&&e(t);continue}if(d.isKeyWordPicked("R")){const M=new Ce;M.setMessage(f("407")),M.setKeyWords("");const C=await M.go();if(M.getDetailedResult()===T.kNewCommadIn)return s(),!1;if(typeof C!="number")break;const L=C*Math.sin(y/2);d.setBasePt(h),d.setMessage(`${f("408")}(${f("409")})`),d.setKeyWords(""),d.setUserDraw((S,R)=>{const F=h.clone().addvec(S.sub(h).normalize().mult(L*2)),K=P(F);let V=new _;t.forEach(({point:O,bulge:j})=>{V.addVertexAt(O,j)}),V.addVertexAt(x.point,K),V.addVertexAt(F),R.drawMcDbEntity(V)});const v=await d.go();if(d.getDetailedResult()===T.kNewCommadIn)return s(),!1;if(!v)break;const E=h.clone().addvec(v.sub(h).normalize().mult(L*2));let k={};k.bulge=0,k.point=E,t[t.length-1].bulge=P(E),t.push(k),e&&e(t);continue}if(!p)break;let A={};A.bulge=0,A.point=p,t[t.length-1].bulge=P(p),t.push(A),e&&e(t)}if(d.isKeyWordPicked("CE")){d.setMessage(f("406")),d.setKeyWords(""),d.setUserDraw(()=>{});const c=await d.go();if(d.getDetailedResult()===T.kNewCommadIn)return s(),!1;if(!c)return s();const y=x.point,h=c.distanceTo(y);d.setLastInputPoint(y),d.setMessage(`${f("401")}(${f("402")})`),d.setKeyWords(`[${f("281")}(A)/${f("410")}(L)]`),d.clearLastInputPoint(),d.setUserDraw((k,S)=>{S.drawLine(k.toVector3(),c.toVector3());const R=c.clone().addvec(k.sub(c).normalize().mult(h)),F=c.sub(y).angleTo2(c.sub(R),Z.kZAxis),K=new W((y.x+R.x)/2,(y.y+R.y)/2),V=c.sub(K).normalize().mult(-h),O=c.clone().addvec((o?F<Math.PI:F>Math.PI)?V.negate():V),j=U.calcBulge(y,O,R).val;let X=new _;t.forEach(({point:ee,bulge:le})=>{X.addVertexAt(ee,le)}),X.addVertexAt(x.point,j),X.addVertexAt(R),S.drawMcDbEntity(X)});const P=await d.go();if(d.isKeyWordPicked("A")){const k=new re;k.setBasePt(c),k.setMessage(fe("ID_ARX_PLGET_I_ANGLE",`${f("405")}(${f("402")})`)),k.setKeyWords(""),k.setUserDraw((ee,le)=>{const ne=Z.kXAxis.clone().angleTo2(ee.sub(c),Z.kZAxis),se=o?Math.PI*2-ne/2:ne/2,ge=y.sub(c).rotateBy(se).normalize().mult(h),oe=y.sub(c).rotateBy(ne).normalize().mult(h),xe=c.clone().addvec(ge),Q=c.clone().addvec(oe),Pe=U.calcBulge(y,xe,Q).val;let de=new _;t.forEach(({point:pe,bulge:Ae})=>{de.addVertexAt(pe,Ae)}),de.addVertexAt(x.point,Pe),de.addVertexAt(Q),le.drawMcDbEntity(de)});const S=await k.go();if(k.getDetailedResult()===T.kNewCommadIn)return s(),!1;if(S===null)break;const R=o?Math.PI*2-S/2:S/2,F=y.sub(c).rotateBy(R).normalize().mult(h),K=y.sub(c).rotateBy(S).normalize().mult(h),V=c.clone().addvec(F),O=c.clone().addvec(K),j=U.calcBulge(y,V,O).val;let X={};X.bulge=0,X.point=O,t[t.length-1].bulge=j,t.push(X),new _,e&&e(t);continue}if(d.isKeyWordPicked("L")){const k=new Ce;k.setMessage(`${f("411")}(${f("402")})`),k.setKeyWords(""),k.setBasePt(y),k.setUserDraw((X,ee)=>{const le=X.distanceTo(y);if(le>h*2)return;const ne=Math.asin(le/2/h)*2,se=y.sub(c).normalize().mult(h),ge=c.clone().addvec(se.clone().rotateBy(o?Math.PI-ne/2:ne/2)),oe=c.clone().addvec(se.clone().rotateBy(ne)),xe=U.calcBulge(y,ge,oe).val;let Q=new _;t.forEach(({point:Pe,bulge:de})=>{Q.addVertexAt(Pe,de)}),Q.addVertexAt(x.point,xe),Q.addVertexAt(oe),ee.drawMcDbEntity(Q)});const S=await k.go();if(k.getDetailedResult()===T.kNewCommadIn)return s(),!1;if(typeof S!="number")break;if(S>h*2){I.acutPrintf(`*${f("412")} ${f("413")}*`),d.setLastInputPoint(y);continue}const R=Math.asin(S/2/h)*2,F=y.sub(c).normalize().mult(h),K=c.clone().addvec(F.clone().rotateBy(o?Math.PI-R/2:R/2)),V=c.clone().addvec(F.clone().rotateBy(R)),O=U.calcBulge(y,K,V).val;let j={};j.bulge=0,j.point=V,t[t.length-1].bulge=O,t.push(j),d.setLastInputPoint(V),e&&e(t);continue}if(d.getDetailedResult()===T.kNewCommadIn)return s(),!1;if(!P)return s();const p=c.clone().addvec(P.sub(c).normalize().mult(h)),A=c.sub(y).angleTo2(c.sub(p),Z.kZAxis),M=new W((y.x+p.x)/2,(y.y+p.y)/2),C=c.sub(M).normalize().mult(-h),L=c.clone().addvec((o?A<Math.PI:A>Math.PI)?C.negate():C),v=U.calcBulge(y,L,p).val;let E={};E.bulge=0,E.point=p,t[t.length-1].bulge=v,t.push(E),d.setLastInputPoint(p),e&&e(t);continue}if(d.isKeyWordPicked("R")){let c=new Ce;c.setMessage(fe("ID_ARX_PLGETSTARTRADIUS",f("414"))),c.setKeyWords("");let y=await c.go();if(c.getDetailedResult()===T.kNewCommadIn)return s(),!1;if(y===null)break;const h=x.point;d.setLastInputPoint(h),d.setMessage(`${f("401")}(${f("402")})`),d.setKeyWords(`[${f("281")}(A)]`),d.setUserDraw((E,k)=>{if(!y)return;const S=E.distanceTo(h);if(S>y*2)return;const R=Math.acos(S/2/y),F=h.clone().addvec(E.sub(h).rotateBy(R).normalize().mult(y)),K=F.clone().addvec(new W((h.x+E.x)/2,(h.y+E.y)/2).sub(F).normalize().mult(o?-y:y)),V=U.calcBulge(h,K,E).val;let O=new _;t.forEach(({point:j,bulge:X})=>{O.addVertexAt(j,X)}),O.addVertexAt(x.point,V),O.addVertexAt(E),k.drawMcDbEntity(O)});const P=await d.go();if(d.isKeyWordPicked("A")){const E=new re;E.setMessage(fe("ID_ARX_PLGET_I_ANGLE",f("405"))),E.setKeyWords(""),E.setBasePt(h);const k=await E.go();if(E.getDetailedResult()===T.kNewCommadIn)return s(),!1;if(typeof k!="number")break;const S=Math.sin(k/2)*y*2;d.setMessage(`${f("408")}(${f("402")})`),d.setKeyWords(""),d.setBasePt(h),d.setUserDraw((ee,le)=>{if(!y)return;const ne=ee.sub(h).normalize(),se=h.clone().addvec(ne.clone().mult(S)),ge=se.clone().addvec(ne.clone().negate().rotateBy(-(Math.PI/2-k/2)).mult(y)),oe=ge.clone().addvec(ge.sub(new W((h.x+se.x)/2,(h.y+se.y)/2)).normalize().mult(o?y:-y)),xe=U.calcBulge(h,oe,se).val;let Q=new _;t.forEach(({point:Pe,bulge:de})=>{Q.addVertexAt(Pe,de)}),Q.addVertexAt(x.point,xe),Q.addVertexAt(se),le.drawMcDbEntity(Q)});const R=await d.go();if(d.getDetailedResult()===T.kNewCommadIn)return s(),!1;if(!R)break;const F=R.sub(h).normalize(),K=h.clone().addvec(F.clone().mult(S)),V=K.clone().addvec(F.clone().negate().rotateBy(-(Math.PI/2-k/2)).mult(y)),O=V.clone().addvec(V.sub(new W((h.x+K.x)/2,(h.y+K.y)/2)).normalize().mult(o?y:-y)),j=U.calcBulge(h,O,K).val;let X={};X.bulge=0,X.point=K,t[t.length-1].bulge=j,t.push(X),e&&e(t);continue}if(d.getDetailedResult()===T.kNewCommadIn)return s(),!1;if(!P)break;const p=P.distanceTo(h);if(p>y*2){I.acutPrintf(`${f("401")} *${f("413")}*`);continue}const A=Math.acos(p/2/y),M=h.clone().addvec(P.sub(h).rotateBy(A).normalize().mult(y)),C=M.clone().addvec(new W((h.x+P.x)/2,(h.y+P.y)/2).sub(M).normalize().mult(o?-y:y)),L=U.calcBulge(h,C,P).val;let v={};v.bulge=0,v.point=P,t[t.length-1].bulge=L,t.push(v),e&&e(t);continue}if(d.isKeyWordPicked("D")){d.setMessage(f("415")),d.setKeyWords(""),d.setUserDraw((p,A)=>{A.drawLine(p.toVector3(),x.point.toVector3())});const c=await d.go();if(d.getDetailedResult()===T.kNewCommadIn)return s(),!1;if(!c)break;const y=c.sub(x.point);d.setLastInputPoint(x.point),d.setMessage(`${f("401")}(${f("402")})`),d.setKeyWords(""),d.setUserDraw((p,A)=>{let M=$e(x.point,p,o?y.clone().negate():y),C=new _;t.forEach(({point:L,bulge:v})=>{C.addVertexAt(L,v)}),C.addVertexAt(x.point,M),C.addVertexAt(p),A.drawMcDbEntity(C)});const h=await d.go();if(d.getDetailedResult()===T.kNewCommadIn)return s(),!1;let P={};P.bulge=0,P.point=h,t[t.length-1].bulge=$e(x.point,P.point,o?y.clone().negate():y),t.push(P),e&&e(t);continue}if(d.isKeyWordPicked("L"))r=!0;else if(d.isKeyWordPicked("S")){let c=new W(t[t.length-1].point.x,t[t.length-1].point.y,0),y=new Y;y.setMessage(fe("NO1_ID_SPECIFY_ARC2",f("84"))),y.setBasePt(c);let h=await y.go();if(y.getDetailedResult()===T.kNewCommadIn)return s(),!1;if(h===null)break;let P=new Y;P.setMessage(fe("ID_CIRCULAR_ENDPOINT",`${f("401")}(${f("402")})`)),P.setUserDraw((A,M)=>{const C=new J;C.computeArc(c.x,c.y,h.x,h.y,A.x,A.y);let L=h;o&&(L=C.center.clone().addvec(C.center.clone().sub(h)));let v=U.calcBulge(c,L,A).val;const E=new _;t.forEach(({point:k,bulge:S})=>{E.addVertexAt(k,S)}),E.addVertexAt(x.point,v),E.addVertexAt(A),M.drawMcDbEntity(E)});let p=await P.go();if(p!==null){let A=h;if(o){const C=new J;C.computeArc(c.x,c.y,h.x,h.y,p.x,p.y),A=C.center.clone().addvec(C.center.clone().sub(h))}let M=U.calcBulge(c,A,p);if(M.ret){let C={};C.point=p,C.dBluge=0,t[t.length-1].bulge=M.val,t.push(C),e&&e(t)}else I.acutPrintf(fe("ID_ENDPOINT_INVALID1",`
 ${f("416")} *${f("413")}*`))}else{if(P.getDetailedResult()===T.kNewCommadIn)return s(),!1;break}}else if(d.isKeyWordPicked("C")){t[t.length-1].bulge=$e(x.point,n,o?m.clone().negate():m);const c=new _;c.isClosed=!0,t.forEach(({point:P,bulge:p})=>{c.addVertexAt(P,p)});const y=c.getLength().val;e&&e(t);const h=a.drawEntity(c);return{dist:y,pl:c,plId:h}}else if(d.isKeyWordPicked("U")&&t.length>1){t.pop();const c=new _;t.forEach(({point:y,bulge:h})=>{c.addVertexAt(y,h)}),e&&e(t),t.length>0&&d.setLastInputPoint(t[t.length-1].point)}}else{if(d.getDetailedResult()===T.kNewCommadIn)return s(),!1;break}}const w=new _;t.forEach(({point:d,bulge:m})=>{w.addVertexAt(d,m)});const D=w.getLength().val;e&&e(t);const b=a.drawEntity(w);return{dist:D,pl:w,plId:b}};function nt(n,e){const r=e.x,i=e.y;return n.x>r&&n.y>i||n.x<r&&n.y<i}function Ln(n,e){const r=(n.x+e.x)/2,i=(n.y+e.y)/2;return new W(r,i)}function Tn(n,e,r){const i=nt(n,r),o=nt(e,r);return i&&o}async function At(n=!1){const e=new Y;let r=[];e.clearLastInputPoint(),e.setMessage(f("396"));const i=await e.go();if(e.getDetailedResult()===T.kNewCommadIn)return!1;if(!i)return;e.setUserDraw((u,a)=>{a.drawLine(i.toVector3(),u.toVector3())}),e.setMessage(f("434")),e.setKeyWords(`[${f("435")}(M)]`);const o=await e.go();if(e.getDetailedResult()===T.kNewCommadIn)return!1;if(e.getStatus()!==$.kCancel){if(e.isKeyWordPicked("M")){const u=await pt(i,s=>{const t=new _;s.forEach(({point:w,bulge:D})=>{t.addVertexAt(w,D)}),I.acutPrintf(f("279")+" = "+t.getLength().val)});if(u===!1)return u;const{plId:a}=u||{};if(!a)return;const l=a.getMcDbEntity();return l&&r.push(l),{markedLines:r}}else if(o){if(n){const b=Tn(i,o,Ln(i,o)),d=new ke;if(d.referenceAxis=new THREE.Vector3(0,0,b?-1:1),d.isAligned=!0,d.pt1=i.toVector3(),d.pt2=o.toVector3(),I.addToCurrentSpace(d),r.push(d),Math.abs(i.x-o.x)>.001&&Math.abs(i.y-o.y)>.001){const x=new ke;x.isAligned=!1,x.referenceAxis=new THREE.Vector3(0,i.y>o.y?1:-1,-1),x.pt1=i.toVector3(),x.pt2=o.toVector3(),I.addToCurrentSpace(x),r.push(x);const g=new ke;g.isAligned=!1,g.referenceAxis=new THREE.Vector3(i.x>o.x?1:-1,0,-1),g.pt1=i.toVector3(),g.pt2=o.toVector3(),I.addToCurrentSpace(g),r.push(g);const c=new we;c.offsetDist=0,c.center=i.toVector3(),c.pt1=i.toVector3().add(new THREE.Vector3(1,0,0)),c.pt2=o.toVector3(),I.addToCurrentSpace(c),r.push(c)}const m=new ue;m.pt1=i.toVector3(),m.pt2=o.toVector3(),I.addToCurrentSpace(m),r.push(m)}const u=i.distanceTo(o);let a=o.x-i.x,l=o.y-i.y,s=o.z-i.z,t=Math.atan2(l,a)*(180/Math.PI),w=Math.sqrt(a*a+l*l+s*s),D=w===0?0:Math.asin(s/w)*(180/Math.PI);return I.acutPrintf(`${f("279")} = ${ve(u,4)} , XY ${f("436")} = ${ve(t,4)} , ${f("437")} XY ${f("438")} = ${ve(D,4)}
 X 增量 = ${ve(a,4)}，  Y 增量 = ${ve(l,4)}，   Z 增量 = ${ve(s,4)}`),{dist:u,angleInDegrees:t,angleWithZAxis:D,deltaX:a,deltaY:l,deltaZ:s,markedLines:r}}}}B("_MxMeasurementDistance",At);async function Sn(n=!1){for(;;){const e=new ae,r=new ce;r.AddMcDbEntityTypes("CIRCLE,ARC"),e.setFilter(r),e.setMessage(f("选择圆弧或圆"));const i=await e.go();if(e.getDetailedResult()===T.kCodeAbort||e.getDetailedResult()===T.kEcsIn||e.getDetailedResult()===T.kMouseRightIn)return;if(e.getDetailedResult()===T.kNewCommadIn)return!1;if(e.getDetailedResult()===T.kNullSpaceIn||e.getDetailedResult()===T.kNullEnterIn||!i)return;const o=i.getMcDbEntity();if(o instanceof J||o instanceof q){const u=[];if(n){const{val:a,ret:l}=o.getClosestPointTo(e.getDocPickPoint(),!1);if(!l||!a)return;const s=o.center,t=new ke;t.pt1=a.toVector3(),t.pt2=s.toVector3(),t.isAligned=!0,t.offsetHeight=0,I.addToCurrentSpace(t),u.push(t)}return I.acutPrintf(f("半径")+" = "+o.radius+`
`+f("直径")+" = "+o.radius*2+`
`),{radius:o.radius,markedLines:u}}}}function Ue(n,e,r,i){const o=Re(i,n,r),u=Re(i,e,r);return isNaN(u)?!0:isNaN(o)?!1:u===o?r.distanceTo(n)>r.distanceTo(e):o<u}async function Rn(){const n=new ae,e=new ce;for(e.AddMcDbEntityTypes("CIRCLE,ARC,LINE,LWPOLYLINE");;){n.setFilter(e),n.setMessage(`${f("238")}${f("284")}、${f("268")}、${f("283")}<${f("328")}>`);const r=await n.go(),i=n.getDetailedResult(),o=[];if(n.getDetailedResult()===T.kNewCommadIn||n.getDetailedResult()===T.kCodeAbort)return!1;if(n.getDetailedResult()===T.kEcsIn)return;if(i===T.kNullEnterIn||i===T.kNullSpaceIn||i===T.kMouseRightIn){const a=new Zt,l=new we;a.setMessage(`
${f("418")}`);const s=await a.go();if(!s)return;l.center=s,a.setMessage("\n${t('330')}"),a.setUserDraw((x,g)=>{g.drawLine(x,s)});const t=await a.go();if(!t)return;l.pt1=t,a.setMessage(`
${f("331")}:`),a.setUserDraw((x,g)=>{g.drawLine(x,s),g.drawLine(t,s),l.offsetDist=0,l.pt2=x,g.drawCustomEntity(l)});const w=await a.go();if(!w)return;l.pt2=w;let D=s.distanceTo(t)-s.distanceTo(w);l.offsetDist=D<0?0:D/2;const b=new ue;b.pt1=s,b.pt2=t;const d=new ue;d.pt1=s,d.pt2=w,I.addToCurrentSpace(b),I.addToCurrentSpace(d),I.addToCurrentSpace(l);const m=l.getAngle();return I.acutPrintf(f("281")+" = "+m+"°"),o.push(l,b,d),{angle:m,markedArcs:o}}if(!r)continue;const u=r.getMcDbEntity();if(u){if(u instanceof H||u instanceof _){let a,l,s,t;const w=n.getDocPickPoint();let D;if(u instanceof _){const{start:A,end:M}=Ve(u,w,2)||{};a=A,l=M}else a=u.startPoint,l=u.endPoint;if(!a||!l)continue;for(;;){const A=new ce;A.AddMcDbEntityTypes("LINE,LWPOLYLINE"),n.setFilter(A),n.setMessage(f("332"));const M=await n.go();if(D=n.getDocPickPoint(),n.getDetailedResult()===T.kNewCommadIn||n.getDetailedResult()===T.kCodeAbort)return!1;if(n.getDetailedResult()===T.kEcsIn)return;if(!M||!M.isValid())continue;let C=M.getMcDbEntity();if(C instanceof _){const{start:L,end:v}=Ve(C,D,2)||{};s=L,t=v}else C instanceof H&&(s=C.startPoint,t=C.endPoint);if(s&&t)break}const b=new H(a,l).IntersectWith(new H(s,t),Te.Intersect.kExtendBoth);if(b.isEmpty()){I.acutPrintf(f("419"));return}if(!a||!l||!s||!t||!D)continue;const d=b.at(0),x=Ue(a,l,d,w)?a:l,c=Ue(s,t,d,w)?s:t,y=new we;y.center=d.toVector3(),y.pt1=x.toVector3(),y.pt2=c.toVector3(),y.isMaxRadius=!0,y.offsetDist=0,I.addToCurrentSpace(y);const h=y.getAngle();I.acutPrintf(f("281")+" = "+h+"°"),o.push(y);const P=new ue;P.pt1=y.pt1,P.pt2=y.center;const p=new ue;return p.pt1=y.center,p.pt2=y.pt2,I.addToCurrentSpace(P),I.addToCurrentSpace(p),o.push(P,p),{angle:h,markedArcs:o}}if(u instanceof J){const a=new we;a.offsetDist=u.radius,a.center=u.center.toVector3(),a.pt1=new THREE.Vector3(u.radius),a.pt1.applyAxisAngle(new THREE.Vector3(0,0,1),u.startAngle).add(u.center.toVector3()),a.pt2=new THREE.Vector3(u.radius),a.pt2.applyAxisAngle(new THREE.Vector3(0,0,1),u.endAngle).add(u.center.toVector3());let l=u.endAngle-u.startAngle;l>Math.PI?l-=Math.PI*2:l<-Math.PI&&(l+=Math.PI*2),a.isClockwise=Math.abs(l)>Math.PI&&l>0,I.addToCurrentSpace(a);const s=a.getAngle();I.acutPrintf(f("281")+" = "+s+"°"),o.push(a);const t=new ue;t.pt1=a.pt1,t.pt2=a.center;const w=new ue;return w.pt1=a.center,w.pt2=a.pt2,I.addToCurrentSpace(t),I.addToCurrentSpace(w),o.push(t,w),{angle:s,markedArcs:o}}if(u instanceof q){const a=new we;a.center=u.center.toVector3(),a.pt1=n.getDocPickPoint().toVector3();const l=new Y;l.setUserDraw((w,D)=>{a.pt2=w.toVector3(),a.offsetDist=0,D.drawCustomEntity(a)}),l.setMessage(f("420"));const s=await l.go();if(l.getDetailedResult()===T.kNewCommadIn)return!1;if(!s)return;a.pt2=s.toVector3(),a.offsetDist=u.radius,I.addToCurrentSpace(a);const t=a.getAngle();return I.acutPrintf(f("281")+" = "+t+"°"),o.push(a),{angle:t,markedArcs:o}}}}}const bt=n=>{const e=n.numVerts(),r=[];if(e<3)return r;const i=N.getCurrentMxCAD(),o=n.getPointAt(0).val,u=n.getPointAt(e-1).val;if(!n.isClosed&&!o.isEqualTo(u))return r;i.pathMoveTo(o.x,o.y);for(let s=0;s<e;s++){const t=n.getPointAt(s).val,w=n.getBulgeAt(s),{val1:D,val2:b}=n.getWidthsAt(s);i.pathLineToEx(t.x,t.y,D,b,w)}i.pathLineTo(u.x,u.y);const l=i.drawPathToHatch().getMcDbEntity();return l&&r.push(l),r},Vn=async n=>{const e=N.getCurrentMxCAD();if(n instanceof _)return bt(n);if(n instanceof q){e.pathMoveTo(n.center.x,n.center.y),e.pathCircle(n.center.x,n.center.y,n.radius);const i=e.drawPathToHatch().getMcDbEntity();if(i)return[i]}else{const r=n.clone(),{minPt:i,maxPt:o}=e.getDatabase().currentSpace.getBoundingBox(),u=i.clone(),a=o.clone();r.move(u,a);const l=r.getBoundingBox(),s=new W((l.minPt.x+l.maxPt.x)/2,(l.minPt.y+l.maxPt.y)/2),t=e.drawEntity(r);await(async()=>new Promise(D=>setTimeout(D,1)))();const w=U.builderHatchFromPoint(s);if(t.erase(),r.erase(),w){w.move(a,u);const b=e.drawEntity(w).getMcDbEntity();if(b)return[b]}}},_n=async()=>{const n=new Y;let e=[],r=!1,i=!1,o=0;for(;;){n.setMessage((r?`(${f("421")})`:"")+(i?`(${f("422")})`:"")+`${f("357")}<${f("208")}>`),n.setKeyWords(`[${f("208")}(O)/${r?"":f("423")+"(A)/"}${i?"":f("424")+"(S)/"}${f("425")}(X)]`),await(async()=>new Promise(a=>setTimeout(a,1)))();const u=await n.go();if(n.getDetailedResult()===T.kNewCommadIn)return e.length>0?{markedLines:e}:!1;if(n.getDetailedResult()===T.kCodeAbort)return e.length>0?{markedLines:e}:!1;if(n.getDetailedResult()===T.kEcsIn||n.getDetailedResult()===T.kMouseRightIn)return e.length>0?{markedLines:e}:void 0;if(n.isKeyWordPicked("O")||n.getDetailedResult()===T.kNullEnterIn||n.getDetailedResult()===T.kNullSpaceIn)for(;;){const a=new ae;a.setMessage(f("205")),await(async()=>new Promise(b=>setTimeout(b,1)))();const l=await a.go();if(a.getDetailedResult()===T.kNewCommadIn)return e.length>0?{markedLines:e}:!1;if(a.getDetailedResult()===T.kCodeAbort)return e.length>0?{markedLines:e}:!1;if(a.getDetailedResult()===T.kEcsIn)return e.length>0?{markedLines:e}:!1;if(a.getDetailedResult()===T.kNullEnterIn||a.getDetailedResult()===T.kNullSpaceIn||a.getDetailedResult()===T.kMouseRightIn)break;if(!l)continue;const s=l.getMcDbEntity();if(!s)continue;let t=s.getArea().val,w=0;s instanceof Le&&(w=s.getLength().val);const D=await Vn(s);if(D&&e.push(...D),t===0&&D)if(s instanceof q)t=Math.PI*Math.pow(s.radius,2);else if(s instanceof Ne&&s.startAngle===0&&s.endAngle===Math.PI*2){const b=Math.sqrt(s.majorAxis.x*s.majorAxis.x+s.majorAxis.y*s.majorAxis.y)/2,d=b*s.radiusRatio;t=Math.PI*b*d;const m=Math.pow((b-d)/(b+d),2);w=Math.PI*(b+d)*(1+3*m/(10+Math.sqrt(4-3*m)))}else t=D.reduce((b,d)=>b+d.getArea().val,0);if(t===0?I.acutPrintf(f("426")+`
`):I.acutPrintf(`${f("234")} = ${t}, ${f("427")} = ${w}
`),r||i){r?o+=t:o-=t,I.acutPrintf(`${f("428")} = ${o}
`);continue}return{markedLines:e}}if(n.isKeyWordPicked("A")){r=!0,i=!1;continue}if(n.isKeyWordPicked("S")){i=!0,r=!1;continue}if(n.isKeyWordPicked("X"))return I.acutPrintf(`${f("428")} = ${o}
`),e.length>0?{markedLines:e}:void 0;if(u){const a=await pt(u);if(a===!1)return e.length>0?{markedLines:e}:a;const{plId:l}=a||{};if(!l)return e.length>0?{markedLines:e}:void 0;const s=l.getMcDbEntity();if(s instanceof _){s.isClosed=!0,e.push(s);const t=bt(s);e.push(...t);const w=s.getArea().val;if(I.acutPrintf(`${f("234")} = ${s.getArea().val}, ${f("427")} = ${s.getLength().val}
`),r||i){r?o+=w:o-=w,I.acutPrintf(`${f("428")} = ${o}
`);continue}return{markedLines:e}}}}};class $n extends ue{getTypeName(){return"MxSelectLine"}worldDraw(e){e.setLineWidthByPixels(!0),e.setLineWidth(10),e.setColor(16737894),e.drawSelectLine(this.pt1,this.pt2)}}function Dt(n,e,r){const o=n.distanceTo(e)/2,u=o*r,a=(o*o+u*u)/(2*u),l=(n.x+e.x)/2,s=(n.y+e.y)/2;return new W(l,s).addvec(e.clone().sub(n).perpVector().normalize().mult(a-u))}function Wn(n,e,r,i){const o=e.clone().sub(n),u=r.clone().sub(n),a=o.length(),l=new THREE.Vector3().crossVectors(o,u);typeof i>"u"&&(i=l.dot(new THREE.Vector3(0,0,1))<0);let s=o.clone().add(u);return i&&(s=s.negate()),s=s.normalize(),n.clone().add(s.multiplyScalar(a))}B("_MEASUREGEOM",async()=>{let n,e,r=f("移动光标"),i=[];const o=()=>{i.forEach(u=>{u.erase()})};try{for(;;){const u=new Y;u.setCursorType(qt.kCross),u.disableAllTrace(),u.setDisableDynInput(!0),u.clearLastInputPoint(),u.setMessage(r);const a=`[${f("距离")}(D)/${f("半径")}(R)/${f("角度")}(A)/${f("面积")}(AR)/${f("快速")}(Q)/${f("模式")}(M)/${f("退出")}(X)]`;u.setKeyWords(a);const l=N.getCurrentMxCAD(),s=new gt,t=new ce([Ot.kEntityType,"LINE,ARC,CIRCLE,LWPOLYLINE,INSERT"]),w=(g,c,y)=>{let h,P,p;for(let A=0;A<g.length;A++){let C=g[A].getMcDbEntity();if(!C)continue;if(C instanceof Ht){const k=C.blockTableRecordId.getMcDbBlockTableRecord();if(!k)continue;return w(k.getAllEntityId(),c.clone(),y.clone())}if(!(C instanceof Le))continue;const L=C.IntersectWith(new H(c,y),Te.Intersect.kOnBothOperands);if(C instanceof _&&C.isClosed){const k=C.numVerts()-1,S=C.getPointAt(0).val,R=C.getPointAt(k).val,F=new H(S,R).IntersectWith(new H(c,y),Te.Intersect.kOnBothOperands);if(!F.isEmpty()){const K=F.at(0),V=K.distanceTo(c);(typeof h>"u"||V<h)&&(P=C,h=V,p=K)}}if(L.isEmpty())continue;let v,E;L.forEach(k=>{const S=k.distanceTo(c);(typeof E>"u"||S<E)&&(E=S,v=k)}),(typeof h>"u"||E<h)&&(P=C,h=E,p=v)}if(P)return{minDistEnt:P,minDist:h,intersectPt:p}},D=(g,c)=>(s.crossingSelect(g.x,g.y,c.x,c.y,t),s.isNull()?void 0:w(s.getIds(),g,c)),b=(g,c,y,h)=>{const P=c.clone().addvec(y.clone().mult(I.viewCoordLong2Cad(16))),p=Se.createDashedLines([c.toVector3(),P.toVector3()],8421504,.1,.1),A=c.clone().addvec(y.clone().mult(I.viewCoordLong2Cad(28)));g.drawEntity(p),g.drawText(h,I.viewCoordLong2Cad(16),0,A.toVector3())},d=(g,c,y,h,P)=>{let p;if(c instanceof _||c instanceof H){let A,M;if(c instanceof H&&(A=c.startPoint,M=c.endPoint),c instanceof _){const C=Ve(c,P,10);if(!C)return;const L=c.getBulgeAt(C.startIndex);if(L===0)A=C.start,M=C.end;else{const v=c.getParamAtPoint(C.start).val,E=c.getParamAtPoint(C.end).val,k=c.getDistAtParam(v).val,S=c.getDistAtParam(E).val,R=Dt(C.start,C.end,L);g.drawCircle(C.end.toVector3(),5);const{x:F,y:K}=Wn(R.toVector3(),C.start.toVector3(),C.end.toVector3()),V=new W(F,K);let O=V.clone().sub(R).normalize();const j=R.distanceTo(V);Math.abs(L)>1&&(V.addvec(R.clone().sub(V).normalize().mult(j*2)),O.negate()),b(g,V,O,Math.abs(S-k).toFixed(3))}}if(A&&M){const C=new $n;C.pt1=A.toVector3(),C.pt2=M.toVector3(),g.drawCustomEntity(C);const L=new ke;L.pt1=A.toVector3(),L.pt2=M.toVector3(),L.isAligned=!0,p=(E=h.x>y.x||h.y<y.y)=>{L.referenceAxis=new THREE.Vector3(0,0,E?1:-1),g.drawCustomEntity(L)};const v=new H(A,M);return{draw:p,line:v}}}if(c instanceof q){const A=new Z(-1,0,0),M=c.center.clone().addvec(A.clone().mult(c.radius));b(g,M,A,c.getLength().val.toFixed(3))}if(c instanceof J){const M=c.getGripPoints().at(2);b(g,M,M.sub(c.center).normalize(),c.getLength().val.toFixed(3))}},m=(g,c,y,h,P)=>{if(!h||!P||h.startPoint.isEqualTo(P.startPoint)&&h.endPoint.isEqualTo(P.endPoint))return;const p=h.IntersectWith(P,Te.Intersect.kOnBothOperands);if(p.isEmpty())return;const A=new we;A.offsetDist=0;const M=p.at(0),C=Ue(h.startPoint,h.endPoint,M,c),L=Ue(P.startPoint,P.endPoint,M,y);C?A.pt1=h.startPoint.toVector3():A.pt1=h.endPoint.toVector3(),L?A.pt2=P.startPoint.toVector3():A.pt2=P.endPoint.toVector3(),A.center=M.toVector3();const v=A.getAngle();if(v===Math.PI/2){g.setColor(16760576),A.radius=Math.min(I.viewCoordLong2Cad(10),Math.min(h.getLength().val,P.getLength().val)/10);const E=A.getRadius(!0),k=A.getStartPoint().clone().add(A.center.clone().sub(A.pt1).normalize().negate().multiplyScalar(E)),S=A.getEndPoint().clone().add(A.center.clone().sub(A.pt2).normalize().negate().multiplyScalar(E)),R=new THREE.Vector3(k.x+S.x-A.center.x,k.y+S.y-A.center.y);g.drawLine(k,R),g.drawLine(S,R)}else A.radius=I.viewCoordLong2Cad(26),g.drawCustomEntity(A);return v},x=(g,c,y,h,P,p)=>{const A=new H(c,y),M=new H(c,h),C=new H(c,P),L=new H(c,p),v=D(c,y),E=D(c,h),k=D(c,P),S=D(c,p);v&&(A.endPoint=v.intersectPt),E&&(M.endPoint=E.intersectPt),k&&(C.endPoint=k.intersectPt),S&&(L.endPoint=S.intersectPt),g.setColor(16777215);const{line:R,draw:F}=v&&d(g,v.minDistEnt,c,y,v.intersectPt)||{},{line:K,draw:V}=E&&d(g,E.minDistEnt,c,h,E.intersectPt)||{},{line:O,draw:j}=k&&d(g,k.minDistEnt,c,P,k.intersectPt)||{},{line:X,draw:ee}=S&&d(g,S.minDistEnt,c,p,S.intersectPt)||{},le=v&&k&&m(g,v.intersectPt,k.intersectPt,R,O),ne=v&&S&&m(g,v.intersectPt,S.intersectPt,R,X),se=E&&k&&m(g,E.intersectPt,k.intersectPt,K,O),ge=E&&S&&m(g,E.intersectPt,S.intersectPt,K,X);v&&E&&m(g,v.intersectPt,E.intersectPt,R,K),k&&S&&m(g,k.intersectPt,S.intersectPt,O,X);const oe=Math.PI/2,xe=le===oe&&ne===oe&&se===oe&&ge===oe;function Q(z){return z.startPoint.x===z.endPoint.x?1/0:(z.endPoint.y-z.startPoint.y)/(z.endPoint.x-z.startPoint.x)}const Pe=(z,te)=>{const Ee=Q(z),be=Q(te);return Ee===be},de=(z,te)=>{const Ee=z.getLength().val,be=te.getLength().val;return Ee===be},pe=(z,te)=>Pe(z,te)&&de(z,te),Ae=[];R&&Ae.push({line:R,draw:F,info:v}),K&&Ae.push({line:K,draw:V,info:E}),O&&Ae.push({line:O,draw:j,info:k}),X&&Ae.push({line:X,draw:ee,info:S}),Ae.reduce((z,te)=>{let Ee=!1;for(let be=0;be<z.length;be++){const qe=z[be];if(pe(qe[0].line,te.line)){qe.push(te),Ee=!0;break}}return Ee||z.push([te]),z},[]).forEach(z=>{if(z.length===1){if(pe(z[0].line,new H(A.endPoint,M.endPoint))||pe(z[0].line,new H(C.endPoint,L.endPoint)))return;z[0].draw&&z[0].draw()}else if(!xe)if(z.length===2){if(pe(z[1].line,new H(A.endPoint,M.endPoint))||pe(z[1].line,new H(C.endPoint,L.endPoint)))return;v&&E||k&&S?(v&&E&&(v.intersectPt.distanceTo(c)<E.intersectPt.distanceTo(c)?F&&F(!0):V&&V()),k&&S&&(k.intersectPt.distanceTo(c)<S.intersectPt.distanceTo(c)?j&&j(!1):ee&&ee())):(z[0].draw&&z[0].draw(),z[1].draw&&z[1].draw())}else z.forEach(({draw:te})=>{te&&te()})}),g.setColor(16760576),g.drawMcDbEntity(A),g.drawMcDbEntity(M),g.drawMcDbEntity(C),g.drawMcDbEntity(L);const ze=I.viewCoordLong2Cad(16);if(v&&E){const z=A.endPoint.distanceTo(M.endPoint);A.endPoint.distanceTo(c)>M.endPoint.distanceTo(c)?g.drawText(z.toFixed(3),ze,0,new THREE.Vector3((c.x+A.endPoint.x)/2,(c.y+A.endPoint.y)/2)):g.drawText(z.toFixed(3),ze,0,new THREE.Vector3((c.x+M.endPoint.x)/2,(c.y+M.endPoint.y)/2))}if(k&&S){const z=C.endPoint.distanceTo(L.endPoint);C.endPoint.distanceTo(c)>L.endPoint.distanceTo(c)?g.drawText(z.toFixed(3),ze,0,new THREE.Vector3((c.x+C.endPoint.x)/2,(c.y+C.endPoint.y)/2)):g.drawText(z.toFixed(3),ze,0,new THREE.Vector3((c.x+L.endPoint.x)/2,(c.y+L.endPoint.y)/2))}};if(u.setUserDraw((g,c)=>{const y=I.viewCoordLong2Cad(l.mxdraw.getViewWidth()),h=I.viewCoordLong2Cad(l.mxdraw.getViewHeight()),P=new W(-y,g.y),p=new W(g.x+y,g.y),A=new W(g.x,g.y+h),M=new W(g.x,-h);x(c,g,P,p,A,M)}),await u.go(),n=u.keyWordPicked(),u.getStatus()===$.kCancel||u.getDetailedResult()===T.kCodeAbort||u.getDetailedResult()===T.kEcsIn)break;if((u.getDetailedResult()===T.kMouseRightIn||u.getDetailedResult()===T.kNullSpaceIn||u.getDetailedResult()===T.kNullEnterIn)&&(n=e),n?.toLocaleUpperCase()==="D"){o();const g=await At(!0);if(g===!1)break;const{markedLines:c}=g||{};c&&i.push(...c),await(async()=>new Promise(y=>setTimeout(y,1)))()}if(n?.toLocaleUpperCase()==="R"){o();const g=await Sn(!0);if(g===!1)break;const{markedLines:c}=g||{};c&&i.push(...c),await(async()=>new Promise(y=>setTimeout(y,1)))()}if(n?.toLocaleUpperCase()==="A"){o();const g=await Rn();if(g===!1)break;const{markedArcs:c}=g||{};c&&i.push(...c),await(async()=>new Promise(y=>setTimeout(y,1)))()}if(n?.toLocaleUpperCase()==="AR"){o();const g=await _n();if(g===!1)break;const{markedLines:c}=g||{};c&&i.push(...c),await(async()=>new Promise(y=>setTimeout(y,1)))()}if(n?.toLocaleUpperCase()==="Q"&&o(),n?.toLocaleUpperCase()==="X")break;a.includes(n)&&n!==""&&(e=n)}}catch(u){console.warn(u)}o()});async function Nn(){const n=new Y;n.setMessage(f("417"));const e=await n.go();e&&I.acutPrintf(`X = ${e.x}     Y = ${e.y}     Z = ${e.z} 
`)}B("ID",Nn);function ie(n,e,r,i){const o=n-r,u=e-i;let a=0;return o==0?a=Math.PI/2:a=Math.atan(Math.abs(u/o)),o<0&&u>=0?a=Math.PI-a:o<0&&u<0?a=Math.PI+a:o>=0&&u<0&&(a=Math.PI*2-a),a-Math.PI}function zn(n,e,r,i=!1){let o={x:e.x-n.x,y:e.y-n.y},u={x:r.x-n.x,y:r.y-n.y},a=o.x*o.x+o.y*o.y;if(a===0||i){let l=a===0?0:(u.x*o.x+u.y*o.y)/a;return new W(n.x+o.x*l,n.y+o.y*l)}else{let l=(u.x*o.x+u.y*o.y)/a;return l<0?n:l>1?e:new W(n.x+o.x*l,n.y+o.y*l)}}function Kn(n,e,r){let i={x:e.x-n.x,y:e.y-n.y},o={x:r.x-n.x,y:r.y-n.y},u=i.x*i.x+i.y*i.y;if(u===0)return r.x===n.x&&r.y===n.y;let a=(o.x*i.x+o.y*i.y)/u;return a>=0&&a<=1?!0:a<0?{isStart:!0}:{isStart:!1}}let Oe=0,He=0,Fn=0,st=0,it=0;const Bn={DE:f("166"),P:f("820"),T:f("399"),DY:f("821")};let De="DE";const We=(n,e,r,i)=>{const o=n.getLength().val,u=n.getDistAtPoint(r).val;let a=n.getStartPoint().val,l=n.getEndPoint().val;const s=u<o/2;let t;if(n instanceof H){const w=n.startPoint,D=n.endPoint;return s?(n.startPoint=a.clone().addvec(a.sub(l).normalize().mult(e)),t=()=>{n.startPoint=w}):(n.endPoint=n.endPoint.clone().addvec(l.sub(a).normalize().mult(e)),t=()=>{n.endPoint=D}),{ent:n,fallback:t}}if(n instanceof J){const w=n.startAngle,D=n.endAngle,b=n.clone();s?(b.startAngle=n.startAngle,b.endAngle=n.startAngle-1e-7):(b.startAngle=n.endAngle,b.endAngle=n.endAngle-1e-7);const d=b.getLength().val,m=n.center,x=n.getGripPoints().at(2);let c=U.calcBulge(a,x,l).val>0,y;if(s)if(y=b.getPointAtDist(c?d-e:e).val,c)n.startAngle=ie(m.x,m.y,y.x,y.y),t=()=>{n.startAngle=w};else{const h=n.endAngle;n.startAngle=h,n.endAngle=ie(m.x,m.y,y.x,y.y),t=()=>{n.startAngle=w,n.endAngle=D}}else if(y=b.getPointAtDist(c?e:d-e).val,c)n.endAngle=ie(m.x,m.y,y.x,y.y),t=()=>{n.endAngle=D};else{const h=n.startAngle;n.startAngle=ie(m.x,m.y,y.x,y.y),n.endAngle=h,t=()=>{n.startAngle=w,n.endAngle=D}}return{fallback:t,ent:n}}if(n instanceof Ne){const w=n.clone();w.startAngle=0,w.endAngle=Math.PI*2;const b=w.getLength().val-o;if(e>b)return;const d=w.getPointAtDist(0).val,m=w.getPointAtDist(s?w.getDistAtPoint(a).val-e:w.getDistAtPoint(l).val+e).val,x=d.sub(w.center),g=m.sub(w.center),c=Math.atan2(x.y,x.x);let h=Math.atan2(g.y,g.x)-c;h<0&&(h+=2*Math.PI);const P=n.startAngle,p=n.endAngle;return s?n.startAngle=h:n.endAngle=h,{fallback:()=>{s?n.startAngle=P:n.endAngle=p},ent:n}}if(n instanceof _){if(n.isClosed)return;const w=h=>{const P=[];for(let p=0;p<h.numVerts();p++){const{val1:A,val2:M}=h.getWidthsAt(p);P.push({point:h.getPointAt(p).val,bulge:h.getBulgeAt(p),startWidth:A,endWidth:M})}return P},D=(h,P)=>{for(;h.removeVertexAt(0););for(let p=0;p<P.length;p++){const A=P[p];h.addVertexAt(A.point,A.bulge,A.startWidth,A.endWidth,p)}},b=(h,P,p)=>{const A=Dt(h,P,p),M=new J;return M.center=A,M.radius=A.sub(h).length(),M.startAngle=ie(A.x,A.y,h.x,h.y),M.endAngle=ie(A.x,A.y,P.x,P.y),M},d=(h,P,p,A)=>{if(p)if(A)h.startAngle=ie(h.center.x,h.center.y,P.x,P.y);else{const M=h.endAngle;h.startAngle=M,h.endAngle=ie(h.center.x,h.center.y,P.x,P.y)}else if(A)h.endAngle=ie(h.center.x,h.center.y,P.x,P.y);else{const M=h.startAngle;h.startAngle=ie(h.center.x,h.center.y,P.x,P.y),h.endAngle=M}return h},m=(h,P,p,{startIndex:A,endIndex:M,numVerts:C})=>{const L=w(h);if(h.setPointAt(p?A:M,P),p)for(let v=0;v<A;v++)h.removeVertexAt(0);else for(let v=M+1;v<C;v++)h.removeVertexAt(M+1);return()=>D(h,L)},x=(h,P,p,{start:A,end:M,startIndex:C,endIndex:L,numVerts:v,bulge:E})=>{const k=w(h),S=b(A,M,E),R=E>0;d(S,P,p,R);const F=S.getGripPoints().at(2),K=U.calcBulge(p?P:A,F,p?M:P).val;if(h.setPointAt(p?C:L,P),h.setBulgeAt(C,K),p)for(let V=0;V<C;V++)h.removeVertexAt(0);else for(let V=L+1;V<v;V++)h.removeVertexAt(L+1);return()=>D(h,k)},g=(h,P,p,{startPoint:A,endPoint:M,numVerts:C})=>{const L=p?A.clone().addvec(A.sub(M).normalize().mult(P)):M.clone().addvec(M.sub(A).normalize().mult(P)),v=p?0:C-1,E=p?A:M;return h.setPointAt(v,L),()=>h.setPointAt(v,E)},c=(h,P,p,{startPoint:A,endPoint:M,numVerts:C,bulge:L})=>{const v=b(A,M,L),E=v.clone();E.startAngle=p?v.startAngle:v.endAngle,E.endAngle=E.startAngle-1e-7;const k=E.getLength().val,S=L>0,R=P%k,F=E.getPointAtDist(p?S?k-R:R:S?R:k-R).val;d(v,F,p,S);const K=v.getGripPoints().at(2),V=U.calcBulge(p?F:A,K,p?M:F).val,O=p?0:C-1,j=p?0:C-2,X=p?A:M,ee=L;return h.setPointAt(O,F),h.setBulgeAt(j,V),()=>{h.setPointAt(O,X),h.setBulgeAt(j,ee)}},y=n.numVerts();if(e<0){const h=s?n.getPointAtDist(Math.abs(e)).val:n.getPointAtDist(o-Math.abs(e)).val,P=Ve(n,h,.1);if(!P)return;const{start:p,end:A,startIndex:M,endIndex:C}=P,L=n.getBulgeAt(M),v=L===0?m(n,h,s,{startIndex:M,endIndex:C,numVerts:y}):x(n,h,s,{start:p,end:A,startIndex:M,endIndex:C,numVerts:y,bulge:L});return{ent:n,fallback:v}}else{l=s?n.getPointAt(1).val:l,a=s?a:n.getPointAt(y-2).val;const h=s?n.getBulgeAt(0):n.getBulgeAt(y-2),P=h===0?g(n,e,s,{startPoint:a,endPoint:l,numVerts:y}):c(n,e,s,{startPoint:a,endPoint:l,numVerts:y,bulge:h});return{ent:n,fallback:P}}}};B("Mx_lengthen",async()=>{let n=[];const e=new ae,r=new ce;r.AddMcDbEntityTypes("LWPOLYLINE,LINE,ARC,ELLIPSE"),e.setFilter(r);const i=async u=>{for(;;){e.setMessage(f("823")),e.setKeyWords(`[${f("373")}(U)]`);let a;e.setUserDraw((w,D)=>{const b=U.findEntAtPoint(w.x,w.y,w.z,void 0,r);a&&a.highlight(!1),a=b.getMcDbCurve();const d=b.clone();d&&d instanceof Le&&We(d,typeof u=="number"?u:u(d,w),w)&&(a&&a.highlight(!0),D.setColor(Number(d.trueColor.getColorValue(d.layerId))),D.drawMcDbEntity(d))});const l=await e.go();if(a&&a.highlight(!1),e.getStatus()===$.kCancel||e.getStatus()===$.kNone)return!0;if(e.isKeyWordPicked("U")){const w=n.pop();w?w?.fallback():I.acutPrintf(`
`+f("825"));continue}if(l===null)return!0;const s=l.getMcDbCurve();if(s===null)return!0;const t=We(s,typeof u=="number"?u:u(s,e.pickPoint()),e.pickPoint());t&&n.push(t)}},o=async u=>{const a=l=>{if(l instanceof J){const s=l.clone();return s.startAngle=0,s.endAngle=u,s.getLength().val}if(l instanceof Ne){const s=l.clone(),t=s.getLength().val;return s.endAngle+=u,s.getLength().val-t}return 0};for(;;){e.setMessage(f("823")),e.setKeyWords(`[${f("373")}(U)]`);let l;const s=new ce;s.AddMcDbEntityTypes("LWPOLYLINE,ARC,ELLIPSE"),e.setUserDraw((d,m)=>{const x=U.findEntAtPoint(d.x,d.y,d.z,void 0,s);l&&l.highlight(!1),l=x.getMcDbCurve();const g=x.clone();if(!g||!(g instanceof Le))return;let c=a(g);We(g,c,d)&&(l&&l.highlight(!0),m.setColor(Number(g.trueColor.getColorValue(g.layerId))),m.drawMcDbEntity(g))});const t=await e.go();if(l&&l.highlight(!1),e.getStatus()===$.kCancel||e.getStatus()===$.kNone)return!0;if(e.isKeyWordPicked("U")){const d=n.pop();d?d?.fallback():I.acutPrintf(`
`+f("825"));continue}if(t===null)return!0;const w=t.getMcDbCurve();if(w===null)return!0;const D=a(w),b=We(w,D,e.pickPoint());b&&n.push(b)}};e:for(;;){e.setMessage(`${f("819")}<${Bn[De]}(DE)>`),e.setKeyWords(`[${f("166")}(DE)/${f("820")}(P)/${f("399")}(T)/${f("821")}(DY)]`);const u=await e.go();if(e.getStatus()===$.kCancel)break;if(e.getStatus()===$.kNone){setTimeout(()=>{De&&I.setCommandLineInputData(De,13)});continue}if(e.isKeyWordPicked("DE")){De="DE";const l=new Ce;l.setMessage(`${f("822")}<${Oe.toFixed(4)}>`),l.setKeyWords(`[${f("281")}(A)]`);let s=await l.go();if(l.getStatus()===$.kCancel)break;if(l.getStatus()===$.kNone&&typeof Oe=="number"&&(s=Oe),l.isKeyWordPicked("A")){const t=new re;t.setMessage(f("826"));let w=await t.go();if(t.getStatus()===$.kNone&&(w=Fn),l.getStatus()===$.kCancel||typeof w!="number"||await o(w))break}if(typeof s=="number"){if(Oe=s,await i(s))break}else break}if(e.isKeyWordPicked("P"))for(De="P";;){const l=new Ut;l.setMessage(`${f("827")}<${He.toFixed(4)}>`);let s=await l.go();if(l.getStatus()===$.kCancel)break e;if(l.getStatus()===$.kNone&&typeof s=="number"&&(He=s),typeof s=="number"){if(s<=0){I.acutPrintf(`
`+f("828"));continue}if(He=s,await i(t=>{const w=t.getLength().val;return w/100*He-w}))break e}else break}if(e.isKeyWordPicked("T")){De="T";const l=new Ce;l.setMessage(f("829")+"<"+st.toFixed(4)+">"),l.setKeyWords(`[${f("281")}(A)]`);let s=await l.go();if(l.getStatus()===$.kNone&&(s=st),l.getStatus()===$.kCancel)break;if(l.isKeyWordPicked("A")){const t=new re;t.setMessage(f("830")+"<"+it.toFixed(4)+">");let w=await t.go();if(l.getStatus()===$.kCancel||(l.getStatus()===$.kNone&&(w=it),typeof w!="number")||await i(D=>0))break}if(typeof s!="number"||await i(t=>s-t.getLength().val))break}if(e.isKeyWordPicked("DY")){De="DY";const l=(s,t,w,D,b,d)=>{if(!(s instanceof _)){if(s instanceof H){let m=0;const x=zn(s.startPoint,s.endPoint,t,!0),g=Kn(s.startPoint,s.endPoint,x);if(typeof g=="object"&&g.isStart!==w){const c=s.startPoint,y=s.endPoint;return g.isStart?(s.endPoint=c,s.startPoint=x):(s.startPoint=y,s.endPoint=x),{ent:s,fallback:()=>{s.startPoint=c,s.endPoint=y}}}else return w?m=x.distanceTo(s.endPoint)-D:m=x.distanceTo(s.startPoint)-D,We(s,m,b)}if(s instanceof J){const m=s.startAngle,x=s.endAngle,g=s.getClosestPointTo(t,!0).val,c=s.getGripPoints().at(2);let h=U.calcBulge(s.getGripPoints().at(0),c,s.getGripPoints().at(1)).val>0;const P=s.center;let p;if(h)s.endAngle=ie(P.x,P.y,g.x,g.y),p=()=>{s.endAngle=x};else{const A=s.startAngle;s.startAngle=ie(P.x,P.y,g.x,g.y),s.endAngle=A,p=()=>{s.startAngle=m,s.endAngle=x}}return{ent:s,fallback:p}}if(s instanceof Ne){const m=s.endAngle,x=s.center,g=Math.atan2(t.y-x.y,t.x-x.x),c=Math.atan2(s.majorAxis.y,s.majorAxis.x);let y=(g-c+Math.PI*2)%(Math.PI*2);return y<s.startAngle&&(y+=Math.PI*2),s.endAngle=y,{ent:s,fallback:()=>{s.endAngle=m}}}}};for(;;){e.setMessage(f("823")),e.setKeyWords(`[${f("373")}(U)]`);let s;const t=await e.go();if(e.isKeyWordPicked("U")){const g=n.pop();g?g?.fallback():I.acutPrintf(`
`+f("825"));continue}if(e.getStatus()===$.kCancel||e.getStatus()===$.kNone)return!0;if(!t||(s=t.getMcDbCurve(),!s))continue;const w=e.pickPoint(),D=s.getLength().val,d=s.getDistAtPoint(w).val<D/2,m=new Y;m.setMessage(f("831")),e.setKeyWords(`[${f("373")}(U)]`),m.setUserDraw((g,c)=>{if(!s)return;s.highlight(!1);const y=t.clone();y instanceof Le&&l(y,g,d,D,w)&&(s.highlight(!0),c.setColor(Number(s.trueColor.getColorValue(s.layerId))),c.drawMcDbEntity(y))});const x=await m.go();if(s&&s.highlight(!1),m.getStatus()===$.kCancel||e.getStatus()===$.kNone)return!0;if(m.isKeyWordPicked("U")){const g=n.pop();g?g?.fallback():I.acutPrintf(`
`+f("825"));continue}if(x===null)return!0;if(s){const g=l(s,x,d,D,w);g&&n.push(g)}}}if(!u)continue;const a=u.getMcDbCurve();a&&I.acutPrintf(`
`+f("824")+": "+a.getLength().val.toFixed(4))}});B("deselect",()=>{const n=N.getCurrentMxCAD();n.mxdraw.clearMxCurrentSelect(),n.updateDisplay()});async function Mt(n,e){return new Promise(async r=>{let i=n.hash,o=n.type;const u=n.file,{hideLoading:a,showLoading:l}=at();if(o===he()){u.source.source.size/(1024*1024)>1&&l();const t=URL.createObjectURL(u.source.source);setTimeout(()=>{Pt(t),setTimeout(()=>{URL.revokeObjectURL(t)},5e3)})}else{let{mxfilepath:t=""}=lt()||{},w=Vt(),D=w+t+i+"."+o+he(!0),b=w+t+i+"/___mx___tz___.dwg.mxweb",d=!1;if(!n.isUseServerExistingFile){let y=await tn(D,i);if(!y.ok)return a(),r(!1);y.tz&&(d=!0)}_t(f("305")+"..."),$t(!0);var s=new Date().getTime();let m=0;e||(m=Xe.EMSCRIPTEN_FETCH_LOAD_TO_MEMORY|Xe.EMSCRIPTEN_FETCH_PERSIST_FILE|Xe.EMSCRIPTEN_FETCH_REPLACE);let x=N.App.getCurrentMxCAD(),g=!1;const c=x.openWebFile(D,y=>{if(a(),g=!0,Wt(),y===0){r(!0);var h=new Date().getTime();if(h-s>5e3&&Me().success(f("306")+"..."),d){let P=w+"/mxcad/files/tz";Nt.post(P,{fileHash:i}).then(p=>{p&&p.data&&p.data.code==0&&x.getImp().loadTz(b)})}}else r(!1),Me().error(f("307"))},void 0,void 0,m,!d);rt(D).then(y=>{y/(1024*1024)>1&&c&&!g&&l()}),r(!!c),r(!1)}zt(u.name)})}let _e=ye();B("OpenDwg",async()=>{_e=ye(),await ct(!1,"OpenDwgImp"),_e&&me("MxFullScreen")});B("OpenDwg_DoNotUseCache",async()=>{_e=ye(),await ct(!0,"OpenDwgImp_DoNotUseCache"),_e&&me("MxFullScreen")});B("OpenDwgImp_DoNotUseCache",async n=>{await Mt(n,!1)});B("OpenDwgImp",async n=>{await Mt(n,!0)});B("Mx_SaveAs",yt);B("Mx_QSave",en);B("Mx_Export_DWG",async()=>{{_e=ye();const n=()=>{_e&&me("MxFullScreen")};let{baseUrl:e="",saveDwgUrl:r="",mxfilepath:i=""}=lt()||{};e.substring(0,16)=="http://localhost"&&(e=Je()+e.substring(16)),r.substring(0,16)=="http://localhost"&&(r=Je()+r.substring(16));let o=N.getCurrentMxCAD();o.saveFileToUrl(r,(u,a)=>{try{let l=JSON.parse(a);if(l.ret=="ok"){let s=e+i+l.file;fetch(s).then(async t=>{const w=await t.blob();let D=o.getCurrentOriginaFileName();function b(d){const m=d.toLowerCase(),x=m.lastIndexOf(".");if(x!==-1){const g=m.substring(x);if(g===he(!0))return d.substring(0,x)+".dwg";if(g===".dwg"||g===".dxf")return d}return d+".dwg"}D=b(D),await dt.saveAsFileDialog({blob:w,filename:D,types:[{description:"dwg"+f("231"),accept:{"application/octet-stream":[".dwg"]}}]}),n()})}else console.log(a)}catch{console.log("Mx: sserverResult error")}})}});B("Mx_debug",()=>{});B("Mx_clear_buf",()=>{});B("Mx_Array",Xt);B("Mx_NewFile",()=>{{N.getCurrentMxCAD().newFile();const{initLayerList:e}=Tt(),{initColorIndexList:r}=ot(),{initLineTypeList:i}=St();e(),r(),i()}});B("Mx_NewFile_Template",()=>{N.getCurrentMxCAD().openWebFile("empty_template.mxweb")});B("MxPE_DrawMText",async()=>{const n=new Y;n.setMessage(f("308"));const e=await n.go();if(!e)return;const{open:r}=Rt(),i=await r();if(typeof i=="boolean")return;const{text:o,size:u}=i,a=new jt;a.contents=o||"",u&&(a.textHeight=u),a.location=e;const l=N.getCurrentMxCAD(),s=l.drawEntity(a);return l.updateDisplay(),s});
