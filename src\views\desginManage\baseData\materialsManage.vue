<template>
  <div class="app-container">
    <div class="form-left">
      <div style="color: dodgerblue; font-weight: 600; margin-bottom: 11px">物料类型</div>
      <table-tree @nodeClick="nodeClick" :treeData="materialTypeOptions" :defaultProps="{ label: 'materialsTypeName', children: 'children' }"></table-tree>      
    </div>
    <div class="form-right">
      <div style="width: 100%; height: 60%">
        <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
          <el-form-item label="资源库" prop="baseVersionId">
            <el-select v-model="queryParams.baseVersionId" @change="versionChange" placeholder="请选择" style="width: 180px;">
              <el-option v-for="item in versionList" :key="item.objId" :label="item.versionName" :value="item.objId"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="物料名称" prop="materialsName">
            <el-input
            v-model="queryParams.materialsName"
            placeholder="请输入物料名称"
            clearable
            @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="物料型号" prop="materialsSpec">
            <el-input
            v-model="queryParams.materialsSpec"
            placeholder="请输入物料型号"
            clearable
            @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8" style="background:rgba(166,215,213,0.3);padding:10px;padding:5px;">
          <el-col :span="1.5">
            <el-button
            type="text"
            plain
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['system:BaseMaterials:add']"
            >新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
            type="text"
            plain
            icon="Edit"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['system:BaseMaterials:edit']"
            >修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
            type="text"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['system:BaseMaterials:remove']"
            >删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
            type="text"
            plain
            icon="Download"
            @click="handleExport"
            v-hasPermi="['system:BaseMaterials:export']"
            >导出</el-button>
          </el-col>
        </el-row>

        <el-table v-loading="loading" highlight-current-row style="width: 100%;height:calc(100% - 100px)" :data="BaseMaterialsList" show-overflow-tooltip scrollbar-always-on @row-click="onRowClick" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" fixed align="center" />
          <el-table-column label="物料名称" header-align="center" align="left" width="200" prop="materialsName" />
          <el-table-column label="物料编码" header-align="center" align="left" width="120" prop="materialsCodeErp" />
          <el-table-column label="物料型号" header-align="center" align="left" width="280" prop="materialsSpec" />
          <el-table-column label="物料描述" header-align="center" align="left" width="280" prop="materialsDescription" />
          <el-table-column label="扩展描述" header-align="center" align="left" prop="extensionDescription" />
          <el-table-column label="电压等级" align="center" prop="materialsVoltage" />
          <el-table-column label="是否主材" align="center" width="80" prop="isMain">
            <template #default="scope">
              <div v-if="scope.row.isMain === 1">是</div>
              <div v-else>否</div>
            </template>
          </el-table-column>
          <el-table-column label="线性标识" align="center" width="80" prop="isLine">
            <template #default="scope">
              <div v-if="scope.row.isLine === 1">是</div>
              <div v-else>否</div>
            </template>
          </el-table-column>
          <el-table-column label="设计单位" align="center" width="80" prop="designUnit" />
          <el-table-column label="统计单位" align="center" width="80" prop="erpUnit" />
          <el-table-column label="物料比重" align="center" width="80" prop="materialsRatio" />
          <el-table-column label="物料单重" align="center" width="80" prop="materialsWeight" />
          <el-table-column label="供应方" align="center" width="80" prop="isDonor">
            <template #default="scope">
              <div v-if="scope.row.isDonor === 1">甲供</div>
              <div v-else>乙供</div>
            </template>
          </el-table-column>
          <el-table-column label="含税单价" align="center" width="80" prop="taxPrice" />
          <el-table-column label="不含税单价" align="center" prop="materialsPrice" />
        </el-table>
      </div>
      <div style="width: 100%; height: 40%">
        <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
          <el-tab-pane label="方案选择" name="first">方案选择</el-tab-pane>
          <el-tab-pane label="属性" name="second">
            <el-table style="width: 100%;" height="30vh" show-overflow-tooltip scrollbar-always-on :data="BaseMaterialsList" border>
              <el-table-column label="属性名称" align="center" width="220" prop="materialsName" />
              <el-table-column label="属性值" align="left" prop="materialsCodeErp" />
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="图纸" name="third">
            <el-row :gutter="10" class="mb8" style="background:rgba(166,215,213,0.3);padding:5px;">
              <el-col :span="1.5">
                <el-button
                type="text"
                plain
                icon="Plus"
                @click="handleAdd"
                >新增</el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button
                type="text"
                plain
                icon="Delete"
                :disabled="multiple"
                @click="handleDelete"
                >删除</el-button>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-table style="width: 100%;" height="25vh" :data="drawList" show-overflow-tooltip scrollbar-always-on border>
                  <el-table-column  type="selection" width="55"/>
                  <el-table-column label="图纸名称" align="center" prop="drawingName" />
                </el-table>
              </el-col>
              <el-col :span="12">
                <div style="padding: 0 5px;text-align: center;">
                  <el-image :src="src" style="height: 25vh;">
                    <template #error>
                      <div class="image-slot">
                        <el-icon><icon-picture /></el-icon>
                      </div>
                    </template>
                  </el-image>
                </div>                
              </el-col>
            </el-row>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
    <!-- 添加或修改物料对话框 -->
    <el-dialog :title="title" v-model="open" inline width="50%" append-to-body>
      <el-form ref="BaseMaterialsRef" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-form-item label="物料名称" prop="materialsName">
            <el-input v-model="form.materialsName" placeholder="请输入物料名称" />
          </el-form-item>
          <el-form-item label="物料类别" prop="materialsTypeKey">
            <el-input v-model="form.materialsTypeKey" placeholder="请输入物料类别key" />
          </el-form-item>       
          <el-form-item label="物料编码" prop="materialsCodeErp">
            <el-input v-model="form.materialsCodeErp" placeholder="请输入物料编码" />
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="物料型号" prop="materialsSpec">
            <el-input v-model="form.materialsSpec" placeholder="请输入物料型号" />
          </el-form-item>
          <el-form-item label="物料描述" prop="materialsDescription">
            <el-input v-model="form.materialsDescription" placeholder="请输入物料描述" />
          </el-form-item>
          <el-form-item label="扩展描述" prop="extensionDescription">
            <el-input v-model="form.extensionDescription" placeholder="请输入扩展描述" />
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="电压等级" prop="materialsVoltage">
            <el-input v-model="form.materialsVoltage" placeholder="请输入10kV、380V、220V" />
          </el-form-item>
          <el-form-item label="是否主材" prop="isMain">
            <el-input v-model="form.isMain" placeholder="请输入是否主材" />
          </el-form-item>
          <el-form-item label="线性标识" prop="isLine">
            <el-input v-model="form.isLine" placeholder="请输入线性标识" />
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="统计单位" prop="erpUnit">
            <el-input v-model="form.erpUnit" placeholder="请输入统计单位" />
          </el-form-item>
          <el-form-item label="物料比重" prop="materialsRatio">
            <el-input v-model="form.materialsRatio" placeholder="请输入物料比重" />
          </el-form-item>
          <el-form-item label="设计单位" prop="designUnit">
            <el-input v-model="form.designUnit" placeholder="请输入设计单位" />
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="物料单重" prop="materialsWeight">
            <el-input v-model="form.materialsWeight" placeholder="请输入物料单重" />
          </el-form-item>
          <el-form-item label="技术规范" prop="technicalProtocol">
            <el-input v-model="form.technicalProtocol" placeholder="请输入技术规范(固化ID)" />
          </el-form-item>
          <el-form-item label="物料标识" prop="materialsFlag">
            <el-input v-model="form.materialsFlag" placeholder="请输入物料标识" />
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="可用标识" prop="stateFlag">
            <el-input v-model="form.stateFlag" placeholder="请输入可用标识" />
          </el-form-item>
          <el-form-item label="拆除时名称" prop="removeName">
            <el-input v-model="form.removeName" placeholder="请输入拆除时名称" />
          </el-form-item>
          <el-form-item label="供应方" prop="isDonor">
            <el-input v-model="form.isDonor" placeholder="请输入供应方" />
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="原有时名称" prop="originallName">
            <el-input v-model="form.originallName" placeholder="请输入原有时名称" />
          </el-form-item>
          <el-form-item label="含税单价" prop="taxPrice">
            <el-input v-model="form.taxPrice" placeholder="请输入含税单价" />
          </el-form-item>
          <el-form-item label="不含税单价" prop="materialsPrice">
            <el-input v-model="form.materialsPrice" placeholder="请输入不含税单价" />
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="资金类型" prop="financeTypeKey">
            <el-input v-model="form.financeTypeKey" placeholder="请输入资金类型" />
          </el-form-item>
          <el-form-item label="物料大类" prop="bigCategory">
            <el-input v-model="form.bigCategory" placeholder="请输入物料大类" />
          </el-form-item>
          <el-form-item label="物料中类" prop="midCategory">
            <el-input v-model="form.midCategory" placeholder="请输入物料中类" />
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="物料小类" prop="smallCategory">
            <el-input v-model="form.smallCategory" placeholder="请输入物料小类" />
          </el-form-item>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="BaseMaterials">
import { listBaseMaterials, getBaseMaterials, delBaseMaterials, addBaseMaterials, updateBaseMaterials } from "@/api/baseDate/BaseMaterials";
import { listBaseMaterialsDrawing } from "@/api/baseDate/BaseMaterialsDrawing.js"
import { getBaseMaterialsTypeTreeList } from "@/api/baseDate/BaseMaterialsType";
import { listVersion } from "@/api/baseDate/baseVersion";
import { ref } from "vue";
import useUserStore from '@/store/modules/user'

const userStore = useUserStore()
const { proxy } = getCurrentInstance();

const BaseMaterialsList = ref([]);
const drawList = ref([])
const src = 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg'
const open = ref(false);
const loading = ref(false);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const title = ref("");
const materialTypeOptions = ref([])
const versionList = ref([])


const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 100000,
    materialsName: null,
    materialsCodeErp: null,
    materialsSpec: null,
    baseVersionId: null
  },
  rules: {
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询物料列表 */
function getList() {
  loading.value = true;
  listBaseMaterials(queryParams.value).then(response => {
    BaseMaterialsList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  }).catch(() => {
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    objId: null,
    materialsName: null,
    materialsTypeKey: null,
    materialsType: null,
    materialsCodeErp: null,
    materialsSpec: null,
    materialsDescription: null,
    extensionDescription: null,
    materialsVoltage: null,
    isMain: null,
    isLine: null,
    designUnit: null,
    erpUnit: null,
    materialsRatio: null,
    materialsWeight: null,
    technicalProtocol: null,
    materialsFlag: null,
    isDonor: null,
    materialsModify: null,
    belongTo: null,
    stateFlag: null,
    removeName: null,
    originallName: null,
    simplyName: null,
    taxPrice: null,
    materialsPrice: null,
    costMaterialsProjectid: null,
    financeTypeKey: null,
    operateTime: null,
    materialsSort: null,
    bigCategory: null,
    midCategory: null,
    smallCategory: null,
    isAttachment: null,
    segmentLength: null,
    isStandard: null,
    baseVersionId: null,
    isDel: null
  };
  proxy.resetForm("BaseMaterialsRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  queryParams.value.materialsName = null
  queryParams.value.materialsCodeErp = null
  queryParams.value.materialsSpec = null 
  handleQuery();
}

/**列表点击事件 */
function onRowClick(row) {
  console.log(row)
  const params = {
    pageNum: 1,
    pageSize: 100000,
    baseVersionId: queryParams.value.baseVersionId,
    materialsProjectId: row.objId
  }
  listBaseMaterialsDrawing(params).then((res) => {
    drawList.value = res.rows
  })
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.objId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加物料";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _objId = row.objId || ids.value
  getBaseMaterials(_objId).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改物料";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["BaseMaterialsRef"].validate(valid => {
    if (valid) {
      if (form.value.objId != null) {
        updateBaseMaterials(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addBaseMaterials(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _objIds = row.objId || ids.value;
  proxy.$modal.confirm('是否确认删除物料编号为"' + _objIds + '"的数据项？').then(function() {
    return delBaseMaterials(_objIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/BaseMaterials/export', {
    ...queryParams.value
  }, `BaseMaterials_${new Date().getTime()}.xlsx`)
}

/**版本值改变事件 */
function versionChange(row) {
  getBaseMaterialsTypeTree(row)
}

/**获取版本下拉数据 */
function getlistVersion() {
  userStore.getInfo().then(res => {
      const queryItem = {
        pageNum: 1,
        pageSize: 10000,
        companyId: undefined
      }
      queryItem.companyId = res.dept.deptId 
      listVersion(queryItem).then(response => {
         versionList.value = response.rows;
         if (versionList.value.length > 0) {
          queryParams.value.baseVersionId = versionList.value[0].objId
          getBaseMaterialsTypeTree(versionList.value[0].objId)
         }
      })
   })
}

/**物料类型树形图 */
function getBaseMaterialsTypeTree(versionId){
  const queryParams = {
    baseVersionId: versionId
  }
  getBaseMaterialsTypeTreeList(queryParams).then(res => {
    materialTypeOptions.value = res.data
  })
}

/**物料类型点击事件 */
function nodeClick(node,item) {
  loading.value = true
  queryParams.value.materialsTypeKey = node.materialsTypeKey
  listBaseMaterials(queryParams.value).then(res => {
    console.log(res)
    BaseMaterialsList.value = res.rows
    loading.value = false
  }).catch(() => {
    loading.value = false
  })
}

getlistVersion()
// getList();
</script>

<style lang='scss' scoped>
/* 选中某行时的背景色*/
.el-table__body tr.current-row > td {
   background-color: #a0eddf !important;
}
 
/*鼠标移入某行时的背景色*/
.el-table--enable-row-hover .el-table__body tr:hover > td {
   background-color: #a0eddf;
}

.app-container {
  display: flex;
  height: calc(100vh - 120px);
  .form-left {
    width: 20%;
    height: 100%;
    margin-right: 10px;
  }
  .form-right {
    width: 80%;
    height: 100%;
  }
  .module-btn {
    background-color: #f5f5f5;
    width: 100%;
    display: flex;
    justify-content: flex-start;
    font-size: 13px;
    div {
      border: 1px solid #ccc;
      padding: 4px 10px 2px 10px;
      color: #73797e;
      border-radius: 5px 5px 0 0;
      margin-right: 5px;
      cursor: pointer;
    }
    .active {
      font-weight: bold;
      color: #000;
      background: #fff;
    }
  }
  .sub-bottom {
    background-color: #f5f5f5;
    width: 100%;
  }
// 图纸
  .sub-photo{
    display: flex;
    justify-content: space-between;
    width: 100%;
    height: 100%;
    .photo-left{
      width: 48%;
      height: 100%;
    }
    .photo-right{
      width: 48%;
      height: 100%;
      border:2px solid #ccc;
    }
  }
}
</style>
