import { MxCpp, McCmColor } from "mxcad";

// 画填充
function drawHatch() {
    const mxcad = MxCpp.getCurrentMxCAD();
    // 创建新画布
    mxcad.newFile();
    // 绘制一个实心填充
    //定义一个路径的开始点
    mxcad.pathMoveTo(150, 3300);
    //路径的一下个点
    mxcad.pathLineTo(250, 3300);
    //路径的一下个点
    mxcad.pathLineTo(250, 3400);
    //路径的一下个点
    mxcad.pathLineTo(150, 3300);
    //把路径拟合成一个样线
    mxcad.drawColor = new McCmColor(255, 0, 0);
    mxcad.drawPathToHatch(1);

    // 绘制一个图形填充
    mxcad.addPatternDefinition("MyHatchPattern1", "((45, 0,0, 0,0.125))");
    mxcad.drawPatternDefinition = "MyHatchPattern1";
    //定义一个路径的开始点
    mxcad.pathMoveTo(250, 3300);
    //路径的一下个点
    mxcad.pathLineTo(350, 3300);
    //路径的一下个点
    mxcad.pathLineTo(350, 3400);
    //路径的一下个点
    mxcad.pathLineTo(250, 3300);
    mxcad.drawColor = new McCmColor(255, 255, 0);
    //把路径变成一个填充,80,是填充图案的缩放比例.
    mxcad.drawPathToHatch(100);
    mxcad.zoomAll()
    mxcad.zoomScale(0.8)
};

// 调用画填充的方法
drawHatch();