<template>
  <!-- 一.架空线路设计 -->
  <!-- 1.线路绘制 -index==0 -->
  <data-dialog @close="closeDialog" v-if="appStore.mapIndex == '线路绘制'">
    <template #header>
      <h4 style="margin-left: 10px; margin-top: 0px; line-height: 39px">
        线路绘制
      </h4>
    </template>
    <template #body>
      <div style="height: 655px; overflow: auto">
        <div class="line">
          <div class="line-item">
            <span> 电压等级 </span>
          </div>
          <div class="line-input">
            <el-select
              v-model="lineForm.voltageLevel"
              placeholder="请选择"
              @change="lineFormVoltageLevelChange"
            >
              <el-option
                v-for="item in lineOption.voltageLevelOption"
                :key="item.id"
                :label="item.text"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </div>
        </div>
        <div class="line">
          <div class="line-item">
            <span> 回路 </span>
          </div>
          <div class="line-input">
            <el-select
              v-model="lineForm.loop"
              placeholder="请选择"
              @change="lineFormLoopChange"
            >
              <el-option
                v-for="item in lineOption.loopOption"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </div>
        </div>
        <div class="line">
          <div class="line-item">
            <span> 状态 </span>
          </div>
          <div class="line-input">
            <el-select v-model="lineForm.status" placeholder="请选择">
              <el-option
                v-for="item in lineOption.statusOption"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </div>
        </div>
        <div class="line">
          <div class="line-item">
            <span> 杆塔类型 </span>
          </div>
          <div class="line-input">
            <el-radio-group
              class="radio-group"
              style="margin-left: 17px"
              v-model="lineForm.checkList1"
              @change="changeLineRadio"
            >
              <el-radio value="水泥杆" size="small" v-if="show.sng"
                >水泥杆</el-radio
              >
              <el-radio value="钢管杆" size="small" v-if="show.ggg"
                >钢管杆</el-radio
              >
              <el-radio
                value="水泥双杆"
                size="small"
                v-if="lineForm.loop === '单回' && show.snsg"
                >水泥双杆</el-radio
              >
              <el-radio value="宽基塔" size="small" v-if="show.kjt"
                >宽基塔</el-radio
              >
              <el-radio value="窄基塔" size="small" v-if="show.zjt"
                >窄基塔</el-radio
              >
            </el-radio-group>
          </div>
        </div>
        <div class="line">
          <div
            class="line-item"
            :style="lineItemTitle === '钢管杆' ? 'height: 70px' : ''"
          >
            <span> {{ lineItemTitle }} </span>
          </div>
          <div
            class="line-input"
            :style="lineItemTitle === '钢管杆' ? 'height: 70px' : ''"
          >
            <div v-if="lineItemTitle === '钢管杆'">
              <div>
                <span style="font-weight: normal; font-size: 14px">杆高: </span>
                <el-select
                  style="width: 220px"
                  size="small"
                  v-model="lineForm.gggGg"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in lineOption.gggGgOption"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                    :title="item.label"
                  >
                  </el-option>
                </el-select>
              </div>
              <div>
                <span style="font-weight: normal; font-size: 14px">形式: </span>
                <el-select
                  size="small"
                  style="width: 220px"
                  v-model="lineForm.gggXs"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in lineOption.gggXsOption"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </div>
            </div>
            <div v-else>
              <el-select v-model="lineForm.cement" placeholder="请选择">
                <el-option
                  v-for="item in lineOption.cementOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                  :title="item.label"
                >
                </el-option>
              </el-select>
            </div>
          </div>
        </div>
        <div class="line" v-if="lineForm.checkList2.indexOf('1') !== -1">
          <div class="line-item">
            <span> 横担长度 </span>
          </div>
          <div class="line-input">
            <el-select v-model="lineForm.crossLength" placeholder="请选择">
              <el-option
                v-for="item in lineOption.crossLengthOption"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </div>
        </div>
        <div class="line">
          <div class="line-item">
            <span> 基础 </span>
          </div>
          <div class="line-input">
            <el-select v-model="lineForm.foundation" placeholder="请选择">
              <el-option
                v-for="item in lineOption.foundationOption"
                :key="item.value"
                :label="item.text"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </div>
        </div>
        <div class="line">
          <div class="line-item">
            <span> 土质 </span>
          </div>
          <div class="line-input">
            <el-select v-model="lineForm.soil" placeholder="请选择">
              <el-option
                v-for="item in lineOption.soilOption"
                :key="item.value"
                :label="item.key"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </div>
        </div>
        <div class="line">
          <div class="line-item">
            <span> 地形 </span>
          </div>
          <div class="line-input">
            <el-select v-model="lineForm.terrain" placeholder="请选择">
              <el-option
                v-for="item in lineOption.terrainOption"
                :key="item.value"
                :label="item.key"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </div>
        </div>
        <div class="line">
          <el-table class="input-table" size="small" :data="data" border>
            <el-table-column
              width="90"
              label="线路名称"
              align="center"
              prop="name"
            >
              <template #default="scope">
                <el-select
                  filterable
                  allow-create
                  default-first-option
                  :reserve-keyword="false"
                  size="small"
                  style="width: 70px"
                  v-model="scope.row.name"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in lineOption.tableNameOption"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column width="90" label="状态" align="center" prop="key">
              <template #default="scope">
                <el-select
                  size="small"
                  style="width: 70px"
                  v-model="scope.row.key"
                  placeholder="请选择"
                  @change="tableKeyChange"
                >
                  <el-option
                    v-for="item in lineOption.tableKeyOption"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="导线型号" align="center" prop="sk">
              <template #default="scope">
                <el-select
                  size="small"
                  style="width: 130px"
                  v-model="scope.row.sk"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in lineOption.tableSkOption"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column width="50" align="center" label="删除">
              <template #default="scope">
                <div
                  style="color: #0e8b8d; cursor: pointer"
                  @click="lineTableDel(scope.row)"
                >
                  {{ scope.row.ksd }}
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <!-- 两行from表单  -->
        <div class="line">
          <div class="tw-ipt">
            <span> 杆塔排布数量 </span>
          </div>
          <div class="tw-sct">
            <el-input
              class="in-item"
              :disabled="disabledLine"
              v-model="lineForm.ganNum"
              placeholder=""
            ></el-input>
          </div>
          <div class="tw-ipt">
            <span> 档距步长 </span>
          </div>
          <div class="tw-sct">
            <el-input
              :disabled="disabledLine"
              class="in-item"
              v-model="lineForm.stepSize"
              placeholder=""
            ></el-input>
          </div>
        </div>
        <div class="line">
          <div class="tw-ipt">
            <span> 杆号前缀 </span>
          </div>
          <div class="tw-sct">
            <el-input
              class="in-item"
              v-model="lineForm.ganPrefix"
              placeholder=""
            ></el-input>
          </div>
          <div class="tw-ipt">
            <span> 杆号后缀 </span>
          </div>
          <div class="tw-sct">
            <el-input
              class="in-item"
              v-model="lineForm.ganSuffix"
              placeholder=""
            ></el-input>
          </div>
        </div>
        <div class="line">
          <div class="tw-ipt">
            <span> 起始杆号 </span>
          </div>
          <div class="tw-sct">
            <el-input
              class="in-item"
              v-model="lineForm.ganStart"
              placeholder=""
            ></el-input>
          </div>
          <div class="tw-ipt">
            <span> 杆号增量 </span>
          </div>
          <div class="tw-sct">
            <el-input
              class="in-item"
              v-model="lineForm.ganAddNum"
              placeholder=""
            ></el-input>
          </div>
        </div>
        <!-- 绘制设置 -->
        <div class="line">
          <div class="line-item" style="height: 70px">
            <span> 绘制设置 </span>
          </div>
          <div
            style="
              width: 294px;
              display: flex;
              align-content: center;
              flex-wrap: wrap;
            "
            class="li-input"
          >
            <el-checkbox-group
              class="checkbox-group"
              style="display: flex; flex-wrap: wrap; margin-left: 15px"
              v-model="lineForm.checkList2"
              @change="checkboxChange"
            >
              <el-checkbox
                style="width: 68px; margin-right: 25px"
                size="small"
                label="自动选型"
                value="1"
              />
              <el-checkbox
                :disabled="true"
                style="width: 68px; margin-right: 25px"
                size="small"
                label="连续绘制"
                value="2"
              />
              <!-- <el-checkbox
                style="width: 68px; margin-right: 25px"
                size="small"
                label="地线"
                value="3"
              /> -->
              <el-checkbox
                style="width: 68px; margin-right: 25px"
                size="small"
                label="手动绘制"
                value="4"
              />
              <!-- <el-checkbox
                style="width: 68px; margin-right: 25px"
                size="small"
                label="绘制起始杆塔"
                value="5"
              /> -->
              <el-checkbox
                v-show="false"
                style="width: 68px; margin-right: 25px"
                size="small"
                label="防风"
                value="6"
              />
            </el-checkbox-group>
            <div style="margin-left: 15px">
              <span
                style="
                  font-size: 12px;
                  font-weight: normal;
                  color: #282b33;
                  font-weight: 400;
                "
                >耐张段(m)</span
              >
              <el-input
                style="width: 75px; height: 20px"
                v-model="lineForm.drawingSetting"
                placeholder=""
              ></el-input>
            </div>
          </div>
        </div>
        <div style="background: #e4f2f2; height: 50px" class="line">
          <div @click="lineJkClick" class="line-bnt">绘制</div>
        </div>
      </div>
    </template>
  </data-dialog>
  
  <!-- 2.柱上变压器绘制 -index==1 -->
  <data-dialog
    @close="closeDialog"
    v-if="appStore.mapIndex == '柱上变压器绘制'"
  >
    <template #header>
      <h4 style="margin-left: 10px; margin-top: 0px; line-height: 39px">
        增加柱上变台
      </h4>
    </template>
    <template #body>
      <div class="line">
        <div class="line-item">
          <span> 电压等级 </span>
        </div>
        <div class="line-input">
          <el-select
            v-model="lineForm2.voltageLevel"
            placeholder="请选择"
            @change="changeFaFun"
          >
            <el-option
              v-for="item in lineOption2.voltageLevelOption"
              :key="item.value"
              :label="item.text"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 杆高 </span>
        </div>
        <div class="line-input">
          <el-select
            v-model="lineForm2.GHeight"
            placeholder="请选择"
            @change="changeFaFun"
          >
            <el-option
              v-for="item in lineOption2.GHeightOption"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 状态 </span>
        </div>
        <div class="line-input">
          <el-select v-model="lineForm2.status" placeholder="请选择">
            <el-option
              v-for="item in lineOption2.statusOption"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 柱上变台方案类别 </span>
        </div>
        <div class="line-input">
          <el-select v-model="lineForm2.columnClass" placeholder="请选择">
            <el-option
              v-for="item in lineOption2.columnClassOption"
              :key="item.value"
              :label="item.label"
              :value="item.value"
              :title="item.label"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 是否成套化 </span>
        </div>
        <div class="line-input">
          <el-select
            v-model="lineForm2.isComplete"
            placeholder="请选择"
            @change="isCompleteChange"
          >
            <el-option
              v-for="item in lineOption2.isCompleteOption"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 柱上变台方案 </span>
        </div>
        <div class="line-input">
          <el-select v-model="lineForm2.column" placeholder="请选择">
            <el-option
              v-for="item in lineOption2.columnOption"
              :key="item.value"
              :label="item.label"
              :value="item.value"
              :title="item.label"
            >
            </el-option>
          </el-select>
        </div>
      </div>

      <div class="line">
        <div class="line-item">
          <span> 变台台区 </span>
        </div>
        <div class="line-input">
          <el-input
            size="small"
            class="in-item"
            v-model="lineForm2.tower"
            placeholder="请输入内容"
          ></el-input>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 变台编号 </span>
        </div>
        <div class="line-input">
          <el-input
            size="small"
            class="in-item"
            v-model="lineForm2.towerCode"
            placeholder="请输入内容"
          ></el-input>
        </div>
      </div>
      <div style="background: #e4f2f2; height: 50px" class="line">
        <div @click="lineZsbtClick" class="line-bnt">绘制</div>
      </div>
    </template>
  </data-dialog>
  <!-- 3.柱上设备绘制 -index==2 -->
  <data-dialog @close="closeDialog" v-if="appStore.mapIndex == '柱上设备绘制'">
    <template #header>
      <h4 style="margin-left: 10px; margin-top: 0px; line-height: 39px">
        增加柱上设备
      </h4>
    </template>
    <template #body>
      <el-form
        :model="lineForm3"
        ref="deviceForm"
        label-width="0px"
        class="form"
      >
        <el-form-item>
          <div class="line">
            <div class="line-item">
              <span> 适配杆塔 </span>
            </div>
            <div class="line-input">
              <el-select
                v-model="lineForm3.fitterTower"
                @change="handleFitterTower"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in dataList.fitterTowerList"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </div>
          </div>
        </el-form-item>
        <el-form-item>
          <div class="line">
            <div class="line-item">
              <span> 柱上设备类型 </span>
            </div>
            <div class="line-input">
              <el-select
                v-model="lineForm3.columnDeviceType"
                @change="handleColumnDeviceType"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in dataList.columnDeviceList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </div>
          </div>
        </el-form-item>
        <el-form-item>
          <div class="line">
            <div class="line-item">
              <span> 电压等级 </span>
            </div>
            <div class="line-input">
              <el-input
              disabled
                class="in-item"
                v-model="lineForm3.voltageLevel"
                placeholder="请输入内容"
              ></el-input>
            </div>
          </div>
        </el-form-item>
        <el-form-item>
          <div class="line">
            <div class="line-item">
              <span> 状态 </span>
            </div>
            <div class="line-input">
              <el-select
                v-model="lineForm3.status"
                @change="handleCross"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in dataList.statusList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </div>
          </div>
        </el-form-item>
        <el-form-item>
          <div class="line">
            <div class="line-item">
              <span> 设备型号 </span>
            </div>
            <div class="line-input">
              <el-select v-model="lineForm3.deviceModel" placeholder="请选择">
                <el-option
                  v-for="item in dataList.deviceModelList"
                  :key="item.moduleid"
                  :label="item.modulename"
                  :value="item.moduleid"
                >
                </el-option>
              </el-select>
            </div>
          </div>
        </el-form-item>
        <el-form-item v-if="deviceModelFlag">
          <div class="line">
            <div class="line-item">
              <span> 是否存在智能开关 </span>
            </div>
            <div class="line-input">
              <el-select
                v-model="lineForm3.isFlag"
                @change="handleCross"
                placeholder="请选择"
              >
                <el-option label="是" value="是"></el-option>
                <el-option label="否" value="否"></el-option>
              </el-select>
            </div>
          </div>
        </el-form-item>
        <el-form-item>
          <div class="line">
            <div class="line-item">
              <span> 设备编号 </span>
            </div>
            <div class="line-input">
              <el-input
                class="in-item"
                v-model="lineForm3.deviceCode"
                placeholder="请输入设备编号"
              ></el-input>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <div style="background: #e4f2f2; height: 50px" class="line">
        <div @click="deviceClick" class="line-bnt">绘制</div>
      </div>
    </template>
  </data-dialog>
  <!-- 4.拉线绘制 index==3 -->
  <data-dialog @close="closeDialog" v-if="appStore.mapIndex == '拉线绘制'">
    <template #header>
      <h4 style="margin-left: 10px; margin-top: 0px; line-height: 39px">
        拉线绘制
      </h4>
    </template>
    <template #body>
      <el-form
        :model="lineFormLx"
        ref="deviceForm"
        label-width="0px"
        class="form"
      >
        <el-form-item>
          <div class="line">
            <div class="line-item">
              <span> 电压等级 </span>
            </div>
            <div class="line-input">
              <el-select
                v-model="lineFormLx.voltageLevel"
                placeholder="请选择"
                @change="voltageLevelGetLxfa"
              >
                <el-option
                  v-for="item in lineOptionLx.voltageLevelOption"
                  :key="item.value"
                  :label="item.text"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </div>
          </div>
        </el-form-item>
        <el-form-item>
          <div class="line">
            <div class="line-item">
              <span> 状态 </span>
            </div>
            <div class="line-input">
              <el-select
                v-model="lineFormLx.status"
                placeholder="请选择"
                @change="stateLXChange"
              >
                <el-option
                  v-for="item in lineOptionLx.statusOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </div>
          </div>
        </el-form-item>
        <el-form-item>
          <div class="line">
            <div class="line-item">
              <span> 拉线类型 </span>
            </div>
            <div class="line-input">
              <el-select
                v-model="lineFormLx.lxType"
                placeholder="请选择"
                @change="lxTypeGetLxfa"
              >
                <el-option
                  v-for="item in lineOptionLx.lxTypeOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </div>
          </div>
        </el-form-item>
        <el-form-item>
          <div class="line">
            <div class="line-item">
              <span> 拉线方案 </span>
            </div>
            <div class="line-input">
              <el-select v-model="lineFormLx.lxfa" placeholder="请选择">
                <el-option
                  v-for="item in lineOptionLx.lxfaOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <div style="background: #e4f2f2; height: 50px" class="line">
        <div @click="lineLxClick" class="line-bnt">绘制</div>
      </div>
    </template>
  </data-dialog>
  <!-- 5.附属设施绘制 -index==4 -->
  <data-dialog @close="closeDialog" v-if="appStore.mapIndex == '附属设施绘制'">
    <template #header>
      <h4 style="margin-left: 10px; margin-top: 0px; line-height: 39px">
        增加附属设施
      </h4>
    </template>
    <template #body>
      <div class="line">
        <div class="line-item">
          <span> 附属设备类型 </span>
        </div>
        <div class="line-input">
          <el-select
            @change="handleZssb"
            v-model="lineForm4.deviceClass"
            placeholder="请选择"
          >
            <el-option
              v-for="item in zssbList.fssbList"
              :key="item.moduletypekey"
              :label="item.moduletypename"
              :value="item.moduletypekey"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 设备型号 </span>
        </div>
        <div class="line-input">
          <el-select v-model="lineForm4.deviceModel" placeholder="请选择">
            <el-option
              v-for="item in zssbList.sbxhList"
              :key="item.moduleid"
              :label="item.modulename"
              :value="item.moduleid"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 状态 </span>
        </div>
        <div class="line-input">
          <el-select v-model="lineForm4.status" placeholder="请选择">
            <el-option label="新建" value="New"></el-option>
            <el-option label="原有" value="Original"></el-option>
            <el-option label="拆除" value="Remove"></el-option>
          </el-select>
        </div>
      </div>
      <div style="background: #e4f2f2; height: 50px" class="line">
        <div class="line-bnt" @click="lineFsssClick">绘制</div>
      </div>
    </template>
  </data-dialog>
  <!-- 6.同杆并架绘制 -index==5 -->
  <data-dialog @close="closeDialog" v-if="appStore.mapIndex == '同杆并架绘制'">
    <template #header>
      <h4 style="margin-left: 10px; margin-top: 0px; line-height: 39px">
        低压同杆并架
      </h4>
    </template>
    <template #body>
      <div class="line">
        <div class="line-item">
          <span> 电压等级 </span>
        </div>
        <div class="line-input">
          <el-select v-model="lineForm5.voltageLevel" placeholder="请选择">
            <el-option
              v-for="item in lineOption5.voltageLevelOption"
              :key="item.id"
              :label="item.text"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 线路名称 </span>
        </div>
        <div class="line-input">
          <el-select
            filterable
            allow-create
            default-first-option
            :reserve-keyword="false"
            v-model="lineForm5.lineName"
            placeholder="请选择"
          >
            <el-option
              v-for="item in lineOption5.lineNameOption"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 导线状态 </span>
        </div>
        <div class="line-input">
          <el-select
            v-model="lineForm5.wireStatus"
            placeholder="请选择"
            @change="tgbjdxxhFun"
          >
            <el-option
              v-for="item in lineOption5.wireStatusOption"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 导线型号 </span>
        </div>
        <div class="line-input">
          <el-select v-model="lineForm5.wireModel" placeholder="请选择">
            <el-option
              v-for="item in lineOption5.wireModelOption"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 杆头状态 </span>
        </div>
        <div class="line-input">
          <el-select v-model="lineForm5.ganStatus" placeholder="请选择">
            <el-option
              v-for="item in lineOption5.ganStatusOption"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <div style="background: #e4f2f2; height: 50px" class="line">
        <div class="line-bnt" @click="lineDytgbjClick">绘制</div>
      </div>
    </template>
  </data-dialog>
  <!-- 7.导线绘制 -index==6 -->
  <data-dialog @close="closeDialog" v-if="appStore.mapIndex == '导线绘制'">
    <template #header>
      <h4 style="margin-left: 10px; margin-top: 0px; line-height: 39px">
        导线绘制
      </h4>
    </template>
    <template #body>
      <div class="line">
        <div class="line-item">
          <span> 电压等级 </span>
        </div>
        <div class="line-input">
          <el-select
            v-model="lineForm6.voltageLevel"
            placeholder="请选择"
            @change="dxhzLoopList"
          >
            <el-option
              v-for="item in lineOption6.voltageLevelOption"
              :key="item.id"
              :label="item.text"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 回路 </span>
        </div>
        <div class="line-input">
          <el-select
            v-model="lineForm6.loop"
            placeholder="请选择"
            @change="loopShowLine"
          >
            <el-option
              v-for="item in lineOption6.loopOption"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 绘制设置 </span>
        </div>
        <div class="line-input">
          <el-checkbox
            class="check_radio"
            v-model="lineForm6.checkRadio"
            label="自动推导杆塔型号"
          />
        </div>
      </div>
      <div
        style="
          background: #e4f2f2;
          width: 100%;
          height: 30px;
          display: flex;
          align-items: center;
        "
      >
        <div
          style="width: 3px; height: 20px; background: #07888a; margin: 0 10px"
        ></div>
        <div class="title-text">线路绘制</div>
      </div>
      <div class="line">
        <el-table class="input-table" size="small" :data="data" border>
          <el-table-column
            width="90"
            label="线路名称"
            align="center"
            prop="name"
          >
            <template #default="scope">
              <el-select
                filterable
                allow-create
                default-first-option
                :reserve-keyword="false"
                size="small"
                style="width: 70px"
                v-model="scope.row.name"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in lineOption6.tableNameOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column width="90" label="状态" align="center" prop="key">
            <template #default="scope">
              <el-select
                size="small"
                style="width: 70px"
                v-model="scope.row.key"
                placeholder="请选择"
                @change="dxhzdxxhFun(scope.row.key, scope.$index)"
              >
                <el-option
                  v-for="item in lineOption6.tableKeyOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="导线型号" align="center" prop="sk">
            <template #default="scope">
              <el-select
                size="small"
                style="width: 180px"
                v-model="scope.row.sk"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in lineOption6.tableSkOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div style="background: #e4f2f2; height: 50px" class="line">
        <div class="line-bnt" @click="lineDxhzClick">绘制</div>
      </div>
    </template>
  </data-dialog>
  <!-- 8.交叉跨域 -index==7 -->
  <data-dialog @close="closeDialog" v-if="appStore.mapIndex == '交叉跨域'">
    <template #header>
      <h4 style="margin-left: 10px; margin-top: 0px; line-height: 39px">
        交叉跨域
      </h4>
    </template>
    <template #body>
      <el-form
        :model="lineForm7"
        ref="crossForm"
        :rules="crossRules"
        label-width="0px"
      >
        <el-form-item>
          <div class="line">
            <div class="line-item">
              <span> 跨越物级别 </span>
            </div>
            <div class="line-input">
              <!-- v-model="lineForm7.spanGrade" -->

              <el-checkbox
                @change="handleCrossCheckbox"
                class="check_radio"
                label="重点跨越物"
              />
            </div>
          </div>
        </el-form-item>
        <el-form-item>
          <div class="line">
            <div class="line-item">
              <span> 跨越物类型 </span>
            </div>
            <div class="line-input">
              <el-select
                v-model="lineForm7.spanClass"
                @change="handleCross"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in crossList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </div>
          </div>
        </el-form-item>
        <el-form-item prop="spanWidth">
          <div class="line">
            <div class="line-item">
              <span> 跨越物宽度 </span>
            </div>
            <div class="line-input">
              <el-input
                :disabled="lineWidthFlag"
                class="in-item"
                v-model="lineForm7.spanWidth"
                placeholder="请输入内容"
              ></el-input>
            </div>
          </div>
        </el-form-item>
        <el-form-item prop="spanHeight">
          <div class="line">
            <div class="line-item">
              <span> 跨越物高度 </span>
            </div>
            <div class="line-input">
              <el-input
                :disabled="lineHeightFlag"
                class="in-item"
                v-model="lineForm7.spanHeight"
                placeholder="请输入内容"
              ></el-input>
            </div>
          </div>
        </el-form-item>
        <el-form-item prop="voltageLevel">
          <div class="line" v-if="crossFlag == 'DLX'">
            <div class="line-item">
              <span> 电压等级 </span>
            </div>
            <div class="line-input">
              <el-input
                class="in-item"
                v-model="lineForm7.voltageLevel"
                placeholder="请输入电压等级"
              ></el-input>
            </div>
          </div>
        </el-form-item>
        <el-form-item prop="houseNum">
          <div class="line" v-if="crossFlag == 'FW'">
            <div class="line-item">
              <span> 房屋层数 </span>
            </div>
            <div class="line-input">
              <el-input
                class="in-item"
                v-model="lineForm7.houseNum"
                placeholder="请输入房屋层数"
              ></el-input>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <div style="background: #e4f2f2; height: 50px" class="line">
        <div @click="crossOverClick" class="line-bnt">绘制</div>
      </div>
    </template>
  </data-dialog>
  <!-- 9.插入杆塔 -- 8> -->
  <data-dialog @close="closeDialog" v-if="appStore.mapIndex == '插入杆塔'">
    <template #header>
      <h4 style="margin-left: 10px; margin-top: 0px; line-height: 39px">
        插入杆塔
      </h4>
    </template>
    <template #body>
      <div class="line">
        <div class="line-item">
          <span> 插入方式 </span>
        </div>
        <div class="line-input">
          <el-radio-group class="radio-group" v-model="lineForm8.checkRadio" @input="checkRadioChange">
            <el-radio value="1" size="small">自由插入</el-radio>
            <el-radio value="2" size="small">等距插入</el-radio>
            <el-radio value="3" size="small">等分插入</el-radio>
          </el-radio-group>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 电压等级 </span>
        </div>
        <div class="line-input">
          <el-select
            v-model="lineForm8.voltageLevel"
            placeholder="请选择"
            @change="crgtVoltageLevelChange"
          >
            <el-option
              v-for="item in lineOption8.voltageLevelOption"
              :key="item.id"
              :label="item.text"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 杆塔级别 </span>
        </div>
        <div class="line-input">
          <el-select
            v-model="lineForm8.ganClass"
            placeholder="请选择"
            @change="ganClassCrgtChange"
          >
            <el-option
              v-for="item in lineOption8.ganClassOption"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <div v-if="lineForm8.ganClass === '钢管杆'">
        <div class="line">
          <div class="line-item">
            <span> 钢管杆杆高 </span>
          </div>
          <div class="line-input">
            <el-select v-model="lineForm8.gggHeight" placeholder="请选择">
              <el-option
                v-for="item in lineOption8.gggHeightOption"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </div>
        </div>
        <div class="line">
          <div class="line-item">
            <span> 形式 </span>
          </div>
          <div class="line-input">
            <el-select v-model="lineForm8.gggXs" placeholder="请选择">
              <el-option
                v-for="item in lineOption8.gggXsOption"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </div>
        </div>
      </div>
      <div v-else-if="lineForm8.ganClass === '水泥杆'">
        <div class="line">
          <div class="line-item">
            <span> 水泥杆型号 </span>
          </div>
          <div class="line-input">
            <el-select v-model="lineForm8.cementModel" placeholder="请选择">
              <el-option
                v-for="item in lineOption8.cementModelOption"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </div>
        </div>
        <div class="line" v-if="lineForm8.checkbox === true">
          <div class="line-item">
            <span> 横担长度 </span>
          </div>
          <div class="line-input">
            <el-input
              class="in-item"
              v-model="lineForm8.crossLength"
              placeholder="请输入内容"
            ></el-input>
          </div>
        </div>
      </div>
      <div v-else>
        <div class="line">
          <div class="line-item">
            <span>
              {{
                lineForm8.ganClass === "宽基塔" ||
                lineForm8.ganClass === "窄基塔"
                  ? "铁塔型号"
                  : "双杆型号"
              }}
            </span>
          </div>
          <div class="line-input">
            <el-select v-model="lineForm8.TtOrSg" placeholder="请选择">
              <el-option
                v-for="item in lineOption8.TtOrSgOption"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </div>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 基础 </span>
        </div>
        <div class="line-input">
          <el-select v-model="lineForm8.foundation" placeholder="请选择">
            <el-option
              v-for="item in lineOption8.foundationOption"
              :key="item.value"
              :label="item.text"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 土质 </span>
        </div>
        <div class="line-input">
          <el-select v-model="lineForm8.soil" placeholder="请选择">
            <el-option
              v-for="item in lineOption8.soilOption"
              :key="item.value"
              :label="item.text"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 地形 </span>
        </div>
        <div class="line-input">
          <el-select v-model="lineForm8.terrain" placeholder="请选择">
            <el-option
              v-for="item in lineOption8.terrainOption"
              :key="item.value"
              :label="item.text"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 状态 </span>
        </div>
        <div class="line-input">
          <el-select v-model="lineForm8.status" placeholder="请选择">
            <el-option
              v-for="item in lineOption8.statusOption"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span>
            {{
              lineForm8.checkRadio === "2"
                ? "杆塔间距"
                : lineForm8.checkRadio === "3"
                ? "杆塔数量"
                : "距所选参照杆距离"
            }}
          </span>
        </div>
        <div class="line-no-display">
          <el-input
            style="width: 100px; height: 27px; margin-left: 17px"
            v-model="lineForm8.referenceDistance"
            placeholder="整数"
          ></el-input>
          <span style="font-size: 12px; font-weight: normal">{{
            lineForm8.checkRadio === "3" ? "个" : "米"
          }}</span>
          <span
            style="font-size: 12px; font-weight: normal; margin-left: 10px"
            v-if="lineForm8.checkRadio === '2'"
            >范围: 0~80</span
          >
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 绘制设置 </span>
        </div>
        <div class="line-no-display">
          <el-checkbox
            class="check_radio"
            style="margin-left: 17px"
            label="自动选型"
            v-model="lineForm8.checkbox"
          ></el-checkbox>
        </div>
      </div>
      <div style="background: #e4f2f2; height: 50px" class="line">
        <div class="line-bnt" @click="insertTowerClick">绘制</div>
      </div>
    </template>
  </data-dialog>
  <!--  10.低压下户 -- 9> -->
  <data-dialog @close="closeDialog" v-if="appStore.mapIndex == '低压下户'">
    <template #header>
      <h4 style="margin-left: 10px; margin-top: 0px; line-height: 39px">
        低压下户
      </h4>
    </template>
    <template #body>
      <div class="line">
        <div class="line-item">
          <span> 下户描述分类 </span>
        </div>
        <div class="line-input">
          <el-radio-group
            class="radio-group"
            v-model="lineForm9.checkRadio"
            @change="dxhhFlChange"
          >
            <el-radio value="1" size="small">导线入户</el-radio>
            <el-radio value="2" size="small">导线沿墙</el-radio>
            <el-radio value="3" size="small">插入户表</el-radio>
          </el-radio-group>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 电压等级 </span>
        </div>
        <div class="line-input">
          <el-select v-model="lineForm9.voltageLevel" placeholder="请选择">
            <el-option
              v-for="item in lineOption9.voltageLevelOption"
              :key="item.id"
              :label="item.text"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 表箱类型 </span>
        </div>
        <div class="line-input">
          <el-select v-model="lineForm9.meterBoxClass" placeholder="请选择">
            <el-option
              v-for="item in lineOption9.meterBoxClassOption"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 状态 </span>
        </div>
        <div class="line-input">
          <el-select
            v-model="lineForm9.status"
            placeholder="请选择"
            @change="dyxhXhxXh"
          >
            <el-option
              v-for="item in lineOption9.statusOption"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="line" v-if="lineForm9.checkRadio === '3'">
        <div class="line-item">
          <span> 编号 </span>
        </div>
        <div class="line-input">
          <el-input
            style="width: 255px"
            v-model="lineForm9.bh"
            placeholder="请输入"
          ></el-input>
        </div>
      </div>
      <div class="line" v-if="lineForm9.checkRadio === '3'">
        <div class="line-item">
          <span> 表数 </span>
        </div>
        <div class="line-input">
          <el-input
            style="width: 255px"
            v-model="lineForm9.bs"
            placeholder="请输入"
          ></el-input>
        </div>
      </div>
      <div class="line">
        <el-table class="input-table" size="small" :data="dataDyxh" border>
          <el-table-column label="序号" align="center" type="index">
          </el-table-column>
          <el-table-column label="导线型号" align="center" prop="key">
            <template #default="scope">
              <div>
                <el-select
                  size="small"
                  style="width: 150px"
                  v-model="scope.row.xh"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in lineOption9.tableKeyOption"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="下户线长度" align="center" prop="sk">
            <template #default="scope">
              <div>
                <el-input
                  v-if="lineForm9.checkRadio !== '2'"
                  size="small"
                  v-model="scope.row.cd"
                  placeholder=""
                ></el-input>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div style="background: #e4f2f2; height: 50px" class="line">
        <div class="line-bnt">绘制</div>
      </div>
    </template>
  </data-dialog>
  <!--  11.接地绘制 -- 10> -->
  <data-dialog @close="closeDialog" v-if="appStore.mapIndex == '接地绘制'">
    <template #header>
      <h4 style="margin-left: 10px; margin-top: 0px; line-height: 39px">
        接地绘制
      </h4>
    </template>
    <template #body>
      <div class="line">
        <div class="line-item">
          <span> 状态 </span>
        </div>
        <div class="line-input">
          <el-select
            class="in-item"
            v-model="lineForm10.status"
            placeholder="请选择"
          >
            <el-option label="新建" value="New"></el-option>
            <el-option label="原有" value="Original"></el-option>
            <el-option label="拆除" value="Remove"></el-option>
          </el-select>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 适用设备 </span>
        </div>
        <div class="line-input">
          <el-select
            class="in-item"
            v-model="lineForm10.device"
            placeholder="请选择"
          >
            <el-option label="水泥杆" value="水泥杆"></el-option>
          </el-select>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 接地类型 </span>
        </div>
        <div class="line-input">
          <el-select
            class="in-item"
            @change="handleGrounding"
            v-model="lineForm10.groundingType"
            placeholder="请选择"
          >
            <el-option value="环形接地">环形接地</el-option>
            <el-option value="放射形接地">放射形接地</el-option>
            <el-option value="防雷引下接地">防雷引下接地</el-option>
          </el-select>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 接地型号 </span>
        </div>
        <div class="line-input">
          <el-select
                  v-model="lineForm10.groundingModel"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in jdhzList"
                    :key="item.moduleid"
                    :label="item.modulename"
                    :value="item.moduleid"
                  >
                  </el-option>
                </el-select>
          <!-- <el-input
            class="in-item"
            v-model="lineForm10.groundingModel"
            placeholder="请输入内容"
          ></el-input> -->
        </div>
      </div>
      <div style="background: #e4f2f2; height: 50px" class="line">
        <div @click="lineClick" class="line-bnt">绘制</div>
      </div>
    </template>
  </data-dialog>
  <data-dialog @close="closeDialog" v-if="appStore.mapIndex == '带电作业'">
    <template #header>
      <h4 style="margin-left: 10px; margin-top: 0px; line-height: 39px">
        带电作业
      </h4>
    </template>
    <template #body>
      <div style="height: 555px; overflow: auto">
        <el-table
          border
          class="input-table"
          size="small"
          @selection-change="handleSelectionChange2"
          :data="filteredTableData"
        
          style="width: 100%;height: 600px;"
        >
        <el-table-column
        
            align="center"
            label="是否显示"
            type="selection"
            width="50px"
          ></el-table-column>
          <el-table-column
            align="center"
            type="index"
            label="序号"
            width="50px"
          >
          </el-table-column>
          <el-table-column
            align="center"
            prop="moduleName"
            label="带电作业名称"
         
          >
          </el-table-column>
        </el-table>
        <div style="background: #e4f2f2; height: 50px" class="line">
          <div @click="DdianClick" class="line-bnt">确认</div>
        </div>
      </div>
    </template>
  </data-dialog>
</template>
<script setup>
import DataDialog from "@/components/DataDialog/index.vue";
import useAppStore from "@/store/modules/app.js";
import {
  getBasicTower,
  getData,
  getDataByKey,
  getDicts,
  getEleicLevel,
  getEngineerLine,
  getOriginalRodLineJointDraw,
  getPullScheme,
  getPullType,
  getTableScheme,
  getCementPoleHeight,
  getModuleByTypeKeys
} from "@/api/desginManage/GccsDialog.js";
import {
  GetSpanCategory,
  GetPageData,
  GetRodEquipmentNumber,
  getFacilitiesType,
  getModuleByParentKey,
  getGroundingList,
} from "@/api/baseDate/onlineDesign";
import { getHengDanLengthData } from "../../../../api/desginManage/GccsDialog";
import { useIframeCommunication } from "@/hooks/useIframeCommunication";
import { ElMessage } from "element-plus";
import { generateUuid } from "@/utils";
import { stringifyQuery } from "vue-router";
import {
  voltageLevelData,
  getLegendTypeKeyByTowerType,
  getlegendTypeKey,
  getSymbolIdByTowerType
} from "@/views/pages/online/commonData.js";
import { drawEndTowerLine, drawEndZSSB, drawEndDlSg, drawEndjdhz, drawEndydjdh, drawEndLX, drawEndDXHZ, drawEndZsbyq } from "../sdkDrawBackData/overheadModuleSDK.js";
import useProjectInfoStore from "@/store/modules/projectInfo.js";
import { insertTower } from "../sdkDrawBackData/insertTower.js";


const { sendMessage, cleanup } = useIframeCommunication();
const cadAppRef = inject("cadAppRef");
const appStore = useAppStore();
const projectInfoStore = useProjectInfoStore();
const closeDialog = () => {
  appStore.mapIndex = "";
  daidianParams.value=[]
};
const { proxy } = getCurrentInstance();
//!增加柱上设备绘制 --start
const dataList = ref({
  fitterTowerList: [],
  columnDeviceList: [
    { label: "柱上负荷开关", value: "251701" },
    { label: "柱上断路器", value: "251404" },
    { label: "柱上避雷器", value: "251302" },
    { label: "跌落式熔断器", value: "251602" },
    { label: "柱上隔离开关", value: "251501" },
  ],
  statusList: [],
  deviceModelList: [],
});
const filteredTableData=ref([])
const daidianParams=ref([])
const lineItemTitle = ref("水泥杆");
const disabledLine = ref(true);
const dataDyxh = ref([{ xh: "", cd: "" }]);
const taskId = new URLSearchParams(new URL(window.location.href).search).get(
  "id"
);
const data = ref([
  {
    name: "线路1",
    key: "New",
    sk: " JG/TH",
    ksd: "删除",
  },
]);
const show = ref({
  sng: true, // 水泥杆
  ggg: true, // 钢管杆
  snsg: true, // 水泥双杆
  kjt: true, // 宽基塔
  zjt: true, // 窄基塔
});
const lineOption9 = ref({
  voltageLevelOption: voltageLevelData.filter(
    (o) => o.id === "1" || o.id === "2" || o.id === "3"
  ),
  meterBoxClassOption: [
    { label: "单相表箱", value: "单相表箱" },
    { label: "三相表箱", value: "三相表箱" },
  ],
  statusOption: [],
  tableKeyOption: [],
});
const lineOption8 = ref({
  voltageLevelOption: voltageLevelData.filter(
    (o) => o.id === "1" || o.id === "2" || o.id === "3"
  ),
  ganClassOption: [],
  cementModelOption: [],
  gggHeightOption: [],
  gggXsOption: [],
  TtOrSgOption: [],
  foundationOption: [],
  soilOption: [],
  terrainOption: [],
});
const lineOption6 = ref({
  voltageLevelOption: voltageLevelData.filter(
    (o) => o.id === "1" || o.id === "2" || o.id === "3"
  ),
  loopOption: [],
  tableNameOption: [],
  tableKeyOption: [],
  tableSkOption: [],
});
const lineOption5 = ref({
  ganStatusOption: [],
  wireStatusOption: [],
  wireModelOption: [],
  lineNameOption: [],
  voltageLevelOption: voltageLevelData.filter(
    (o) => o.id === "1" || o.id === "2" || o.id === "3"
  ),
});
const lineOption2 = ref({
  voltageLevelOption: voltageLevelData.filter(
    (o) => o.id === "1" || o.id === "0"
  ),
  GHeightOption: [],
  statusOption: [],
  columnClassOption: [],
  columnOption: [],
  isCompleteOption: [
    { value: "是", label: "是" },
    { value: "否", label: "否" },
  ],
});
// 定义线路绘制 下拉选项
const lineOption = ref({
  voltageLevelOption: voltageLevelData.filter(
    (o) => o.id === "1" || o.id === "2" || o.id === "3"
  ),
  loopOption: [], //
  statusOption: [{ value: "新建", label: "新建" }], // 状态
  cementOption: [], // 根据类型显示不同(默认是水泥杆)
  foundationOption: [], // 基础
  soilOption: [], // 土质
  terrainOption: [], // 地形
  gggGgOption: [], // 钢管杆-高杆
  gggXsOption: [], // 钢管杆-形式
  tableNameOption: [], // 表格-线路名称
  tableKeyOption: [], // 表格-状态
  tableSkOption: [], // 表格导线型号
  crossLengthOption: [],
});
const lineOptionLx = ref({
  voltageLevelOption: voltageLevelData.filter(
    (o) => o.id === "1" || o.id === "2"
  ),
  statusOption: [],
  lxTypeOption: [],
  lxfaOption: [],
});
const lineOption4 = ref({
  deviceClassOption: [],
  deviceModelOption: [],
  statusOption: [],
});
// 定义线路拉线绘制 form表单
const lineFormLx = ref({
  voltageLevel: "",
  status: "",
  lxType: "",
  lxfa: "",
});
// 定义线路绘制 form表单
const lineForm = ref({
  crossLength: "",
  gggGg: "", //钢管杆-杆高
  gggXs: "", //钢管杆-形式
  voltageLevel: "", //电压等级
  loop: "", //回路
  status: "", //状态
  cement: "", //水泥杆
  foundation: "", //基础
  soil: "", //土质
  terrain: "", //地形
  ganNum: "4", //杆塔数量
  stepSize: "50", //档距步长
  ganPrefix: "", //杆塔前缀
  ganSuffix: "", //杆塔后缀
  ganStart: "1", //起始杆号
  ganAddNum: "1", //杆号增量
  checkList1: "水泥杆", //杆塔类型
  drawingSetting:
    projectInfoStore.getBaseSetting("towerlineSetting") &&
    projectInfoStore.getBaseSetting("towerlineSetting")["NaiZhangMaxLength"], //绘制设置
  checkList2: ["2", "4"], //绘制设置
});
//定义 柱上变压器绘制form表单
const lineForm2 = ref({
  voltageLevel: "",
  GHeight: "", // 杆高
  status: "", //状态
  columnClass: "", //柱上变台方案类别
  column: "", //柱上变台方案
  isComplete: "", //是否成套化
  tower: "", //变台台区
  towerCode: "", //变台编号
});
//定义柱上设备绘制 form表单
const lineForm3 = ref({
  fitterTower: "", //适配杆塔
  columnDeviceType: "", //柱上设备类别
  voltageLevel: "10kV", //电压等级
  status: "", //状态
  deviceModel: "", //设别型号
  isFlag: "否", //是否存在智能开关
  deviceCode: "", //设备编号
});

//定义附属设施绘制 form表单
const lineForm4 = ref({
  deviceModel: "", //设备型号
  deviceClass: "", //附属设备类型
  status: "New", //状态
});

//定义同杆并架绘制 form表单
const lineForm5 = ref({
  voltageLevel: "", //电压等级
  lineName: "", //线路名称
  wireModel: "", //导线型号
  wireStatus: "", //导线状态
  ganStatus: "", //杆头状态
});
//定义导线绘制 form表单
const lineForm6 = ref({
  voltageLevel: "", //电压等级
  loop: "", //回路
  checkRadio: "", //绘制实施
});
//定义 交叉跨域form表单
const lineForm7 = ref({
  spanGrade: false, //跨越物级别
  spanClass: "TXX", //跨越物类型
  spanHeight: "", //跨越物高度
  spanWidth: "", //跨越物宽度
  houseNum: "", //房屋层数
  voltageLevel: "", //电压等级
});
//定义 插入杆塔 form表单
const lineForm8 = ref({
  TtOrSg: "", // 铁塔型号或者水泥双杆型号
  gggXs: "", // 钢管杆形式
  gggHeight: "", // 钢管杆杆高
  checkRadio: "1", //插入方式
  voltageLevel: "", //电压等级
  ganClass: "", //杆塔类别
  cementModel: "", //水泥杆型号
  crossLength: "1500", //横担长度
  foundation: "", //基础
  soil: "", //土质
  terrain: "", //地形
  status: "", //状态
  referenceDistance: "", //距所选参照杆距离
  checkbox: true, //绘制设置
});

//定义 低压下户 form表单
const lineForm9 = ref({
  checkRadio: "1", //下户描述分类1
  voltageLevel: "", //电压等级
  meterBoxClass: "", //表箱类型
  status: "", //状态
  bh: "", // 编号
  bs: "", //表数
});
//定义 接地绘制 form表单
const lineForm10 = ref({
  status: "", //状态
  groundingType: "", //接地类型
  groundingModel: "", //接地型号
  device: "", //适用设备
});
// 同杆并架-导线型号
const tgbjdxxhFun = (val) => {
  const voltageLevelValue = lineOption5.value.voltageLevelOption.find(
    (item) => item.id === lineForm5.value.voltageLevel
  ).value;
  getOriginalRodLineJointDraw({
    voltageLevel: voltageLevelValue,
    status: val,
    engineeringId: taskId,
  }).then((res) => {
    lineOption5.value.wireModelOption = res.data.map((item) => ({
      label: item.moduleName,
      value: item.moduleId,
    }));
    lineForm5.value.wireModel =
      lineOption5.value.wireModelOption.length > 0
        ? lineOption5.value.wireModelOption[0].value
        : "";
  });
};
// 导线绘制-根据状态获取导线型号
const dxhzdxxhFun = (val, lineIndex) => {
  const voltageLevelValue = lineOption6.value.voltageLevelOption.find(
    (item) => item.id === lineForm6.value.voltageLevel
  ).value;
  getOriginalRodLineJointDraw({
    voltageLevel: voltageLevelValue,
    status: val,
    engineeringId: taskId,
  }).then((res) => {
    lineOption6.value.tableSkOption = res.data.map((item) => ({
      label: item.moduleName,
      value: item.moduleId,
      legendTypeKey: item.legendTypekey,
      sdkClassName: item.sdkClassName
    }));
    // 只更新对应线路的默认值
    if (data.value[lineIndex]) {
      data.value[lineIndex].key = val; // 更新状态
      data.value[lineIndex].sk =
        lineOption6.value.tableSkOption[0]?.value || ""; // 设置默认值
    }
  });
};
const mapMethods = (arr) => {
  const outputArray = arr.map((item) => {
    const dynamicKey1 = Object.keys(item)[0]; // 获取第一个动态键名
    const value = item[dynamicKey1];
    return {
      label: dynamicKey1, // 使用第一个动态键名
      value: value, // 使用第二个动态键名
    };
  });
  return outputArray;
};
// 导线绘制-选择回路数显示多少线路
const loopShowLine = (value) => {
  const lineCountMap = {
    单回: 1,
    双回: 2,
    三回: 3,
    四回: 4,
  };
  const count = lineCountMap[value] || 0; // 根据 value 获取线路数量，默认 0
  data.value = Array.from({ length: count }, (_, index) => ({
    name: `线路${index + 1}`,
    key: "New",
    sk: lineOption6.value.tableSkOption[0]?.value || "", // 安全处理，防止 tableSkOption 为空
  }));
};
// 导线绘制-根据电压等级判断回路数
const dxhzLoopList = () => {
  if (lineForm6.value.voltageLevel === "1") {
    // 10kV
    lineOption6.value.loopOption = [
      { label: "单回", value: "单回" },
      { label: "双回", value: "双回" },
      { label: "三回", value: "三回" },
      { label: "四回", value: "四回" },
    ];
  } else {
    lineOption6.value.loopOption = [{ label: "单回", value: "单回" }];
  }
  lineForm6.value.loop =
    lineOption6.value.loopOption.length > 0
      ? lineOption6.value.loopOption[0].value
      : "";
  loopShowLine(lineForm6.value.loop);
};
// 拉线绘制-根据电压等级获取拉线方案
const voltageLevelGetLxfa = (value) => {
  getPullSchemeModule(value, lineFormLx.value.lxType, lineFormLx.value.status);
};
// 拉线绘制-根据状态筛选拉线方案
const stateLXChange = (value) => {
  getPullSchemeModule(
    lineFormLx.value.voltageLevel,
    lineFormLx.value.lxType,
    value
  );
};
// 拉线绘制-根据拉线类型获取拉线方案
const lxTypeGetLxfa = (value) => {
  getPullSchemeModule(
    lineFormLx.value.voltageLevel,
    value,
    lineFormLx.value.status
  );
};
/**
 * 获取拉线模块数据
 * @param voltage 电压等级
 * @param moduleTypeKey 拉线模块类型
 * @param lxStates 拉线状态
 */
const getPullSchemeModule = (voltage, moduleTypeKey, lxStates) => {
  getPullScheme({ voltageLevel: voltage, moduleTypeKey: moduleTypeKey }).then(
    (res) => {
      let stateflag =
        lxStates === "Remove" &&
        res.data.findIndex((o) => o.stateflag === 1) >= 0
          ? 1
          : 0;
      lineOptionLx.value.lxfaOption =
        res.data.length > 0
          ? res.data
              .filter((o) => o.stateflag === stateflag)
              .map((item) => ({
                label: item.modulename,
                value: item.moduleid,
                symbolId: item.symbolId,
                sdkClassName: item.sdkClassName
              }))
          : [];
      lineFormLx.value.lxfa =
        lineOptionLx.value.lxfaOption.length > 0
          ? lineOptionLx.value.lxfaOption[0].value
          : "";
    }
  );
};

// 低压下户获取下户线型号
const dyxhXhxXh = (val) => {
  const voltageLevelValue = lineOption9.value.voltageLevelOption.find(
    (item) => item.id === lineForm9.value.voltageLevel
  ).value;
  getOriginalRodLineJointDraw({
    voltageLevel: voltageLevelValue,
    status: val,
    engineeringId: taskId,
  }).then((res) => {
    lineOption9.value.tableKeyOption = res.data.map((item) => ({
      label: item.moduleName,
      value: item.moduleId,
    }));
    dataDyxh.value = [
      {
        xh:
          lineOption9.value.tableKeyOption.length > 0
            ? lineOption9.value.tableKeyOption[0].value
            : "",
        cd: "3",
      },
    ];
  });
};
// 插入杆塔-电压等级事件
const crgtVoltageLevelChange = (val) => {
  if (val === "4") {
    lineOption8.value.ganClassOption = [{ label: "宽基塔", value: "宽基塔" ,className:"PWPolePSR" }];
  } else if (val === "3") {
    lineOption8.value.ganClassOption = [{ label: "水泥杆", value: "水泥杆" ,className:"PWPolePSR" }];
  } else if (val === "2") {
    lineOption8.value.ganClassOption = [
      { label: "水泥杆", value: "水泥杆" ,className:"PWPolePSR" },
      { label: "钢管杆", value: "钢管杆" ,className:"PWPolePSR" },
    ];
  } else {
    lineOption8.value.ganClassOption = [
      { label: "水泥杆", value: "水泥杆" ,className:"PWPolePSR" },
      { label: "钢管杆", value: "钢管杆",className:"PWPolePSR"  },
      { label: "窄基塔", value: "窄基塔",className:"PWPolePSR"  },
      { label: "宽基塔", value: "宽基塔" ,className:"PWPolePSR" },
      { label: "水泥双杆", value: "水泥双杆" ,className:"PWPolePSR" },
    ];
  }
  lineForm8.value.ganClass = lineOption8.value.ganClassOption[0].value || "";
};
// 插入杆塔
const ganClassCrgtChange = () => {
  //  获取杆塔基础
  getBasicTower({ towerType: lineForm8.value.ganClass }).then((res) => {
    lineOption8.value.foundationOption = res.data;
    lineForm8.value.foundation = res.data[0].value;
  });
};
const handleSelectionChange2=(val)=>{
  daidianParams.value=val

}
// const msg = {
//     content: "Ancillary_Facilities_Annotation",
//     type: "onlycad",
//     options:{name:'JGFZH_Draw'}
//   }
const DdianClick=()=>{
  if( daidianParams.value.length==0)return  proxy.$message.warning("请选择至少一条数据");
   const msg = {
    content: "Mx_hotLineWork",
    type: "onlycad",
    options:{name:'JGFZH_Draw',tableData:JSON.stringify(daidianParams.value)}
    // param:{labelList:daidianParams.value}
  }
  sendMessage(cadAppRef.value, msg, (res => {
   console.log(res,'ddddddddd')
  }))
}
// 绘制插入杆塔
const insertTowerClick = () => {
  const symbolId =
    lineForm8.value.ganClass === "水泥杆"
      ? "252011"
      : lineForm8.value.ganClass === "水泥双杆"
      ? "252018"
      : lineForm8.value.ganClass === "钢管杆"
      ? "252008"
      : lineForm8.value.ganClass === "窄基塔"
      ? "252016"
      : lineForm8.value.ganClass === "宽基塔"
      ? "252016"
      : null;
  if (!symbolId) {
    proxy.$message.error("未能找到相关图元");
    return;
  }
  const options = {
    symbolId,
    ...lineForm8.value,
  };
  console.log(options,'return')
  // return;
  sendMessage(
    cadAppRef.value,
    { type: "greeting", content: "SDK_cgt", options },
    (res) => {
      console.log("子页面返回的数据", res);
      insertTower(res.res.result.data,options)
    }
  );
};

const lineItemTitleId = ref("252011"); // 默认水泥杆
const towerTypeChage=ref('水泥杆')
// 绘制线路杆塔类型事件
const changeLineRadio = (val) => {
  const towerType = val;
  towerTypeChage.value=val
  getBasicTowerFun(towerType);
  const voltageLevel = lineOption.value.voltageLevelOption.find(
    (item) => item.id === lineForm.value.voltageLevel
  ).text;
  getData({ voltageLevel: voltageLevel, taskId: taskId }).then((res) => {
    lineOption.value.loopOption = res.data.map((item) => ({
      label: item.HLtext,
      value: item.HLvalue,
    }));
    console.log(lineOption.value.loopOption,'llllllllllllllllll');
    
    // lineForm.value.loop =
    //   lineOption.value.loopOption.length > 0
    //     ? lineOption.value.loopOption[0].value
    //     : "";
    const entity = res.data.find(
      (item) => item.HLvalue === lineForm.value.loop
    );
    if (entity) {
      if (lineForm.value.checkList1 === "宽基塔") {
        lineOption.value.cementOption = [
          { label: "9m", value: "9" },
          { label: "12m", value: "12" },
          { label: "15m", value: "15" },
          { label: "21m", value: "21" },
          { label: "24m", value: "24" },
          { label: "27m", value: "27" },
        ];
      } else if (lineForm.value.checkList1 === "窄基塔") {
        lineOption.value.cementOption = [
          { label: "13m", value: "13" },
          { label: "15m", value: "15" },
          { label: "18m", value: "18" },
        ];
      } else {
        lineOption.value.cementOption = entity.cementTypeList.map((item) => ({
          label: item.spec,
          value: item.materialsprojectid,
        }));
      }

      lineForm.value.cement =
        lineOption.value.cementOption.length > 0
          ? lineOption.value.cementOption[0].value
          : "";
      lineOption.value.gggGgOption = entity.towerHeightList;
      lineForm.value.gggGg =
        lineOption.value.gggGgOption.length > 0
          ? lineOption.value.gggGgOption[0].value
          : "";
    }
  });
  // 根据杆塔类型获取状态
  const key = getLegendTypeKeyByTowerType(val);
  getDataByKey({ key: key }).then((res) => {
    lineOption.value.statusOption = res.msg.split(";").map((item) => {
      const [value, label] = item.split(",");
      return { label, value };
    });
    lineForm.value.status = lineOption.value.statusOption[0].value;
  });

  lineItemTitle.value = val
  lineItemTitleId.value = getSymbolIdByTowerType(val)
  if (val === "水泥杆" || val === "水泥双杆") {
    lineItemTitle.value = "水泥杆";
  } else if (val === "钢管杆") {
    lineItemTitle.value = "钢管杆";
  } else if (val === "宽基塔" || val === "窄基塔") {
    lineItemTitle.value = "塔高";
  }
  sendMessage(cadAppRef.value, 
    {
      type: "greeting",
      content: "SDK_gtlxxz",
      options: {
        symbolId: lineItemTitleId.value,
        extendInfo: {
          extendData: JSON.stringify(lineForm.value),
        },
      },
    },(res)=>{
      console.log('SDK_gtlxxz回调', res)
      if(res.cmd === "SDK_gtlxxz"){
        drawEndTowerLine(res.params.status === "000000" && res.params.result)
      }
  })
};
const lineJkClick = () => {
  console.log("🚀 ~ lineJkClick ~ lineForm.loop:", lineForm.value.loop)

  const lines = data.value.map((item) => {
    return {
      lineId: "",
      lineName: item.name,
      lineModuleId: item.sk,
      lineState: item.key,
    };
  });
  lineForm.value.lines = lines;
  console.log(lineForm.value,voltageLevelData.find(o=>o.id === lineForm.value.voltageLevel).value, "1122");
  if(lineForm.value.loop=='单回'){
 // 基础信息
  let baseInfo = {
    // 电压等级
    voltageLevel: voltageLevelData.find(o=>o.id === lineForm.value.voltageLevel).value,
    // 起始设备ID
    startDeviceId: "",
    // 线路信息
    lines: lines,
    // 杆塔图元id
    symbolId: lineItemTitleId.value, //角钢塔（直线型）:252016; 直线铁杆:252012; 直线钢管塔:252009;
  };
  // 设备信息
  let deviceInfo = {
    Substation: [
      // {
      //   ID: "30000000-102415770",
      //   DEV_ID: "30000000-102415770",
      //   VOLTAGELEVEL_ID: "32",
      //   CLASS_NAME: "Substation",
      //   CONNECTION: null,
      // },
    ],
    // PWPolePSR: [{NAME: "1111#"}],
  };
  let options = {
    baseInfo: baseInfo,
    deviceInfo: deviceInfo,
    extendInfo: {
      extendData: JSON.stringify(lineForm.value),
    },
  };
  sendMessage(
    cadAppRef.value,
    { type: "greeting", content: "SDK_xlhz", options },
    (res) => {
      console.log('SDK_xlhz回调', res)
      if(res.cmd === "SDK_xlhz"){
        drawEndTowerLine(res.params.status === "000000" && res.params.result)
      }
    }
  );
  }else{
    let baseInfo = {
          // 电压等级
          voltageLevel: voltageLevelData.find(o=>o.id === lineForm.value.voltageLevel).value,
          symbolId: "", //角钢塔（直线型）:252016; 直线铁杆:252012; 直线钢管塔:252009;252011
        };
        // 设备信息
        let deviceInfo = {
          Substation: [],
          PWPolePSR: [],
        };
        let options = {
          baseInfo: baseInfo,
          deviceInfo: deviceInfo,
        };
        sendMessage(
    cadAppRef.value,
    { type: "greeting", content: "SDK_slxlhz", options },
    (res) => {
      if(res.cmd === "SDK_slxlhz"){
        drawEndTowerLine(res.params.status === "000000" && res.params.result,lineForm.value)
      }
    }
  );
  }
 
};

// 柱上变台-绘制按钮
const lineZsbtClick = () => {
  if (
    lineForm2.value.GHeight == "" ||
    lineForm2.value.status == "" ||
    lineForm2.value.columnClass == "" ||
    lineForm2.value.column == "" ||
    lineForm2.value.isComplete == "" ||
    lineForm2.value.tower == "" ||
    lineForm2.value.towerCode == ""
  ) {
    proxy.$message.error("请填写完整信息");
  } else {
    // oniframeMessage({
    //   type: "greeting",
    //   content: "SDK_zsbd",
    //   options: { symbolId: "251901" },
    // }, (data)=>{

    // });
    sendMessage(
    cadAppRef.value,
    { type: "greeting", content: "SDK_zsbd", options: { symbolId: "251901",isAdd:false } },
    (res) => {
      console.log('SDK_zsbd柱上变台回调', res)
      drawEndZsbyq(res.params.result, lineForm2.value)
    }
  );
  }
};
// 拉线绘制-绘制按钮
const lineLxClick = () => {
  console.log('拉线页面：', lineFormLx)
  const {symbolId, sdkClassName} = lineOptionLx.value.lxfaOption.find(o=>o.value === lineFormLx.value.lxfa)
  let options = {
    symbolId,
  };
  sendMessage(
    cadAppRef.value,
    { type: "greeting", content: "SDK_lxhz", options},
    (res) => {
      console.log('SDK_lxhz回调', res)
      if(res.cmd === "SDK_lxhz"){
        drawEndLX(res.params.status === "000000" && res.params.result, lineFormLx.value, sdkClassName)
      }
    }
  );
};
//接地绘制
const lineClick=()=>{
  console.log(jdhzList.value,lineForm10.value.groundingModel,'sfsdfdgf')
  let arr=jdhzList.value.find(o=>o.moduleid===lineForm10.value.groundingModel)
  sendMessage(
    cadAppRef.value,
    { type: "greeting", content: "SDK_jdhz",options:{symbolId:arr.symbolId}},
    (res) => {
      console.log('SDK_jdhz柱上设备回调', res)
      drawEndjdhz(lineForm10.value,arr,res.params.result.data)
    }
  );
}
// 附属设施绘制-绘制按钮
const lineFsssClick = () => {
  const paramsListBox = zssbList.value.fssbList.find(
    (item) => lineForm4.value.deviceClass === item.moduletypekey
  );
  if (lineForm4.value.deviceClass === "FZC") {
    sendMessage(
        cadAppRef.value,
      { type: "onlycad", content: "MX_fssb", options: { ...lineForm4.value } },
      (res) => {
        console.log("子页面返回的数据", res);
      }
    );
  } else {
    if (!paramsListBox.symbolId) {
      return ElMessage.error("暂无此图元！");
    }
    const options = {
      drawingmethod: "Block",
      symbolId: paramsListBox.symbolId,
    };
    sendMessage(
        cadAppRef.value,
      { type: "greeting", content: "SDK_zssbsblx", options },
      (res) => {
        console.log("子页面返回的数据", res);
        if (res.params.status !== "000000") return;
        paramsList.value = res.params;
        drawEndydjdh(lineForm4.value,paramsListBox,res.params.result.data);
        cleanup();
      }
    );
  }
  /* if (paramsListBox.symbolId) {
    oniframeMessage({
      type: "greeting",
      content: "SDK_fssshz",
      columnDeviceType: paramsListBox.symbolId,
      drawingmethod: "Block",
    });
  } */
};
// 低压同杆并架-绘制按钮
const lineDytgbjClick = () => {
  alert("低压同杆并架");
};
// 导线-绘制按钮
const lineDxhzClick = () => {
  console.log(lineForm6.value, '导线绘制表单', data.value)
  const voltageLevel = lineOption6.value.voltageLevelOption.find(o=>o.id === lineForm6.value.voltageLevel).value
  let baseInfo = {
    voltageLevel,
    symbolId: "",
  };
  let deviceInfo = { Substation: [], PWPolePSR: []};
  let extendInfo = { extendData: {}};
  
  // 配置信息
  let options = {
    baseInfo,
    deviceInfo,
    extendInfo,
  };
  
  sendMessage(
    cadAppRef.value,
    { type: "greeting", content: "SDK_dxhz", options},
    (res) => {
      console.log('SDK_dxhz导线绘制回调', res)
      drawEndDXHZ(res.params.status === "000000" && res.params.result, lineForm6.value, data.value)
    }
  );
};
// 绘制设置的change事件
const checkboxChange = () => {
  if (lineForm.value.checkList2.indexOf("4") === -1) {
    disabledLine.value = false;
  } else {
    disabledLine.value = true;
  }
};
// 单回双回点击事件
const lineFormLoopChange = () => {
  // lineForm.value.checkList1 = "水泥杆";
  lineForm.value.checkList1=(lineForm.value.loop=='双回'&&towerTypeChage.value=='水泥双杆')?'水泥杆':towerTypeChage.value
  console.log(lineForm.value.loop,towerTypeChage.value);
 
  if (lineForm.value.loop === "双回") {
    data.value = [
      {
        name: "线路1",
        key: "New",
        sk: lineOption.value.tableSkOption[0].value,
        ksd: "删除",
      },
      {
        name: "线路2",
        key: "New",
        sk: lineOption.value.tableSkOption[0].value,
        ksd: "删除",
      },
    ];
    lineOption.value.cementOption = sngList.value[1].cementTypeList.map((item) => ({
          label: item.spec,
          value: item.materialsprojectid,
        }));
  } else {
    data.value = [
      {
        name: "线路1",
        key: "New",
        sk: lineOption.value.tableSkOption[0].value,
        ksd: "删除",
      },
    ];
    lineOption.value.cementOption = sngList.value[0].cementTypeList.map((item) => ({
          label: item.spec,
          value: item.materialsprojectid,
        }));
  }
  lineForm.value.cement =
        lineOption.value.cementOption.length > 0
          ? lineOption.value.cementOption[0].value
          : "";
  HengDanLengthDataFun();
};
// 插入杆塔-表单的radio选中事件
const checkRadioChange=()=>{
  lineForm8.value.referenceDistance=''
}
//架空删除线路按钮
const lineTableDel = (val) => {
  if (data.value.length === 1) {
    ElMessageBox.alert("至少要保留一条导线", "消息", {
      confirmButtonText: "OK",
      callback: (action) => {},
    });
  } else {
    data.value = data.value.filter((item) => item !== val);
  }
};
const sngList=ref([])
// 架空绘制线路压等级下拉事件
const lineFormVoltageLevelChange = (val) => {
  // 根据电压等级展示那些杆塔类型
  if (val === "4") {
    lineForm.value.checkList1 = "钢管杆";
    show.value.sng = false;
    show.value.ggg = true;
    show.value.snsg = false;
    show.value.kjt = false;
    show.value.zjt = false;
  } else if (val === "2" || val === "3") {
    lineForm.value.checkList1 = "水泥杆";
    show.value.sng = true;
    show.value.ggg = false;
    show.value.snsg = false;
    show.value.kjt = false;
    show.value.zjt = false;
  } else {
    lineForm.value.checkList1 = "水泥杆";
    show.value.sng = true;
    show.value.ggg = true;
    show.value.snsg = true;
    show.value.kjt = true;
    show.value.zjt = true;
  }
  const voltageLevel = lineOption.value.voltageLevelOption.find(
    (item) => item.id === lineForm.value.voltageLevel
  ).text;
  getData({ voltageLevel: voltageLevel, taskId: taskId }).then((res) => {
    sngList.value=res.data
    lineOption.value.loopOption = res.data.map((item) => ({
      label: item.HLtext,
      value: item.HLvalue,
    }));
    
    lineForm.value.loop =
      lineOption.value.loopOption.length > 0
        ? lineOption.value.loopOption[0].value
        : "";
    const entity = res.data.find(
      (item) => item.HLvalue === lineForm.value.loop
    );
    if (entity) {
      if (lineForm.value.checkList1 === "宽基塔") {
        lineOption.value.cementOption = [
          { label: "9m", value: "9" },
          { label: "12m", value: "12" },
          { label: "15m", value: "15" },
          { label: "21m", value: "21" },
          { label: "24m", value: "24" },
          { label: "27m", value: "27" },
        ];
      } else if (lineForm.value.checkList1 === "窄基塔") {
        lineOption.value.cementOption = [
          { label: "13m", value: "13" },
          { label: "15m", value: "15" },
          { label: "18m", value: "18" },
        ];
      } else {
        lineOption.value.cementOption = entity.cementTypeList.map((item) => ({
          label: item.spec,
          value: item.materialsprojectid,
        }));
      }

      lineForm.value.cement =
        lineOption.value.cementOption.length > 0
          ? lineOption.value.cementOption[0].value
          : "";
      lineOption.value.gggGgOption = entity.towerHeightList;
      lineForm.value.gggGg =
        lineOption.value.gggGgOption.length > 0
          ? lineOption.value.gggGgOption[0].value
          : "";
    }
    // 横担长度数据
    HengDanLengthDataFun();
  });
  getBasicTowerFun("水泥杆");
  // 地形土质下拉数据
  getDicts({ dictionarKeys: ["Geological", "Terrain", "SteelTower"] }).then(
    (res) => {
      lineOption.value.soilOption = res.data.Geological;
      lineForm.value.soil = res.data.Geological[0].value;
      lineOption.value.terrainOption = res.data.Terrain;
      lineForm.value.terrain = res.data.Terrain[0].value;
      lineOption.value.gggXsOption = res.data.SteelTower;
      lineForm.value.gggXs = res.data.SteelTower[0].value;
    }
  );
  // 线路状态
  getDataByKey({ key: "TY_JKDX" }).then((res) => {
    lineOption.value.tableKeyOption = res.msg.split(";").map((item) => {
      const [value, label] = item.split(",");
      return { label, value };
    });
  });
  tableKeyChange("New");
  getEngineerLineFun("架空").then((options) => {
    lineOption.value.tableNameOption = options; // 手动赋值
  });
};
// 读取线路名称接口
const getEngineerLineFun = (lineType) => {
  return getEngineerLine({ engineeringObjid: taskId, type: lineType }).then(
    (res) => {
      return res.data.map((item) => ({
        label: item.Name,
        value: item.Name,
      }));
    }
  );
};

const getBasicTowerFun = (towerType) => {
  //  获取杆塔基础
  getBasicTower({ towerType: towerType }).then((res) => {
    lineOption.value.foundationOption = res.data;
    lineForm.value.foundation = res.data[0].value;
  });
};
// 获取线路-导线型号
const tableKeyChange = (val) => {
  const voltageLevelValue = lineOption.value.voltageLevelOption.find(
    (item) => item.id === lineForm.value.voltageLevel
  ).value;
  getOriginalRodLineJointDraw({
    voltageLevel: voltageLevelValue,
    status: val,
    engineeringId: taskId,
  }).then((res) => {
    lineOption.value.tableSkOption = res.data.map((item) => ({
      label: item.moduleName,
      value: item.moduleId,
    }));
    data.value = [
      {
        name: "线路1",
        key: "New",
        sk: lineOption.value.tableSkOption[0].value,
        ksd: "删除",
      },
    ];
  });
};
const columnOptionList = ref([]);
// 绘制柱上变压器-是否成套化事件
const isCompleteChange = (val) => {
  lineOption2.value.columnOption = columnOptionList.value
    .filter((item) => item.isCompleteSet === val)
    .map((item) => ({
      label: item.modulename,
      value: item.moduleid,
      isComplete: item.isCompleteSet,
    }));
  lineForm2.value.column =
    lineOption2.value.columnOption.length > 0
      ? lineOption2.value.columnOption[0].value
      : "";
};
// 柱上变台方案类别列表与柱上变台列表
const changeFaFun = () => { 
  getTableScheme({
    voltageLevel: lineForm2.value.voltageLevel,
    status: lineForm2.value.status,
    rodHeight: lineForm2.value.GHeight,
  }).then((res) => {
    lineOption2.value.columnClassOption = res.data.moduleTypes.map((item) => ({
      label: item.moduletypename,
      value: item.moduletypekey,
    }));
    lineForm2.value.columnClass =
      lineOption2.value.columnClassOption.length > 0
        ? lineOption2.value.columnClassOption[0].value
        : "";
    columnOptionList.value = res.data.modules[lineForm2.value.columnClass];
    lineOption2.value.columnOption = columnOptionList.value
      ? columnOptionList.value.map((item) => ({
          label: item.modulename,
          value: item.moduleid,
          isComplete: item.isCompleteSet,
        }))
      : [];
    lineForm2.value.column =
      lineOption2.value.columnOption.length > 0
        ? lineOption2.value.columnOption[0].value
        : "";
    lineForm2.value.isComplete =
      lineOption2.value.columnOption.length > 0
        ? lineOption2.value.columnOption[0].isComplete
        : "";
  });
};
// 绘制线路-横担长度数据
const HengDanLengthDataFun = () => {
  getHengDanLengthData({ loopNum: lineForm.value.loop }).then((res) => {
    lineOption.value.crossLengthOption = res.data;
    lineForm.value.crossLength =
      lineOption.value.crossLengthOption.length > 0
        ? lineOption.value.crossLengthOption[0].value
        : "";
  });
};
const EquipmentMTList=ref([])
//!接地绘制  --end
watch(
  () => appStore.mapIndex,
  (newInfo, oldInfo) => {
    if (newInfo == "线路绘制") {
      lineItemTitleId.value='252011'
      show.value.sng=true
      show.value.ggg=false
      show.value.snsg=false
      show.value.kjt=false
      show.value.zjt=false
      lineItemTitle.value='水泥杆'
      getDataByKey({ key: "TY_SNG" }).then((res) => {
        lineOption.value.statusOption = res.msg.split(";").map((item) => {
          const [value, label] = item.split(",");
          return { label, value };
        });
        lineForm.value.status =
          lineOption.value.statusOption.length > 0
            ? lineOption.value.statusOption[0].value
            : "";
      });
      // 电压等级默认值
      lineForm.value.voltageLevel = lineOption.value.voltageLevelOption[0].id;
      lineFormVoltageLevelChange();
    } else if (newInfo == "柱上变压器绘制") {
    lineForm2.value.tower=''
    lineForm2.value.towerCode=''
      lineForm2.value.voltageLevel =
        lineOption2.value.voltageLevelOption.length > 0
          ? lineOption2.value.voltageLevelOption[0].value
          : "";
      // 杆高数据获取
      getCementPoleHeight().then((res) => {
        lineOption2.value.GHeightOption = res.data.map((item) => ({
          label: item + "m",
          value: item,
        }));
        lineForm2.value.GHeight = lineOption2.value.GHeightOption[0].value;
        // 柱上变压器绘制状态数据
        getDataByKey({ key: "TY_ZSBYQ" }).then((res) => {
          lineOption2.value.statusOption = res.msg.split(";").map((item) => {
            const [value, label] = item.split(",");
            return { label, value };
          });
          lineForm2.value.status = lineOption2.value.statusOption[0].value;
          changeFaFun();
        });
      });
    } else if (newInfo == "同杆并架绘制") {
      // 电压等级默认值
      lineForm5.value.voltageLevel = lineOption5.value.voltageLevelOption[0].id;
      // 获取线路名称数据
      getEngineerLineFun("架空").then((options) => {
        lineOption5.value.lineNameOption = options; // 手动赋值
      });
      // 导线状态
      getDataByKey({ key: "TY_JKDX" }).then((res) => {
        lineOption5.value.wireStatusOption = res.msg.split(";").map((item) => {
          const [value, label] = item.split(",");
          return { label, value };
        });
        lineForm5.value.wireStatus =
          lineOption5.value.wireStatusOption[0].value;
        // 导线类型
        tgbjdxxhFun(lineForm5.value.wireStatus);
      });
      // 杆头状态
      getDataByKey({ key: "TY_TGBJGT" }).then((res) => {
        lineOption5.value.ganStatusOption = res.msg.split(";").map((item) => {
          const [value, label] = item.split(",");
          return { label, value };
        });
        lineForm5.value.ganStatus = lineOption5.value.ganStatusOption[0].value;
      });
    } else if (newInfo == "导线绘制") {
      // 电压等级默认值
      lineForm6.value.voltageLevel =
        lineOption6.value.voltageLevelOption.length > 0
          ? lineOption6.value.voltageLevelOption[0].id
          : "";
      // 根据电压等级判断回路数
      dxhzLoopList();
      // 获取线路名称数据
      getEngineerLineFun("架空").then((options) => {
        lineOption6.value.tableNameOption = options; // 手动赋值
      });
      // 导线状态
      getDataByKey({ key: "TY_JKDX" }).then((res) => {
        lineOption6.value.tableKeyOption = res.msg.split(";").map((item) => {
          const [value, label] = item.split(",");
          return { label, value };
        });
        // 导线型号默认值
        dxhzdxxhFun("New", 0);
      });
    } else if (newInfo == "拉线绘制") {
      // 电压等级默认值
      lineFormLx.value.voltageLevel =
        lineOptionLx.value.voltageLevelOption.length > 0
          ? lineOptionLx.value.voltageLevelOption[0].value
          : "";
      // 状态
      getDataByKey({ key: "TY_LX" }).then((res) => {
        lineOptionLx.value.statusOption = res.msg.split(";").map((item) => {
          const [value, label] = item.split(",");
          return { label, value };
        });
        lineFormLx.value.status =
          lineOptionLx.value.statusOption[0].value || "";
      });
      // 拉线类型
      getPullType().then((res) => {
        lineOptionLx.value.lxTypeOption = res.data.map((item) => ({
          label: item.moduletypename,
          value: item.moduletypekey,
        }));
        lineFormLx.value.lxType =
          lineOptionLx.value.lxTypeOption[0].value || "";
        // 拉线方案
        lxTypeGetLxfa(lineFormLx.value.lxType);
      });
    } else if (newInfo == "低压下户") {
      // 电压等级默认值
      lineForm9.value.voltageLevel =
        lineOption9.value.voltageLevelOption[0].id || "";
      // 表箱类型默认值
      lineForm9.value.meterBoxClass =
        lineOption9.value.meterBoxClassOption[0].value || "";
      // 状态
      getDataByKey({ key: "TY_BX" }).then((res) => {
        lineOption9.value.statusOption = res.msg.split(";").map((item) => {
          const [value, label] = item.split(",");
          return { label, value };
        });
        lineForm9.value.status = lineOption9.value.statusOption[0].value || "";
        dyxhXhxXh(lineForm9.value.status);
      });
    } else if (newInfo == "插入杆塔") {
      // 电压等级默认值
      lineForm8.value.voltageLevel =
        lineOption8.value.voltageLevelOption[0].id || "";
      // 杆塔级别默认值
      lineOption8.value.ganClassOption = [
        { label: "水泥杆", value: "水泥杆",className:"PWPolePSR" },
        { label: "钢管杆", value: "钢管杆",className:"PWPolePSR" },
        { label: "窄基塔", value: "窄基塔",className:"PWPolePSR"  },
        { label: "宽基塔", value: "宽基塔",className:"PWPolePSR"  },
        { label: "水泥双杆", value: "水泥双杆",className:"PWPolePSR"  },
      ];
      lineForm8.value.ganClass =
        lineOption8.value.ganClassOption[0].value || "";
      // 获取物料信息
      const voltageLevel = lineOption8.value.voltageLevelOption.find(
        (item) => item.id === lineForm8.value.voltageLevel
      ).text;
      getData({ voltageLevel: voltageLevel, taskId: taskId }).then((res) => {
        const entity = res.data.find((item) => item.HLvalue === "单回");
        if (entity) {
          lineOption8.value.cementModelOption =
            entity.cementTypeList.length > 0
              ? entity.cementTypeList.map((item) => ({
                  label: item.spec,
                  value: item.materialsprojectid,
                }))
              : [];
          lineForm8.value.cementModel =
            lineOption8.value.cementModelOption[0].value || "";
          lineOption8.value.gggHeightOption = entity.towerHeightList;
          lineForm8.value.gggHeight =
            lineOption8.value.gggHeightOption[0].value;
          // 地形土质下拉数据
          getDicts({
            dictionarKeys: ["Geological", "Terrain", "SteelTower"],
          }).then((res) => {
            lineOption8.value.soilOption = res.data.Geological;
            lineForm8.value.soil =
              res.data.Geological.length > 0
                ? res.data.Geological[0].value
                : "";
            lineOption8.value.terrainOption = res.data.Terrain;
            lineForm8.value.terrain =
              res.data.Terrain.length > 0 ? res.data.Terrain[0].value : "";
            lineOption8.value.gggXsOption = res.data.SteelTower;
            lineForm8.value.gggXs =
              res.data.SteelTower.length > 0
                ? res.data.SteelTower[0].value
                : "";
          });
          //  获取杆塔基础
          getBasicTower({ towerType: lineForm8.value.ganClass }).then((res) => {
            lineOption8.value.foundationOption = res.data;
            lineForm8.value.foundation = res.data[0].value;
          });
          // // 线路状态
          // getDataByKey({key: 'TY_JKDX'}).then(res => {
          //   lineOption.value.tableKeyOption = res.msg.split(';').map(item => {
          //     const [value, label] = item.split(',');
          //     return {label, value};
          //   })
          // })
          // tableKeyChange('New')
          // getEngineerLineFun('架空').then(options => {
          //   lineOption.value.tableNameOption = options; // 手动赋值
          // });
        }
      });
      // 状态
      getDataByKey({ key: "TY_SNG" }).then((res) => {
        lineOption8.value.statusOption = res.msg.split(";").map((item) => {
          const [value, label] = item.split(",");
          return { label, value };
        });
        lineForm8.value.status =
          lineOption8.value.statusOption.length > 0
            ? lineOption8.value.statusOption[0].value
            : "";
            lineForm8.value.checkRadio='1'
      });
    } else if (newInfo == "交叉跨域") {
      crossCheckBox.value = false;
      crossFlag.value = "";
      lineForm7.value = {};
      if (crossForm.value) {
        crossForm.value.resetFields();
      }
      GetSpanCategory().then((res) => {
        crossList.value = res.rows.map((item) => ({
          label: item.crossovername,
          value: item.crossoverkey,
        }));
        lineForm7.value.spanClass = crossList.value[0].value;
      });
    } else if (newInfo == "柱上设备绘制") {
      lineForm3.value.deviceCode=''
      GetPageData().then((res) => {
        dataList.value.fitterTowerList = [];
        dataList.value.statusList = mapMethods(res.data.StateList);
        dataList.value.fitterTowerList = res.data.TowerMTList;
        // dataList.value.columnDeviceList = res.data.EquipmentMTList[dataList.value.fitterTowerList[0].value].map(
        //   (item) => ({
        //     label: item.moduletypename,
        //     value: item.moduletypekey,
        //     drawingmethod: item.drawingmethod,
        //     symbol_id: item.symbolId,
        //     legendtypekey: item.legendtypekey,
        //     sdkClassname: item.sdkClassname
        //   })
        // );
        EquipmentMTList.value = res.data.EquipmentMTList;
        lineForm3.value.fitterTower = dataList.value.fitterTowerList[0].value;
        lineForm3.value.status = dataList.value.statusList[0].value;
        handleFitterTower(lineForm3.value.fitterTower);
        
        lineForm3.value.voltageLevel = "10kV";
        lineForm3.value.deviceModel = "";
        lineForm3.value.isFlag = "否";
        // deviceModelFlag.value=true;
      });
    } else if (newInfo == "附属设施绘制") {
      getFacilitiesType().then((res) => {
        // zssbList.value.fssbList = res.data.map((item) => ({
        //   label: item.moduletypename,
        //   value: item.moduletypekey,
        //   symbolId: item.symbolId,
        //   legendtypekey: item.legendtypekey,
        // }));
        zssbList.value.fssbList = res.data
        lineForm4.value.deviceClass = zssbList.value.fssbList[0].moduletypekey;
        lineForm4.value.state = "New";
        handleZssb(lineForm4.value.deviceClass, 1);
      });
    } else if (newInfo == "接地绘制") {
      lineForm10.value.status = "New";
      lineForm10.value.groundingType = "环形接地";
      lineForm10.value.device = "水泥杆";
      handleGrounding(lineForm10.value.groundingType);
    }else if(newInfo == "带电作业"){
      getModuleByTypeKeys({moduleTypeKey:"DDZY"}).then(res=>{
        console.log(res,'带电作业')
        filteredTableData.value=res.data
      })
    }
  }
);

// 处理点击事件
function oniframeMessage(param, callback) {
  const myiframe = document.querySelector("#myiframe").querySelector("iframe");
  if (myiframe && myiframe.contentWindow) {
    console.log('oniframeMessage:', param?.content)
    // 设置一个回调函数，用于接收子页面的返回信息
    const messageCallback = (event) => {
      // 执行回调
      callback(event.data);
      // 移除事件监听器，避免重复调用
      window.removeEventListener("message", messageCallback);
    };

    // 监听来自子页面的消息
    window.addEventListener("message", messageCallback);
    myiframe.contentWindow.postMessage(param, "*");
  }
}

//适配杆塔
const handleFitterTower = (e) => {
  dataList.value.columnDeviceList = EquipmentMTList.value[e].map(
          (item) => ({
            label: item.moduletypename,
            value: item.moduletypekey,
            drawingmethod: item.drawingmethod,
            symbol_id: item.symbolId,
            legendtypekey: item.legendtypekey,
            sdkClassname: item.sdkClassname
          })
        );
        lineForm3.value.columnDeviceType =
          dataList.value.columnDeviceList[0].value;
  handleColumnDeviceType(lineForm3.value.columnDeviceType)
};

//柱上设备类型
const handleColumnDeviceType = (e) => {
  GetRodEquipmentNumber({
    state: lineForm3.value.status,
    equipmentType: e,
  }).then((res) => {
    dataList.value.deviceModelList = res.data;
    if(res.data.length>0){
    lineForm3.value.deviceModel = dataList.value.deviceModelList[0].moduleid;
    }else{
      lineForm3.value.deviceModel = "";
    }
  });
  if (e == "ZSFFKG_S" || e == "ZSDLQ_S") {
    deviceModelFlag.value = true;
  } else {
    deviceModelFlag.value = false;
  }
};

const deviceClick = () => {
  if (
    lineForm3.value.columnDeviceType == "" ||
    lineForm3.value.fitterTower == "" ||
    lineForm3.value.status == "" ||
    lineForm3.value.deviceModel == "" ||
    lineForm3.value.voltageLevel == "" ||
    lineForm3.value.deviceCode == ""
  ) {
    proxy.$message.error("请填写完整信息");
  } else {
    // 需要增加判断如果drawingmethod=block，使用华云画图功能， 如果是Point， 不需要华云画图,点击绘制按钮之后需要点击设备
    const paramsListBox = dataList.value.columnDeviceList.find(
      (item) => lineForm3.value.columnDeviceType === item.value
    );
    if (paramsListBox.legendtypekey === "TY_DLSXG") {
      sendMessage(cadAppRef.value,{ 
        type: "greeting", 
        content:'SDK_selectEntity', 
        options:{ classNames: ["PWPolePSR"], errorMsg: "请选择杆塔！" } 
      }, (res) => {
        console.log(res,'回调')
        if(res.params==null){
          proxy.$message.warning('请选择杆塔！')
        }else{
          drawEndDlSg(res.params, lineForm3.value, paramsListBox);
        }
      });
    } else {
      let flag=false
      let symbolId
      if(lineForm3.value.columnDeviceType.includes('ZSDLQ_S')&&lineForm3.value.isFlag=='是'){
        symbolId='251405'
        flag=true
      }
      if(lineForm3.value.columnDeviceType=='ZSFFKG_S'&&lineForm3.value.isFlag=='是'){
        symbolId='251705'
        flag=true
      }
      sendMessage(
        cadAppRef.value,{
          type: "greeting",
          content: "SDK_zssbsblx",
          options: {
            symbolId: flag?symbolId:paramsListBox.symbol_id,
            drawingmethod: paramsListBox.drawingmethod,
          },
        },
        (res) => {
          if(res.cmd === "SDK_zssbsblx"){
            drawEndZSSB(res, lineForm3.value, paramsListBox)
          }
        }
      );
    }
  }
};
const crossCheckBox = ref(false);
//!:  交叉跨域 list        ---start
const crossList = ref([]);

const lineWidthFlag = ref(true);
const lineHeightFlag = ref(true);
const crossForm = ref(null);
//交叉跨域规则
const crossRules = ref({
  spanWidth: [
    {
      pattern: /^\d+(\.\d+)?$/,
      message: "请输入数字",
      trigger: "blur",
    },
  ],
  spanHeight: [
    {
      pattern: /^\d+(\.\d+)?$/,
      message: "请输入数字",
      trigger: "blur",
    },
  ],
  houseNum: [
    {
      pattern: /^[1-9]\d*$/,
      message: "请输入正整数",
      trigger: "blur",
    },
  ],
  voltageLevel: [
    { required: true, message: "请输入电压等级", trigger: "blur" },
  ],
});
const crossFlag = ref("");
const handleCrossCheckbox = (e) => {
  crossCheckBox.value = e;
  handleCross(lineForm7.value.spanClass);
};
const handleCross = (e) => {
  if (crossCheckBox.value) {
    if (e == "TXX" || e == "DLX") {
      lineHeightFlag.value = true;
    } else {
      lineHeightFlag.value = false;
    }
    lineWidthFlag.value = false;
  } else {
    lineHeightFlag.value = true;
    lineWidthFlag.value = true;
  }
  crossFlag.value = e;
  // if (e == "DLX") {
  //   crossFlag.value = e;
  // }
  // if (e == "FW") {
  //   crossFlag.value = e;
  // }
};
//交叉跨越
const crossOverClick = () => {
  // const form = crossForm.value;
  // form.validate((valid) => {
  //   if (valid) {
  //     console.log("表单验证通过");
  //   } else {
  //     console.log("表单验证失败");
  //   }
  // });
  //TODO 交叉跨域 --cad命令
  oniframeMessage({
    type: "onlycad",
    content: appStore.currentMxAppMenu.cmd,
    options: {...lineForm7.value}
  }, (data)=>{

  });
};
//! 交叉跨域 ----------------ending

const deviceModelFlag = ref(true);

//!增加柱上设备绘制-----------ending
const zssbList = ref({
  fssbList: [],
  sbxhList: [],
});

//!附属设施绘制  --start

//附属设备类型
const handleZssb = (e, item) => {
  getModuleByParentKey({ parentKey: e }).then((res) => {
    zssbList.value.sbxhList = res;
    lineForm4.value.deviceModel =
      zssbList.value.sbxhList.length > 0
        ? zssbList.value.sbxhList[0].moduleid
        : "";
  });
};

//!附属设施绘制  --end

//!接地绘制  --start
const jdhzList=ref([])
const handleGrounding = (e) => {
  getGroundingList({ lx: e, spgt: lineForm10.value.device }).then((res) => {
    jdhzList.value=res.data
    lineForm10.value.groundingModel=res.data[0].moduleid

  });
};
//!接地绘制  --end

// 绘制结束调用方法
const paramsList = ref({});
</script>

<style scoped lang="scss">
.line {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 1px;
  text-align: center;
  font-family: "Noto Sans SC", sans-serif;
  font-weight: bold;

  :last-child {
    margin-bottom: 0;
  }

  ::v-deep .el-table .el-table__header-wrapper th {
    background: #50a9aa !important;
    font-size: 14px;
    color: #ffffff;
    font-weight: 600;
  }

  .tw-ipt {
    width: 84px;
    height: 40px;
    background: #b5dddd;
    //   font-weight: 550;
    font-size: 14px;
    color: #282b33;
    display: flex;
    justify-content: center;
    align-items: center;
    // span {
    //   width: 60px;
    // }
  }

  .radio-group {
    color: #282b33;
    font-weight: 400;

    ::v-deep .el-radio__label {
      color: #282b33;
      font-weight: 400;
    }
  }

  .checkbox-group {
    ::v-deep .el-checkbox__label {
      color: #282b33;
      font-weight: 400;
    }
  }

  .input-table {
    margin-top: -2px;
    margin-bottom: 1px;
  }

  .tw-sct {
    width: 105px;
    height: 40px;
    background: #e4f2f2;
    display: flex;
    justify-content: center;
    align-items: center;

    .in-item {
      width: 82px;
      height: 27px;
    }
  }

  .line-item {
    width: 84px;
    height: 40px;
    background: #b5dddd;
    //   font-weight: 550;
    font-size: 14px;
    color: #282b33;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 10px;
    // span {
    //   width: 60px;
    // }
  }

  .line-input {
    // flex: 1;
    width: 294px;
    height: 40px;
    background: #e4f2f2;
    display: flex;
    justify-content: center;
    align-items: center;

    .in-item {
      width: 257px;
      height: 27px;
    }
  }

  .line-no-display {
    width: 294px;
    height: 40px;
    background: #e4f2f2;
    display: flex;
    align-items: center;
  }

  .radio-input {
    width: 294px;
    height: 60px;
    display: flex;
    background: #e4f2f2;
    flex-direction: column;

    .in-item {
      height: 20px;

      ::v-deep .el-input__wrapper {
        width: 150px;
      }
    }
  }

  //按钮
  .line-bnt {
    width: 84px;
    height: 30px;
    background: #0e8b8d;
    border-radius: 5px;
    color: white;
    line-height: 30px;
    cursor: pointer;
    font-size: 12px;
  }
}
</style>
