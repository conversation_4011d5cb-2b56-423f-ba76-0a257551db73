///////////////////////////////////////////////////////////////////////////////
//版权所有（C）2002-2022，成都梦想凯德科技有限公司。
//本软件代码及其文档和相关资料归成都梦想凯德科技有限公司,应用包含本软件的程序必须包括以下版权声明
//此应用程序应与成都梦想凯德科技有限公司达成协议，使用本软件、其文档或相关材料
//https://www.mxdraw.com/
///////////////////////////////////////////////////////////////////////////////

import { MrxDbgUiPrBaseReturn, MxFun } from "mxdraw";
import {
    McDbCustomEntity, McGePoint3d, IMcDbDwgFiler, McGePoint3dArray, MxCADWorldDraw, McDbLine,MxCADResbuf,DxfCode,
    McDbPolyline, MxCADUiPrPoint, MxCpp, McDbCircle, McDb, McDbCurve, McDbText, MxCADUiPrString, MxCADUiPrKeyWord, MxCADSelectionSet, McDbRotatedDimension,
} from "mxcad";
import {init as gtInit } from './geometricTolerances'

// 审图标注框
class McDbRectBoxLeadComment extends McDbCustomEntity {
    /** 云线角点1 */
    private pt1: McGePoint3d;
    /** 云线角点2 */
    private pt2: McGePoint3d;
    /** 审图标注点 */
    private pt3: McGePoint3d;
    /** 审图标注内容 */
    private _text: string = "";
    /** 字高 */
    private _textsize: number = 20;
    /** 云线半圆弧半径 */
    private _radius: number = 15;

    constructor(imp?: any) {
        super(imp);
    }
    public create(imp: any) {
        return new McDbRectBoxLeadComment(imp)
    }
    public getTypeName(): string {
        return "McDbRectBoxLeadComment";
    }

    //设置或获取文本值
    public set text(val: string) {
        this._text = val;
    }
    public get text(): string {
        return this._text;
    }
    //设置或获取文本大小
    public set textsize(val: number) {
        this._textsize = val;
    }
    public get textsize(): number {
        return this._textsize;
    }
    //设置或获取云线半圆弧半径
    public set radius(val: number) {
        this._radius = val;
    }
    public get radius(): number {
        return this._radius;
    }

    public dwgInFields(filter: IMcDbDwgFiler): boolean {
        this.pt1 = filter.readPoint("pt1").val;
        this.pt2 = filter.readPoint("pt2").val;
        this.pt3 = filter.readPoint("pt3").val;
        this._text = filter.readString("text").val;
        this._textsize = filter.readDouble("textsize").val;
        return true;
    }
    public dwgOutFields(filter: IMcDbDwgFiler): boolean {
        filter.writePoint("pt1", this.pt1);
        filter.writePoint("pt2", this.pt2);
        filter.writePoint("pt3", this.pt3);
        filter.writeString("text", this._text);
        filter.writeDouble("textsize", this._textsize);
        return true;
    }
    public moveGripPointsAt(iIndex: number, dXOffset: number, dYOffset: number, dZOffset: number) {
        this.assertWrite();
        if (iIndex === 0) {
            this.pt1.x += dXOffset;
            this.pt1.y += dYOffset;
            this.pt1.z += dZOffset;
        } else if (iIndex === 1) {
            this.pt2.x += dXOffset;
            this.pt2.y += dYOffset;
            this.pt2.z += dZOffset;
        } else if (iIndex === 2) {
            this.pt3.x += dXOffset;
            this.pt3.y += dYOffset;
            this.pt3.z += dZOffset;
        }
    };
    public getGripPoints(): McGePoint3dArray {
        let ret = new McGePoint3dArray();
        ret.append(this.pt1);
        ret.append(this.pt2);
        ret.append(this.pt3);
        return ret;
    };
    public worldDraw(draw: MxCADWorldDraw): void {
        const radius = this._radius;

        // 根据弧线半径，重新计算云线角点
        if (this.pt1.x < this.pt2.x) {
            const dist = this.pt2.x - this.pt1.x;
            const num = parseInt((dist / (radius * 2)).toString());
            this.pt2.x = this.pt1.x + radius * 2 * num;
        } else {
            const dist = this.pt1.x - this.pt2.x;
            const num = parseInt((dist / (radius * 2)).toString());
            this.pt1.x = this.pt2.x + radius * 2 * num;
        }

        if (this.pt1.y < this.pt2.y) {
            const dist = this.pt2.y - this.pt1.y;
            const num = parseInt((dist / (radius * 2)).toString());
            this.pt2.y = this.pt1.y + radius * 2 * num;
        } else {
            const dist = this.pt1.y - this.pt2.y;
            const num = parseInt((dist / (radius * 2)).toString());
            this.pt1.y = this.pt2.y + radius * 2 * num;
        }
        // 审图框线
        const pl = new McDbPolyline();
        pl.isClosed = true;
        pl.addVertexAt(this.pt1);
        pl.addVertexAt(new McGePoint3d(this.pt2.x, this.pt1.y));
        pl.addVertexAt(this.pt2);
        pl.addVertexAt(new McGePoint3d(this.pt1.x, this.pt2.y));

        // 根据审图框线与半径获取出云线所有圆弧的圆心
        const length = pl.getLength().val;
        let num = length / radius;
        // 获取圆心
        const circleCenterArr: McGePoint3d[] = [];
        for (let i = 1; i <= num; i++) {
            const pt = pl.getPointAtDist(radius * i).val;
            circleCenterArr.push(pt)
        }

        // 根据圆心与圆弧半径绘制云线圆弧
        circleCenterArr.forEach((center, index) => {
            const circle = new McDbCircle(center.x, center.y, center.z, radius);
            if (index % 2 === 0) {
                const ptArr: McGePoint3d[] = []
                circle.IntersectWith(pl, McDb.Intersect.kOnBothOperands).forEach(pt => {
                    ptArr.push(pt)
                });
                circle.splitCurves(ptArr).forEach(e => {
                    const pt = (e as McDbCurve).getPointAtDist((e as McDbCurve).getLength().val / 2).val;
                    if (pt.x < Math.min(...[this.pt1.x, this.pt2.x]) || pt.x > Math.max(...[this.pt1.x, this.pt2.x])) {
                        draw.drawEntity(e as McDbCurve);
                    }
                    if (pt.y < Math.min(...[this.pt1.y, this.pt2.y]) || pt.y > Math.max(...[this.pt1.y, this.pt2.y])) {
                        draw.drawEntity(e as McDbCurve);
                    }
                })
            }
        });
        /**
         * 绘制标注
         */
        const distanceArr: number[] = []
        for (let i = 0; i < pl.numVerts(); i++) {
            const dist = pl.getPointAt(i).val.distanceTo(this.pt3);
            distanceArr.push(dist);
        };
        const index = distanceArr.indexOf(Math.min(...distanceArr));
        const pt = pl.getPointAt(index).val;

        const pl_text = new McDbPolyline();
        pl_text.addVertexAt(pt);
        pl_text.addVertexAt(this.pt3);
        if (this._text) {
            const text = new McDbText();
            text.textString = this._text;
            text.position = text.alignmentPoint = this.pt3;
            text.height = 20;
            draw.drawEntity(text);
            const { minPt, maxPt, ret } = text.getBoundingBox();
            if (!ret) return;

            const textWidth = maxPt.x - minPt.x;
            pl_text.addVertexAt(new McGePoint3d(this.pt3.x + textWidth, this.pt3.y));
            draw.drawEntity(pl_text);
        } else {
            draw.drawEntity(pl_text);
        }

    }
    public setPoint1(pt1: McGePoint3d) {
        this.assertWrite();
        this.pt1 = pt1.clone();
    }
    public setPoint2(pt2: McGePoint3d) {
        this.assertWrite();
        this.pt2 = pt2.clone();
    }
    public setPoint3(pt3: McGePoint3d) {
        this.assertWrite();
        this.pt3 = pt3.clone();
    }
    public getPoint1() {
        return this.pt1;
    }
    public getPoint2() {
        return this.pt2;
    }
    public getPoint3() {
        return this.pt3;
    }
}

// 审图标注
async function Mx_Approval() {
    // 创建审图批注
    const rectBoxLeadComment = new McDbRectBoxLeadComment();
    // 设置审批意见
    const getContent = new MxCADUiPrString();
    getContent.setMessage("请输入审批意见");
    let content = await getContent.go();
    if (!content) content = "审批意见XXXX"
    rectBoxLeadComment.text = content;

    // 设置云线框角点
    const getPt1 = new MxCADUiPrPoint();
    getPt1.setMessage("指定云线框起始点");
    const pt1 = await getPt1.go();
    if (!pt1) return;
    const getPt2 = new MxCADUiPrPoint();
    getPt2.setMessage("指定云线框终止点");
    getPt2.setUserDraw((pt, pw) => {
        rectBoxLeadComment.setPoint1(pt1);
        rectBoxLeadComment.setPoint2(pt);
        rectBoxLeadComment.setPoint3(pt);
        pw.drawMcDbEntity(rectBoxLeadComment);
    })
    const pt2 = await getPt2.go();
    if (!pt2) return;
    rectBoxLeadComment.setPoint2(pt2);

    // 设置审图标注点
    const getPt3 = new MxCADUiPrPoint();
    getPt3.setMessage("指定审图标注点");
    getPt3.setUserDraw((pt, pw) => {
        rectBoxLeadComment.setPoint3(pt);
        pw.drawMcDbEntity(rectBoxLeadComment);
    })
    const pt3 = await getPt3.go();
    if (!pt3) return;
    rectBoxLeadComment.setPoint3(pt3);

    // 绘制审图标注对象
    MxCpp.getCurrentMxCAD().drawEntity(rectBoxLeadComment);
}

// 新创建 McDbTestAnnotatedRectangle 类继承 McDbCustomEntity
class McDbTestAnnotatedRectangle extends McDbCustomEntity {
    // 定义McDbTestLineCustomEntity内部的点对象 
    // 矩形角点pt1、pt2
    private pt1: McGePoint3d = new McGePoint3d();
    private pt2: McGePoint3d = new McGePoint3d();
    // 构造函数
    constructor(imp?: any) {
        super(imp);
    }
    // 创建函数
    public create(imp: any) {
        return new McDbTestAnnotatedRectangle(imp)
    }
    // 获取类名
    public getTypeName(): string {
        return "McDbTestRectangle";
    }
    // 读取自定义实体数据pt1、pt2
    public dwgInFields(filter: IMcDbDwgFiler): boolean {
        this.pt1 = filter.readPoint("pt1").val;
        this.pt2 = filter.readPoint("pt2").val;
        return true;
    }
    // 写入自定义实体数据pt1、pt2
    public dwgOutFields(filter: IMcDbDwgFiler): boolean {
        filter.writePoint("pt1", this.pt1);
        filter.writePoint("pt2", this.pt2);
        return true;
    }

    // 移动自定义对象的夹点
    public moveGripPointsAt(iIndex: number, dXOffset: number, dYOffset: number, dZOffset: number) {
        this.assertWrite();
        if (iIndex == 0) {
            this.pt1.x += dXOffset;
            this.pt1.y += dYOffset;
            this.pt1.z += dZOffset;
        }
        else if (iIndex == 1) {
            this.pt2.x += dXOffset;
            this.pt2.y += dYOffset;
            this.pt2.z += dZOffset;
        }
    };
    // 获取自定义对象的夹点
    public getGripPoints(): McGePoint3dArray {
        let ret = new McGePoint3dArray()
        ret.append(this.pt1);
        ret.append(this.pt2);
        return ret;
    };
    // 绘制实体
    public worldDraw(draw: MxCADWorldDraw): void {
        const pl = new McDbPolyline();
        pl.isClosed = true;
        pl.addVertexAt(this.pt1);
        pl.addVertexAt(new McGePoint3d(this.pt2.x, this.pt1.y));
        pl.addVertexAt(this.pt2);
        pl.addVertexAt(new McGePoint3d(this.pt1.x, this.pt2.y));
        draw.drawEntity(pl);

        let mxcad = MxCpp.getCurrentMxCAD();
        const pt3 = new McGePoint3d(this.pt2.x, this.pt1.y);
        mxcad.addDimStyle("MyDimStyle", "41,0.18,141,0.09,40,200", "77,1,271,3", "", "");
        mxcad.drawDimStyle = "MyDimStyle";
        const id = mxcad.drawDimAligned(this.pt1.x, this.pt1.y + 20, pt3.x, pt3.y + 20, (this.pt1.x + pt3.x) / 2, this.pt1.y + 20);
        draw.drawEntity(id.getMcDbEntity());
        id.erase();
        const _id = mxcad.drawDimAligned(this.pt2.x + 20, this.pt2.y, pt3.x + 20, pt3.y, pt3.x + 20, (this.pt2.y + pt3.y) / 2);
        draw.drawEntity(_id.getMcDbEntity());
        _id.erase();
    }
    // 设置pt1
    public setPoint1(pt1: McGePoint3d) {
        this.assertWrite();
        this.pt1 = pt1.clone();
    }
    // 设置pt2
    public setPoint2(pt2: McGePoint3d) {
        this.assertWrite();
        this.pt2 = pt2.clone();
    }
    // 获取pt1
    public getPoint1() {
        return this.pt1;
    }
    // 获取pt2
    public getPoint2() {
        return this.pt2;
    }
}

// 矩形标注
async function Mx_AnnotatedRectangle() {
    let mxcad = MxCpp.getCurrentMxCAD();
    const getPoint = new MxCADUiPrPoint();
    getPoint.setMessage("\n指定第一个角点:");
    let pt1 = await getPoint.go();
    if (!pt1) return;
    const myRect = new McDbTestAnnotatedRectangle();
    myRect.setPoint1(pt1);

    getPoint.setMessage("\n指定第二个角点:");
    getPoint.setUserDraw((pt, pw) => {
        myRect.setPoint2(pt);
        pw.drawMcDbEntity(myRect);
    })
    let pt2 = await getPoint.go();
    if (!pt2) return;

    myRect.setPoint2(pt2);
    mxcad.drawEntity(myRect);
}

// 等分点标注
export class MxDbTestDivide extends McDbCustomEntity {
    // 定义MxDbTestDivide内部的点对象 
    // 等分点圆心，半径
    private center: McGePoint3d = new McGePoint3d();
    private _radius: number = 1;
    // 构造函数
    constructor(imp?: any) {
        super(imp);
    }
    // 创建函数
    public create(imp: any) {
        return new MxDbTestDivide(imp)
    }
    // 获取类名
    public getTypeName(): string {
        return "MxDbTestDivide";
    }
    //设置或获取云线半圆弧半径
    public set radius(val: number) {
        this._radius = val;
    }
    public get radius(): number {
        return this._radius;
    }
    // 读取自定义实体数据pt1、pt2
    public dwgInFields(filter: IMcDbDwgFiler): boolean {
        this.center = filter.readPoint("center").val;
        this._radius = filter.readDouble("radius").val;
        return true;
    }
    // 写入自定义实体数据pt1、pt2
    public dwgOutFields(filter: IMcDbDwgFiler): boolean {
        filter.writePoint("center", this.center);
        filter.writeDouble("radius", this._radius);
        return true;
    }

    // 移动自定义对象的夹点
    public moveGripPointsAt(iIndex: number, dXOffset: number, dYOffset: number, dZOffset: number) {
        this.assertWrite();
        this.center.x += dXOffset;
        this.center.y += dYOffset;
        this.center.z += dZOffset;
    };
    // 获取自定义对象的夹点
    public getGripPoints(): McGePoint3dArray {
        let ret = new McGePoint3dArray()
        ret.append(this.center);
        return ret;
    };
    // 绘制实体
    public worldDraw(draw: MxCADWorldDraw): void {
        const circle = new McDbCircle(this.center.x,this.center.y,this.center.z, this._radius);
        draw.drawEntity(circle);
        const { minPt, maxPt, ret } = circle.getBoundingBox();
        if(!ret) return;
        const line1 = new McDbLine(minPt,maxPt);
        const line2 = new McDbLine(new McGePoint3d(minPt.x,maxPt.y,0), new McGePoint3d(maxPt.x,minPt.y,0));
        draw.drawEntity(line1);
        draw.drawEntity(line2);
    }
    // 设置圆心
    public setCenter(pt: McGePoint3d) {
        this.assertWrite();
        this.center = pt.clone();
    }
    // 获取圆心
    public getCenter() {
        return this.center;
    }
}

// 对齐
async function Mx_Alignment(){
    // 选择要对齐水平标注或竖直标注[水平(H)/竖直(V)]<H>:
    const getKey = new MxCADUiPrKeyWord;
    getKey.setMessage(`选择要对齐水平标注或竖直标注：`)
    getKey.setKeyWords("[水平(H)/竖直(V)]");
    const keyVal = await getKey.go();
    if(!keyVal) return;
     
    // 选择对齐标注
    const ss = new MxCADSelectionSet();
    if (!await ss.userSelect("选择对齐标注:")) return;
    if (ss.count() == 0) return;
    const alignArr:McDbRotatedDimension[] = [];
    ss.forEach(id=>{
        const ent = id.getMcDbEntity();
        if(ent.objectName === 'McDbRotatedDimension'){
           const rDim = ent.clone() as McDbRotatedDimension;
           const xDist = Math.abs(rDim.xLine1Point.x - rDim.xLine2Point.x).toFixed(0);
           const yDist = Math.abs(rDim.xLine1Point.y - rDim.xLine2Point.y).toFixed(0);
           const xNum = Math.abs(rDim.dimLinePoint.x - rDim.xLine1Point.x) + Math.abs(rDim.dimLinePoint.x - rDim.xLine2Point.x);
           const yNum = Math.abs(rDim.dimLinePoint.y - rDim.xLine1Point.y) + Math.abs(rDim.dimLinePoint.y - rDim.xLine2Point.y);

           if(keyVal === 'H' && xNum.toFixed(0) == xDist){
             alignArr.push(ent as McDbRotatedDimension)
           };
           if(keyVal === 'V' && yNum.toFixed(0) == yDist){
             alignArr.push(ent as McDbRotatedDimension)
           };
        }
    });
    if(alignArr.length === 0) return;
    const mxcad = MxCpp.getCurrentMxCAD();
    // 设置尺寸线位置
    const getPt1 = new MxCADUiPrPoint();
    getPt1.setMessage('设置尺寸线位置');
    const pt1 = await getPt1.go();
    if(!pt1) return;
    alignArr.forEach(ent=>{
        ent.dimLinePoint = pt1;
    });
    mxcad.updateDisplay();

    // 设置尺寸线界限
    const getPt2 = new MxCADUiPrPoint();
    getPt2.setMessage('设置尺寸线界限');
    const pt2 = await getPt2.go();
    if(!pt2) return;
    alignArr.forEach(ent=>{
       if(keyVal === 'H'){
        ent.xLine1Point = new McGePoint3d(ent.xLine1Point.x,pt2.y);
        ent.xLine2Point = new McGePoint3d(ent.xLine2Point.x,pt2.y);
       }else if(keyVal === 'V'){
        ent.xLine1Point = new McGePoint3d(pt2.x, ent.xLine1Point.y);
        ent.xLine2Point = new McGePoint3d(pt2.x, ent.xLine2Point.y);
       }
    });
    mxcad.updateDisplay();
}

export function init() {
    new McDbRectBoxLeadComment().rxInit();
    new McDbTestAnnotatedRectangle().rxInit();
    new MxDbTestDivide().rxInit();
    MxFun.addCommand("Mx_Approval", Mx_Approval);
    MxFun.addCommand("Mx_AnnotatedRectangle", Mx_AnnotatedRectangle);
    MxFun.addCommand("Mx_Alignment", Mx_Alignment);

    gtInit();
}