<template>
  <!-- <el-dialog v-model="props.modelValue" append-to-body title="基础参数设置" width="1000px" @close="close"> -->
  <div class="auto-height-container">
    <div class="">
      <!-- <div class="title">配网工程设计任务管理</div> -->
      <!-- <el-divider/> -->
    </div>
    <el-tabs
      v-model="activeName"
      class="demo-tabs"
      type="card"
      @tab-change="handleClick"
    >
      <el-tab-pane label="架空线路参数设置" name="1">
        <OverheadParameters
          v-model:baseData="baseData.towerlineSetting"
        ></OverheadParameters>
      </el-tab-pane>
      <el-tab-pane label="电缆线路参数设置" name="2">
        <CableParameters
          v-model:baseData="baseData.cableSetting"
        ></CableParameters>
      </el-tab-pane>
      <el-tab-pane label="标注设置" name="3">
        <AnnotationSettings
          v-model:baseData="baseData.tagSetting"
        ></AnnotationSettings>
      </el-tab-pane>
      <el-tab-pane label="绘图设置" name="4">
        <DrawingSettings
          v-model:baseData="baseData.drawSetting"
        ></DrawingSettings>
      </el-tab-pane>
      <el-tab-pane label="图签设置" name="5">
        <SignatureSettings
          v-model:baseData="baseData.graphtagSetting"
        ></SignatureSettings>
      </el-tab-pane>
    </el-tabs>
    <div class="container-footer">
      <el-button @click="cancelDialog">取 消</el-button>
      <el-button :loading="submitLoading" type="primary" @click="submit">确 定</el-button>
    </div>
  </div>
  <!-- <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancelDialog">取 消</el-button>
        <el-button :loading="submitLoading" type="primary" @click="submit">确 定</el-button>
      </div>
    </template> -->
<!-- </el-dialog> -->
</template>
<script setup>
import OverheadParameters from "../../taskBaseData/components/OverheadParameters.vue";
import CableParameters from "../../taskBaseData/components/CableParameters.vue";
import SignatureSettings from "../../taskBaseData/components/SignatureSettings.vue";
import AnnotationSettings from "../../taskBaseData/components/AnnotationSettings.vue";
import DrawingSettings from "../../taskBaseData/components/DrawingSettings.vue";
import { ref } from "vue";
import { getBaseSettingList } from "@/api/taskBaseData/index.js";
import { addSettingList } from "@/api/config-management/index.js";
import { ElMessage } from "element-plus";
import { useRoute } from "vue-router";
const route = useRoute();
const activeName = ref("1");
// tab切换设置
const handleClick = () => {};
const props = defineProps({
  modelValue: { // v-model 默认绑定到 modelValue
    type: Boolean,
    default: false,
  },
  taskId: {
    type: String,
    required: true,
  },
  getEngineeringList: {
    type: Object,
    required: true
  }
});

const taskId = computed(() => {
  return props.taskId || route.query.id || ''
})
const emit = defineEmits(['submitFormDialog', 'cancelDialog']);
const cancelDialog = () => {
  emit('cancelDialog');
};

const close = () => {
  emit('close');
}
const defaultData = {
  tagSetting: [],
  towerlineSetting: {
    CementTower10KV: [],
    CementTower10KVText: "",
    Line10KV: [],
    Line10KVText: "",
    LX10KV: "",
    LX10KVText: "",
    SingleLine: "",
    DoubleLine: "",
    TribleLine: "",
    QuardraLine: "",
    SingleCorner: "",
    DoubleCorner: "",
    TribleCorner: "",
    QuardraCorner: "",
    SingleNaiZhang: "",
    DoubleNaiZhang: "",
    TribleNaiZhang: "",
    QuardraNaiZhang: "",
    SingleTerminal: "",
    DoubleTerminal: "",
    TribleTerminal: "",
    QuardraTerminal: "",
    SiArrayMode: null,
    DanArrayMode: null,
    ShuangArrayMode: null,
    SanArrayMode: null,
    CementTowerLow: [],
    CementTowerLowText: "",
    LineLow: [],
    LineLowText: "",
    LXLow: "",
    LXLowText: "",
    NaiZhangMaxLength: "",
    TaMaxDistance: "",
    TaMinDistance: "",
  },
  graphtagSetting: {
    GraphTagGroup: "",
    GraphTagPath: "",
  },
  cableSetting: {
    CableSpec10KV: [],
    CableSpec10KVText: "",
    CableSpecLow: [],
    CableSpecLowText: "",
    ZhiXianWell: "",
    ZhiXianWellText: "",
    ZhuanJiaoWell: "",
    ZhuanJiaoWellText: "",
    SanTongWell: "",
    SanTongWellText: "",
    SiTongWell: "",
    SiTongWellText: "",
    BaJiaoSiTongWell: "",
    BaJiaoSiTongWellText: "",
    CableWells: [
      {
        MinQuantity: "",
        MaxQuantity: "",
        ZhiXianWell: "",
        ZhiXianWellText: "",
        ZhuanJiaoWell: "",
        ZhuanJiaoWellText: "",
        SanTongWell: "",
        SanTongWellText: "",
        SiTongWell: "",
        SiTongWellText: "",
        BaJiaoSiTongWell: "",
        BaJiaoSiTongWellText: "",
      },
    ],
    WellSpacing: "",
    MiddleHeadSpacing: "",
  },
  drawSetting: [],
};

const baseData = ref(defaultData);

const getData = () => {
  console.log('getData',baseData.value);
  getBaseSettingList({ taskId: taskId.value }).then((res) => {
    console.log('hahahah')
    baseData.value = Object.entries(res.data).reduce((acc, [key, value]) => {
      acc[key] = JSON.parse(value);
      return acc;
    }, {});

  });
};
const submitLoading = ref(false);
const submit = () => {
  submitLoading.value = true;
  const data = Object.entries(baseData.value).reduce((acc, [key, value]) => {
    acc[key] = JSON.stringify(value);
    return acc;
  }, {});
  data.engineeringObjid= taskId.value
  addSettingList(data)
    .then((res) => {
      if (res.code === 200) {
        ElMessage.success("保存成功！");
        emit('close');
      } else {
        ElMessage.error(res.msg);
      }
    })
    .finally(() => {
      submitLoading.value = false;
    });
};
onMounted(() => {
  getData();
});
</script>
<style lang="scss" scoped>
.auto-height-container {
  //overflow: hidden;
}

.title {
  color: #0e8b8f;
  font-size: 20px;
  font-weight: 700;
}
::v-deep .el-select {
  width: 100%!important;
  height:auto!important;
}
/*.demo-tabs ::v-deep .el-tabs__header {
    background-color: #fff; !* Tab栏背景颜色 *!
  }
  
  .demo-tabs ::v-deep .el-tabs__item.is-active {
    color: #fff; !* 选中Tab的字体颜色 *!
    background-color: #50adaa; !* 选中Tab的背景颜色 *!
  }
  
  .demo-tabs ::v-deep .el-tabs__item {
    color: #333; !* 未选中Tab的字体颜色 *!
  }
  
  !* 设置表头的背景颜色 *!
  ::v-deep .el-table .el-table__header-wrapper th, .el-table .el-table__fixed-header-wrapper th {
    background-color: #d7d7d7 !important; !* 自定义表头背景颜色 *!
  }
  
  !* 设置表头字体的颜色 *!
  .demo-table ::v-deep .el-table__header th {
    color: #000; !* 自定义字体颜色 *!
    font-weight: 700;
  }
  
  ::v-deep .el-input-group__append {
    background-color: #50adaa;
  
    .el-icon {
      color: #fff;
    }
  }
  
  ::v-deep .el-divider--horizontal {
    margin: 15px 0;
  }
  
  !* 修改选中复选框的勾选颜色 *!
  ::v-deep .custom-checkbox.is-checked .el-checkbox__inner {
    background-color: #50adaa; !* 设置选中复选框的背景颜色 *!
    border-color: #50adaa; !* 设置选中复选框的边框颜色 *!
  }
  
  !* 设置勾选后的勾选标记颜色 *!
  ::v-deep .custom-checkbox.is-checked .el-checkbox__inner::after {
    border-color: white; !* 设置勾选标记的颜色 *!
  }
  
  !* 修改选中复选框时的文字颜色 *!
  ::v-deep .custom-checkbox.is-checked .el-checkbox__label {
    color: #50adaa; !* 设置选中状态文字颜色 *!
  }
  
  ::v-deep .el-pager li.is-active {
    background-color: #50adaa !important;
  }
  
  // 下拉框选项边框
  ::v-deep .is-focused {
    box-shadow: 0 0 0 1px #50adaa inset;
  }
  
  // 输入边框
  .custom-input ::v-deep .is-focus {
    box-shadow: 0 0 0 1px #50adaa inset;
  }
  
  .pagination ::v-deep .is-focus {
    box-shadow: 0 0 0 1px #50adaa inset;
  }
  
  .el-select-dropdown__item.is-selected {
    color: #50adaa;
  }*/

.container-footer {
  border-top: 1px solid var(--el-border-color);
  padding: 20px 0;
  text-align: right;
}
</style>
