import{M as ke}from"./index-8X61wlK0.js";import{w as le,s as u,u as H,x as Se,_ as ne,y as Ae}from"./index-D95UjFey.js";import{d as b,s as J,c as k,w as W,A as P,h as oe,z as Ie,R as Te,a0 as V,a3 as D,a1 as x,ab as L,F as Be,a4 as Fe,_ as E,$,V as O,U as G,a5 as K,H as be,I as $e,m as F,Q as N,u as g,B as te,n as X,a7 as Ve}from"./vue-DfH9C9Rx.js";import{l as Oe,c as Z,b as j,i as re,L as Re,W as Ue,d as se,I as De}from"./vuetify-B_xYg4qv.js";import"./mxcad-CfPpL1Bn.js";import"./mxdraw-C_n_7lEs.js";import"./mapbox-gl-PN3pDJHq.js";import"./xlsx-Ck5MUZvf.js";const Ee=f=>{const n=b({searchText:"",sortBy:"command",sortOrder:"asc"}),l=J([]),a=J([]),m=(A,C)=>{if(!C)return A;const e=C.toLowerCase().split(/\s+/);return A.map(s=>{const r=s.command.toLowerCase(),T=s.aliases.join(" ").toLowerCase();let B=0;return e.forEach(t=>{r===t?B+=5:r.startsWith(t)?B+=4:r.includes(t)?B+=3:s.aliases.some(w=>w.toLowerCase()===t)?B+=2:T.includes(t)&&(B+=1)}),{item:s,score:B}}).filter(({score:s})=>s>0).sort((s,r)=>{const T=r.score-s.score;return T!==0?T:s.item.command.localeCompare(r.item.command)}).map(({item:s})=>s)},c=A=>[...A].sort((C,e)=>{let s=0;if(n.value.sortBy==="command")s=C.command.localeCompare(e.command,void 0,{numeric:!0});else{const r=C.aliases[0]||"",T=e.aliases[0]||"";C.aliases.length===0&&e.aliases.length>0?s=1:C.aliases.length>0&&e.aliases.length===0?s=-1:s=r.localeCompare(T,void 0,{numeric:!0})}return n.value.sortOrder==="asc"?s:-s}),I=le(()=>{l.value=m(f.value,n.value.searchText),a.value=c(l.value)},200),y=k(()=>(I(),a.value)),S=k({get:()=>n.value.searchText,set:A=>{n.value.searchText=A,I()}}),o=k(()=>n.value.sortBy),M=k(()=>n.value.sortOrder),i=A=>{n.value.sortBy===A?n.value.sortOrder=n.value.sortOrder==="asc"?"desc":"asc":(n.value.sortBy=A,n.value.sortOrder="asc"),a.value=c(l.value)},p=()=>{n.value={searchText:"",sortBy:"command",sortOrder:"asc"},l.value=[],a.value=c(f.value)};return l.value=f.value,a.value=c(f.value),{searchText:S,filteredAliasList:y,sortBy:o,sortOrder:M,toggleSort:i,resetAll:p}},We=(f=50,n)=>{const l=J([]),a=J([]),m=i=>({aliasList:JSON.parse(JSON.stringify(i)),timestamp:Date.now()}),c=i=>{const p=m(i);l.value=[...l.value,p].slice(-f),a.value=[]},I=()=>{if(l.value.length===0)return;const i=m(n.value);a.value=[...a.value,i];const p=l.value.pop();p&&(n.value=JSON.parse(JSON.stringify(p.aliasList)))},y=()=>{if(a.value.length===0)return;const i=m(n.value);l.value=[...l.value,i];const p=a.value.pop();p&&(n.value=JSON.parse(JSON.stringify(p.aliasList)))},S=()=>{l.value=[],a.value=[]},o=k(()=>l.value.length>0),M=k(()=>a.value.length>0);return{addHistory:c,handleUndo:I,handleRedo:y,clearHistory:S,canUndo:o,canRedo:M}},ze=(f,n,l,a)=>{const m=H(),c=o=>o.length<1?{valid:!1,message:u("731")}:o.length>50?{valid:!1,message:u("732")+o+u("733")}:/[<>:"\/\\|?*\x00-\x1F]/.test(o)?{valid:!1,message:u("732")+o+u("734")}:{valid:!0},I=(o,M)=>{const i=f.value.find(e=>e.id===o);if(!i)return{success:!1,message:u("735")};const p=M.map(e=>e.trim()).filter(Boolean).map(e=>({alias:e,validation:c(e)})),A=p.filter(e=>!e.validation.valid).map(e=>e.validation.message);if(A.length>0)return{success:!1,message:A.join(`
`),type:"validation"};const C=p.map(e=>e.alias);for(const e of C)if(a(e,i.command)){for(const s of f.value)if(s.id!==o&&(s.command.toLowerCase()===e.toLowerCase()||s.aliases.some(r=>r.toLowerCase()===e.toLowerCase())))return{success:!1,message:`${u("732")}"${e}"${u("736")}"${s.command}"${u("737")}`,type:"conflict"}}try{return l(f.value),n(o,C),{success:!0}}catch(e){return console.error("更新别名失败:",e),{success:!1,message:u("738"),type:"error"}}};return{handleUpdateAlias:I,handleRemoveAlias:(o,M)=>{const i=f.value.find(p=>p.id===o);if(!i)return m.error(u("735")),!1;try{return l(f.value),n(o,i.aliases.filter(p=>p!==M)),!0}catch(p){return console.error("删除别名失败:",p),m.error(u("739")),!1}},handleBatchUpdate:o=>{const M={success:[],failed:[]};for(const i of o){const p=I(i.id,i.aliases);p.success?M.success.push(i.id):M.failed.push({id:i.id,message:p.message||u("740")})}return M},validateAlias:c}},Le=(f,n,l)=>{const a=b({searchInputMap:{},menuModeMap:{},expandedMap:{},aliasFilterMap:{},focusedId:null}),m=()=>{const e=new Set(f.value.map(r=>r.id)),s={...a.value};for(const r of Object.keys(s.searchInputMap))e.has(r)||delete s.searchInputMap[r];for(const r of Object.keys(s.menuModeMap))e.has(r)||delete s.menuModeMap[r];for(const r of Object.keys(s.expandedMap))e.has(r)||delete s.expandedMap[r];for(const r of Object.keys(s.aliasFilterMap))e.has(r)||delete s.aliasFilterMap[r];a.value=s};W(()=>f.value,()=>{P(m)},{deep:!0});const c=e=>{a.value.focusedId=e,n(e)&&(a.value.menuModeMap[e]=!0)},I=e=>{setTimeout(()=>{a.value.focusedId===e&&(a.value.focusedId=null),a.value.searchInputMap[e]||(a.value.menuModeMap[e]=!1)},200)},y=e=>{if(l.value){a.value.expandedMap[e]=!a.value.expandedMap[e];return}const s=Object.entries(a.value.expandedMap).find(([r,T])=>T)?.[0];if(a.value.expandedMap[e]){a.value.expandedMap[e]=!1;return}s&&(a.value.expandedMap[s]=!1),a.value.expandedMap[e]=!0},S=(e,s)=>{a.value.aliasFilterMap[e]=s,s&&(a.value.expandedMap[e]=!0)},o=e=>{a.value.searchInputMap[e]="",a.value.menuModeMap[e]=!1,a.value.aliasFilterMap[e]=""},M=k({get:()=>a.value.searchInputMap,set:e=>a.value.searchInputMap=e}),i=k({get:()=>a.value.menuModeMap,set:e=>a.value.menuModeMap=e}),p=k({get:()=>{const e={...a.value.expandedMap};return f.value.forEach(s=>{typeof e[s.id]!="boolean"&&(e[s.id]=!1)}),e},set:e=>a.value.expandedMap=e}),A=k({get:()=>{const e={...a.value.aliasFilterMap};return f.value.forEach(s=>{e[s.id]||(e[s.id]="")}),e},set:e=>a.value.aliasFilterMap=e});return{searchInputMap:M,menuModeMap:i,expandedMap:p,aliasFilterMap:A,handleClear:o,handleFocus:c,handleBlur:I,toggleExpand:y,setAliasFilter:S,resetState:()=>{a.value={searchInputMap:{},menuModeMap:{},expandedMap:{},aliasFilterMap:{},focusedId:null}}}},{isShow:Ne,showDialog:Y}=Se,je=()=>{const f=b(),n=b(!1),l=H(),a=async y=>{n.value=!0;try{await y()}catch(S){console.error("操作失败:",S),l.error(u("726"))}finally{n.value=!1}},m=()=>f.value?.click();return{isShow:Ne,showDialog:Y,fileInput:f,loading:n,handleImport:m,handleFileSelect:async(y,S)=>{const o=y.target.files;o?.[0]&&(await a(async()=>{await S(o[0])}),f.value&&(f.value.value=""))},createFooterBtnList:(y,S,o,M)=>[{name:u("727"),fun:async()=>{await a(async()=>{await y(),M.value=!M.value,Y(!1)})},primary:!0},{name:u("728"),fun:()=>{S()}},{name:u("729"),fun:o},{name:u("730"),fun:m},{name:u("512"),fun:()=>{Y(!1)}}],withLoading:a}},He={class:"alias-cell"},Je={class:"alias-container"},Pe={class:"chips-container"},Ke=["title"],qe={key:0,class:"more-hint"},Qe={class:"expanded-wrapper"},Ge={class:"expanded-content"},Xe={class:"alias-input"},Ye=["title"],Ze=oe({__name:"AliasCell",props:{item:{},expanded:{type:Boolean},filterText:{}},emits:["update:aliases","remove","toggle-expand","update:filterText","show-error"],setup(f,{emit:n}){const l=f,a=n,m=k(()=>Math.floor(552/100)),c=b(""),I=b(!1),y=b(!1),S=k(()=>l.filterText?l.item.aliases.filter(t=>t.toLowerCase().includes(l.filterText?.toLowerCase()||"")):l.item.aliases),o=k(()=>{const t=l.filterText?S.value:l.item.aliases;return l.expanded||t.length<=m.value?t:t.slice(0,m.value)}),M=k(()=>S.value.length>m.value),i=k(()=>{const t=S.value.length,w=l.expanded?t:Math.min(t,m.value);return t-w}),p=k(()=>!!(I.value||l.filterText||c.value.trim()));W(p,t=>{t&&!l.expanded&&a("toggle-expand")});const A=t=>{t.target.closest(".v-chip__close, .add-btn, .v-field__input")||window.getSelection()?.toString()||a("toggle-expand")},C=()=>{const t=c.value.trim();if(!t)return;const w=t.split(/[\s,]+/).map(h=>h.trim()).filter(h=>h&&!l.item.aliases.includes(h));if(w.length===0){a("show-error",u("789"));return}a("update:aliases",[...l.item.aliases,...w]),c.value="",P(()=>{T.value?.focus()})};k(()=>c.value.trim()?u("790"):u("791")),le(()=>{const t=c.value.trim();if(!t)return;const w=t.split(/[\s,]+/).map(h=>h.trim()).filter(h=>h&&!l.item.aliases.includes(h));if(w.length===0){a("show-error",u("789"));return}a("update:aliases",[...l.item.aliases,...w]),c.value="",y.value=!1},300);const e=t=>{t.key==="Enter"?(t.preventDefault(),C()):t.key==="Escape"?(t.preventDefault(),c.value="",y.value=!1,t.target.blur()):t.key==="Tab"?c.value.trim()||(t.preventDefault(),a("toggle-expand")):t.key===","&&(t.preventDefault(),C())},s=()=>{I.value=!0,y.value=!0},r=()=>{I.value=!1,setTimeout(()=>{y.value=!1},200)},T=b();W(()=>l.expanded,t=>{t&&P(()=>{T.value?.focus()})});const B=t=>{if(l.expanded){const w=t.target,h=w.closest(".alias-cell");w.closest(".v-dialog")&&!h&&a("toggle-expand")}};return Ie(()=>{document.addEventListener("click",B)}),Te(()=>{document.removeEventListener("click",B)}),W(c,t=>{t.trim()&&(y.value=!0)},{immediate:!0}),k(()=>Math.floor(768/150)),(t,w)=>(V(),D("div",He,[x("div",{class:L(["alias-content",{"is-expanded":t.expanded}]),onClick:G(A,["stop"])},[x("div",Je,[x("div",{class:L(["chips-wrapper",{"has-focus":I.value,"has-many":t.item.aliases.length>m.value,clickable:!t.expanded&&t.item.aliases.length>0}])},[x("div",Pe,[(V(!0),D(Be,null,Fe(o.value,h=>(V(),E(Oe,{key:h,size:"x-small",closable:t.expanded,class:L(["ma-1",{highlight:c.value===h}]),"onClick:close":G(U=>a("remove",h),["stop"])},{default:$(()=>[x("span",{class:"alias-text",title:h},O(h),9,Ke)]),_:2},1032,["closable","onClick:close","class"]))),128))]),!t.expanded&&M.value?(V(),D("span",qe," +"+O(i.value),1)):K("",!0)],2)]),be(x("div",Qe,[x("div",Ge,[x("div",Xe,[F(re,{ref_key:"inputRef",ref:T,modelValue:c.value,"onUpdate:modelValue":w[0]||(w[0]=h=>c.value=h),placeholder:g(u)("791"),onKeydown:e,onFocus:s,onBlur:r},{append:$(()=>[F(Z,{color:"primary",variant:"text",size:"small",onClick:G(C,["stop"]),disabled:!c.value.trim(),class:"add-btn"},{default:$(()=>[F(j,{start:"",size:"small",icon:"$mdi-plus"}),N(" "+O(g(u)("602")),1)]),_:1},8,["disabled"])]),_:1},8,["modelValue","placeholder"])]),!t.expanded&&i.value>0?(V(),D("span",{key:0,class:"more-hint",title:g(u)("792",[i.value])}," +"+O(i.value),9,Ye)):K("",!0)])],512),[[$e,t.expanded]])],2)]))}}),ea=ne(Ze,[["__scopeId","data-v-ed4b52c2"]]),aa={class:"alias-dialog"},ta={class:"search-bar mb-2"},sa={class:"d-flex align-center gap-2 ml-4"},la={class:"grid-container"},na={class:"grid-header"},oa={class:"grid-body custom-scrollbar"},ra={key:0,class:"empty-state"},ia={class:"mt-2"},ca=["onClick"],da={class:"command-cell"},ua={class:"alias-cell"},pa=oe({__name:"index",setup(f){const{aliasList:n,initData:l,updateAlias:a,saveConfig:m,resetToDefault:c,exportConfig:I,importConfig:y,checkConflict:S}=Ae(),{isShow:o,showDialog:M,fileInput:i,handleFileSelect:p,createFooterBtnList:A}=je(),C=b(!1),e=A(m,c,I,C),{searchText:s,filteredAliasList:r,sortBy:T,sortOrder:B,toggleSort:t,resetAll:w}=Ee(n),{addHistory:h,handleUndo:U,handleRedo:ee,clearHistory:va,canUndo:ie,canRedo:ce}=We(50,n),{handleUpdateAlias:de,handleRemoveAlias:ue}=ze(n,a,h,S),pe={"ctrl + z":U,"ctrl + y":ee,"ctrl + s":async()=>{try{await m(),C.value=!C.value,M(!1)}catch(v){console.error("保存失败:",v)}}},ve=k(()=>({height:400,itemHeight:32,items:r.value})),q=b(null),fe=v=>{q.value=v.id},me=v=>{const _=n.value.find(d=>d.id===v);return _?_.aliases.length>3:!1};W(()=>n.value,()=>{P(()=>{ge.value={}})},{deep:!0});const z=b(!1);(async()=>{z.value=!0;try{await l(),w(),q.value=null,Q.success(u("加载成功"))}catch(v){console.error("初始化失败:",v),Q.error(u("加载失败"))}finally{z.value=!1}})();const Q=H(),he=v=>{p(v,y)},{searchInputMap:ma,menuModeMap:ge,handleClear:ha,handleFocus:ga,handleBlur:xa,expandedMap:xe,aliasFilterMap:ye,toggleExpand:Me,setAliasFilter:we}=Le(n,me,s),Ce=v=>({"table-row":!0,"is-highlighted":v.id===q.value,"has-aliases":v.aliases.length>0}),ae=v=>{(v==="command"||v==="aliases")&&t(v)},_e=(v,_)=>{const d=de(v,_);!d.success&&d.message&&H().error(d.message)};return(v,_)=>(V(),E(ke,{title:v.t("775"),modelValue:g(o),"onUpdate:modelValue":_[4]||(_[4]=d=>te(o)?o.value=d:null),maxWidth:"800",footerBtnList:g(e),keys:pe},{default:$(()=>[x("div",aa,[F(Ue,{modelValue:z.value,"onUpdate:modelValue":_[0]||(_[0]=d=>z.value=d),class:"align-center justify-center"},{default:$(()=>[F(Re,{indeterminate:""})]),_:1},8,["modelValue"]),x("div",ta,[F(re,{modelValue:g(s),"onUpdate:modelValue":_[1]||(_[1]=d=>te(s)?s.value=d:null),placeholder:v.t("776"),"prepend-inner-icon":"$mdi-magnify",class:"flex-grow-1",clearable:""},null,8,["modelValue","placeholder"]),x("div",sa,[F(se,{text:v.t("777"),location:"top"},{activator:$(({props:d})=>[F(Z,X(d,{icon:"houtui",disabled:!g(ie),onClick:g(U),variant:"text",size:"32"}),null,16,["disabled","onClick"])]),_:1},8,["text"]),F(se,{text:v.t("713"),location:"top"},{activator:$(({props:d})=>[F(Z,X(d,{icon:"qianjin",disabled:!g(ce),onClick:g(ee),variant:"text",size:"32"}),{append:$(()=>_[5]||(_[5]=[])),_:2},1040,["disabled","onClick"])]),_:1},8,["text"])])]),x("div",la,[x("div",na,[x("div",{class:"command-header text-center",onClick:_[2]||(_[2]=d=>ae("command"))},[N(O(v.t("394"))+" ",1),g(T)==="command"?(V(),E(j,{key:0,size:"20",icon:g(B)==="asc"?"$mdi-arrow-up":"$mdi-arrow-down",color:"primary",class:"ml-1"},null,8,["icon"])):K("",!0)]),x("div",{class:"alias-header text-center",onClick:_[3]||(_[3]=d=>ae("aliases"))},[N(O(v.t("732"))+" ",1),g(T)==="aliases"?(V(),E(j,{key:0,size:"20",icon:g(B)==="asc"?"$mdi-arrow-up":"$mdi-arrow-down",color:"primary",class:"ml-1"},null,8,["icon"])):K("",!0)])]),x("div",oa,[g(r).length===0?(V(),D("div",ra,[F(j,{size:"48",color:"grey"},{default:$(()=>_[6]||(_[6]=[N("$mdi-magnify")])),_:1}),x("p",ia,O(v.t("778")),1)])):(V(),E(De,Ve(X({key:1},ve.value)),{default:$(({item:d})=>[x("div",{class:L(["grid-row",Ce(d)]),onClick:R=>fe(d)},[x("div",da,O(d.command),1),x("div",ua,[F(ea,{item:d,expanded:!!g(xe)[d.id],filterText:g(ye)[d.id],"onUpdate:filterText":R=>g(we)(d.id,R),"onUpdate:aliases":R=>_e(d.id,R),onRemove:R=>g(ue)(d.id,R),onToggleExpand:R=>g(Me)(d.id),onShowError:g(Q).error},null,8,["item","expanded","filterText","onUpdate:filterText","onUpdate:aliases","onRemove","onToggleExpand","onShowError"])])],10,ca)]),_:1},16))])])]),x("input",{type:"file",ref_key:"fileInput",ref:i,accept:".json",style:{display:"none"},onChange:he},null,544)]),_:1},8,["title","modelValue","footerBtnList"]))}}),Ia=ne(pa,[["__scopeId","data-v-2ed8832e"]]);export{Ia as default};
