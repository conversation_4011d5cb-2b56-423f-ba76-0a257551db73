<template>
  <div style="display: flex;">
    <div style="border: 0.5px solid #50adaa;margin-right: 10px">
      <div class="title">列表</div>
      <el-divider class="divider5"/>
      <el-tree
          style="width: 150px;height: 70vh;"
          ref="treeRef"
          node-key="LegendTypeKey"
          :data="data"
          :props="defaultProps"
          @node-click="handleNodeClick"
      >
        <template #default="{ node, data }">
        <span class="custom-tree-node">
          <span style="display: flex;align-items: center"><el-icon><Document/></el-icon>{{ node.label }}</span>
        </span>
        </template>
      </el-tree>
    </div>
    <div style="width: 100%;border: 0.5px solid #50adaa">
      <div class="title">设置</div>
      <el-divider class="divider5"/>
      <el-table :data="tableData" border style="width: 100%;height: 70vh">
        <el-table-column width="85" label="是否显示">
          <template #default="scope">
            <el-switch v-model="scope.row.IsShow" active-value="1" inactive-value="0"/>
          </template>
        </el-table-column>
        <el-table-column prop="TagName" label="标注项目"/>
        <el-table-column prop="" label="字体样式">
          <template #default="scope">
            <el-select
                v-model="scope.row.FontStyle"
                filterable
                placeholder="请选择"
            >
              <el-option
                  v-for="item in fontOption"
                  :key="item.value"
                  :label="item.text"
                  :value="item.value"
              />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column prop="" label="字体颜色">
          <template #default="scope">
            <el-color-picker v-model="scope.row.FontColor"/>
          </template>
        </el-table-column>
        <el-table-column prop="" label="字体高度">
          <template #default="scope">
            <el-input type="number" v-model="scope.row.FontHeight"/>
          </template>
        </el-table-column>
        <el-table-column prop="" label="宽度因子">
          <template #default="scope">
            <el-input type="number" v-model="scope.row.FontWidthFactor"/>
          </template>
        </el-table-column>
        <el-table-column prop="" label="角度">
          <template #default="scope">
            <el-input type="number" v-model="scope.row.Angle"/>
          </template>
        </el-table-column>
        <el-table-column prop="" label="距离">
          <template #default="scope">
            <el-input type="number" v-model="scope.row.Distance"/>
          </template>
        </el-table-column>
        <el-table-column prop="" label="标注对齐方式">
          <template #default="scope">
            <el-select
                v-model="scope.row.AttachmentPoint"
                filterable
                placeholder="请选择"
            >
              <el-option
                  v-for="item in attachmentPoint"
                  :key="item.name"
                  :label="item.description"
                  :value="item.name"
              />
            </el-select>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>
<script setup>
import {getAttachmentPointList, getFontList} from "@/api/taskBaseData/index.js";


const treeRef = ref()

const data = defineModel('baseData', {type: Array, required: true});


const defaultProps = ref({
  children: 'children',
  label: 'LegendTypeName',
})

// tree点击事件
const handleNodeClick = (val) => {
  console.log(val, 'tree点击事件')
}


const tableData = computed(() => {
  return treeRef.value?.getCurrentNode()?.SettingList || []
})

const fontOption = ref([])
const getFontOption = () => {
  getFontList().then((res) => {
    fontOption.value = res.data
  })
}

const attachmentPoint = ref([])
const getAttachmentPoint = () => {
  getAttachmentPointList().then((res) => {
    attachmentPoint.value = res.data
  })
}

onMounted(() => {
  getFontOption()
  getAttachmentPoint()
})
</script>

<style scoped lang="scss">
.title {
  margin: 10px;
  color: #50adaa;
  font-weight: bold;
}
</style>
