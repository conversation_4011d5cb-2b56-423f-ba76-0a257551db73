<template>
  <data-dialog
    dataWidth="378px"
    @close="closeDialog"
    v-if="appStore.mapIndex == '应力弧垂表'"
  >
    <template #header>
      <h4 style="margin-left: 10px; margin-top: 0px; line-height: 39px">
        插入应力弧垂表
      </h4>
    </template>
    <template #body>
      <div class="line">
        <div class="line-item">
          <span> 导线型号 </span>
        </div>
        <div class="line-input">
          <el-select
            class="in-item"
            @change="selectLine"
            v-model="sagForm.line"
            placeholder="请选择"
          >
            <el-option
              v-for="item in list"
              :label="item.modulecode"
              :value="item.moduleid"
              :key="item.moduleid"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 安全系数 </span>
        </div>
        <div class="line-input">
          <el-select
            class="in-item"
            v-model="sagForm.coefficient"
            placeholder="请选择"
          >
            <el-option
              v-for="item in coefficientList"
              :label="item.label"
              :value="item.value"
              :key="item.value"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <div style="background: #e4f2f2; height: 50px" class="line">
        <div @click="insetCad" class="line-bnt">插入CAD</div>
      </div>
    </template>
  </data-dialog>
</template>
<script setup>
import DataDialog from "@/components/DataDialog/index.vue";
import useAppStore from "@/store/modules/app.js";
import { useRoute } from "vue-router";
import {
  getYlhcbByTaskId,
  getDxaqxsByTaskIdModuleId,
  ylhcbInsertTUzhi,
  getYlhcbFileNameList,
} from "@/api/insertSag/index.js";
import { getTuQianFileByPath } from "@/api/onlineDesign/index.js";
import { ElLoading } from 'element-plus'
const { proxy } = getCurrentInstance();
import { inject } from "vue";
const route = useRoute();
const appStore = useAppStore();
const cadAppRef = inject("cadAppRef");
const taskId = route.query.id;
const callChildC = inject("callChildC");
const closeDialog = () => {
  appStore.mapIndex = "";
};
const list = ref([]);
const weatherarea = ref("");
const coefficientList = ref([]);
const sagForm = ref({
  coefficient: "",
  line: "",
});

const insetCad = async () => {
  if (!sagForm.value.coefficient) {
    proxy.$message.error("请选择导线型号或安全系数");
    return;
  }

  try {
    const res = await getYlhcbFileNameList({
      moduleId: sagForm.value.line,
      weatherarea: weatherarea.value,
      safetyfactor: sagForm.value.coefficient,
    });
    if (res.data.length > 0) {
      let arr = [];
      for (let i = 0; i < res.data.length; i++) {
        const filePath = await getTuQianFileByPath({ filePath: res.data[i] });
        arr.push({ fileName: res.data[i], filePath });
      }
      oniframeMessage({
        type: "cadPreview",
        content: "Mx_cadtext",
        formData: {
          openUrl: arr,
        },
      });
    } else {
      proxy.$message.error("暂无文件");
    }
  } catch (error) {
    proxy.$message.error("操作失败");
    console.error("Error:", error);
  }
};

// 处理点击事件
const oniframeMessage = (param) => {
  const myiframe = appStore.iframeHide;
  console.log("🚀 ~ oniframeMessage ~ appStore.iframe:", appStore.iframe);
  if (myiframe && myiframe.contentWindow) {
    myiframe.contentWindow.postMessage(param, "*");
    // 先移除之前的事件监听器，防止重复绑定
    window.removeEventListener("message", handleMessage);
    // 添加新的事件监听器
    window.addEventListener("message", handleMessage);
  }
};
// 事件处理函数
const handleMessage = (event) => {
  console.log("🚀 ~ handleMessage ~ event:", event.data);

  // 判断event.data.params.content是否为 '测试中'
  if (event.data.params.content === "应力弧垂表") {
    const loading = ElLoading.service({
        lock: true,
        text: '报表正在生成中，请稍等...',
        background: 'rgba(0, 0, 0, 0.7)',
      })
    const files = event.data.params.formData.files;
    const fileFormData = new FormData();
    files.forEach((item) => {
      fileFormData.append("files", item.file);
    });
    fileFormData.append("taskId", taskId);
    fileFormData.append("moduleId", sagForm.value.line);
    fileFormData.append("weatherarea", weatherarea.value);
    fileFormData.append("safetyfactor", sagForm.value.coefficient);
    fileFormData.append("specificationName", "设计图纸");
    // 调用ylhcbInsertTUzhi并处理返回结果
    ylhcbInsertTUzhi(fileFormData)
      .then((res) => {
        if (res.code === 200) {
          proxy.$message.success(res.msg); // 成功时弹出消息
          loading.close()
          if (callChildC) {
            callChildC(); // 如果callChildC存在，则调用
          }
        }else{
          proxy.$message.error(res.msg);
          loading.close()
        }
      })
      .catch((err) => {
        // 处理错误的情况
        console.error("🚨 ~ handleMessage ~ error:", err);
      });
  }
};
const selectLine = (e) => {
  getDxaqxs(e);
};
const queryList = () => {
  getYlhcbByTaskId(taskId).then((res) => {
    if (res.data) {
      list.value = res.data;
      sagForm.value.line = res.data[0].moduleid;
      getDxaqxs(sagForm.value.line);
    }
  });
};
const getDxaqxs = (moduleid) => {
  getDxaqxsByTaskIdModuleId(taskId, moduleid).then((res) => {
    if (res.data) {
      coefficientList.value = res.data.aqxs.map((item) => {
        return {
          label: item,
          value: item,
        };
      });
      if (coefficientList.value.length > 0) {
        sagForm.value.coefficient = coefficientList.value[0].value;
      } else {
        sagForm.value.coefficient = "";
      }
      weatherarea.value = res.data.weatherarea;
    }
  });
};
watch(
  () => appStore.mapIndex,
  (newInfo, oldInfo) => {
    if (newInfo == "应力弧垂表") {
      queryList();
    }
  }
);
</script>

<style lang="scss" scoped>
@use "../../index" as *;
</style>
