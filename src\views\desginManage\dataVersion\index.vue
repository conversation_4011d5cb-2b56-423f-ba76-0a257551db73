<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true">
         <el-form-item label="版本名称" prop="versionName">
            <el-input
               v-model="queryParams.versionName"
               placeholder="请输入版本名称"
               clearable
               style="width: 200px"
            />
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
         </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
         <el-col :span="1.5">
            <el-button
               type="primary"
               plain
               icon="Plus"
               @click="handleAdd"
            >新增</el-button>
         </el-col>
      </el-row>
      <el-table v-loading="loading" :data="versionList">
         <el-table-column type="index" width="55" align="center" />
         <el-table-column label="版本号" align="center" width="220" prop="versionCode" />
         <el-table-column label="版本名称" align="center" prop="versionName" />
         <el-table-column label="省公司名称" align="center" prop="provinceName" />
         <el-table-column label="公司名称" align="center" prop="companyName" />
         <el-table-column label="创建人" align="center" width="220" prop="operatorName" />
         <el-table-column label="创建时间" align="center" width="220" prop="createTime" />
         <el-table-column label="操作" width="180" align="center" class-name="small-padding fixed-width">
            <template #default="scope">
               <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:post:edit']">修改</el-button>
               <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:post:remove']">删除</el-button>
            </template>
         </el-table-column>
      </el-table>

      <pagination
         v-show="total > 0"
         :total="total"
         v-model:page="queryParams.pageNum"
         v-model:limit="queryParams.pageSize"
         @pagination="getList"
      />
      <!--新增或修改对话框-->
      <el-dialog :title="title" v-model="open" width="400px" append-to-body>
         <el-form :model="form" :rules="rules" ref="versionRef" label-width="100px">
            <el-form-item label="版本号" prop="versionCode">
               <el-input v-model="form.versionCode" placeholder="请输入版本号" maxlength="30" />
            </el-form-item>
            <el-form-item label="版本名称" prop="versionName">
               <el-input v-model="form.versionName" placeholder="请输入版本名称" maxlength="30" />
            </el-form-item>
            <el-form-item label="省公司名称" prop="provinceId">
               <el-select v-model="form.provinceId" @change="provinceChange" placeholder="请选择">
                <el-option v-for="dict in provinceOptions" :key="dict.deptId" :label="dict.deptName" :value="dict.deptId"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="供电公司" prop="cityCompanyId">
               <el-select v-model="form.cityCompanyId" placeholder="请选择">
                <el-option v-for="dict in CityCompanyOptions" :key="dict.deptId" :label="dict.deptName" :value="dict.deptId"></el-option>
              </el-select>
            </el-form-item>
         </el-form>
         <template #footer>
         <div class="dialog-footer">
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
         </div>
         </template>
      </el-dialog>
  </div>
</template>  
<script setup name="dataVersion">
const { proxy } = getCurrentInstance();
import { listVersion,addVersion,updateVersion,delVersion } from "@/api/baseDate/baseVersion";
// import { listProvince,listCityCompany } from "@/api/baseDate/basePower";
import { listDept } from "@/api/system/dept"
import { ref } from 'vue';
import useUserStore from '@/store/modules/user'

const userStore = useUserStore()
const loading = ref(false)
const total = ref(0)
const versionList = ref([])
const title = ref("");
const open = ref(false)
const provinceOptions = ref([])
const CityCompanyOptions = ref([])
const data = reactive({
  form: {
   versionCode: undefined,
   versionName: undefined,
   provinceId: undefined,
   provinceName: undefined,
   cityCompanyId: undefined,
   cityCompanyName: undefined,
   companyId: undefined,
   companyName: undefined
  },
  queryParams: {
   pageNum: 1,
   pageSize: 10,
   companyId: undefined,
   versionName: undefined,
  },
  companyInfo: {
   companyId: undefined,
   companyName: undefined,
  },
  rules: {
   versionCode: [{ required: true, message: "版本号不能为空", trigger: "blur" }],
   versionName: [{ required: true, message: "版本名称不能为空", trigger: "blur" }],
   provinceId: [{ required: true, message: "省份不能为空", trigger: "blur" }],
  },
});
const { queryParams,form,companyInfo,rules } = toRefs(data);

/** 查询省公司下拉树结构 */
function getProvinceTree() {
   const queryParams = {
      parentId: 100
   }
   listDept(queryParams).then(response => {
      provinceOptions.value = response.data;      
  });
};

/**查询事件 */
function handleQuery() {
   queryParams.value.pageNum = 1
   getList()
}

/**重置查询参数 */
function resetQuery() {
   queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    companyId: undefined,
    versionName: undefined
  }
  getList()
}

/**新增版本 */
function handleAdd() {
   getProvinceTree()
   open.value = true
   title.value = '新增数据版本信息'
}

/**修改版本 */
function handleUpdate(row) {
   getProvinceTree()
   open.value = true
   title.value = '修改数据版本信息'
   form.value = row
   form.value.provinceId = parseInt(form.value.provinceId)
   form.value.cityCompanyId = parseInt(form.value.cityCompanyId)
   if (form.value.provinceId) {
      provinceChange(form.value.provinceId)
   }
}

/**删除版本 */
function handleDelete(row){
   proxy.$modal.confirm('是否确认删除版本号为"' + row.versionCode + '"的数据项?').then(function() {
    return delVersion(row.objId);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/**获取版本数据 */
function getList() {
   loading.value = true;
   userStore.getInfo().then(res => {
      queryParams.value.companyId = res.dept.deptId 
      companyInfo.value.companyId = res.dept.deptId
      companyInfo.value.companyName = res.dept.deptName
      listVersion(queryParams.value).then(response => {
         versionList.value = response.rows;
         total.value = response.total;
         loading.value = false;
      }).catch(() => {
         loading.value = false;
      })
   })
}

/**保存新版本 */
function submitForm() {
   proxy.$refs["versionRef"].validate(valid => {
      if (valid) {
         form.value.companyId = companyInfo.value.companyId
         form.value.companyName = companyInfo.value.companyName
         const provinceOption = provinceOptions.value.find(option => option.deptId == form.value.provinceId)
         form.value.provinceName = provinceOption.deptName
         const cityCompanyOption = CityCompanyOptions.value.find(option => option.deptId == form.value.cityCompanyId)
         form.value.cityCompanyName = cityCompanyOption.deptName

         if (form.value.objId != undefined) {
            updateVersion(form.value).then(res => {
               getList()
               proxy.$modal.msgSuccess("修改成功");
               cancel()
            })
         } else {
            addVersion(form.value).then(res => {
               getList()
               proxy.$modal.msgSuccess("新增成功");
               cancel()
            })
         }         
      }
   })
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
};

/** 重置操作表单 */
function reset() {
  form.value = {
   versionCode: undefined,
   versionName: undefined,
   provinceId: undefined,
   provinceName: undefined,
   cityCompanyId: undefined,
   cityCompanyName: undefined,
   companyId: undefined,
   companyName: undefined
  };
  proxy.resetForm("versionRef");
};

/** 选中省公司事件 */
function provinceChange(row) {
   const queryParams = {
      parentId: row
   }
   listDept(queryParams).then(res => {
      CityCompanyOptions.value = res.data
   })
}
getList()
</script>