<script setup>
const form = reactive({})
const rules = reactive({})

const emit = defineEmits(['close'])

const onSubmit = () => {
  emit('close')
}
</script>

<template>
  <div class="" style="width: 300px;">
    <el-form ref="formRef" class="b-form" :model="form" :rules="rules" :show-message="false" label-width="auto">
      <div class="group-header">
        <div class="group-title">参数设置</div>
      </div>
      <el-form-item class="b-form-item" label="类型" prop="radio">
        <el-radio-group v-model="form.radio">
          <el-radio-button value="1">杆塔重排</el-radio-button>
          <el-radio-button value="3">电缆井重排</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item class="b-form-item" label="起始节点" prop="radio">
        <el-input/>
      </el-form-item>
      <el-form-item class="b-form-item" label="终止节点" prop="radio">
        <el-input/>
      </el-form-item>
      <div class="group-header">
        <div class="group-title">节点编号</div>
      </div>
      <el-form-item class="b-form-item" label="前缀" prop="radio">
        <el-input/>
      </el-form-item>
      <el-form-item class="b-form-item" label="起始编码" prop="radio">
        <el-input/>
      </el-form-item>
      <el-form-item class="b-form-item" label="后缀" prop="radio">
        <el-input/>
      </el-form-item>
    </el-form>
    <div class="b-footer">
      <el-button type="primary" @click="onSubmit">确定</el-button>
    </div>
  </div>
</template>

<style scoped lang="scss">

</style>
