<script setup>
import Edit from './edit'
import EditMaterial from './edit-material'
import {
  getDrawByLegendGuidKey,
  getInfoByLegendGuidKey, getLegendInfo, getModulematerialsTree,
  getModuleTree,
  getPropertyByLegendGuidKey, saveWithOldEquipmentId
} from "@/api/onlineDesign/index.js";
import {downloadFileDWG} from "@/api/task/index.js";
import {useIframeCommunication} from '@/hooks/useIframeCommunication.js'
import {useCAdApp} from '@/hooks/useCAdApp.js'
import {useState} from './utils/state.js'
import {useRoute} from "vue-router";
import {ElMessage} from "element-plus";
import useAppStore from "@/store/modules/app";
const appStore = useAppStore();
const route = useRoute()

const taskId = route.query.id

const {sendMessage} = useIframeCommunication()

const {cadAppSrc} = useCAdApp()

const {stateOption, stateGather} = useState()

const visible = defineModel('visible', {type: Boolean, default: false})

const mainCadAppRef = inject('cadAppRef');

const {id} = defineProps({
  id: {
    type: [String, Number],
  },
})
// const id = '3ciopCpeQMmxC8cP5EQ8bg'
const emit = defineEmits(['submit'])

const loading = ref(false)

const activeName = ref('1')

const legendGuidKey = ref('ec53ee1efb414a28b91cc98fbee66ab2')

const basicsInfo = ref({
  "DX": "",
  "TZ": "",
  "AngleType": "",
  "LineNumber": "",
  "UserNumber": ""
})
const getBasicsInfo = () => {
  const data = {
    legendGuidKey: id,
    taskId:taskId
  }
  getInfoByLegendGuidKey(data).then(res => {
    if (res.code === 200) {
      basicsInfo.value = res.data
    }
  })
}

const currentModuleId = computed(() => {
  if (gridOptions.data.length) {
    return gridOptions.data[0].moduleid
  }
  return ''
})

const moudleName = ref('')

const getModuleList = () => {
  console.log('id', id)
  const params = {
    equipmentId: id,
    taskId:taskId
  }
  getModuleTree(params).then(res => {
    if(res.msg!='操作成功'){
      ElMessage.warning(res.msg)
      return;
    }
    if(res.data){
      gridOptions.data = initTreeData(res.data.materialTree)
    console.log(' gridOptions.data', gridOptions.data)
    moudleName.value = res.data.name
    }
  
  })
}

// 处理树数据
const initTreeData = (data, parentState = null) => {
  return data.map(node => {
    // 在当前节点上，拿到父节点的 modulestate
    node.parentState = parentState;
    const newNode = {...node}; // 创建节点的副本

    if (node.modulelevel==null) {
      newNode.options = stateGather[parentState] || []; // 添加 options 字段
    }

    // 更新父节点的 modulestate 为当前节点的 modulestate
    const currentState = node.modulestate;

    // 如果有子节点，递归处理
    if (newNode.children && newNode.children.length > 0) {
      newNode.children = initTreeData(newNode.children, currentState);
    }

    return newNode;
  });
}

const formatModuleLabel = (val) => {
  if (val) {
    const item = stateOption.find(item => item.value === val)
    return item ? item.label : val
  }
  return ''
}

const formatMaterialLabel = (value) => {
  // 遍历 stateGather 对象的每个数组
  for (let key in stateGather) {
    // 使用 Array.find() 查找匹配的项
    const match = stateGather[key].find(item => item.value === value);

    // 如果找到匹配的项，返回该项
    if (match) {
      return match.label;
    }
  }

  // 如果没有找到匹配的项，返回 null
  return '';
}

const quantityEditRender = reactive({
  name: 'VxeInput',
  props: {
    disabled: false
  }
})

const gridOptions = reactive({
  border: true,
  height: '100%',
  treeConfig: {
    rowField: 'moduleid',
    childrenField: 'children'
  },
  editConfig: {
    trigger: 'click',
    mode: 'cell'
  },
  columns: [
    {type: 'seq', width: 70},
    {field: 'moduleName', title: '名称', width: 250, treeNode: true},
    {field: 'spec', title: '规格'},
    {field: 'designunit', title: '单位'},
    {field: 'quantity', title: '数量', editRender: quantityEditRender},
    {field: 'remark', title: '备注'},
    {field: 'state', title: '状态', editRender: {}, slots: {edit: 'edit_state', default: 'state'}, width: '120px'},
    {title: '操作', width: '80px', slots: {default: 'action'}},
    // {field: '', title: '工厂化模块', slots: {default: 'factoryModule'}, width: '90px'},
  ],
  data: [],
})

const gridEvents = {
  editActivated({row}) {
    // name 为 'x' 开头的列禁止编辑
    quantityEditRender.props.disabled = row.mustneed === '1'
  }
}
// 
// 顶层组件
const isFather = (row) => {
  return !row.childmoduleid && !row.materialsprojectid
}

// 模块
const isModule = (row) => {
  return row.childmoduleid
}

// 物料
const isMaterial = (row) => {
  return row.materialsprojectid
}
const moduleStateChange = (row, e) => {
  updateState(row)
}


// 更新 state 字段的递归函数
const updateState = (node) => {
  const updateNodeState = (nodes) => {
    nodes.forEach(item => {
      // 如果是父节点或者模块节点，递归更新其子节点
      if (item.modulelevel==1 || item.modulelevel==2) {
        item.modulestate = node.modulestate;  // 修改 state 字段
      } else {
        item.materialsstate = stateGather[node.modulestate][0].value
        item.options = stateGather[node.modulestate]
      }
      // 如果有子节点，则递归修改子节点
      if (item.children && item.children.length > 0) {
        updateNodeState(item.children);
      }
    });
  };

  // 调用递归函数来修改节点状态
  updateNodeState(node.children);
}

const tabChange = (tab) => {
  if (tab === '2') {
    getPropertyList()
  } else if (tab === '3') {
    getDrawingList()
  }
}


const propertyTableData = ref([])

const getPropertyList = () => {
  const data = {
    // legendGuidKey: id,
    moduleid: currentModuleId.value,
  }
  getPropertyByLegendGuidKey(data).then(res => {
    if (res.code === 200) {
      propertyTableData.value = res.data
    }
  })
}

const drawingList = computed(() => {
  const {materialdrawings, moduledrawings} = drawingData.value
  return materialdrawings.concat(moduledrawings)
})
const drawingData = ref(
    {
      materialdrawings: [],
      moduledrawings: []
    }
)
const getDrawingList = () => {
  getDrawByLegendGuidKey(gridOptions.data).then(res => {
    if (res.code === 200) {
      drawingData.value = res.data
    }
  })
}

const currentTreeData = ref(null)
const handleNodeClick = (data) => {
  currentTreeData.value = data
  getDwgFile(data)
}

const cadLoading = ref(false);

const cadIframeRef = ref(null)
const cadIframeSrc = cadAppSrc({
  isPreview: '1'
})
const getDwgFile = (data) => {
  const id = data.drawingid
  if (!id) return
  cadLoading.value = true
  downloadFileDWG(id).then(res => {
    nextTick(() => {
      setTimeout(() => {
        const params = {
          type: "cadPreview",
          content: "Mx_cadPreview",
          formData: {
            openUrl: res,
          }
        }
        sendMessage(cadIframeRef.value, params, (res => {
        }))
        cadLoading.value = false
      }, 2000)
    })
  }).catch(() => {
    cadLoading.value = false
  })
}
const defaultProps = {
  children: 'children',
  label: 'drawingname',
}

// 物料编辑
const editMaterialVisible = ref(false)

// 模块编辑
const editVisible = ref(false)

const currentData = ref(null)

const editHandle = (row) => {
  if(row.modulelevel==null){  
    editMaterialVisible.value = true
    row.flag=false
  }else if(row.modulelevel==1){
    editVisible.value = true
    row.flag=true
    row.title='组件选型'
  }else{
    editVisible.value = true
    row.flag=false
    row.title='模块选型'
  }
  currentData.value = row
}

const moduleSubmitChange = (row) => {
  if (row?.moduleid) {
    getModuleMaterialsTreeData(row)
  }
}
const getModuleMaterialsTreeData = (row) => {
  const params = {
    taskId: taskId,
    moduleId: row.moduleid,
    materialsstate: currentData.value.materialsstate,
    modulestate: currentData.value.modulestate,
    rootModuleId: gridOptions.data[0].moduleid,
    isComponent: row.level==1?true:false,
  }
  getModulematerialsTree(params).then(res => {  
    const data = findAndReplaceById(gridOptions.data, currentData.value._X_ROW_KEY, res.data)
    gridOptions.data = initTreeData(data)
  })
}

const findAndReplaceById = (array, targetId, newItem) => {
  return array.map(item => {
    if (item._X_ROW_KEY === targetId) {
      return newItem
    }

    if (item.children && item.children.length > 0) {
      return {
        ...item,
        children: findAndReplaceById(item.children, targetId, newItem)
      }
    }

    return item
  })
}

const materialFindAndReplaceById = (tree, targetId) => {
  // 遍历树的每一层
  for (let node of tree) {
    // 检查当前节点是否是目标节点
    if (node._X_ROW_KEY === targetId) {
      return node;
    }

    // 如果当前节点有子节点，并且还没有找到目标节点，则递归查找子节点
    if (node.children && Array.isArray(node.children)) {
      const foundNode = materialFindAndReplaceById(node.children, targetId);
      if (foundNode) {
        return foundNode;
      }
    }
  }

  // 如果没有找到目标节点，返回null
  return null;
}

const materialSubmitChange = (row) => {
  if(!row) return
  let node = materialFindAndReplaceById(gridOptions.data,currentData.value._X_ROW_KEY)
  Object.assign(node, {
    materialsprojectid: row.materialsprojectid,
    // quantity: row.quantity,
    designunit: row.designunit,
    spec: row.spec,
    moduleName: row.materialname
  });
  // 触发视图更新（Vue/React等框架需要）
  // gridOptions.data = [...gridOptions.data];
}

const onSubmit = () => {
  loading.value = true
  const data = gridOptions.data[0]
  if (!gridOptions.data.length) return ElMessage.error('暂无数据！')
  const params = {
    equipmentId: id,
    taskId: taskId,
    // equipmentId: legendGuidKey.value,
    moduleid: data.moduleid,
    moduleState: data.modulestate
  }
  getLegendInfo(params).then(res => {
    if (res.code === 200) {
      if(appStore.sdkClassName.clickItemClassName=="PWPolePSR"){
        let symbolId
        // '水泥双杆新建'||res.data.legendtypekeyName=='水泥单杆原有'
      if(res.data.legendtypekeyName.includes('水泥双杆')){
        symbolId="252018"
      }else if(res.data.legendtypekeyName.includes('水泥杆')){
        symbolId="252011"
      }else if(res.data.legendtypekeyName.includes('钢管杆')){
        symbolId="252008"
      }else if(res.data.legendtypekeyName.includes('铁塔')){
        symbolId="252016"
      }
      sendMessage(
        mainCadAppRef.value,
    { type: "greeting", content: "SDK_replace_elements", options: { symbolId: symbolId,className:appStore.sdkClassName.clickItemClassName,devId:appStore.sdkClassName.clickItem } },
    (res) => {
      saveChange()
    }
  );
      }else{
        saveChange()
      }
      
    } else {
      ElMessage.error(res.msg)
    }
  }).catch(err => {
    loading.value = false
  })
}

const saveChange = (e) => {
  try {
      const param = {
        taskId,
        newEquipmentId: id,
        oldEquipmentId: id,
        // oldEquipmentId: legendGuidKey.value,
        materialList: gridOptions.data
      }
      saveWithOldEquipmentId(param).then(res => {
        if (res.code === 200) {
          visible.value = false
          ElMessage.success('操作成功！')
        } else {
          ElMessage.error(res.msg)
        }
      })
  } finally {
    loading.value = false
  }
}

const closeVisible = () => {
  visible.value = false
}

onMounted(() => {
  getBasicsInfo()
  getModuleList()
})

</script>

<template>
  <el-dialog
      v-model="visible"
      :z-index="1002"
      draggable
      overflow
      title="单设备选型"
      width="60%"
      @close="closeVisible"
  >
    <el-descriptions
        :column="3"
        :title="`设备情况描述 ${moudleName}`"
        border
        label-width="100px"
    >
      <el-descriptions-item label="线路名称">
        {{ basicsInfo.LineNumber }}
      </el-descriptions-item>
      <el-descriptions-item label="编号">
        {{ basicsInfo.UserNumber }}
      </el-descriptions-item>
      <el-descriptions-item label="类型">
        {{ basicsInfo.AngleType }}
      </el-descriptions-item>
      <el-descriptions-item label="地形">
        {{ basicsInfo.DX }}
      </el-descriptions-item>
      <el-descriptions-item label="土质">
        {{ basicsInfo.TZ }}
      </el-descriptions-item>
    </el-descriptions>
    <el-tabs
        v-model="activeName" @tab-change="tabChange">
      <el-tab-pane label="模块物料" name="1">
        <vxe-grid v-bind="gridOptions" v-on="gridEvents">
          <!--          <template #name="{ row }">
                      {{ row.type === 'material' ? row.materialname : row.modulename }}
                    </template>-->
          <template #edit_state="{ row }">
            <vxe-select
                v-if="row.modulelevel==1 || row.modulelevel==2"
                v-model="row.modulestate"
                :options="stateOption"
                transfer
                @change="e => moduleStateChange(row, e)"
            />
            <vxe-select
                v-if="row.modulelevel==null"
                v-model="row.materialsstate"
                :options="row.options"
                transfer
            />

          </template>
          <template #state="{ row }">
            {{ row.modulelevel==null ? formatMaterialLabel(row.materialsstate) : formatModuleLabel(row.modulestate) }}
          </template>
          <template #action="{ row }">
            <el-button :disabled="row.modify === '1'" text type="primary" @click="editHandle(row)">编辑</el-button>
          </template>
          <!--          <template #factoryModule="{ row }">
                      <el-checkbox v-model="row.checked1" label=""/>
                    </template>-->
        </vxe-grid>
      </el-tab-pane>
      <el-tab-pane label="属性" name="2">
        <el-table :data="propertyTableData" border height="60vh" stripe style="width: 100%">
          <el-table-column align="center" label="属性名" prop="propertyname" width=""/>
          <el-table-column align="center" label="属性值" prop="value" width=""/>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="图纸" name="3">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-card header="图纸列表">
              <el-tree
                  :data="drawingList"
                  :props="defaultProps"
                  highlight-current
                  style="height: 50vh"
                  @node-click="handleNodeClick"
              >
                <template #default="{ node, data }">
                  <el-tooltip
                      :content="node.label"
                      effect="dark"
                      placement="right"
                  >
                    <el-text class="" truncated>
                      {{ node.label }}
                    </el-text>
                  </el-tooltip>
                </template>
              </el-tree>
            </el-card>
          </el-col>
          <el-col :span="18">
            <el-card v-loading="cadLoading" header="预览">
              <div class="" style="height: 50vh">
                <iframe
                    v-if="currentTreeData"
                    ref="cadIframeRef"
                    :src="cadIframeSrc"
                    style="width: 100%;height: 100%"
                />
              </div>
            </el-card>
          </el-col>
        </el-row>
      </el-tab-pane>
    </el-tabs>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeVisible">取消</el-button>
        <el-button :loading="loading" type="primary" @click="onSubmit">确定</el-button>
      </div>
    </template>
    <edit v-if="editVisible" v-model:visible="editVisible" :current-data="currentData" :equipmentId="id"
          @submit="moduleSubmitChange"/>
    <edit-material
        v-if="editMaterialVisible"
        v-model:visible="editMaterialVisible"
        :current-id="currentData?.materialsprojectid"
        @submit="materialSubmitChange"
    />
  </el-dialog>
</template>

<style lang="scss" scoped>
.el-tab-pane {
  height: 60vh;
}
</style>
