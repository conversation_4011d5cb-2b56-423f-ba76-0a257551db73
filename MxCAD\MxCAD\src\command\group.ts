import { McObjectId, MxCADUiPrString, MxCADUtility, MxCpp, McDbGroup, MxCADUiPrKeyWord, MxCADUiPrEntity } from "mxcad";
import { DetailedResult, MrxDbgUiPrPoint, MxFun } from "mxdraw";

MxFun.addCommand("group", async ()=> {
  let getPt: MrxDbgUiPrPoint
  let name = ""
  let description = ""
  let ids: McObjectId[]
  const database = MxCpp.getCurrentDatabase()
  const groupDict = database.GetGroupDictionary()
  while(true) {
      const _ids = await MxCADUtility.userSelect("选择对象", null, (ss, getPoint)=> {
        getPoint.setKeyWords("[名称(N)/说明(D)]")
        getPt = getPoint
      })
      if(getPt.getDetailedResult() === DetailedResult.kEcsIn || getPt.getDetailedResult() === DetailedResult.kCodeAbort || getPt.getDetailedResult() === DetailedResult.kNewCommadIn) return
      if(_ids) ids = _ids
      if(getPt.isKeyWordPicked("N")) {
        while(true) {
          const getName = new MxCADUiPrString()
          getName.setMessage("输入编组名")
          getName.setKeyWords("[查询(A)]")
          const str = await getName.go()
          if(getName.getDetailedResult() === DetailedResult.kCodeAbort || getName.getDetailedResult() === DetailedResult.kEcsIn || getName.getDetailedResult() === DetailedResult.kNewCommadIn) return
          if(getName.isKeyWordPicked("A")) {
            getName.setMessage("请输入要列出的编码组名<*>")
            getName.setKeyWords("")
            const name = await getName.go()
            console.log(name)
            if(getName.getDetailedResult() === DetailedResult.kCodeAbort || getName.getDetailedResult() === DetailedResult.kEcsIn || getName.getDetailedResult() === DetailedResult.kNewCommadIn) return
            if(name && name!== "*")  {
              const groupId = groupDict.getAt(name)
              const group = groupId.getMcDbObject() as McDbGroup
              MxFun.acutPrintf(`\n定义的编组:`)
              if(group) {
                MxFun.acutPrintf(`\n ${group.name}`)
              }
            }
            else if(name === "*" || getName.getDetailedResult() === DetailedResult.kNullEnterIn || getName.getDetailedResult() === DetailedResult.kNullSpaceIn) {
              const groupIds = groupDict.getAllObject()
              MxFun.acutPrintf(`\n定义的编组:`)
              groupIds.forEach((groupId)=> {
                const group = groupId.getMcDbObject() as McDbGroup
                group && MxFun.acutPrintf(`\n ${group.name}`)
              })
            }

            continue;
          }
          if(!str) return
          const groupId = groupDict.getAt(name)
          const group = groupId.getMcDbObject() as McDbGroup
          if(group && groupId.isValid()) {
            MxFun.acutPrintf(`编组 ${name} 已经存在`)
            continue;
          }
          name = str
          break;
        }
        continue;
      }
      if(getPt.isKeyWordPicked("D")) {
        const getName = new MxCADUiPrString()
        getName.setMessage("输入编组说明")
        const str = await getName.go()
        if(!str) return
        description = str
        continue;
      }
      if(getPt.getDetailedResult() === DetailedResult.kNullEnterIn || getPt.getDetailedResult() === DetailedResult.kNullSpaceIn) {
        const isPresence = ids.some((id)=> {
          return database.getEntitiesInTheGroup(id).length !== 0
        })
        if(isPresence) {
          const getKey = new MxCADUiPrKeyWord()
          getKey.setMessage("包含相同对象的组已经存在。仍要创建新的组？<N>")
          getKey.setKeyWords("[是(Y)/否(N)]")
          const key = await getKey.go()
          if(key?.toLocaleUpperCase() === "N") {
            return
          }
          if(!key) return
        }
        if(database.CreateGroup(ids, name)) {
          const groupId = groupDict.getAt(name)
          const group = groupId.getMcDbObject() as McDbGroup
          group.description = description
        }
      }
      break
  }
})

MxFun.addCommand("ungroup", async ()=> {

  let name!: string
  const database = MxCpp.getCurrentDatabase()
  const groupDict = database.GetGroupDictionary()
  while(true) {
      const getEnt = new MxCADUiPrEntity()
      getEnt.setMessage("选择对象")
      getEnt.setKeyWords("[名称(N)]")
      const id = await getEnt.go()
      if(getEnt.getDetailedResult() === DetailedResult.kEcsIn || getEnt.getDetailedResult() === DetailedResult.kCodeAbort || getEnt.getDetailedResult() === DetailedResult.kNewCommadIn) return

      if(getEnt.isKeyWordPicked("N")) {
        while(true) {
          const getName = new MxCADUiPrString()
          getName.setMessage("输入编组名")
          getName.setKeyWords("[查询(A)]")
          const str = await getName.go()
          if(getName.getDetailedResult() === DetailedResult.kCodeAbort || getName.getDetailedResult() === DetailedResult.kEcsIn || getName.getDetailedResult() === DetailedResult.kNewCommadIn) return
          if(getName.isKeyWordPicked("A")) {
            getName.setMessage("请输入要列出的编码组名<*>")
            getName.setKeyWords("")
            const name = await getName.go()
            if(getName.getDetailedResult() === DetailedResult.kCodeAbort || getName.getDetailedResult() === DetailedResult.kEcsIn || getName.getDetailedResult() === DetailedResult.kNewCommadIn) return
            if(name && name!== "*")  {
              const groupId = groupDict.getAt(name)
              const group = groupId.getMcDbObject() as McDbGroup
              MxFun.acutPrintf(`\n定义的编组:`)
              if(group) {
                MxFun.acutPrintf(`\n ${group.name}`)
              }
            }
            else if(name === "*" || getName.getDetailedResult() === DetailedResult.kNullEnterIn || getName.getDetailedResult() === DetailedResult.kNullSpaceIn) {
              const groupIds = groupDict.getAllObject()
              MxFun.acutPrintf(`\n定义的编组:`)
              groupIds.forEach((groupId)=> {
                const group = groupId.getMcDbObject() as McDbGroup
                group && MxFun.acutPrintf(`\n ${group.name}`)
              })
            }

            continue;
          }
          if(!str) return
          const groupId = groupDict.getAt(name)
          const group = groupId.getMcDbObject() as McDbGroup
          if(group && groupId.isValid()) {
            MxFun.acutPrintf(`编组 ${name} 已经存在`)
            continue;
          }
          name = str
          break;
        }
        continue;
      }
      if(id) {
        const ent = id.getMcDbEntity()
        if(ent) {
          const handle = ent.getHandle()
          const groupNames = groupDict.getAllObjectName()
          const length = groupNames.length()
          for (let index = 0; index < length; index++) {
            const groupName = groupNames.at(index);
            const groupId = groupDict.getAt(groupName)
            const group = groupId.getMcDbObject() as McDbGroup
            if(!group) continue;
            const entityIds = group.getAllEntityId()
            entityIds.some((entityId)=> {
              const entity = entityId.getMcDbEntity()
              if(entity?.getHandle() === handle) {
                name = groupName
                return true
              }
              return false
            })
          }
        }
      }

      if(name) {
        const groupId = groupDict.getAt(name)
        const group = groupId.getMcDbObject() as McDbGroup
        if(group) {
          group.clear()
          groupDict.remove(name)
          MxFun.acutPrintf(`\n组 ${name} 已分解`)
        }else {
          MxFun.acutPrintf(`\n对象不是组成员`)
          continue;
        }
      }
      else {
        MxFun.acutPrintf(`\n对象不是组成员`)
        continue;
      }
      break
  }
})
