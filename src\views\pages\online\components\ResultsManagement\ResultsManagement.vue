<template>
   <!-- 六.成果输出管理 -->
    <!-- 拆旧物资清册 -->
    <inventoryMaterials></inventoryMaterials>
    <!-- 主要设备材料清单 -->
    <MajorEquipment></MajorEquipment>
    <!-- 钢管杆明细表 -->
    <steelPipe></steelPipe>
    <!-- 应力弧垂表 -->
    <insertSag></insertSag>
    <!-- 利旧物资表 -->
    <InventoryOldMaterials></InventoryOldMaterials>
    <!-- 电缆附属设施统计表 -->
    <CableFacilitieTable></CableFacilitieTable>
    <!-- 电缆明细表 -->
    <CableLineInfoTable></CableLineInfoTable>
    <!-- 导出工程设计成果 -->
    <!-- <data-dialog
      @close="closeDialog"
      dataWidth="750"
      v-if="appStore.mapIndex == '导出工程设计成果'"
    >
      <template #header>
        <h4 style="margin-left: 10px; margin-top: 0px; line-height: 39px">
          导出工程
        </h4>
      </template>
      <template #body>
        <div style="padding: 10px; font-size: 13px">标注设置</div>
        <div class="material">
          <el-table
            border
            class="input-table"
            size="small"
            :data="data"
            style="width: 100%"
          >
            <el-table-column
              align="center"
              prop="sk"
              label="工程名称"
              width="150px"
            >
            </el-table-column>
            <el-table-column align="center" label="设计阶段" width="150px">
              <el-table-column
                align="center"
                prop="sk"
                label="初步设计"
                width="150px"
              >
              </el-table-column>
              <el-table-column
                align="center"
                prop="sk"
                label="施工图设计"
                width="150px"
              >
              </el-table-column>
              <el-table-column
                align="center"
                prop="sk"
                label="初始施设一体化"
                width="150px"
              >
              </el-table-column>
              <el-table-column
                align="center"
                prop="sk"
                label="竣工图设计"
                width="150px"
              >
              </el-table-column>
            </el-table-column>
          </el-table>
        </div>
        <div style="background: #e4f2f2; height: 50px" class="line">
          <div class="line-bnt">确定</div>
          <div
            style="
              margin-left: 30px;
              background: #e4f2f2;
              border: 2px solid #199092;
              color: #199092;
              width: 84px;
              height: 30px;
              line-height: 27px;
              cursor: pointer;
              border-radius: 5px;
            "
          >
            取消
          </div>
        </div>
      </template>
    </data-dialog> -->
      
      
</template>
<script setup>
import DataDialog from "@/components/DataDialog/index.vue";
import MajorEquipment from "./components/MajorEquipment.vue"
import insertSag from "./components/insertSag.vue"
import inventoryMaterials from "./components/inventoryMaterials.vue"
import InventoryOldMaterials from "./components/InventoryOldMaterials.vue";
import CableFacilitieTable from "./components/CableFacilitieTable.vue";
import { useIframeCommunication } from "@/hooks/useIframeCommunication";
import useAppStore from "@/store/modules/app.js";
import { useRoute } from "vue-router";
import steelPipe from "./components/steelPipe.vue";
import CableLineInfoTable from "./components/CableLineInfoTable.vue";
import {
  pushJghDataToZaojia
} from "@/api/insertSag/index.js";
import { ElLoading } from 'element-plus'
const { proxy } = getCurrentInstance();
const appStore = useAppStore();
const route = useRoute();
const taskId = route.query.id;
const data = ref([
  {
    name: "线路1",
    key: "New",
    sk: " JG/TH",
    ksd: "删除",
  },
])
const closeDialog = () => {
  appStore.mapIndex = "";
};

// 处理点击事件
const oniframeMessage = (param) => {
  const myiframe =appStore.iframe
  if (myiframe && myiframe.contentWindow) {
    myiframe.contentWindow.postMessage(param, "*");
    // 先移除之前的事件监听器，防止重复绑定
    window.removeEventListener('message', handleMessage);
    // 添加新的事件监听器
    window.addEventListener('message', handleMessage);
  }
}

// 事件处理函数
const handleMessage = (event) => {
  console.log("🚀 ~ handleMessage ~ event:", event)
  
}
watch(
  () => appStore.mapIndex,
  (newInfo, oldInfo) => {
    if (newInfo == "绘制路径图元") {
    }else if(newInfo=='技经提资'){  
      const loading = ElLoading.service({
        lock: true,
        text: '技经正在提资中，请稍等...',
        background: 'rgba(0, 0, 0, 0.7)',
      })
      pushJghDataToZaojia(taskId).then(res=>{
         if(res.code===200){
          proxy.$message.success("操作成功");
          loading.close()
        }else{
          proxy.$message.error(res.msg);
          loading.close()
         }
      })
    }
  }
);
</script>

<style scoped lang="scss">
@use '../index' as *;
</style>
