import{_ as D,V as _,U as o,Q as B}from"./index-CzBriCFR.js";import{M as L}from"./index-itnQ6avM.js";import{M as P}from"./index-D2jCiJ2B.js";import{_ as r,$ as C,h as g,m as l,a4 as e,Q as u,V as s,F as M,a0 as p,a3 as T,k,d as F,u as U,B as G}from"./vue-Cj9QYd7Z.js";import{B as t,z as n,V as f,L as m,I as i,G as y,H as d}from"./vuetify-BqCp6y38.js";import"./mxcad-DrgW2waE.js";import"./mxdraw-BQ5HhRCY.js";import"./handsontable-Ch5RdAT_.js";import"./mapbox-gl-DQAr7S0z.js";const I={},R={class:"box"};function E(b,a){return r(),C("div",R)}const h=D(I,[["render",E],["__scopeId","data-v-dc3c2c1a"]]),N=g({__name:"Line",setup(b){return(a,c)=>(r(),C(M,null,[l(n,null,{default:e(()=>[l(t,{cols:"6","align-stretch":""},{default:e(()=>[l(_,{title:a.t("563")},{default:e(()=>[l(n,{class:"mt-2"},{default:e(()=>[l(t,{cols:"6"},{default:e(()=>[l(o,{"key-name":"C",colon:""},{default:e(()=>[u(s(a.t("226")),1)]),_:1})]),_:1}),l(t,{cols:"6"},{default:e(()=>[l(f,{"bg-color":"grey-lighten-2",class:"",items:[]})]),_:1})]),_:1}),l(n,{class:"mt-2"},{default:e(()=>[l(t,{cols:"6"},{default:e(()=>[l(o,{"key-name":"L",colon:""},{default:e(()=>[u(s(a.t("254")),1)]),_:1})]),_:1}),l(t,{cols:"6"},{default:e(()=>[l(f,{"bg-color":"grey-lighten-2",class:"",items:[]})]),_:1})]),_:1}),l(n,{class:"mt-2"},{default:e(()=>[l(t,{cols:"6"},{default:e(()=>[l(o,{"key-name":"G",colon:""},{default:e(()=>[u(s(a.t("183")),1)]),_:1})]),_:1}),l(t,{cols:"6"},{default:e(()=>[l(f,{"bg-color":"grey-lighten-2",class:"",items:[]})]),_:1})]),_:1}),l(n,{class:"mt-2"},{default:e(()=>[l(t,{cols:"6"},{default:e(()=>[l(o,{"key-name":"G",colon:""},{default:e(()=>[u(s(a.t("564")),1)]),_:1})]),_:1}),l(t,{cols:"3"}),l(t,{cols:"3"},{default:e(()=>[l(m,{class:"",type:"number"})]),_:1})]),_:1}),l(n,null,{default:e(()=>[l(t,{cols:"2"},{default:e(()=>[u(s(a.t("440"))+": ",1)]),_:1}),l(t,{cols:"5"},{default:e(()=>[l(i,{class:""},{label:e(()=>[l(o,{"key-name":"M"},{default:e(()=>[u(s(a.t("563"))+"1",1)]),_:1})]),_:1})]),_:1}),l(t,{cols:"5"},{default:e(()=>[l(i,{class:"ml-2"},{label:e(()=>[l(o,{"key-name":"D"},{default:e(()=>[u(s(a.t("563"))+"2",1)]),_:1})]),_:1})]),_:1})]),_:1})]),_:1},8,["title"])]),_:1}),l(t,{cols:"6"},{default:e(()=>[l(h)]),_:1})]),_:1}),l(_,{title:a.t("565")},{default:e(()=>[l(n,null,{default:e(()=>[l(t,{cols:"6"},{default:e(()=>[l(n,{class:"mt-2"},{default:e(()=>[l(t,{cols:"6"},{default:e(()=>[l(o,{"key-name":"C",colon:""},{default:e(()=>[u(s(a.t("226")),1)]),_:1})]),_:1}),l(t,{cols:"6"},{default:e(()=>[l(f,{"bg-color":"grey-lighten-2",class:"",items:[]})]),_:1})]),_:1}),l(n,{class:"mt-2"},{default:e(()=>[l(t,{cols:"6"},{default:e(()=>[l(o,{"key-name":"T",colon:""},{default:e(()=>[u(s(a.t("565"))+"1"+s(a.t("508")+a.t("254")),1)]),_:1})]),_:1}),l(t,{cols:"6"},{default:e(()=>[l(f,{"bg-color":"grey-lighten-2",class:"",items:[]})]),_:1})]),_:1}),l(n,{class:"mt-2"},{default:e(()=>[l(t,{cols:"6"},{default:e(()=>[l(o,{"key-name":"T",colon:""},{default:e(()=>[u(s(a.t("565"))+"2"+s(a.t("508")+a.t("254")),1)]),_:1})]),_:1}),l(t,{cols:"6"},{default:e(()=>[l(f,{"bg-color":"grey-lighten-2",class:"",items:[]})]),_:1})]),_:1}),l(n,{class:"mt-2"},{default:e(()=>[l(t,{cols:"6"},{default:e(()=>[l(o,{"key-name":"G",colon:""},{default:e(()=>[u(s(a.t("183")),1)]),_:1})]),_:1}),l(t,{cols:"6"},{default:e(()=>[l(f,{"bg-color":"grey-lighten-2",class:"",items:[]})]),_:1})]),_:1}),l(n,null,{default:e(()=>[l(t,{cols:"2"},{default:e(()=>[u(s(a.t("440"))+": ",1)]),_:1}),l(t,{cols:"5"},{default:e(()=>[l(i,{class:"ml-2"},{label:e(()=>[l(o,{"key-name":"M"},{default:e(()=>[u(s(a.t("563"))+"1",1)]),_:1})]),_:1})]),_:1}),l(t,{cols:"5"},{default:e(()=>[l(i,{class:"ml-2"},{label:e(()=>[l(o,{"key-name":"D"},{default:e(()=>[u(s(a.t("563"))+"2",1)]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),l(t,{cols:"6"},{default:e(()=>[l(n,{class:"mt-2"},{default:e(()=>[l(t,{cols:"8"},{default:e(()=>[l(o,{"key-name":"X",colon:""},{default:e(()=>[u(s(a.t("566")),1)]),_:1})]),_:1}),l(t,{cols:"4"},{default:e(()=>[l(m,{class:"",type:"number"})]),_:1})]),_:1}),l(n,{class:"mt-4"},{default:e(()=>[l(t,{cols:"8"},{default:e(()=>[l(o,{"key-name":"F",colon:""},{default:e(()=>[u(s(a.t("567")),1)]),_:1})]),_:1}),l(t,{cols:"4"},{default:e(()=>[l(m,{class:"",type:"number"})]),_:1})]),_:1}),l(i,{class:""},{label:e(()=>[l(o,{"key-name":"O"},{default:e(()=>[u(s(a.t("568")),1)]),_:1})]),_:1}),l(n,{class:"mt-2"},{default:e(()=>[l(t,{cols:"1"}),l(t,{cols:"7"},{default:e(()=>[l(o,{"key-name":"E",colon:"",class:"ml-3"},{default:e(()=>[u(s(a.t("410")),1)]),_:1})]),_:1}),l(t,{cols:"4"},{default:e(()=>[l(m,{class:"",type:"number",disabled:""})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1},8,["title"])],64))}}),O={class:"h-100"},j=g({__name:"SymbolsAndArrows",setup(b){return(a,c)=>(r(),C(M,null,[l(n,null,{default:e(()=>[l(t,{cols:"6"},{default:e(()=>[l(_,{title:a.t("550")},{default:e(()=>[l(o,{"key-name":"T",colon:""},{default:e(()=>[u(s(a.t("581")),1)]),_:1}),l(f,{"bg-color":"grey-lighten-2",class:"",items:[]}),l(o,{"key-name":"D",colon:""},{default:e(()=>[u(s(a.t("582")),1)]),_:1}),l(f,{"bg-color":"grey-lighten-2",class:"",items:[]}),l(o,{"key-name":"L",colon:""},{default:e(()=>[u(s(a.t("583")),1)]),_:1}),l(f,{"bg-color":"grey-lighten-2",class:"",items:[]}),l(n,{class:"mt-1"},{default:e(()=>[l(t,{cols:"8"},{default:e(()=>[l(o,{"key-name":"I",colon:""},{default:e(()=>[u(s(a.t("180")),1)]),_:1})]),_:1}),l(t,{cols:"4"},{default:e(()=>[l(m,{class:"",type:"number"})]),_:1})]),_:1})]),_:1},8,["title"])]),_:1}),l(t,{cols:"6"},{default:e(()=>[l(h)]),_:1})]),_:1}),l(n,null,{default:e(()=>[l(t,{cols:"6"},{default:e(()=>[l(_,{title:a.t("584")},{default:e(()=>[l(n,null,{default:e(()=>[l(t,{cols:"8"},{default:e(()=>[l(y,{class:"",inline:!1},{default:e(()=>[l(d,{value:"1"},{label:e(()=>[l(o,{"key-name":"N"},{default:e(()=>[u(s(a.t("585")),1)]),_:1})]),_:1}),l(d,{value:"2"},{label:e(()=>[l(o,{"key-name":"M"},{default:e(()=>[u(s(a.t("586")),1)]),_:1})]),_:1}),l(d,{value:"3"},{label:e(()=>[l(o,{"key-name":"E"},{default:e(()=>[u(s(a.t("283")),1)]),_:1})]),_:1})]),_:1})]),_:1}),l(t,{cols:"4"},{default:e(()=>[p("div",O,[l(m,{class:"",type:"number"})])]),_:1})]),_:1})]),_:1},8,["title"])]),_:1}),l(t,{cols:"6"},{default:e(()=>[l(_,{title:a.t("587")},{default:e(()=>[l(y,{class:"",inline:!1},{default:e(()=>[l(d,{value:"1"},{label:e(()=>[l(o,{"key-name":"P"},{default:e(()=>[u(s(a.t("588")),1)]),_:1})]),_:1}),l(d,{value:"2"},{label:e(()=>[l(o,{"key-name":"A"},{default:e(()=>[u(s(a.t("589")),1)]),_:1})]),_:1}),l(d,{value:"3"},{label:e(()=>[l(o,{"key-name":"O"},{default:e(()=>[u(s(a.t("585")),1)]),_:1})]),_:1})]),_:1})]),_:1},8,["title"])]),_:1})]),_:1})],64))}}),Q=g({__name:"Text",setup(b){return(a,c)=>(r(),C(M,null,[l(n,{"align-stretch":""},{default:e(()=>[l(t,{cols:"6","align-self":"auto"},{default:e(()=>[l(_,{title:a.t("590"),class:"h-100"},{default:e(()=>[l(n,null,{default:e(()=>[l(t,{cols:"6"},{default:e(()=>[l(o,{"key-name":"Y",colon:""},{default:e(()=>[u(s(a.t("277")),1)]),_:1})]),_:1}),l(t,{cols:"6"},{default:e(()=>[l(f,{"bg-color":"grey-lighten-2",class:"",items:[]})]),_:1})]),_:1}),l(n,null,{default:e(()=>[l(t,{cols:"6"},{default:e(()=>[l(o,{"key-name":"C",colon:""},{default:e(()=>[u(s(a.t("591")),1)]),_:1})]),_:1}),l(t,{cols:"6"},{default:e(()=>[l(f,{"bg-color":"grey-lighten-2",class:"",items:[]})]),_:1})]),_:1}),l(n,null,{default:e(()=>[l(t,{cols:"8"},{default:e(()=>[l(o,{"key-name":"C",colon:""},{default:e(()=>[u(s(a.t("181")),1)]),_:1})]),_:1}),l(t,{cols:"4"},{default:e(()=>[l(m,{class:"",type:"number"})]),_:1})]),_:1}),l(i,{class:""},{label:e(()=>[l(o,{"key-name":"F"},{default:e(()=>[u(s(a.t("592")),1)]),_:1})]),prepend:e(v=>c[0]||(c[0]=[])),_:1})]),_:1},8,["title"])]),_:1}),l(t,{cols:"6"},{default:e(()=>[l(h)]),_:1})]),_:1}),l(n,{"align-stretch":""},{default:e(()=>[l(t,{cols:"6"},{default:e(()=>[l(_,{title:a.t("554")},{default:e(()=>[l(n,null,{default:e(()=>[l(t,{cols:"6"},{default:e(()=>[l(o,{"key-name":"V",colon:""},{default:e(()=>[u(s(a.t("353")),1)]),_:1})]),_:1}),l(t,{cols:"6"},{default:e(()=>[l(f,{"bg-color":"grey-lighten-2",class:"",items:[]})]),_:1})]),_:1}),l(n,null,{default:e(()=>[l(t,{cols:"6"},{default:e(()=>[l(o,{"key-name":"C",colon:""},{default:e(()=>[u(s(a.t("352"))+"Z",1)]),_:1})]),_:1}),l(t,{cols:"6"},{default:e(()=>[l(f,{"bg-color":"grey-lighten-2",class:"",items:[]})]),_:1})]),_:1}),l(n,null,{default:e(()=>[l(t,{cols:"8"},{default:e(()=>[l(o,{"key-name":"O",colon:""},{default:e(()=>[u(s(a.t("593")),1)]),_:1})]),_:1}),l(t,{cols:"4"},{default:e(()=>[l(m,{class:"",type:"number"})]),_:1})]),_:1})]),_:1},8,["title"])]),_:1}),l(t,{cols:"6","align-self":"auto"},{default:e(()=>[l(_,{title:a.t("594"),class:"h-100"},{default:e(()=>[l(y,{class:"",inline:!1},{default:e(()=>[l(d,{value:"1",label:a.t("352")},null,8,["label"]),l(d,{value:"2",label:a.t("595")},null,8,["label"]),l(d,{value:"3",label:"ISO"+a.t("596")},null,8,["label"])]),_:1})]),_:1},8,["title"])]),_:1})]),_:1})],64))}}),W={class:"h-100 d-flex flex-column align-stretch justify-space-between"},X={class:"w-75"},z={class:""},H=g({__name:"Adjust",setup(b){return(a,c)=>(r(),T(n,{"align-stretch":""},{default:e(()=>[l(t,{cols:"6","align-self":"auto"},{default:e(()=>[p("div",W,[l(_,{title:a.t("546")},{default:e(()=>[p("p",X,s(a.t("547"))+": ",1),l(y,{class:"",inline:!1},{default:e(()=>[l(d,{value:"1",label:a.t("548")+"("+a.t("549")+")"},null,8,["label"]),l(d,{value:"2",label:a.t("550")},null,8,["label"]),l(d,{value:"3",label:a.t("285")},null,8,["label"]),l(d,{value:"4",label:a.t("551")},null,8,["label"]),l(d,{value:"5",label:a.t("552")},null,8,["label"])]),_:1}),l(i,{class:"",label:a.t("553")},null,8,["label"])]),_:1},8,["title"]),l(_,{title:a.t("554")},{default:e(()=>[p("p",z,s(a.t("555"))+":",1),l(y,{class:"",inline:!1},{default:e(()=>[l(d,{value:"1",label:a.t("548")+"("+a.t("549")+")"},null,8,["label"]),l(d,{value:"2",label:a.t("550")},null,8,["label"]),l(d,{value:"3",label:a.t("285")},null,8,["label"])]),_:1})]),_:1},8,["title"])])]),_:1}),l(t,{cols:"6"},{default:e(()=>[l(h),l(_,{title:a.t("556")},{default:e(()=>[l(n,null,{default:e(()=>[l(t,{cols:"8"},{default:e(()=>[l(y,{class:"",inline:!1},{default:e(()=>[l(d,{value:"1",label:a.t("557")},null,8,["label"]),l(d,{value:"2"},{label:e(()=>[l(o,{"key-name":"S",colon:""},{default:e(()=>[u(s(a.t("558")),1)]),_:1})]),_:1})]),_:1})]),_:1}),l(t,{cols:"4","align-self":"end"},{default:e(()=>[l(m,{class:"",type:"number"})]),_:1})]),_:1})]),_:1},8,["title"]),l(_,{title:a.t("559")},{default:e(()=>[l(i,{class:""},{label:e(()=>[l(o,{"key-name":"P"},{default:e(()=>[u(s(a.t("560")),1)]),_:1})]),_:1}),l(i,{class:""},{label:e(()=>[l(o,{"key-name":"D"},{default:e(()=>[u(s(a.t("561")),1)]),_:1})]),_:1})]),_:1},8,["title"])]),_:1})]),_:1}))}}),Y=g({__name:"MainUnit",setup(b){return(a,c)=>(r(),T(n,{"align-stretch":""},{default:e(()=>[l(t,{cols:"6","align-self":"auto"},{default:e(()=>[l(_,{title:a.t("546"),class:"h-100"},{default:e(()=>[l(n,null,{default:e(()=>[l(t,{cols:"6"},{default:e(()=>[l(o,{colon:"","key-name":"U"},{default:e(()=>[u(s(a.t("569")),1)]),_:1})]),_:1}),l(t,{cols:"6"},{default:e(()=>[l(f,{"bg-color":"grey-lighten-2",class:"",items:[]})]),_:1})]),_:1}),l(n,null,{default:e(()=>[l(t,{cols:"6"},{default:e(()=>[l(o,{colon:"","key-name":"P"},{default:e(()=>[u(s(a.t("570")),1)]),_:1})]),_:1}),l(t,{cols:"6"},{default:e(()=>[l(f,{"bg-color":"grey-lighten-2",class:"",items:[]})]),_:1})]),_:1}),l(n,null,{default:e(()=>[l(t,{cols:"8"},{default:e(()=>[l(o,{colon:"","key-name":"C"},{default:e(()=>[u(s(a.t("571")),1)]),_:1})]),_:1}),l(t,{cols:"4"},{default:e(()=>[l(f,{"bg-color":"grey-lighten-2",class:"",items:[]},{clear:e(v=>c[0]||(c[0]=[])),_:1})]),_:1})]),_:1}),l(n,null,{default:e(()=>[l(t,{cols:"8"},{default:e(()=>[l(o,{colon:"","key-name":"R"},{default:e(()=>[u(s(a.t("572")),1)]),_:1})]),_:1}),l(t,{cols:"4"},{default:e(()=>[l(m,{class:"",type:"number"})]),_:1})]),_:1}),l(n,null,{default:e(()=>[l(t,{cols:"6"},{default:e(()=>[l(o,{colon:"","key-name":"X"},{default:e(()=>[u(s(a.t("573")),1)]),_:1})]),_:1}),l(t,{cols:"6"},{default:e(()=>[l(m,{class:""})]),_:1})]),_:1}),l(n,null,{default:e(()=>[l(t,{cols:"6"},{default:e(()=>[l(o,{colon:"","key-name":"S"},{default:e(()=>[u(s(a.t("574")),1)]),_:1})]),_:1}),l(t,{cols:"6"},{default:e(()=>[l(m,{class:""})]),_:1})]),_:1}),l(_,{title:a.t("575")},{default:e(()=>[l(n,null,{default:e(()=>[l(t,{cols:"8"},{default:e(()=>[l(o,{colon:"","key-name":"E"},{default:e(()=>[u(s(a.t("576")),1)]),_:1})]),_:1}),l(t,{cols:"4"},{default:e(()=>[l(m,{class:"",type:"number"})]),_:1})]),_:1}),l(i,{class:"",label:a.t("577")},null,8,["label"])]),_:1},8,["title"]),l(_,{title:a.t("578")},{default:e(()=>[l(i,{class:""},{label:e(()=>[l(o,{"key-name":"L"},{default:e(()=>[u(s(a.t("579")),1)]),_:1})]),_:1}),l(i,{class:""},{label:e(()=>[l(o,{"key-name":"T"},{default:e(()=>[u(s(a.t("580")),1)]),_:1})]),prepend:e(v=>c[1]||(c[1]=[])),_:1})]),_:1},8,["title"])]),_:1},8,["title"])]),_:1}),l(t,{cols:"6"},{default:e(()=>[l(h),l(_,{title:a.t("291")},{default:e(()=>[l(n,null,{default:e(()=>[l(t,{cols:"5"},{default:e(()=>[l(o,{colon:"","key-name":"A"},{default:e(()=>[u(s(a.t("569")),1)]),_:1})]),_:1}),l(t,{cols:"7"},{default:e(()=>[l(f,{"bg-color":"grey-lighten-2",class:"",items:[]})]),_:1})]),_:1}),l(n,null,{default:e(()=>[l(t,{cols:"5"},{default:e(()=>[l(o,{colon:"","key-name":"D"},{default:e(()=>[u(s(a.t("570")),1)]),_:1})]),_:1}),l(t,{cols:"7"},{default:e(()=>[l(f,{"bg-color":"grey-lighten-2",class:"",items:[]})]),_:1})]),_:1}),l(_,{title:a.t("578")},{default:e(()=>[l(i,{class:""},{label:e(()=>[l(o,{"key-name":"L"},{default:e(()=>[u(s(a.t("579")),1)]),_:1})]),_:1}),l(i,{class:""},{label:e(()=>[l(o,{"key-name":"T"},{default:e(()=>[u(s(a.t("580")),1)]),_:1})]),_:1})]),_:1},8,["title"])]),_:1},8,["title"])]),_:1})]),_:1}))}}),Z=()=>({tab:"线",component:()=>k(N)}),q=()=>({tab:"符号与箭头",component:()=>k(j)}),J=()=>({tab:"文字",component:()=>k(Q)}),K=()=>({tab:"调整",component:()=>k(H)}),x=()=>({tab:"主单位",component:()=>k(Y)}),ll={class:"px-3"},_l=g({__name:"index",setup(b){const a=F(0),{isShow:c,showDialog:v}=B(!1),$=[{name:"确定",fun:()=>{},primary:!0},{name:"取消",fun:()=>v(!1)}],S=[Z(),q(),J(),K(),x()];return(A,V)=>(r(),T(L,{title:A.t("修改标注样式")+":Standard",modelValue:U(c),"onUpdate:modelValue":V[1]||(V[1]=w=>G(c)?c.value=w:null),footerBtnList:$,"max-width":"820"},{default:e(()=>[p("div",ll,[l(P,{items:S,modelValue:a.value,"onUpdate:modelValue":V[0]||(V[0]=w=>a.value=w),height:444,isTabMinWidthAuto:""},null,8,["modelValue"])])]),_:1},8,["title","modelValue"]))}});export{_l as default};
