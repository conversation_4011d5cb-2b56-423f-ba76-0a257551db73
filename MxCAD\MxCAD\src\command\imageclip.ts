import { McDb, McDbPolyline, McDbRasterImage, McGePoint3d, McGePoint3dArray, MxCADResbuf, MxCADUiPrEntity, MxCADUiPrKeyWord, MxCADUiPrPoint, MxCADUtility } from "mxcad";
import { DetailedResult, MxFun } from "mxdraw";


MxFun.addCommand("imageclip", async ()=> {
  const filter = new MxCADResbuf();
  filter.AddMcDbEntityTypes("IMAGE");
  const getEnt = new MxCADUiPrEntity()
  getEnt.setFilter(filter)
  while(true) {
    getEnt.setMessage("选择想要剪裁的图像")
    const entId = await getEnt.go()
    if(getEnt.getDetailedResult() === DetailedResult.kCodeAbort) return
    if(getEnt.getDetailedResult() === DetailedResult.kEcsIn) return
    if(getEnt.getDetailedResult() === DetailedResult.kNewCommadIn) return
    if(!entId) continue;
    if(!entId.isValid()) continue;

    const img = entId.getMcDbEntity() as McDbRasterImage
    let isInversion = false
    const getKey = new MxCADUiPrKeyWord()
    getKey.setMessage("输入图像剪裁的选项")
    getKey.setKeyWords("[删除(D)/新建边界(N)]")
    const key = await getKey.go()
    if(getKey.getDetailedResult() === DetailedResult.kCodeAbort) return
    if(getKey.getDetailedResult() === DetailedResult.kEcsIn) return
    if(getKey.getDetailedResult() === DetailedResult.kNewCommadIn) return

    if(key?.toLocaleUpperCase() === "D") {
      img.setClipBoundary(McDb.ClipBoundaryType.kInvalid, new McGePoint3dArray())

    }
    if(key?.toLocaleUpperCase() === "N" || getKey.getDetailedResult()  === DetailedResult.kNullSpaceIn || getKey.getDetailedResult()  === DetailedResult.kNullEnterIn) {
      const getPoint = new MxCADUiPrPoint()
      getPoint.setMessage("指定图像剪裁的边界<矩形>")
      getPoint.setKeyWords("[多边形(P)/矩形(R)/反向剪裁(I)]")
      let point = await getPoint.go()
      if(getPoint.getDetailedResult() === DetailedResult.kCodeAbort) return
      if(getPoint.getDetailedResult() === DetailedResult.kEcsIn) return
      if(getPoint.getDetailedResult() === DetailedResult.kNewCommadIn) return
      if(getPoint.isKeyWordPicked("I")) {
        isInversion = !isInversion
        if(isInversion) {
          MxFun.acutPrintf(`\n内部模式 - 边界内的对象将被隐藏\n`)
        }else {
          MxFun.acutPrintf(`\n外部模式 - 边界外的对象将被隐藏\n`)
        }
        continue;
      }
      if(getPoint.isKeyWordPicked("P")) {
        const getPoint = new MxCADUiPrPoint()
        getPoint.setMessage("指定第一点")
        const startPoint = await getPoint.go()
        if(getPoint.getDetailedResult() === DetailedResult.kCodeAbort) return
        if(getPoint.getDetailedResult() === DetailedResult.kEcsIn) return
        if(getPoint.getDetailedResult() === DetailedResult.kNewCommadIn) return
        if(!startPoint) return;
        let points: McGePoint3d[] = [startPoint]
        getPoint.setUserDraw((pt, pw)=> {
          const pl = new McDbPolyline()
          points.forEach((point)=> {
            pl.addVertexAt(point)
          })
          pw.drawMcDbEntity(pl)
          pw.drawLine(points[points.length - 1].toVector3(), pt.toVector3())
        })
        while(true) {
          if(points.length > 1) getPoint.setKeyWords(`[${points.length > 2 ? "闭合(C)/" : ""}放弃(U)]`)

          const point = await getPoint.go()
          if(getPoint.getDetailedResult() === DetailedResult.kCodeAbort) return
          if(getPoint.getDetailedResult() === DetailedResult.kEcsIn) return
          if(getPoint.getDetailedResult() === DetailedResult.kNewCommadIn) return
          if(getPoint.getDetailedResult() === DetailedResult.kNullEnterIn || getPoint.getDetailedResult() === DetailedResult.kNullSpaceIn) {
            break;
          }
          if(getPoint.isKeyWordPicked("U")) {
            points.pop()
            continue;
          }
          if(getPoint.isKeyWordPicked("C")) {
            points.push(points[0])
            break;
          }
          if(!point) continue;
          points.push(point)
          continue;
        }
        if(isInversion) {
          const { minPt, maxPt } = img.getBoundingBox()
          points = [minPt, new McGePoint3d(minPt.x, maxPt.y), maxPt, new McGePoint3d(maxPt.x, minPt.y), minPt, ...points, points[0]]
        }
        img.setClipBoundary(McDb.ClipBoundaryType.kPoly, new McGePoint3dArray().copy(points))
      }
      if(getPoint.isKeyWordPicked("R")) {
        getPoint.setMessage("指定第一角点")
        point = await getPoint.go()
        if(!point) return
      }
      if(point) {
        getPoint.setMessage("指定对角点")
        getPoint.setKeyWords("")
        const point2 = await getPoint.go()
        if(!point2) return
        if(isInversion) {
          const { minPt, maxPt } = img.getBoundingBox()
          const points = [minPt, new McGePoint3d(minPt.x, maxPt.y), maxPt, new McGePoint3d(maxPt.x, minPt.y), minPt,
            point, new McGePoint3d(point.x, point2.y), point2, new McGePoint3d(point2.x, point.y)]
            img.setClipBoundary(McDb.ClipBoundaryType.kPoly, new McGePoint3dArray().copy(points))
        }else {
          console.log(img.setClipBoundary(McDb.ClipBoundaryType.kRect, new McGePoint3dArray().copy([point, point2])))
        }
      }
    }
    break;
  }
})
