import request from '@/utils/request'

// 查询物料列表
export function listBaseMaterials(query) {
  return request({
    url: '/base_materials/list',
    method: 'get',
    params: query
  })
}

// 查询物料详细
export function getBaseMaterials(objId) {
  return request({
    url: '/base_materials/' + objId,
    method: 'get'
  })
}

// 新增物料
export function addBaseMaterials(data) {
  return request({
    url: '/base_materials',
    method: 'post',
    data: data
  })
}

// 修改物料
export function updateBaseMaterials(data) {
  return request({
    url: '/base_materials/update',
    method: 'post',
    data: data
  })
}

// 删除物料
export function delBaseMaterials(objId) {
  return request({
    url: '/base_materials/' + objId,
    method: 'post'
  })
}

