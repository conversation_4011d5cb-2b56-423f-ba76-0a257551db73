import{d as H,w as $e,h as Je,$ as Ce,m as p,a4 as u,u as d,B as W,F as Fe,_ as Ve,a0 as S,V as b,a5 as Ge,H as Qe,W as Ke,Q as T,n as Re}from"./vue-Cj9QYd7Z.js";import{M as Ue}from"./index-itnQ6avM.js";import{s as E,Q as Te,l as B,e as q,d as Ze,c as qe,o as _e,a as et,V as be,U as Be,Z as ue,ao as pe}from"./index-CzBriCFR.js";import{e as tt,J as le,g as Ee,i as at,a8 as lt,h as De,y as R,M as $}from"./mxcad-DrgW2waE.js";import{M as Me,D as Oe,n as nt,r as ot,x as st}from"./mxdraw-BQ5HhRCY.js";import"./mapbox-gl-DQAr7S0z.js";import{p as rt}from"./print-BKdhRc9P.js";import{S as it,c as Se,z as ut,B as Le,G as dt,H as ze,L as ae,V as Ie,a as je}from"./vuetify-BqCp6y38.js";import"./handsontable-Ch5RdAT_.js";function ct(x){x.sort((y,C)=>y.minPt.y===C.minPt.y?y.minPt.x-C.minPt.x:y.minPt.y-C.minPt.y);let v=[];for(const y of x){let C=!0;for(const V of v)if(y.minPt.x>=V.minPt.x&&y.minPt.y>=V.minPt.y&&y.maxPt.x<=V.maxPt.x&&y.maxPt.y<=V.maxPt.y){C=!1;break}C&&v.push(y)}return v}const pt=async()=>{const x=await tt.userSelect(E("466")),v=[],y=(c,i,M)=>(i.x-c.x)*(i.x-M.x)+(i.y-c.y)*(i.y-M.y)==0,C=(c,i,M,g)=>y(c,i,M),V=c=>{if(c instanceof le){const i=c.numVerts();if(i===0||(c.isClosed?i>4:i>5))return;for(let g=0;g<i;g++)if(c.getBulgeAt(g)>.001)return;const M=c.getPointAt(0).val;if(C(M,c.getPointAt(1).val,c.getPointAt(2).val,c.isClosed?M:c.getPointAt(3).val)){const{minPt:g,maxPt:P}=c.getBoundingBox();v.push({minPt:g,maxPt:P})}}},A=c=>{const i=c.blockTableRecordId.getMcDbBlockTableRecord();i&&i.getAllEntityId().forEach(M=>{const g=M.getMcDbEntity();g&&(g instanceof le&&V(g),g instanceof Ee&&A(g))})};return x.forEach(c=>{const i=c.getMcDbEntity();i instanceof le?V(i):i instanceof Ee&&A(i)}),ct(v).filter(({minPt:c,maxPt:i})=>{const M=new at;return M.imp.userSelect(c.x,c.y,i.x,i.y,lt(),!1),M.count()!==0})};function He(x,v,y,C){const V=Math.abs(C.x-y.x),A=Math.abs(C.y-y.y);let O;return x/V<v/A?O=V/x:O=A/v,O}function mt(x,v,y,C,V){const A=x*V,O=v*V,c=(y.x+C.x)/2,i=(y.y+C.y)/2,M=new R(c-A/2,i+O/2),g=new R(c+A/2,i-O/2);return[M,g]}const ft=()=>{const x=Te(!1,"Mx_PrintDialog"),v=H(!0),y=H(0),C=H(0),V=H(0),A=H(0),O=["A1","A2","A3","A4","自定义16.55x23.90"],c=H("A4"),i=()=>{switch(c.value){case"A1":return{w:594,h:841};case"A2":return{w:420,h:594};case"A3":return{w:297,h:420};case"A4":return{w:210,h:297};case"自定义16.55x23.90":return{w:165.5,h:239,nw:130,nh:190}}},M=["横向","纵向"],g=H("横向"),P=H(1),L=H(1);let ne,oe,_=L.value/P.value;const de=()=>{let{w:a,h:t}=i();const e=L.value/P.value;if(Math.abs(e-_)<1e-6)return;_=e;const[l,s]=mt(a,t,ne,oe,e);y.value=B(l?.x||0,4),C.value=B(l?.y||0,4),V.value=B(s?.x||0,4),A.value=B(s?.y||0,4),z.pt1=l,z.pt2=s,z.w=a,z.h=t},me=()=>{let{w:a,h:t}=i();const e=He(a,t,ne,oe);_=e,L.value=B(P.value*e,4),de()},Y=[];let z;const J=(a,t)=>{ne=a,oe=t;let{w:e,h:l,nw:s,nh:h}=i();if(g.value==="横向"){let I=e;e=l,l=I,I=s,s=h,h=I}z&&Y.push(z);const D=He(e,l,a,t);return _=D,L.value=B(P.value*D,4),z={pt1:a,pt2:t,w:e,h:l,size:c.value,paperOrientation:g.value,printParameterMillimeter:P.value,printParametersCADDrawingUnits:L.value,isDrawingBoundary:v.value},y.value=B(a?.x||0,4),C.value=B(a?.y||0,4),V.value=B(t?.x||0,4),A.value=B(t?.y||0,4),z},se=()=>{const{minPt:a,maxPt:t}=$.getCurrentDatabase().currentSpace.getBoundingBox();return J(a,t)};_e(()=>{v.value&&se()});const fe=()=>{const{pt1:a,pt3:t}=$.getCurrentMxCAD().getViewCADCoord();return J(a,t)},ye=()=>{typeof v.value=="boolean"&&(v.value?se():fe())},ge=()=>{const a=Y.pop();if(!a)return q().error(E("467"));const{pt1:t,pt2:e,size:l}=a;return z=a,y.value=B(t?.x||0,4),C.value=B(t?.y||0,4),V.value=B(e?.x||0,4),A.value=B(e?.y||0,4),c.value=l,g.value=a.paperOrientation,P.value=a.printParameterMillimeter,L.value=a.printParametersCADDrawingUnits,v.value=a.isDrawingBoundary,v.value=null,q().success(E("468"))},re=async()=>{x.isShow.value=!1;const a=()=>{x.isShow.value=!0},t=new De;t.clearLastInputPoint(),t.setMessage(`
`+E("317")+":"),t.disableAllTrace();let e=await t.go();if(!e)return a();t.setMessage(`
`+E("326")+":"),t.setUserDraw((s,h)=>{if(!e)return;h.setColor(16711680);let w=new le;w.addVertexAt(e),w.addVertexAt(new R(e.x,s.y)),w.addVertexAt(s),w.addVertexAt(new R(s.x,e.y)),w.constantWidth=Me.screenCoordLong2Doc(2),w.isClosed=!0,h.drawMcDbEntity(w);let D=[];D.push(e.toVector3()),D.push(new THREE.Vector3(e.x,s.y)),D.push(s.toVector3()),D.push(new THREE.Vector3(s.x,e.y)),h.setColor(12868),h.drawSolid(D,.5)}),t.setDisableOsnap(!0),t.setDisableOrthoTrace(!0),t.setDynamicInputType(Oe.kXYCoordInput);let l=await t.go();if(!l)return a();J(e,l),v.value=null,x.isShow.value=!0,q().success(E("469"))},he=async()=>{function a(r,k,m,U){const X=Math.abs(U.x-m.x),ie=Math.abs(U.y-m.y);let K;X>ie?K=X/r:K=ie/k;const Z=r*K,ce=k*K;return new R(m.x+Z,m.y-ce)}x.isShow.value=!1;const t=()=>{x.isShow.value=!0},e=new De;e.clearLastInputPoint(),e.setMessage(`
`+E("470")+":"),e.disableAllTrace();let{w:l,h:s,nw:h,nh:w}=i();const D=L.value/P.value;(r=>{if(l=l*r,s=s*r,h&&(h=h*r),w&&(w=w*r),g.value==="横向"){let m=l;l=s,s=m,m=h,h=w,w=m}})(D);let F=await e.go();if(!F)return t();e.setMessage(`
`+E("471")+":"),e.setUserDraw((r,k)=>{if(!F)return;k.setColor(16711680),r=a(l,s,F,r);let m=new le;m.addVertexAt(F),m.addVertexAt(new R(F.x,r.y)),m.addVertexAt(r),m.addVertexAt(new R(r.x,F.y)),m.constantWidth=Me.screenCoordLong2Doc(2),m.isClosed=!0,k.drawMcDbEntity(m);let U=[];U.push(F.toVector3()),U.push(new THREE.Vector3(F.x,r.y)),U.push(r.toVector3()),U.push(new THREE.Vector3(r.x,F.y)),k.setColor(12868),k.drawSolid(U,.5)}),e.setDisableOsnap(!0),e.setDisableOrthoTrace(!0),e.setDynamicInputType(Oe.kXYCoordInput);let N=await e.go();if(!N)return t();N=a(l,s,F,N),J(F,N),v.value=null,x.isShow.value=!0,q().success(E("469"))},we=async()=>{x.isShow.value=!1;const a=L.value/P.value;let{w:t,h:e,nw:l,nh:s}=i();(r=>{if(t=t*r,e=e*r,l&&(l=l*r),s&&(s=s*r),g.value==="横向"){let m=t;t=e,e=m,m=l,l=s,s=m}})(a);const w=new De;w.clearLastInputPoint(),w.disableAllTrace(),w.setMessage(`
`+E("325")),w.setKeyWords("");const D=$.getCurrentMxCAD();w.setUserDraw((r,k)=>{k.setColor(16711680);let m=new le,U=r.clone(),X=new R(r.x,r.y+e),ie=new R(r.x+t,r.y+e),K=new R(r.x+t,r.y);m.addVertexAt(U),m.addVertexAt(X),m.addVertexAt(ie),m.addVertexAt(K),m.constantWidth=Me.screenCoordLong2Doc(2),m.isClosed=!0,k.drawMcDbEntity(m);let Z=[];if(Z.push(U.toVector3()),Z.push(X.toVector3()),Z.push(ie.toVector3()),Z.push(K.toVector3()),k.setColor(12868),k.drawSolid(Z,.5),l&&s&&l>0&&s>0){let ce=r.clone(),Pe=new R(r.x,r.y+e),Xe=new R(r.x+t,r.y+e),Ye=new R(r.x+t,r.y),te=[];te.push(ce.toVector3()),te.push(Pe.toVector3()),te.push(Xe.toVector3()),te.push(Ye.toVector3()),te.push(ce.toVector3());let ke=D.mxdraw.viewCoordLong2Cad(3),Ne=nt.createDashedLines(te,16777215,ke*2,ke);k.drawEntity(Ne)}});let I=await w.go();if(!I)return x.isShow.value=!0;let F=I.clone(),N=new R(I.x+t,I.y+e);J(F,N),x.isShow.value=!0,v.value=null,q().success(E("469"))},G=(a,t,e,l,s=!1)=>new Promise(h=>{let w={width:""+e,height:""+l,roate_angle:0,bd_pt1_x:""+a.x,bd_pt1_y:""+a.y,bd_pt2_x:""+t.x,bd_pt2_y:""+t.y};const D=et(),I=()=>{D&&qe("MxFullScreen")};{let{baseUrl:F="",mxfilepath:N="",printPdfUrl:r=""}=Ze()||{};$.getCurrentMxCAD().saveFileToUrl(r,(k,m)=>{try{let U=JSON.parse(m);if(U.ret=="ok"){let X=F+N+U.file;if(s)return h(X);rt(X),I(),q().success(E("472")),h(X)}else console.log(m),q().error(E("473")),h(!1)}catch{console.log("Mx: sserverResult error"),h(!1)}},void 0,JSON.stringify(w))}}),ve=()=>{const{pt1:a,pt2:t,w:e,h:l}=z||se();G(a,t,e,l),x.isShow.value=!1},xe=(a,t,e=16711680)=>{const l=new st;return l.pt1=a.toVector3(),l.pt2=t.toVector3(),l.setLineWidth(10),l.color=e,l.top(),l},Q=Te(!1,"Mx_batch_PrintDialog");let n=[];const f=H([]);let o=[];const j=async()=>{x.isShow.value=!1,Q.isShow.value=!1,o=await pt(),f.value=o.map((a,t)=>({name:E("474")+t,index:t})),n=o.filter(a=>!!a).map((a,t)=>{const{minPt:e,maxPt:l}=a,s=xe(e,l),h=$.getCurrentMxCAD();h.getMxDrawObject().addMxEntity(s);const w=new R((e.x+l.x)/2,(e.y+l.y)/2),D=new ot;return D.text=t.toString(),D.position=w.toVector3(),D.color=16711680,D.height=e.distanceTo(l)*.5,h.getMxDrawObject().addMxEntity(D),[D,s]}),Q.isShow.value=!0};function ee(a,t){var e=document.createElement("a");e.setAttribute("href",a),e.setAttribute("download",t),document.body.appendChild(e),e.click(),document.body.removeChild(e)}const We=async()=>{let{w:a,h:t}=i();for(let e=0;e<o.length;e++){const{minPt:l,maxPt:s}=o[e]||{};if(!l||!s)continue;const h=await G(l,s,a,t,!0);h&&ee(h,f.value[e].name)}Q.isShow.value=!1},Ae=()=>{const a=$.getCurrentMxCAD();n.forEach(([t,e])=>{a.mxdraw.eraseMxEntity(t.objectId()),a.mxdraw.eraseMxEntity(e.objectId())}),n=[],o=[],f.value=[],a.updateDisplay()};return $e(Q.isShow,a=>{a||Ae()}),{dialog:x,isDrawingBoundary:v,lowerLeftCornerCoordinateX:y,lowerLeftCornerCoordinateY:C,upperRightCornerCoordinateX:V,upperRightCornerCoordinateY:A,sheetSizes:O,sheetSize:c,paperOrientations:M,paperOrientation:g,printParameterMillimeter:P,printParametersCADDrawingUnits:L,scopeHistory:Y,callLastTimeScopeHistory:ge,callFreeChoiceOfRange:re,callFixedProportionalSelection:he,callFixedDrawingSizeSelection:we,callPrint:ve,updateDrawingBoundary:ye,updatePrintParameters:de,updateSize:me,universalBatchPrinting:We,frameIndexArr:f,removeFrame:a=>{const[t,e]=n[a],l=$.getCurrentMxCAD();l.mxdraw.eraseMxEntity(t.objectId()),l.mxdraw.eraseMxEntity(e.objectId()),l.updateDisplay(),o.splice(a,1),n.splice(a,1),f.value.splice(a,1)},removeFramesRectBoxArr:Ae,frameRecognition:j,batchPrintingDialog:Q,framePrint:a=>{if(!o[a])return;const{maxPt:t,minPt:e}=o[a];let{w:l,h:s}=i();G(e,t,l,s,!0)},positioningFrame:a=>{if(!o[a])return;let{maxPt:t,minPt:e}=o[a];const l=$.getCurrentMxCAD(),s=e.distanceTo(t)*.1;t=t.clone(),e=e.clone(),e.x-=s,e.y-=s,t.x+=s,t.y+=s,l.zoomW(e,t)}}},yt=["onUpdate:modelValue"],gt={class:"d-flex flex-column"},ht={class:"h-100 mt-2"},wt={class:"d-flex align-center flex-column"},Rt=Je({__name:"index",setup(x){const{dialog:v,isDrawingBoundary:y,lowerLeftCornerCoordinateX:C,lowerLeftCornerCoordinateY:V,upperRightCornerCoordinateX:A,upperRightCornerCoordinateY:O,sheetSizes:c,sheetSize:i,paperOrientations:M,paperOrientation:g,printParameterMillimeter:P,printParametersCADDrawingUnits:L,callLastTimeScopeHistory:ne,callFreeChoiceOfRange:oe,callFixedProportionalSelection:_,callFixedDrawingSizeSelection:de,callPrint:me,updateDrawingBoundary:Y,updatePrintParameters:z,updateSize:J,universalBatchPrinting:se,frameIndexArr:fe,removeFrame:ye,frameRecognition:ge,batchPrintingDialog:re,framePrint:he,positioningFrame:we}=ft(),{isShow:G,showDialog:ve}=v,xe=[{name:"生成打印PDF",fun:me,primary:!0},{name:"取消",fun:()=>ve(!1)}],Q=[{name:"批量下载",fun:se,primary:!0},{name:"取消",fun:()=>re.showDialog(!1)}];return(n,f)=>(Ve(),Ce(Fe,null,[p(Ue,{title:n.t("611"),modelValue:d(re).isShow.value,"onUpdate:modelValue":f[0]||(f[0]=o=>d(re).isShow.value=o),"max-width":"600",footerBtnList:Q},{default:u(()=>[p(it,{density:"compact","fixed-header":"",height:300,class:"attribute_table",style:{"table-layout":"fixed"}},{default:u(()=>[S("thead",null,[S("tr",null,[S("th",null,"pdf"+b(n.t("209")),1),S("th",null,b(n.t("612")),1)])]),S("tbody",null,[(Ve(!0),Ce(Fe,null,Ge(d(fe),(o,j)=>(Ve(),Ce("tr",{key:j},[S("td",null,[Qe(S("input",{class:"w-100 h-100","onUpdate:modelValue":ee=>o.name=ee},null,8,yt),[[Ke,o.name]])]),S("td",null,[p(Se,{onClick:ee=>d(ye)(j)},{default:u(()=>[T(b(n.t("219")),1)]),_:2},1032,["onClick"]),p(Se,{onClick:ee=>d(he)(j),class:"ml-1"},{default:u(()=>[T(b(n.t("225")),1)]),_:2},1032,["onClick"]),p(Se,{onClick:ee=>d(we)(j),class:"ml-1"},{default:u(()=>[T(b(n.t("613")),1)]),_:2},1032,["onClick"])])]))),128))])]),_:1})]),_:1},8,["title","modelValue"]),p(Ue,{title:n.t("225"),modelValue:d(G),"onUpdate:modelValue":f[10]||(f[10]=o=>W(G)?G.value=o:null),"max-width":"600",footerBtnList:xe},{default:u(()=>[p(ut,{align:"stretch"},{default:u(()=>[p(Le,{cols:"6","align-self":"start"},{default:u(()=>[p(be,{title:n.t("614"),class:"mt-2"},{default:u(()=>[p(dt,{modelValue:d(y),"onUpdate:modelValue":[f[1]||(f[1]=o=>W(y)?y.value=o:null),d(Y)],inline:!1},{default:u(()=>[p(ze,{value:!0,onClick:d(Y)},{label:u(()=>[p(Be,{class:"","key-name":"W"},{default:u(()=>[T(b(n.t("231")+n.t("232")),1)]),_:1})]),_:1},8,["onClick"]),p(ze,{class:"mt-1",value:!1,onClick:d(Y)},{label:u(()=>[p(Be,{class:"","key-name":"R"},{default:u(()=>[T(b(n.t("233")+n.t("234")),1)]),_:1})]),_:1},8,["onClick"])]),_:1},8,["modelValue","onUpdate:modelValue"]),S("div",gt,[p(ue,{class:"mt-2",onClick:d(ne)},{default:u(()=>[T(b(n.t("235")+n.t("236")),1)]),_:1},8,["onClick"]),p(ue,{class:"mt-1",onClick:d(oe)},{default:u(()=>[T(b(n.t("237")+n.t("238")),1)]),_:1},8,["onClick"]),p(ue,{class:"mt-1",onClick:d(_)},{default:u(()=>[T(b(n.t("239")+n.t("240")+n.t("238")),1)]),_:1},8,["onClick"]),p(ue,{class:"mt-1",onClick:d(de)},{default:u(()=>[T(b(n.t("239")+n.t("231")+n.t("241")+n.t("238")),1)]),_:1},8,["onClick"]),p(ue,{class:"mt-1",onClick:d(ge)},{default:u(()=>[T(b(n.t("615")+" "+n.t("616")),1)]),_:1},8,["onClick"])]),p(ae,{modelValue:d(C),"onUpdate:modelValue":f[2]||(f[2]=o=>W(C)?C.value=o:null),class:"mt-2",type:"number"},{prepend:u(()=>[S("span",null,b(n.t("242")+n.t("243")+n.t("244"))+"X:",1)]),_:1},8,["modelValue"]),p(ae,{modelValue:d(V),"onUpdate:modelValue":f[3]||(f[3]=o=>W(V)?V.value=o:null),class:"mt-1",type:"number"},{prepend:u(()=>[S("span",null,b(n.t("242")+n.t("243")+n.t("244"))+"Y:",1)]),_:1},8,["modelValue"]),p(ae,{modelValue:d(A),"onUpdate:modelValue":f[4]||(f[4]=o=>W(A)?A.value=o:null),class:"mt-1",type:"number"},{prepend:u(()=>[S("span",null,b(n.t("245")+n.t("243")+n.t("244"))+"X:",1)]),_:1},8,["modelValue"]),p(ae,{modelValue:d(O),"onUpdate:modelValue":f[5]||(f[5]=o=>W(O)?O.value=o:null),class:"mt-1",type:"number"},{prepend:u(()=>[S("span",null,b(n.t("245")+n.t("243")+n.t("244"))+"Y:",1)]),_:1},8,["modelValue"])]),_:1},8,["title"])]),_:1}),p(Le,{cols:"6","align-self":"start"},{default:u(()=>[S("div",ht,[p(be,{title:n.t("231")+n.t("246")},{default:u(()=>[p(Ie,{modelValue:d(i),"onUpdate:modelValue":[f[6]||(f[6]=o=>W(i)?i.value=o:null),d(J)],items:d(c),class:"mr-1 my-2"},{prepend:u(()=>[S("span",null,b(n.t("231")+n.t("241"))+":",1)]),selection:u(({item:o})=>[T(b(d(pe)(o.title)),1)]),item:u(({item:o,props:j})=>[p(je,Re(j,{title:d(pe)(o.title)}),null,16,["title"])]),_:1},8,["modelValue","items","onUpdate:modelValue"]),p(Ie,{modelValue:d(g),"onUpdate:modelValue":[f[7]||(f[7]=o=>W(g)?g.value=o:null),d(Y)],items:d(M),class:"mr-1 my-2"},{prepend:u(()=>[S("span",null,b(n.t("231")+n.t("247"))+":",1)]),selection:u(({item:o})=>[T(b(d(pe)(o.title)),1)]),item:u(({item:o,props:j})=>[p(je,Re(j,{title:d(pe)(o.title)}),null,16,["title"])]),_:1},8,["modelValue","items","onUpdate:modelValue"])]),_:1},8,["title"]),p(be,{class:"my-4 py-4",title:n.t("225")+n.t("248")},{default:u(()=>[S("div",wt,[p(ae,{modelValue:d(P),"onUpdate:modelValue":f[8]||(f[8]=o=>W(P)?P.value=o:null),modelModifiers:{lazy:!0},min:"0","onUpdate:focused":d(z),class:"w-75 mr-1",type:"number"},{append:u(()=>[T(b(n.t("249")),1)]),_:1},8,["modelValue","onUpdate:focused"]),f[11]||(f[11]=S("span",null,"=",-1)),p(ae,{modelValue:d(L),"onUpdate:modelValue":f[9]||(f[9]=o=>W(L)?L.value=o:null),modelModifiers:{lazy:!0},min:"0","onUpdate:focused":d(z),class:"w-75 ml-1 mr-1",type:"number"},{append:u(()=>[T(b("CAD"+n.t("250")+n.t("251")),1)]),_:1},8,["modelValue","onUpdate:focused"])])]),_:1},8,["title"])])]),_:1})]),_:1})]),_:1},8,["title","modelValue"])],64))}});export{Rt as default};
