import {getLogSetting} from  '../util/logSetting'
import SDKDrawState from  './sdkDrawState'

class SDKClickInfo {
    _rastermap = null
    _clickInfo = null
    _clickInfoExtend = null
    _currentMapSDKType = null
    constructor(){
        this._rastermap = null
        this._clickInfo = null
        this._clickInfoExtend = null
        this._currentMapSDKType = null
    }
    bind(rastermap, callback){
        this._rastermap = rastermap
        const tempWindow = window
        this._rastermap.on("click", (params) => {
            console.log(params,'sdk_click_info')    
            const isLog = getLogSetting("sdk_click_info")
            if(isLog) console.log('rastermapclick', params);
            this._clickInfo = params
            if (params.clickItem) {
                let devices = [
                    {
                        className: params.clickItemClassName,
                        devId: params.clickItem,
                    },
                ];
                // 通过设备id查询设备信息
                if(isLog) console.log('获取设备信息数据响应入参', devices);
                
                //在SDK绘制功能里 调用方法会 丢失之前绘制方法的回调  20250221
                if(!SDKDrawState.getSDKDrawState() || SDKDrawState.getSDKDrawInfo()?.drawCMD === "SDK_selectEntity"){
                    this._rastermap.getDeviceInfo(devices, (extend) => {
                        if(isLog) console.log("获取设备信息数据响应结果：", extend);
                        this._clickInfoExtend = extend
                        
                        if(callback){
                            callback({type: 'click', params: params, mapSDKType: this._currentMapSDKType}, tempWindow)
                        }
                    });
                }
            }else if(callback){
                callback({type: 'click', params: params, mapSDKType: this._currentMapSDKType}, tempWindow)
            }
        })
    }

    setCurrentMapSDKType(currentMapSDKType){
        this._currentMapSDKType = currentMapSDKType
    }

    getClickInfoClassName(){
        return this._clickInfo.clickItemClassName
    }
    getCurrentClickInfo(){
        return this._clickInfo
    }
    getCurrentClickInfoExtend(){
        return this._clickInfoExtend
    }
    hasClickInfo(){
        return this._clickInfo && this._clickInfo.clickItem
    } 
}
export default new SDKClickInfo()