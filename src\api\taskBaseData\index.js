import request from '@/utils/request'

// 10kv 常用水泥杆
export function getGridMaterials10KVData(params) {
    return request({
        url: '/rodLineJointDraw/getGridMaterials10KVData',
        method: 'get',
        params
    })
}

// 10kv常用导线(下拉数据)
export function getGridModuleLine10KVData(params) {
    return request({
        url: '/rodLineJointDraw/getGridModuleLine10KVData',
        method: 'get',
        params
    })
}

// 查询水泥杆-低压物料类下拉框
export function getGridMaterialsLowData(params) {
    return request({
        url: '/rodLineJointDraw/getGridMaterialsLowData',
        method: 'get',
        params
    })
}

// 查询常用导线低压模块下拉框
export function getGridModuleLineLowData(params) {
    return request({
        url: '/rodLineJointDraw/getGridModuleLineLowData',
        method: 'get',
        params
    })
}

// 10kv常用电缆(低压常用电缆)
export function getGridMaterialsCableData(params) {
    return request({
        url: '/rodLineJointDraw/getGridMaterialsCableData',
        method: 'get',
        params
    })
}

// 电缆井参数(下拉选项)
export function getAllGridModuleWellData(params) {
    return request({
        url: '/rodLineJointDraw/getAllGridModuleWellData',
        method: 'get',
        params
    })
}

// 获得标注树形和表格集合
export function getTagList(params) {
    return request({
        url: '/rodLineJointDraw/getTagList',
        method: 'get',
        params
    })
}


// 获取CAD+系统字体
export function getFontList(params) {
    return request({
        url: '/rodLineJointDraw/getFontList',
        method: 'get',
        params
    })
}

// 获得所有CAD图签
export function getPhotoSignListStr(params) {
    return request({
        url: '/rodLineJointDraw/getPhotoSignListStr',
        method: 'get',
        params
    })
}

// 获得标注对齐方式下拉列表框数据
export function getAttachmentPointList(params) {
    return request({
        url: '/rodLineJointDraw/getAttachmentPointList',
        method: 'get',
        params
    })
}

export function getBaseSettingList(params) {
    return request({
        url: '/rodLineJointDraw/getBaseSettingList',
        method: 'get',
        params
    })
}
// 获取工程设置-地理条件设置
export function getEngineeringSettingByTaskId(params) {
    return request({
        url: '/rodLineJointDraw/getEngineeringSettingByTaskId',
        method: 'get',
        params
    })
}

