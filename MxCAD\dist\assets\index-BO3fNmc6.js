import{h as j,d as i,c as D,w as h,K as G,a0 as m,_ as w,$ as c,m as u,Q as k,V as M,u as _,a1 as H,a6 as P,H as Q,a3 as W,a4 as q,F as z,B as X}from"./vue-DfH9C9Rx.js";import{M as Y}from"./index-8X61wlK0.js";import{ah as Z,I as ee,aS as ae,aT as B,w as le,s as L,n as E,aU as y,c as te}from"./index-D95UjFey.js";import{e as se,i as oe}from"./mxcad-CfPpL1Bn.js";import"./mapbox-gl-PN3pDJHq.js";import{j as I,a5 as re,i as ne,k as ue,a as ie}from"./vuetify-B_xYg4qv.js";import"./mxdraw-C_n_7lEs.js";import"./xlsx-Ck5MUZvf.js";const ce={class:"d-flex algin-center"},he=j({__name:"index",setup(de){const{isShow:f}=y,b=Z(),{setValue:N,stringifyJSON:S,remove:A,setLayerList:U}=b,{list:p}=ee(b),r=i(!1),n=D(()=>p.value.filter(e=>e.name).map(({name:e})=>e)),t=i([...n.value]);let x;const V=i(!0);h(f,l=>{l?x=ae():x&&V.value&&B(x)}),h(t,le(l=>{p.value.forEach((e,s)=>{N("visible",l.includes(e.name),s)}),B(S())},100));const d=i(""),g=i(!1),F=async()=>{y.showDialog(!1);const l=await se.userSelect(L("选择对象")),e=new Set;l.forEach(s=>{const a=s.getMcDbEntity();a&&e.add(a.layer)}),t.value=Array.from(e),y.showDialog(!0)};h(d,l=>{l===""&&(g.value=!1,r.value=!1)});const K=()=>{if(d.value===""){t.value=[];return}g.value=!0,r.value=!0,t.value=n.value.filter(l=>l.toLocaleLowerCase()===d.value.toLocaleLowerCase())},C=i([]),T=l=>{const{map:e}=l(C.value.map(a=>a.$el),(a,o)=>o);if(e.length===0)return;const s=r.value?t.value:n.value;t.value=e.map(a=>s[a])},$=(l,e,s)=>{if(l.ctrlKey)t.value.indexOf(e)===-1?t.value=[...t.value,e]:t.value=t.value.filter(o=>o!==e);else if(l.shiftKey){const a=t.value.map(R=>n.value.indexOf(R));let o=Math.min(...a),v=Math.max(...a);o>s?o=s:v=s,t.value=n.value.slice(o,v+1)}else t.value=[e]},J=D(()=>`${L("图层漫游")} - ${L("图层数")}:`+n.value.length),O=()=>{const l=new oe,e=new Set;l.allSelect(),l.forEach(a=>{let o=a.getMcDbEntity();o&&e.add(o.layer)});const s=Array.from(e);A(p.value.filter(({name:a})=>!s.includes(a)),"this"),U(S())};return(l,e)=>{const s=G("box-selection");return m(),w(Y,{maxWidth:"400px",ref:"layerDialog",modelValue:_(f),"onUpdate:modelValue":e[4]||(e[4]=a=>X(f)?f.value=a:null),title:J.value},{actions:c(()=>[u(re,{class:"mx-1 mt-0 mb-1 py-0 px-2 bg-dialog-card-text d-flex justify-end"},{default:c(()=>[u(I,{class:"mr-5",label:l.t("671"),modelValue:V.value,"onUpdate:modelValue":e[2]||(e[2]=a=>V.value=a)},null,8,["label","modelValue"]),u(E,{onClick:O},{default:c(()=>[k(M(l.t("672")),1)]),_:1}),u(E,{onClick:e[3]||(e[3]=a=>_(y).showDialog(!1))},{default:c(()=>[k(M(l.t("230")),1)]),_:1})]),_:1})]),default:c(()=>[H("div",ce,[u(te,{onClick:F}),u(ne,{modelValue:d.value,"onUpdate:modelValue":e[0]||(e[0]=a=>d.value=a),onKeydown:P(K,["enter"])},null,8,["modelValue"]),u(I,{label:l.t("670"),modelValue:r.value,"onUpdate:modelValue":e[1]||(e[1]=a=>r.value=a),disabled:!g.value},null,8,["label","modelValue","disabled"])]),Q((m(),w(ue,{density:"compact",color:"primary",style:{height:"300px"}},{default:c(()=>[(m(!0),W(z,null,q(r.value?[...t.value]:n.value,(a,o)=>(m(),w(ie,{class:"ma-0",title:a,ref_for:!0,ref_key:"refItems",ref:C,key:o,onClick:v=>$(v,a,o),active:r.value?r.value:t.value.includes(a)},null,8,["title","onClick","active"]))),128))]),_:1})),[[s,T]])]),_:1},8,["modelValue","title"])}}});export{he as default};
