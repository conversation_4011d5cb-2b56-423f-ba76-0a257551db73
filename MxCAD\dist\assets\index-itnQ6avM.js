import{h as v,a3 as r,a4 as s,_ as o,m as l,ab as t,a0 as d,u as b,V as n,U as B,ac as i,$ as u,a5 as w,n as D,Q as T,F as M}from"./vue-Cj9QYd7Z.js";import{az as $,aA as A,Z as z,U as L,_ as E}from"./index-CzBriCFR.js";import{a5 as I,a6 as N,c as P,b as S,a2 as j,a7 as F,e as U}from"./vuetify-BqCp6y38.js";const K={class:"d-flex justify-space-between align-center w-100 py-1"},Q={class:"title_box"},X=["src"],Y={class:"ml-2"},Z={key:1},q=v({__name:"index",props:{title:{default:"标题"},logo:{},cardClass:{},cardTextClass:{},cardActionClass:{},footerBtnList:{default:()=>[]},modelValue:{type:Boolean,default:!1},keys:{}},emits:["update:modelValue"],setup(c,{expose:f,emit:p}){const g=p,{dialog:_,dialogTitleEl:y,getMoveX:C,getMoveY:h,closeDialog:G,bindKeys:V}=$();V(c.keys,()=>c.modelValue);const k=a=>{g("update:modelValue",a)};return f({getMoveX:C,getMoveY:h}),(a,m)=>(o(),r(I,{ref_key:"dialog",ref:_,"model-value":a.modelValue,contained:"",persistent:"","no-click-animation":"","retain-focus":""},{default:s(()=>[l(U,{class:i(["w-100 h-100 rounded-0",a.cardClass||"bg-dialog-card box-shadow"])},{default:s(()=>[l(N,{class:"pa-0",ref_key:"dialogTitleEl",ref:y},{default:s(()=>[t(a.$slots,"header",{},()=>[d("div",K,[d("div",Q,[t(a.$slots,"header-icon",{},()=>[d("img",{class:"ml-1",alt:"logo",width:"24",height:"24",src:a.logo||b(A)()},null,8,X)],!0),d("span",Y,n(a.title),1)]),l(P,{size:"24px",class:"mr-2",variant:"plain",onClick:m[0]||(m[0]=B(e=>k(!1),["stop"]))},{default:s(()=>[l(S,{size:"16px",icon:"cha"})]),_:1})])],!0)]),_:3},512),l(j,{class:i([a.cardTextClass||"mx-1 px-2  bg-dialog-card-text rounded-t","py-0"])},{default:s(()=>[t(a.$slots,"default",{},void 0,!0)]),_:3},8,["class"]),t(a.$slots,"actions",{},()=>[l(F,{class:i(["mx-1 mt-0 mb-1 py-0 px-2 d-flex justify-end",a.cardActionClass||"bg-dialog-card-text"])},{default:s(()=>[(o(!0),u(M,null,w(a.footerBtnList,(e,x)=>(o(),r(z,{key:x+e.name,onClick:e.fun,isAction:"",primary:e.primary,disabled:e.disabled?e.disabled():!1,class:i(["ml-3 px-2",e.disabled&&e.disabled()?"disabled":""])},{default:s(()=>[e.labelProps?(o(),r(L,D({key:0,ref_for:!0},e.labelProps,{noTextCaption:""}),{default:s(()=>[T(n(a.t(e.name)),1)]),_:2},1040)):(o(),u("span",Z,n(a.t(e.name)),1))]),_:2},1032,["onClick","primary","disabled","class"]))),128))]),_:1},8,["class"])],!0)]),_:3},8,["class"])]),_:3},8,["model-value"]))}}),R=E(q,[["__scopeId","data-v-3613277d"]]);export{R as M};
